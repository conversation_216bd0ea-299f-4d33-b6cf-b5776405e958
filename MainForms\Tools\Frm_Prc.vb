﻿Option Strict Off
Option Explicit On
Imports System.Windows.Forms
Imports System.Data
Imports System.Data.SqlClient
Imports vb = Microsoft.VisualBasic
Imports System.Drawing
Imports System.Drawing.Drawing2D
Imports System.Drawing.Imaging
Imports System.IO
Imports CrystalDecisions.CrystalReports.Engine
Imports System.Data.OleDb
Imports System.Drawing.Imaging.ImageFormat
Imports BarcodeLib.Barcode.CrystalReports
Imports BarcodeLib.Barcode

Public Class Frm_Prc
    Dim WithEvents BS As New BindingSource

    Dim R() As String
    Dim Mnm As String
    Dim _totalItem As Double
    Dim RateVAT As String = "0"
    Dim ParcodeUnity As String = ""


    Private Sub sales_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        GetValueDefaultBarcodePaper()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Bra.Fil("groups", "g_name", cmbcats)
        Bra.Fil("groups", "g_name", cmbcatsAll)
        Cls.fill_combo_Branch("Items", "sname", cmbname)

        ExpirationDate()
    End Sub

    Private Sub cmbcats_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbcats.DropDown
        ClearAdd()
    End Sub

    Private Sub cmbcats_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbcats.KeyUp
        If e.KeyCode = 13 Then
            If cmbcats.Text.Trim = "" Then
                cmbname.Focus()
            End If
        End If
    End Sub

    Private Sub txtprice_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtprice.KeyUp
        If e.KeyCode = 13 Then
            txtqunt.Focus()
        End If
    End Sub

    Private Sub txtprice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtprice.TextChanged
        MyVars.CheckNumber(txtprice)
    End Sub

    Private Sub txtqunt_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtqunt.KeyUp
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub txtqunt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtqunt.TextChanged
        MyVars.CheckNumber(txtqunt)
    End Sub

    Function ValidateTextAdd() As Boolean

        If cmbcats.Text = "" Then MsgBox("فضلا حدد المجموعة", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbname.Text = "" Then MsgBox("فضلا أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbname.Focus() : Return False
        If Val(txtprice.Text.Trim) = 0 Then MsgBox("فضلا أدخل السعر", MsgBoxStyle.Exclamation) : txtprice.Focus() : Return False
        If Val(txtqunt.Text.Trim) = 0 Then MsgBox("فضلا أدخل العدد ", MsgBoxStyle.Exclamation) : txtqunt.Focus() : Return False
        If txtprc.Text.Length = 13 Then
            If rdoOneRow13Number.Checked = False And rdoOneRow13Number2.Checked = False And rdoPaperHalves13Number.Checked = False And rdoPaperHalves13Number2.Checked = False And rdoMediumBarCodeVAT_50_25_13Number.Checked = False Then
                MsgBox("فضلا حدد اخيار ورقة الباركود من نوع 13 رقم", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
            End If
        End If

        If txtprc.Text.Length < 13 Then
            If rdoOneRow13Number.Checked = True Or rdoOneRow13Number2.Checked = True Or rdoPaperHalves13Number.Checked = True Or rdoPaperHalves13Number2.Checked = True Or rdoMediumBarCodeVAT_50_25_13Number.Checked = True Then
                MsgBox("فضلا حدد اخيار ورقة الباركود من نوع اقل من 13 رقم", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
            End If
        End If

        Return True
    End Function

    Function ValidateTextSave() As Boolean
        If Dgv_Add.Rows.Count < 1 Then MsgBox("فضلا أكمل البيانات ", MsgBoxStyle.Exclamation) : Return False
        Return True
    End Function

    Friend Sub DTV_Width()
        If Dgv_Add.Rows.Count > 0 Then
            Dgv_Add.Columns(0).Width = 90
            Dgv_Add.Columns(1).Width = 90
            Dgv_Add.Columns(2).Width = 160
            Dgv_Add.Columns(3).Width = 60
            Dgv_Add.Columns(4).Width = 60
            'Dgv_Add.Columns(5).Width = 75
        End If

    End Sub

    Private Sub BtnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAdd.Click
        'If rdoParcodeA4_4_17.Checked = True Then
        '    Dim Qunt As Integer = Val(txtqunt.Text) / Val(4)
        '    txtqunt.Text = Qunt
        'End If
        If txtPriceAfterDisco.Text = "" Then
            txtPriceAfterDisco.Text = "0"
        End If

        Dim Qunt As Integer
        Qunt = txtqunt.Text
        If rdoPaperHalves1.Checked = True Then
            If Val(txtqunt.Text) = 1 Then
                Qunt = Val(txtqunt.Text)
            Else
                Qunt = Val(txtqunt.Text) / Val(2)
            End If
        End If
        If rdoPaperHalves2.Checked = True Then
            If Val(txtqunt.Text) = 1 Then
                Qunt = Val(txtqunt.Text)
            Else
                Qunt = Val(txtqunt.Text) / Val(2)
            End If
        End If
        If rdoPaperHalves3.Checked = True Then
            If Val(txtqunt.Text) = 1 Then
                Qunt = Val(txtqunt.Text)
            Else
                Qunt = Val(txtqunt.Text) / Val(2)
            End If
        End If
        If rdoPaperHalves13Number.Checked = True Then
            If Val(txtqunt.Text) = 1 Then
                Qunt = Val(txtqunt.Text)
            Else
                Qunt = Val(txtqunt.Text) / Val(2)
            End If
        End If
        If rdoPaperHalves13Number2.Checked = True Then
            If Val(txtqunt.Text) = 1 Then
                Qunt = Val(txtqunt.Text)
            Else
                Qunt = Val(txtqunt.Text) / Val(2)
            End If
        End If

        If ValidateTextAdd() = False Then Exit Sub
        Dim prc_ As String
        If txtprc.Text = "" Then
            prc_ = 0
        Else
            prc_ = txtprc.Text
        End If
        If cmbcats.Text = "" Or cmbname.Text = "" Or txtprice.Text = "" Or txtqunt.Text = "" Then
            MsgBox("اكمل البيانات")
            Exit Sub
        End If

        Dim XParcode As String = ""
        If rdoOneRow13Number.Checked = True Or rdoOneRow13Number2.Checked = True Or rdoPaperHalves13Number.Checked = True Or rdoPaperHalves13Number2.Checked = True Or rdoMediumBarCodeVAT_50_25_13Number.Checked = True Then
            If txtCodeEAN13Barre.Text = "" Then
                XParcode = mykey.GetValue("CodeEAN13Reg", "CodeEAN13Reg")
            Else
                XParcode = txtCodeEAN13Barre.Text
            End If
        Else
            XParcode = "!" & txtprc.Text.Trim & "!"
        End If

        Dim TextRateVAT As String = ""
        If RateVAT <> "0" Then
            TextRateVAT = "شامل الضريبة  -"
        Else
            TextRateVAT = ""
        End If

        Dim ExpirationDate As String = mykey.GetValue("ExpirationDate", "NO")
        If ExpirationDate = "NO" Then
            Dgv_Add.DataSource = Fn_AddBill(XParcode, cmbcats.Text, cmbname.Text, txtprice.Text, Qunt, txtprc.Text, "", txtPriceAfterDisco.Text, TextRateVAT)
        Else
            Dgv_Add.DataSource = Fn_AddBill(XParcode, cmbcats.Text, cmbname.Text, txtprice.Text, Qunt, txtprc.Text, txtValidFor.Text, txtPriceAfterDisco.Text, TextRateVAT)
        End If


        ClearAdd() : cmbname.Focus()
        Dgv_Add.Columns(5).Visible = False
        Dgv_Add.Columns(6).Visible = False
        Dgv_Add.Columns(8).Visible = False

        ', Val(txtprice.Text) * Val(txtquntUnity.Text)
    End Sub

    Private Sub ClearAdd()
        cmbname.Text = ""
        txtprc.Text = ""
        txtprice.Text = ""
        txtqunt.Text = ""
        txtValidFor.Text = ""
        txtPriceAfterDisco.Text = ""
    End Sub

    Dim Dt_AddBill As New DataTable
    Friend Function Fn_AddBill(ByVal Col_Prc As String, ByVal Col_Cats As String, ByVal Col_Name As String _
, ByVal Col_Price As Double, ByVal Col_Quant As Double, ByVal Col_Parcode As String, ByVal Col_ValidFor As String, ByVal Col_PriceAfterDisco As Double, ByVal Col_PriceIncludesVAT As String) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("الباركود", GetType(String))
            Dt_AddBill.Columns.Add("المجموعة", GetType(String))
            Dt_AddBill.Columns.Add("الاسم", GetType(String))
            Dt_AddBill.Columns.Add("السعر", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية", GetType(Double))
            Dt_AddBill.Columns.Add("ID", GetType(String))
            Dt_AddBill.Columns.Add("صالح لمدة", GetType(String))
            Dt_AddBill.Columns.Add("السعر بعد الخصم", GetType(Double))
            Dt_AddBill.Columns.Add("PriceIncludesVAT", GetType(String))
        End If

        DTV_Width()

        Dt_AddBill.Rows.Add(Col_Prc, Col_Cats, Col_Name, Col_Price, Col_Quant, Col_Parcode, Col_ValidFor, Col_PriceAfterDisco, Col_PriceIncludesVAT)
        Return Dt_AddBill
    End Function

    Private Sub Dgv_Add_CellContentClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles Dgv_Add.CellContentClick
        On Error Resume Next
        If e.ColumnIndex = 0 Then
            txtprc.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Prc").Value.ToString
            cmbcats.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Cats").Value.ToString
            cmbname.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Name").Value.ToString
            txtprice.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Price").Value
            txtqunt.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Quant").Value
            Dgv_Add.Rows.RemoveAt(e.RowIndex)
        End If
    End Sub

    Private Sub BtnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnSave.Click
        On Error Resume Next
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        SetValueDefaultBarcodePaper()

        Cls.GetDefaultPrinterBarcode()

        If Dgv_Add.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If
        AddReportView()

        Dim txtNameAr, txtType_Currency As TextObject

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintAllItems")

        Dim Price As String = "" : Dim GenerateAutoID8 As String : Dim GenerateAutoID3 As String
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            For m As Integer = 0 To Dgv_Add.Rows(i).Cells(4).Value - 1
                Price = Dgv_Add.Rows(i).Cells(3).Value
                If rdoMediumBarCode4.Checked = True Then
                    GenerateAutoID8 = vb.Right(Clss.GenerateItmId_Or_Parcode(), 8)
                    GenerateAutoID3 = vb.Right(Clss.GenerateItmId_Or_Parcode(), 3)
                    Price = GenerateAutoID8 & Dgv_Add.Rows(i).Cells(3).Value & "." & GenerateAutoID3
                End If
                S = "insert into PrintAllItems(Company_Branch_ID,itm_id,group_name,sname,TinPrice,qunt,Notes,profits,tinpricetotal,bill_date,ValStore)  values("
                S = S & "N'" & Company_Branch_ID & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Price & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & dtpProductionDate.Text & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
        Next
        Dim rpt
        If rdoPaperHalves1.Checked = True Then
            rpt = New rpt_Parcode10
        End If
        If rdoPaperHalves2.Checked = True Then
            rpt = New rpt_Parcode5
        End If
        If rdoPaperHalves3.Checked = True Then
            If chkShowPrice.Checked = True Then
                rpt = New rpt_ParcodePaperHalvesPrice3
            Else
                rpt = New rpt_ParcodePaperHalves3
            End If
        End If
        If rdoPaperHalves13Number2.Checked = True Then
            rpt = New rpt_PaperHalves13Number2
        End If
        If rdoPaperHalves13Number.Checked = True Then
            If chkShowPrice.Checked = True Then
                rpt = New rpt_PaperHalves13NumberPrice
            Else
                rpt = New rpt_PaperHalves13Number
            End If
        End If
        If rdoOneRow.Checked = True Then
            If chkShowPrice.Checked = True Then
                If rdoFonts7.Checked = True Then
                    rpt = New rpt_Parcode7
                End If
                If rdoFonts9.Checked = True Then
                    rpt = New rpt_Parcode9
                End If
            Else
                If rdoFonts7.Checked = True Then
                    rpt = New rpt_Parcode6
                End If
                If rdoFonts9.Checked = True Then
                    rpt = New rpt_Parcode8
                End If
            End If
        End If
        If rdoMediumBarCode2.Checked = True Then
            If chkShowPrice.Checked = True Then
                rpt = New rpt_ParcodeMediumBarCode_Price2
            Else
                rpt = New rpt_ParcodeMediumBarCode2
            End If
        End If
        If rdoMediumBarCode3.Checked = True Then
            rpt = New rpt_ParcodeMediumBarCode_Price3
        End If
        If rdoMediumBarCode4.Checked = True Then
            rpt = New rpt_ParcodeMediumBarCode_Price4
            txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
            txtNameAr.Text = NameArCompay
        End If
        If rdoMediumBarCode5.Checked = True Then
            If chkShowPrice.Checked = True Then
                rpt = New rpt_ParcodeMediumBarCode_Price5
            Else
                rpt = New rpt_ParcodeMediumBarCode2
            End If
            txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
            txtNameAr.Text = NameArCompay
        End If
        If rdoMediumBarCode05_25.Checked = True Then
            If chkShowPrice.Checked = True Then
                rpt = New rpt_ParcodeMediumBarCode_Price05_25
            Else
                rpt = New rpt_ParcodeMediumBarCode05_25
            End If
            txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
            txtNameAr.Text = NameArCompay
        End If
        If rdoMediumBarCodeAfterDisco3.Checked = True Then
            rpt = New rpt_ParcodeMediumBarCode_Price_AfterDisco3
        End If
        If rdoMediumBarCodeProductionDate.Checked = True Then
            rpt = New rpt_ParcodeMediumBarCode_5_37_ProDate
        End If
        If rdoThreeRows.Checked = True Then
            rpt = New rpt_Parcode2
        End If
        If rdoOneRow13Number.Checked = True Then
            If chkShowPrice.Checked = True Then
                rpt = New rpt_Parcode13NumberPrice
            Else
                rpt = New rpt_Parcode13Number
            End If
        End If
        If rdoOneRow13Number2.Checked = True Then
            If chkShowPrice.Checked = True Then
                rpt = New rpt_Parcode13NumberPrice2
            Else
                rpt = New rpt_Parcode13Number2
            End If
            txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
            txtNameAr.Text = NameArCompay
        End If
        If rdoParcodeExpirationDate.Checked = True Then
            If chkExpiration.Checked = False Or txtValidFor.Text = "" Then
                rpt = New rpt_ParcodeExpirationDate2
            Else
                rpt = New rpt_ParcodeExpirationDate
            End If
        End If
        If rdoParcodeA4_4_17.Checked = True Then
            If chkShowPrice.Checked = True Then
                rpt = New rpt_ParcodeA4_17_4_Price
            Else
                rpt = New rpt_ParcodeA4_17_4
            End If
        End If
        If rdoParcodeA4_6_12.Checked = True Then
            rpt = New rpt_ParcodeA4_12_6_Price
        End If
        If rdoParcodeA4_6_16.Checked = True Then
            rpt = New rpt_ParcodeA4_16_6_Price
        End If
        If rdoMediumBarCode5_37.Checked = True Then
            rpt = New rpt_ParcodeMediumBarCode_5_37
        End If
        If rdoMediumBarCode55_40.Checked = True Then
            rpt = New rpt_ParcodeMediumBarCode_55_40
        End If
        If RadioButton1.Checked = True Then
            rpt = New rpt_ParcodeMediumBarCode2Windows
        End If
        If rdoMediumBarCodeVAT_50_25.Checked = True Then
            If chkShowPrice.Checked = True Then
                rpt = New rpt_PaperHalvesVAT_50_25_Price
            Else
                rpt = New rpt_PaperHalvesVAT_50_25
            End If
        End If
        If rdoMediumBarCodeVAT_50_25_13Number.Checked = True Then
            If chkShowPrice.Checked = True Then
                rpt = New rpt_PaperHalvesVAT_50_25_13Number_Price
            Else
                rpt = New rpt_PaperHalvesVAT_50_25_13Number
            End If
        End If
        If rdoMediumBarCode38_25.Checked = True Then
            rpt = New rpt_ParcodeMediumBarCode_Price_38_25
        End If
        If rdoMediumBarCodeNot38_25.Checked = True Then
            rpt = New rpt_ParcodeMediumBarCode_Price_Not_38_25
        End If
        Cls.Select_More_Data_Branch_Print("PrintAllItems", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)

        If rdoParcodeA4_4_17.Checked = True Then
            Dim txtTitel1, txtTitel2, txtTitel3, txtTitel4 As TextObject
            txtTitel1 = rpt.Section1.ReportObjects("txtTitel1")
            txtTitel1.Text = NameArCompay
            txtTitel2 = rpt.Section1.ReportObjects("txtTitel2")
            txtTitel2.Text = NameArCompay
            txtTitel3 = rpt.Section1.ReportObjects("txtTitel3")
            txtTitel3.Text = NameArCompay
            txtTitel4 = rpt.Section1.ReportObjects("txtTitel4")
            txtTitel4.Text = NameArCompay
        End If
        If rdoMediumBarCode2.Checked = True Or rdoMediumBarCode5.Checked = True Then
            txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
            txtNameAr.Text = NameArCompay
        End If
        If rdoMediumBarCode5_37.Checked = True Or rdoMediumBarCode55_40.Checked = True Then
            txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
            txtNameAr.Text = NameArCompay
        End If
        Dim ExpirationDate As String = mykey.GetValue("ExpirationDate", "YES")
        If ExpirationDate = "YES" Then
            txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
            txtNameAr.Text = NameArCompay
        End If
        If chkShowPrice.Checked = True Then
            If chkShowPrice.Enabled = True Then
                Dim Type_Currency As String = mykey.GetValue("TypeCurrency", "جنية مصرى")
                If chkPriceLE.Checked = True Then
                    Type_Currency = "LE: "
                Else
                    txtType_Currency = rpt.Section1.ReportObjects("txtTypeCurrency")
                    txtType_Currency.Text = Type_Currency
                End If
            End If
        End If
        If rdoPaperHalves2.Checked = True Or rdoPaperHalves3.Checked = True Or rdoPaperHalves13Number.Checked = True Then
            Dim txtName1, txtName2 As TextObject
            txtName1 = rpt.Section1.ReportObjects("txtName1")
            txtName1.Text = NameArCompay
            txtName2 = rpt.Section1.ReportObjects("txtName2")
            txtName2.Text = NameArCompay
        End If
        If rdoMediumBarCode3.Checked = True Then
            txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
            txtNameAr.Text = NameArCompay
        End If

        If rdoMediumBarCodeVAT_50_25.Checked = True Or rdoMediumBarCodeVAT_50_25_13Number.Checked = True Or rdoMediumBarCode38_25.Checked = True Then
            txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
            txtNameAr.Text = NameArCompay
            Dim Type_Currency As String = mykey.GetValue("TypeCurrency", "جنية مصرى")
            txtType_Currency = rpt.Section1.ReportObjects("txtTypeCurrency")
            txtType_Currency.Text = Type_Currency
        End If
        If chkViewPrint.Checked = True Then
            Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
            Frm_PrintReports.Show()
        Else
            'On Error Resume Next
            rpt.PrintToPrinter(1, False, 0, 0)
        End If

        Dt_AddBill.Rows.Clear()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Sub print(ByVal itm_id As String, ByVal group_name As String, ByVal sname As String, ByVal TinPrice As String, ByVal qunt As String)
        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "Sp_PrintParcode"
        cmd.Parameters.Clear()
        cmd.Parameters.AddWithValue("@itm_id", itm_id)
        cmd.Parameters.AddWithValue("@group_name", group_name)
        cmd.Parameters.AddWithValue("@sname", sname)
        cmd.Parameters.AddWithValue("@TinPrice", TinPrice)
        cmd.Parameters.AddWithValue("@qunt", qunt)
        cmd.ExecuteNonQuery()
    End Sub

    Private Sub cmbname_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbname.KeyUp
        If e.KeyCode = 13 Then
            If cmbname.Text.Trim = "" Then Exit Sub
            If Bol = True Then Exit Sub

            txtprc.Text = Cls.Get_Code_Value_Stores("items", "itm_id", "sname", cmbname.Text)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If chkSetWholeSalePrice.Checked = False Then
                Cls.Select_More_Data_Stores("items", "group_name,itm_id,SalPrice", "sname =N'" & cmbname.Text & "'")
            Else
                Cls.Select_More_Data_Stores("items", "group_name,itm_id,WholePrice", "sname =N'" & cmbname.Text & "'")
            End If
            If dr.HasRows = True Then
                cmbcats.Text = dr(0).ToString
                txtprc.Text = dr(1).ToString
                txtprice.Text = dr(2).ToString
            End If

            mykey.SetValue("CodeEAN13Prc", txtprc.Text)

            Dim Qu As String = ""
            Qu = Cls.Get_Code_Value_Stores("items", "store", "sname", txtprc.Text)
            If Qu = "0" Then
                txtqunt.Text = "1"
            Else
                txtqunt.Text = Qu
            End If

            If rdoParcodeA4_4_17.Checked = True Then
                Dim Qunt As Integer = Val(txtqunt.Text) / Val(4)
                txtqunt.Text = Qunt
            End If

            txtqunt.Focus()
            txtqunt.SelectAll()

            'cmbcats.Text = Cls.Get_Code_Value_Stores("items", "group_name", "sname", cmbname.Text)
            'txtprice.Text = Cls.Get_Code_Value_Stores("items", "SalPrice", "sname", cmbname.Text)
            'txtqunt.Text = Cls.Get_Code_Value_Stores("items", "store", "sname", cmbname.Text)

            'Dim ExpirationDate As String = mykey.GetValue("ExpirationDate", "NO")
            'If ExpirationDate = "NO" Then
            '    txtqunt.SelectAll()
            '    txtqunt.Focus()
            'Else
            '    txtValidFor.Focus()
            '    txtValidFor.SelectAll()
            'End If
        End If
    End Sub

    Private Sub cmbname_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbname.SelectedIndexChanged
        If txtprc.Text = "" Then
            ItemsUnityNumber = 0
            If cmbcats.Text.Trim = "" Or cmbname.Text.Trim = "" Then Exit Sub
            If Bol = True Then Exit Sub
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If chkSetWholeSalePrice.Checked = False Then
                Cls.Select_More_Data_Stores("items", "itm_id,SalPrice,CodeEAN13Barre", "group_name =N'" & cmbcats.Text & "' and sname =N'" & cmbname.Text & "'")
            Else
                Cls.Select_More_Data_Stores("items", "itm_id,WholePrice,CodeEAN13Barre", "group_name =N'" & cmbcats.Text & "' and sname =N'" & cmbname.Text & "'")
            End If
            If dr.HasRows = True Then
                txtprc.Text = dr(0).ToString
                txtprice.Text = dr(1).ToString
                txtCodeEAN13Barre.Text = dr(2).ToString
            End If

            mykey.SetValue("CodeEAN13Prc", txtprc.Text)

            Dim Qu As String = ""
            Qu = Cls.Get_Code_Value_Stores("items", "store", "sname", cmbname.Text)
            If Qu = "0" Then
                txtqunt.Text = "1"
            Else
                txtqunt.Text = Qu
            End If

            If rdoParcodeA4_4_17.Checked = True Then
                Dim Qunt As Integer = Val(txtqunt.Text) / Val(4)
                txtqunt.Text = Qunt
            End If


            RateVAT = Cls.Get_Code_Value_Stores("items", "RateVAT", "sname", cmbname.Text)
            If RateVAT <> 0 Then
                Dim VATPrice As String = Format(Val(txtprice.Text) * Val(RateVAT) / 100, "Fixed")
                txtprice.Text = VATPrice
            End If

        End If

        txtqunt.Focus()
        txtqunt.SelectAll()
    End Sub

    Private Sub cmbcats_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbcats.SelectedIndexChanged
        If txtprc.Text = "" Then
            If cmbcats.Text = "" Then GoTo 1
            If Bol = True Then GoTo 1
            Bol = True
            Cls.fill_combo_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbname)
            cmbname.Text = ""
1:
            Bol = False
        End If
    End Sub
    Dim RNXD As Integer

    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click

        If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        For i As Integer = 0 To Dgv_Add.SelectedRows.Count - 1
            RNXD = Dgv_Add.CurrentRow.Index
            Dgv_Add.Rows.RemoveAt(RNXD)
        Next

    End Sub

    Private Sub BtnClear_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnClear.Click
        Dt_AddBill.Rows.Clear()
        Exit Sub
    End Sub

    Private Sub btnPrice_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrice.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If Dgv_Add.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If
        AddReportView()

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintAllItems")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            For m As Integer = 0 To Dgv_Add.Rows(i).Cells(4).Value - 1
                S = "insert into PrintAllItems(Company_Branch_ID,sname,TinPrice) values ("
                S = S & "N'" & Company_Branch_ID & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Dgv_Add.Rows(i).Cells(3).Value & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
        Next
        Dim rpt
        If rdoRow1.Checked = True Then
            rpt = New rptPrintPriceOne
        End If
        If rdoRow2.Checked = True Then
            rpt = New rptPrintPrice2
        End If
        If rdoRow3.Checked = True Then
            rpt = New rptPrintPrice3
        End If
        If rdoRow4.Checked = True Then
            rpt = New rptPrintPrice4
        End If

        Cls.Select_More_Data_Branch_Print("PrintAllItems", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()
        Dt_AddBill.Rows.Clear()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Declare Function WriteProfileString Lib "kernel32" Alias "WriteProfileStringA" _
(ByVal lpszSection As String, ByVal lpszKeyName As String,
ByVal lpszString As String) As Long
    Private Declare Function SendMessage Lib "user32" Alias "SendMessageA" _
         (ByVal hwnd As Long, ByVal wMsg As Long,
         ByVal wParam As Long, ByVal lparam As String) As Long
    Private Const HWND_BROADCAST As Long = &HFFFF&
    Private Const WM_WININICHANGE As Long = &H1A

    Private Function SetDefaultSystemPrinter3(ByVal strPrinterName As String) As Boolean
        'this method does not valid if the change is correct and does not revert to previous printer if wrong
        Dim DeviceLine As String

        'rebuild a valid device line string 
        DeviceLine = strPrinterName & ",,"

        'Store the new printer information in the 
        '[WINDOWS] section of the WIN.INI file for 
        'the DEVICE= item 
        Call WriteProfileString("windows", "Device", DeviceLine)

        'Cause all applications to reload the INI file 
        Call SendMessage(HWND_BROADCAST, WM_WININICHANGE, 0, "windows")

        Return True
    End Function

    Private Sub btnSettingPrinter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSettingPrinter.Click
        FrmSettingPrinterDefault.ShowDialog()
    End Sub

    Private Sub cmbcatsAll_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbcatsAll.KeyUp
        If e.KeyCode = 13 Then
            BtnAddAll.PerformClick()
        End If
    End Sub

    Private Sub BtnAddAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAddAll.Click
        Dim aray_itm_id As New ArrayList
        Dim aray_group_name As New ArrayList
        Dim aray_sname As New ArrayList
        Dim aray_TinPrice As New ArrayList
        Dim aray_qunt As New ArrayList
        Dim aray_Prc As New ArrayList
        Dim aray_PriceIncludesVAT As New ArrayList
        Dim aray_CodeEAN13Barre As New ArrayList

        aray_itm_id.Clear()
        aray_group_name.Clear()
        aray_qunt.Clear()
        aray_sname.Clear()
        aray_TinPrice.Clear()
        aray_Prc.Clear()
        aray_PriceIncludesVAT.Clear()
        aray_CodeEAN13Barre.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        Cls.Select_More_Data_Stores_Loop("items", "itm_id,group_name,sname,SalPrice,store,PriceIncludesVAT,CodeEAN13Barre", "group_name=N'" & cmbcatsAll.Text & "'")
        Do While dr.Read = True
            aray_itm_id.Add(dr(0))
            aray_group_name.Add(dr(1))
            aray_sname.Add(dr(2))
            aray_TinPrice.Add(dr(3))
            aray_qunt.Add(dr(4))
            aray_Prc.Add(dr(0))
            aray_PriceIncludesVAT.Add(dr(5))
            aray_CodeEAN13Barre.Add(dr(6))
        Loop

        Dim Qunt As Integer
        Dim aryRateVAT As String = ""
        Dim TextRateVAT As String = ""
        Dim CodeEAN13Barre As String = ""

        For i As Integer = 0 To aray_itm_id.Count - 1

            Qunt = aray_qunt(i).ToString
            If rdoPaperHalves1.Checked = True Then
                If Val(aray_qunt(i).ToString) = 1 Then
                    Qunt = Val(aray_qunt(i).ToString)
                Else
                    Qunt = Val(aray_qunt(i).ToString) / Val(2)
                End If
            End If
            If rdoPaperHalves2.Checked = True Then
                If Val(aray_qunt(i).ToString) = 1 Then
                    Qunt = Val(aray_qunt(i).ToString)
                Else
                    Qunt = Val(aray_qunt(i).ToString) / Val(2)
                End If
            End If
            If rdoPaperHalves3.Checked = True Then
                If Val(aray_qunt(i).ToString) = 1 Then
                    Qunt = Val(aray_qunt(i).ToString)
                Else
                    Qunt = Val(aray_qunt(i).ToString) / Val(2)
                End If
            End If
            If rdoPaperHalves13Number.Checked = True Then
                If Val(aray_qunt(i).ToString) = 1 Then
                    Qunt = Val(aray_qunt(i).ToString)
                Else
                    Qunt = Val(aray_qunt(i).ToString) / Val(2)
                End If
            End If

            aryRateVAT = aray_PriceIncludesVAT(i).ToString

            If aryRateVAT <> "0" Then
                TextRateVAT = "شامل الضريبة  -"
            Else
                TextRateVAT = ""
            End If

            Dim XParcode As String = ""


            If rdoOneRow13Number.Checked = True Or rdoOneRow13Number2.Checked = True Or rdoPaperHalves13Number.Checked = True Or rdoPaperHalves13Number2.Checked = True Or rdoMediumBarCodeVAT_50_25_13Number.Checked = True Then
                If aray_itm_id(i).Length = 13 Then
                    CodeEAN13Barre = aray_CodeEAN13Barre(i).ToString()
                    If CodeEAN13Barre = "" Then
                        mykey.SetValue("CodeEAN13Prc", aray_itm_id(i))
                        XParcode = mykey.GetValue("CodeEAN13Reg", "CodeEAN13Reg")
                    Else
                        XParcode = CodeEAN13Barre
                    End If
                Else
                    GoTo 2
                End If
            Else
                XParcode = "!" & aray_itm_id(i) & "!"
            End If

            Dgv_Add.DataSource = Fn_AddBill(XParcode, aray_group_name(i), aray_sname(i), aray_TinPrice(i), aray_qunt(i), aray_Prc(i), "", "0", TextRateVAT)
2:
        Next
        If aray_itm_id.Count <> 0 Then
            Dgv_Add.Columns(0).ReadOnly = True
            Dgv_Add.Columns(1).ReadOnly = True
            Dgv_Add.Columns(2).ReadOnly = True
            Dgv_Add.Columns(3).ReadOnly = True
            Dgv_Add.Columns(4).ReadOnly = False
        End If

        Dgv_Add.Columns(5).Visible = False
        Dgv_Add.Columns(6).Visible = False
        Dgv_Add.Columns(8).Visible = False

        cmbname.Focus()
        cmbname.SelectAll()

    End Sub

    Private Sub SetValueDefaultBarcodePaper()
        If chkShowPrice.Checked = True Then
            mykey.SetValue("DefaultBarcodeShowPrice", "YES")
            DefaultBarcodeShowPrice = "YES"
        Else
            mykey.SetValue("DefaultBarcodeShowPrice", "NO")
            DefaultBarcodeShowPrice = "NO"
        End If

        If chkViewPrint.Checked = True Then
            mykey.SetValue("DefaultBarcodeViewPrinter", "YES")
            DefaultBarcodeViewPrinter = "YES"
        Else
            mykey.SetValue("DefaultBarcodeViewPrinter", "NO")
            DefaultBarcodeViewPrinter = "NO"
        End If

        If rdoPaperHalves1.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "PaperHalves1")
            DefaultBarcodePaper = "PaperHalves1"
        End If
        If rdoPaperHalves2.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "PaperHalves2")
            DefaultBarcodePaper = "PaperHalves2"
        End If
        If rdoPaperHalves3.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "PaperHalves3")
            DefaultBarcodePaper = "PaperHalves3"
        End If
        If rdoPaperHalves13Number2.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "OneRow13Number2")
            DefaultBarcodePaper = "OneRow13Number2"
        End If
        If rdoPaperHalves13Number.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "OneRow13Number1")
            DefaultBarcodePaper = "OneRow13Number1"
        End If
        If rdoThreeRows.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "ThreeRows")
            DefaultBarcodePaper = "ThreeRows"
        End If
        If rdoOneRow.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "OneRow")
            DefaultBarcodePaper = "OneRow"
        End If
        If rdoMediumBarCode2.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "MediumBarCode2")
            DefaultBarcodePaper = "MediumBarCode2"
        End If
        If rdoMediumBarCode3.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "MediumBarCode3")
            DefaultBarcodePaper = "MediumBarCode3"
        End If
        If rdoMediumBarCode4.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "MediumBarCode4")
            DefaultBarcodePaper = "MediumBarCode4"
        End If
        If rdoMediumBarCode5.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "MediumBarCode5")
            DefaultBarcodePaper = "MediumBarCode5"
        End If
        If rdoMediumBarCode05_25.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "MediumBarCode05_25")
            DefaultBarcodePaper = "MediumBarCode05_25"
        End If
        If rdoMediumBarCodeAfterDisco3.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "MediumBarCodeAfterDisco3")
            DefaultBarcodePaper = "MediumBarCodeAfterDisco3"
        End If
        If rdoOneRow13Number.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "OneRow13Number")
            DefaultBarcodePaper = "OneRow13Number"
        End If
        If rdoOneRow13Number2.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "OneRow13Number_2")
            DefaultBarcodePaper = "OneRow13Number_2"
        End If
        If rdoParcodeExpirationDate.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "ParcodeExpirationDate")
            DefaultBarcodePaper = "ParcodeExpirationDate"
        End If
        If rdoParcodeA4_4_17.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "ParcodeA4_4_17")
            DefaultBarcodePaper = "ParcodeA4_4_17"
        End If
        If rdoParcodeA4_6_12.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "ParcodeA4_6_12")
            DefaultBarcodePaper = "ParcodeA4_6_12"
        End If
        If rdoParcodeA4_6_16.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "ParcodeA4_6_16")
            DefaultBarcodePaper = "ParcodeA4_6_16"
        End If
        If rdoMediumBarCode5_37.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "ParcodeMediumBarCode5_37")
            DefaultBarcodePaper = "ParcodeMediumBarCode5_37"
        End If
        If rdoMediumBarCode55_40.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "ParcodeMediumBarCode55_40")
            DefaultBarcodePaper = "ParcodeMediumBarCode55_40"
        End If
        If rdoMediumBarCodeProductionDate.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "MediumBarCodeProductionDate")
            DefaultBarcodePaper = "MediumBarCodeProductionDate"
        End If
        If rdoMediumBarCodeVAT_50_25.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "ParcodeMediumBarCodeVAT_5_2_5")
            DefaultBarcodePaper = "ParcodeMediumBarCodeVAT_5_2_5"
        End If
        If rdoMediumBarCodeVAT_50_25_13Number.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "ParcodeMediumBarCodeVAT_5_2_5_13Number")
            DefaultBarcodePaper = "ParcodeMediumBarCodeVAT_5_2_5_13Number"
        End If
        If rdoMediumBarCode38_25.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "ParcodeMediumBarCode_38_25")
            DefaultBarcodePaper = "ParcodeMediumBarCode_38_25"
        End If
        If rdoMediumBarCodeNot38_25.Checked = True Then
            mykey.SetValue("DefaultBarcodePaper", "ParcodeMediumBarCodeNot38_25")
            DefaultBarcodePaper = "ParcodeMediumBarCodeNot38_25"
        End If
    End Sub

    Private Sub rdoPaperHalves1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdoPaperHalves1.CheckedChanged
        ParcodeOneRow()
    End Sub
    Private Sub ParcodeOneRow()
        If rdoPaperHalves1.Checked = True Then
            chkShowPrice.Enabled = False
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = False
        End If
        If rdoPaperHalves2.Checked = True Then
            chkShowPrice.Enabled = False
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = False
        End If
        If rdoThreeRows.Checked = True Then
            chkShowPrice.Enabled = False
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = False
        End If
        If rdoOneRow.Checked = True Then
            chkShowPrice.Enabled = True
            rdoFonts7.Enabled = True
            rdoFonts9.Enabled = True
            chkPriceLE.Enabled = True
        End If
        If rdoMediumBarCode2.Checked = True Then
            chkShowPrice.Enabled = True
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = True
        End If
        If rdoMediumBarCode5.Checked = True Then
            chkShowPrice.Enabled = True
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = True
        End If
        If rdoMediumBarCode05_25.Checked = True Then
            chkShowPrice.Enabled = True
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = True
        End If
        If rdoOneRow13Number.Checked = True Or rdoOneRow13Number2.Checked = True Then
            chkShowPrice.Enabled = True
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = True
        End If
        If rdoParcodeExpirationDate.Checked = True Then
            chkShowPrice.Enabled = False
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = False
        End If
        If rdoPaperHalves3.Checked = True Then
            chkShowPrice.Enabled = True
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = True
        End If
        If rdoParcodeA4_4_17.Checked = True Then
            chkShowPrice.Enabled = True
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = True
        End If
        If rdoMediumBarCodeProductionDate.Checked = True Then
            chkShowPrice.Enabled = False
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = False
        End If
        If rdoPaperHalves13Number.Checked = True Then
            chkShowPrice.Enabled = True
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = False
        End If
        If rdoPaperHalves13Number2.Checked = True Then
            chkShowPrice.Enabled = False
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = False
        End If
        If rdoParcodeA4_6_12.Checked = True Then
            chkShowPrice.Enabled = True
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = True
        End If
        If rdoParcodeA4_6_16.Checked = True Then
            chkShowPrice.Enabled = True
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = True
        End If
        If rdoMediumBarCode3.Checked = True Then
            chkShowPrice.Enabled = False
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = False
        End If
        If rdoMediumBarCode4.Checked = True Then
            chkShowPrice.Enabled = False
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = False
        End If
        If rdoMediumBarCodeAfterDisco3.Checked = True Then
            chkShowPrice.Enabled = False
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = False
        End If
        If rdoMediumBarCodeVAT_50_25.Checked = True Then
            chkShowPrice.Enabled = True
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = False
        End If
        If rdoMediumBarCodeVAT_50_25_13Number.Checked = True Then
            chkShowPrice.Enabled = True
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = False
        End If
        If rdoMediumBarCode38_25.Checked = True Then
            chkShowPrice.Enabled = False
            rdoFonts7.Enabled = False
            rdoFonts9.Enabled = False
            chkPriceLE.Enabled = False
        End If
    End Sub

    Private Sub rdoPaperHalves2_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdoPaperHalves2.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoOneRow_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdoOneRow.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoThreeRows_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdoThreeRows.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub GetValueDefaultBarcodePaper()
        Dim DefaultBarcodeViewPrinter As String = mykey.GetValue("DefaultBarcodeViewPrinter", "NO")
        If DefaultBarcodeViewPrinter = "YES" Then
            chkShowPrice.Checked = True
        Else
            chkShowPrice.Checked = False
        End If

        Dim DefaultBarcodePaper As String = mykey.GetValue("DefaultBarcodePaper", "PaperHalves1")
        If DefaultBarcodePaper = "PaperHalves1" Then
            rdoPaperHalves1.Checked = True
        End If
        If DefaultBarcodePaper = "PaperHalves2" Then
            rdoPaperHalves2.Checked = True
        End If
        If DefaultBarcodePaper = "PaperHalves3" Then
            rdoPaperHalves3.Checked = True
        End If
        If DefaultBarcodePaper = "ThreeRows" Then
            rdoThreeRows.Checked = True
        End If
        If DefaultBarcodePaper = "OneRow" Then
            rdoOneRow.Checked = True
        End If
        If DefaultBarcodePaper = "MediumBarCode2" Then
            rdoMediumBarCode2.Checked = True
        End If
        If DefaultBarcodePaper = "MediumBarCode3" Then
            rdoMediumBarCode3.Checked = True
        End If
        If DefaultBarcodePaper = "MediumBarCode4" Then
            rdoMediumBarCode4.Checked = True
        End If
        If DefaultBarcodePaper = "MediumBarCode5" Then
            rdoMediumBarCode5.Checked = True
        End If
        If DefaultBarcodePaper = "MediumBarCode05_25" Then
            rdoMediumBarCode05_25.Checked = True
        End If
        If DefaultBarcodePaper = "MediumBarCodeAfterDisco3" Then
            rdoMediumBarCodeAfterDisco3.Checked = True
        End If
        If DefaultBarcodePaper = "OneRow13Number" Then
            rdoOneRow13Number.Checked = True
        End If
        If DefaultBarcodePaper = "OneRow13Number_2" Then
            rdoOneRow13Number2.Checked = True
        End If
        If DefaultBarcodePaper = "OneRow13Number2" Then
            rdoPaperHalves13Number2.Checked = True
        End If
        If DefaultBarcodePaper = "OneRow13Number1" Then
            rdoPaperHalves13Number.Checked = True
        End If
        If DefaultBarcodePaper = "ParcodeExpirationDate" Then
            rdoParcodeExpirationDate.Checked = True
        End If
        If DefaultBarcodePaper = "ParcodeA4_4_17" Then
            rdoParcodeA4_4_17.Checked = True
        End If
        If DefaultBarcodePaper = "ParcodeA4_6_12" Then
            rdoParcodeA4_6_12.Checked = True
        End If
        If DefaultBarcodePaper = "ParcodeA4_6_16" Then
            rdoParcodeA4_6_16.Checked = True
        End If
        If DefaultBarcodePaper = "ParcodeMediumBarCode5_37" Then
            rdoMediumBarCode5_37.Checked = True
        End If
        If DefaultBarcodePaper = "ParcodeMediumBarCode55_40" Then
            rdoMediumBarCode55_40.Checked = True
        End If
        If DefaultBarcodePaper = "MediumBarCodeProductionDate" Then
            rdoMediumBarCodeProductionDate.Checked = True
        End If
        If DefaultBarcodePaper = "ParcodeMediumBarCodeVAT_5_2_5" Then
            rdoMediumBarCodeVAT_50_25.Checked = True
        End If
        If DefaultBarcodePaper = "ParcodeMediumBarCodeVAT_5_2_5_13Number" Then
            rdoMediumBarCodeVAT_50_25_13Number.Checked = True
        End If
        If DefaultBarcodePaper = "ParcodeMediumBarCode_38_25" Then
            rdoMediumBarCode38_25.Checked = True
        End If
        If DefaultBarcodePaper = "ParcodeMediumBarCodeNot38_25" Then
            rdoMediumBarCodeNot38_25.Checked = True
        End If
    End Sub

    Private Sub rdoMediumBarCode2_CheckedChanged(sender As Object, e As EventArgs) Handles rdoMediumBarCode2.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoParcodeA4_4_17_CheckedChanged(sender As Object, e As EventArgs) Handles rdoParcodeA4_4_17.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoPaperHalves3_CheckedChanged(sender As Object, e As EventArgs) Handles rdoPaperHalves3.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoOneRow13Number_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdoOneRow13Number.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub ExpirationDate()
        Dim ExpirationDate As String = mykey.GetValue("ExpirationDate", "NO")
        If ExpirationDate = "NO" Then
            txtValidFor.Visible = False
            lblValidFor.Visible = False
            rdoParcodeExpirationDate.Visible = False
            chkExpiration.Visible = False
            'cmbname.Size = New System.Drawing.Size(310, 25)
            'cmbname.Location = New System.Drawing.Point(510, 85)
        Else
            txtValidFor.Visible = True
            lblValidFor.Visible = True
            rdoParcodeExpirationDate.Visible = True
            chkExpiration.Visible = True
        End If
    End Sub

    Private Sub txtprc_KeyUp(sender As Object, e As KeyEventArgs) Handles txtprc.KeyUp
        If e.KeyCode = 13 Then
            ItemsUnityNumber = 0
            If NotUnityItemsProgram = "YES" Then
                ParcodeUnity = txtprc.Text
                Dim PrcUnity As String = Cls.Get_Code_Value("ItemsUnity", "itm_id", "itm_id_Unity", ParcodeUnity)
                If PrcUnity <> "0" Then
                    txtprc.Text = PrcUnity
                End If
            End If

            If txtprc.Text.Trim = "" Then
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                If chkSetWholeSalePrice.Checked = False Then
                    S = "select group_name,sname,SalPrice,CodeEAN13Barre from items where itm_id =N'" & txtprc.Text & "'"
                Else
                    S = "select group_name,sname,WholePrice,CodeEAN13Barre from items where itm_id =N'" & txtprc.Text & "'"
                End If
                cmd.CommandText = S : dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    cmbcats.Text = dr(0)
                    cmbname.Text = dr(1)
                    txtprice.Text = dr(2)
                    txtCodeEAN13Barre.Text = dr(3).ToString
                End If

                If NotUnityItemsProgram = "YES" Then
                    If ItemsUnityNumber <> 1 Then
                        If ItemsUnityNumber <> 0 Then
                            Dim XPrice As String = ""
                            XPrice = Cls.Get_Code_Value("ItemsUnity", "SalPriceUnit", "itm_id_Unity", ParcodeUnity)
                            If XPrice <> "0" Then
                                txtprice.Text = XPrice
                            End If
                        End If
                    End If
                    txtprc.Text = ParcodeUnity
                End If

                mykey.SetValue("CodeEAN13Prc", txtprc.Text)

                Dim Qu As String = ""
                Qu = IM.Get_Itm_Store2(txtprc.Text.Trim)
                If Qu = "0" Then
                    txtqunt.Text = "1"
                Else
                    txtqunt.Text = Qu
                End If

                If rdoParcodeA4_4_17.Checked = True Then
                    Dim Qunt As Integer = Val(txtqunt.Text) / Val(4)
                    txtqunt.Text = Qunt
                End If

                RateVAT = Cls.Get_Code_Value_Stores("items", "RateVAT", "itm_id", txtprc.Text)
                If RateVAT <> 0 Then
                    Dim VATPrice As String = Format(Val(txtprice.Text) * Val(RateVAT) / 100, "Fixed")
                    txtprice.Text = VATPrice
                End If

                txtqunt.Focus()
                txtqunt.SelectAll()
            End If
        End If
    End Sub

    Private Sub rdoMediumBarCodeProductionDate_CheckedChanged(sender As Object, e As EventArgs) Handles rdoMediumBarCodeProductionDate.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub chkExpiration_CheckedChanged(sender As Object, e As EventArgs) Handles chkExpiration.CheckedChanged
        If chkExpiration.Checked = True Then
            txtValidFor.Enabled = False
        Else
            txtValidFor.Enabled = True
        End If
    End Sub

    Private Sub chkProductionDate_CheckedChanged(sender As Object, e As EventArgs) Handles chkProductionDate.CheckedChanged
        If chkProductionDate.Checked = True Then
            dtpProductionDate.Enabled = False
        Else
            dtpProductionDate.Enabled = True
        End If
    End Sub

    Private Sub rdoPaperHalves13Number_CheckedChanged(sender As Object, e As EventArgs) Handles rdoPaperHalves13Number.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoPaperHalves4_CheckedChanged(sender As Object, e As EventArgs) Handles rdoPaperHalves13Number2.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoParcodeA4_6_12_CheckedChanged(sender As Object, e As EventArgs) Handles rdoParcodeA4_6_12.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoMediumBarCode3_CheckedChanged(sender As Object, e As EventArgs) Handles rdoMediumBarCode3.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub cmbname_DropDown(sender As Object, e As EventArgs) Handles cmbname.DropDown
        txtprc.Text = ""
        txtprice.Text = ""
        txtqunt.Text = ""
        txtValidFor.Text = ""
    End Sub

    Private Sub rdoMediumBarCodeAfterDisco3_CheckedChanged(sender As Object, e As EventArgs) Handles rdoMediumBarCodeAfterDisco3.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoMediumBarCode4_CheckedChanged(sender As Object, e As EventArgs) Handles rdoMediumBarCode4.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub chkViewPrint_CheckedChanged(sender As Object, e As EventArgs) Handles chkViewPrint.CheckedChanged
        SetValueDefaultBarcodePaper()
    End Sub

    Private Sub rdoMediumBarCode5_37_CheckedChanged(sender As Object, e As EventArgs) Handles rdoMediumBarCode5_37.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoParcodeExpirationDate_CheckedChanged(sender As Object, e As EventArgs) Handles rdoParcodeExpirationDate.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoMediumBarCodeVAT_CheckedChanged(sender As Object, e As EventArgs) Handles rdoMediumBarCodeVAT_50_25.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoMediumBarCodeVAT_50_25_13Number_CheckedChanged(sender As Object, e As EventArgs) Handles rdoMediumBarCodeVAT_50_25_13Number.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoMediumBarCode38_25_CheckedChanged(sender As Object, e As EventArgs) Handles rdoMediumBarCode38_25.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoOneRow13Number2_CheckedChanged(sender As Object, e As EventArgs) Handles rdoOneRow13Number2.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoMediumBarCode55_40_CheckedChanged(sender As Object, e As EventArgs) Handles rdoMediumBarCode55_40.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoMediumBarCode5_CheckedChanged(sender As Object, e As EventArgs) Handles rdoMediumBarCode5.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoMediumBarCode05_25_CheckedChanged(sender As Object, e As EventArgs) Handles rdoMediumBarCode05_25.CheckedChanged
        ParcodeOneRow()
    End Sub

    Private Sub rdoParcodeA4_6_16_CheckedChanged(sender As Object, e As EventArgs) Handles rdoParcodeA4_6_16.CheckedChanged
        ParcodeOneRow()
    End Sub
End Class