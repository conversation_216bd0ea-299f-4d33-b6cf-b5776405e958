﻿Imports System.Windows.Forms
Imports System.Data
Imports System.Data.SqlClient
Imports vb = Microsoft.VisualBasic
Imports CrystalDecisions.CrystalReports.Engine
Imports System.IO
Public Class Frm_IM_BSal_Edit_Ad
    Dim WithEvents BS As New BindingSource
    Dim Amntcredit, Amntdebit, AmntcreditPrevious, AmntdebitPrevious As Double
    Dim R() As String
    Dim Mnm As String
    Dim _totalItem As Double
    Dim DiscountTax As String
    Dim pay As String
    Dim bill_NoTax As String
    Dim ShowTax As String = mykey.GetValue("SalesTax", "0")
    Dim TextNotActivateEditSalesPrice As String = mykey.GetValue("TextNotActivateEditSalesPrice", "NO")
    Dim NotLoadItemDataScreensOpen As String = mykey.GetValue("NotLoadItemDataScreensOpen", "NO")
    Dim AddBillAuto As String = mykey.GetValue("AddBillAuto", "NO")
    Dim ValueDiscountTax As String = mykey.GetValue("ValueDiscountTax", "0")
    Dim ExpirationDate As String = mykey.GetValue("ExpirationDate", "NO")
    Dim SalesBillNotDiscount As String = mykey.GetValue("SalesBillNotDiscount", "NO")
    Dim SalesBillNotDiscountBill As String = mykey.GetValue("SalesBillNotDiscountBill", "NO")
    Dim ShowCustomerBalanceSalesScreen As String = mykey.GetValue("ShowCustomerBalanceSalesScreen", "NO")
    Dim SalesPricePublic As String = mykey.GetValue("SalesPricePublic", "NO")
    Dim Vendorname As String = ""
    Dim Cls_Altfiqith As New Class_Altfiqith
    Dim CustomerAddress As String = ""
    Dim CustomerTel As String = ""
    Dim DiscountsTin As Double = 0
    Dim DiscountsValue As Double = 0
    Dim TotalDiscountsValue As Double = 0
    Dim StateDisc As String = ""
    Dim aray_itm_id As New ArrayList
    Dim aray_qu As New ArrayList


    Private Sub FillData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id AS [الباركود], itm_cat as [المجموعة] ,itm_name as [اسم الصنف],price as [سعر التجزئة],qu as [1الكمية],qu_unity as [الكمية],itm_Unity as [الوحدة]  ,totalprice as [الأجمالي],Stores as [المخزن],bill_EndDate as [تاريخ الصلاحية],bill_no_Expired as [رقم الصلاحية],Discounts as [الخصم],DiscountsValue as [قيمة الخصم],Discount_Price_After as [سعر البيع] from IM_Bsal_Data where bill_no =N'" & EditItmId & "'"
        dr = cmd.ExecuteReader
        Dgv_Add.DataSource = Cls.PopulateDataView(dr)
        Dgv_Add.Columns(1).Visible = False
        Dgv_Add.Columns(4).Visible = False
        Dgv_Add.Columns(9).Visible = False
        Dgv_Add.Columns(10).Visible = False
        Dgv_Add.Columns(11).Visible = True
        Dgv_Add.Columns(12).Visible = False
        Dgv_Add.Columns(13).Visible = False
        txtCountItems.Text = Dgv_Add.Rows.Count
        If ShowDiscountRateItemSales = "NO" Then
            Dgv_Add.Columns(3).Visible = True
            Dgv_Add.Columns(13).Visible = False
        Else
            Dgv_Add.Columns(3).Visible = False
            Dgv_Add.Columns(13).Visible = True
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,SalesTax,Notes from IM_Bsal where bill_no =N'" & EditItmId & "'"
        dr = cmd.ExecuteReader : dr.Read()
        Dim Xbill_No, XVendorname, Xbill_date, Xbilltime, Xtotalpricebeforedisc, Xdisc, Xtotalpriceafterdisc, Xstat, Xbey, XSTAYING, XSalesTax, XNotes As String
        Xbill_No = dr("bill_No").ToString
        XVendorname = dr("Vendorname").ToString
        Xbill_date = dr("bill_date").ToString
        Xbilltime = dr("billtime").ToString
        Xtotalpricebeforedisc = dr("totalpricebeforedisc").ToString
        Xdisc = dr("disc").ToString
        Xtotalpriceafterdisc = dr("totalpriceafterdisc").ToString
        Xstat = dr("stat").ToString
        Xbey = dr("bey").ToString
        XSTAYING = dr("STAYING").ToString
        XSalesTax = dr("SalesTax").ToString
        If dr(11) Is DBNull.Value Then
        Else
            XNotes = dr("Notes").ToString
        End If

        If Xstat = "نقداً" Then : ChkCash.Checked = True : End If
        If Xstat = "آجل" Then : ChkState2.Checked = True : End If
        If Xstat = "فيزا" Then : chkVisa.Checked = True : End If
        txtbillno.Text = Xbill_No
        cmbCustomer.Text = XVendorname
        DateTimePicker1.Text = Cls.R_date(Xbill_date)
        txttotalpeforedisc.Text = Xtotalpricebeforedisc
        txttotalafterdisc.Text = Xtotalpriceafterdisc
        txtdisc.Text = Xdisc
        txtpaying.Text = Xbey
        txtstaying.Text = XSTAYING
        txtSalestax.Text = XSalesTax
        txtNotes.Text = XNotes

    End Sub

    Private Sub sales_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Me.MdiParent = MDIParent1
        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", cmbCustomer)
        End If
        Cls.FillComboDataAdapterQuickSearch("Items", "sname", cmbname)

        Bra.Fil("groups", "g_name", cmbcats)
        Cls.fill_combo_Branch("stores", "store", cmbStores)
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployees)
        cmbCustomer.Items.Add("نقدا")
        FillData()
        cmbStores.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        PaneldiscBill.Top = 10000
        PanelEmployees.Top = 2000
        Panel_Prices.Top = 10000
        PanelSearch.Top = 5000
        GetShowDiscountRateItemSales()
        GetDateNotBeenActivatedPrograms(DateTimePicker1)
        GetDateNotBeenActivatedOutcome()
    End Sub

    Private Sub GetDateNotBeenActivatedOutcome()
        If PermtionName <> "مدير" Then
            If DateNotBeenActivatedOutcome = "NO" Then
                DateTimePicker1.Enabled = True
            Else
                DateTimePicker1.Enabled = False
            End If
        End If
    End Sub

    Private Sub cmbvendores_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCustomer.KeyUp
        If e.KeyCode = 13 Then
            txtbillno.Focus()
        End If
    End Sub
    Private Sub txtbillno_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtbillno.KeyUp
        If e.KeyCode = 13 Then
            DateTimePicker1.Focus()
        End If
    End Sub
    Private Sub DateTimePicker1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles DateTimePicker1.KeyUp
        If e.KeyCode = 13 Then
            txtprc.Focus()
        End If
    End Sub
    Private Sub cmbcats_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            If cmbcats.Text.Trim = "" Then
                txtdisc.Focus()
            Else
                cmbname.Focus()
            End If
        End If
    End Sub

    Private Sub txtprice_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtprice.KeyUp
        If e.KeyCode = 13 Then
            txtquntUnity.Focus()
        End If
    End Sub

    Private Sub txtprice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtprice.TextChanged
        MyVars.CheckNumber(txtprice)
        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If
        txtTotal.Text = Val(txtprice.Text) * Val(txtquntUnity.Text)
        Dim XTotal As Double = Val(txtTotal.Text)
        txtTotal.Text = Math.Round(XTotal, 2)
    End Sub
    Private Sub txtqunt_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtqunt.KeyUp
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub txtqunt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtqunt.TextChanged
        MyVars.CheckNumber(txtqunt)
        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If

        txtTotal.Text = Val(txtprice.Text) * Val(txtquntUnity.Text)
        Dim XTotal As Double = Val(txtTotal.Text)
        txtTotal.Text = Math.Round(XTotal, 2)
    End Sub
    Function ValidateTextAdd() As Boolean

        If cmbCustomer.Text = "" Then MsgBox("فضلا أختر العميل", MsgBoxStyle.Exclamation) : cmbCustomer.Focus() : Return False
        If txtprc.Text = "" Then MsgBox("فضلا ادخل الباركود", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
        If txtbillno.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الفاتورة", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        If cmbcats.Text = "" Then MsgBox("فضلا حدد المجموعة", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbname.Text = "" Then MsgBox("فضلا أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbname.Focus() : Return False
        If cmbUnityItems.Text = "" Then MsgBox("فضلا أدخل وحدة القياس", MsgBoxStyle.Exclamation) : cmbUnityItems.Focus() : Return False
        If cmbStores.Text = "" Then MsgBox("من فضلك اختر المخزن", MsgBoxStyle.Exclamation) : cmbStores.Focus() : Return False
        If Val(txtprice.Text.Trim) = 0 Then MsgBox("فضلا أدخل السعر", MsgBoxStyle.Exclamation) : txtprice.Focus() : Return False
        If Val(txtqunt.Text.Trim) = 0 Then MsgBox("فضلا أدخل الكمية ", MsgBoxStyle.Exclamation) : txtqunt.Focus() : Return False

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where itm_id =N'" & txtprc.Text.Trim & "'and group_name =N'" & cmbcats.Text.Trim & "'and sname =N'" & cmbname.Text.Trim & "'and Stores =N'" & cmbStores.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H = 0 Then
                MsgBox("الصنف غير مطابق من فضلك راجع الصنف", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
            End If


            Dim ReturnInvoice As String = mykey.GetValue("ReturnInvoice", "NO")
            If ReturnInvoice = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select count(*) from Sales_Bill where bill_no =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
                If H = 0 Then
                    MsgBox("رقم الفاتورة غير مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
                ElseIf H > 0 Then
                    cmd.CommandText = "select count(*) from Sales_Bill where bill_no =N'" & txtbillno.Text.Trim & "' and Vendorname =N'" & cmbCustomer.Text.Trim & "'" : H = cmd.ExecuteScalar
                    If H = 0 Then
                        MsgBox("رقم الفاتورة غير مسجل باسم هذا العميل", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
                    End If
                End If
            End If

            'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            'cmd.CommandText = "select count(*) from IM_Bsal where bill_no =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
            'If H > 0 Then
            '    MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
            'End If

            For i As Integer = 0 To Dt_AddBill.Rows.Count - 1
                If Dgv_Add.Rows(i).Cells(0).Value = txtprc.Text.Trim Then MsgBox("صنف مكرر بنفس الفاتورة", MsgBoxStyle.Exclamation) : txtprc.Focus() : txtprc.SelectAll() : Return False
            Next

            If cmbCustomer.Text <> "نقداً" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select Vendorname from Customers where Vendorname=N'" & cmbCustomer.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = False Then
                    MsgBox("هذا العميل غير مسجل مسبقا", MsgBoxStyle.Exclamation)
                    cmbCustomer.Focus() : Return False
                End If
            End If

            Dim MinimumSalPrice As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select MinimumSalPrice from items where sname=N'" & cmbname.Text & "' and Stores=N'" & cmbStores.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
                Return False
            Else
                If dr(0) Is DBNull.Value Then
                Else
                    MinimumSalPrice = dr(0)
                End If

                If txtprice.Text < MinimumSalPrice Then
                    MsgBox("سعر التجزئة وصل للحد الادنى", MsgBoxStyle.Information)
                    Return False
                End If
            End If

            '==========================================================================================

            Dim ActivationEmail As String = mykey.GetValue("ActivationEmail", "NO")
            Dim XMSG As String
            Dim QuntFrom As Double = 0
            Dim QuntAdd As Double = 0
            Dim XXstore As Double = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)
            Dim Xstore As Double = 0
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                If Dgv_Add.Rows(i).Cells(0).Value = txtprc.Text Then
                    QuntFrom += Dgv_Add.Rows(i).Cells(5).Value
                End If
            Next
            For M As Integer = 0 To aray_itm_id.Count - 1
                If aray_itm_id(M) = txtprc.Text Then
                    QuntAdd += aray_qu(M)
                End If
            Next

            If XXstore < QuntAdd Then
                Xstore = QuntFrom + QuntAdd - XXstore
            Else
                Xstore = XXstore - QuntFrom + QuntAdd
            End If


            Dim UseOnlySales As String = mykey.GetValue("UseOnlySales", "NO")
            If UseOnlySales <> "YES" Then
                If Val(txtqunt.Text) > Xstore Then
                    MsgBox("الكمية بالمخزن لا تكفي الكمية المرتجعة", MsgBoxStyle.Critical) : Return False
                End If

                If Xstore < 1 Then
                    XMSG = MsgBox("الكمية بالمخزن قد نفذت أو أنك لم تقم بتسجيل آخر عملية مشتريات " & Environment.NewLine & " هل تريد إتمام عملية البيع ؟", MsgBoxStyle.OkCancel + MsgBoxStyle.MsgBoxRight + MsgBoxStyle.Exclamation) : txtprice.Focus()
                    If ActivationEmail = "YES" Then
                        SendEmail("الكمية بالمخزن قد نفذت", txtprc.Text, cmbname.Text, Xstore)
                    End If
                    If XMSG = vbCancel Then Return False
                End If

                Dim XMSG2 As String
                If Xstore - Val(txtqunt.Text) = 0 Then
                    XMSG2 = MsgBox("الكمية بالمخزن ستنفذ هل تريد الأستمرار", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
                    If XMSG2 = vbCancel Then Return False
                End If

                If Xstore - Val(txtqunt.Text) < IM.Get_Itm_Range(txtprc.Text.Trim, cmbStores.Text.Trim) Then
                    If ActivationEmail = "YES" Then
                        SendEmail("الكمية وصلت للحد الادنى", txtprc.Text, cmbname.Text, Xstore)
                    End If
                    XMSG = MsgBox("الكمية بالمخزن قد وصلت للحد الأدنى", MsgBoxStyle.Information) : txtprice.Focus()
                End If
            End If


            '=====================================================================


            Return True
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Function

    Function ValidateTextSave() As Boolean
        If cmbCustomer.Text = "" Then MsgBox("فضلا أختر العميل", MsgBoxStyle.Exclamation) : cmbCustomer.Focus() : Return False
        If txtbillno.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الفاتورة", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        If txtdisc.Text = "" Then
            txtdisc.Text = "0"
        End If
        If txtpaying.Text = "" Then
            txtpaying.Text = "0"
        End If
        If Dgv_Add.Rows.Count < 1 Then MsgBox("فضلا أكمل بيانات الفاتورة", MsgBoxStyle.Exclamation) : cmbCustomer.Focus() : Return False

        Try
            Dim ReturnInvoice As String = mykey.GetValue("ReturnInvoice", "NO")
            If ReturnInvoice = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select count(*) from Sales_Bill where bill_no =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
                If H = 0 Then
                    MsgBox("رقم الفاتورة غير مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
                ElseIf H > 0 Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select count(*) from Sales_Bill where bill_no =N'" & txtbillno.Text.Trim & "' and Vendorname =N'" & cmbCustomer.Text.Trim & "'" : H = cmd.ExecuteScalar
                    If H = 0 Then
                        MsgBox("رقم الفاتورة غير مسجل باسم هذا العميل", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
                    End If
                End If
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from IM_Bsal where bill_no =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                If txtbillno.Text.Trim <> EditItmId Then
                    MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
                End If
            End If

            If cmbCustomer.Text <> "نقداً" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select Vendorname from Customers where Vendorname=N'" & cmbCustomer.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = False Then
                    MsgBox("هذا العميل غير مسجل مسبقا", MsgBoxStyle.Exclamation)
                    cmbCustomer.Focus() : Return False
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        Return True
    End Function
    Friend Sub DTV_Width()
        Try
            If Dgv_Add.Rows.Count > 0 Then
                Dgv_Add.Columns(0).Width = 90
                Dgv_Add.Columns(1).Width = 90
                Dgv_Add.Columns(2).Width = 160
                Dgv_Add.Columns(3).Width = 60
                Dgv_Add.Columns(4).Width = 60
                Dgv_Add.Columns(5).Width = 75
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub BtnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAdd.Click
         If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateTextAdd() = False Then Exit Sub
        IM.Store(txtprc.Text.Trim, cmbStores.Text)

        If cmbcats.Text = "" Or cmbname.Text = "" Or txtprice.Text = "" Or txtqunt.Text = "" Then
            MsgBox("اكمل البيانات")
            Exit Sub
        End If

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If txtbill_no_Expired.Text = "0" Then
                cmd.CommandText = "delete from TmpBillsalData where ITM_ID =N'" & txtprc.Text.Trim & "' " : cmd.ExecuteNonQuery()
            Else
                cmd.CommandText = "delete from TmpBillsalData where ITM_ID =N'" & txtprc.Text.Trim & "' and bill_no_Expired =N'" & txtbill_no_Expired.Text.Trim & "' " : cmd.ExecuteNonQuery()
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into TmpBillsalData (bill_no,itm_id,itm_cat,itm_name,price,qu,qu_unity,itm_Unity,totalprice,Stores,UserName,bill_date,bill_EndDate,bill_no_Expired,Discounts,DiscountsValue,Discount_Price_After)"
            S = S & " values (N'" & txtbillno.Text & "',N'" & txtprc.Text & "',N'" & cmbcats.Text.Trim & "',N'" & cmbname.Text.Trim & "',N'" & txtprice.Text & "'," & Val(txtqunt.Text) & "," & Val(txtquntUnity.Text) & ",N'" & cmbUnityItems.Text & "'," & Val(txtTotal.Text) & ",N'" & cmbStores.Text.Trim & "',N'" & UserName & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & txtbill_EndDate.Text & "',N'" & txtbill_no_Expired.Text & "',N'" & txtdiscBill.Text & "',N'" & DiscountsValue & "',N'" & lblDiscount_Price_After.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ITM_ID AS [الباركود], itm_cat as [المجموعة] ,itm_name as [اسم الصنف],price as [سعر التجزئة] ,qu as [1الكمية],qu_unity as [الكمية],itm_Unity as [الوحدة]  ,totalprice as [الأجمالي],Stores as [المخزن],bill_EndDate as [تاريخ الصلاحية],bill_no_Expired as [رقم الصلاحية],Discounts as [الخصم],DiscountsValue as [قيمة الخصم],Discount_Price_After as [سعر البيع] from TmpBillsalData"
            dr = cmd.ExecuteReader
            Dgv_Add.DataSource = Cls.PopulateDataView(dr)

            Dgv_Add.Columns(1).Visible = False
            Dgv_Add.Columns(4).Visible = False
            Dgv_Add.Columns(9).Visible = False
            Dgv_Add.Columns(10).Visible = False
            Dgv_Add.Columns(11).Visible = True
            Dgv_Add.Columns(12).Visible = False
            Dgv_Add.Columns(13).Visible = False
            If ShowDiscountRateItemSales = "NO" Then
                Dgv_Add.Columns(3).Visible = True
                Dgv_Add.Columns(13).Visible = False
            Else
                Dgv_Add.Columns(3).Visible = False
                Dgv_Add.Columns(13).Visible = True
            End If
            txtCountItems.Text = Dgv_Add.Rows.Count
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        ClearAdd() : SumAllPrice() : FocusText()

    End Sub
    Private Sub FocusText()
        Dim FocusText As String = mykey.GetValue("FocusText", "NO")
        If FocusText = "YES" Then
            txtprc.Focus()
        Else
            cmbcats.Focus()
        End If
    End Sub
    Private Sub ClearAdd()
        GrpMain.Enabled = False
        cmbname.Text = ""
        txtqunt.Text = ""
        txtprc.Text = ""
        txtprice.Text = ""
        cmbUnityItems.Text = ""
        txtRateDiscPriceAfter.Text = ""
        txtStore.Text = ""
        txtTotal.Text = ""
    End Sub

    Dim Dt_AddBill As New DataTable
    Friend Function Fn_AddBill(ByVal Col_Prc As String, ByVal Col_Stores As String) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("الباركود", GetType(String))
            Dt_AddBill.Columns.Add("المخزن", GetType(String))
        End If
        Dt_AddBill.Rows.Add(Col_Prc, Col_Stores)
        Return Dt_AddBill
    End Function

    Private Sub Dgv_Add_CellContentClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles Dgv_Add.CellContentClick
        On Error Resume Next
        If e.ColumnIndex = 0 Then
            txtprc.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Prc").Value.ToString
            cmbcats.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Cats").Value.ToString
            cmbname.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Name").Value.ToString
            txtprice.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Price").Value
            txtqunt.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Quant").Value
            Dgv_Add.Rows.RemoveAt(e.RowIndex)
        End If
        SumAllPrice()
    End Sub


    Private Sub BtnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnSave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        SumAllPrice()
        If ValidateTextSave() = False Then Exit Sub

        Dim x As String = MsgBox("هل تريد بالفعل إتمام عملية مرتجعات المبيعات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        Dim STAT As String
        If ChkCash.Checked = True Then : STAT = "نقداً" : End If
        If ChkState2.Checked = True Then : STAT = "آجل" : End If
        If chkVisa.Checked = True Then : STAT = "فيزا" : End If
        Try
            ' XXXXXXXXXXXXXXXXXXXXXXXXXXX
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            'cmd.CommandText = "delete From  IM_Bsal where bill_no =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  IM_Bsal_Data where bill_no =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  IM_Vst where BillNo =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  IM_Vst_disc where TIN_NO =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
            ' XXXXXXXXXXXXXXXXXXXXXXXXXXXXX

            Dim DiscTotal As Double
            If ChkCent.Checked = True Then
                DiscTotal = Val(txttotalpeforedisc.Text) - Val(txttotalafterdisc.Text)
                DiscTotal = Math.Round(DiscTotal, 1)
            Else
                DiscTotal = txtdisc.Text
            End If

            'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            'S = "insert into IM_Bsal(bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,SalesTax,Notes,username) values ("
            'S = S & "N'" & txtbillno.Text.Trim & "' ,N'" & cmbCustomer.Text.Trim & "' ,N'" & Cls.C_date(DateTimePicker1.Text) & "' ,N'" & Cls.get_time(True) & "' ," & Val(txttotalpeforedisc.Text.Trim) & "," & Val(DiscTotal) & " ," & Val(txttotalafterdisc.Text.Trim) & " ,N'" & STAT & "',N'" & txtpaying.Text.Trim & "',N'" & txtstaying.Text.Trim & "',N'" & txtSalestax.Text & "',N'" & txtNotes.Text & "',N'" & UserName & "')"
            'cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal set bill_No =N'" & txtbillno.Text & "',Vendorname =N'" & cmbCustomer.Text & "',bill_date =N'" & Cls.C_date(DateTimePicker1.Text) & "',billtime =N'" & Cls.get_time(True) & "',totalpricebeforedisc =N'" & Val(txttotalpeforedisc.Text.Trim) & "',disc =N'" & Val(DiscTotal) & "',totalpriceafterdisc =N'" & txttotalafterdisc.Text & "',stat =N'" & STAT & "',bey =N'" & txtpaying.Text & "',STAYING =N'" & txtstaying.Text & "',Notes =N'" & txtNotes.Text & "',username =N'" & UserName & "',Treasury_Code =N'" & Treasury_Code & "',EmpName =N'" & cmbEmployees.Text & "',DiscountsValue =N'" & Val(TotalDiscountsValue) & "' where bill_No =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()


            For i As Integer = 0 To Dgv_Add.Rows.Count - 1

                DiscountsTin = Cls.Get_Code_Value_More_ALL_Select("Select Top(100) PERCENT Discounts From dbo.BilltINData Where (itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "') Order By id DESC")

                AverageTinPrice(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value, Dgv_Add.Rows(i).Cells(3).Value, Dgv_Add.Rows(i).Cells(5).Value, Dgv_Add.Rows(i).Cells(6).Value, Dgv_Add.Rows(i).Cells(11).Value, Dgv_Add.Rows(i).Cells(11).Value)

                'Dim bill_no_Expired As String = Cls.Get_Code_Value_More_ALL_Select("Select Top(100) PERCENT MIN(bill_no_Expired) As Exbill_no_Expired, qu_expired, itm_id, Stores, bill_EndDate From dbo.BilltINData Group By itm_id, Stores, bill_EndDate, qu_expired HAVING(itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "') AND (Stores = N'" & Dgv_Add.Rows(i).Cells(8).Value & "') AND (qu_expired <> 0)  ORDER BY MIN(bill_no_Expired)")

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into IM_Bsal_Data (Company_Branch_ID,bill_no,itm_id,itm_cat,itm_name,price,qu,qu_unity,itm_Unity,totalprice,Stores,UserName,bill_date,TinPriceAverage,Profits,TinPrice,Treasury_Code,bill_EndDate,bill_no_Expired,Price_Unity,EmpName,Discounts,DiscountsValue,Discount_Price_After)  values (N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Dgv_Add.Rows(i).Cells(3).Value & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "',N'" & UserName & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & TinPriceAverage & "',N'" & Profits & "',N'" & TinPrice & "',N'" & Treasury_Code & "',N'" & Dgv_Add.Rows(i).Cells(9).Value & "',N'" & Dgv_Add.Rows(i).Cells(10).Value & "',N'" & Price_Unity & "',N'" & cmbEmployees.Text & "',N'" & Dgv_Add.Rows(i).Cells(11).Value & "',N'" & Dgv_Add.Rows(i).Cells(12).Value & "',N'" & Dgv_Add.Rows(i).Cells(13).Value & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                IM.StoreExpired(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value, Dgv_Add.Rows(i).Cells(9).Value.ToString(), Dgv_Add.Rows(i).Cells(10).Value)

            Next
            If Val(txtpaying.Text) > 0 Then
                'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                'cmd.CommandText = "update IM_Vst set Vendorname =N'" & cmbCustomer.Text & "',VND_XTM =N'" & Cls.get_time(True) & "',VND_dt =N'" & Cls.C_date(DateTimePicker1.Text) & "',VND_amx =N'" & txtpaying.Text.Trim & "',UserName =N'" & UserName & "' where billno =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into IM_Vst (Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,billno,VND_no,username,Treasury_Code) values"
                S = S & " (N'" & Company_Branch_ID & "',N'" & cmbCustomer.Text & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(DateTimePicker1.Text) & "'," & txtpaying.Text.Trim & ",N'بفاتورة',N'بفاتورة',N'بفاتورة',N'" & txtbillno.Text & "',N'دفعة نقدية',N'" & UserName & "',N'" & Treasury_Code & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If

            If Val(txtdisc.Text) > 0 Then
                'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                'cmd.CommandText = "update IM_Vst_disc set Vendorname =N'" & cmbCustomer.Text & "',amnt =N'" & Val(DiscTotal) & "',pdate =N'" & Cls.C_date(DateTimePicker1.Text) & "',UserName =N'" & UserName & "' where TIN_NO =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into IM_Vst_disc (Company_Branch_ID,Vendorname,amnt,pdate,det,TIN_NO,username,Treasury_Code) values (N'" & Company_Branch_ID & "',N'" & cmbCustomer.Text.Trim & "'," & Val(DiscTotal) & ",N'" & Cls.C_date(DateTimePicker1.Text) & "',N'خصم على فاتورة مباشرة',N'" & txtbillno.Text.Trim & "',N'" & UserName & "',N'" & Treasury_Code & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        Dim UpdateSalPrice As String = mykey.GetValue("UpdateSalPrice", "YES")
        If UpdateSalPrice = "YES" Then
            UpdatePriceItems()
        End If

        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            IM.Store(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value)

            If ConnectOnlineStore = "YES" Then
                EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
                StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
                Cos.UpdateProductStock(StockOnline, Dgv_Add.Rows(i).Cells(0).Value, EditItmId)
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal_Data set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbillno.Text & "' and itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "' and Stores =N'" & Dgv_Add.Rows(i).Cells(8).Value & "'" : cmd.ExecuteNonQuery()

        Next
        For i As Integer = 0 To DgvDelete.Rows.Count - 1
            IM.Store(DgvDelete.Rows(i).Cells(0).Value, DgvDelete.Rows(i).Cells(1).Value)

            If ConnectOnlineStore = "YES" Then
                EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", DgvDelete.Rows(i).Cells(0).Value)
                StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", DgvDelete.Rows(i).Cells(0).Value)
                Cos.UpdateProductStock(StockOnline, DgvDelete.Rows(i).Cells(0).Value, EditItmId)
            End If
        Next

        Get_Movement_In_Out_Money(DateTimePicker1.Text, Treasury_Code)

        IM.CustomerAccountTotal(cmbCustomer.Text)

        GetDebtorlCreditorPrevious()

        GetDebtorlCreditor()

        Amntcredit = Amntcredit - Amntdebit
        AmntcreditPrevious = AmntcreditPrevious - AmntdebitPrevious

        Try
            Dim CurrentBalanceCustVnd As String = GetCurrentBalanceCustVnd(cmbCustomer.Text)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal set CreditPrevious = " & Val(AmntcreditPrevious) & ",DebitPrevious = " & Val(AmntdebitPrevious) & ",CreditCurrent = " & Val(Amntcredit) & ",DebitCurrent = " & Val(Amntdebit) & ",CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where bill_No =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()

            If Val(txtpaying.Text) > 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update IM_Vst set CreditPrevious = " & Val(AmntcreditPrevious) & ",DebitPrevious = " & Val(AmntdebitPrevious) & ",CreditCurrent = " & Val(Amntcredit) & ",DebitCurrent = " & Val(Amntdebit) & ",CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where BillNo =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
            End If
            If Val(txtdisc.Text) > 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update IM_Vst_disc set CreditPrevious = " & Val(AmntcreditPrevious) & ",DebitPrevious = " & Val(AmntdebitPrevious) & ",CreditCurrent = " & Val(Amntcredit) & ",DebitCurrent = " & Val(Amntdebit) & ",CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where TIN_NO =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        'MsgBox("تمت تعديل مرتجعات المبيعات بنجاح", MsgBoxStyle.Information) : txtprc.Focus()
        If chkprint.Checked = True Then
            PrintReport()
        End If

        ClearSave()
        Dt_AddBill.Rows.Clear()
        Me.Close()
    End Sub
    Private Sub UpdatePriceItems()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                cmd.CommandText = "update items set SalPrice = " & Val(Dgv_Add.Rows(i).Cells(3).Value) & " where itm_id =N'" & Dgv_Add.Rows(i).Cells(0).Value & "'" : cmd.ExecuteNonQuery()
            Next
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
    Private Sub PrintReport()
        Dim txt, txtname, txtNameAr, txtCmpUnderBILL, txtNameEn, txtCmpAddress, txtCmpEmail, txtCmpTel, txtCmpMobile, txtCustomerAddressSales, txtCommercialRecord, txtTaxCard, txtSaleTax, txtDiscountTax, txtCMPNameDown, txtAltfiqith, txtCmpFax, txtEndorsement, txtDelegateName, txtCustomerTel, txtProgramNameBill, txtObjectUserName, txtObjectCommercialAndIndustrialProfitsTax As TextObject
        'Try
        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        Dim XTotalBill As String
        If txtpaying.Text = "0" Then
                XTotalBill = txtstaying.Text
            Else
                XTotalBill = txttotalafterdisc.Text
            End If

            Dim RateDiscount As String
            If ChkCent2.Checked = True Then : RateDiscount = "%" : Else RateDiscount = "$" : End If

            Dim TotalAfterDisc As Double
            If TotalDiscountsValue <> 0 Then
                TotalAfterDisc = Val(txttotalpeforedisc.Text) + TotalDiscountsValue
            Else
                TotalAfterDisc = Val(txttotalpeforedisc.Text)
            End If


        If ShowCustomerAddressSales = "YES" Then
            GetCustomerAddress()
        End If

        Dim BillSerialNumber As Double = 0
        Dim SalPrice As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            BillSerialNumber += 1

            If ShowDiscountRateItemSales = "NO" Then
                SalPrice = Dgv_Add.Rows(i).Cells(3).Value.ToString
            Else
                SalPrice = Dgv_Add.Rows(i).Cells(13).Value.ToString
            End If

            S = "insert into PrintSalesPurchases(Company_Branch_ID,itm_id,itm_cat,itm_name,price,qu,Unity,totalprice,store,BILL_NO,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,BEY,STAYING,vnamntcredit,vnamntdebit,TotalCreditor,TotalDebtor,UserName,Delivery_Date,det,Supervisor_Reform,TotalDisc,Recipient,Received_Date,Name1,Name2,NumberInt1,TotalBeforeDisc,Name10)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & SalPrice & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "',N'" & txtbillno.Text & "',N'" & cmbCustomer.Text & "',N'" & DateTimePicker1.Text & "',N'" & Cls.Get_Time_AM_PM(TxtHour.Text.ToString) & "',N'" & TotalAfterDisc & "',N'" & txtdisc.Text & "',N'" & txttotalafterdisc.Text & "',N'" & txtpaying.Text & "',N'" & txtstaying.Text & "',N'" & Amntcredit & "',N'" & Amntdebit & "',N'" & AmntcreditPrevious & "',N'" & AmntdebitPrevious & "',N'" & XTotalBill & "',N'" & DefaultCurrencyProgram & "',N'" & Dgv_Add.Rows(i).Cells(11).Value & "',N'" & TotalDiscountsValue & "',N'" & Dgv_Add.Rows.Count & "',N'" & UserName & "',N'" & RateDiscount & "',N'" & CustomerTel & "',N'" & CustomerAddress & "',N'" & BillSerialNumber & "',N'" & BillSerialNumber & "',N'" & txtTotalCountItems.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next


        AddReportView()

        Cls.GetDefaultPrinterA4()

        Dim rpt
        If PrintSmall = "YES" Then
            Cls.GetDefaultPrinterBill()
            If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                rpt = New Rpt_SoldSmall
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                rpt = New Rpt_SoldSmall_2
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                rpt = New Rpt_SoldSmall_3
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                rpt = New Rpt_SoldSmall_4
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                rpt = New Rpt_SoldSmall_5
                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                txtDelegateName.Text = cmbEmployees.Text
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                rpt = New Rpt_SoldSmall_6
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                rpt = New Rpt_SoldSmall_7
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                rpt = New Rpt_SoldSmall_8
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                rpt = New Rpt_SoldSmall_9
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                rpt = New Rpt_SoldSmall_10
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                rpt = New Rpt_SoldSmall_11
                If ShowCustomerAddressSales = "YES" Then
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                    End If
                End If
                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                txtDelegateName.Text = cmbEmployees.Text
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                rpt = New Rpt_SoldSmall_12
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                rpt = New Rpt_SoldSmall_13
                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                txtDelegateName.Text = cmbEmployees.Text
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                rpt = New Rpt_SoldSmall_14
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                rpt = New Rpt_SoldSmall_15
                txtObjectUserName = rpt.Section1.ReportObjects("UserName")
                txtObjectUserName.Text = UserName
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                rpt = New Rpt_SoldSmall_16
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                rpt = New Rpt_SoldSmall_17
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                rpt = New Rpt_SoldSmall_18
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                rpt = New Rpt_SoldSmall_19
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                rpt = New Rpt_SoldSmall_20
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                rpt = New Rpt_SoldSmall_21
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                rpt = New Rpt_SoldSmall_10
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                rpt = New Rpt_SoldSmall_10
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                rpt = New Rpt_SoldSmall_24
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                rpt = New Rpt_SoldSmall_10
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                rpt = New Rpt_SoldSmall_24
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                rpt = New Rpt_SoldSmall_24
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                rpt = New Rpt_SoldSmall_24
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                rpt = New Rpt_SoldSmall_24
            End If
            If SalesInvoicePrintingLanguage = "English" Then
                rpt = New Rpt_SoldSmall_EN
            End If
        End If
        If PrintSmall = "NO" Then
            If SalesBillNotDiscountBill = "YES" Then
                If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                    rpt = New Rpt_SalesBill_NotDiscountBill
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                    rpt = New Rpt_SalesBill_2
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                    rpt = New Rpt_SalesBill_3
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                    rpt = New Rpt_SalesBill_4
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                    rpt = New Rpt_SalesBill_5
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                    rpt = New Rpt_SalesBill_6
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                    rpt = New Rpt_SalesBill_7
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                    rpt = New Rpt_SalesBill_8
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                    rpt = New Rpt_SalesBill_9
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                    rpt = New Rpt_SalesBill_10
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                    rpt = New Rpt_SalesBill_11
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                    rpt = New Rpt_SalesBill_Delegate
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                    rpt = New Rpt_SalesBill_Delegate_2
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                    rpt = New Rpt_SalesBill_14
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                    rpt = New Rpt_SalesBill_15
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                    rpt = New Rpt_SalesBill_16
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                    rpt = New Rpt_SalesBill_17
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                    rpt = New Rpt_SalesBill_18
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                    rpt = New Rpt_SalesBill_19
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                    rpt = New Rpt_SalesBill_20
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                    rpt = New Rpt_SalesBill_21
                    If CommercialAndIndustrialProfitsTax = 0 Then
                        txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                        txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                    End If
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                    rpt = New Rpt_SalesBill_22
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                    rpt = New Rpt_SalesBill_23
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                    rpt = New Rpt_SalesBill_24
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                    rpt = New Rpt_SalesBill_25
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                    rpt = New Rpt_SalesBill_26
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                    rpt = New Rpt_SalesBill_27
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                    rpt = New Rpt_SalesBill_28
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                    rpt = New Rpt_SalesBill_29
                End If
                GoTo 10
            End If
            If SalesBillNotDiscount = "YES" Then
                If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                    rpt = New Rpt_SalesBill_NotDiscount
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                            rpt = New Rpt_SalesBill_2
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                    rpt = New Rpt_SalesBill_3
                End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                            rpt = New Rpt_SalesBill_4
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                            rpt = New Rpt_SalesBill_5
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                            rpt = New Rpt_SalesBill_6
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                            rpt = New Rpt_SalesBill_7
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                            rpt = New Rpt_SalesBill_8
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                            rpt = New Rpt_SalesBill_9
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                            rpt = New Rpt_SalesBill_10
                        End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                        rpt = New Rpt_SalesBill_11
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                        rpt = New Rpt_SalesBill_Delegate
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                    rpt = New Rpt_SalesBill_Delegate_2
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                    rpt = New Rpt_SalesBill_14
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                    rpt = New Rpt_SalesBill_15
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                    rpt = New Rpt_SalesBill_16
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                    rpt = New Rpt_SalesBill_17
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                    rpt = New Rpt_SalesBill_18
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                    rpt = New Rpt_SalesBill_19
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                    rpt = New Rpt_SalesBill_20
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                    rpt = New Rpt_SalesBill_21
                    If CommercialAndIndustrialProfitsTax = 0 Then
                        txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                        txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                    End If
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                    rpt = New Rpt_SalesBill_22
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                    rpt = New Rpt_SalesBill_23
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                    rpt = New Rpt_SalesBill_24
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                    rpt = New Rpt_SalesBill_25
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                    rpt = New Rpt_SalesBill_26
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                    rpt = New Rpt_SalesBill_27
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                    rpt = New Rpt_SalesBill_28
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                    rpt = New Rpt_SalesBill_29
                End If
            Else
                    If ShowCustomerBalanceSalesScreen = "YES" Then
                        If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                            rpt = New Rpt_SalesBill
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                            rpt = New Rpt_SalesBill_2
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                            rpt = New Rpt_SalesBill_3
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                            rpt = New Rpt_SalesBill_4
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                            rpt = New Rpt_SalesBill_5
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                            rpt = New Rpt_SalesBill_6
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                            rpt = New Rpt_SalesBill_7
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                            rpt = New Rpt_SalesBill_8
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                            rpt = New Rpt_SalesBill_9
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                            rpt = New Rpt_SalesBill_10
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                            rpt = New Rpt_SalesBill_11
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                            rpt = New Rpt_SalesBill_Delegate
                            txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                            txtEndorsement.Text = CMPEndorsement
                            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                            txtDelegateName.Text = cmbEmployees.Text
                        End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                        rpt = New Rpt_SalesBill_Delegate_2
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                        rpt = New Rpt_SalesBill_14
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                        rpt = New Rpt_SalesBill_15
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                        rpt = New Rpt_SalesBill_16
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                        rpt = New Rpt_SalesBill_17
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                        rpt = New Rpt_SalesBill_18
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                        End If
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                        rpt = New Rpt_SalesBill_19
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                        End If
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                        rpt = New Rpt_SalesBill_20
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                        End If
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                        rpt = New Rpt_SalesBill_21
                        If CommercialAndIndustrialProfitsTax = 0 Then
                            txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                            txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                        End If
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                        rpt = New Rpt_SalesBill_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                        rpt = New Rpt_SalesBill_23
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                        rpt = New Rpt_SalesBill_24
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                        rpt = New Rpt_SalesBill_25
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                        rpt = New Rpt_SalesBill_26
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                        rpt = New Rpt_SalesBill_27
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                        rpt = New Rpt_SalesBill_28
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                        rpt = New Rpt_SalesBill_29
                    End If
                Else
                        If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                            rpt = New Rpt_SalesBill_Cash
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                            rpt = New Rpt_SalesBill_2
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                            rpt = New Rpt_SalesBill_3
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                            rpt = New Rpt_SalesBill_4
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                            rpt = New Rpt_SalesBill_5
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                            rpt = New Rpt_SalesBill_6
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                            rpt = New Rpt_SalesBill_7
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                            rpt = New Rpt_SalesBill_8
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                            rpt = New Rpt_SalesBill_9
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                            rpt = New Rpt_SalesBill_10
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                            rpt = New Rpt_SalesBill_11
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                            rpt = New Rpt_SalesBill_Delegate
                            txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                            txtEndorsement.Text = CMPEndorsement
                            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                            txtDelegateName.Text = cmbEmployees.Text
                        End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                        rpt = New Rpt_SalesBill_Delegate_2
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                        rpt = New Rpt_SalesBill_14
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                        rpt = New Rpt_SalesBill_15
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                        rpt = New Rpt_SalesBill_16
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                        rpt = New Rpt_SalesBill_17
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                        rpt = New Rpt_SalesBill_18
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                        rpt = New Rpt_SalesBill_19
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                        rpt = New Rpt_SalesBill_20
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                        rpt = New Rpt_SalesBill_21
                        If CommercialAndIndustrialProfitsTax = 0 Then
                            txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                            txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                        End If
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                        rpt = New Rpt_SalesBill_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                        rpt = New Rpt_SalesBill_23
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                        rpt = New Rpt_SalesBill_24
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                        rpt = New Rpt_SalesBill_25
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                        rpt = New Rpt_SalesBill_26
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                        rpt = New Rpt_SalesBill_27
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                        rpt = New Rpt_SalesBill_28
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                        rpt = New Rpt_SalesBill_29
                    End If
                End If
10:
                End If
                    txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
                    txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(txttotalafterdisc.Text)

                txtCMPNameDown = rpt.Section1.ReportObjects("txtCMPNameDown")
                txtCMPNameDown.Text = CMPNameDown
                txtCmpFax = rpt.Section1.ReportObjects("txtFax")
                txtCmpFax.Text = CmpFax
            If ShowCustomerAddressSales = "YES" Then
                If CustomerAddress <> "" Then
                    txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                    txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                    txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                    txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                End If
            End If
            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
            txtDelegateName.Text = cmbEmployees.Text
        End If

            If PrintSmall = "A5" Then
                If ShowCustomerBalanceSalesScreen = "YES" Then
                    If SalesPricePublic = "YES" Then
                        rpt = New Rpt_SalesBill_A5_PricePublic
                    Else
                        rpt = New Rpt_SalesBill_A5_1
                    End If
                Else
                    If SalesPricePublic = "YES" Then
                        rpt = New Rpt_SalesBill_BalanceCust_A5_PricePublic
                    Else
                        rpt = New Rpt_SalesBill_BalanceCust_A5
                    End If
                End If

                txtCMPNameDown = rpt.Section1.ReportObjects("txtCMPNameDown")
                txtCMPNameDown.Text = CMPNameDown
                txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
                txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(txttotalafterdisc.Text)
                If ShowTax = "YES" Then
                    txtSaleTax = rpt.Section1.ReportObjects("txtSaleTax")
                    txtSaleTax.Text = txtSalestax.Text & " : ضريبة المبيعات"
                    txtDiscountTax = rpt.Section1.ReportObjects("txtDiscountTax")
                    txtDiscountTax.Text = ValueDiscountTax & " : ضريبة الخصم"
                End If
            End If

            If ShowValueVAT = "YES" Then
                If PrintSmall = "YES" Then
                    rpt = New Rpt_SoldSmall_VAT
                End If
                If PrintSmall = "NO" Then
                    rpt = New Rpt_SalesBill_Cash_VAT
                End If
                If PrintSmall = "A5" Then
                    rpt = New Rpt_SalesBill_BalanceCust_A5_VAT
                End If
            End If

        Cls.Select_More_Data_Branch_Print_Orderby("PrintSalesPurchases", "*", "NumberInt1")
        Dim dt As New DataTable
            dt.Load(dr)
            rpt.SetDataSource(dt)
            txt = rpt.Section1.ReportObjects("txtTitelAddress")
            txt.Text = "تقرير مرتجعات مبيعات"
            txtname = rpt.Section1.ReportObjects("txtName")
            txtname.Text = "أسم العميل"
            txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
            txtNameAr.Text = NameArCompay
            txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
            txtNameEn.Text = NameEnCompany
            txtCmpAddress = rpt.Section1.ReportObjects("txtAddress")
            txtCmpAddress.Text = CmpAddress
            txtCmpEmail = rpt.Section1.ReportObjects("txtEmail")
            txtCmpEmail.Text = CmpEmail
            txtCmpTel = rpt.Section1.ReportObjects("txtTel")
            txtCmpTel.Text = CmpTel
            txtCmpMobile = rpt.Section1.ReportObjects("txtMobile")
            txtCmpMobile.Text = CmpMobile
            If PrintSmall <> "YES" Then
                txtCmpFax = rpt.Section1.ReportObjects("txtFax")
                txtCmpFax.Text = CmpFax
            End If
            txtCommercialRecord = rpt.Section1.ReportObjects("txtCommercialRecord")
        txtCommercialRecord.Text = CMPCommercialRecord
        txtTaxCard = rpt.Section1.ReportObjects("txtTaxCard")
        txtTaxCard.Text = CMPTaxCard

        If HideProgramNameBill = "YES" Then
            txtProgramNameBill = rpt.Section1.ReportObjects("txtProgramNameBill")
            txtProgramNameBill.Text = ""
        Else
            txtProgramNameBill = rpt.Section1.ReportObjects("txtProgramNameBill")
            txtProgramNameBill.Text = ProgramNameBill
        End If

        If SelectLogoPathOther = "YES" Then
                rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
            Else
                rpt.SetParameterValue("ImageURL", CMPLogoPath)
            End If
            Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "فاتورة مرتجعات مبيعات"
        Frm_PrintReports.Show()

            If RunDatabaseInternet = "YES" Then : connect() : End If

        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub
    Sub ForPrintAll(ByVal itm_id As String, ByVal itm_cat As String, ByVal itm_name As String, ByVal price As String, ByVal qu As String, ByVal totalprice As String, ByVal stors As String, _
                ByVal BILL_NO As String, ByVal Vendorname As String, ByVal bill_date As String, ByVal billtime As String, _
                ByVal totalpricebeforedisc As String, ByVal disc As String, ByVal totalpriceafterdisc As String, ByVal BEY As String, ByVal STAYING As String)
        Try
            cmd.CommandType = CommandType.StoredProcedure
            cmd.CommandText = "Sp_PrintSalesPurchases"
            cmd.Parameters.Clear()

            cmd.Parameters.AddWithValue("@itm_id", itm_id)
            cmd.Parameters.AddWithValue("@itm_cat", itm_cat)
            cmd.Parameters.AddWithValue("@itm_name", itm_name)
            cmd.Parameters.AddWithValue("@price", price)
            cmd.Parameters.AddWithValue("@qu", qu)
            cmd.Parameters.AddWithValue("@totalprice", totalprice)
            cmd.Parameters.AddWithValue("@store", stors)
            cmd.Parameters.AddWithValue("@BILL_NO", BILL_NO)
            cmd.Parameters.AddWithValue("@Vendorname", Vendorname)
            cmd.Parameters.AddWithValue("@bill_date", bill_date)
            cmd.Parameters.AddWithValue("@billtime", billtime)
            cmd.Parameters.AddWithValue("@totalpricebeforedisc", totalpricebeforedisc)
            cmd.Parameters.AddWithValue("@disc", disc)
            cmd.Parameters.AddWithValue("@totalpriceafterdisc", totalpriceafterdisc)
            cmd.Parameters.AddWithValue("@BEY", BEY)
            cmd.Parameters.AddWithValue("@STAYING", STAYING)

            cmd.ExecuteNonQuery()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
    Private Sub ClearSave()
        GrpMain.Enabled = True
        cmbCustomer.SelectedIndex = -1
        'cmbUnity.Text = ""
        cmbname.Text = ""
        cmbcats.Text = ""
        txtprice.Text = ""
        txtqunt.Text = ""
        txtbillno.Text = ""
        txtprc.Text = ""
        txttotalafterdisc.Text = ""
        txttotalpeforedisc.Text = ""
        txtdisc.Text = ""
        txtpaying.Text = ""
        txtstaying.Text = ""
        AmntcreditPrevious = "0"
        AmntdebitPrevious = "0"
        Amntdebit = "0"
        Amntcredit = "0"
        txtCountItems.Text = Dgv_Add.Rows.Count
    End Sub
    Private Sub SumAllPrice()
        Try
            Dim SM, SMVAT As Double
            Dim TotalCountItems As Integer
            Dim Price, Qunt, Total, DiscountsValue As Double
            For i As Integer = 0 To Dgv_Add.RowCount - 1
                Price = Dgv_Add.Rows(i).Cells(3).Value
                Qunt = Dgv_Add.Rows(i).Cells(5).Value
                Total = Val(Price) * Val(Qunt)

                SM = SM + Dgv_Add.Rows(i).Cells(7).Value
                DiscountsValue = DiscountsValue + Dgv_Add.Rows(i).Cells(12).Value
                TotalCountItems += Dgv_Add.Rows(i).Cells(5).Value
            Next
            txtTotalCountItems.Text = TotalCountItems
            TotalDiscountsValue = DiscountsValue
            txttotalpeforedisc.Text = SM
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub txttotalpeforedisc_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txttotalpeforedisc.TextChanged
        MyVars.CheckNumber(txttotalpeforedisc)

        sumdisc()
    End Sub
    Private Sub sumdisc()
        Dim DiscVal, TaxVal, TotalVal, TotalDiscountTax As Double
        TaxVal = Format(Val(txttotalpeforedisc.Text) * Val(txtSalestax.Text) / 100, "Fixed")

        TotalDiscountTax = Val((Val(txttotalpeforedisc.Text) * (100 - Val(DiscountTax))) / 100)
        TotalDiscountTax = Val(txttotalpeforedisc.Text) - Val(TotalDiscountTax)
        TotalDiscountTax = Math.Round(TotalDiscountTax, 2)

        If ChkCent.Checked = True Then
            DiscVal = Val((Val(txttotalpeforedisc.Text) * (100 - Val(txtdisc.Text))) / 100)
            DiscVal = Math.Round(DiscVal, 2)
        ElseIf ChkVal.Checked = True Then
            DiscVal = Val(txttotalpeforedisc.Text) - Val(txtdisc.Text)
        End If
        TotalVal = TaxVal + DiscVal - TotalDiscountTax
        txttotalafterdisc.Text = TotalVal

        If ChkState2.Checked = True Then
            txtstaying.Text = Val(txttotalafterdisc.Text) - Val(txtpaying.Text)
        Else
            txtstaying.Text = "0"
        End If
        If txtstaying.Text = "0" Then
            txtpaying.Text = Val(txttotalafterdisc.Text) - Val(txtstaying.Text)
        End If
    End Sub

    Private Sub txtdisc_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtdisc.KeyUp
        If e.KeyCode = 13 Then
            txtpaying.Focus()
        End If
    End Sub

    Private Sub txtdisc_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtdisc.TextChanged
        MyVars.CheckNumber(txtdisc)

        sumdisc()
    End Sub

    Private Sub txttotalafterdisc_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txttotalafterdisc.TextChanged
        MyVars.CheckNumber(txttotalafterdisc)

        sumdisc()
    End Sub

    Private Sub GroupBox3_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles GroupBox3.Enter

    End Sub

    Private Sub txtpaying_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtpaying.KeyUp
        If e.KeyCode = 13 Then
            BtnSave.PerformClick()
        End If
    End Sub

    Private Sub txtpaying_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtpaying.TextChanged
        MyVars.CheckNumber(txtpaying)

        sumdisc()
    End Sub

    Private Sub txtstaying_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtstaying.TextChanged
        MyVars.CheckNumber(txtstaying)

        sumdisc()
    End Sub

    Private Sub txtbillno_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtbillno.TextChanged
        MyVars.CheckNumber(txtbillno)

    End Sub

    Private Sub txtprc_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtprc.KeyUp
        LblStore.ForeColor = Color.Black
        If e.KeyCode = 13 Then
            If txtprc.Text.Trim = "" Then
                BtnSave.PerformClick()
            Else
                Bol = True
                ItemsUnityNumber = 0
                If txtprc.Text.Trim = "" Then Exit Sub

                ParcodeMore = txtprc.Text
                GetBarcodeMore(txtprc.Text) : If ParcodeMore <> "" Then : If ParcodeMore <> 0 Then : txtprc.Text = ParcodeMore : End If : End If
                If ParcodeMore = "0" Then
                    Dim PrcUnity As String = Cls.Get_Code_Value("ItemsUnity", "itm_id", "itm_id_Unity", ParcodeMore)
                    If PrcUnity <> "0" Then
                        txtprc.Text = PrcUnity
                    End If
                End If

                Try
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select group_name , sname,SalPrice,RateVAT from items where itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "' and QuickSearch=0"
                    dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then
                        cmbcats.Text = dr(0).ToString
                        cmbname.Text = dr(1).ToString
                        txtprice.Text = dr(2).ToString
                        ItemsRateVAT = Val(dr("RateVAT").ToString)
                    Else
                        Cls.Select_More_Data_Stores("items", "group_name,sname,SalPrice,RateVAT,Stores", "itm_id=N'" & txtprc.Text & "' and QuickSearch=0")
                        If dr.HasRows = True Then
                            cmbcats.Text = dr("group_name").ToString
                            cmbname.Text = dr("sname").ToString
                            txtprice.Text = dr("SalPrice").ToString
                            ItemsRateVAT = Val(dr("RateVAT").ToString)
                            cmbStores.Text = dr("Stores").ToString
                        End If
                    End If
                Catch ex As Exception
                    ErrorHandling(ex, Me.Text)
                End Try
1:
                txtqunt.Focus()
                txtqunt.Text = 1
                txtqunt.SelectAll()

                txtStore.Text = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)

                WholeasalePrice()

                GetItemsUnity(cmbUnityItems, txtprc.Text)

                Dim ItemsUnity As String = Cls.Get_Code_Value("ItemsUnity", "Unity_Name", "itm_id_Unity", ParcodeMore)
                If ItemsUnity <> "0" Then
                    cmbUnityItems.Text = ItemsUnity
                End If

                SetItemsUnity()

                GetItemsUnityTotalCarton()

                Bol = False
                ParcodeMore = 0
            End If
        End If
    End Sub

    Private Sub cmbname_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbname.DropDown
        cmbname.Text = ""
        txtqunt.Text = ""
        txtprc.Text = ""
        txtprice.Text = ""
        cmbUnityItems.Text = ""
        txtRateDiscPriceAfter.Text = ""
        txtStore.Text = ""
        txtTotal.Text = ""
    End Sub

    Private Sub cmbname_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbname.KeyUp
        If e.KeyCode = 13 Then
            cmbStores.Focus()
        End If
    End Sub

    Private Sub GetDataBsal()
        Try
            Bol = True
            ItemsUnityNumber = 0
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id,group_name,SalPrice,RateVAT,RateDiscSalPriceAfter from items where sname=N'" & cmbname.Text & "' and Stores=N'" & cmbStores.Text & "' and QuickSearch=0"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                txtprc.Text = dr(0).ToString
                cmbcats.Text = dr(1).ToString
                txtprice.Text = dr(2).ToString
                ItemsRateVAT = Val(dr(3).ToString)
                txtRateDiscPriceAfter.Text = Val(dr(4).ToString)
            Else
                Cls.Select_More_Data_Stores("items", "itm_id,group_name,SalPrice,RateVAT,Stores,RateDiscSalPriceAfter", "sname=N'" & cmbname.Text & "' and QuickSearch=0")
                If dr.HasRows = True Then
                    txtprc.Text = dr("itm_id").ToString
                    cmbcats.Text = dr("group_name").ToString
                    txtprice.Text = dr("SalPrice").ToString
                    ItemsRateVAT = Val(dr("RateVAT").ToString)
                    cmbStores.Text = dr("Stores").ToString
                    txtRateDiscPriceAfter.Text = Val(dr("RateDiscSalPriceAfter").ToString)
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        txtStore.Text = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)

        WholeasalePrice()

        GetItemsUnity(cmbUnityItems, txtprc.Text)

        SetItemsUnity()

        GetItemsUnityTotalCarton()

        txtqunt.Text = 1
        txtquntUnity.Text = 1
        txtquntUnity.Focus()
        txtquntUnity.SelectAll()


        Bol = False
    End Sub

    Private Sub cmbname_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbname.SelectedIndexChanged
        If txtprc.Text = "" Then
            GetDataBsal()
        End If
    End Sub

    Private Sub ChkVal_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkVal.CheckedChanged
        sumdisc()
    End Sub

    Private Sub ChkCent_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkCent.CheckedChanged
        sumdisc()
    End Sub
    Dim RNXD As Integer
    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        For i As Integer = 0 To Dgv_Add.SelectedRows.Count - 1
            DgvDelete.DataSource = Fn_AddBill(Dgv_Add.SelectedRows(0).Cells(0).Value, Dgv_Add.SelectedRows(0).Cells(8).Value)
            Try
                Dim ItmID As String
                ItmID = Dgv_Add.SelectedRows(0).Cells(0).Value
                RNXD = Dgv_Add.CurrentRow.Index
                Dgv_Add.Rows.RemoveAt(RNXD) : SumAllPrice() : sumdisc()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete from TmpBillsalData where ITM_ID =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
            Catch ex As Exception
                ErrorHandling(ex, Me.Text)
            End Try
        Next
    End Sub

    Private Sub BtnAddCat_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        FrmCats.Show()
    End Sub

    Private Sub BtnAddVendor_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAddVendor.Click
        frmvendors.Show()
    End Sub

    Private Sub BtnClear_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnClear.Click
        Try
            Dt_AddBill.Rows.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete from TmpBillsalData" : cmd.ExecuteNonQuery()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        SumAllPrice() : sumdisc()
    End Sub

    Private Sub TextBox1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            DateTimePicker1.Focus()
        End If
    End Sub

    Private Sub TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        MyVars.CheckNumber(txtbillno)
    End Sub

    Private Sub cmbvendoresx_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            txtbillno.Focus()
        End If
    End Sub

    Private Sub cmbcats_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbcats.DropDown
        cmbname.Text = ""
        cmbcats.Text = ""
        txtqunt.Text = ""
        txtprc.Text = ""
        txtprice.Text = ""
        'cmbUnity.Text = ""
    End Sub

    Private Sub cmbcats_KeyUp1(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbcats.KeyUp
        If e.KeyCode = 13 Then
            cmbname.Focus()
        End If
    End Sub

    Private Sub cmbcats_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbcats.SelectedIndexChanged
        If cmbname.Text = "" Then
            Try
                If txtprc.Text = "" Then
                    If cmbcats.Text.Trim = "" Then Exit Sub
                    'Cls.fill_combo_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbname)
                    Cls.fill_combo_DataAdapter_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbname)

                    'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    'If Company_Branch_ID = "0" Then
                    '    cmd.CommandText = "select distinct sname from Items where group_name =N'" & cmbcats.Text & "' and QuickSearch=0 order by 1"
                    'Else
                    '    cmd.CommandText = "select distinct sname from Items where group_name =N'" & cmbcats.Text & "' and QuickSearch=0 and Company_Branch_ID = N'" & Company_Branch_ID & "'  or Company_Branch_ID =N'0' order by 1"
                    'End If
                    'dr = cmd.ExecuteReader
                    'Do While dr.Read = True
                    '    cmbname.Items.Add(Trim(dr(0)))
                    'Loop
                    cmbname.Text = ""
                End If
            Catch ex As Exception
                ErrorHandling(ex, Me.Text)
            End Try
        End If

    End Sub

    Private Sub ChkCash_CheckedChanged(sender As Object, e As EventArgs) Handles ChkCash.CheckedChanged
        If ChkCash.Checked = True Then
            pay = txtpaying.Text
            txtpaying.Text = txttotalafterdisc.Text
            txtpaying.ReadOnly = True
        End If
    End Sub

    Private Sub chkVisa_CheckedChanged(sender As Object, e As EventArgs) Handles chkVisa.CheckedChanged
        If chkVisa.Checked = True Then
            pay = txtpaying.Text
            txtpaying.Text = txttotalafterdisc.Text
            txtpaying.ReadOnly = True
        End If
    End Sub

    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        TxtHour.Text = Cls.get_time(True)
    End Sub

    Private Sub cmbStores_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbStores.KeyUp
        If e.KeyCode = 13 Then
            GetDataBsal()
        End If
    End Sub

    Private Sub cmbStores_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles cmbStores.MouseClick
        GetDataBsal()
    End Sub

    Private Sub cmbStores_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbStores.SelectedIndexChanged
        GetDataBsal()
    End Sub

    Public Sub AverageTinPrice(ByVal itm_id As String, ByVal Stores As String, ByVal Price As Double, ByVal Qunt As Double, ByVal Unity As String, ByVal DiscountRate As Double, ByVal PriceDiscount As Double)
        Try
            Dim PriceAverage As String
            TinPriceAverage = 0
            Dim Xqunt As Double = Qunt

            If NotUnityItemsProgram = "YES" Then
                PriceAverage = Cls.Get_Code_Value_Branch_More("ItemsUnity", "TinPriceUnit", "itm_id=N'" & itm_id & "' and Unity_Name=N'" & Unity & "'")
                TinPrice = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)

                Price = Cls.Get_Code_Value_Branch_More("ItemsUnity", "SalPriceUnit", "itm_id=N'" & itm_id & "' and Unity_Name=N'" & Unity & "'")
                Price = Math.Round(Convert.ToDouble(Val(Price)), 2)
            Else
                PriceAverage = Cls.Get_Code_Value_Stores_More("items", "TinPriceAverage", "itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'")
                TinPriceAverage = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)

                PriceAverage = Cls.Get_Code_Value_Stores_More("items", "TinPrice", "itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'")
                TinPrice = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)
            End If

            'If NotUnityItemsProgram = "YES" Then
            '    NumberPieces = Cls.Get_Code_Value_Stores_More("ItemsUnity", "NumberPieces", "itm_id =N'" & itm_id & "' and Unity_Name=N'" & Unity & "'")
            '    If NumberPieces <> 1 Then
            '        Price = Cls.Get_Code_Value_Stores_More("Items", "SalPrice", "itm_id =N'" & itm_id & "' and Stores=N'" & Stores & "'")
            '        If NumberPieces <> 0 Then
            '            Xqunt = Val(NumberPieces) * Val(Qunt)
            '        End If
            '    End If
            'End If

            Price_Unity = Price
            If DiscountRate <> 0 Then
                Price = PriceDiscount
                Price_Unity = PriceDiscount
            End If

            If PriceAverage = "" Then : PriceAverage = 0 : End If

            If LastTinPriceItems = "NO" Then : PriceAverage = TinPriceAverage : Else PriceAverage = TinPrice : End If
            If TinPriceAverage = 0 Then
                PriceAverage = TinPrice
                TinPriceAverage = TinPrice
            End If

            TotalPrice = Price - PriceAverage
            Profits = TotalPrice * Xqunt
            Profits = Math.Round(Profits, 2)

            If DiscountRate <> 0 Then
                If DiscountsTin <> 0 Then
                    Dim TotalDiscountRate As Double = DiscountsTin - DiscountRate
                    Dim TotalRate As Double = Format(Val(PriceAverage) * Val(TotalDiscountRate) / 100, "Fixed")
                    Profits = Format(Val(Xqunt) * Val(TotalRate))
                    Profits = Math.Round(Profits, 2)
                End If
            End If

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub


    Private Declare Function WriteProfileString Lib "kernel32" Alias "WriteProfileStringA" _
    (ByVal lpszSection As String, ByVal lpszKeyName As String,
    ByVal lpszString As String) As Long
    Private Declare Function SendMessage Lib "user32" Alias "SendMessageA" _
         (ByVal hwnd As Long, ByVal wMsg As Long,
         ByVal wParam As Long, ByVal lparam As String) As Long

    Private Sub Dgv_Add_DoubleClick(sender As Object, e As EventArgs) Handles Dgv_Add.DoubleClick
        Try

            If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
            If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            txtprc.Text = Dgv_Add.SelectedRows(0).Cells(0).Value
            cmbcats.Text = Dgv_Add.SelectedRows(0).Cells(1).Value
            cmbname.Text = Dgv_Add.SelectedRows(0).Cells(2).Value
            txtprice.Text = Dgv_Add.SelectedRows(0).Cells(3).Value
            txtqunt.Text = Dgv_Add.SelectedRows(0).Cells(4).Value
            txtquntUnity.Text = Dgv_Add.SelectedRows(0).Cells(5).Value
            cmbUnityItems.Text = Dgv_Add.SelectedRows(0).Cells(6).Value
            cmbStores.Text = Dgv_Add.SelectedRows(0).Cells(8).Value
            txtbill_EndDate.Text = Dgv_Add.SelectedRows(0).Cells(9).Value
            txtbill_no_Expired.Text = Dgv_Add.SelectedRows(0).Cells(10).Value
            txtdiscBill.Text = Dgv_Add.SelectedRows(0).Cells(11).Value
            DiscountsValue = Dgv_Add.SelectedRows(0).Cells(12).Value

            If NotUnityItemsProgram = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 0 : End If
                txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
            Else
                txtqunt.Text = Val(txtquntUnity.Text)
            End If

            txtqunt.Text = Dgv_Add.SelectedRows(0).Cells(4).Value

            txtStore.Text = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)
            txtprice.SelectAll()
            txtprice.Focus()

            Bol = False
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub txtquntUnity_TextChanged(sender As Object, e As EventArgs) Handles txtquntUnity.TextChanged
        cmbUnityItems_SelectedIndexChanged(sender, e)
        MyVars.CheckNumber(txtqunt)
        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If

        txtTotal.Text = Val(txtprice.Text) * Val(txtquntUnity.Text)
        Dim XTotal As Double = Val(txtTotal.Text)
        txtTotal.Text = Math.Round(XTotal, 2)
    End Sub

    Private Sub ChkState2_CheckedChanged(sender As Object, e As EventArgs) Handles ChkState2.CheckedChanged
        If ChkState2.Checked = True Then
            txtpaying.Text = pay
            txtpaying.ReadOnly = False
            txtpaying.Enabled = True
        End If
    End Sub

    Private Sub txtquntUnity_KeyUp(sender As Object, e As KeyEventArgs) Handles txtquntUnity.KeyUp
        If e.KeyCode = 13 Then
            If NotUnityItemsProgram = "YES" Then
                cmbUnityItems.Focus()
                cmbUnityItems.SelectAll()
            Else
                BtnAdd.PerformClick()
            End If
        End If
    End Sub

    Private Sub cmbUnityItems_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbUnityItems.KeyUp
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub cmbUnityItems_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbUnityItems.SelectedIndexChanged
        Try
            If Bol = False Then
                If NotUnityItemsProgram = "YES" Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 0 : End If
                    txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)

                    If ItemsUnityNumber <> 1 Then
                        If ItemsUnityNumber <> 0 Then
                            Dim PriceNumber As String = Cls.Get_Code_Value_Branch_More("ItemsUnity", "SalPriceUnit", "itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'")
                            If PriceNumber <> "0" Then
                                txtprice.Text = PriceNumber
                            End If
                        End If
                    End If
                Else
                    txtqunt.Text = Val(txtquntUnity.Text)
                End If

                GetItemsUnityTotalCarton()

                sumdisc1()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Const HWND_BROADCAST As Long = &HFFFF&
    Private Const WM_WININICHANGE As Long = &H1A

    Private Function SetDefaultSystemPrinter3(ByVal strPrinterName As String) As Boolean
        'this method does not valid if the change is correct and does not revert to previous printer if wrong
        Dim DeviceLine As String

        'rebuild a valid device line string 
        DeviceLine = strPrinterName & ",,"

        'Store the new printer information in the 
        '[WINDOWS] section of the WIN.INI file for 
        'the DEVICE= item 
        Call WriteProfileString("windows", "Device", DeviceLine)

        'Cause all applications to reload the INI file 
        Call SendMessage(HWND_BROADCAST, WM_WININICHANGE, 0, "windows")

        Return True
    End Function

    Private Sub btnClosediscBill_Click(sender As Object, e As EventArgs) Handles btnClosediscBill.Click
        PaneldiscBill.Top = 10000
    End Sub

    Private Sub ChkCent2_CheckedChanged(sender As Object, e As EventArgs) Handles ChkCent2.CheckedChanged
        sumdisc1()
    End Sub

    Private Sub ChkVal2_CheckedChanged(sender As Object, e As EventArgs) Handles ChkVal2.CheckedChanged
        sumdisc1()
    End Sub

    Private Sub sumdisc1()
        Dim DiscVal As Double
        Dim TotalPriseQunt As Double = Val(txtprice.Text) * Val(txtquntUnity.Text)
        If ChkCent2.Checked = True Then
            DiscVal = Val((Val(TotalPriseQunt) * (100 - Val(txtdiscBill.Text))) / 100)
            DiscVal = Math.Round(DiscVal, 2)
        ElseIf ChkVal.Checked = True Then
            DiscVal = Val(TotalPriseQunt) - Val(txtdiscBill.Text)
        End If

        txtTotal.Text = Math.Round(TotalValueVAT, 2) + Math.Round(DiscVal, 2)

        If ChkCent2.Checked = True Then
            StateDisc = "نسبة"
            Dim XVal As String = Format(Val(txtprice.Text) * Val(txtdiscBill.Text) / 100, "Fixed")
            DiscountsValue = Val(XVal) * Val(txtquntUnity.Text)
        Else
            StateDisc = "قيمة"
            DiscountsValue = Val(txtdiscBill.Text)
        End If
    End Sub

    Private Sub txtdiscBill_TextChanged(sender As Object, e As EventArgs) Handles txtdiscBill.TextChanged
        MyVars.CheckNumber(txtdiscBill)
        sumdisc1()
    End Sub

    Private Sub txtdiscBill_MouseDoubleClick(sender As Object, e As MouseEventArgs) Handles txtdiscBill.MouseDoubleClick
        PaneldiscBill.Top = 200
    End Sub

    Private Sub Label22_MouseDoubleClick(sender As Object, e As MouseEventArgs) Handles Label22.MouseDoubleClick
        Dim MyString As String
        MyString = InputBox("أدخل خصم الصنف", "طلب معلومات", Nothing)

        If MyString <> "" Then
            mykey.SetValue("DiscountItems", MyString)
            txtdiscBill.Text = MyString
            For i As Integer = 0 To Dgv_Add.RowCount - 1
                Dgv_Add.Rows(i).Cells(12).Value = MyString
            Next
            sumdisc1()
        End If
    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        PanelEmployees.Top = 2000
    End Sub

    Private Sub btnEmp_Click(sender As Object, e As EventArgs) Handles btnEmp.Click
        PanelEmployees.Top = 4
    End Sub

    Private Sub GetDebtorlCreditorPrevious()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select vnamntcredit , vnamntdebit from Customers where Vendorname=N'" & cmbCustomer.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then Exit Sub
            If dr(0) Is DBNull.Value Or dr(1) Is DBNull.Value Then
            Else
                AmntcreditPrevious = dr("vnamntcredit")
                AmntdebitPrevious = dr("vnamntdebit")
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub btnClosePrices_Click(sender As Object, e As EventArgs) Handles btnClosePrices.Click
        Panel_Prices.Top = 10000
    End Sub

    Private Sub btnPricees_Click(sender As Object, e As EventArgs) Handles btnPricees.Click
        Panel_Prices.Top = 4
    End Sub

    Private Sub SetItemsUnity()
        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 1 : End If
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(txtquntUnity.Text)
        End If
    End Sub

    Private Sub txtdiscBill_KeyUp(sender As Object, e As KeyEventArgs) Handles txtdiscBill.KeyUp
        If ((e.KeyCode = Keys.Enter)) Then
            txtprice.Focus()
        End If
    End Sub

    Private Sub GetItemsUnityTotalCarton()
        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from View_ItemsUnity where itm_id=N'" & txtprc.Text & "' and Unity_Name =N'" & cmbUnityItems.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                NumberPieces = dr(0).ToString
            End If

            Dim Itm_Store As Double = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)
            If NumberPieces = 0 Or NumberPieces = 1 Then
                txtStore.Text = Itm_Store
            Else
                If Itm_Store = 0 And NumberPieces = 0 Then
                    txtStore.Text = 0
                Else
                    txtStore.Text = Val(Itm_Store) / Val(NumberPieces)
                    txtStore.Text = Math.Round(Val(txtStore.Text), 2)
                End If
            End If
        Else
            txtqunt.Text = Val(txtquntUnity.Text)
        End If


    End Sub

    Private Sub GetCustomerAddress()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select addr,tel1 from Customers where Vendorname=N'" & cmbCustomer.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                CustomerAddress = dr(0).ToString
                CustomerTel = dr(1).ToString
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbCustomer_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbCustomer.SelectedIndexChanged
        '==============================================================
        If cmbCustomer.Text <> "نقداً" Then
            Dim PriceType_ID As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PriceType_ID from Customers where Vendorname =N'" & cmbCustomer.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                PriceType_ID = dr("PriceType_ID").ToString
                If PriceType_ID = 1 Then
                    rdoSectors.Checked = True
                End If
                If PriceType_ID = 2 Then
                    rdoWhole.Checked = True
                End If
                If PriceType_ID = 3 Then
                    rdoWholeWhole.Checked = True
                End If
            Else
                rdoSectors.Checked = True
            End If
        Else
            rdoSectors.Checked = True
        End If
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        PanelSearch.Top = 5000
        PanelSearch.Dock = DockStyle.None
        MaximizeButtons = True
    End Sub

    Private Sub btnMaximizeButtons_Click(sender As Object, e As EventArgs) Handles btnMaximizeButtons.Click
        If MaximizeButtons = True Then
            PanelSearch.Dock = DockStyle.Fill
            MaximizeButtons = False
        Else
            PanelSearch.Dock = DockStyle.None
            MaximizeButtons = True
        End If
        PanelSearch.Location = New System.Drawing.Point(5, 200)
        PanelSearch.Size = New System.Drawing.Size(800, 240)

    End Sub

    Private Sub txtsearsh_TextChanged(sender As Object, e As EventArgs) Handles txtsearsh.TextChanged
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
                S = Cls.Get_Select_Grid_S("itm_id As [الباركود],sname as [الاسم],SalPrice As [سعر التجزئة],WholePrice As [سعر الجملة],TinPrice As [سعر الشراء],store as [المخزون]", "items", "Stores=N'" & cmbStores.Text & "'  and QuickSearch=0 and sname <> ''")
            Else
                S = Cls.Get_Select_Grid_S("itm_id as [الباركود],sname as [الاسم],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],TinPrice as [سعر الشراء],store as [المخزون]", "items", "Stores=N'" & cmbStores.Text & "'  and QuickSearch=0 and sname Like N'%" & txtsearsh.Text & "%'")
            End If
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)
            DTGV.Columns(0).Width = 130
            DTGV.Columns(1).Width = 400
            DTGV.Columns(2).Width = 100
            DTGV.Columns(3).Width = 100
            DTGV.Columns(4).Width = 100
            DTGV.Columns(5).Width = 100
            DTGV.Columns(3).Visible = False
            DTGV.Columns(4).Visible = False

            Dim HideQuntItems As String = mykey.GetValue("HideQuntItems", "NO")
            If HideQuntItems = "YES" Then
                DTGV.Columns(5).Visible = False
            Else
                DTGV.Columns(5).Visible = True
            End If

            If PermtionName = "مدير" Then
                DTGV.Columns(3).Visible = True
                DTGV.Columns(4).Visible = True
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbFindCats_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbFindCats.SelectedIndexChanged
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If cmbFindCats.Text = "" Then
                S = Cls.Get_Select_Grid_S("itm_id as [الباركود],sname as [الاسم],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],TinPrice as [سعر الشراء],store as [المخزون]", "items", "Stores=N'" & cmbStores.Text & "' and sname <> ''")
            Else
                S = Cls.Get_Select_Grid_S("itm_id as [الباركود],sname as [الاسم],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],TinPrice as [سعر الشراء],store as [المخزون]", "items", "Stores=N'" & cmbStores.Text & "' and group_name =N'" & cmbFindCats.Text & "'")
            End If
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)
            DTGV.Columns(0).Width = 130
            DTGV.Columns(1).Width = 400
            DTGV.Columns(2).Width = 100
            DTGV.Columns(3).Width = 100
            DTGV.Columns(4).Width = 100
            DTGV.Columns(5).Width = 100
            DTGV.Columns(3).Visible = False
            DTGV.Columns(4).Visible = False

            Dim HideQuntItems As String = mykey.GetValue("HideQuntItems", "NO")
            If HideQuntItems = "YES" Then
                DTGV.Columns(5).Visible = False
            Else
                DTGV.Columns(5).Visible = True
            End If

            If PermtionName = "مدير" Then
                DTGV.Columns(3).Visible = True
                DTGV.Columns(4).Visible = True
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub DTGV_DoubleClick(sender As Object, e As EventArgs) Handles DTGV.DoubleClick
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        txtprc.Text = DTGV.SelectedRows(0).Cells(0).Value
        cmbname.Text = DTGV.SelectedRows(0).Cells(1).Value
        GetDataBsal()
        txtsearsh.Text = ""
    End Sub

    Private Sub btnSearchItems_Click(sender As Object, e As EventArgs) Handles btnSearchItems.Click
        txtsearsh.Focus()
        PanelSearch.Location = New System.Drawing.Point(5, 200)
        PanelSearch.Size = New System.Drawing.Size(800, 240)
        txtsearsh_TextChanged(sender, e)
    End Sub

    Private Sub btnAddNewItems_Click(sender As Object, e As EventArgs) Handles btnAddNewItems.Click
        ActionAddNewItems = True
        FrmItemsNew.ShowDialog()
    End Sub

    Private Sub WholeasalePrice()
        Try
10:
            If rdoSectors.Checked = True Then
                txtprice.Text = Cls.Get_Code_Value_Stores_More("items", "SalPrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
                txtdiscBill.Text = Cls.Get_Code_Value_Stores_More("items", "RateDiscSalPrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
            End If

            If rdoWhole.Checked = True Then
                txtprice.Text = Cls.Get_Code_Value_Stores_More("items", "WholePrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
                txtdiscBill.Text = Cls.Get_Code_Value_Stores_More("items", "RateDiscWholePrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
            End If

            If rdoWholeWhole.Checked = True Then
                txtprice.Text = Cls.Get_Code_Value_Stores_More("items", "WholeWholePrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
                txtdiscBill.Text = Cls.Get_Code_Value_Stores_More("items", "RateDiscWholeWholePrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
            If NetworkName = "Yes" Then
                If UseExternalServer = "Yes" Then
                    connect()
                    GoTo 10
                End If
            End If
        End Try
    End Sub

    Private Sub GetDebtorlCreditor()
        Try
            If AmntcreditPrevious <> 0 Then
                Dim XCredit As Double = AmntcreditPrevious
                Amntcredit = Val(XCredit) - Val(txtstaying.Text)
            End If

            If AmntdebitPrevious <> 0 Then
                Dim XDebit As Double = AmntdebitPrevious
                Amntdebit = Val(XDebit) + Val(txtstaying.Text)
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetShowDiscountRateItemSales()
        If ShowDiscountRateItemSales = "NO" Then
            txtRateDiscPriceAfter.Visible = False
            txtprice.Visible = True
        Else
            txtRateDiscPriceAfter.Visible = True
            txtprice.Visible = False
        End If
    End Sub
End Class
