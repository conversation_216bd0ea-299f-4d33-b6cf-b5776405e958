﻿Imports System.Data.SqlClient
Imports System.Collections.Generic

Public Class frmSalary
    Dim YersMonth As String = ""
    Dim Yers As String = ""
    Dim Month As String = ""
    Dim Day As String = ""
    Dim Week As Double = 0
    Dim Result_Code As String

    Private Sub frmSalary_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If Action = "Edit" Then
            FillData()
            btnSave.Text = "تعديل"
        Else
            MAXRECORD()
            cmbNameEmployee.Focus()
        End If
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbNameEmployee)
        Bra.Fil("EmployeesSalarySystem", "SalarySystemName", cmbSalaryPaymentSystem)
        GetDateNotBeenActivatedPrograms(DateTimePicker1)
        GetDateNotBeenActivatedOutcome()
    End Sub

    Private Sub GetDateNotBeenActivatedOutcome()
        If PermtionName <> "مدير" Then
            If DateNotBeenActivatedOutcome = "NO" Then
                DateTimePicker1.Enabled = True
            Else
                DateTimePicker1.Enabled = False
            End If
        End If
    End Sub

    Private Sub MAXRECORD()
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select * from Salary"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            txtSeries.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(SLYID As float)) as mb FROM Salary"
            dr = cmd.ExecuteReader
            dr.Read()
            Dim sh As Long
            sh = dr("mb")
            txtSeries.Text = sh + 1
        End If
    End Sub

    Private Sub FillData()
        Try
            ' استعلام آمن لتحميل بيانات الراتب
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()

            Using cmd As New SqlCommand()
                cmd.Connection = Cn
                cmd.CommandText = "SELECT SLYID, NameEmployee, Month, BasicSalary, SalarySystem_ID, ValueAdditional, TotalValueAdditional, ValuePremium, TotalValuePremium, ValueIncentive, TotalValueIncentive, Reward, RatherNatureWork, TransferAllowance, HousingAllowance, OtherAddition, ChnagedSalary, Insurances, WorkTax, Advance, DiscountsDelay, DiscountsAbsence, DiscountsSanctions, OtherDiscount, NetSalary FROM Salary WHERE SLYID = @salaryId"
                cmd.Parameters.AddWithValue("@salaryId", EditItmId)

                dr = cmd.ExecuteReader()

                If dr.Read() Then
                    ' تعبئة المتغيرات من البيانات المسترجعة
                    Dim XSLYID As String = If(IsDBNull(dr("SLYID")), "", dr("SLYID").ToString())
                    Dim XNameEmployee As String = If(IsDBNull(dr("NameEmployee")), "", dr("NameEmployee").ToString())
                    Dim XMonth As String = If(IsDBNull(dr("Month")), "", dr("Month").ToString())
                    Dim XBasicSalary As String = If(IsDBNull(dr("BasicSalary")), "", dr("BasicSalary").ToString())
                    Dim XSalarySystem_ID As String = If(IsDBNull(dr("SalarySystem_ID")), "", dr("SalarySystem_ID").ToString())
                    Dim XValueAdditional As String = If(IsDBNull(dr("ValueAdditional")), "", dr("ValueAdditional").ToString())
                    Dim XTotalValueAdditional As String = If(IsDBNull(dr("TotalValueAdditional")), "", dr("TotalValueAdditional").ToString())
                    Dim XValuePremium As String = If(IsDBNull(dr("ValuePremium")), "", dr("ValuePremium").ToString())
                    Dim XTotalValuePremium As String = If(IsDBNull(dr("TotalValuePremium")), "", dr("TotalValuePremium").ToString())
                    Dim XValueIncentive As String = If(IsDBNull(dr("ValueIncentive")), "", dr("ValueIncentive").ToString())
                    Dim XTotalValueIncentive As String = If(IsDBNull(dr("TotalValueIncentive")), "", dr("TotalValueIncentive").ToString())
                    Dim XReward As String = If(IsDBNull(dr("Reward")), "", dr("Reward").ToString())
                    Dim XRatherNatureWork As String = If(IsDBNull(dr("RatherNatureWork")), "", dr("RatherNatureWork").ToString())
                    Dim XTransferAllowance As String = If(IsDBNull(dr("TransferAllowance")), "", dr("TransferAllowance").ToString())
                    Dim XHousingAllowance As String = If(IsDBNull(dr("HousingAllowance")), "", dr("HousingAllowance").ToString())
                    Dim XOtherAddition As String = If(IsDBNull(dr("OtherAddition")), "", dr("OtherAddition").ToString())
                    Dim XChnagedSalary As String = If(IsDBNull(dr("ChnagedSalary")), "", dr("ChnagedSalary").ToString())
                    Dim XInsurances As String = If(IsDBNull(dr("Insurances")), "", dr("Insurances").ToString())
                    Dim XWorkTax As String = If(IsDBNull(dr("WorkTax")), "", dr("WorkTax").ToString())
                    Dim XAdvance As String = If(IsDBNull(dr("Advance")), "", dr("Advance").ToString())
                    Dim XDiscountsDelay As String = If(IsDBNull(dr("DiscountsDelay")), "", dr("DiscountsDelay").ToString())
                    Dim XDiscountsAbsence As String = If(IsDBNull(dr("DiscountsAbsence")), "", dr("DiscountsAbsence").ToString())
                    Dim XDiscountsSanctions As String = If(IsDBNull(dr("DiscountsSanctions")), "", dr("DiscountsSanctions").ToString())
                    Dim XOtherDiscount As String = If(IsDBNull(dr("OtherDiscount")), "", dr("OtherDiscount").ToString())
                    Dim XNetSalary As String = If(IsDBNull(dr("NetSalary")), "", dr("NetSalary").ToString())

                    ' الحصول على اسم نظام الراتب بشكل آمن
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()

                    Dim salarySystemName As String = ""
                    Using cmdSystem As New SqlCommand()
                        cmdSystem.Connection = Cn
                        cmdSystem.CommandText = "SELECT SalarySystemName FROM EmployeesSalarySystem WHERE SalarySystem_ID = @systemId"
                        cmdSystem.Parameters.AddWithValue("@systemId", XSalarySystem_ID)

                        Dim salarySystemResult As Object = cmdSystem.ExecuteScalar()
                        salarySystemName = If(salarySystemResult Is Nothing, "", salarySystemResult.ToString())
                    End Using

                    ' تعبئة النموذج بالبيانات
                    cmbSalaryPaymentSystem.DropDownStyle = ComboBoxStyle.DropDown
                    cmbSalaryPaymentSystem.Text = salarySystemName

                    txtSeries.Text = XSLYID
                    cmbNameEmployee.Text = XNameEmployee
                    cmbMonth.Text = XMonth
                    txtBasicSalary.Text = XBasicSalary
                    txtValueAdditional.Text = XValueAdditional
                    txtValuePremium.Text = XValuePremium
                    txtTotalValuePremium.Text = XTotalValuePremium
                    txtValueIncentive.Text = XValueIncentive
                    txtTotalValueIncentive.Text = XTotalValueIncentive
                    txtReward.Text = XReward
                    txtRatherNatureWork.Text = XRatherNatureWork
                    txtTransferAllowance.Text = XTransferAllowance
                    txtHousingAllowance.Text = XHousingAllowance
                    txtOtherAddition.Text = XOtherAddition
                    txtChnagedSalary.Text = XChnagedSalary
                    txtInsurances.Text = XInsurances
                    txtWorkTax.Text = XWorkTax
                    txtAdvance.Text = XAdvance
                    txtDiscountsDelay.Text = XDiscountsDelay
                    txtDiscountsAbsence.Text = XDiscountsAbsence
                    txtDiscountsSanctions.Text = XDiscountsSanctions
                    txtOtherDiscount.Text = XOtherDiscount
                    txtNetSalary.Text = XNetSalary

                    GetDiscountReward()
                Else
                    ' لا توجد بيانات للراتب
                    MessageBox.Show("لم يتم العثور على بيانات الراتب المحدد", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                End If
            End Using

        Catch ex As Exception
            ' Log error details for debugging
            System.Diagnostics.Debug.WriteLine($"خطأ في تحميل بيانات الراتب - SalaryID: {EditItmId}, Error: {ex.Message}")
            MessageBox.Show("حدث خطأ في تحميل بيانات الراتب: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SumTotal()
        Try
            ' حساب المجاميع بشكل آمن
            Dim totalValueAdditional As Double = Val(txtValueAdditional.Text)
            Dim totalValuePremium As Double = Val(txtValuePremium.Text)
            Dim totalValueIncentive As Double = Val(txtValueIncentive.Text)

            txtTotalValueAdditional.Text = Format(totalValueAdditional, "Fixed")
            txtTotalValuePremium.Text = Format(totalValuePremium, "Fixed")
            txtTotalValueIncentive.Text = Format(totalValueIncentive, "Fixed")

            ' حساب الأجر المتغير
            Dim changedSalary As Double = totalValueAdditional + totalValuePremium + totalValueIncentive +
                                        Val(txtReward.Text) + Val(txtRatherNatureWork.Text) +
                                        Val(txtTransferAllowance.Text) + Val(txtHousingAllowance.Text) +
                                        Val(txtOtherAddition.Text)

            txtChnagedSalary.Text = Format(changedSalary, "Fixed")

            ' حساب التأمينات والراتب الصافي
            Dim basicSalaryValue As Double = Val(txtBasicSalary.Text)

            If chkInsurances.Checked = True Then
                Dim insurances As Double = (basicSalaryValue * 14 / 100) + (changedSalary * 11 / 100)
                txtInsurances.Text = Format(insurances, "Fixed")
            Else
                txtInsurances.Text = "0"
            End If

            ' حساب صافي الراتب
            Dim netSalary As Double = basicSalaryValue + changedSalary - Val(txtInsurances.Text) -
                                    Val(txtWorkTax.Text) - Val(txtAdvance.Text) -
                                    Val(txtDiscountsDelay.Text) - Val(txtDiscountsAbsence.Text) -
                                    Val(txtDiscountsSanctions.Text) - Val(txtOtherDiscount.Text)

            txtNetSalary.Text = Format(netSalary, "Fixed")

        Catch ex As Exception
            ' تسجيل الخطأ
            MessageBox.Show("حدث خطأ في حساب الراتب: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub cmbNameEmployee_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbNameEmployee.KeyUp
        If e.KeyCode = 13 Then
            cmbMonth.Focus()
        End If
    End Sub

    Private Sub cmbMonth_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbMonth.KeyUp
        If e.KeyCode = 13 Then
            cmbSalaryPaymentSystem.Focus()
        End If
    End Sub

    Private Sub txtBasicSalary_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtBasicSalary.KeyUp
        If e.KeyCode = 13 Then
            txtValueAdditional.Focus()
        End If
    End Sub

    Private Sub txtExtraAmount_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtValueAdditional.KeyUp
        If e.KeyCode = 13 Then
            txtValuePremium.Focus()
        End If
    End Sub

    Private Sub txtBonusamount_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtValuePremium.KeyUp
        If e.KeyCode = 13 Then
            txtValueIncentive.Focus()
        End If
    End Sub

    Private Sub txtInducementAmount_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtValueIncentive.KeyUp
        If e.KeyCode = 13 Then
            txtReward.Focus()
        End If
    End Sub

    Private Sub txtRecompense_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtReward.KeyUp
        If e.KeyCode = 13 Then
            txtRatherNatureWork.Focus()
        End If
    End Sub

    Private Sub txtWorkBenefit_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtRatherNatureWork.KeyUp
        If e.KeyCode = 13 Then
            txtTransferAllowance.Focus()
        End If
    End Sub

    Private Sub txtTransportBenefit_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtTransferAllowance.KeyUp
        If e.KeyCode = 13 Then
            txtHousingAllowance.Focus()
        End If
    End Sub

    Private Sub txtHouseBenefit_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtHousingAllowance.KeyUp
        If e.KeyCode = 13 Then
            txtOtherAddition.Focus()
        End If
    End Sub

    Private Sub txtOtherAddition_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtOtherAddition.KeyUp
        If e.KeyCode = 13 Then
            txtWorkTax.Focus()
        End If
    End Sub

    Private Sub txtWorkTax_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtWorkTax.KeyUp
        If e.KeyCode = 13 Then
            txtAdvance.Focus()
        End If
    End Sub

    Private Sub txtAdvnacedPayment_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtAdvance.KeyUp
        If e.KeyCode = 13 Then
            txtDiscountsDelay.Focus()
        End If
    End Sub

    Private Sub txtDelayDiscount_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtDiscountsDelay.KeyUp
        If e.KeyCode = 13 Then
            txtDiscountsAbsence.Focus()
        End If
    End Sub

    Private Sub txtAbsenceDiscount_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtDiscountsAbsence.KeyUp
        If e.KeyCode = 13 Then
            txtDiscountsSanctions.Focus()
        End If
    End Sub

    Private Sub txtPunishmentDiscount_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtDiscountsSanctions.KeyUp
        If e.KeyCode = 13 Then
            txtOtherDiscount.Focus()
        End If
    End Sub

    Private Sub txtOtherDiscount_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtOtherDiscount.KeyUp
        If e.KeyCode = 13 Then
            btnSave.PerformClick()
        End If
    End Sub

    Private Sub txtBasicSalary_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtBasicSalary.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtBasicSalary)
        SumTotal()
    End Sub

    Private Sub txtExtraAmount_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtValueAdditional.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtValueAdditional)
        SumTotal()
    End Sub

    Private Sub txtBonusamount_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtValuePremium.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtValuePremium)
        SumTotal()
    End Sub

    Private Sub txtInducementAmount_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtValueIncentive.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtValueIncentive)
        SumTotal()
    End Sub

    Private Sub txtRecompense_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtReward.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtReward)
        SumTotal()
    End Sub

    Private Sub txtWorkBenefit_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtRatherNatureWork.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtRatherNatureWork)
        SumTotal()
    End Sub

    Private Sub txtTransportBenefit_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTransferAllowance.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtTransferAllowance)
        SumTotal()
    End Sub

    Private Sub txtHouseBenefit_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtHousingAllowance.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtHousingAllowance)
        SumTotal()
    End Sub

    Private Sub txtOtherAddition_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtOtherAddition.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtOtherAddition)
        SumTotal()
    End Sub

    Private Sub txtChnagedSalary_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtChnagedSalary.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtChnagedSalary)
        SumTotal()
    End Sub

    Private Sub txtInsuranceAmount_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtInsurances.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtInsurances)
        SumTotal()
    End Sub

    Private Sub txtWorkTax_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtWorkTax.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtWorkTax)
        SumTotal()
    End Sub

    Private Sub txtAdvnacedPayment_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtAdvance.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtAdvance)
        SumTotal()
    End Sub

    Private Sub txtDelayDiscount_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtDiscountsDelay.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtDiscountsDelay)
        SumTotal()
    End Sub

    Private Sub txtAbsenceDiscount_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtDiscountsAbsence.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtDiscountsAbsence)
        SumTotal()
    End Sub

    Private Sub txtPunishmentDiscount_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtDiscountsSanctions.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtDiscountsSanctions)
        SumTotal()
    End Sub

    Private Sub txtOtherDiscount_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtOtherDiscount.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtOtherDiscount)
        SumTotal()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateTextAdd() = False Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If Action = "Edit" Then
            cmd.CommandText = "delete From  Salary where SLYID =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
        End If

        Chack_Code("EmployeesSalarySystem", "SalarySystem_ID", "SalarySystemName", cmbSalaryPaymentSystem.Text)
        Dim SalarySystem_ID As Integer = Result_Code

        Dim NetSalaryTreasury As Double = Val(txtBasicSalary.Text) - Val(txtInsurances.Text) - Val(txtWorkTax.Text) - Val(txtAdvance.Text)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Salary(Company_Branch_ID,SLYID,NameEmployee,Month,SalarySystem_ID,SalaryDate,BasicSalary,ValueAdditional,TotalValueAdditional,ValuePremium,TotalValuePremium,ValueIncentive,TotalValueIncentive,Reward,RatherNatureWork,TransferAllowance,HousingAllowance,OtherAddition,ChnagedSalary,Insurances,WorkTax,Advance,DiscountsDelay,DiscountsAbsence,DiscountsSanctions,OtherDiscount,NetSalary,NetSalaryTreasury,UserName,Treasury_Code) values ("
        S = S & "N'" & Company_Branch_ID & "',N'" & txtSeries.Text & "',N'" & cmbNameEmployee.Text.Trim & "',N'" & cmbMonth.Text.Trim & "',N'" & SalarySystem_ID & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & txtBasicSalary.Text & "',"
        S = S & "N'" & txtValueAdditional.Text & "',N'" & txtTotalValueAdditional.Text.Trim & "',N'" & txtValuePremium.Text.Trim & "',"
        S = S & "N'" & txtTotalValuePremium.Text.Trim & "',N'" & txtValueIncentive.Text.Trim & "',N'" & txtTotalValueIncentive.Text.Trim & "',"
        S = S & "N'" & txtReward.Text.Trim & "',N'" & txtRatherNatureWork.Text.Trim & "',N'" & txtTransferAllowance.Text.Trim & "',"
        S = S & "N'" & txtHousingAllowance.Text.Trim & "',N'" & txtOtherAddition.Text.Trim & "',N'" & txtChnagedSalary.Text.Trim & "',"
        S = S & "N'" & txtInsurances.Text.Trim & "',N'" & txtWorkTax.Text.Trim & "',N'" & txtAdvance.Text.Trim & "',N'" & txtDiscountsDelay.Text.Trim & "',"
        S = S & "N'" & txtDiscountsAbsence.Text.Trim & "',N'" & txtDiscountsSanctions.Text.Trim & "',N'" & txtOtherDiscount.Text.Trim & "',N'" & txtNetSalary.Text.Trim & "',N'" & NetSalaryTreasury & "',N'" & UserName & "',N'" & Treasury_Code & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()


        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "update EmployeesDiscountReward set ChackAccount =1" : cmd.ExecuteNonQuery()

        '===============================================================================
        Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
        If UseAccounts = "YES" Then
            Daily_Restrictions()
        End If
        '===============================================================================

        Get_Movement_In_Out_Money(DateTimePicker1.Text, Treasury_Code)

        If Action = "Edit" Then
            Action = "Add"
            Me.Close()
            frmShowSalary.btnShow.PerformClick()
        Else
            MsgBox("تمت عملية الحفظ بنجاح", MsgBoxStyle.Information)
            ClearSave()
            MAXRECORD()
            Cls.fill_combo_Branch("Employees", "NameEmployee", cmbNameEmployee)
        End If
    End Sub

    Function ValidateTextAdd() As Boolean
        If cmbNameEmployee.Text = "" Then MsgBox("فضلا ادخل إسم الموظف", MsgBoxStyle.Exclamation) : cmbNameEmployee.Focus() : Return False
        If cmbMonth.Text = "" Then MsgBox("فضلا ادخل الشهر", MsgBoxStyle.Exclamation) : cmbMonth.Focus() : Return False
        If txtBasicSalary.Text = "" Then MsgBox("فضلا ادخل المرتب الاساسى", MsgBoxStyle.Exclamation) : txtBasicSalary.Focus() : Return False
        Return True
    End Function

    Private Sub ClearSave()
        Cls.clear(Me)
        cmbNameEmployee.Focus()
    End Sub

    Private Sub txtTotalValueAdditional_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTotalValueAdditional.TextChanged
        SumTotal()
    End Sub

    Private Sub txtTotalValuePremium_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTotalValuePremium.TextChanged
        SumTotal()
    End Sub

    Private Sub txtTotalValueIncentive_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTotalValueIncentive.TextChanged
        SumTotal()
    End Sub

    Private Sub chkInsurances_CheckedChanged(sender As Object, e As EventArgs) Handles chkInsurances.CheckedChanged
        SumTotal()
    End Sub

    Private Sub Daily_Restrictions()
        Dim Account As String = ""
        Dim AccountCode As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مرتبات'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            Account = dr("Link_AccountsTree")
            AccountCode = dr("ACCNumber")
        End If

        '========================================================================================

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into MOVESDATA (MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName,Treasury_Code)  values ("
        S = S & "N'0',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCode & "',N'" & Account & "',N'0',N'" & txtNetSalary.Text & "',N'" & Account & "',N'" & UserName & "',N'" & Treasury_Code & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()


    End Sub

    Private Sub GetDiscountReward()

        Dim aray_2 As New ArrayList
        Dim aray_3 As New ArrayList

        Dim PRC As String = DateTimePicker1.Text
        Dim split As String() = New String() {"/"}
        Dim itemsSplit As String() = PRC.Split(split, StringSplitOptions.None)
        Yers = itemsSplit(0).ToString()
        Month = itemsSplit(1).ToString()
        Day = itemsSplit(2).ToString()


        Chack_Code("EmployeesSalarySystem", "SalarySystem_ID", "SalarySystemName", cmbSalaryPaymentSystem.Text)
        Dim SalarySystem_ID As Integer = Result_Code


        Dim DiscountReward_Type As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If SalarySystem_ID = 1 Then
            cmd.CommandText = "Select dbo.Employees.NameEmployee, dbo.EmployeesDiscountReward.Month, dbo.EmployeesDiscountReward.Years, dbo.EmployeesDiscountReward_Type.DiscountReward_Type, dbo.EmployeesDiscountReward.Amount,   dbo.EmployeesDiscountReward.Recase   From dbo.EmployeesDiscountReward INNER Join dbo.EmployeesDiscountReward_Type On dbo.EmployeesDiscountReward.DiscountReward_Type_ID = dbo.EmployeesDiscountReward_Type.DiscountReward_Type_ID INNER Join dbo.Employees On dbo.EmployeesDiscountReward.EMPID = dbo.Employees.EMPID  Where (dbo.Employees.NameEmployee = N'" & cmbNameEmployee.Text & "') AND (dbo.EmployeesDiscountReward.Month = N'" & cmbMonth.Text & "') AND (dbo.EmployeesDiscountReward.Years = N'" & Yers & "')"
        End If
        If SalarySystem_ID = 2 Then
            cmd.CommandText = "Select dbo.Employees.NameEmployee, dbo.EmployeesDiscountReward.Month, dbo.EmployeesDiscountReward.Years, dbo.EmployeesDiscountReward_Type.DiscountReward_Type, dbo.EmployeesDiscountReward.Amount,  dbo.EmployeesDiscountReward.Recase, dbo.EmployeesSalarySystem.SalarySystem_ID, dbo.EmployeesSalarySystem.SalarySystemName From dbo.EmployeesDiscountReward INNER Join  dbo.EmployeesDiscountReward_Type ON dbo.EmployeesDiscountReward.DiscountReward_Type_ID = dbo.EmployeesDiscountReward_Type.DiscountReward_Type_ID INNER Join dbo.Employees ON dbo.EmployeesDiscountReward.EMPID = dbo.Employees.EMPID INNER Join  dbo.EmployeesSalarySystem ON dbo.EmployeesDiscountReward.SalarySystem_ID = dbo.EmployeesSalarySystem.SalarySystem_ID Where (dbo.Employees.NameEmployee = N'" & cmbNameEmployee.Text & "') AND (dbo.EmployeesDiscountReward.Month = N'" & cmbMonth.Text & "') AND (dbo.EmployeesDiscountReward.Years = N'" & Yers & "') AND   (dbo.EmployeesSalarySystem.SalarySystem_ID = N'" & SalarySystem_ID & "')"
        End If
        If SalarySystem_ID <> 1 And SalarySystem_ID <> 2 Then
            cmd.CommandText = "Select dbo.Employees.NameEmployee, dbo.EmployeesDiscountReward.Month, dbo.EmployeesDiscountReward.Years, dbo.EmployeesDiscountReward_Type.DiscountReward_Type, dbo.EmployeesDiscountReward.Amount,  dbo.EmployeesDiscountReward.Recase, dbo.EmployeesSalarySystem.SalarySystem_ID, dbo.EmployeesSalarySystem.SalarySystemName From dbo.EmployeesDiscountReward INNER Join  dbo.EmployeesDiscountReward_Type ON dbo.EmployeesDiscountReward.DiscountReward_Type_ID = dbo.EmployeesDiscountReward_Type.DiscountReward_Type_ID INNER Join dbo.Employees ON dbo.EmployeesDiscountReward.EMPID = dbo.Employees.EMPID INNER Join  dbo.EmployeesSalarySystem ON dbo.EmployeesDiscountReward.SalarySystem_ID = dbo.EmployeesSalarySystem.SalarySystem_ID Where (dbo.Employees.NameEmployee = N'" & cmbNameEmployee.Text & "') AND (dbo.EmployeesDiscountReward.Month = N'" & cmbMonth.Text & "') AND (dbo.EmployeesDiscountReward.Years = N'" & Yers & "') AND   (dbo.EmployeesSalarySystem.SalarySystem_ID = N'" & SalarySystem_ID & "')"
        End If
        'cmd.CommandText = "Select dbo.Employees.NameEmployee, dbo.EmployeesDiscountReward.Month, dbo.EmployeesDiscountReward.Years, dbo.EmployeesDiscountReward_Type.DiscountReward_Type, dbo.EmployeesDiscountReward.Amount,   dbo.EmployeesDiscountReward.Recase   From dbo.EmployeesDiscountReward INNER Join dbo.EmployeesDiscountReward_Type On dbo.EmployeesDiscountReward.DiscountReward_Type_ID = dbo.EmployeesDiscountReward_Type.DiscountReward_Type_ID INNER Join dbo.Employees On dbo.EmployeesDiscountReward.EMPID = dbo.Employees.EMPID  Where (dbo.Employees.NameEmployee = N'" & cmbNameEmployee.Text & "') AND (dbo.EmployeesDiscountReward.Month = N'" & cmbMonth.Text & "') AND (dbo.EmployeesDiscountReward.Years = N'" & Yers & "')"
        dr = cmd.ExecuteReader()
        Do While dr.Read = True
            aray_2.Add(dr("DiscountReward_Type"))
            aray_3.Add(dr("Amount"))
        Loop

        Dim SM1, SM2, SM3, SM4, SM5, SM6, SM7, SM8, SM9, SM10 As Double
        For i As Integer = 0 To aray_2.Count - 1
            If aray_2(i).ToString = "سلفة" Then
                SM1 += aray_3(i).ToString
                txtAdvance.Text = SM1
            End If
            If aray_2(i).ToString = "خصومات تأخير" Then
                SM2 += aray_3(i).ToString
                txtDiscountsDelay.Text = SM2
            End If

            If aray_2(i).ToString = "خصومات غياب" Then
                SM3 += aray_3(i).ToString
                txtDiscountsAbsence.Text = SM3
            End If
            If aray_2(i).ToString = "خصومات جزاءات" Then
                SM4 += aray_3(i).ToString
                txtDiscountsSanctions.Text = SM4
            End If
            If aray_2(i).ToString = "مكأفئة" Then
                SM5 += aray_3(i).ToString
                txtReward.Text = SM5
            End If
            If aray_2(i).ToString = "علاوة" Then
                SM6 += aray_3(i).ToString
                txtValuePremium.Text = SM6
            End If
            If aray_2(i).ToString = "بدل نقل" Then
                SM7 += aray_3(i).ToString
                txtTransferAllowance.Text = SM7
            End If
            If aray_2(i).ToString = "بدل طبيعة عمل" Then
                SM8 += aray_3(i).ToString
                txtRatherNatureWork.Text = SM8
            End If
            If aray_2(i).ToString = "بدل سكن" Then
                SM9 += aray_3(i).ToString
                txtHousingAllowance.Text = SM9
            End If
            If aray_2(i).ToString = "إضافات أخرى" Then
                SM10 += aray_3(i).ToString
                txtOtherAddition.Text = SM10
            End If
        Next

        GetIncreaseDeficitAmount()

    End Sub

    Private Sub GetBasicSalary()
        Dim DiscountReward_Type As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Emp_Salary from Employees where NameEmployee=N'" & cmbNameEmployee.Text & "'"
        dr = cmd.ExecuteReader()
        If dr.Read = True Then
            txtBasicSalary.Text = dr("Emp_Salary").ToString
        End If
    End Sub


    Private Sub cmbMonth_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbMonth.SelectedIndexChanged
        GetDiscountReward()
    End Sub

    Private Sub GetIncreaseDeficitAmount()
        SplitYersMonth()
        Dim DeficitAmount, IncreaseAmount, TotalDeficitAmount, TotalIncreaseAmount As Double
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "select sum(Amount) from Employee_Deficit_Increase where Deficit_Increase_Code =1 and Employee_Name=N'" & cmbNameEmployee.Text & "' and  Emp_Date >=N'" & Cls.C_date(YersMonth) & "' and Emp_Date <=N'" & Cls.C_date(DateTimePicker1.Text) & "'"
        cmd.CommandText = S
        dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then DeficitAmount = 0 Else DeficitAmount = dr(0)
        '==========================================================================================
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "select sum(Amount) from Employee_Deficit_Increase where Deficit_Increase_Code =2 and Employee_Name=N'" & cmbNameEmployee.Text & "' and  Emp_Date >=N'" & Cls.C_date(YersMonth) & "' and Emp_Date <=N'" & Cls.C_date(DateTimePicker1.Text) & "'"
        cmd.CommandText = S
        dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then IncreaseAmount = 0 Else IncreaseAmount = dr(0)

        If DeficitAmount > IncreaseAmount Then
            TotalDeficitAmount = Val(DeficitAmount) - Val(IncreaseAmount)
        Else
            TotalIncreaseAmount = Val(IncreaseAmount) - Val(DeficitAmount)
        End If
        Dim Discount As Long = Val(txtOtherDiscount.Text)
        Dim Addition As Long = Val(txtOtherAddition.Text)

        Discount += TotalDeficitAmount
        Addition += TotalIncreaseAmount

        txtOtherDiscount.Text = Discount
        txtOtherAddition.Text = Addition

    End Sub

    Private Sub SplitYersMonth()
        Dim PRC As String = DateTimePicker1.Text
        Dim split As String() = New String() {"/"}
        Dim itemsSplit As String() = PRC.Split(split, StringSplitOptions.None)
        Dim Yers As String = itemsSplit(0).ToString()
        Dim XMonth As String = itemsSplit(1).ToString()
        YersMonth = Yers & "/" & XMonth & "/" & "01"
    End Sub

    Private Sub ClearText()
        txtAdvance.Text = ""
        txtChnagedSalary.Text = ""
        txtDiscountsAbsence.Text = ""
        txtDiscountsDelay.Text = ""
        txtDiscountsSanctions.Text = ""
        txtHousingAllowance.Text = ""
        txtInsurances.Text = ""
        txtNetSalary.Text = ""
        txtOtherAddition.Text = ""
        txtOtherDiscount.Text = ""
        txtRatherNatureWork.Text = ""
        txtReward.Text = ""
        txtTransferAllowance.Text = ""
        txtValueAdditional.Text = ""
        txtValueIncentive.Text = ""
        txtValuePremium.Text = ""
        txtWorkTax.Text = ""
    End Sub

    Private Sub cmbMonth_DropDown(sender As Object, e As EventArgs) Handles cmbMonth.DropDown
        ClearText()
    End Sub

    Private Sub cmbNameEmployee_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbNameEmployee.SelectedIndexChanged
        GetBasicSalary()
    End Sub

    Private Sub BtnFind_Click(sender As Object, e As EventArgs) Handles BtnFind.Click
        frmShowSalary.Show()
    End Sub

    Private Sub cmbSalaryPaymentSystem_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbSalaryPaymentSystem.KeyUp
        If e.KeyCode = 13 Then
            txtBasicSalary.Focus()
        End If
    End Sub

    Private Sub Chack_Code(ByVal Table As String, ByVal Code As String, ByVal Name As String, ByVal TextBox As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select " & Code & " from " & Table & " where " & Name & "=N'" & TextBox & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            Result_Code = dr(0)
        End If
    End Sub

    Private Sub cmbSalaryPaymentSystem_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbSalaryPaymentSystem.SelectedIndexChanged
        GetDiscountReward()
    End Sub

    Private Sub txtSeries_TextChanged(sender As Object, e As EventArgs) Handles txtSeries.TextChanged
        MyVars.CheckNumber(txtSeries)
    End Sub
End Class