﻿Public Class Frm_ItemsAlternative

    Private Sub Frm_ItemsAlternative_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "select itm_idTradeName  as [الباركود], TradeName as [الاسم التجارى], ScientificName as [الاسم العلمى], DrugConcentration as [التركيز] from ItemsAlternative where itm_id =N'" + itmprc + "'"
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

    End Sub

    Private Sub DataGridView1_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DataGridView1.DoubleClick
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        FrmSales.txtprc.Text = DataGridView1.SelectedRows(0).Cells(0).Value

        Bol = True
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select group_name , sname,SalPrice,Stores from items where itm_id=N'" & FrmSales.txtprc.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Or dr(1) Is DBNull.Value Then
        Else
            FrmSales.cmbcats.Text = dr(0)
            FrmSales.cmbname.Text = dr(1)
            If dr(2) Is DBNull.Value Then
                FrmSales.txtprice.Text = 0
                GoTo 1
            End If
            FrmSales.txtprice.Text = dr(2)
            FrmSales.cmbStores.Text = dr(3)
        End If

1:

        FrmSales.txtqunt.Text = 1

        Dim X As Double = IM.Get_Itm_Store(FrmSales.txtprc.Text.Trim, FrmSales.cmbStores.Text.Trim)
        FrmSales.txtStore.Text = X
        If X < IM.Get_Itm_Range(FrmSales.txtprc.Text.Trim, FrmSales.cmbStores.Text.Trim) Then
            FrmSales.txtStore.ForeColor = Color.Red
        End If

        Dim AddBillAuto As String = mykey.GetValue("AddBillAuto", "NO")
        If AddBillAuto = "YES" Then
            FrmSales.BtnAdd.PerformClick()
        Else
            FrmSales.txtqunt.Focus()
            FrmSales.txtqunt.SelectAll()
        End If

        Bol = False
        Me.Close()
    End Sub

End Class