﻿Imports CrystalDecisions.CrystalReports.Engine

Public Class frmMedicationDoseSlip
    Dim MaxRecoedCode As String

    Private Sub frmAdditional_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        txtSellerName.Text = UserName

        txtMedicamentName.Text = Cls.Get_Code_Value_Branch_More("Items", "sname", "itm_id=N'" & EditItmId & "'")
        txtPatientName.Text = ""
        txtPatientName.Focus()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If


        PrintReport()


        ClearAll()
        txtPatientName.Focus()

    End Sub

    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        ClearAll()
        txtPatientName.Focus()
    End Sub

    Private Sub ClearAll()
        txtPatientName.Text = ""
        txtMedicamentName.Text = ""
        txtDoseMedication.Text = ""
    End Sub

    Private Sub dtpDateExchange_KeyUp(sender As Object, e As KeyEventArgs) Handles dtpDateExchange.KeyUp
        If e.KeyCode = 13 Then
            txtDoseMedication.Focus()
        End If
    End Sub

    Private Sub PrintReport()
        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If
        Cls.delete_Branch_All("PrintSalesPurchases")

        AddReportView()
        Dim rpt As New rpt_ParcodeMedicationDoseSlip2
        Dim txtNameAr, txtTimeAMBMObject, txtDateExchange, txtSellerNameObject, txtPatientNameObject, txtMedicamentNameObject, txtDoseMedicationObject As TextObject

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txtNameAr = rpt.Section1.ReportObjects("txtTitelNameAr")
        txtNameAr.Text = NameArCompay
        txtSellerNameObject = rpt.Section1.ReportObjects("txtSellerName")
        txtSellerNameObject.Text = txtSellerName.Text
        txtPatientNameObject = rpt.Section1.ReportObjects("txtPatientName")
        txtPatientNameObject.Text = txtPatientName.Text
        txtMedicamentNameObject = rpt.Section1.ReportObjects("txtMedicamentName")
        txtMedicamentNameObject.Text = txtMedicamentName.Text
        txtDateExchange = rpt.Section1.ReportObjects("dtpDateExchange")
        txtDateExchange.Text = dtpDateExchange.Text
        txtTimeAMBMObject = rpt.Section1.ReportObjects("txtTimeAMBMObject")
        txtTimeAMBMObject.Text = txtTimeAMBM.Text
        txtDoseMedicationObject = rpt.Section1.ReportObjects("txtDoseMedication")
        txtDoseMedicationObject.Text = txtDoseMedication.Text


        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "'طباعة قسيمة جرعة الدواء"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        On Error Resume Next
        SlpitTime()
        txtTimeAMBM.Text = TimeAmBm
    End Sub

    Private Sub txtSellerName_KeyUp(sender As Object, e As KeyEventArgs) Handles txtSellerName.KeyUp
        If e.KeyCode = 13 Then
            txtPatientName.Focus()
        End If
    End Sub

    Private Sub txtPatientName_KeyUp(sender As Object, e As KeyEventArgs) Handles txtPatientName.KeyUp
        If e.KeyCode = 13 Then
            txtDoseMedication.Focus()
        End If
    End Sub

    Private Sub txtMedicamentName_KeyUp(sender As Object, e As KeyEventArgs) Handles txtMedicamentName.KeyUp
        If e.KeyCode = 13 Then
            dtpDateExchange.Focus()
        End If
    End Sub

    Private Sub txtDoseMedication_KeyUp(sender As Object, e As KeyEventArgs) Handles txtDoseMedication.KeyUp
        If e.KeyCode = 13 Then
            btnSave.PerformClick()
        End If
    End Sub
End Class