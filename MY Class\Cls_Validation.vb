﻿Public Class Cls_Validation
    Friend Shared ErrMsg As String = "من فضلك راجع الدعم لوجود مشكله "
    Public Function GetDaysInMonth(ByVal Month As Int16, ByVal Year As Int16)
        Dim D As Int16
        D = Date.DaysInMonth(Year, Month)
        Return D
    End Function
    Public Sub Clear(ByVal Frm As Form)
        Dim Ctl As Control
        For Each Ctl In Frm.Controls
            If TypeOf Ctl Is TextBox Then
                CType(Ctl, TextBox).Clear()
            End If
            If TypeOf Ctl Is PictureBox Then
                CType(Ctl, PictureBox).Image = Nothing
            End If
            If TypeOf Ctl Is ComboBox Then
                CType(Ctl, ComboBox).Items.Clear()
            End If
        Next
    End Sub
 
    Public Sub Clear1(ByVal Frm As Form)
        Dim Ctl As Control
        For Each Ctl In Frm.Controls
            If TypeOf Ctl Is GroupBox Then
                For Each ctl1 As Control In CType(Ctl, GroupBox).Controls
                    If TypeOf ctl1 Is TextBox Then
                        CType(Ctl, TextBox).Clear()
                    End If

                    If TypeOf Ctl Is PictureBox Then
                        CType(Ctl, PictureBox).Image = Nothing
                    End If
                    If TypeOf Ctl Is ComboBox Then
                        CType(Ctl, ComboBox).Items.Clear()
                    End If
                Next
            End If
        Next
    End Sub

    Public Sub GetMonthName(ByVal DrpMonth As ComboBox)

        Dim x As Integer

        DrpMonth.Items.Clear()

        For x = 1 To 12

            DrpMonth.Items.Add(MonthName(x))

        Next

    End Sub

    Public Function CheckNumber(ByVal txt As String)
        If Not IsNumeric(txt) = False Then
            Return False
        Else
            Return True
        End If
    End Function

    Function ErrProvider(ByVal ctl As Control, ByVal Err As ErrorProvider, ByVal ctl2 As Control) As Boolean

        If ctl.Text = "" Then
            Err.SetError(ctl, "من فضلك ادخل البيان")
            ctl2.Visible = False
            Return True
        Else
            Err.Clear()
            ctl2.Visible = True
            Return False
        End If



    End Function
    Friend Sub CloseBtn(ByVal Btn_Enabled1 As Button, ByVal Btn_Enabled2 As Button, ByVal Btn_Disabled1 As Button, ByVal Btn_Disabled2 As Button)

        Btn_Enabled1.Enabled = True
        Btn_Enabled2.Enabled = True
        Btn_Disabled1.Enabled = False
        Btn_Disabled2.Enabled = False

    End Sub
    Friend Sub clean(ByVal frm As Form)
        Dim ctl As Control
        Dim ctl1 As Control
        For Each ctl In frm.Controls
            If TypeOf ctl Is GroupBox Then
                For Each ctl1 In ctl.Controls
                    If TypeOf ctl1 Is TextBox Then
                        ctl1.Text = ""
                    End If
                Next
            End If
        Next
    End Sub
    Friend Sub banna(ByVal frm As Form)
        Dim ctl As Object
        'Dim ctl1 As Object
        For Each ctl In frm.Controls
            If TypeOf ctl Is MenuStrip Then
                For Each ctl1 As ToolStripMenuItem In CType(ctl, MenuStrip).Items

                    If TypeOf ctl1 Is ToolStripMenuItem Then
                        CType(ctl1, ToolStripMenuItem).Enabled = True
                    End If
                Next
            End If

        Next
    End Sub

    Sub rezk(ByVal frm As Form)
        Dim ctl As Object
        'Dim ctl1 As Object
        For Each ctl In frm.Controls
            If TypeOf ctl Is MenuStrip Then
                For Each ctl1 As ToolStripMenuItem In CType(ctl, MenuStrip).Items

                    If TypeOf ctl1 Is ToolStripMenuItem Then
                        CType(ctl1, ToolStripMenuItem).Enabled = True
                    End If
                Next
            End If

        Next 

    End Sub
    Public Sub Cleanbanna(ByVal Frm As Form)
        Dim Ctr, Ctr1, Ctr2, ctr3 As Control
        For Each Ctr In Frm.Controls
            If TypeOf Ctr Is TabControl Then
                For Each Ctr1 In Ctr.Controls
                    If TypeOf Ctr1 Is TabPage Then
                        For Each ctr3 In Ctr1.Controls
                            If TypeOf ctr3 Is GroupBox Then
                                For Each Ctr2 In ctr3.Controls
                                    If TypeOf Ctr2 Is TextBox Then
                                        Ctr2.Text = Nothing
                                    ElseIf TypeOf Ctr2 Is ComboBox Then
                                        Ctr2.Text = Nothing
                                    ElseIf TypeOf Ctr2 Is MaskedTextBox Then
                                        Ctr2.Text = Nothing
                                    End If
                                Next
                            End If
                        Next
                    End If
                Next
            End If
        Next
    End Sub
End Class

