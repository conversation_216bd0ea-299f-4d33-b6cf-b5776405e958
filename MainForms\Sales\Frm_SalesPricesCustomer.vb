﻿Imports CrystalDecisions.CrystalReports.Engine

Public Class Frm_SalesPricesCustomer
    Dim WithEvents BS As New BindingSource


    Private Sub Frm_PreviousSalesPricesCustomer_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", Cmbvendorname)

        End If
        Bra.Fil("groups", "g_name", cmbcats)

    End Sub

    Private Sub cmbcats_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbcats.SelectedIndexChanged
        If cmbcats.Text.Trim = "" Then Exit Sub
        cmbitmnm.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sname from Items where group_name =N'" & cmbcats.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbitmnm.Items.Add(Trim(dr(0)))
        Loop
        cmbitmnm.Text = ""
    End Sub

    Private Sub BtnShow_Click(sender As Object, e As EventArgs) Handles BtnShow.Click
        DataGridView1.DataSource = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select bill_no As [رقم الفاتورة],Vendorname As [اسم العميل],bill_date As [تاريخ الفاتورة],itm_id As [الباركود],itm_cat As [المجموعة],itm_name As [الصنف],price As [سعر البيع] FROM View_SalesPricesCustomer where id <> N''"
        If Cmbvendorname.Text <> "" Then
            S = S & " and Vendorname =N'" & Cmbvendorname.Text.Trim & "'"
        End If
        If chkAll.Checked = False Then
            If TxtPrc.Text <> "" Then
                S = S & " and itm_id =N'" & TxtPrc.Text.Trim & "'"
            End If
            If cmbcats.Text <> "" Then
                S = S & " and itm_cat =N'" & cmbcats.Text.Trim & "'"
            End If
            If cmbitmnm.Text <> "" Then
                S = S & " and itm_name =N'" & cmbitmnm.Text.Trim & "'"
            End If
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم الفاتورة]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [تاريخ الفاتورة]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [اسم العميل]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbcats.Enabled = False
            cmbitmnm.Enabled = False
            TxtPrc.Enabled = False
        Else
            cmbcats.Enabled = True
            cmbitmnm.Enabled = True
            TxtPrc.Enabled = True
        End If
    End Sub

    Private Sub btnPrint_Click(sender As Object, e As EventArgs) Handles btnPrint.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            S = "insert into PrintSalesPurchases(Company_Branch_ID,Vendorname,BILL_NO,itm_id,itm_cat,itm_name,price)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & Cmbvendorname.Text & "',N'" & DataGridView1.Rows(i).Cells(0).Value & "',N'" & DataGridView1.Rows(i).Cells(2).Value & "',N'" & DataGridView1.Rows(i).Cells(3).Value & "',N'" & DataGridView1.Rows(i).Cells(4).Value & "',N'" & DataGridView1.Rows(i).Cells(5).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New Rpt_SalesPricesCustomer

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        AddReportView()
        Dim txtname, txtNameAr, txtNameEn As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير أسعار البيع للعميل"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "تقرير أسعار البيع للعميل"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class