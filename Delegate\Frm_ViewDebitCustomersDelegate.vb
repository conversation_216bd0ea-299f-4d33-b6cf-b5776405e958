﻿Imports CrystalDecisions.CrystalReports.Engine
Public Class Frm_ViewDebitCustomersDelegate
    Dim WithEvents BS As New BindingSource
    Dim Custamntcredit, Custamntdebit, DateCustamntcredit, DateCustamntdebit, CountFirstamntcredit, CountFirstamntdebit, CountLastamntcredit, CountLastamntdebit As Double
    Dim CustMainDateAll As String
    Dim CustMainDate As Long
    Dim TotalBtin As Double
    Dim aray_1 As New ArrayList
    Dim TotalActual As Double

    Private Sub FrmShowCustomerData_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployees)

        Cmbvendorname.Items.Add("نقدا")
    End Sub

    Public Sub Header()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If ChbAll.Checked = True Then
            S = "Select dbo.Employees.NameEmployee, dbo.Customers.Vendorname, dbo.Customers.vintinval, dbo.Customers.valuereturns, dbo.Customers.vndiscount, dbo.Customers.VnPay, dbo.Customers.vnamntcredit, dbo.Customers.vnamntdebit  From dbo.Customers LEFT OUTER Join dbo.Employees ON dbo.Customers.Emp_Code = dbo.Employees.EMPID  Group By dbo.Employees.NameEmployee, dbo.Customers.Vendorname, dbo.Customers.vintinval, dbo.Customers.valuereturns, dbo.Customers.vndiscount, dbo.Customers.VnPay, dbo.Customers.vnamntcredit,  dbo.Customers.vnamntdebit  HAVING(dbo.Employees.NameEmployee = N'" & cmbEmployees.Text & "')"
            If FilterSelect = "Number" Then
                S = S & " order by dbo.Employees.NameEmployee"
            End If
            If FilterSelect = "Date" Then
                S = S & " order by dbo.Customers.Vendorname"
            End If
            If FilterSelect = "Name" Then
                S = S & " order by dbo.Customers.Vendorname"
            End If
        Else
            Cls.delete_Branch_All("PrintSalesPurchases")

            CustomerAccountTotalAll(Cmbvendorname.Text)
        End If
        If Chek_WithoutDate.Checked = False Then
            If Cmbvendorname.Text = "" Then
                UpdateCustomers()
                S = "Select UserName,Vendorname,vintinval,valuereturns ,vndiscount ,VnPay ,vnamntcredit,vnamntdebit  from PrintSalesPurchases"
            Else
                S = "Select UserName,Vendorname ,vintinval ,valuereturns ,vndiscount ,VnPay ,vnamntcredit,vnamntdebit  from PrintSalesPurchases where Vendorname = N'" & Cmbvendorname.Text.Trim & "'"
            End If
            If FilterSelect = "Number" Then
                S = S & " order by UserName"
            End If
            If FilterSelect = "Date" Then
                S = S & " order by UserName"
            End If
            If FilterSelect = "Name" Then
                S = S & " order by Vendorname"
            End If
        Else
            If ChbAll.Checked = False Then
                S = "Select dbo.Employees.NameEmployee, dbo.Customers.Vendorname, dbo.Customers.vintinval, dbo.Customers.valuereturns, dbo.Customers.vndiscount, dbo.Customers.VnPay, dbo.Customers.vnamntcredit, dbo.Customers.vnamntdebit  From dbo.Customers LEFT OUTER Join  dbo.Employees ON dbo.Customers.Emp_Code = dbo.Employees.EMPID  Group By dbo.Employees.NameEmployee, dbo.Customers.Vendorname, dbo.Customers.vintinval, dbo.Customers.valuereturns, dbo.Customers.vndiscount, dbo.Customers.VnPay, dbo.Customers.vnamntcredit,  dbo.Customers.vnamntdebit  HAVING(dbo.Employees.NameEmployee = N'" & cmbEmployees.Text & "') AND (dbo.Customers.Vendorname = N'" & Cmbvendorname.Text.Trim & "')"
            Else
                S = "Select dbo.Employees.NameEmployee, dbo.Customers.Vendorname, dbo.Customers.vintinval, dbo.Customers.valuereturns, dbo.Customers.vndiscount, dbo.Customers.VnPay, dbo.Customers.vnamntcredit, dbo.Customers.vnamntdebit  From dbo.Customers LEFT OUTER Join   dbo.Employees ON dbo.Customers.Emp_Code = dbo.Employees.EMPID  Group By dbo.Employees.NameEmployee, dbo.Customers.Vendorname, dbo.Customers.vintinval, dbo.Customers.valuereturns, dbo.Customers.vndiscount, dbo.Customers.VnPay, dbo.Customers.vnamntcredit, dbo.Customers.vnamntdebit  HAVING(dbo.Employees.NameEmployee = N'" & cmbEmployees.Text & "')"
            End If
            If FilterSelect = "Number" Then
                S = S & " order by dbo.Employees.NameEmployee"
            End If
            If FilterSelect = "Date" Then
                S = S & " order by dbo.Employees.NameEmployee"
            End If
            If FilterSelect = "Name" Then
                S = S & " order by dbo.Customers.Vendorname"
            End If
        End If

        cmd.CommandText = S
        Dim dt As New DataTable
        dr = cmd.ExecuteReader
        dt.Load(dr)
        DataGridView3.DataSource = dt

        DataGridView3.Columns(0).HeaderCell.Value = "أسم المندوب"
        DataGridView3.Columns(1).HeaderCell.Value = "أسم العميل"
        DataGridView3.Columns(2).HeaderCell.Value = "قيمة المبيعات"
        DataGridView3.Columns(3).HeaderCell.Value = "قيمة مرتجعات المبيعات"
        DataGridView3.Columns(4).HeaderCell.Value = "قيمة الخصومات"
        DataGridView3.Columns(5).HeaderCell.Value = "قيمة المقبوضات"
        DataGridView3.Columns(6).HeaderCell.Value = "مدين"
        DataGridView3.Columns(7).HeaderCell.Value = "دائن"

        DataGridView3.Columns(0).Width = 190
        DataGridView3.Columns(0).Width = 190

        If Chek_WithoutDate.Checked = False Then
            If Cmbvendorname.Text = "" Then
                For i As Integer = 0 To DataGridView3.RowCount - 1
                    DataGridView3.Rows(i).Cells(0).Value = cmbEmployees.Text
                Next
            End If
        End If

        SumDGV3()
        IM.CustomerAccountTotal(Cmbvendorname.Text.Trim)
        'allVendorAccount()
        ''
    End Sub

    Private Sub GetDataBsal()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()

        S = "Select dbo.IM_Bsal_Data.bill_no, dbo.IM_Bsal.Vendorname, dbo.IM_Bsal_Data.UserName As IM_Bsal_State, dbo.IM_Bsal_Data.itm_name, dbo.IM_Bsal_Data.price, dbo.IM_Bsal_Data.qu, dbo.IM_Bsal_Data.totalprice, dbo.IM_Bsal_Data.bill_date,   dbo.IM_Bsal_Data.totalprice AS vnamntdebit, dbo.Employees.NameEmployee, dbo.IM_Bsal_Data.Company_Branch_ID  From dbo.Employees RIGHT OUTER Join  dbo.IM_Bsal_Data INNER Join  dbo.IM_Bsal ON dbo.IM_Bsal_Data.bill_no = dbo.IM_Bsal.bill_No INNER Join  dbo.Items ON dbo.IM_Bsal_Data.itm_id = dbo.Items.itm_id INNER Join  dbo.Customers ON dbo.IM_Bsal.Vendorname = dbo.Customers.Vendorname ON dbo.Employees.EMPID = dbo.Customers.Emp_Code  Group By dbo.IM_Bsal_Data.bill_no, dbo.IM_Bsal.Vendorname, dbo.IM_Bsal_Data.UserName, dbo.IM_Bsal_Data.itm_name, dbo.IM_Bsal_Data.price, dbo.IM_Bsal_Data.qu, dbo.IM_Bsal_Data.totalprice, dbo.IM_Bsal_Data.bill_date,  dbo.Employees.NameEmployee, dbo.IM_Bsal_Data.Company_Branch_ID  HAVING(dbo.IM_Bsal_Data.bill_no <> N'')"
        If ChbAll.Checked = False Then
            S = S & " And dbo.IM_Bsal.Vendorname = N'" & Cmbvendorname.Text.Trim & "'"
        End If
        If ChbAll.Checked = True Then
            S = S & " and dbo.Employees.NameEmployee = N'" & cmbEmployees.Text.Trim & "'"
        End If
        If Chek_WithoutDate.Checked = False Then
            S = S & " and dbo.IM_Bsal_Data.bill_date >=N'" & Cls.C_date(Dtp_from.Text) & "' and dbo.IM_Bsal_Data.bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " and dbo.IM_Bsal_Data.Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridViewBsal.DataSource = Cls.PopulateDataView(dr)
        DataGridViewBsal.Columns(1).Visible = False
        DataGridViewBsal.Columns(10).Visible = False
        Dim SM As String
        For i As Integer = 0 To DataGridViewBsal.RowCount - 1
            SM = Val(DataGridViewBsal.Rows(i).Cells(7).Value)
            SM = Cls.R_date(SM)
            DataGridViewBsal.Rows(i).Cells(7).Value = SM
        Next

        For i As Integer = 0 To DataGridViewBsal.RowCount - 1
            DataGridViewBsal.Rows(i).Cells(2).Value = "مرتجعات مبيعات"
        Next
    End Sub

    Private Sub GetDataPay()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()

        S = "Select dbo.Vst.BillNo, dbo.Vst.Vendorname, dbo.Vst.VND_dec, dbo.Vst.VND_amx, dbo.Vst.VND_dt, dbo.Vst.VND_amx As EXVND_amx, dbo.Employees.NameEmployee, dbo.Vst.Company_Branch_ID  From dbo.Customers LEFT OUTER Join  dbo.Employees ON dbo.Customers.Emp_Code = dbo.Employees.EMPID LEFT OUTER Join  dbo.Vst ON dbo.Customers.Vendorname = dbo.Vst.Vendorname Group By dbo.Vst.BillNo, dbo.Vst.Vendorname, dbo.Vst.VND_dec, dbo.Vst.VND_amx, dbo.Vst.VND_dt, dbo.Employees.NameEmployee, dbo.Vst.Company_Branch_ID  HAVING(dbo.Vst.BillNo <> N'')"
        If ChbAll.Checked = False Then
            S = S & " and dbo.Vst.Vendorname = N'" & Cmbvendorname.Text.Trim & "'"
        End If
        If ChbAll.Checked = True Then
            S = S & " and dbo.Employees.NameEmployee = N'" & cmbEmployees.Text.Trim & "'"
        End If
        If Chek_WithoutDate.Checked = False Then
            S = S & " and dbo.Vst.VND_dt >=N'" & Cls.C_date(Dtp_from.Text) & "' and dbo.Vst.VND_dt <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " and dbo.Vst.Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridViewPay.DataSource = Cls.PopulateDataView(dr)
        DataGridViewPay.Columns(1).Visible = False
        DataGridViewPay.Columns(7).Visible = False
        Dim SM As String
        For i As Integer = 0 To DataGridViewPay.RowCount - 1
            SM = Val(DataGridViewPay.Rows(i).Cells(4).Value)
            SM = Cls.R_date(SM)
            DataGridViewPay.Rows(i).Cells(4).Value = SM
        Next

        For i As Integer = 0 To DataGridViewPay.RowCount - 1
            DataGridViewPay.Rows(i).Cells(2).Value = "مقبوضات عملاء"
        Next

    End Sub

    Private Sub GetDataDisc()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select dbo.Vst_disc.TIN_NO, dbo.Vst_disc.Vendorname, dbo.Vst_disc.det, dbo.Vst_disc.amnt, dbo.Vst_disc.pdate, dbo.Vst_disc.amnt As EXamnt, dbo.Employees.NameEmployee, dbo.Vst_disc.Company_Branch_ID  From dbo.Customers LEFT OUTER Join   dbo.Employees ON dbo.Customers.Emp_Code = dbo.Employees.EMPID LEFT OUTER Join  dbo.Vst_disc ON dbo.Customers.Vendorname = dbo.Vst_disc.Vendorname  Group By dbo.Vst_disc.TIN_NO, dbo.Vst_disc.Vendorname, dbo.Vst_disc.det, dbo.Vst_disc.amnt, dbo.Vst_disc.pdate, dbo.Employees.NameEmployee, dbo.Vst_disc.Company_Branch_ID  HAVING(dbo.Vst_disc.TIN_NO <> N'')"
        If ChbAll.Checked = False Then
            S = S & " and dbo.Vst_disc.Vendorname = N'" & Cmbvendorname.Text.Trim & "'"
        End If
        If ChbAll.Checked = True Then
            S = S & " and dbo.Employees.NameEmployee = N'" & cmbEmployees.Text.Trim & "'"
        End If
        If Chek_WithoutDate.Checked = False Then
            S = S & " and dbo.Vst_disc.pdate >=N'" & Cls.C_date(Dtp_from.Text) & "' and dbo.Vst_disc.pdate <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " and dbo.Vst_disc.Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridViewDisc.DataSource = Cls.PopulateDataView(dr)
        DataGridViewDisc.Columns(1).Visible = False
        DataGridViewDisc.Columns(7).Visible = False
        Dim SM As String
        For i As Integer = 0 To DataGridViewDisc.RowCount - 1
            SM = Val(DataGridViewDisc.Rows(i).Cells(4).Value)
            SM = Cls.R_date(SM)
            DataGridViewDisc.Rows(i).Cells(4).Value = SM
        Next

        For i As Integer = 0 To DataGridViewDisc.RowCount - 1
            DataGridViewDisc.Rows(i).Cells(2).Value = "خصومات عملاء"
        Next

    End Sub

    Private Sub GetDataDiscOther()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select dbo.Vst_disc_other.TIN_NO, dbo.Vst_disc_other.Vendorname, dbo.Vst_disc_other.DiscStatement, dbo.Vst_disc_other.amnt, dbo.Vst_disc_other.pdate, dbo.Vst_disc_other.amnt As Expr1, dbo.Employees.NameEmployee,      dbo.Vst_disc_other.Company_Branch_ID   From dbo.Customers LEFT OUTER Join   dbo.Employees ON dbo.Customers.Emp_Code = dbo.Employees.EMPID LEFT OUTER Join dbo.Vst_disc_other ON dbo.Customers.Vendorname = dbo.Vst_disc_other.Vendorname  Group By dbo.Vst_disc_other.TIN_NO, dbo.Vst_disc_other.Vendorname, dbo.Vst_disc_other.DiscStatement, dbo.Vst_disc_other.amnt, dbo.Vst_disc_other.pdate, dbo.Employees.NameEmployee, dbo.Vst_disc_other.Company_Branch_ID  HAVING(dbo.Vst_disc_other.TIN_NO <> N'')"
        If ChbAll.Checked = False Then
            S = S & " and dbo.Vst_disc_other.Vendorname = N'" & Cmbvendorname.Text.Trim & "'"
        End If
        If ChbAll.Checked = True Then
            S = S & " and dbo.Employees.NameEmployee = N'" & cmbEmployees.Text.Trim & "'"
        End If
        If Chek_WithoutDate.Checked = False Then
            S = S & " and dbo.Vst_disc_other.pdate >=N'" & Cls.C_date(Dtp_from.Text) & "' and dbo.Vst_disc_other.pdate <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " and dbo.Vst_disc_other.Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridViewDiscOther.DataSource = Cls.PopulateDataView(dr)
        DataGridViewDiscOther.Columns(1).Visible = False
        DataGridViewDiscOther.Columns(7).Visible = False
        Dim SM As String
        For i As Integer = 0 To DataGridViewDiscOther.RowCount - 1
            SM = Val(DataGridViewDiscOther.Rows(i).Cells(4).Value)
            SM = Cls.R_date(SM)
            DataGridViewDiscOther.Rows(i).Cells(4).Value = SM
        Next

    End Sub

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select dbo.Employees.NameEmployee, dbo.Customers.Vendorname As ExVendorname, dbo.IM_Bsal.Vendorname, dbo.IM_Bsal.totalpricebeforedisc, dbo.IM_Bsal.bill_date, dbo.IM_Bsal.Company_Branch_ID From dbo.Customers INNER Join dbo.IM_Bsal ON dbo.Customers.Vendorname = dbo.IM_Bsal.Vendorname LEFT OUTER Join      dbo.Employees ON dbo.Customers.Emp_Code = dbo.Employees.EMPID Group By dbo.Employees.NameEmployee, dbo.Customers.Vendorname, dbo.IM_Bsal.Vendorname, dbo.IM_Bsal.totalpricebeforedisc, dbo.IM_Bsal.bill_date, dbo.IM_Bsal.Company_Branch_ID HAVING(dbo.Customers.Vendorname <> N'')"
        If ChbAll.Checked = False Then
            S = S & " and dbo.IM_Bsal.Vendorname = N'" & Cmbvendorname.Text.Trim & "'"
        End If
        If ChbAll.Checked = True Then
            S = S & " and dbo.Employees.NameEmployee = N'" & cmbEmployees.Text.Trim & "'"
        End If
        If Chek_WithoutDate.Checked = False Then
            S = S & " and dbo.IM_Bsal.bill_date >=N'" & Cls.C_date(Dtp_from.Text) & "' and dbo.IM_Bsal.bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " and dbo.IM_Bsal.Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If
        S = S & " order by 1"
        cmd.CommandText = S
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            TotalBtin = 0
        Else
            If dr(0) Is DBNull.Value Then
            Else
                TotalBtin = dr("totalpricebeforedisc")
            End If
        End If
        '==============================================================================================
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select bill_no,Vendorname,StateCust,qu,price,totalprice,bill_date,State,vnamntcredit,itm_name,NameEmployee,Company_Branch_ID from View_DebitCustomersDelegate where bill_no <> N'جرد'"
        If ChbAll.Checked = False Then
            S = S & " and Vendorname = N'" & Cmbvendorname.Text.Trim & "'"
        End If
        If ChbAll.Checked = False Then
            S = S & " and NameEmployee = N'" & cmbEmployees.Text.Trim & "'"
        End If
        If Chek_WithoutDate.Checked = False Then
            S = S & " and bill_date >=N'" & Cls.C_date(Dtp_from.Text) & "' and bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " and Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If


        'If ChbAll.Checked = False Then
        '    S = S & " and dbo.Sales_Bill.Vendorname = N'" & Cmbvendorname.Text.Trim & "'"
        'End If
        'If ChbAll.Checked = True Then
        '    S = S & " and dbo.Employees.NameEmployee = N'" & cmbEmployees.Text.Trim & "'"
        'End If
        'If Chek_WithoutDate.Checked = False Then
        '    S = S & " and dbo.BillsalData.bill_date >=N'" & Cls.C_date(Dtp_from.Text) & "' and dbo.BillsalData.bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        'End If
        'If PermtionName <> "مدير" Then
        '    S = S & " and dbo.BillsalData.Company_Branch_ID =N'" & Company_Branch_ID & "'"
        'End If
        S = S & " order by 1"
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridViewSal.DataSource = Cls.PopulateDataView(dr)
        DataGridViewSal.Columns(1).Visible = False
        DataGridViewSal.Columns(11).Visible = False

        Dim SM As String
        For i As Integer = 0 To DataGridViewSal.RowCount - 1
            SM = Val(DataGridViewSal.Rows(i).Cells(7).Value.ToString)
            SM = Cls.R_date(SM)
            DataGridViewSal.Rows(i).Cells(7).Value = SM
            DataGridViewSal.Rows(i).Cells(2).Value = "مبيعات"
        Next


    End Sub

    Function Return_Alf_Genih(ByVal X As Integer) As String
        If Len(X) <= 3 Then Return X : Exit Function
        If Len(X) > 3 Then
            Dim M As Integer
            M = Int(X - 1000 * Int(X / 1000))
            Dim MM As Integer = Int(X / 1000)
            Return M & " | " & MM
        End If
    End Function

    Private Sub ChbAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChbAll.CheckedChanged
        If ChbAll.Checked = True Then
            Cmbvendorname.Enabled = False
            Cmbvendorname.SelectedIndex = -1
        ElseIf ChbAll.Checked = False Then
            Cmbvendorname.Enabled = True
        End If
    End Sub

    Private Sub Chek_WithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Chek_WithoutDate.CheckedChanged
        If Chek_WithoutDate.Checked = True Then
            Dtp_from.Enabled = False
            Dtp_To.Enabled = False
        ElseIf Chek_WithoutDate.Checked = False Then
            Dtp_from.Enabled = True
            Dtp_To.Enabled = True
        End If

    End Sub

    Private Sub DataGridView1_CellClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridViewSal.CellClick
        Header()
    End Sub

    Private Sub DataGridView1_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles DataGridViewSal.DoubleClick
        'GetDetails()
    End Sub

    Private Sub SumDGV3()
        Dim SM As Double
        For i As Integer = 0 To DataGridView3.Rows.Count - 1
            SM = SM + DataGridView3.Rows(i).Cells(2).Value
        Next
        txt_ValuPurchases.Text = Math.Round(SM, 2)

        Dim SM6 As Double
        For i As Integer = 0 To DataGridView3.Rows.Count - 1
            SM6 = SM6 + DataGridView3.Rows(i).Cells(3).Value
        Next
        txtvaluereturns.Text = Math.Round(SM6, 2)

        Dim SM2 As Double
        For i As Integer = 0 To DataGridView3.Rows.Count - 1
            SM2 = SM2 + DataGridView3.Rows(i).Cells(4).Value
        Next
        txtValDisc.Text = Math.Round(SM2, 2)

        Dim SM3 As Double
        For i As Integer = 0 To DataGridView3.Rows.Count - 1
            SM3 = SM3 + DataGridView3.Rows(i).Cells(5).Value
        Next
        txtValPay.Text = Math.Round(SM3, 2)

        Dim SM4 As Double
        For i As Integer = 0 To DataGridView3.Rows.Count - 1
            SM4 = SM4 + DataGridView3.Rows(i).Cells(6).Value
        Next
        txtdebtor.Text = Math.Round(SM4, 2)

        Dim SM5 As Double
        For i As Integer = 0 To DataGridView3.Rows.Count - 1
            SM5 = SM5 + DataGridView3.Rows(i).Cells(7).Value
        Next
        txtCreditor.Text = Math.Round(SM5, 2)

    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView3.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView3.Rows.Count - 1
            If chkNotShowCustBalance.Checked = True Then
                If DataGridView3.Rows(i).Cells(6).Value <> 0 Or DataGridView3.Rows(i).Cells(7).Value <> 0 Then
                    S = "insert into PrintSalesPurchases (Company_Branch_ID,Vendorname,vintinval,valuereturns,vndiscount,VnPay,vnamntdebit,vnamntcredit,totalpricebeforedisc,Totalreturns,TotalDisc,TotalBay,TotalDebtor,TotalCreditor)  values("
                    S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView3.Rows(i).Cells(1).Value & "',N'" & DataGridView3.Rows(i).Cells(2).Value & "',"
                    S = S & "N'" & DataGridView3.Rows(i).Cells(3).Value & "',N'" & DataGridView3.Rows(i).Cells(4).Value & "',N'" & DataGridView3.Rows(i).Cells(5).Value & "',"
                    S = S & "N'" & DataGridView3.Rows(i).Cells(6).Value & "',N'" & DataGridView3.Rows(i).Cells(7).Value & "',"
                    S = S & "N'" & txt_ValuPurchases.Text & "',N'" & txtvaluereturns.Text & "',N'" & txtValDisc.Text & "',N'" & txtValPay.Text & "',N'" & txtdebtor.Text & "',N'" & txtCreditor.Text & "')"
                    cmd.CommandText = S : cmd.ExecuteNonQuery()
                End If
            Else
                S = "insert into PrintSalesPurchases (Company_Branch_ID,Vendorname,vintinval,valuereturns,vndiscount,VnPay,vnamntdebit,vnamntcredit,totalpricebeforedisc,Totalreturns,TotalDisc,TotalBay,TotalDebtor,TotalCreditor)  values("
                S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView3.Rows(i).Cells(1).Value & "',N'" & DataGridView3.Rows(i).Cells(2).Value & "',"
                S = S & "N'" & DataGridView3.Rows(i).Cells(3).Value & "',N'" & DataGridView3.Rows(i).Cells(4).Value & "',N'" & DataGridView3.Rows(i).Cells(5).Value & "',"
                S = S & "N'" & DataGridView3.Rows(i).Cells(6).Value & "',N'" & DataGridView3.Rows(i).Cells(7).Value & "',"
                S = S & "N'" & txt_ValuPurchases.Text & "',N'" & txtvaluereturns.Text & "',N'" & txtValDisc.Text & "',N'" & txtValPay.Text & "',N'" & txtdebtor.Text & "',N'" & txtCreditor.Text & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        Next
        AddReportView()
        Dim rpt As New Rpt_CustomerDelegateAcount
        Dim txt, txtNameAr, txtNameEn, txtDelegateName As TextObject

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txt = rpt.Section1.ReportObjects("Text12")
        txt.Text = "كشف حساب عملاء المندوب"
        txtNameAr = rpt.Section1.ReportObjects("Text1")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("Text14")
        txtNameEn.Text = NameEnCompany
        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
        txtDelegateName.Text = cmbEmployees.Text
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "كشف حساب عملاء المندوب"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If

    End Sub

    Sub InsertForPrint(ByVal Vendorname As String, ByVal vintinval As String, ByVal valuereturns As String, ByVal vndiscount As String, ByVal VnPay As String _
, ByVal vnamntdebit As String, ByVal vnamntcredit As String, ByVal totalpricebeforedisc As String, ByVal Totalreturns As String, ByVal TotalDisc As String _
, ByVal TotalBay As String, ByVal TotalDebtor As String, ByVal TotalCreditor As String)


        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "SP_Print_CustomerAcount"
        cmd.Parameters.Clear()

        cmd.Parameters.AddWithValue("@Vendorname", Vendorname)
        cmd.Parameters.AddWithValue("@vintinval", vintinval)
        cmd.Parameters.AddWithValue("@valuereturns", valuereturns)
        cmd.Parameters.AddWithValue("@vndiscount", vndiscount)
        cmd.Parameters.AddWithValue("@VnPay", VnPay)
        cmd.Parameters.AddWithValue("@vnamntdebit", vnamntdebit)
        cmd.Parameters.AddWithValue("@vnamntcredit", vnamntcredit)
        cmd.Parameters.AddWithValue("@totalpricebeforedisc", totalpricebeforedisc)
        cmd.Parameters.AddWithValue("@Totalreturns", Totalreturns)
        cmd.Parameters.AddWithValue("@TotalDisc", TotalDisc)
        cmd.Parameters.AddWithValue("@TotalBay", TotalBay)
        cmd.Parameters.AddWithValue("@TotalDebtor", TotalDebtor)
        cmd.Parameters.AddWithValue("@TotalCreditor", TotalCreditor)

        cmd.ExecuteNonQuery()

    End Sub

    Sub InsertForPrintAll(ByVal BILL_NO As String, ByVal Vendorname As String, ByVal bill_date As String, ByVal totalpricebeforedisc As String _
, ByVal disc As String, ByVal totalpriceafterdisc As String, ByVal BEY As String, ByVal STAYING As String _
, ByVal vintinval As String, ByVal valuereturns As String, ByVal vndiscount As String, ByVal VnPay As String, ByVal vnamntdebit As String, ByVal vnamntcredit As String)


        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "SP_Print_AcountCtmAll"
        cmd.Parameters.Clear()

        cmd.Parameters.AddWithValue("@BILL_NO", BILL_NO)
        cmd.Parameters.AddWithValue("@Vendorname", Vendorname)
        cmd.Parameters.AddWithValue("@bill_date", bill_date)
        cmd.Parameters.AddWithValue("@totalpricebeforedisc", totalpricebeforedisc)
        cmd.Parameters.AddWithValue("@disc", disc)
        cmd.Parameters.AddWithValue("@totalpriceafterdisc", totalpriceafterdisc)
        cmd.Parameters.AddWithValue("@BEY", BEY)
        cmd.Parameters.AddWithValue("@STAYING", STAYING)
        cmd.Parameters.AddWithValue("@vintinval", vintinval)
        cmd.Parameters.AddWithValue("@valuereturns", valuereturns)
        cmd.Parameters.AddWithValue("@vndiscount", vndiscount)
        cmd.Parameters.AddWithValue("@VnPay", VnPay)
        cmd.Parameters.AddWithValue("@vnamntdebit", vnamntdebit)
        cmd.Parameters.AddWithValue("@vnamntcredit", vnamntcredit)

        cmd.ExecuteNonQuery()

    End Sub

    Private Sub UpdateCustomers()
        aray_1.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If cmbEmployees.Text = "" Then
            If PermtionName = "مدير" Then
                cmd.CommandText = "Select distinct Vendorname from customers"
            Else
                cmd.CommandText = "Select distinct Vendorname from customers where Company_Branch_ID =N'" & Company_Branch_ID & "'"
            End If
        Else
            If PermtionName = "مدير" Then
                cmd.CommandText = "Select dbo.Customers.Vendorname, dbo.Employees.NameEmployee  From dbo.Customers LEFT OUTER Join  dbo.Employees ON dbo.Customers.Emp_Code = dbo.Employees.EMPID  Group By dbo.Customers.Vendorname, dbo.Employees.NameEmployee  HAVING(dbo.Employees.NameEmployee = N'" & cmbEmployees.Text & "')"
            Else
                cmd.CommandText = "Select dbo.Customers.Vendorname, dbo.Employees.NameEmployee, dbo.Customers.Company_Branch_ID  From dbo.Customers LEFT OUTER Join  dbo.Employees ON dbo.Customers.Emp_Code = dbo.Employees.EMPID  Group By dbo.Customers.Vendorname, dbo.Employees.NameEmployee, dbo.Customers.Company_Branch_ID  HAVING(dbo.Employees.NameEmployee = N'" & cmbEmployees.Text & "') AND (dbo.Customers.Company_Branch_ID = N'" & Company_Branch_ID & "')"
            End If
        End If
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr(0))
        Loop

        Cls.delete_Branch_All("PrintSalesPurchases")

        For i As Integer = 0 To aray_1.Count - 1
            CustomerAccountTotalAll(aray_1(i))
        Next
    End Sub

    Friend Sub CustomerAccountTotalAll(ByVal Cmbvendorname As String)

        If Cmbvendorname = "" Then Exit Sub
        Dim vintinval, vinbtinval, vndiscount, vndiscountOther, BVstdiscount, VnPay, BVstPay, TotalActual, TotalBVstdiscount, TotalBVstPay, Totalvintinval, ValueVAT As Double
        ' مبيعات

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(totalpricebeforedisc) from Sales_bill where Vendorname = N'" & Cmbvendorname & "' and bill_date >=N'" & Cls.C_date(Dtp_from.Text) & "' and bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then vintinval = 0 Else vintinval = dr(0)

        ' مرتجع مبيعات
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(totalpricebeforedisc) from IM_Bsal where Vendorname = N'" & Cmbvendorname & "' and bill_date >=N'" & Cls.C_date(Dtp_from.Text) & "' and bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then vinbtinval = 0 Else vinbtinval = dr(0)

        'خصومات 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(amnt) from Vst_disc where Vendorname = N'" & Cmbvendorname & "' and pdate >=N'" & Cls.C_date(Dtp_from.Text) & "' and pdate <=N'" & Cls.C_date(Dtp_To.Text) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then vndiscount = 0 Else vndiscount = dr(0)

        'خصومات أخرى 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(amnt) from Vst_disc_other where Vendorname =N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then vndiscountOther = 0 Else vndiscountOther = dr(0)

        ' مرتجع خصومات
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(amnt) from IM_Vst_disc where Vendorname = N'" & Cmbvendorname & "' and pdate >=N'" & Cls.C_date(Dtp_from.Text) & "' and pdate <=N'" & Cls.C_date(Dtp_To.Text) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then BVstdiscount = 0 Else BVstdiscount = dr(0)

        'مقبوضات  
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(VND_amx) from vst where Vendorname = N'" & Cmbvendorname & "' and VND_dt >=N'" & Cls.C_date(Dtp_from.Text) & "' and VND_dt <=N'" & Cls.C_date(Dtp_To.Text) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then VnPay = 0 Else VnPay = dr(0)

        'مرتجع مقبوضات
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(VND_amx) from IM_Vst where Vendorname = N'" & Cmbvendorname & "' and VND_dt >=N'" & Cls.C_date(Dtp_from.Text) & "' and VND_dt <=N'" & Cls.C_date(Dtp_To.Text) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then BVstPay = 0 Else BVstPay = dr(0)

        'ضريبة VAT 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(ValueVAT) from Sales_bill where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then ValueVAT = 0 Else ValueVAT = dr(0)
        ValueVAT = Math.Round(ValueVAT, 2)

        Totalvintinval = vintinval - vinbtinval
        TotalBVstdiscount = vndiscount + vndiscountOther - BVstdiscount
        TotalBVstPay = VnPay - BVstPay
        TotalActual = Totalvintinval - TotalBVstdiscount - TotalBVstPay + ValueVAT

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If TotalActual > 0 Then
            S = "insert into PrintSalesPurchases(Company_Branch_ID,Vendorname,valuereturns,vintinval,vndiscount,VnPay,vnamntcredit,vnamntdebit) values ("
            S = S & "N'" & Company_Branch_ID & "',N'" & Cmbvendorname & "'," & vinbtinval & "," & Totalvintinval & "," & TotalBVstdiscount & "," & TotalBVstPay & "," & TotalActual & ",0)"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        ElseIf TotalActual < 0 Then
            S = "insert into PrintSalesPurchases(Company_Branch_ID,Vendorname,valuereturns,vintinval,vndiscount,VnPay,vnamntcredit,vnamntdebit) values ("
            S = S & "N'" & Company_Branch_ID & "',N'" & Cmbvendorname & "'," & vinbtinval & "," & Totalvintinval & "," & TotalBVstdiscount & "," & TotalBVstPay & ",0," & TotalActual * -1 & ")"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        ElseIf TotalActual = 0 Then
            S = "insert into PrintSalesPurchases(Company_Branch_ID,Vendorname,valuereturns,vintinval,vndiscount,VnPay,vnamntcredit,vnamntdebit) values ("
            S = S & "N'" & Company_Branch_ID & "',N'" & Cmbvendorname & "'," & vinbtinval & "," & Totalvintinval & "," & TotalBVstdiscount & "," & TotalBVstPay & ",0,0)"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If
    End Sub

    Private Sub cmbEmployees_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbEmployees.SelectedIndexChanged
        Cmbvendorname.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select dbo.Employees.NameEmployee, dbo.Customers.Vendorname  From dbo.Employees RIGHT OUTER Join      dbo.Customers ON dbo.Employees.EMPID = dbo.Customers.Emp_Code  Group By dbo.Employees.NameEmployee, dbo.Customers.Vendorname  HAVING(dbo.Employees.NameEmployee = N'" & cmbEmployees.Text & "')"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            Cmbvendorname.Items.Add(dr("Vendorname"))
        Loop
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        GetData()
        GetDataBsal()
        GetDataPay()
        GetDataDisc()
        GetDataDiscOther()
        Header()
        Header_Balance_First()
        'Header_Balance_Last()

        '=======================================
    End Sub

    Public Sub Header_Balance_First()


        Cls.delete_Branch_All("PrintSalesPurchases")

        CustomerAccountTotalAll(Cmbvendorname.Text)

        '===========================================================
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select vnamntcredit,vnamntdebit from PrintSalesPurchases where Vendorname = N'" & Cmbvendorname.Text.Trim & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Or dr(1) Is DBNull.Value Then
        Else
            DateCustamntcredit = dr("vnamntcredit")
            DateCustamntdebit = dr("vnamntdebit")
        End If

        '===========================================================
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select vnamntcredit,vnamntdebit from Customers where Vendorname = N'" & Cmbvendorname.Text.Trim & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Or dr(1) Is DBNull.Value Then
        Else
            Custamntcredit = dr("vnamntcredit")
            Custamntdebit = dr("vnamntdebit")
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select totalpricebeforedisc from Sales_Bill where Vendorname=N'" & Cmbvendorname.Text.Trim & "' and bill_No=N'جرد'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Custamntcredit = dr("totalpricebeforedisc")
        End If
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select VND_amx from Vst where Vendorname=N'" & Cmbvendorname.Text.Trim & "' and BillNo=N'جرد'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Custamntdebit = dr("VND_amx")
        End If

        CountFirstamntcredit = Custamntcredit
        CountFirstamntdebit = Custamntdebit



        'If Custamntcredit = 0 Then
        '    CountFirstamntcredit = 0
        'Else
        '    CountFirstamntcredit = Custamntcredit - DateCustamntcredit
        'End If
        'If Custamntdebit = 0 Then
        '    CountFirstamntdebit = 0
        'Else
        '    CountFirstamntdebit = Custamntdebit - DateCustamntdebit
        'End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update PrintSalesPurchases set TotalCreditor =N'" & CountFirstamntcredit & "',TotalDebtor =N'" & CountFirstamntdebit & "',bill_date =N'" & Cls.C_date(Dtp_from.Text) & "' ,billtime =N'" & Cls.C_date(Dtp_To.Text) & "' where Vendorname = N'" & Cmbvendorname.Text & "'" : cmd.ExecuteNonQuery()


        ''===========================================================
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select TotalCreditor,TotalDebtor,vnamntcredit,vnamntdebit from PrintSalesPurchases where Vendorname =N'" & Cmbvendorname.Text.Trim & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Or dr(1) Is DBNull.Value Then
        Else
            txtCustamntcredit.Text = dr("TotalCreditor")
            txtCustamntdebit.Text = dr("TotalDebtor")
            txtCreditLast.Text = dr("vnamntcredit")
            txtDebitLast.Text = dr("vnamntdebit")
        End If

    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()

        S = "Select Top(100) PERCENT dbo.Customers.id, dbo.Customers.Cust_Code As [كود العميل], dbo.Customers.Vendorname As الاسم, dbo.Customers.addr As العنوان, dbo.Customers.tel1 As تليفون, dbo.Customers.vnamntcredit As مدين,     dbo.Customers.vnamntdebit AS دائن, dbo.Customers.notes As ملاحظات, dbo.Customers.Mobile As موبايل, dbo.Customers.Region As المنطقة, dbo.Customers.Apartment As الشقة, dbo.Customers.Mark As علامة,     dbo.Customers.Role AS الدور, dbo.PriceType.PriceTypeName As [نوع السعر], dbo.Customers.PriceType_ID As [كود نوع السعر], dbo.Employees.NameEmployee As الموظف, dbo.Customers.Company_Branch_ID From dbo.Customers INNER Join     dbo.Employees ON dbo.Customers.Emp_Code = dbo.Employees.EMPID LEFT OUTER Join dbo.PriceType ON dbo.Customers.PriceType_ID = dbo.PriceType.PriceType_ID Group By dbo.Customers.id, dbo.Customers.Cust_Code, dbo.Customers.Vendorname, dbo.Customers.addr, dbo.Customers.tel1, dbo.Customers.vnamntcredit, dbo.Customers.vnamntdebit, dbo.Customers.notes,    dbo.Customers.Mobile, dbo.Customers.Region, dbo.Customers.Apartment, dbo.Customers.Mark, dbo.Customers.Role, dbo.Customers.PriceType_ID, dbo.PriceType.PriceTypeName, dbo.Employees.NameEmployee,    dbo.Customers.Company_Branch_ID HAVING(dbo.Customers.id <> '')"
        If ChbAll.Checked = False Then
            S = S & " and dbo.Employees.NameEmployee = N'" & cmbEmployees.Text.Trim & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " and dbo.Customers.Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If
        S = S & " ORDER BY dbo.Customers.id DESC"
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.Rows.Count) = 0 Then Beep() : Exit Sub

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases (Company_Branch_ID,BILL_NO,CustomerName,itm_cat,itm_id,itm_name,totalpricebeforedisc,disc,totalpriceafterdisc,BEY,STAYING,Unity,Vendorname)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView1.Rows(i).Cells(1).Value & "',N'" & DataGridView1.Rows(i).Cells(2).Value & "',N'" & DataGridView1.Rows(i).Cells(3).Value & "',N'" & DataGridView1.Rows(i).Cells(4).Value & "',N'" & DataGridView1.Rows(i).Cells(7).Value & "',N'" & DataGridView1.Rows(i).Cells(8).Value & "',N'" & DataGridView1.Rows(i).Cells(9).Value & "',N'" & DataGridView1.Rows(i).Cells(10).Value & "',N'" & DataGridView1.Rows(i).Cells(11).Value & "',N'" & DataGridView1.Rows(i).Cells(12).Value & "',N'" & DataGridView1.Rows(i).Cells(13).Value & "',N'" & cmbEmployees.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        AddReportView()
        Dim rpt = New Rpt_DataCustomerALL

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        Dim txtNameAr, txtNameEn, txtTitel As TextObject
        txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
        txtNameEn.Text = NameEnCompany
        txtTitel = rpt.Section1.ReportObjects("txtTitel")
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()
        Frm_PrintReports.Text = "تقرير كل بيانات العملاء"

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub

    Public Sub Header_Balance_Last()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "SELECT MIN(CAST(bill_date As float)) as mb FROM Sales_Bill where Vendorname = N'" & Cmbvendorname.Text.Trim & "'"
        dr = cmd.ExecuteReader
        dr.Read()
        If dr(0) Is DBNull.Value Then
        Else
            CustMainDate = dr("mb")
        End If

        FormateDate(CustMainDate)
        'CustMainDateAll = R_date(CustMainDate)

        '===========================================================
        CustomerAccountTotalAllLast(Cmbvendorname.Text)

        txtCreditLast.Text = CountLastamntcredit
        txtDebitLast.Text = CountFirstamntdebit

    End Sub

    Private Sub FormateDate(ByVal literal As String)
        Dim substringYers As String = literal.Substring(0, 4)
        Dim substringMonth As String = Val(Mid$(literal, 5, Len(literal) - 6))
        Dim substringDay As String = literal.Substring(6)

        CustMainDateAll = substringYers + "/" + substringMonth + "/" + substringDay
    End Sub

    Friend Sub CustomerAccountTotalAllLast(ByVal Cmbvendorname As String)

        If Cmbvendorname = "" Then Exit Sub
        Dim vintinval, vinbtinval, vndiscount, vndiscountOther, BVstdiscount, VnPay, BVstPay, TotalActual, TotalBVstdiscount, TotalBVstPay, Totalvintinval, ValueVAT As Double
        ' مبيعات
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(totalpricebeforedisc) from Sales_bill where Vendorname = N'" & Cmbvendorname & "' and bill_date >=N'" & Cls.C_date(CustMainDateAll) & "' and bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then vintinval = 0 Else vintinval = dr(0)

        ' مرتجع مبيعات
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(totalpricebeforedisc) from IM_Bsal where Vendorname = N'" & Cmbvendorname & "' and bill_date >=N'" & Cls.C_date(CustMainDateAll) & "' and bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then vinbtinval = 0 Else vinbtinval = dr(0)

        'خصومات 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(amnt) from Vst_disc where Vendorname = N'" & Cmbvendorname & "' and pdate >=N'" & Cls.C_date(CustMainDateAll) & "' and pdate <=N'" & Cls.C_date(Dtp_To.Text) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then vndiscount = 0 Else vndiscount = dr(0)

        'خصومات أخرى 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(amnt) from Vst_disc_other where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then vndiscountOther = 0 Else vndiscountOther = dr(0)

        ' مرتجع خصومات
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(amnt) from IM_Vst_disc where Vendorname = N'" & Cmbvendorname & "' and pdate >=N'" & Cls.C_date(CustMainDateAll) & "' and pdate <=N'" & Cls.C_date(Dtp_To.Text) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then BVstdiscount = 0 Else BVstdiscount = dr(0)

        'مقبوضات  
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(VND_amx) from vst where Vendorname = N'" & Cmbvendorname & "' and VND_dt >=N'" & Cls.C_date(CustMainDateAll) & "' and VND_dt <=N'" & Cls.C_date(Dtp_To.Text) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then VnPay = 0 Else VnPay = dr(0)

        'مرتجع مقبوضات
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(VND_amx) from IM_Vst where Vendorname = N'" & Cmbvendorname & "' and VND_dt >=N'" & Cls.C_date(CustMainDateAll) & "' and VND_dt <=N'" & Cls.C_date(Dtp_To.Text) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then BVstPay = 0 Else BVstPay = dr(0)

        'ضريبة VAT 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(ValueVAT) from Sales_bill where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then ValueVAT = 0 Else ValueVAT = dr(0)
        ValueVAT = Math.Round(ValueVAT, 2)

        Totalvintinval = vintinval - vinbtinval
        TotalBVstdiscount = vndiscount + vndiscountOther - BVstdiscount
        TotalBVstPay = VnPay - BVstPay
        TotalActual = Totalvintinval - TotalBVstdiscount - TotalBVstPay + ValueVAT

        If TotalActual > 0 Then
            CountLastamntcredit = TotalActual
            CountLastamntdebit = 0
        ElseIf TotalActual < 0 Then
            CountLastamntcredit = 0
            CountLastamntdebit = TotalActual * -1
        ElseIf TotalActual = 0 Then
            CountLastamntcredit = 0
            CountLastamntdebit = 0
        End If
    End Sub

    Function R_date(ByVal dt As String) As Date
        Dim dy As String
        Dim mn As String
        Dim yr As String
        dy = Mid(dt, 7, 2)
        If Len(Trim(dy)) = 1 Then dy = "0" + Trim(Str(dy))
        mn = Mid(dt, 5, 2)
        If Len(Trim(mn)) = 1 Then mn = "0" + Trim(Str(mn))
        yr = Mid(dt, 1, 4)
        Return Convert.ToDateTime(yr & "/" & mn & "/" & dy)

    End Function


    Private Sub GetDataTotal()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT bill_No AS [رقم الفاتورة],Vendorname AS [اسم العميل], bill_date AS [تاريخ ],Stat AS [بيان], totalpriceafterdisc AS [مدين] FROM Sales_Bill where BILL_NO <> N'جرد'"

        If ChbAll.Checked = False Then
            S = S & " and Vendorname = N'" & Cmbvendorname.Text.Trim & "'"
        End If
        If Chek_WithoutDate.Checked = False Then
            S = S & " and bill_date >=N'" & Cls.C_date(Dtp_from.Text) & "' and bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " and Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridViewSal.DataSource = Cls.PopulateDataView(dr)
        Dim SM As String
        For i As Integer = 0 To DataGridViewSal.RowCount - 1
            SM = Val(DataGridViewSal.Rows(i).Cells(2).Value)
            SM = Cls.R_date(SM)
            DataGridViewSal.Rows(i).Cells(2).Value = SM
        Next

        For i As Integer = 0 To DataGridViewSal.RowCount - 1
            DataGridViewSal.Rows(i).Cells(3).Value = "مبيعات"
        Next

    End Sub

    Private Sub GetDataBsalTotal()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT bill_No AS [رقم الفاتورة],Vendorname AS [اسم العميل], bill_date AS [تاريخ ],Stat AS [بيان], totalpriceafterdisc AS [مدين] FROM IM_Bsal where BILL_NO <> N'جرد'"

        If ChbAll.Checked = False Then
            S = S & " and Vendorname = N'" & Cmbvendorname.Text.Trim & "'"
        End If
        If Chek_WithoutDate.Checked = False Then
            S = S & " and bill_date >=N'" & Cls.C_date(Dtp_from.Text) & "' and bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " and Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridViewBsal.DataSource = Cls.PopulateDataView(dr)
        Dim SM As String
        For i As Integer = 0 To DataGridViewBsal.RowCount - 1
            SM = Val(DataGridViewBsal.Rows(i).Cells(2).Value)
            SM = Cls.R_date(SM)
            DataGridViewBsal.Rows(i).Cells(2).Value = SM
        Next

        For i As Integer = 0 To DataGridViewBsal.RowCount - 1
            DataGridViewBsal.Rows(i).Cells(3).Value = "مرتجعات مبيعات"
        Next

    End Sub

    Friend Sub CustomerAccountTotal(ByVal Cmbvendorname As String, ByVal DateTimePicker1 As String, ByVal DateTimePicker2 As String)
        If Cmbvendorname = "" Then Exit Sub
        Dim vintinval, vinbtinval, vndiscount, vndiscountOther, BVstdiscount, VnPay, BVstPay, TotalBVstdiscount, TotalBVstPay, Totalvintinval, ValueVAT As Double
        ' مبيعات
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select totalpricebeforedisc from Sales_bill where Vendorname = N'" & Cmbvendorname & "' and  bill_date >=N'" & Cls.C_date(DateTimePicker1) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then vintinval = 0 Else vintinval = dr(0)
        ' مرتجع مبيعات
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(totalpricebeforedisc) from IM_Bsal where Vendorname = N'" & Cmbvendorname & "' and  bill_date >=N'" & Cls.C_date(DateTimePicker1) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then vinbtinval = 0 Else vinbtinval = dr(0)
        'خصومات 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(amnt) from Vst_disc where Vendorname = N'" & Cmbvendorname & "' and  pdate >=N'" & Cls.C_date(DateTimePicker1) & "' and pdate <=N'" & Cls.C_date(DateTimePicker2) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then vndiscount = 0 Else vndiscount = dr(0)
        'خصومات أخرى 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(amnt) from Vst_disc_other where Vendorname = N'" & Cmbvendorname & "' and  pdate >=N'" & Cls.C_date(DateTimePicker1) & "' and pdate <=N'" & Cls.C_date(DateTimePicker2) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then vndiscountOther = 0 Else vndiscountOther = dr(0)
        ' مرتجع خصومات
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(amnt) from IM_Vst_disc where Vendorname = N'" & Cmbvendorname & "' and  pdate >=N'" & Cls.C_date(DateTimePicker1) & "' and pdate <=N'" & Cls.C_date(DateTimePicker2) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then BVstdiscount = 0 Else BVstdiscount = dr(0)
        'مقبوضات  
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(VND_amx) from vst where Vendorname = N'" & Cmbvendorname & "' and  VND_dt >=N'" & Cls.C_date(DateTimePicker1) & "' and VND_dt <=N'" & Cls.C_date(DateTimePicker2) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then VnPay = 0 Else VnPay = dr(0)
        'مرتجع مقبوضات
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(VND_amx) from IM_Vst where Vendorname = N'" & Cmbvendorname & "' and  VND_dt >=N'" & Cls.C_date(DateTimePicker1) & "' and VND_dt <=N'" & Cls.C_date(DateTimePicker2) & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then BVstPay = 0 Else BVstPay = dr(0)

        'ضريبة VAT 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(ValueVAT) from Sales_bill where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then ValueVAT = 0 Else ValueVAT = dr(0)
        ValueVAT = Math.Round(ValueVAT, 2)

        Totalvintinval = vintinval - vinbtinval
        TotalBVstdiscount = vndiscount + vndiscountOther - BVstdiscount
        TotalBVstPay = VnPay - BVstPay
        TotalActual = Totalvintinval - TotalBVstdiscount - TotalBVstPay + ValueVAT

    End Sub

End Class