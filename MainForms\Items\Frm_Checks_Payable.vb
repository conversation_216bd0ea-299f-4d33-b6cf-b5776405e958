﻿Public Class Frm_Checks_Payable

   

    Private Sub Frm_Checks_Payable_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        txtCust_Name.Focus()
    End Sub

    Private Sub chkSearchChecks_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkSearchChecks.CheckedChanged
        If chkSearchChecks.Checked = True Then
            txtSearchNumChack.Visible = True
        Else
            txtSearchNumChack.Visible = False
        End If
    End Sub

    Private Sub ClearTest()
        txtAmount.Text = ""
        txtBank_Name.Text = ""
        txtCheck_Number.Text = ""
        txtCust_Name.Text = ""
        txtNotes.Text = ""
    End Sub

    Private Sub btnNew_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnNew.Click
        ClearTest()
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click

        If ValidateTextSave() = False Then Exit Sub

        Dim State_Check As String = "لم يتم السداد"
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Checks_Payable(Cust_Name,Amount,Due_Date,Bank_Name,Check_Number,Notes,State_Check) values ("
        S = S & "N'" & txtCust_Name.Text.Trim & "' ,N'" & txtAmount.Text.Trim & "' ,N'" & Cls.C_date(dtpDue_Date.Text) & "',N'" & txtBank_Name.Text & "',N'" & txtCheck_Number.Text & "',N'" & txtNotes.Text.Trim & "',N'" & State_Check & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        ClearTest()

        MsgBox("تم ترحيل الشيك الى تاريخ أستحقاقة", MsgBoxStyle.Information) : txtCust_Name.Focus()

    End Sub

    Function ValidateTextSave() As Boolean
        If txtCust_Name.Text = "" Then MsgBox("فضلا أختر العميل", MsgBoxStyle.Exclamation) : txtAmount.Focus() : Return False
        If txtAmount.Text.Trim = "" Then MsgBox("فضلا أدخل المبلغ", MsgBoxStyle.Exclamation) : txtAmount.Focus() : Return False
        If txtBank_Name.Text.Trim = "" Then MsgBox("فضلا أدخل أسم البنك", MsgBoxStyle.Exclamation) : txtBank_Name.Focus() : Return False
        If txtCheck_Number.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الشيك", MsgBoxStyle.Exclamation) : txtCheck_Number.Focus() : Return False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from Checks_Payable where Check_Number =N'" & txtCheck_Number.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H > 0 Then
            MsgBox("رقم الشيك مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtCheck_Number.Focus() : Return False
        End If

        Return True
    End Function

    Function ValidateTextEdit() As Boolean
        If txtSearchNumChack.Text = "" Then
            MsgBox("لاتوجد بيانات للحذف حدد رقم الشيك", MsgBoxStyle.Exclamation, "حذف")
            Exit Function
        End If

        If txtCust_Name.Text = "" Then MsgBox("فضلا أختر العميل", MsgBoxStyle.Exclamation) : txtAmount.Focus() : Return False
        If txtAmount.Text.Trim = "" Then MsgBox("فضلا أدخل المبلغ", MsgBoxStyle.Exclamation) : txtAmount.Focus() : Return False
        If txtBank_Name.Text.Trim = "" Then MsgBox("فضلا أدخل أسم البنك", MsgBoxStyle.Exclamation) : txtBank_Name.Focus() : Return False
        If txtCheck_Number.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الشيك", MsgBoxStyle.Exclamation) : txtCheck_Number.Focus() : Return False

        Return True
    End Function


    Private Sub txtSearchNumChack_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearchNumChack.TextChanged

        Dim PayMade As String
        PayMade = "لم يتم السداد"
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Checks_Payable where Check_Number=N'" & txtSearchNumChack.Text & "' and State_Check =N'" & PayMade & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            txtCust_Name.Text = dr("Cust_Name")
            txtCust_Name.Focus()
        End If
        If dr(1) Is DBNull.Value Then
        Else
            txtAmount.Text = dr("Amount")
        End If
        If dr(2) Is DBNull.Value Then
        Else
            dtpDue_Date.Text = Cls.R_date(dr("Due_Date"))
        End If
        If dr(3) Is DBNull.Value Then
        Else
            txtBank_Name.Text = dr("Bank_Name")
        End If
        If dr(4) Is DBNull.Value Then
        Else
            txtCheck_Number.Text = dr("Check_Number")
        End If
        If dr(5) Is DBNull.Value Then
        Else
            txtNotes.Text = dr("Notes")
        End If

    End Sub

    Private Sub btnEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnEdit.Click

        If ValidateTextEdit() = False Then Exit Sub
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Checks_Payable set Cust_Name =N'" & txtCust_Name.Text & "',Amount =N'" & txtAmount.Text & "',Due_Date =N'" & Cls.C_date(dtpDue_Date.Text) & "',Check_Number =N'" & txtCheck_Number.Text & "',Notes =N'" & txtNotes.Text & "' where Check_Number =N'" & txtSearchNumChack.Text & "'" : cmd.ExecuteNonQuery()

        ClearTest()

        MsgBox("تم تعديل الشيك", MsgBoxStyle.Information) : txtCust_Name.Focus()

    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If txtSearchNumChack.Text = "" Then
            MsgBox("لاتوجد بيانات للحذف حدد رقم الشيك", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If

        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        Dim PayMade As String
        PayMade = "لم يتم السداد"

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete from Checks_Payable where Check_Number =N'" & txtSearchNumChack.Text & "' and State_Check =N'" & PayMade & "'" : cmd.ExecuteNonQuery()

        MsgBox("تم حذف الشيك", MsgBoxStyle.Information) : txtCust_Name.Focus()
    End Sub

    Private Sub btnSearchChecks_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearchChecks.Click

        Frm_Checks_Payable_Show.Show()

    End Sub

    Private Sub txtCust_Name_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtCust_Name.KeyUp
        If e.KeyCode = 13 Then
            txtAmount.Focus()
        End If
    End Sub

    Private Sub txtAmount_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtAmount.KeyUp
        If e.KeyCode = 13 Then
            dtpDue_Date.Focus()
        End If
    End Sub

    Private Sub dtpDue_Date_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDue_Date.KeyUp
        If e.KeyCode = 13 Then
            txtBank_Name.Focus()
        End If
    End Sub

    Private Sub txtBank_Name_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtBank_Name.KeyUp
        If e.KeyCode = 13 Then
            txtCheck_Number.Focus()
        End If
    End Sub

    Private Sub txtCheck_Number_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtCheck_Number.KeyUp
        If e.KeyCode = 13 Then
            txtNotes.Focus()
        End If
    End Sub

    Private Sub txtNotes_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtNotes.KeyUp
        If e.KeyCode = 13 Then
            btnSave.PerformClick()
        End If
    End Sub
End Class