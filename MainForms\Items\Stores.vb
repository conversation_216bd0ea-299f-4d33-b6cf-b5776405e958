﻿Imports System.Drawing.Text
Public Class Stores
    Dim IDStore As String

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If Trim(txtStores.Text) = "" Then MsgBox("من فضلك ادخل بيانات صحيحة", MsgBoxStyle.Exclamation) : Exit Sub
        If Cls.Check_Field_Value("stores", "store", txtStores.Text) Then MsgBox("عفوا بيان مسجل من قبل", MsgBoxStyle.Exclamation) : Exit Sub

        If chkAddingItemsStore.Checked = True Then
            AddNewItems()
        End If

        Dim StausMainStore As Integer
        If RadioButton1.Checked = True Then
            StausMainStore = 0
        Else
            StausMainStore = 1
        End If

        Dim Branch_ID As String = Cls.Get_Code_Value_Branch("Company_Branch", "Company_Branch_ID", "Company_Branch_Name", cmbCompany_Branch_Name.Text)

        Cls.insert("stores", "Company_Branch_ID,store,UserName,StausMainStore", "N'" & Branch_ID & "',N'" & txtStores.Text & "',N'" & UserName & "',N'" & StausMainStore & "'")
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)
        txtStores.Text = ""
        Cls.fill_combo_Branch("stores", "store", ComboBox1)
        ChackStausMainStore()
    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ComboBox1.SelectedIndex = -1 Then MsgBox("من فضلك اختر بيان صحيح", MsgBoxStyle.Exclamation) : Exit Sub

        Dim msg As New MsgBoxResult
        msg = (MsgBox("هل تريد بالفعل الحذف", MsgBoxStyle.Question + MsgBoxStyle.YesNo))
        If msg = MsgBoxResult.Yes Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Cls.delete("stores", "store=N'" & ComboBox1.Text & "'")
            MsgBox("تم الحذف بنجاح", MsgBoxStyle.Information)
            Cls.fill_combo_Branch("stores", "store", ComboBox1)

        End If
    End Sub

    Private Sub Stores_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        'ChooseLanguageProgram(Me, True)
        'Me.InitializeComponent()
        'Me.MdiParent = MDIParent1
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cls.fill_combo_Branch("stores", "store", ComboBox1)
        Cls.fill_combo("Company_Branch", "Company_Branch_Name", cmbCompany_Branch_Name)
        ChackStausMainStore()
        If LanguageMainProgram = "العربية" Then
            SetArabic()
        ElseIf LanguageMainProgram = "English" Then
            SetEnglish()
        End If
    End Sub

    Private Sub ComboBox1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ComboBox1.SelectedIndexChanged
        Dim StausMainStore As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select store,StausMainStore,Company_Branch_ID from Stores where store=N'" & ComboBox1.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            txtStores.Text = dr("store")
            IDStore = dr("store")
            StausMainStore = dr("StausMainStore").ToString
            Dim Branch_ID As String = dr("Company_Branch_ID")
            If StausMainStore = "" Then
                RadioButton2.Checked = False
                RadioButton2.Checked = False
            Else
                If StausMainStore = 0 Then
                    RadioButton1.Checked = True
                Else
                    RadioButton2.Checked = True
                End If
            End If

            cmbCompany_Branch_Name.Text = Cls.Get_Code_Value("Company_Branch", "Company_Branch_Name", "Company_Branch_ID", Branch_ID)

        End If

    End Sub

    Private Sub ChackStausMainStore()
        Dim StausMainStore As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select store,StausMainStore from Stores where StausMainStore=0"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            StausMainStore = dr("StausMainStore")
            If StausMainStore = 0 Then
                RadioButton1.Enabled = False
            End If
        End If
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs) Handles btnEdit.Click
        Dim StausMainStore As Integer
        If RadioButton1.Checked = True Then
            StausMainStore = 0
        Else
            StausMainStore = 1
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update Stores set StausMainStore =N'" & StausMainStore & "' where store =N'" & ComboBox1.Text & "'"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update Stores set store =N'" & txtStores.Text & "' where store =N'" & IDStore & "'"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update Items set Stores =N'" & txtStores.Text & "' where Stores =N'" & IDStore & "'"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update BillsalData set Stores =N'" & txtStores.Text & "' where Stores =N'" & IDStore & "'"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update BilltINData set Stores =N'" & txtStores.Text & "' where Stores =N'" & IDStore & "'"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update IM_Bsal_Data set Stores =N'" & txtStores.Text & "' where Stores =N'" & IDStore & "'"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update IM_Btin_Data set Stores =N'" & txtStores.Text & "' where Stores =N'" & IDStore & "'"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update ItemsTransfer set Stores =N'" & txtStores.Text & "' where Stores =N'" & IDStore & "'"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update ItemsTransferData set StoresTO =N'" & txtStores.Text & "' where StoresTO =N'" & IDStore & "'"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update ItemsTransferData set StoresFrom =N'" & txtStores.Text & "' where StoresFrom =N'" & IDStore & "'"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update Manufacturing_BillsalData set Stores =N'" & txtStores.Text & "' where Stores =N'" & IDStore & "'"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update ManufacturingProductAdd set Stores =N'" & txtStores.Text & "' where Stores =N'" & IDStore & "'"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update Receive_BillsalData set Stores =N'" & txtStores.Text & "' where Stores =N'" & IDStore & "'"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update data_decayed set Stores =N'" & txtStores.Text & "' where Stores =N'" & IDStore & "'"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        MsgBox("تم التحديث بنجاح", MsgBoxStyle.Information)
        txtStores.Text = ""
        Cls.fill_combo_Branch("stores", "store", ComboBox1)
        ChackStausMainStore()

    End Sub

    Private Sub SetEnglish()
        Label1.Text = ENT_Language.Store
        Label3.Text = ENT_Language.StoreType
        Label2.Text = ENT_Language.Stores
        Label26.Text = ENT_Language.RegisterStores
        RadioButton1.Text = ENT_Language.Main
        RadioButton2.Text = ENT_Language.Subs
        Button1.Text = ENT_Language.Save
        btnEdit.Text = ENT_Language.Edit
        Button2.Text = ENT_Language.Delete
        Me.Text = ENT_Language.RegisterStores
    End Sub

    Private Sub SetArabic()
        Label1.Text = AR_Language.Store
        Label3.Text = AR_Language.StoreType
        Label2.Text = AR_Language.Stores
        Label26.Text = AR_Language.RegisterStores
        RadioButton1.Text = AR_Language.Main
        RadioButton2.Text = AR_Language.Subs
        Button1.Text = AR_Language.Save
        btnEdit.Text = AR_Language.Edit
        Button2.Text = AR_Language.Delete
        Me.Text = AR_Language.RegisterStores
    End Sub

    Dim pfc As New PrivateFontCollection()
    Private Function FindALLControlRecursive(ByVal list As List(Of Control), ByVal parent As Control) As List(Of Control)
        ' function that returns all control in a form, parent or child regardless of control's type
        If parent Is Nothing Then
            Return list
        Else
            list.Add(parent)
        End If
        For Each child As Control In parent.Controls
            FindALLControlRecursive(list, child)
        Next
        Return list
    End Function

    Private Sub Stores_Shown(sender As Object, e As EventArgs) Handles Me.Shown
        '' On Form1 shown, start applying font 
        'Dim CFontPath As String = Application.StartupPath
        'pfc.AddFontFile(CFontPath & "\" & ChangeFontProgram)
        'Dim allCtrl As New List(Of Control)
        'For Each ctrl As Control In FindALLControlRecursive(allCtrl, Me)
        '    ' You need to define which control type to change it's font family; not recommendd to just change all controls' fonts, it will create a missy shape
        '    If TypeOf ctrl Is Label Or TypeOf ctrl Is TextBox Or TypeOf ctrl Is Button Or TypeOf ctrl Is CheckBox Or TypeOf ctrl Is RadioButton Or TypeOf ctrl Is ProgressBar Or TypeOf ctrl Is GroupBox Or TypeOf ctrl Is ComboBox Or TypeOf ctrl Is TabControl Or TypeOf ctrl Is ListBox Or TypeOf ctrl Is DataGridView Then
        '        Dim CurrentCtrlFontSize = ctrl.Font.Size ' get current object's font size before applying new font family
        '        ctrl.Font = New Font(pfc.Families(0), CurrentCtrlFontSize, FontStyle.Bold)
        '    End If
        'Next
        'allCtrl.Clear()
    End Sub

    Private Sub AddNewItems()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Dim bill_no_Expired As String = ""
        Dim XLoopName As Double = 0
        Dim XLoopCode As Double = 0

        Dim Name As String
        Dim aray_itm_id, aray_group_name, aray_group_branch, aray_sname, aray_Unity, aray_rng, aray_TinPrice, aray_TinPriceAverage, aray_SalPrice, aray_WholePrice, aray_WholeWholePrice, aray_MinimumSalPrice, aray_RatePriceOffers, aray_RateWholePrice, aray_RateWholeWholePrice, aray_RateMinimumSalPrice, aray_PriceOffers, aray_UserName, aray_Company_Branch_ID, aray_RateVAT, aray_BalanceBarcode, aray_Vendorname, aray_RateDiscTinPrice, aray_RateDiscSalPrice, aray_RateDiscWholePrice, aray_RateDiscWholeWholePrice, aray_RateDiscTinPriceAfter, aray_RateDiscSalPriceAfter, aray_RateDiscWholePriceAfter, aray_RateDiscWholeWholePriceAfter, aray_TypePlusDiscRateSalPrice, aray_TypePlusDiscRateWholePrice, aray_TypePlusDiscRateWholeWholePrice, aray_DiscountedPrice, aray_DiscountedPrice2, aray_DiscountedPrice3, aray_QuickSearch, aray_PriceIncludesVAT, aray_Description, aray_LimitQuantity, aray_CompaniesID, aray_CompaniesName As New ArrayList

        aray_itm_id.Clear() : aray_group_name.Clear() : aray_group_branch.Clear() : aray_sname.Clear() : aray_Unity.Clear()
        aray_rng.Clear() : aray_TinPrice.Clear() : aray_TinPriceAverage.Clear() : aray_SalPrice.Clear() : aray_WholePrice.Clear()
        aray_WholeWholePrice.Clear() : aray_MinimumSalPrice.Clear() : aray_RatePriceOffers.Clear() : aray_RateWholePrice.Clear()
        aray_RateWholeWholePrice.Clear() : aray_RateMinimumSalPrice.Clear() : aray_PriceOffers.Clear()
        aray_UserName.Clear() : aray_Company_Branch_ID.Clear() : aray_RateVAT.Clear() : aray_BalanceBarcode.Clear() : aray_Vendorname.Clear()


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * from Items"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_itm_id.Add(dr("itm_id"))
            aray_group_name.Add(dr("group_name"))
            aray_group_branch.Add(dr("group_branch"))
            aray_sname.Add(dr("sname"))
            aray_Unity.Add(dr("Unity"))
            aray_rng.Add(dr("rng"))
            aray_TinPrice.Add(dr("TinPrice"))
            aray_TinPriceAverage.Add(dr("TinPriceAverage"))
            aray_SalPrice.Add(dr("SalPrice"))
            aray_WholePrice.Add(dr("WholePrice"))
            aray_WholeWholePrice.Add(dr("WholeWholePrice"))
            aray_MinimumSalPrice.Add(dr("MinimumSalPrice"))
            aray_RatePriceOffers.Add(dr("RatePriceOffers"))
            aray_RateWholePrice.Add(dr("RateWholePrice"))
            aray_RateWholeWholePrice.Add(dr("RateWholeWholePrice"))
            aray_RateMinimumSalPrice.Add(dr("RateMinimumSalPrice"))
            aray_RateDiscTinPrice.Add(dr("RateDiscTinPrice"))
            aray_RateDiscSalPrice.Add(dr("RateDiscSalPrice"))
            aray_RateDiscWholePrice.Add(dr("RateDiscWholePrice"))
            aray_RateDiscWholeWholePrice.Add(dr("RateDiscWholeWholePrice"))
            aray_RateDiscTinPriceAfter.Add(dr("RateDiscTinPriceAfter"))
            aray_RateDiscSalPriceAfter.Add(dr("RateDiscSalPriceAfter"))
            aray_RateDiscWholePriceAfter.Add(dr("RateDiscWholePriceAfter"))
            aray_RateDiscWholeWholePriceAfter.Add(dr("RateDiscWholeWholePriceAfter"))
            aray_TypePlusDiscRateSalPrice.Add(dr("TypePlusDiscRateSalPrice"))
            aray_TypePlusDiscRateWholePrice.Add(dr("TypePlusDiscRateWholePrice"))
            aray_TypePlusDiscRateWholeWholePrice.Add(dr("TypePlusDiscRateWholeWholePrice"))
            aray_DiscountedPrice.Add(dr("DiscountedPrice"))
            aray_DiscountedPrice2.Add(dr("DiscountedPrice2"))
            aray_DiscountedPrice3.Add(dr("DiscountedPrice3"))
            aray_PriceOffers.Add(dr("PriceOffers"))
            aray_UserName.Add(dr("UserName"))
            aray_Company_Branch_ID.Add(dr("Company_Branch_ID"))
            aray_RateVAT.Add(dr("RateVAT"))
            aray_BalanceBarcode.Add(dr("BalanceBarcode"))
            aray_Vendorname.Add(dr("Vendorname"))
            aray_QuickSearch.Add(dr("QuickSearch"))
            aray_PriceIncludesVAT.Add(dr("PriceIncludesVAT"))
            aray_Description.Add(dr("Description"))
            aray_LimitQuantity.Add(dr("LimitQuantity"))
            aray_CompaniesID.Add(dr("CompaniesID"))
            aray_CompaniesName.Add(dr("CompaniesName"))
        Loop

        For i As Integer = 0 To aray_itm_id.Count - 1
            Name = aray_sname(i).ToString()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where sname =N'" & Name & "' and Stores =N'" & txtStores.Text & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                XLoopName = 1
            End If
        Next
        If XLoopName = 1 Then
            MsgBox("يوجد اصناف موجودة بالمخزن المحدد", MsgBoxStyle.Exclamation)
            Exit Sub
        End If

        connectionStringClose()
        connectionStringTransaction()

        For i As Integer = 0 To aray_itm_id.Count - 1

            Dim X As String = "جرد"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Items (Company_Branch_ID,itm_id,group_name,group_branch,sname,Unity,rng,tinprice,salprice,TinPriceAverage,WholePrice,WholeWholePrice,MinimumSalPrice,RatePriceOffers,RateWholePrice,RateWholeWholePrice,RateMinimumSalPrice,RateDiscTinPrice,RateDiscSalPrice,RateDiscWholePrice,RateDiscWholeWholePrice,RateDiscTinPriceAfter,RateDiscSalPriceAfter,RateDiscWholePriceAfter,RateDiscWholeWholePriceAfter,TypePlusDiscRateSalPrice,TypePlusDiscRateWholePrice,TypePlusDiscRateWholeWholePrice,DiscountedPrice,DiscountedPrice2,DiscountedPrice3,tin,sal,btin,bsal,decayed,tinpricetotal,salpricetotal,btinpricetotal,bsalpricetotal,decayedpricetotal,store,ValStore,profits,UserName,Stores,QuickSearch,BalanceBarcode,RateVAT,Vendorname,PriceIncludesVAT,DeferredCurrentDiscount,Tag,Description,LimitQuantity,CompaniesID,CompaniesName) values ("
            S = S & "N'" & aray_Company_Branch_ID(i).ToString() & "',N'" & aray_itm_id(i).ToString() & "',N'" & aray_group_name(i).ToString() & "',N'" & aray_group_branch(i).ToString() & "',N'" & aray_sname(i).ToString() & "',N'" & aray_Unity(i).ToString() & "',"
            S = S & "" & Val(aray_rng(i).ToString()) & ","
            S = S & "" & Val(aray_TinPrice(i).ToString()) & ","
            S = S & "" & Val(aray_SalPrice(i).ToString()) & ","
            S = S & "" & Val(aray_TinPriceAverage(i).ToString()) & ","
            S = S & "" & Val(aray_WholePrice(i).ToString()) & ","
            S = S & "" & Val(aray_WholeWholePrice(i).ToString()) & ","
            S = S & "" & Val(aray_MinimumSalPrice(i).ToString()) & ","
            S = S & "" & Val(aray_RatePriceOffers(i).ToString()) & ","
            S = S & "" & Val(aray_RateWholePrice(i).ToString()) & ","
            S = S & "" & Val(aray_RateWholeWholePrice(i).ToString()) & ","
            S = S & "" & Val(aray_RateMinimumSalPrice(i).ToString()) & ","
            S = S & "" & Val(aray_RateDiscTinPrice(i).ToString()) & ","
            S = S & "" & Val(aray_RateDiscSalPrice(i).ToString()) & ","
            S = S & "" & Val(aray_RateDiscWholePrice(i).ToString()) & ","
            S = S & "" & Val(aray_RateDiscWholeWholePrice(i).ToString()) & ","
            S = S & "" & Val(aray_RateDiscTinPriceAfter(i).ToString()) & ","
            S = S & "" & Val(aray_RateDiscSalPriceAfter(i).ToString()) & ","
            S = S & "" & Val(aray_RateDiscWholePriceAfter(i).ToString()) & ","
            S = S & "" & Val(aray_RateDiscWholeWholePriceAfter(i).ToString()) & ","
            S = S & "" & Val(aray_TypePlusDiscRateSalPrice(i).ToString()) & ","
            S = S & "" & Val(aray_TypePlusDiscRateWholePrice(i).ToString()) & ","
            S = S & "" & Val(aray_TypePlusDiscRateWholeWholePrice(i).ToString()) & ","
            S = S & "" & Val(aray_DiscountedPrice(i).ToString()) & ","
            S = S & "" & Val(aray_DiscountedPrice2(i).ToString()) & ","
            S = S & "" & Val(aray_DiscountedPrice3(i).ToString()) & ","
            S = S & "0,0,0,0,0,0,0,0,0,0,0,0,0,N'" & aray_UserName(i).ToString() & "',N'" & txtStores.Text.Trim & "',N'" & aray_QuickSearch(i).ToString() & "',N'" & Val(aray_BalanceBarcode(i).ToString()) & "',N'" & Val(aray_RateVAT(i).ToString()) & "',N'" & aray_Vendorname(i).ToString() & "'," & aray_PriceIncludesVAT(i).ToString() & "," & DeferredCurrentDiscount & ",N'" & aray_rng(i).ToString() & "',N'" & aray_Description(i).ToString() & "',N'" & aray_LimitQuantity(i).ToString() & "',N'" & aray_CompaniesID(i).ToString() & "',N'" & aray_CompaniesName(i).ToString() & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            bill_no_Expired = MaxRecordTables("BilltINData", "bill_no_Expired")

            Dim bill_date, bill_EndDate, Expired, bill_ProductionDate As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_date,bill_EndDate,Expired,bill_ProductionDate from BilltINData where itm_id=N'" & aray_itm_id(i).ToString() & "' and bill_no =N'جرد' "
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                bill_date = dr(0).ToString
                bill_EndDate = dr(1).ToString
                Expired = dr(2).ToString
                bill_ProductionDate = dr(3).ToString
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into BilltINData (Company_Branch_ID,bill_no,itm_id,itm_cat,group_branch,itm_name,itm_Unity,price,TinPriceAverage,qu,qu_unity,totalprice,UserName,Stores,bill_date,bill_EndDate,Expired,bill_ProductionDate,Vendorname,qu_expired,Treasury_Code,bill_no_Expired,CurrentStock,CurrentStockTotal,Discounts)"
            S = S & " values (N'" & Company_Branch_ID & "',N'" & X & "',N'" & aray_itm_id(i).ToString() & "',N'" & aray_group_name(i).ToString() & "',N'" & aray_group_branch(i).ToString() & "',N'" & aray_sname(i).ToString() & "',N'" & aray_Unity(i).ToString() & "'," & Val(aray_TinPrice(i).ToString()) & "," & Val(aray_TinPrice(i).ToString()) & "," & Val(0) & "," & Val(0) & "," & Val(0) * Val(0) & ",N'" & UserName & "',N'" & txtStores.Text & "',N'" & bill_date & "',N'" & bill_EndDate & "',N'" & Expired & "',N'" & bill_ProductionDate & "',N'" & aray_Vendorname(i).ToString() & "'," & Val(0) & ",N'" & Treasury_Code & "',N'" & bill_no_Expired & "'," & Val(0) & "," & Val(aray_TinPrice(i).ToString()) * Val(0) & "," & Val(aray_RateDiscTinPrice(i).ToString()).ToString() & ")"
            cmd.CommandText = S : cmd.ExecuteNonQuery()


            IM.Store(aray_itm_id(i).ToString(), txtStores.Text)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & X & "' and itm_id = N'" & aray_itm_id(i).ToString() & "' and Stores =N'" & txtStores.Text & "'" : cmd.ExecuteNonQuery()

            IM.StoreExpired(aray_itm_id(i).ToString(), txtStores.Text, bill_EndDate, bill_no_Expired)

        Next
        trans.Commit()
        connectionStringOpen()

    End Sub

End Class