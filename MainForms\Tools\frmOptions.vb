﻿Imports Microsoft.Win32
Imports System.Drawing.Text
Public Class frmOptions
    Dim Reg As Registry
    Dim Startup As String = "Software\Microsoft\Windows\CurrentVersion\Run"
    Dim ID_Currency As String
    Dim SQLServerName As String

    Private Sub frmOptions_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Get_Add_And_Update_Option()
        PanelUnity.Top = 5000
        PanelCurrency.Top = 5000

        Cls.fill_combo_Branch("BankData", "AccountNumber", cmbAccountNumber)
        Cls.fill_combo_Branch("BankData", "BankName", cmbBank)
        Cls.fill_combo_Branch("Type_Currency", "Name_Currency", cmbCurrencyBank)
        Cls.fill_combo_Branch("stores", "store", cmbStoresName)
        Cls.fill_combo_Branch("Treasury", "Treasury_Name", cmbTreasuryName)
        Bra.Fil("Type_Currency", "Name_Currency", cmbType_Currency)
        Bra.Fil("Type_Currency", "Name_Currency", cmbDefaultCurrencyProgram)
        Bra.Fil("Type_Unity", "Unity_Name", cmbType_Unity)
        Me.Label10.Text = Format(DateTime.Now, "dd-MM-yyyy")
        Me.Label11.Text = MydateWord(DateTime.Now)
        ComboBox1.Text = mykey.GetValue("UP", "NO")
        ComboBox2.Text = mykey.GetValue("BACKUP", "NO")
        ComboBox3.Text = mykey.GetValue("BACKUPTIME", "NO")
        cmbPrintSales.Text = mykey.GetValue("PRINTSALES", "NO")
        cmbDataBase.Text = mykey.GetValue("DataBase", "DatabaseElectric")
        cmbLanguageScripting.Text = mykey.GetValue("LANGUAGE", "ARABIC")

        If PrintSmall = "NO" Then
            rdoPrintA4.Checked = True
        End If
        If PrintSmall = "YES" Then
            rdoPrintSmall.Checked = True
        End If
        If PrintSmall = "A5" Then
            rdoPrintA5.Checked = True
        End If

        Dim FocusText As String = mykey.GetValue("FocusText", "NO")
        If FocusText = "NO" Then
            rdoFocusTextItems.Checked = True
        End If
        If FocusText = "YES" Then
            rdoFocusTextParcode.Checked = True
        End If
        If FocusText = "SearchFree" Then
            rdoFocusTextSearchFree.Checked = True
        End If

        ComboBox7.Text = mykey.GetValue("ReturnInvoice", "NO")
        ComboBox6.Text = mykey.GetValue("BACKUPCOMBO", "0")
        cmbSalesTax.Text = mykey.GetValue("SalesTax", "0")
        Me.Label8.Text = mykey.GetValue("SONG", "None")

        'السماح بالمبيع بدون رصيد بالمخزن من الصنف
        cmbUseOnlySales.Text = mykey.GetValue("UseOnlySales", "NO")
        cmbType_Currency.Text = mykey.GetValue("TypeCurrency", "جنية مصرى")
        FillSettingDataGrid("ID_Currency", "Name_Currency", "Type_Currency", dgvCurrency)
        FillSettingDataGrid("Unity_ID", "Unity_Name", "Type_Unity", dgvUnity)
        cmbAddBillAuto.Text = mykey.GetValue("AddBillAuto", "NO")

        'شاشة المبيعات فى حفظ الفاتورة يتم تعديل سعر البيع
        cmbUpdateSalPrice.Text = mykey.GetValue("UpdateSalPrice", "NO")

        'تعديل سعر البيع فى كل المخازن
        UpdateSalPriceAllStores = mykey.GetValue("UpdateSalPriceAllStores", "NO")
        If UpdateSalPriceAllStores = "NO" Then
            chkUpdateSalPriceAllStores.Checked = False
        Else
            chkUpdateSalPriceAllStores.Checked = True
        End If

        cmbGroup_Branch.Text = mykey.GetValue("GroupBranch", "NO")
        Dim DiscountQuantity As String = mykey.GetValue("SelectDiscountQuantity", "SelectPrice")
        If DiscountQuantity = "SelectPrice" Then
            rdoSelectPrice.Checked = True
        End If
        If DiscountQuantity = "SelectDiscount" Then
            rdoSelectDiscount.Checked = True
        End If
        If DiscountQuantity = "SelectQuantity" Then
            rdoSelectQuantity.Checked = True
        End If
        cmbStoresName.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        cmbLastSalPriceCustomer.Text = mykey.GetValue("LastSalPriceCustomer", "NO")
        cmbBillnoAutoNumber.Text = mykey.GetValue("BillnoAutonumber", "YES")
        cmbMoreItemsInvoice.Text = mykey.GetValue("MoreItemsInvoice", "NO")
        cmbExpirationDate.Text = mykey.GetValue("ExpirationDate", "NO")
        cmbHideQuntItems.Text = mykey.GetValue("HideQuntItems", "NO")
        cmbAllowSellLessTinPrice.Text = mykey.GetValue("AllowSellLessTinPrice", "NO")
        cmbLastTinPriceItems.Text = mykey.GetValue("LastTinPriceItems", "NO")
        cmbSearchForProduct.Text = mykey.GetValue("SearchForProduct", "NO")
        cmbAcceptedSalePriceZero.Text = mykey.GetValue("AcceptedSalePriceZero", "NO")
        cmbAddingNewItmesFromPurchase.Text = mykey.GetValue("AddingNewItmesFromPurchase", "NO")
        cmbAddMoreInvoiceSeparately.Text = mykey.GetValue("AddMoreInvoiceSeparately", "NO")
        cmbHideWholeWholeSalePrice.Text = mykey.GetValue("HideWholeWholeSalePrice", "NO")
        cmbInvoicePrintedTwoCopies.Text = mykey.GetValue("InvoicePrintedTwoCopies", "NO")
        cmbShowItemImageSalesScreen.Text = mykey.GetValue("ShowItemImageSalesScreen", "NO")
        cmbUseAccounts.Text = mykey.GetValue("UseAccounts", "NO")
        cmbNotLoadItemDataScreensOpen.Text = mykey.GetValue("NotLoadItemDataScreensOpen", "NO")
        cmbOpenCashDrawerInvoicePrinting.Text = mykey.GetValue("OpenCashDrawerInvoicePrinting", "NO")
        Dim SelectTypeDeviceCar As String = mykey.GetValue("SelectTypeMaintenanceDeviceCar", "مركز صيانة السيارات")
        If SelectTypeDeviceCar = "CarMaintenance" Then
            cmbSelectTypeMaintenanceDeviceCar.Text = "مركز صيانة السيارات"
        End If
        If SelectTypeDeviceCar = "DevicesMaintenance" Then
            cmbSelectTypeMaintenanceDeviceCar.Text = "مركز صيانة الاجهزة"
        End If
        cmbUseColorWithItems.Text = mykey.GetValue("UseColorWithItems", "")
        cmbLanguageMainProgram.Text = mykey.GetValue("LanguageMainProgram", "Arabic")
        cmbUseManufacturingProduct.Text = mykey.GetValue("UseManufacturingProduct", "NO")
        Dim AccountPriceBiggest As String = mykey.GetValue("AccountPriceBiggest", "الأكبر")
        Dim AccountPriceMedium As String = mykey.GetValue("AccountPriceMedium", "المتوسط")
        Dim AccountPriceSmall As String = mykey.GetValue("AccountPriceSmall", "الأصغر")
        ' إظهار عنوان العميل فى فاتورة المبيعات
        cmbShowCustomerAddressSales.Text = mykey.GetValue("ShowCustomerAddressSales", "NO")

        cmbTextNotActivateEditSalesPrice.Text = mykey.GetValue("TextNotActivateEditSalesPrice", "NO")
        cmbValueDiscountTax.Text = mykey.GetValue("ValueDiscountTax", "0")
        cmbDoNotDiscountsSalesScreen.Text = mykey.GetValue("DoNotDiscountsSalesScreen", "NO")
        cmbBarcodeMore.Text = mykey.GetValue("BarcodeMore", "NO")
        cmbSalesPricePublic.Text = mykey.GetValue("SalesPricePublic", "NO")
        cmbNotUnityItemsProgram.Text = mykey.GetValue("NotUnityItemsProgram", "NO")
        ' أختيار الصنف من قائمة الاصناف يكون بزرار أنتر
        cmbSelectItemsComboBoxEnter.Text = mykey.GetValue("SelectItemsComboBoxEnter", "NO")

        cmbDefaultSellingPrices.Text = mykey.GetValue("DefaultSellingPrices", "سعر البيع")
        cmbSalesBillNotDiscount.Text = mykey.GetValue("SalesBillNotDiscount", "NO")
        cmbPurchaseTax.Text = mykey.GetValue("PurchaseTax", "0")
        cmbParcodeEdit13Number.Text = mykey.GetValue("ParcodeEdit13Number", "NO")
        cmbType_Unity.Text = mykey.GetValue("DefaultUnityName", "قطعة")
        cmbRngDefaultStock.Text = mykey.GetValue("RngDefaultStock", "2")
        cmbShow_Height_Width_Altitude_Density.Text = mykey.GetValue("Show_Height_Width_Altitude_Density", "NO")
        cmbShowBalanceCashInvoice.Text = mykey.GetValue("ShowBalanceCashInvoice", "NO")
        cmbShowCustomerBalanceSalesScreen.Text = mykey.GetValue("ShowCustomerBalanceSalesScreen", "NO")
        cmbSalesBillNotDiscountBill.Text = mykey.GetValue("SalesBillNotDiscountBill", "NO")
        cmbCustomizeSaleTermAdminOnly.Text = mykey.GetValue("CustomizeSaleTermAdminOnly", "NO")
        cmbCustomizeSaleWholesalePriceAdminOnly.Text = mykey.GetValue("CustomizeSaleWholesalePriceAdminOnly", "NO")
        cmbRateBankExpensesPaidByVisa.Text = mykey.GetValue("RateBankExpensesPaidByVisa", "0")
        cmbDesignAnotherSalesInvoice.Text = mykey.GetValue("DesignAnotherSalesInvoice", "تصميم رقم 1")
        cmbItemAddedInvoiceSavedAutomatically.Text = mykey.GetValue("ItemAddedInvoiceSavedAutomatically", "NO")
        cmbUseBalanceBarcode.Text = mykey.GetValue("UseBalanceBarcode", "NO")
        cmbDefaultCurrencyProgram.Text = mykey.GetValue("DefaultCurrencyProgram", "جنية مصرى")
        cmbNOTUseUnitItemsPrice.Text = mykey.GetValue("NOTUseUnitItemsPrice", "NO")
        cmbRunDatabaseInternet.Text = mykey.GetValue("RunDatabaseInternet", "NO")

        Dim XNetwork As String = mykey.GetValue("NetworkNameInternet", "Source")
        If XNetwork = "Source" Then
            rdoSource.Checked = True
        End If
        If XNetwork = "NO" Then
            rdoNoNetwork.Checked = True
        End If
        If XNetwork = "YES" Then
            rdoYesNetwork.Checked = True
        End If
        SQLServerName = Environment.MachineName
        txtDataBaseName.Text = mykey.GetValue("DatabaseElectricNetwork", "DatabaseElectricNetwork")
        txtServerName.Text = mykey.GetValue("ServerNameInternet", SQLServerName)
        txtUserName.Text = mykey.GetValue("UserNameInternet", "sa")
        txtPassword.Text = mykey.GetValue("PasswordInternet", "0124363")
        cmbTreasuryName.Text = mykey.GetValue("TreasuryName", "الخزينة الرئيسية")
        cmbValueVAT.Text = mykey.GetValue("ValueVAT", "NO")
        cmbTinPriceAverageThreeDigits.Text = mykey.GetValue("TinPriceAverageThreeDigits", "NO")
        cmbDateNotBeenActivatedSales.Text = mykey.GetValue("DateNotBeenActivatedSales", "NO")

        cmbBank.Text = mykey.GetValue("BankPayCustNameBank", "البنك الاهلى")
        cmbCurrencyBank.Text = mykey.GetValue("BankPayCustNameCurrency", "الجنية المصرى")
        cmbAccountNumber.Text = mykey.GetValue("BankPayCustAccountNumber", "0")
        cmbActivateAddDepositBankCustomerPayments.Text = mykey.GetValue("ActivateAddDepositBankCustomerPayments", "NO")
        cmbAllowCustOrSupplierReturnItemNotMovement.Text = mykey.GetValue("AllowCustOrSupplierReturnItemNotMovement", "NO")
        cmbBeforeScanningBarcodeQuantityStar.Text = mykey.GetValue("BeforeScanningBarcodeQuantityStar", "NO")

        'عدم التحكم فى رقم الفاتورة الخاص بفاتورة المبيعات
        cmbNonControlBillNumberSalesAutomatic.Text = mykey.GetValue("NonControlBillNumberSalesAutomatic", "NO")

        'عدم التحكم فى نسبة السائق فى توصيل الطلبات
        cmbNonControlDriverRateOrderDelivery.Text = mykey.GetValue("NonControlDriverRateOrderDelivery", "NO")

        'السماح فى المبيعات بالمدفوع والباقى للعميل بصورة نقدية
        cmbAllowingSalesPaidAndRestToCustomerCash.Text = mykey.GetValue("AllowingSalesPaidAndRestToCustomerCash", "NO")

        'شاشة المبيعات تاكيد أسم المندوب يكون مسجل
        cmbConfirmDelegateRegisteredSales.Text = mykey.GetValue("ConfirmDelegateRegisteredSales", "NO")

        'التعامل مع نظام الصيدلية
        cmbDealingPharmacySystem.Text = mykey.GetValue("DealingPharmacySystem", "NO")

        'عدم التحكم فى أسم المخزن فى شاشة المبيعات
        cmbNotControlStoreTheSales.Text = mykey.GetValue("NotControlStoreTheSales", "NO")

        'طباعة فاتورة المسؤول عن المخزن للصرف بدون اسعار
        cmbPrintBillStoreExchangeWithoutPrices.Text = mykey.GetValue("PrintBillStoreExchangeWithoutPrices", "NO")

        'طباعة فاتورة المسؤول عن المخزن للصرف بالمجموعة
        cmbPrintBillStoreExchangeWithoutGroub.Text = mykey.GetValue("PrintBillStoreExchangeWithoutGroub", "NO")

        'إظهار مجموعة الصنف فى شاشة المبيعات
        cmbShowGroupItemSalesScreen.Text = mykey.GetValue("ShowGroupItemSalesScreen", "NO")

        'إظهار نسبة السعر بعد الخصم الصنف فى المبيعات
        cmbShowDiscountRateItemSales.Text = mykey.GetValue("ShowDiscountRateItemSales", "NO")

        'إضافة عميل جديد فى شاشة المبيعات تلقائى
        cmbAddNewCustomerSalesAutomatically.Text = mykey.GetValue("AddNewCustomerSalesAutomatically", "NO")

        'تحديد البحث الحر فى قائمة الاصناف
        cmbSeletctFreeSearchTheListOfItems.Text = mykey.GetValue("SeletctFreeSearchTheListOfItems", "NO")

        'تفعيل الفاتورة الالكترونية
        cmbActivateElectronicBill.Text = mykey.GetValue("ActivateElectronicBill", "NO")

        'تفعيل خاصية أخر سعر بيع للعميل مع ضرب سعر الوحدة الاصغر فى كمية الاكبر
        cmbLastSalPriceCustomerUnitPriceByLargQuant.Text = mykey.GetValue("LastSalPriceCustomerUnitPriceByLargQuant", "NO")

        'التعامل مع سيريال للاصناف
        cmbDealingWithSerialItems.Text = mykey.GetValue("DealingWithSerialItems", "NO")
        'التعامل مع سيريال للاصناف منفصلة ومتصلة
        cmbDealingWithSerialItemsSeparateConnected.Text = mykey.GetValue("DealingWithSerialItemsSeparateConnected", "منفصلة")

        'قيمة ضريبة أرباح تجارية وصناعية
        cmbCommercialAndIndustrialProfitsTax.Text = mykey.GetValue("CommercialAndIndustrialProfitsTax", "0")
        cmbCommercialAndIndustrialProfitsTaxYESNO.Text = mykey.GetValue("CommercialAndIndustrialProfitsTaxYESNO", "NO")


        'السماح باضافة صنف جديد من شاشة المبيعات
        cmbAddingNewItmesFromSales.Text = mykey.GetValue("AddingNewItmesFromSales", "NO")

        'تحديد رقم الفاتورة الضريبية
        cmbSelectTaxInvoiceNumber.Text = mykey.GetValue("SelectTaxInvoiceNumber", "NO")
        txtSelectTaxInvoiceNumberTax.Text = mykey.GetValue("SelectTaxInvoiceNumberTax", "0001")
        txtSelectTaxInvoiceNumberNotTax.Text = mykey.GetValue("SelectTaxInvoiceNumberNotTax", "10000000")

        'لغة طباعة فاتورة المبيعات
        cmbSalesInvoicePrintingLanguage.Text = mykey.GetValue("SalesInvoicePrintingLanguage", "Arabic")

        'التعامل مع ضريبة القيمة المضافة قمية
        cmbValueVATNumber.Text = mykey.GetValue("ValueVATNumber", "0")

        'إظهار اختيار الاصناف بالازرار فى شاشة المبيعات
        cmbSalesMenuTouch.Text = mykey.GetValue("SalesMenuTouch", "NO")

        'التعامل مع نظام الورديات فى شاشة المبيعات
        cmbDealingSheftStatusSales.Text = mykey.GetValue("DealingSheftStatusSales", "NO")

        'تفعيل هذا الجهاز بمخزن محدد جميع العمليات عليها
        cmbActivateSpecificDeviceStore.Text = mykey.GetValue("ActivateSpecificDeviceStore", "NO")

        'السماح بالتعامل مع أخر سعر الشراء للصنف
        cmbAllowAddPurchasesTinPrice.Text = mykey.GetValue("AllowAddPurchasesTinPrice", "NO")

        'عدم التحكم فى التاريخ فى البرنامج
        cmbDateNotBeenActivatedPrograms.Text = mykey.GetValue("DateNotBeenActivatedPrograms", "NO")

        'تحديد التعامل مع فاتورة مخصصة
        cmbDefineCustomInvoiceHandling.Text = mykey.GetValue("DefineCustomInvoiceHandling", "NO")

        'طباعة رقم فاتورة للعميل مع الفاتورة الاساسية
        cmbPrintInvoiceNumberCustomerMainInvoice.Text = mykey.GetValue("PrintInvoiceNumberCustomerMainInvoice", "NO")

        'تغيير أسم ملف تشغيل البرنامج
        txtChangeFileNameProgramFITSOFT.Text = mykey.GetValue("ChangeFileNameProgramFITSOFT", "FIT SOFT.EXE")

        'شاشة NCR Port Name
        txtScreenNCRHomePortName.Text = mykey.GetValue("ScreenNCRHomePortName", "COM5")

        'شاشة NCR Baud Rate
        txtScreenNCRHomeBaudRate.Text = mykey.GetValue("ScreenNCRHomeBaudRate", "9600")

        'التحكم فى الخزينة
        cmbTreasuryControl.Text = mykey.GetValue("TreasuryControl", "NO")

        'تفعيل الفواتير المعلقة
        cmbActivatePendingBill.Text = mykey.GetValue("ActivatePendingBill", "NO")

        'تغيير من حجم الخط فى شاشة المبيعات
        cmbChangeFontSizeSalesText.Text = mykey.GetValue("ChangeFontSizeSalesText", "Font4")

        'إظهار رسالة تاكيد قبل حفظ فاتورة مبيعات
        cmbShowConfirmationMessageBeforeSavingSalesInvoice.Text = mykey.GetValue("ShowConfirmationMessageBeforeSavingSalesInvoice", "NO")

        'عدم التحكم فى التاريخ فى باقى الشاشات
        cmbDateNotBeenActivatedOutcome.Text = mykey.GetValue("DateNotBeenActivatedOutcome", "NO")

        'عدم التحكم فى رقم الدفعة
        cmbNumberPayNotBeenActivated.Text = mykey.GetValue("NumberPayNotBeenActivated", "NO")


        'تفعيل وضع فواصل بين الارقام (10.000.000)
        cmbActivateFormatNumberWithSeparators.Text = mykey.GetValue("ActivateFormatNumberWithSeparators", "NO")

        'لا يسمح بالبيع عند الحد الادنى للكمية
        cmbNoSalesAllowedTheMinimumQuantity.Text = mykey.GetValue("NoSalesAllowedTheMinimumQuantity", "NO")

        'عدم تحميل بيانات العملاء فى شاشة المبيعات
        cmbNotUploadingCustomerDataToTheSalesScreen.Text = mykey.GetValue("NotUploadingCustomerDataToTheSalesScreen", "NO")

        'اظهار تعديل رصيد أول المدة فى شاشة تعديل الاصناف
        cmbShowBeginningBalanceAdjustmentItemScreen.Text = mykey.GetValue("ShowBeginningBalanceAdjustmentItemScreen", "NO")

        'اظهار خدمة التوصيل فى شاشة المبيعات
        cmbShowDeliveryServiceSales.Text = mykey.GetValue("ShowDeliveryServiceSales", "NO")

        'طباعة حساب العميل بعد طباعة الفاتورة
        cmbPrintCustomerAccountAfterPrinting.Text = mykey.GetValue("PrintCustomerAccountAfterPrinting", "NO")

        'تحديد حالة الدفع بالاجل فى شاشة المبيعات افتراضى
        cmbSelectDeferredPaymentStatusSalesScreenDefault.Text = mykey.GetValue("SelectDeferredPaymentStatusSalesScreenDefault", "NO")

        'إخفاء البحث عن الصنف فى شاشة المشتريات يكون بأسم الصنف
        HideItemGroupSearchForProduct = mykey.GetValue("HideItemGroupSearchForProduct", "NO")
        If HideItemGroupSearchForProduct = "NO" Then
            chkHideItemGroupSearchForProduct.Checked = False
        Else
            chkHideItemGroupSearchForProduct.Checked = True
        End If

        'التعامل مع نظام إدارة عهدة الموظف
        cmbEmployeeCustody.Text = mykey.GetValue("EmployeeCustody", "NO")


    End Sub

    Private Sub CMDPATHSONG_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CMDPATHSONG.Click
        On Error Resume Next
        Dim i As Integer
        OpenFileDialog1.Filter = "Mpeg Files (*.mp2;*.mp3;*.mp4;*.mpeg;*.mpg;*.dat)|*.mp2;*.mp3;*.mp4;*.mpeg;*.mpg;*.dat|Wave Files (*.wav)| *.wav|RealMedia(*.rm;*.ram)|*.rm;*.ram|Windows Media(*.avi;*.asf;*.wmf)|*.avi;*.asf;*.wmf|FLASHFILES (*.SWF)|*.SWF|Image Files (*.bmp) | *.bmp|JPEG Files (*.jpg)|*.jpg|Acrobat Reader Files|*.pdf|All Files(*.*)|*.*"
        OpenFileDialog1.RestoreDirectory = True
        OpenFileDialog1.ShowDialog()
        If OpenFileDialog1.FileName = "" Then
            Exit Sub
        Else
            If Len(OpenFileDialog1.FileName) > 0 Then
                mykey.SetValue("SONG", OpenFileDialog1.FileName)
                Me.Label8.Text = OpenFileDialog1.FileName
            Else
                mykey.SetValue("SONG", "None")
            End If
        End If
    End Sub

    Private Sub ButtonAPPLY_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAPPLY.Click
        On Error Resume Next
        Set_Add_And_Update_Option()

        If Me.ComboBox1.Text = "YES" Then
            key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(Startup, True)
            key.SetValue("CUSTOMS", Application.ExecutablePath)
            mykey.SetValue("UP", "YES")
        ElseIf Me.ComboBox1.Text = "NO" Then
            key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(Startup, True)
            key.DeleteValue("CUSTOMS")
            mykey.SetValue("UP", "NO")
        End If
        If Me.ComboBox2.Text = "YES" Then
            mykey.SetValue("BACKUP", "YES")
        ElseIf Me.ComboBox2.Text = "NO" Then
            mykey.SetValue("BACKUP", "NO")
        End If
        If Me.cmbPrintSales.Text = "YES" Then
            mykey.SetValue("PRINTSALES", "YES")
        ElseIf Me.cmbPrintSales.Text = "NO" Then
            mykey.SetValue("PRINTSALES", "NO")
        End If

        If Me.cmbDataBase.Text = "DatabaseElectric" Then
            mykey.SetValue("DataBase", "DatabaseElectric")
        ElseIf Me.cmbDataBase.Text = "DatabaseElectric2" Then
            mykey.SetValue("DataBase", "DatabaseElectric2")
        End If

        If Me.ComboBox3.Text = "YES" Then
            mykey.SetValue("BACKUPTIME", "YES")
            mykey.SetValue("BACKUPCOMBO", Me.ComboBox6.Text)
        ElseIf Me.ComboBox3.Text = "NO" Then
            mykey.SetValue("BACKUPTIME", "NO")
        End If

        If Me.ComboBox7.Text = "YES" Then
            mykey.SetValue("ReturnInvoice", "YES")
        ElseIf Me.ComboBox7.Text = "NO" Then
            mykey.SetValue("ReturnInvoice", "NO")
        End If

        If rdoFocusTextItems.Checked = True Then
            mykey.SetValue("FocusText", "NO")
            FocusText = "NO"
        End If
        If rdoFocusTextParcode.Checked = True Then
            mykey.SetValue("FocusText", "YES")
            FocusText = "YES"
        End If
        If rdoFocusTextSearchFree.Checked = True Then
            mykey.SetValue("FocusText", "SearchFree")
            FocusText = "SearchFree"
        End If
        mykey.SetValue("SalesTax", cmbSalesTax.Text)
        ShowTax = cmbSalesTax.Text
        mykey.SetValue("ValueVAT", cmbValueVAT.Text)
        ShowValueVAT = cmbValueVAT.Text


        If Me.cmbLanguageScripting.Text = "ARABIC" Then
            mykey.SetValue("LANGUAGE", "ARABIC")
        ElseIf Me.cmbLanguageScripting.Text = "ENGLISH" Then
            mykey.SetValue("LANGUAGE", "ENGLISH")
        End If

        If Me.cmbUseOnlySales.Text = "YES" Then
            mykey.SetValue("UseOnlySales", "YES")
        ElseIf Me.cmbUseOnlySales.Text = "NO" Then
            mykey.SetValue("UseOnlySales", "NO")
        End If

        If Me.cmbAddBillAuto.Text = "YES" Then
            mykey.SetValue("AddBillAuto", "YES")
        ElseIf Me.cmbAddBillAuto.Text = "NO" Then
            mykey.SetValue("AddBillAuto", "NO")
        End If

        If Me.cmbUpdateSalPrice.Text = "YES" Then
            mykey.SetValue("UpdateSalPrice", "YES")
        ElseIf Me.cmbUpdateSalPrice.Text = "NO" Then
            mykey.SetValue("UpdateSalPrice", "NO")
        End If

        If Me.cmbGroup_Branch.Text = "YES" Then
            mykey.SetValue("GroupBranch", "YES")
            MDIParent1.ToolStripRegisterBranchesItems.Visible = True
        ElseIf Me.cmbGroup_Branch.Text = "NO" Then
            mykey.SetValue("GroupBranch", "NO")
            MDIParent1.ToolStripRegisterBranchesItems.Visible = False
        End If

        mykey.SetValue("TypeCurrency", cmbType_Currency.Text)

        If rdoSelectPrice.Checked = True Then
            mykey.SetValue("SelectDiscountQuantity", "SelectPrice")
        End If
        If rdoSelectDiscount.Checked = True Then
            mykey.SetValue("SelectDiscountQuantity", "SelectDiscount")
        End If
        If rdoSelectQuantity.Checked = True Then
            mykey.SetValue("SelectDiscountQuantity", "SelectQuantity")
        End If

        If Me.cmbLastSalPriceCustomer.Text = "YES" Then
            mykey.SetValue("LastSalPriceCustomer", "YES")
        ElseIf Me.cmbLastSalPriceCustomer.Text = "NO" Then
            mykey.SetValue("LastSalPriceCustomer", "NO")
        End If

        If Me.cmbBillnoAutoNumber.Text = "YES" Then
            mykey.SetValue("BillnoAutonumber", "YES")
        ElseIf Me.cmbBillnoAutoNumber.Text = "NO" Then
            mykey.SetValue("BillnoAutonumber", "NO")
        End If

        mykey.SetValue("MoreItemsInvoice", cmbMoreItemsInvoice.Text)
        MoreItemsInvoice = cmbMoreItemsInvoice.Text

        If Me.cmbExpirationDate.Text = "YES" Then
            mykey.SetValue("ExpirationDate", "YES")
        ElseIf Me.cmbExpirationDate.Text = "NO" Then
            mykey.SetValue("ExpirationDate", "NO")
        End If

        'إخفاء الكمية من الصنف فى شاشة المبيعات
        If Me.cmbHideQuntItems.Text = "YES" Then
            mykey.SetValue("HideQuntItems", "YES")
        ElseIf Me.cmbHideQuntItems.Text = "NO" Then
            mykey.SetValue("HideQuntItems", "NO")
        End If

        If Me.cmbAllowSellLessTinPrice.Text = "YES" Then
            mykey.SetValue("AllowSellLessTinPrice", "YES")
        ElseIf Me.cmbAllowSellLessTinPrice.Text = "NO" Then
            mykey.SetValue("AllowSellLessTinPrice", "NO")
        End If
        If Me.cmbSearchForProduct.Text = "YES" Then
            mykey.SetValue("SearchForProduct", "YES")
        ElseIf Me.cmbSearchForProduct.Text = "NO" Then
            mykey.SetValue("SearchForProduct", "NO")
        End If
        If Me.cmbAcceptedSalePriceZero.Text = "YES" Then
            mykey.SetValue("AcceptedSalePriceZero", "YES")
        ElseIf Me.cmbAcceptedSalePriceZero.Text = "NO" Then
            mykey.SetValue("AcceptedSalePriceZero", "NO")
        End If
        If Me.cmbAddingNewItmesFromPurchase.Text = "YES" Then
            mykey.SetValue("AddingNewItmesFromPurchase", "YES")
        ElseIf Me.cmbAddingNewItmesFromPurchase.Text = "NO" Then
            mykey.SetValue("AddingNewItmesFromPurchase", "NO")
        End If

        If Me.cmbLastTinPriceItems.Text = "YES" Then
            mykey.SetValue("LastTinPriceItems", "YES")
        ElseIf Me.cmbLastTinPriceItems.Text = "NO" Then
            mykey.SetValue("LastTinPriceItems", "NO")
        End If
        LastTinPriceItems = cmbLastTinPriceItems.Text

        If Me.cmbHideWholeWholeSalePrice.Text = "YES" Then
            mykey.SetValue("HideWholeWholeSalePrice", "YES")
        ElseIf Me.cmbHideWholeWholeSalePrice.Text = "NO" Then
            mykey.SetValue("HideWholeWholeSalePrice", "NO")
        End If

        If Me.cmbInvoicePrintedTwoCopies.Text = "YES" Then
            mykey.SetValue("InvoicePrintedTwoCopies", "YES")
        ElseIf Me.cmbInvoicePrintedTwoCopies.Text = "NO" Then
            mykey.SetValue("InvoicePrintedTwoCopies", "NO")
        End If

        If Me.cmbShowItemImageSalesScreen.Text = "YES" Then
            mykey.SetValue("ShowItemImageSalesScreen", "YES")
        ElseIf Me.cmbShowItemImageSalesScreen.Text = "NO" Then
            mykey.SetValue("ShowItemImageSalesScreen", "NO")
        End If

        If Me.cmbUseAccounts.Text = "YES" Then
            mykey.SetValue("UseAccounts", "YES")
        ElseIf Me.cmbUseAccounts.Text = "NO" Then
            mykey.SetValue("UseAccounts", "NO")
        End If

        If Me.cmbOpenCashDrawerInvoicePrinting.Text = "YES" Then
            mykey.SetValue("OpenCashDrawerInvoicePrinting", "YES")
        ElseIf Me.cmbOpenCashDrawerInvoicePrinting.Text = "NO" Then
            mykey.SetValue("OpenCashDrawerInvoicePrinting", "NO")
        End If

        If cmbSelectTypeMaintenanceDeviceCar.Text = "مركز صيانة السيارات" Then
            mykey.SetValue("SelectTypeMaintenanceDeviceCar", "CarMaintenance")
            MDIParent1.ToolStripMenuItemDrivers.Visible = True
            MDIParent1.ToolStripMenuItem452.Text = "إدخال نوع المركبة"
            MDIParent1.ToolStripMenuItemRecipient.Text = "مستلم السيارة"
            MDIParent1.ToolStripMenuItemReceivingCar.Text = "ملحقات السيارة"
            MDIParent1.ToolStripMenuItemDeviceModel.Visible = False
            MDIParent1.ToolStripMenuItemDeviceBrand.Visible = False
            MDIParent1.ToolStripMenuItemDrivers.Visible = True
            MDIParent1.ToolStripMenuItem452.Visible = False
            MDIParent1.ToolStripMenuItemCar_Data.Visible = True
        End If
        If cmbSelectTypeMaintenanceDeviceCar.Text = "مركز صيانة الاجهزة" Then
            mykey.SetValue("SelectTypeMaintenanceDeviceCar", "DevicesMaintenance")
            MDIParent1.ToolStripMenuItemDrivers.Visible = False
            MDIParent1.ToolStripMenuItem452.Text = "إدخال نوع الجهاز"
            MDIParent1.ToolStripMenuItemRecipient.Text = "مستلم الجهاز"
            MDIParent1.ToolStripMenuItemReceivingCar.Text = "ملحقات الجهاز"
            MDIParent1.ToolStripMenuItemDeviceModel.Visible = True
            MDIParent1.ToolStripMenuItemDeviceBrand.Visible = True
            MDIParent1.ToolStripMenuItemDrivers.Visible = False
            MDIParent1.ToolStripMenuItem452.Visible = True
            MDIParent1.ToolStripMenuItemCar_Data.Visible = False
        End If
        mykey.SetValue("SelectTypeMaintenanceDeviceCar", cmbSelectTypeMaintenanceDeviceCar.Text)
        SelectTypeMaintenanceDeviceCar = cmbSelectTypeMaintenanceDeviceCar.Text


        If Me.cmbNotLoadItemDataScreensOpen.Text = "YES" Then
            mykey.SetValue("NotLoadItemDataScreensOpen", "YES")
        ElseIf Me.cmbNotLoadItemDataScreensOpen.Text = "NO" Then
            mykey.SetValue("NotLoadItemDataScreensOpen", "NO")
        End If

        If rdoPrintA4.Checked = True Then
            mykey.SetValue("PrintSmall", "NO")
            PrintSmall = "NO"
        End If
        If rdoPrintSmall.Checked = True Then
            mykey.SetValue("PrintSmall", "YES")
            PrintSmall = "YES"
        End If
        If rdoPrintA5.Checked = True Then
            mykey.SetValue("PrintSmall", "A5")
            PrintSmall = "A5"
        End If


        If Me.cmbShowCustomerAddressSales.Text = "YES" Then
            mykey.SetValue("ShowCustomerAddressSales", "YES")
            ShowCustomerAddressSales = "YES"
        ElseIf Me.cmbShowCustomerAddressSales.Text = "NO" Then
            mykey.SetValue("ShowCustomerAddressSales", "NO")
            ShowCustomerAddressSales = "NO"
        End If

        If Me.cmbTextNotActivateEditSalesPrice.Text = "YES" Then
            mykey.SetValue("TextNotActivateEditSalesPrice", "YES")
        ElseIf Me.cmbTextNotActivateEditSalesPrice.Text = "NO" Then
            mykey.SetValue("TextNotActivateEditSalesPrice", "NO")
        End If

        mykey.SetValue("ValueDiscountTax", cmbValueDiscountTax.Text)

        If Me.cmbDoNotDiscountsSalesScreen.Text = "YES" Then
            mykey.SetValue("DoNotDiscountsSalesScreen", "YES")
        ElseIf Me.cmbDoNotDiscountsSalesScreen.Text = "NO" Then
            mykey.SetValue("DoNotDiscountsSalesScreen", "NO")
        End If

        If Me.cmbBarcodeMore.Text = "YES" Then
            mykey.SetValue("BarcodeMore", "YES")
            BarcodeMore = "YES"
        ElseIf Me.cmbBarcodeMore.Text = "NO" Then
            mykey.SetValue("BarcodeMore", "NO")
            BarcodeMore = "NO"
        End If

        If Me.cmbSalesPricePublic.Text = "YES" Then
            mykey.SetValue("SalesPricePublic", "YES")
        ElseIf Me.cmbSalesPricePublic.Text = "NO" Then
            mykey.SetValue("SalesPricePublic", "NO")
        End If

        If Me.cmbNotUnityItemsProgram.Text = "YES" Then
            mykey.SetValue("NotUnityItemsProgram", "YES")
            NotUnityItemsProgram = "YES"
        ElseIf Me.cmbNotUnityItemsProgram.Text = "NO" Then
            mykey.SetValue("NotUnityItemsProgram", "NO")
            NotUnityItemsProgram = "NO"
        End If

        If Me.cmbSelectItemsComboBoxEnter.Text = "YES" Then
            mykey.SetValue("SelectItemsComboBoxEnter", "YES")
            SelectItemsComboBoxEnter = "YES"
        ElseIf Me.cmbSelectItemsComboBoxEnter.Text = "NO" Then
            mykey.SetValue("SelectItemsComboBoxEnter", "NO")
            SelectItemsComboBoxEnter = "NO"
        End If

        If Me.cmbDefaultSellingPrices.Text = "سعر البيع" Then
            mykey.SetValue("DefaultSellingPrices", "سعر البيع")
        ElseIf Me.cmbDefaultSellingPrices.Text = "سعر الجملة" Then
            mykey.SetValue("DefaultSellingPrices", "سعر الجملة")
        ElseIf Me.cmbDefaultSellingPrices.Text = "سعر الجملة" Then
            mykey.SetValue("DefaultSellingPrices", "سعر جملة الجملة")
        End If

        If Me.cmbSalesBillNotDiscount.Text = "YES" Then
            mykey.SetValue("SalesBillNotDiscount", "YES")
        ElseIf Me.cmbSalesBillNotDiscount.Text = "NO" Then
            mykey.SetValue("SalesBillNotDiscount", "NO")
        End If

        If Me.cmbParcodeEdit13Number.Text = "YES" Then
            mykey.SetValue("ParcodeEdit13Number", "YES")
        ElseIf Me.cmbParcodeEdit13Number.Text = "NO" Then
            mykey.SetValue("ParcodeEdit13Number", "NO")
        End If

        mykey.SetValue("ParcodeEdit13Number", cmbParcodeEdit13Number.Text)
        ParcodeEdit13Number = cmbParcodeEdit13Number.Text


        mykey.SetValue("PurchaseTax", cmbPurchaseTax.Text)

        mykey.SetValue("DefaultUnityName", cmbType_Unity.Text)

        mykey.SetValue("RngDefaultStock", cmbRngDefaultStock.Text)

        mykey.SetValue("Show_Height_Width_Altitude_Density", cmbShow_Height_Width_Altitude_Density.Text)
        Show_Height_Width_Altitude_Density = cmbShow_Height_Width_Altitude_Density.Text


        If Me.cmbShowBalanceCashInvoice.Text = "YES" Then
            mykey.SetValue("ShowBalanceCashInvoice", "YES")
        ElseIf Me.cmbShowBalanceCashInvoice.Text = "NO" Then
            mykey.SetValue("ShowBalanceCashInvoice", "NO")
        End If

        If Me.cmbShowCustomerBalanceSalesScreen.Text = "YES" Then
            mykey.SetValue("ShowCustomerBalanceSalesScreen", "YES")
        ElseIf Me.cmbShowCustomerBalanceSalesScreen.Text = "NO" Then
            mykey.SetValue("ShowCustomerBalanceSalesScreen", "NO")
        End If

        mykey.SetValue("UseColorWithItems", cmbUseColorWithItems.Text)

        If Me.cmbSalesBillNotDiscountBill.Text = "YES" Then
            mykey.SetValue("SalesBillNotDiscountBill", "YES")
        ElseIf Me.cmbSalesBillNotDiscountBill.Text = "NO" Then
            mykey.SetValue("SalesBillNotDiscountBill", "NO")
        End If

        If Me.cmbCustomizeSaleTermAdminOnly.Text = "YES" Then
            mykey.SetValue("CustomizeSaleTermAdminOnly", "YES")
        ElseIf Me.cmbCustomizeSaleTermAdminOnly.Text = "NO" Then
            mykey.SetValue("CustomizeSaleTermAdminOnly", "NO")
        End If

        If Me.cmbCustomizeSaleWholesalePriceAdminOnly.Text = "YES" Then
            mykey.SetValue("CustomizeSaleWholesalePriceAdminOnly", "YES")
        ElseIf Me.cmbCustomizeSaleWholesalePriceAdminOnly.Text = "NO" Then
            mykey.SetValue("CustomizeSaleWholesalePriceAdminOnly", "NO")
        End If

        mykey.SetValue("RateBankExpensesPaidByVisa", cmbRateBankExpensesPaidByVisa.Text)
        RateBankExpensesPaidByVisa = cmbRateBankExpensesPaidByVisa.Text


        If Me.cmbItemAddedInvoiceSavedAutomatically.Text = "YES" Then
            mykey.SetValue("ItemAddedInvoiceSavedAutomatically", "YES")
            ItemAddedInvoiceSavedAutomatically = "YES"
        ElseIf Me.cmbItemAddedInvoiceSavedAutomatically.Text = "NO" Then
            mykey.SetValue("ItemAddedInvoiceSavedAutomatically", "NO")
            ItemAddedInvoiceSavedAutomatically = "NO"
        End If

        mykey.SetValue("UseBalanceBarcode", cmbUseBalanceBarcode.Text)
        UseBalanceBarcode = cmbUseBalanceBarcode.Text


        If Me.cmbNOTUseUnitItemsPrice.Text = "YES" Then
            mykey.SetValue("NOTUseUnitItemsPrice", "YES")
        ElseIf Me.cmbNOTUseUnitItemsPrice.Text = "NO" Then
            mykey.SetValue("NOTUseUnitItemsPrice", "NO")
        End If

        If Me.cmbRunDatabaseInternet.Text = "YES" Then
            mykey.SetValue("RunDatabaseInternet", "YES")
            RunDatabaseInternet = "YES"
        ElseIf Me.cmbRunDatabaseInternet.Text = "NO" Then
            mykey.SetValue("RunDatabaseInternet", "NO")
            RunDatabaseInternet = "NO"
        End If

        If rdoYesNetwork.Checked = True Then
            mykey.SetValue("NetworkNameInternet", "YES")
            NetworkNameInternet = "Yes"
        ElseIf rdoNoNetwork.Checked = True Then
            mykey.SetValue("NetworkNameInternet", "NO")
            NetworkNameInternet = "NO"
        ElseIf rdoSource.Checked = True Then
            mykey.SetValue("NetworkNameInternet", "Source")
            NetworkNameInternet = "Source"
        End If

        mykey.SetValue("DatabaseElectricNetwork", txtDataBaseName.Text)
        DataBaseNameInternet = txtDataBaseName.Text
        mykey.SetValue("ServerNameInternet", txtServerName.Text)
        ServerNameInternet = txtServerName.Text
        mykey.SetValue("UserNameInternet", txtUserName.Text)
        UserNameServerInternet = txtUserName.Text
        mykey.SetValue("PasswordInternet", txtPassword.Text)
        PasswordServerInternet = txtPassword.Text

        'mykey.SetValue("ChangeFontProgram", cmbChangeFontProgram.Text)

        mykey.SetValue("DefaultCurrencyProgram", cmbDefaultCurrencyProgram.Text)
        DefaultCurrencyProgram = cmbDefaultCurrencyProgram.Text

        DefaultCurrencyProgram = cmbDefaultCurrencyProgram.Text

        If Me.cmbTinPriceAverageThreeDigits.Text = "YES" Then
            mykey.SetValue("TinPriceAverageThreeDigits", "YES")
            TinPriceAverageThreeDigits = "YES"
        ElseIf Me.cmbTinPriceAverageThreeDigits.Text = "NO" Then
            mykey.SetValue("TinPriceAverageThreeDigits", "NO")
            TinPriceAverageThreeDigits = "NO"
        End If

        If Me.cmbDateNotBeenActivatedSales.Text = "YES" Then
            mykey.SetValue("DateNotBeenActivatedSales", "YES")
            DateNotBeenActivatedSales = "YES"
        ElseIf Me.cmbDateNotBeenActivatedSales.Text = "NO" Then
            mykey.SetValue("DateNotBeenActivatedSales", "NO")
            DateNotBeenActivatedSales = "NO"
        End If


        If Me.cmbActivateAddDepositBankCustomerPayments.Text = "YES" Then
            mykey.SetValue("ActivateAddDepositBankCustomerPayments", "YES")
            ActivateAddDepositBankCustomerPayments = "YES"
        ElseIf Me.cmbActivateAddDepositBankCustomerPayments.Text = "NO" Then
            mykey.SetValue("ActivateAddDepositBankCustomerPayments", "NO")
            ActivateAddDepositBankCustomerPayments = "NO"
        End If

        mykey.SetValue("BankPayCustNameBank", cmbBank.Text)
        BankPayCustNameBank = cmbBank.Text

        mykey.SetValue("BankPayCustNameCurrency", cmbCurrencyBank.Text)
        BankPayCustNameCurrency = cmbCurrencyBank.Text

        mykey.SetValue("BankPayCustAccountNumber", cmbAccountNumber.Text)
        BankPayCustAccountNumber = cmbAccountNumber.Text


        If Me.cmbAllowCustOrSupplierReturnItemNotMovement.Text = "YES" Then
            mykey.SetValue("AllowCustOrSupplierReturnItemNotMovement", "YES")
            AllowCustOrSupplierReturnItemNotMovement = "YES"
        ElseIf Me.cmbAllowCustOrSupplierReturnItemNotMovement.Text = "NO" Then
            mykey.SetValue("AllowCustOrSupplierReturnItemNotMovement", "NO")
            AllowCustOrSupplierReturnItemNotMovement = "NO"
        End If

        If Me.cmbBeforeScanningBarcodeQuantityStar.Text = "YES" Then
            mykey.SetValue("BeforeScanningBarcodeQuantityStar", "YES")
            BeforeScanningBarcodeQuantityStar = "YES"
        ElseIf Me.cmbBeforeScanningBarcodeQuantityStar.Text = "NO" Then
            mykey.SetValue("BeforeScanningBarcodeQuantityStar", "NO")
            BeforeScanningBarcodeQuantityStar = "NO"
        End If

        If Me.cmbNonControlBillNumberSalesAutomatic.Text = "YES" Then
            mykey.SetValue("NonControlBillNumberSalesAutomatic", "YES")
            NonControlBillNumberSalesAutomatic = "YES"
        ElseIf Me.cmbNonControlBillNumberSalesAutomatic.Text = "NO" Then
            mykey.SetValue("NonControlBillNumberSalesAutomatic", "NO")
            NonControlBillNumberSalesAutomatic = "NO"
        End If

        If Me.cmbNonControlDriverRateOrderDelivery.Text = "YES" Then
            mykey.SetValue("NonControlDriverRateOrderDelivery", "YES")
            NonControlDriverRateOrderDelivery = "YES"
        ElseIf Me.cmbNonControlDriverRateOrderDelivery.Text = "NO" Then
            mykey.SetValue("NonControlDriverRateOrderDelivery", "NO")
            NonControlDriverRateOrderDelivery = "NO"
        End If

        If Me.cmbAllowingSalesPaidAndRestToCustomerCash.Text = "YES" Then
            mykey.SetValue("AllowingSalesPaidAndRestToCustomerCash", "YES")
            AllowingSalesPaidAndRestToCustomerCash = "YES"
        ElseIf Me.cmbAllowingSalesPaidAndRestToCustomerCash.Text = "NO" Then
            mykey.SetValue("AllowingSalesPaidAndRestToCustomerCash", "NO")
            AllowingSalesPaidAndRestToCustomerCash = "NO"
        End If

        mykey.SetValue("DesignAnotherSalesInvoice", cmbDesignAnotherSalesInvoice.Text)
        DesignAnotherSalesInvoice = cmbDesignAnotherSalesInvoice.Text

        mykey.SetValue("UseManufacturingProduct", cmbUseManufacturingProduct.Text)
        UseManufacturingProduct = cmbUseManufacturingProduct.Text


        mykey.SetValue("ConfirmDelegateRegisteredSales", cmbConfirmDelegateRegisteredSales.Text)
        ConfirmDelegateRegisteredSales = cmbConfirmDelegateRegisteredSales.Text

        mykey.SetValue("DealingPharmacySystem", cmbDealingPharmacySystem.Text)
        DealingPharmacySystem = cmbDealingPharmacySystem.Text

        mykey.SetValue("NotControlStoreTheSales", cmbNotControlStoreTheSales.Text)
        NotControlStoreTheSales = cmbNotControlStoreTheSales.Text

        mykey.SetValue("PrintBillStoreExchangeWithoutPrices", cmbPrintBillStoreExchangeWithoutPrices.Text)
        PrintBillStoreExchangeWithoutPrices = cmbPrintBillStoreExchangeWithoutPrices.Text

        mykey.SetValue("PrintBillStoreExchangeWithoutGroub", cmbPrintBillStoreExchangeWithoutGroub.Text)
        PrintBillStoreExchangeWithoutGroub = cmbPrintBillStoreExchangeWithoutGroub.Text


        mykey.SetValue("ShowGroupItemSalesScreen", cmbShowGroupItemSalesScreen.Text)
        ShowGroupItemSalesScreen = cmbShowGroupItemSalesScreen.Text

        mykey.SetValue("ShowDiscountRateItemSales", cmbShowDiscountRateItemSales.Text)
        ShowDiscountRateItemSales = cmbShowDiscountRateItemSales.Text

        mykey.SetValue("AddNewCustomerSalesAutomatically", cmbAddNewCustomerSalesAutomatically.Text)
        AddNewCustomerSalesAutomatically = cmbAddNewCustomerSalesAutomatically.Text

        mykey.SetValue("SeletctFreeSearchTheListOfItems", cmbSeletctFreeSearchTheListOfItems.Text)
        SeletctFreeSearchTheListOfItems = cmbSeletctFreeSearchTheListOfItems.Text

        mykey.SetValue("StoresName", cmbStoresName.Text)
        StoresName = cmbStoresName.Text

        mykey.SetValue("TreasuryName", cmbTreasuryName.Text)
        Treasury_Code = Cls.Get_Code_Value_Stores_More("Treasury", "Treasury_Code", "Treasury_Name=N'" & cmbTreasuryName.Text & "'")
        mykey.SetValue("Treasury_Code", Treasury_Code)
        TreasuryName = cmbTreasuryName.Text

        mykey.SetValue("ActivateElectronicBill", cmbActivateElectronicBill.Text)
        ActivateElectronicBill = cmbActivateElectronicBill.Text

        mykey.SetValue("LastSalPriceCustomerUnitPriceByLargQuant", cmbLastSalPriceCustomerUnitPriceByLargQuant.Text)
        LastSalPriceCustomerUnitPriceByLargQuant = cmbLastSalPriceCustomerUnitPriceByLargQuant.Text

        mykey.SetValue("DealingWithSerialItems", cmbDealingWithSerialItems.Text)
        DealingWithSerialItems = cmbDealingWithSerialItems.Text
        mykey.SetValue("DealingWithSerialItemsSeparateConnected", cmbDealingWithSerialItemsSeparateConnected.Text)
        DealingWithSerialItemsSeparateConnected = cmbDealingWithSerialItemsSeparateConnected.Text

        mykey.SetValue("CommercialAndIndustrialProfitsTax", cmbCommercialAndIndustrialProfitsTax.Text)
        CommercialAndIndustrialProfitsTax = cmbCommercialAndIndustrialProfitsTax.Text
        mykey.SetValue("CommercialAndIndustrialProfitsTaxYESNO", cmbCommercialAndIndustrialProfitsTaxYESNO.Text)
        CommercialAndIndustrialProfitsTaxYESNO = cmbCommercialAndIndustrialProfitsTaxYESNO.Text

        mykey.SetValue("AddingNewItmesFromSales", cmbAddingNewItmesFromSales.Text)
        AddingNewItmesFromSales = cmbAddingNewItmesFromSales.Text

        'تحديد رقم الفاتورة الضريبية
        mykey.SetValue("SelectTaxInvoiceNumber", cmbSelectTaxInvoiceNumber.Text)
        SelectTaxInvoiceNumber = cmbSelectTaxInvoiceNumber.Text
        mykey.SetValue("SelectTaxInvoiceNumberTax", txtSelectTaxInvoiceNumberTax.Text)
        SelectTaxInvoiceNumberTax = txtSelectTaxInvoiceNumberTax.Text
        mykey.SetValue("SelectTaxInvoiceNumberNotTax", txtSelectTaxInvoiceNumberNotTax.Text)
        SelectTaxInvoiceNumberNotTax = txtSelectTaxInvoiceNumberNotTax.Text

        mykey.SetValue("SalesInvoicePrintingLanguage", cmbSalesInvoicePrintingLanguage.Text)
        SalesInvoicePrintingLanguage = cmbSalesInvoicePrintingLanguage.Text

        If chkUpdateSalPriceAllStores.Checked = False Then
            UpdateSalPriceAllStores = "NO"
        Else
            UpdateSalPriceAllStores = "YES"
        End If
        mykey.SetValue("UpdateSalPriceAllStores", UpdateSalPriceAllStores)


        Dim xValueVATNumber As String = mykey.GetValue("ValueVATNumber", "0")
        If xValueVATNumber <> cmbValueVATNumber.Text Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Items set RateVAT =N'" & cmbValueVATNumber.Text & "'" : cmd.ExecuteNonQuery()
        End If
        mykey.SetValue("ValueVATNumber", cmbValueVATNumber.Text)
        ValueVATNumber = cmbValueVATNumber.Text

        mykey.SetValue("SalesMenuTouch", cmbSalesMenuTouch.Text)
        SalesMenuTouch = cmbSalesMenuTouch.Text

        mykey.SetValue("DealingSheftStatusSales", cmbDealingSheftStatusSales.Text)
        DealingSheftStatusSales = cmbDealingSheftStatusSales.Text

        mykey.SetValue("ActivateSpecificDeviceStore", cmbActivateSpecificDeviceStore.Text)
        ActivateSpecificDeviceStore = cmbActivateSpecificDeviceStore.Text

        mykey.SetValue("AllowAddPurchasesTinPrice", cmbAllowAddPurchasesTinPrice.Text)
        AllowAddPurchasesTinPrice = cmbAllowAddPurchasesTinPrice.Text

        mykey.SetValue("DateNotBeenActivatedPrograms", cmbDateNotBeenActivatedPrograms.Text)
        DateNotBeenActivatedPrograms = cmbDateNotBeenActivatedPrograms.Text

        mykey.SetValue("DefineCustomInvoiceHandling", cmbDefineCustomInvoiceHandling.Text)
        DefineCustomInvoiceHandling = cmbDefineCustomInvoiceHandling.Text

        mykey.SetValue("PrintInvoiceNumberCustomerMainInvoice", cmbPrintInvoiceNumberCustomerMainInvoice.Text)
        PrintInvoiceNumberCustomerMainInvoice = cmbPrintInvoiceNumberCustomerMainInvoice.Text

        mykey.SetValue("ChangeFileNameProgramFITSOFT", txtChangeFileNameProgramFITSOFT.Text)
        ChangeFileNameProgramFITSOFT = txtChangeFileNameProgramFITSOFT.Text

        mykey.SetValue("ScreenNCRHomePortName", txtScreenNCRHomePortName.Text)
        ScreenNCRHomePortName = txtScreenNCRHomePortName.Text

        mykey.SetValue("ScreenNCRHomeBaudRate", txtScreenNCRHomeBaudRate.Text)
        ScreenNCRHomeBaudRate = txtScreenNCRHomeBaudRate.Text

        mykey.SetValue("TreasuryControl", cmbTreasuryControl.Text)
        TreasuryControl = cmbTreasuryControl.Text

        mykey.SetValue("ActivatePendingBill", cmbActivatePendingBill.Text)
        ActivatePendingBill = cmbActivatePendingBill.Text

        mykey.SetValue("ChangeFontSizeSalesText", cmbChangeFontSizeSalesText.Text)
        ChangeFontSizeSalesText = cmbChangeFontSizeSalesText.Text

        mykey.SetValue("ShowConfirmationMessageBeforeSavingSalesInvoice", cmbShowConfirmationMessageBeforeSavingSalesInvoice.Text)
        ShowConfirmationMessageBeforeSavingSalesInvoice = cmbShowConfirmationMessageBeforeSavingSalesInvoice.Text

        mykey.SetValue("DateNotBeenActivatedOutcome", cmbDateNotBeenActivatedOutcome.Text)
        DateNotBeenActivatedOutcome = cmbDateNotBeenActivatedOutcome.Text

        mykey.SetValue("NumberPayNotBeenActivated", cmbNumberPayNotBeenActivated.Text)
        NumberPayNotBeenActivated = cmbNumberPayNotBeenActivated.Text

        mykey.SetValue("ActivateFormatNumberWithSeparators", cmbActivateFormatNumberWithSeparators.Text)
        ActivateFormatNumberWithSeparators = cmbActivateFormatNumberWithSeparators.Text

        mykey.SetValue("NoSalesAllowedTheMinimumQuantity", cmbNoSalesAllowedTheMinimumQuantity.Text)
        NoSalesAllowedTheMinimumQuantity = cmbNoSalesAllowedTheMinimumQuantity.Text

        mykey.SetValue("AddMoreInvoiceSeparately", cmbAddMoreInvoiceSeparately.Text)
        AddMoreInvoiceSeparately = cmbAddMoreInvoiceSeparately.Text

        mykey.SetValue("NotUploadingCustomerDataToTheSalesScreen", cmbNotUploadingCustomerDataToTheSalesScreen.Text)
        NotUploadingCustomerDataToTheSalesScreen = cmbNotUploadingCustomerDataToTheSalesScreen.Text

        mykey.SetValue("ShowBeginningBalanceAdjustmentItemScreen", cmbShowBeginningBalanceAdjustmentItemScreen.Text)
        ShowBeginningBalanceAdjustmentItemScreen = cmbShowBeginningBalanceAdjustmentItemScreen.Text

        mykey.SetValue("ShowDeliveryServiceSales", cmbShowDeliveryServiceSales.Text)
        ShowDeliveryServiceSales = cmbShowDeliveryServiceSales.Text

        mykey.SetValue("PrintCustomerAccountAfterPrinting", cmbPrintCustomerAccountAfterPrinting.Text)
        PrintCustomerAccountAfterPrinting = cmbPrintCustomerAccountAfterPrinting.Text

        mykey.SetValue("SelectDeferredPaymentStatusSalesScreenDefault", cmbSelectDeferredPaymentStatusSalesScreenDefault.Text)
        SelectDeferredPaymentStatusSalesScreenDefault = cmbSelectDeferredPaymentStatusSalesScreenDefault.Text

        If chkHideItemGroupSearchForProduct.Checked = False Then
            HideItemGroupSearchForProduct = "NO"
        Else
            HideItemGroupSearchForProduct = "YES"
        End If
        mykey.SetValue("HideItemGroupSearchForProduct", HideItemGroupSearchForProduct)

        mykey.SetValue("EmployeeCustody", cmbEmployeeCustody.Text)
        EmployeeCustody = cmbEmployeeCustody.Text


        LanguageChange()
        'Application.Restart()


        'Dim LanguageMain As String = mykey.GetValue("LanguageMainProgram", "Arabic")
        'If Me.cmbLanguageMainProgram.Text = "Arabic" Then
        '    mykey.SetValue("LanguageMainProgram", "Arabic")
        '    LanguageMainProgram = "Arabic"
        'ElseIf Me.cmbLanguageMainProgram.Text = "English" Then
        '    mykey.SetValue("LanguageMainProgram", "English")
        '    LanguageMainProgram = "English"
        'End If
        Dim LanguageMain As String = mykey.GetValue("LanguageMainProgram", "العربية")
        If LanguageMain <> cmbLanguageMainProgram.Text Then
            ChangeLanguage()
        End If
        mykey.SetValue("LanguageMainProgram", cmbLanguageMainProgram.Text)
        LanguageMainProgram = cmbLanguageMainProgram.Text

        If LanguageMain <> cmbLanguageMainProgram.Text Then
            System.Windows.Forms.Application.Restart()
        End If

        Me.Hide()
    End Sub

    Sub Restart(ByVal program As String)
        For Each proc As Process In Process.GetProcesses
            If proc.ProcessName = program Then proc.Kill()
        Next
        Process.Start(program)
    End Sub

    Sub LanguageChange()
        Dim resault2 As String
        resault2 = mykey.GetValue("LANGUAGE", "ARABIC")
        If resault2 = "ARABIC" Then
            InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages().Item(1) 'تغيير لغة لوحة المفاتيح الى اللغه العربية
        ElseIf resault2 = "ENGLISH" Then
            InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages().Item(0) 'تغيير لغة لوحة المفاتيح الى اللغه الانجليزيه
        End If

    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        Me.Close()
    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        frmSettingNetwork.ShowDialog()
    End Sub

    Private Sub btnSettingPrinter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSettingPrinter.Click
        FrmSettingPrinter.ShowDialog()
    End Sub

    Function Name_Check(ByVal Name As String, ByVal Tabel As String, ByVal Text As Control) As Boolean
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select " + Name + " from " + Tabel + " where " + Name + "=N'" & Text.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            MsgBox("تم اضافه هذه البيان مسبقا", MsgBoxStyle.Exclamation)
            Text.Focus()
            Return False
            Exit Function
        End If
        Return True
    End Function

    Private Sub FillSettingDataGrid(ByVal Code As String, ByVal Name As String, ByVal Table As String, ByVal DGV As DataGridView)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "select " + Code + ", " + Name + " AS [الاسم] from " + Table + " order by 1"
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DGV.DataSource = Cls.PopulateDataView(dr) : dr.Close()
        DGV.Columns(0).Visible = False
    End Sub

    Private Sub Deletex(ByVal Tabel As String, ByVal Name As String, ByVal DataGrid As DataGridView)
        If DataGrid.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        For i As Integer = 0 To DataGrid.SelectedRows.Count - 1
            If DataGrid.RowCount = 0 Then Beep() : Exit Sub
            If (DataGrid.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim ItmID As String
            ItmID = DataGrid.SelectedRows(i).Cells(1).Value

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete from " + Tabel + " where " + Name + " =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
        Next

    End Sub

    Private Sub MAXRECORD(ByVal ID As String, ByVal Table As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " & Table & ""
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            ID_Currency = 1
        Else
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" & ID & " As float)) as mb FROM " & Table & ""
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            ID_Currency = sh + 1
        End If
    End Sub

    Private Sub btnAddCurrency_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddCurrency.Click
        If txtCurrency.Text.Trim = "" Then MsgBox("من فضلا أدخل نوع العملة", MsgBoxStyle.Exclamation) : txtCurrency.Focus() : Exit Sub

        If Name_Check("Name_Currency", "Type_Currency", txtCurrency) = False Then Exit Sub
        MAXRECORD("ID_Currency", "Type_Currency")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Type_Currency (ID_Currency,Name_Currency) values"
        S = S & " (N'" & ID_Currency & "',N'" & txtCurrency.Text & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        FillSettingDataGrid("ID_Currency", "Name_Currency", "Type_Currency", dgvCurrency)

        Bra.Fil("Type_Currency", "Name_Currency", cmbType_Currency)
        Bra.Fil("Type_Currency", "Name_Currency", cmbDefaultCurrencyProgram)

        txtCurrency.Text = ""
        txtCurrency.Focus()
        'MessageBox.Show(" تم أضافة البيانات بنجاح", "تنبية", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub btnUpdateCurrency_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdateCurrency.Click
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To dgvCurrency.Rows.Count - 1
            cmd.CommandText = "update Type_Currency set Name_Currency =N'" & dgvCurrency.Rows(i).Cells(1).Value & "' where ID_Currency=N'" & dgvCurrency.Rows(i).Cells(0).Value & "'" : cmd.ExecuteNonQuery()
        Next
        FillSettingDataGrid("ID_Currency", "Name_Currency", "Type_Currency", dgvCurrency)
        Bra.Fil("Type_Currency", "Name_Currency", cmbType_Currency)
        Bra.Fil("Type_Currency", "Name_Currency", cmbDefaultCurrencyProgram)

        MessageBox.Show(" تم الانتهاء من عملية التحديث", "تنبية", MessageBoxButtons.OK, MessageBoxIcon.Information)

    End Sub

    Private Sub btnDeleteCurrency_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDeleteCurrency.Click
        Deletex("Type_Currency", "Name_Currency", dgvCurrency)
        FillSettingDataGrid("ID_Currency", "Name_Currency", "Type_Currency", dgvCurrency)
        Bra.Fil("Type_Currency", "Name_Currency", cmbType_Currency)
        Bra.Fil("Type_Currency", "Name_Currency", cmbDefaultCurrencyProgram)
    End Sub

    Private Sub btnAddUnity_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddUnity.Click
        If txtUnity.Text.Trim = "" Then MsgBox("من فضلا أدخل وحدة القياس", MsgBoxStyle.Exclamation) : txtUnity.Focus() : Exit Sub

        If Name_Check("Unity_Name", "Type_Unity", txtUnity) = False Then Exit Sub
        MAXRECORD("Unity_ID", "Type_Unity")
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Type_Unity (Unity_ID,Unity_Name) values"
        S = S & " (N'" & ID_Currency & "',N'" & txtUnity.Text & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        FillSettingDataGrid("Unity_ID", "Unity_Name", "Type_Unity", dgvUnity)
        txtUnity.Text = ""
        txtUnity.Focus()
        'MessageBox.Show(" تم أضافة البيانات بنجاح", "تنبية", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub btnEditUnity_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnEditUnity.Click
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'For i As Integer = 0 To dgvUnity.Rows.Count - 1
        '    cmd.CommandText = "update Type_Unity set Unity_Name =N'" & dgvUnity.Rows(i).Cells(1).Value & "' where Unity_ID=N'" & dgvUnity.Rows(i).Cells(0).Value & "'" : cmd.ExecuteNonQuery()
        '    cmd.CommandText = "update ItemsUnity set Unity_Name =N'" & dgvUnity.Rows(i).Cells(1).Value & "' where Unity_Name=N'" & dgvUnity.Rows(i).Cells(1).Value & "'" : cmd.ExecuteNonQuery()

        '    cmd.CommandText = "update Items set Unity =N'" & dgvUnity.Rows(i).Cells(1).Value & "' where Unity=N'" & dgvUnity.Rows(i).Cells(1).Value & "'" : cmd.ExecuteNonQuery()
        '    cmd.CommandText = "update BillsalData set itm_Unity =N'" & dgvUnity.Rows(i).Cells(1).Value & "' where itm_Unity=N'" & dgvUnity.Rows(i).Cells(1).Value & "'" : cmd.ExecuteNonQuery()
        '    cmd.CommandText = "update BilltINData set itm_Unity =N'" & dgvUnity.Rows(i).Cells(1).Value & "' where itm_Unity=N'" & dgvUnity.Rows(i).Cells(1).Value & "'" : cmd.ExecuteNonQuery()
        '    cmd.CommandText = "update IM_Bsal_Data set itm_Unity =N'" & dgvUnity.Rows(i).Cells(1).Value & "' where itm_Unity=N'" & dgvUnity.Rows(i).Cells(1).Value & "'" : cmd.ExecuteNonQuery()
        '    cmd.CommandText = "update IM_Btin_Data set itm_Unity =N'" & dgvUnity.Rows(i).Cells(1).Value & "' where itm_Unity=N'" & dgvUnity.Rows(i).Cells(1).Value & "'" : cmd.ExecuteNonQuery()
        '    cmd.CommandText = "update ItemsDeleted set Unity =N'" & dgvUnity.Rows(i).Cells(1).Value & "' where Unity=N'" & dgvUnity.Rows(i).Cells(1).Value & "'" : cmd.ExecuteNonQuery()
        '    cmd.CommandText = "update ItemsTransfer_BillsalData set itm_Unity =N'" & dgvUnity.Rows(i).Cells(1).Value & "' where itm_Unity=N'" & dgvUnity.Rows(i).Cells(1).Value & "'" : cmd.ExecuteNonQuery()
        '    cmd.CommandText = "update ItemsTransfer_BilltINData set itm_Unity =N'" & dgvUnity.Rows(i).Cells(1).Value & "' where itm_Unity=N'" & dgvUnity.Rows(i).Cells(1).Value & "'" : cmd.ExecuteNonQuery()
        '    cmd.CommandText = "update ItemsTransfer_BilltINData set itm_Unity =N'" & dgvUnity.Rows(i).Cells(1).Value & "' where itm_Unity=N'" & dgvUnity.Rows(i).Cells(1).Value & "'" : cmd.ExecuteNonQuery()
        '    cmd.CommandText = "update Manufacturing_BillsalData set itm_Unity =N'" & dgvUnity.Rows(i).Cells(1).Value & "' where itm_Unity=N'" & dgvUnity.Rows(i).Cells(1).Value & "'" : cmd.ExecuteNonQuery()
        '    cmd.CommandText = "update Manufacturing_BilltINData set itm_Unity =N'" & dgvUnity.Rows(i).Cells(1).Value & "' where itm_Unity=N'" & dgvUnity.Rows(i).Cells(1).Value & "'" : cmd.ExecuteNonQuery()
        '    cmd.CommandText = "update ManufacturingProductAdd set itm_Unity =N'" & dgvUnity.Rows(i).Cells(1).Value & "' where itm_Unity=N'" & dgvUnity.Rows(i).Cells(1).Value & "'" : cmd.ExecuteNonQuery()
        '    cmd.CommandText = "update Receive_BillsalData set itm_Unity =N'" & dgvUnity.Rows(i).Cells(1).Value & "' where itm_Unity=N'" & dgvUnity.Rows(i).Cells(1).Value & "'" : cmd.ExecuteNonQuery()
        '    cmd.CommandText = "update Receive_BilltINData set itm_Unity =N'" & dgvUnity.Rows(i).Cells(1).Value & "' where itm_Unity=N'" & dgvUnity.Rows(i).Cells(1).Value & "'" : cmd.ExecuteNonQuery()

        'Next


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To dgvUnity.SelectedRows.Count - 1
            'cmd.CommandText = "update Type_Unity set Unity_Name =N'" & dgvUnity.SelectedRows(i).Cells(1).Value & "' where Unity_ID=N'" & dgvUnity.Rows(i).Cells(0).Value & "'" : cmd.ExecuteNonQuery()

            cmd.CommandText = "update Type_Unity set Unity_Name =N'" & dgvUnity.SelectedRows(i).Cells(1).Value & "' where Unity_Name=N'" & txtUnity.Text & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "update Items set Unity =N'" & dgvUnity.SelectedRows(i).Cells(1).Value & "' where Unity=N'" & txtUnity.Text & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "update BillsalData set itm_Unity =N'" & dgvUnity.SelectedRows(i).Cells(1).Value & "' where itm_Unity=N'" & txtUnity.Text & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "update BilltINData set itm_Unity =N'" & dgvUnity.SelectedRows(i).Cells(1).Value & "' where itm_Unity=N'" & txtUnity.Text & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "update IM_Bsal_Data set itm_Unity =N'" & dgvUnity.SelectedRows(i).Cells(1).Value & "' where itm_Unity=N'" & txtUnity.Text & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "update IM_Btin_Data set itm_Unity =N'" & dgvUnity.SelectedRows(i).Cells(1).Value & "' where itm_Unity=N'" & txtUnity.Text & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "update ItemsDeleted set Unity =N'" & dgvUnity.Rows(i).Cells(1).Value & "' where Unity=N'" & txtUnity.Text & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "update ItemsTransfer_BillsalData set itm_Unity =N'" & dgvUnity.SelectedRows(i).Cells(1).Value & "' where itm_Unity=N'" & txtUnity.Text & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "update ItemsTransfer_BilltINData set itm_Unity =N'" & dgvUnity.SelectedRows(i).Cells(1).Value & "' where itm_Unity=N'" & txtUnity.Text & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "update ItemsTransfer_BilltINData set itm_Unity =N'" & dgvUnity.SelectedRows(i).Cells(1).Value & "' where itm_Unity=N'" & txtUnity.Text & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "update Manufacturing_BillsalData set itm_Unity =N'" & dgvUnity.SelectedRows(i).Cells(1).Value & "' where itm_Unity=N'" & txtUnity.Text & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "update Manufacturing_BilltINData set itm_Unity =N'" & dgvUnity.SelectedRows(i).Cells(1).Value & "' where itm_Unity=N'" & txtUnity.Text & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "update ManufacturingProductAdd set itm_Unity =N'" & dgvUnity.SelectedRows(i).Cells(1).Value & "' where itm_Unity=N'" & txtUnity.Text & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "update Receive_BillsalData set itm_Unity =N'" & dgvUnity.SelectedRows(i).Cells(1).Value & "' where itm_Unity=N'" & txtUnity.Text & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "update Receive_BilltINData set itm_Unity =N'" & dgvUnity.SelectedRows(i).Cells(1).Value & "' where itm_Unity=N'" & txtUnity.Text & "'" : cmd.ExecuteNonQuery()

        Next

        FillSettingDataGrid("Unity_ID", "Unity_Name", "Type_Unity", dgvUnity)

        MessageBox.Show(" تم الانتهاء من عملية التحديث", "تنبية", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub btnDeleteUnity_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDeleteUnity.Click
        Deletex("Type_Unity", "Unity_Name", dgvUnity)
        FillSettingDataGrid("Unity_ID", "Unity_Name", "Type_Unity", dgvUnity)
    End Sub

    Private Sub btnMailMessage_Click(sender As Object, e As EventArgs) Handles btnMailMessage.Click
        Frm_EMailMessageSetting.ShowDialog()
    End Sub

    Private Sub frmOptions_Shown(sender As Object, e As EventArgs) Handles Me.Shown
        '' On Form1 shown, start applying font 
        'Dim CFontPath As String = Application.StartupPath
        'pfc.AddFontFile(CFontPath & "\" & ChangeFontProgram)
        'Dim allCtrl As New List(Of Control)
        'For Each ctrl As Control In FindALLControlRecursive(allCtrl, Me)
        '    ' You need to define which control type to change it's font family; not recommendd to just change all controls' fonts, it will create a missy shape
        '    If TypeOf ctrl Is Label Or TypeOf ctrl Is TextBox Or TypeOf ctrl Is Button Or TypeOf ctrl Is CheckBox Or TypeOf ctrl Is RadioButton Or TypeOf ctrl Is ProgressBar Or TypeOf ctrl Is GroupBox Or TypeOf ctrl Is ComboBox Or TypeOf ctrl Is TabControl Or TypeOf ctrl Is ListBox Or TypeOf ctrl Is DataGridView Then
        '        Dim CurrentCtrlFontSize = ctrl.Font.Size ' get current object's font size before applying new font family
        '        ctrl.Font = New Font(pfc.Families(0), CurrentCtrlFontSize, FontStyle.Bold)
        '    End If
        'Next
        'allCtrl.Clear()
    End Sub

    'Dim pfc As New PrivateFontCollection()
    'Private Function FindALLControlRecursive(ByVal list As List(Of Control), ByVal parent As Control) As List(Of Control)
    '    ' function that returns all control in a form, parent or child regardless of control's type
    '    If parent Is Nothing Then
    '        Return list
    '    Else
    '        list.Add(parent)
    '    End If
    '    For Each child As Control In parent.Controls
    '        FindALLControlRecursive(list, child)
    '    Next
    '    Return list
    'End Function

    Private Sub ComboBox9_TextChanged(sender As Object, e As EventArgs) Handles cmbSalesTax.TextChanged
        'On Error Resume Next
        If cmbSalesTax.Text <> "0" Then
            cmbValueVAT.Enabled = False
            cmbPurchaseTax.Enabled = True
            cmbSalesTax.Enabled = True
        Else
            cmbValueVAT.Enabled = True
            cmbPurchaseTax.Enabled = True
            cmbSalesTax.Enabled = True
        End If
    End Sub

    Private Sub type_connection_server()
        If rdoYesNetwork.Checked = True Then
            txtDataBaseName.Enabled = True
            txtServerName.Enabled = True
            txtPassword.Enabled = True
            txtUserName.Enabled = True
        End If
        If rdoNoNetwork.Checked = True Then
            txtDataBaseName.Enabled = True
            txtServerName.Enabled = True
            txtPassword.Enabled = False
            txtUserName.Enabled = False
        End If
        If rdoSource.Checked = True Then
            txtDataBaseName.Enabled = False
            txtServerName.Enabled = False
            txtPassword.Enabled = False
            txtUserName.Enabled = False
        End If
    End Sub

    Private Sub rdoYesNetwork_CheckedChanged(sender As Object, e As EventArgs) Handles rdoYesNetwork.CheckedChanged
        type_connection_server()
    End Sub

    Private Sub rdoNoNetwork_CheckedChanged(sender As Object, e As EventArgs) Handles rdoNoNetwork.CheckedChanged
        type_connection_server()
    End Sub

    Private Sub rdoSource_CheckedChanged(sender As Object, e As EventArgs) Handles rdoSource.CheckedChanged
        type_connection_server()
    End Sub

    Private Sub cmbActivateAddDepositBankCustomerPayments_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbActivateAddDepositBankCustomerPayments.SelectedIndexChanged
        If cmbActivateAddDepositBankCustomerPayments.Text = "YES" Then
            If cmbBank.Text = "" Or cmbCurrencyBank.Text = "" Or cmbAccountNumber.Text = "" Then
                MsgBox("من فضلك يجب اختيار اولا أسم البنك والعملة ورقم الحساب من الاختيارات وان لم تكن غير ظاهرة يجب اضافة حركة ايداع واحدة فى حركة البنوك", MsgBoxStyle.Exclamation)
            End If
        End If
    End Sub

    Private Sub cmbDealingPharmacySystem_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbDealingPharmacySystem.SelectedIndexChanged
        If cmbDealingPharmacySystem.Text = "YES" Then
            cmbMoreItemsInvoice.Text = "NO"
            cmbMoreItemsInvoice.Enabled = False
        Else
            cmbMoreItemsInvoice.Text = "NO"
            cmbMoreItemsInvoice.Enabled = True
        End If
    End Sub

    Function ChangeLanguage()
        If cmbLanguageMainProgram.Text = "" Then
            MessageBox.Show("من فضلك اختر لغة", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1)
            Return False
        ElseIf cmbLanguageMainProgram.Text = "العربية" Then
            If My.Settings.Language <> "ar" Then
                My.Settings.Language = "ar"
                My.Settings.Save()
                My.Settings.Reload()
                Return True
            End If
        ElseIf cmbLanguageMainProgram.Text = "English" Then
            If My.Settings.Language <> "en" Then
                My.Settings.Language = "en"
                My.Settings.Save()
                My.Settings.Reload()
                Return True
            End If
        End If
    End Function

    Private Sub Set_Add_And_Update_Option()
        connect()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select  * from Options" : dr = cmd.ExecuteReader : dr.Read()
        'If dr.HasRows = True Then
        '    cmbcats.Text = dr(0).ToString
        '    cmbGroup_Branch.Text = dr(1).ToString
        '    cmbitmnm.Text = dr(2).ToString
        '    cmbUnity.Text = dr(3).ToString
        '    txtrng.Text = dr(4).ToString
        '    txtqunt.Text = dr(5).ToString
        '    TxtPrc.Text = dr(6).ToString
        '    itmprc = dr(6).ToString
        '    txttinprice.Text = dr(7).ToString
        '    txt_priseSal.Text = dr(8).ToString
        '    txtWholePrice.Text = dr(9).ToString
        '    txtWholeWholePrice.Text = dr(10).ToString
        '    txtMinimumSalPrice.Text = dr(11).ToString
        '    cmbStores.Text = dr(12).ToString
        '    QuickSearch = dr(13).ToString
        '    txtHeight.Text = dr(14).ToString
        '    txtWidth.Text = dr(15).ToString
        '    txtAltitude.Text = dr(16).ToString
        '    txtDensity.Text = dr(17).ToString
        '    BalanceBarcode = dr(18).ToString
        '    txtRateWholePrice.Text = dr(19).ToString
        '    txtRateWholeWholePrice.Text = dr(20).ToString
        '    txtRateMinimumSalPrice.Text = dr(21).ToString
        '    txtRateVAT.Text = dr(22).ToString
        '    cmbvendores.Text = dr(23).ToString
        '    txtTinPriceAverage.Text = dr(24).ToString
        '    txtRateDiscTinPrice.Text = dr(25).ToString
        '    txtRateDiscSalPrice.Text = dr(26).ToString
        '    txtRateDiscWholePrice.Text = dr(27).ToString
        '    txtRateDiscWholeWholePrice.Text = dr(28).ToString
        '    PriceIncludesVAT = dr(29).ToString
        '    txtRateDiscTinPriceAfter.Text = dr(30).ToString
        '    txtRateDiscSalPriceAfter.Text = dr(31).ToString
        '    txtRateDiscWholePriceAfter.Text = dr(32).ToString
        '    txtRateDiscWholeWholePriceAfter.Text = dr(33).ToString
        'End If
    End Sub

    Private Sub Get_Add_And_Update_Option()
        connect()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'S = "update Customers set "
        'S = S & "Company_Branch_ID =N'" & Company_Branch_ID & "',"
        'S = S & "Cust_Code =N'" & txtCust_Code.Text & "',"
        'S = S & "Vendorname =N'" & vndr.Text.Trim & "',"
        'S = S & "addr =N'" & addr.Text & "',"
        'S = S & "tel1 =N'" & tel1.Text & "',"
        'S = S & "notes =N'" & notes.Text & "',"
        'S = S & "Mobile =N'" & txtMobile.Text & "',"
        'S = S & "Apartment =N'" & txtApartment.Text & "',"
        'S = S & "Role =N'" & txtRole.Text & "',"
        'S = S & "Region =N'" & txtRegion.Text & "',"
        'S = S & "Mark =N'" & txtMark.Text & "',"
        'S = S & "Limit_DrawDowns_Price =N'" & txtLimit_DrawDowns_Price.Text & "',"
        'S = S & "taxn =N'" & txttaxn.Text & "',"
        'S = S & "tradn =N'" & txttradn.Text & "',"
        'S = S & "PriceType_ID =" & Result_Code & ""
        'S = S & "where id = " & ItmID & ""
        'cmd.CommandText = S : cmd.ExecuteNonQuery()


    End Sub

    Private Sub lnkDesignAnotherSalesInvoice_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles lnkDesignAnotherSalesInvoice.LinkClicked
        mykey.SetValue("DesignAnotherSalesInvoice", cmbDesignAnotherSalesInvoice.Text)
        DesignAnotherSalesInvoice = cmbDesignAnotherSalesInvoice.Text

        FrmShowSales.Show()
        FrmShowSales.ChkWithoutDate.Checked = True
        FrmShowSales.ChbAll.Checked = True
        FrmShowSales.BtnShow.PerformClick()
        FrmShowSales.btnDubbileClickGrid.PerformClick()
        FrmShowSales.Button1.PerformClick()
    End Sub

End Class