﻿Imports System.Data.SqlClient
Imports CrystalDecisions.CrystalReports.Engine

Public Class Cust_Pay_CheckType
    Dim VND_no As String

    Private Sub Cust_Pay_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cmbvendorname.Items.Clear()
        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", Cmbvendorname)

        End If
        'Cmbvendorname.Items.Add("كاش")
        PanelCheckPayment.Top = 1000
        PanelCheckTransactions1.Top = 5000
        GetDateNotBeenActivatedPrograms(dtpvndrecipient)

        LoadCheckStatuses()
        LoadTransactionTypes()
        LoadBanks()
        LoadTreasuries()
    End Sub

    Private Sub LoadCheckStatuses()
        Try
            Using conn As New SqlConnection(constring)
                Dim query As String = "SELECT [StatusId], [StatusNameAr] FROM [Check_Status] WHERE [IsActive] = 1"
                Dim da As New SqlDataAdapter(query, conn)
                Dim dt As New DataTable()
                da.Fill(dt)

                cmbStatus.DataSource = dt
                cmbStatus.DisplayMember = "StatusNameAr"
                cmbStatus.ValueMember = "StatusId"
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل حالات الشيكات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadTransactionTypes()
        Try
            Using conn As New SqlConnection(constring)
                Dim query As String = "SELECT [TypeCode], [TypeNameAr] FROM [Check_Transaction_Types]"
                Dim da As New SqlDataAdapter(query, conn)
                Dim dt As New DataTable()
                da.Fill(dt)

                cmbTransactionType.DataSource = dt
                cmbTransactionType.DisplayMember = "TypeNameAr"
                cmbTransactionType.ValueMember = "TypeCode"
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل أنواع الحركات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadBanks()
        Try
            Using conn As New SqlConnection(constring)
                Dim query As String = "SELECT [BankCode], [BankName] FROM [BankData]"
                Dim da As New SqlDataAdapter(query, conn)
                Dim dt As New DataTable()
                da.Fill(dt)

                cmbBank.DataSource = dt
                cmbBank.DisplayMember = "BankName"
                cmbBank.ValueMember = "BankCode"
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل بيانات البنوك: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadTreasuries()
        Try
            Using conn As New SqlConnection(constring)
                Dim query As String = "SELECT [Treasury_Code], [Treasury_Name] FROM [Treasury] WHERE [IsActive] = 1"
                Dim da As New SqlDataAdapter(query, conn)
                Dim dt As New DataTable()
                da.Fill(dt)

                cmbTreasury.DataSource = dt
                cmbTreasury.DisplayMember = "Treasury_Name"
                cmbTreasury.ValueMember = "Treasury_Code"
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل بيانات الخزائن: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub ChbAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChbAll.CheckedChanged
        If ChbAll.Checked = True Then
            Cmbvendorname.Enabled = False
            Cmbvendorname.SelectedIndex = -1
        ElseIf ChbAll.Checked = False Then
            Cmbvendorname.Enabled = True
        End If
    End Sub

    Private Sub BtnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnShow.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        GetData()
    End Sub

    Private Sub GetData()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If rdoCheckOpen.Checked = True Then
                S = Cls.Get_Select_Grid_S("id,VND_no,Vendorname,VND_XTM,VND_dt,VND_Date_Maturity,VND_amx,VND_ho,VND_rcv,VND_dec,Check_Type", "Vst", "VND_no <> N'جرد' and Check_Type =N'مفتوح'")
            End If
            If rdoCheckClosed.Checked = True Then
                S = Cls.Get_Select_Grid_S("id,VND_no,Vendorname,VND_XTM,VND_dt,VND_Date_Maturity,VND_amx,PaymentTotal,VND_ho,VND_rcv,VND_dec,Check_Type", "Vst_Check_Type", "VND_no <> N'جرد' and Check_Type =N'مغلق'")
            End If
            If ChbAll.Checked = False Then
                S = S & " and Vendorname =N'" & Cmbvendorname.Text.Trim & "'"
            End If
            If ChkWithoutDate.Checked = False Then
                S = S & " and VND_dt >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and VND_dt <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
            End If
            If PermtionName <> "مدير" Then
                S = S & " and Company_Branch_ID =N'" & Company_Branch_ID & "'"
            End If
            If FilterSelect = "Number" Then
                S = S & " order by id"
            End If
            If FilterSelect = "Date" Then
                S = S & " order by VND_dt"
            End If
            If FilterSelect = "Name" Then
                S = S & " order by Vendorname"
            End If

            cmd.CommandText = S : dr = cmd.ExecuteReader
            DataGridView1.DataSource = Cls.PopulateDataView(dr)

            If rdoCheckOpen.Checked = True Then
                DataGridView1.Columns(1).HeaderCell.Value = "رقم الإيصال"
                DataGridView1.Columns(2).HeaderCell.Value = "أسم العميل"
                DataGridView1.Columns(3).HeaderCell.Value = "الوقت"
                DataGridView1.Columns(4).HeaderCell.Value = "تاريخ التسجيل"
                DataGridView1.Columns(5).HeaderCell.Value = "تاريخ الاستحقاق"
                DataGridView1.Columns(6).HeaderCell.Value = "المبلغ"
                DataGridView1.Columns(7).HeaderCell.Value = "طريقة الدفع"
                DataGridView1.Columns(8).HeaderCell.Value = "المستلم"
                DataGridView1.Columns(9).HeaderCell.Value = "ملاحظات"
                DataGridView1.Columns(10).HeaderCell.Value = "حالة الشيك"
                DataGridView1.Columns(2).Width = 100
                DataGridView1.Columns(7).Width = 100
                DataGridView1.Columns(0).Width = 0
            End If
            If rdoCheckClosed.Checked = True Then
                DataGridView1.Columns(1).HeaderCell.Value = "رقم الشيك"
                DataGridView1.Columns(2).HeaderCell.Value = "أسم العميل"
                DataGridView1.Columns(3).HeaderCell.Value = "الوقت"
                DataGridView1.Columns(4).HeaderCell.Value = "تاريخ التسجيل"
                DataGridView1.Columns(5).HeaderCell.Value = "تاريخ الاستحقاق"
                DataGridView1.Columns(6).HeaderCell.Value = "المبلغ"
                DataGridView1.Columns(7).HeaderCell.Value = "المتبقى من الشيك"
                DataGridView1.Columns(8).HeaderCell.Value = "طريقة الدفع"
                DataGridView1.Columns(9).HeaderCell.Value = "المستلم"
                DataGridView1.Columns(10).HeaderCell.Value = "ملاحظات"
                DataGridView1.Columns(11).HeaderCell.Value = "حالة الشيك"
                DataGridView1.Columns(2).Width = 100
                DataGridView1.Columns(7).Width = 100
                DataGridView1.Columns(0).Width = 0
            End If

            Dim SM As Double
            For i As Integer = 0 To DataGridView1.Rows.Count - 1
                SM = SM + DataGridView1.Rows(i).Cells(6).Value
            Next
            txt_ValTinx.Text = SM
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        PanelCheckPayment.Top = 1000
    End Sub

    Private Sub DataGridView1_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DataGridView1.DoubleClick
        GetCheckPayment()
    End Sub

    Private Sub GetCheckPayment()
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        txtCustName.Text = DataGridView1.SelectedRows(0).Cells(2).Value
        txtBillNo.Text = DataGridView1.SelectedRows(0).Cells(1).Value
        txtammntOLD.Text = DataGridView1.SelectedRows(0).Cells(7).Value

        If txtammntOLD.Text = "0" Then
            MsgBox("تم سداد الشيك بالكامل", MsgBoxStyle.Exclamation)
            Exit Sub
        End If

        Dim Check_Type As String
        If rdoCheckOpen.Checked = True Then
            Check_Type = DataGridView1.SelectedRows(0).Cells(10).Value
        Else
            Check_Type = DataGridView1.SelectedRows(0).Cells(11).Value
        End If

        If Check_Type = "مغلق" Then
            PanelCheckPayment.Top = 10
            PanelCheckPayment.Location = New System.Drawing.Point(120, 30)
            PanelCheckPayment.Size = New System.Drawing.Size(600, 380)

            MAXRECORD("Vst")
            Dim VstNo As Double = VND_no
            MAXRECORD("Vst_Check_Type")
            Dim Vst_CheckNo As Double = VND_no
            If VstNo > Vst_CheckNo Then
                txtno.Text = VstNo
            Else
                txtno.Text = Vst_CheckNo
            End If

        End If
        txtrecipient.Focus()
    End Sub

    Private Sub MAXRECORD(ByVal Tabel As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from " & Tabel & " where VND_no <> N'جرد' and VND_no <> N'دفعة نقدية'"
            dr = cmd.ExecuteReader

            Dim dt As New DataTable
            dt.Load(dr)

            If dt.Rows.Count = 0 Then
                VND_no = 1
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(CAST(VND_no As float)) as mb FROM " & Tabel & " where VND_no <> N'جرد' and VND_no <> N'دفعة نقدية'"
                dr = cmd.ExecuteReader
                dr.Read()

                Dim sh As Long
                sh = dr("mb")
                VND_no = sh + 1
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub


    Private Sub btnEnterStore_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnEnterStore.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateTextAdd() = False Then Exit Sub

        Custmersfinance()

        '===============================================================================
        Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
        If UseAccounts = "YES" Then
            Daily_Restrictions()
        End If
        '===============================================================================

        Update_Vst_Check_Type()

        Get_Movement_In_Out_Money(DateTimePicker1.Text, Treasury_Code)

        'txtrecipient.Text = ""
        txtno.Text = ""
        txtammnt.Text = ""
        Cmbvendorname.Text = ""
        txtdet.Text = ""
        MsgBox("تم إجراء العملية بنجاح", MsgBoxStyle.Information)
        PanelCheckPayment.Top = 1000
        GetData()
    End Sub

    Sub Custmersfinance()
        Try
            Dim Check_Type As String = "مفتوح"
            Dim MTHODX As String
            Dim Date_Maturity As String
            MTHODX = "حساب مخصم من الشيك"
            Date_Maturity = "0"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into vst (Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,VND_no,billno,UserName,VND_Date_Maturity,Check_Type,BillNo_Check,Treasury_Code) values"
            S = S & " (N'" & Company_Branch_ID & "',N'" & txtCustName.Text & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(dtpvndrecipient.Text) & "'," & txtammnt.Text.Trim & ",N'" & MTHODX & "',N'" & txtrecipient.Text & "',N'" & txtdet.Text.Trim & "',N'" & txtno.Text & "',N'دفعة نقدية',N'" & UserName & "',N'" & Cls.C_date(dtpvndrecipient.Text) & "',N'" & Check_Type & "',N'" & txtBillNo.Text & "',N'" & Treasury_Code & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            IM.CustomerAccountTotal(txtCustName.Text.Trim)

            Dim CurrentBalanceCustVnd As String = GetCurrentBalanceCustVnd(txtCustName.Text)
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update vst set CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where BillNo =N'" & txtno.Text & "' and Vendorname =N'" & txtCustName.Text & "' and VND_dt =N'" & Cls.C_date(dtpvndrecipient.Text) & "'" : cmd.ExecuteNonQuery()


        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try


    End Sub

    Function ValidateTextAdd() As Boolean

        If Val(txtammnt.Text) = 0 Then
            MsgBox("فضلاً أدخل المبلغ", MsgBoxStyle.Exclamation)
            txtammnt.Focus()
            Return False
        End If

        If Trim(txtrecipient.Text) = "" Then
            MsgBox("فضلاً أدخل المستلم", MsgBoxStyle.Exclamation)
            txtrecipient.Focus()
            Return False
        End If

        If Trim(txtno.Text) = "" Then
            MsgBox("فضلاً أدخل رقم الإيصال", MsgBoxStyle.Exclamation)
            txtno.Focus()
            Return False
        End If

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * From vst where VND_no =N'" & txtno.Text & "'"
            dr = cmd.ExecuteReader()
            If dr.HasRows = True Then
                MsgBox("عفواً يوجد رقم ايصال او شيك مسجل مسبقا بنفس الرقم", MsgBoxStyle.Exclamation)
                Return False : Exit Function
            Else
            End If

            If Val(txtammntOLD.Text) < Val(txtammnt.Text) Then
                MsgBox("عفواً الدفعة المدفوعة أكبر من الشيك المستحق", MsgBoxStyle.Exclamation)
                Return False
                Exit Function
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        Return True
    End Function

    Private Sub Update_Vst_Check_Type()
        Try
            Dim Check_Type As String = "مفتوح"
            Dim TOTALAmount As String = txtammntOLD.Text - txtammnt.Text
            If TOTALAmount = 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Vst set Check_Type =N'" & Check_Type & "' where VND_no =N'" & txtBillNo.Text & "'" : cmd.ExecuteNonQuery()
            End If

            Dim TOTAL As String = txtammntOLD.Text - txtammnt.Text
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vst_Check_Type set PaymentTotal = " & Val(TOTAL) & ",Check_Type =N'مغلق' where VND_no =N'" & txtBillNo.Text & "'" : cmd.ExecuteNonQuery()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub txtrecipient_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtrecipient.KeyUp
        If e.KeyCode = 13 Then
            txtammnt.Focus()
        End If
    End Sub

    Private Sub txtammnt_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtammnt.KeyUp
        If e.KeyCode = 13 Then
            txtdet.Focus()
        End If
    End Sub

    Private Sub txtdet_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtdet.KeyUp
        If e.KeyCode = 13 Then
            btnEnterStore.PerformClick()
        End If
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        connect()
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If rdoCheckClosed.Checked = True Then
            Dim ItmID, XDate As String
            For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
                If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
                If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
                Try
                    ItmID = DataGridView1.SelectedRows(i).Cells(0).Value
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "delete From  Vst_Check_Type where id =N'" & ItmID & "'"
                    cmd.ExecuteNonQuery()

                    XDate = DataGridView1.SelectedRows(i).Cells(4).Value
                    Get_Movement_In_Out_Money(XDate, Treasury_Code)
                Catch ex As Exception
                    ErrorHandling(ex, Me.Text)
                End Try
            Next
        End If

        GetData()
    End Sub

    Private Sub btnPrint_Click(sender As Object, e As EventArgs) Handles btnPrint.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        Try
            If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

            Cls.delete_Branch_All("PrintSalesPurchases")

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To DataGridView1.Rows.Count - 1
                S = "insert into PrintSalesPurchases(Company_Branch_ID,BILL_NO,Vendorname,billtime,bill_date,qu,VnPay,CustomerName,det,Stat,totalprice)  values("
                S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView1.Rows(i).Cells(1).Value & "',N'" & DataGridView1.Rows(i).Cells(2).Value & "',N'" & DataGridView1.Rows(i).Cells(3).Value & "',N'" & DataGridView1.Rows(i).Cells(4).Value & "',N'" & DataGridView1.Rows(i).Cells(6).Value & "',N'" & DataGridView1.Rows(i).Cells(7).Value & "',N'" & DataGridView1.Rows(i).Cells(8).Value & "',N'" & DataGridView1.Rows(i).Cells(9).Value & "',N'" & DataGridView1.Rows(i).Cells(10).Value & "',N'" & txt_ValTinx.Text & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
            AddReportView()
            Dim rpt As New Rpt_PayCtm
            Dim txt, txtname, txtNameAr, txtNameEn As TextObject

            Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
            Dim dt As New DataTable
            dt.Load(dr)
            rpt.SetDataSource(dt)
            txt = rpt.Section1.ReportObjects("Text10")
            txt.Text = "تقرير حساب مقبوضات العملاء"
            txtname = rpt.Section1.ReportObjects("Text12")
            txtname.Text = "أسم العميل"
            txtNameAr = rpt.Section1.ReportObjects("Text1")
            txtNameAr.Text = NameArCompay
            txtNameEn = rpt.Section1.ReportObjects("Text2")
            txtNameEn.Text = NameEnCompany
            If SelectLogoPathOther = "YES" Then
                rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
            Else
                rpt.SetParameterValue("ImageURL", CMPLogoPath)
            End If
            Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
            Frm_PrintReports.Text = "تقرير حساب مقبوضات العملاء"
            Frm_PrintReports.Show()

            If RunDatabaseInternet = "YES" Then : connect() : End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub txtammnt_TextChanged(sender As Object, e As EventArgs) Handles txtammnt.TextChanged
        MyVars.CheckNumber(txtammnt)
    End Sub

    Private Sub Daily_Restrictions()
        Try
            Dim Account As String = ""
            Dim AccountCode As String = ""

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مقبوضات عملاء'"
            dr = cmd.ExecuteReader
            If dr.Read Then
                Account = dr("Link_AccountsTree")
                AccountCode = dr("ACCNumber")
            End If

            '========================================================================================

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from MOVES where MOVID =N'0'" : H = cmd.ExecuteScalar
            If H > 0 Then
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVES(MOVID,MOVRegNumber,MOVStatement,MOVDebtor,MOVCreditor,UserName ) values ("
                S = S & "N'0',N'0',N'0',N'0',N'0',N'0')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If


            Dim AccountTreasury As String = "" : Dim AccountCodeTreasury As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select ACCName,ACCNumber from AccountsTree where ACCName=N'الخزينة'" : dr = cmd.ExecuteReader
            If dr.Read Then
                AccountTreasury = dr("ACCName") : AccountCodeTreasury = dr("ACCNumber")
            End If

            Dim MOVRegNumber As String = Cls.MAXRECORD("MOVES", "MOVRegNumber")
            Dim MOVID As String = Cls.MAXRECORD("MOVES", "MOVID")
            Dim bill_no As String = Cls.MAXRECORD("Vst", "id") - 1

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MOVES(MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
            S = S & "N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpvndrecipient.Text) & "',N'" & Account & "',N'" & txtammnt.Text & "',N'" & txtammnt.Text & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            ' من حساب / الخزينة
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
            S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpvndrecipient.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'" & txtammnt.Text & "',N'0',N'" & AccountTreasury & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            ' الى حساب / مقبوضات عملاء
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
            S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpvndrecipient.Text) & "',N'" & AccountCode & "',N'" & Account & "',N'0',N'" & txtammnt.Text & "',N'" & Account & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()


        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs)
        FrmFilter.ShowDialog()
    End Sub

    Private Sub btnFilter_Click_1(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub

    Private Sub btnCheckPayment_Click(sender As Object, e As EventArgs) Handles btnCheckPayment.Click
        GetCheckPayment()
    End Sub

    Private Sub btnCheckStatus_Click(sender As Object, e As EventArgs) Handles btnCheckStatus.Click
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        'txtCustName.Text = DataGridView1.SelectedRows(0).Cells(2).Value
        'txtBillNo.Text = DataGridView1.SelectedRows(0).Cells(1).Value
        txtammntOLD.Text = DataGridView1.SelectedRows(0).Cells(7).Value

        If txtammntOLD.Text = "0" Then
            MsgBox("تم سداد الشيك بالكامل", MsgBoxStyle.Exclamation)
            Exit Sub
        End If

        If DataGridView1.SelectedRows.Count > 0 Then
            Dim row As DataGridViewRow = DataGridView1.SelectedRows(0)
            Dim checkId As Integer = Convert.ToInt32(row.Cells("id").Value)

            LoadCheckDetails(checkId)
            LoadCheckTransactions(checkId)

            PanelCheckTransactions1.Top = 30
            PanelCheckTransactions1.Dock = DockStyle.Fill
        End If
    End Sub

    Private Sub LoadCheckDetails(checkId As Integer)
        Try
            Using conn As New SqlConnection(constring)
                Dim query As String = "SELECT c.id, c.Vendorname, c.VND_XTM, c.VND_dt, c.VND_Date_Maturity, c.VND_amx, c.VND_ho, c.VND_rcv, c.VND_dec, c.VND_no, c.BillNo, c.UserName, c.Check_Type, c.PaymentTotal, c.Company_Branch_ID, " &
                      "c.Treasury_Code, c.CreditPrevious, c.DebitPrevious, c.CreditCurrent, c.DebitCurrent, c.CurrentBalanceCustVnd, c.Check_Status_ID, c.BankID, s.StatusNameAr, b.BankName " &
                      "FROM [Vst_Check_Type] c " &
                      "INNER JOIN [Check_Status] s ON c.[Check_Status_ID] = s.[StatusId] " &
                      "INNER JOIN [BankData] b ON c.[BankID] = b.[BankID] " &
                      "WHERE c.[id] = @CheckId"

                Dim cmd As New SqlCommand(query, conn)
                cmd.Parameters.AddWithValue("@CheckId", checkId)

                conn.Open()
                Dim reader As SqlDataReader = cmd.ExecuteReader()

                If reader.Read() Then
                    txtCheckId.Text = reader("id").ToString()
                    txtVendor.Text = reader("Vendorname").ToString()
                    txtCheckNumber.Text = reader("VND_no").ToString()
                    txtAmount.Text = Convert.ToDecimal(reader("VND_amx")).ToString("N2")
                    dtpIssueDate.Value = Cls.R_date(reader("VND_dt"))
                    dtpMaturityDate.Value = Cls.R_date(reader("VND_Date_Maturity"))
                    txtBankName.Text = reader("BankName").ToString()
                    txtStatus.Text = reader("StatusNameAr").ToString()
                    cmbStatus.SelectedValue = Convert.ToInt32(reader("Check_Status_ID"))

                End If

                reader.Close()
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل تفاصيل الشيك: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadCheckTransactions(checkId As Integer)
        Try
            Using conn As New SqlConnection(constring)
                Dim query As String = "SELECT t.[TransactionId] AS [رقم], " &
                     "t.[TransactionDate] AS [تاريخ الحركة], " &
                     "t.[TransactionTypeAr] AS [نوع الحركة], " &
                     "t.[Amount] AS [المبلغ], " &
                     "u.[UserName] AS [المستخدم], " &
                     "tr.[Treasury_Name] AS [الخزينة], " &
                     "b.[BankName] AS [البنك], " &
                     "t.[Notes] AS [ملاحظات], " &
                     "CASE WHEN t.[IsReversed] = 1 THEN 'معكوسة' ELSE 'فعالة' END AS [الحالة] " &
                     "FROM [Check_Transactions] t " &
                     "INNER JOIN [BankData] b ON t.[BankCode] = b.[BankID] " &
                     "INNER JOIN [Treasury] tr ON t.[TreasuryCode] = tr.[Treasury_Code] " &
                     "INNER JOIN [Users] u ON t.[UserName] = u.[UserID] " &
                     "WHERE t.[CheckId] = @CheckId " &
                     "ORDER BY [تاريخ الحركة] DESC"

                Dim cmd As New SqlCommand(query, conn)
                cmd.Parameters.AddWithValue("@CheckId", checkId)

                Dim da As New SqlDataAdapter(cmd)
                Dim dt As New DataTable()
                da.Fill(dt)

                dgvTransactions.DataSource = dt
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل حركات الشيك: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        PanelCheckTransactions1.Dock = DockStyle.None
        PanelCheckTransactions1.Top = 5000
    End Sub

    Private Sub btnAddTransaction_Click(sender As Object, e As EventArgs) Handles btnAddTransaction.Click
        If String.IsNullOrEmpty(txtCheckId.Text) Then
            MessageBox.Show("يجب اختيار شيك أولاً", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If cmbTransactionType.SelectedIndex = -1 Then
            MessageBox.Show("يجب اختيار نوع الحركة", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim amount As Decimal
        If Not Decimal.TryParse(txtTransactionAmount.Text, amount) OrElse amount <= 0 Then
            MessageBox.Show("يجب إدخال مبلغ صحيح", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            Using conn As New SqlConnection(constring)
                conn.Open()
                Dim cmd As New SqlCommand()
                cmd.Connection = conn

                Try
                    ' بناء جملة INSERT مباشرة للجدول Check_Transactions
                    Dim insertQuery As String = "INSERT INTO Check_Transactions " &
                                              "(CheckId, TransactionTypeCode, TransactionTypeAr, TransactionTypeEn, " &
                                              "Amount, TransactionDate, StatusBefore, StatusAfter, UserName, " &
                                              "TreasuryCode, BankCode, ReferenceNo, Notes) " &
                                              "VALUES " &
                                              "(@CheckId, @TransactionTypeCode, @TransactionTypeAr, @TransactionTypeEn, " &
                                              "@Amount, GETDATE(), @StatusBefore, @StatusAfter, @UserName, " &
                                              "@TreasuryCode, @BankCode, @ReferenceNo, @Notes)"

                    cmd.CommandText = insertQuery

                    ' الحصول على حالة الشيك الحالية (StatusBefore)
                    Dim statusBefore As Integer = 0
                    Dim getStatusCmd As New SqlCommand("SELECT Check_Status_ID FROM Vst_Check_Type WHERE id = @CheckId", conn)
                    getStatusCmd.Parameters.AddWithValue("@CheckId", Convert.ToInt32(txtCheckId.Text))
                    Dim result = getStatusCmd.ExecuteScalar()
                    If result IsNot Nothing AndAlso Not IsDBNull(result) Then
                        statusBefore = Convert.ToInt32(result)
                    End If

                    ' الحصول على حالة الشيك الجديدة (StatusAfter) بناءً على نوع الحركة
                    Dim statusAfter As Integer = statusBefore
                    Dim transactionTypeAr As String = ""
                    Dim transactionTypeEn As String = ""

                    Dim getTypeCmd As New SqlCommand("SELECT TypeNameAr, TypeNameEn, " &
                                                   "CASE WHEN TypeCode = 'PAYMENT' THEN (SELECT StatusId FROM Check_Status WHERE StatusCode = 'PAID') " &
                                                   "WHEN TypeCode = 'RETURN' THEN (SELECT StatusId FROM Check_Status WHERE StatusCode = 'RETURNED') " &
                                                   "WHEN TypeCode = 'CANCEL' THEN (SELECT StatusId FROM Check_Status WHERE StatusCode = 'CANCELLED') " &
                                                   "ELSE @StatusBefore END AS NewStatus " &
                                                   "FROM Check_Transaction_Types WHERE TypeCode = @TypeCode", conn)
                    getTypeCmd.Parameters.AddWithValue("@TypeCode", cmbTransactionType.SelectedValue.ToString())
                    getTypeCmd.Parameters.AddWithValue("@StatusBefore", statusBefore)

                    Using reader = getTypeCmd.ExecuteReader()
                        If reader.Read() Then
                            transactionTypeAr = reader("TypeNameAr").ToString()
                            transactionTypeEn = reader("TypeNameEn").ToString()
                            statusAfter = Convert.ToInt32(reader("NewStatus"))
                        End If
                    End Using

                    ' إضافة المعاملات للـ INSERT
                    cmd.Parameters.AddWithValue("@CheckId", Convert.ToInt32(txtCheckId.Text))
                    cmd.Parameters.AddWithValue("@TransactionTypeCode", cmbTransactionType.SelectedValue.ToString())
                    cmd.Parameters.AddWithValue("@TransactionTypeAr", transactionTypeAr)
                    cmd.Parameters.AddWithValue("@TransactionTypeEn", transactionTypeEn)
                    cmd.Parameters.AddWithValue("@Amount", amount)
                    cmd.Parameters.AddWithValue("@StatusBefore", statusBefore)
                    cmd.Parameters.AddWithValue("@StatusAfter", statusAfter)
                    cmd.Parameters.AddWithValue("@UserName", UserID)

                    If cmbTreasury.SelectedIndex <> -1 Then
                        cmd.Parameters.AddWithValue("@TreasuryCode", cmbTreasury.SelectedValue.ToString())
                    Else
                        cmd.Parameters.AddWithValue("@TreasuryCode", Treasury_Code)
                    End If

                    If cmbBank.SelectedIndex <> -1 Then
                        cmd.Parameters.AddWithValue("@BankCode", cmbBank.SelectedValue.ToString())
                    Else
                        cmd.Parameters.AddWithValue("@BankCode", DBNull.Value)
                    End If

                    cmd.Parameters.AddWithValue("@ReferenceNo", DBNull.Value) ' يمكن تغييرها إذا كان لديك رقم مرجعي
                    cmd.Parameters.AddWithValue("@Notes", txtNotes.Text)

                    ' تنفيذ الأمر
                    cmd.ExecuteNonQuery()

                    ' تحديث حالة الشيك إذا تغيرت
                    If statusAfter <> statusBefore Then
                        Dim updateStatusCmd As New SqlCommand("UPDATE Vst_Check_Type SET Check_Status_ID = @StatusAfter WHERE id = @CheckId", conn)
                        updateStatusCmd.Parameters.AddWithValue("@StatusAfter", statusAfter)
                        updateStatusCmd.Parameters.AddWithValue("@CheckId", Convert.ToInt32(txtCheckId.Text))
                        updateStatusCmd.ExecuteNonQuery()
                    End If

                    ' إضافة الحركة في جدول vst (إذا كنت لا تزال تحتاجها)
                    Dim S As String = "INSERT INTO vst (Company_Branch_ID, Vendorname, VND_XTM, VND_dt, " &
                                      "VND_amx, VND_ho, VND_rcv, VND_dec, VND_no, billno, UserName, " &
                                      "VND_Date_Maturity, Check_Type, BillNo_Check, Treasury_Code) VALUES " &
                                      "(@Company_Branch_ID, @Vendorname, @VND_XTM, @VND_dt, " &
                                      "@VND_amx, @VND_ho, @VND_rcv, @VND_dec, @VND_no, @billno, @UserName, " &
                                      "@VND_Date_Maturity, @Check_Type, @BillNo_Check, @Treasury_Code)"

                    Dim vstCmd As New SqlCommand(S, conn)
                    vstCmd.Parameters.AddWithValue("@Company_Branch_ID", Company_Branch_ID)
                    vstCmd.Parameters.AddWithValue("@Vendorname", txtCustName.Text)
                    vstCmd.Parameters.AddWithValue("@VND_XTM", Cls.get_time(True))
                    vstCmd.Parameters.AddWithValue("@VND_dt", Cls.C_date(dtpvndrecipient.Text))
                    vstCmd.Parameters.AddWithValue("@VND_amx", txtammnt.Text.Trim)
                    vstCmd.Parameters.AddWithValue("@VND_ho", cmbTransactionType.Text)
                    vstCmd.Parameters.AddWithValue("@VND_rcv", txtrecipient.Text)
                    vstCmd.Parameters.AddWithValue("@VND_dec", txtdet.Text.Trim)
                    vstCmd.Parameters.AddWithValue("@VND_no", txtno.Text)
                    vstCmd.Parameters.AddWithValue("@billno", "دفعة نقدية")
                    vstCmd.Parameters.AddWithValue("@UserName", UserName)
                    vstCmd.Parameters.AddWithValue("@VND_Date_Maturity", Cls.C_date(dtpvndrecipient.Text))
                    vstCmd.Parameters.AddWithValue("@Check_Type", cmbTransactionType.Text)
                    vstCmd.Parameters.AddWithValue("@BillNo_Check", txtBillNo.Text)
                    vstCmd.Parameters.AddWithValue("@Treasury_Code", Treasury_Code)

                    vstCmd.ExecuteNonQuery()

                    MessageBox.Show("تم إضافة الحركة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information)

                    ' تحديث البيانات
                    Dim checkId As Integer = Convert.ToInt32(txtCheckId.Text)
                    LoadCheckDetails(checkId)
                    LoadCheckTransactions(checkId)

                Catch ex As Exception
                    MessageBox.Show("حدث خطأ أثناء إضافة الحركة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End Try
            End Using

            'Using conn As New SqlConnection(constring)
            '    Dim cmd As New SqlCommand("sp_AddCheckTransaction", conn)
            '    cmd.CommandType = CommandType.StoredProcedure

            '    cmd.Parameters.AddWithValue("@CheckId", Convert.ToInt32(txtCheckId.Text))
            '    cmd.Parameters.AddWithValue("@TransactionTypeCode", cmbTransactionType.SelectedValue.ToString())
            '    cmd.Parameters.AddWithValue("@Amount", amount)
            '    cmd.Parameters.AddWithValue("@UserId", UserID) ' افترضنا وجود متغير عام للمستخدم الحالي

            '    If cmbTreasury.SelectedIndex <> -1 Then
            '        cmd.Parameters.AddWithValue("@TreasuryCode", cmbTreasury.SelectedValue.ToString())
            '    Else
            '        cmd.Parameters.AddWithValue("@TreasuryCode", Treasury_Code)
            '    End If

            '    If cmbBank.SelectedIndex <> -1 Then
            '        cmd.Parameters.AddWithValue("@BankCode", cmbBank.SelectedValue.ToString())
            '    Else
            '        cmd.Parameters.AddWithValue("@BankCode", DBNull.Value)
            '    End If

            '    cmd.Parameters.AddWithValue("@Notes", txtNotes.Text)

            '    conn.Open()
            '    cmd.ExecuteNonQuery()

            '    MessageBox.Show("تم إضافة الحركة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information)

            '    ' تحديث البيانات
            '    Dim checkId As Integer = Convert.ToInt32(txtCheckId.Text)
            '    LoadCheckDetails(checkId)
            '    LoadCheckTransactions(checkId)
            'End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في إضافة الحركة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

    End Sub

    Private Sub btnReverseTransaction_Click(sender As Object, e As EventArgs) Handles btnReverseTransaction.Click
        If dgvTransactions.SelectedRows.Count = 0 Then
            MessageBox.Show("يجب اختيار حركة لعكسها", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim transactionId As Integer = Convert.ToInt32(dgvTransactions.SelectedRows(0).Cells("TransactionId").Value)

        Dim reverseForm As New CheckReverseTransaction()
        If reverseForm.ShowDialog() = DialogResult.OK Then

            'Try
            '    Using conn As New SqlConnection(constring)
            '        Dim cmd As New SqlCommand("sp_ReverseCheckTransaction", conn)
            '        cmd.CommandType = CommandType.StoredProcedure

            '        cmd.Parameters.AddWithValue("@TransactionId", transactionId)
            '        cmd.Parameters.AddWithValue("@UserId", UserID)
            '        cmd.Parameters.AddWithValue("@Reason", reverseForm.Reason)

            '        conn.Open()
            '        cmd.ExecuteNonQuery()

            '        MessageBox.Show("تم عكس الحركة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information)

            '        ' تحديث البيانات
            '        Dim checkId As Integer = Convert.ToInt32(txtCheckId.Text)
            '        LoadCheckTransactions(checkId)
            '        LoadCheckDetails(checkId)
            '    End Using
            'Catch ex As Exception
            '    MessageBox.Show("خطأ في عكس الحركة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            'End Try

            Try
                Using conn As New SqlConnection(constring)
                    conn.Open()

                    ' بدء المعاملة
                    Dim transaction As SqlTransaction = conn.BeginTransaction()

                    Try
                        ' 1. الحصول على بيانات الحركة الأصلية
                        Dim checkId As Integer = 0
                        Dim transactionTypeCode As String = ""
                        Dim amount As Decimal = 0
                        Dim statusBefore As Integer = 0
                        Dim statusAfter As Integer = 0
                        Dim reason As String = ""

                        Dim getOriginalCmd As New SqlCommand(
                            "SELECT [CheckId], [TransactionTypeCode], [Amount], [StatusBefore], [StatusAfter] " &
                            "FROM [Check_Transactions] " &
                            "WHERE [TransactionId] = @TransactionId AND [IsReversed] = 0",
                            conn, transaction)

                        getOriginalCmd.Parameters.AddWithValue("@TransactionId", transactionId)

                        Using reader As SqlDataReader = getOriginalCmd.ExecuteReader()
                            If reader.Read() Then
                                checkId = reader.GetInt32(0)
                                transactionTypeCode = reader.GetString(1)
                                amount = reader.GetDecimal(2)
                                statusBefore = reader.GetInt32(3)
                                statusAfter = reader.GetInt32(4)
                            Else
                                Throw New Exception("الحركة غير موجودة أو معكوسة مسبقاً")
                            End If
                        End Using

                        ' 2. تحديث الحركة الأصلية لتظهر كمعكوسة
                        Dim updateCmd As New SqlCommand(
                            "UPDATE [Check_Transactions] SET " &
                            "[IsReversed] = 1, " &
                            "[ReversedDate] = GETDATE(), " &
                            "[ReversedBy] = @UserId, " &
                            "[ReversalReason] = @Reason " &
                            "WHERE [TransactionId] = @TransactionId",
                            conn, transaction)

                        updateCmd.Parameters.AddWithValue("@TransactionId", transactionId)
                        updateCmd.Parameters.AddWithValue("@UserId", UserID)
                        updateCmd.Parameters.AddWithValue("@Reason", reason)
                        updateCmd.ExecuteNonQuery()

                        ' 3. الحصول على أسماء أنواع الحركات
                        Dim typeNameAr As String = ""
                        Dim typeNameEn As String = ""
                        Dim affectsStatus As Boolean = False

                        Dim getTypeCmd As New SqlCommand(
                            "SELECT [TypeNameAr], [TypeNameEn], [AffectsStatus] " &
                            "FROM [Check_Transaction_Types] " &
                            "WHERE [TypeCode] = @TypeCode",
                            conn, transaction)

                        getTypeCmd.Parameters.AddWithValue("@TypeCode", transactionTypeCode)

                        Using reader As SqlDataReader = getTypeCmd.ExecuteReader()
                            If reader.Read() Then
                                typeNameAr = reader.GetString(0)
                                typeNameEn = reader.GetString(1)
                                affectsStatus = reader.GetBoolean(2)
                            End If
                        End Using

                        ' 4. إضافة حركة عكسية
                        Dim insertCmd As New SqlCommand(
                            "INSERT INTO [Check_Transactions] (" &
                            "[CheckId], [TransactionTypeCode], [TransactionTypeAr], [TransactionTypeEn], " &
                            "[Amount], [StatusBefore], [StatusAfter], [UserId], " &
                            "[Notes], [IsReversed], [ReferenceNo]) " &
                            "VALUES (" &
                            "@CheckId, @TransactionTypeCode, @TransactionTypeAr, @TransactionTypeEn, " &
                            "@Amount, @StatusBefore, @StatusAfter, @UserId, " &
                            "@Notes, @IsReversed, @ReferenceNo)",
                            conn, transaction)

                        insertCmd.Parameters.AddWithValue("@CheckId", checkId)
                        insertCmd.Parameters.AddWithValue("@TransactionTypeCode", transactionTypeCode)
                        insertCmd.Parameters.AddWithValue("@TransactionTypeAr", "عكس " & typeNameAr)
                        insertCmd.Parameters.AddWithValue("@TransactionTypeEn", "Reverse " & typeNameEn)
                        insertCmd.Parameters.AddWithValue("@Amount", amount)
                        insertCmd.Parameters.AddWithValue("@StatusBefore", statusAfter)
                        insertCmd.Parameters.AddWithValue("@StatusAfter", statusBefore)
                        insertCmd.Parameters.AddWithValue("@UserId", UserID)
                        insertCmd.Parameters.AddWithValue("@Notes", reason)
                        insertCmd.Parameters.AddWithValue("@IsReversed", False)
                        insertCmd.Parameters.AddWithValue("@ReferenceNo", "REV-" & transactionId)
                        insertCmd.ExecuteNonQuery()

                        ' 5. استعادة حالة الشيك الأصلية إذا كانت الحركة تؤثر على الحالة
                        If affectsStatus Then
                            Dim updateStatusCmd As New SqlCommand(
                                "UPDATE [Vst_Check_Type] " &
                                "SET [StatusId] = @StatusId " &
                                "WHERE [id] = @CheckId",
                                conn, transaction)

                            updateStatusCmd.Parameters.AddWithValue("@StatusId", statusBefore)
                            updateStatusCmd.Parameters.AddWithValue("@CheckId", checkId)
                            updateStatusCmd.ExecuteNonQuery()
                        End If

                        ' إتمام المعاملة بنجاح
                        transaction.Commit()

                        MessageBox.Show("تم عكس الحركة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information)

                        ' تحديث البيانات
                        LoadCheckTransactions(checkId)
                        LoadCheckDetails(checkId)

                    Catch ex As Exception
                        ' في حالة حدوث خطأ، التراجع عن المعاملة
                        transaction.Rollback()
                        Throw New Exception("خطأ في عكس الحركة: " & ex.Message)
                    End Try
                End Using
            Catch ex As Exception
                MessageBox.Show(ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub
End Class
