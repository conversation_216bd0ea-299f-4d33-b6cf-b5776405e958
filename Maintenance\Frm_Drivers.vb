﻿Public Class Frm_Drivers


    Sub CLEAR_ALL()
        txtDriv_Address.Text = ""
        txtDriv_Mobile.Text = ""
        txtDriv_Name.Text = ""
    End Sub

    Private Sub txtDriv_Name_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtDriv_Name.KeyUp
        If e.KeyCode = 13 Then
            txtDriv_Mobile.Focus()
        End If
    End Sub

    Private Sub txtDriv_Mobile_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtDriv_Mobile.KeyUp
        If e.KeyCode = 13 Then
            txtDriv_Address.Focus()
        End If
    End Sub

    Private Sub txtDriv_Car_Color_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub BtnNew_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnNew.Click
        CLEAR_ALL()
    End Sub

    Private Sub BtnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAdd.Click
        If Validate_Text() = False Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Maintenance_Drivers(Driv_Name,Driv_Mobile,Driv_Address) values ("
        S = S & "N'" & txtDriv_Name.Text & "',N'" & txtDriv_Mobile.Text.Trim & "',N'" & txtDriv_Address.Text.Trim & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        CLEAR_ALL()
        Header()
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)
        txtDriv_Name.Focus()
    End Sub

    Function Validate_Text() As Boolean
        If Trim(txtDriv_Name.Text) = "" Then
            MsgBox("فضلاً أدخل أسم السائق", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtDriv_Name.Focus() : Return False : Exit Function
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * From Maintenance_Drivers  WHERE Driv_Name =N'" & txtDriv_Name.Text & "'"
        dr = cmd.ExecuteReader()
        If DR.HasRows = True Then
            MsgBox("عفواً يوجد حالة مسجله مسبقا بنفس الاسم", MsgBoxStyle.Exclamation)
            Return False : Exit Function
        End If
        Return True
    End Function

    Private Sub Header()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT  Driv_ID as [رقم], Driv_Name as [اسم السائق], Driv_Mobile as [الموبايل], Driv_Address as [العنوان] FROM  Maintenance_Drivers"
        cmd.CommandText = S : dr = Cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

    End Sub

    Private Sub BtnEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnEdit.Click
        If ValedateEdit() = False Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update Maintenance_Drivers set "
        S = S & "Driv_Name =N'" & txtDriv_Name.Text & "',"
        S = S & "Driv_Mobile =N'" & txtDriv_Mobile.Text & "',"
        S = S & "Driv_Address =N'" & txtDriv_Address.Text & "' where Driv_ID =N'" & txtDriv_ID.Text & "'"
        cmd.CommandText = S : H = cmd.ExecuteNonQuery()

        MsgBox("تم حفظ التعديل بنجاح", MsgBoxStyle.Information)
        Header()
        CLEAR_ALL()
    End Sub

    Function ValedateEdit() As Boolean
        If Trim(txtDriv_Name.Text) = "" Then
            MsgBox("فضلاً أدخل أسم السائق", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtDriv_Name.Focus() : Return False : Exit Function
        End If

        Return True
    End Function

    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If Trim(txtDriv_Name.Text) = "" Then
            MsgBox("فضلاً ادخل أسم السائق", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtDriv_Name.Focus()
            Exit Sub
        End If

        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete  from Maintenance_Drivers  WHERE Driv_Name =N'" & txtDriv_Name.Text & "'" : cmd.ExecuteNonQuery()
        Header()
        CLEAR_ALL()
    End Sub

    Private Sub BtnFind_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnFind.Click
        Panel_Search.Top = 20
        Panel_Search.Dock = DockStyle.Fill
    End Sub

    Private Sub Frm_Drivers_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        Panel_Search.Top = 1000
        Header()
        txtDriv_Name.Focus()
        txtDriv_Name.SelectAll()
    End Sub

    Private Sub DataGridView1_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DataGridView1.DoubleClick
        Dim ItmID As String
        ItmID = DataGridView1.SelectedRows(0).Cells(0).Value

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT Driv_ID, Driv_Name, Driv_Mobile, Driv_Address FROM  Maintenance_Drivers  WHERE Driv_ID =N'" & ItmID & "'" : dr = cmd.ExecuteReader : dr.Read()
        txtDriv_ID.Text = dr(0) : txtDriv_Name.Text = dr(1).ToString() : txtDriv_Mobile.Text = dr(2).ToString() : txtDriv_Address.Text = dr(3).ToString()

        Panel_Search.Dock = DockStyle.None
        Panel_Search.Top = 1000
    End Sub

    Private Sub btnBack_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBack.Click
        Panel_Search.Dock = DockStyle.None
        Panel_Search.Top = 1000
    End Sub

    Private Sub txtSearch_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearch.TextChanged
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If txtSearch.Text = "" Then
            S = "SELECT  Driv_ID as [رقم], Driv_Name as [اسم السائق], Driv_Mobile as [الموبايل], Driv_Address as [العنوان] FROM  Maintenance_Drivers"
        Else
            S = "SELECT  Driv_ID as [رقم], Driv_Name as [اسم السائق], Driv_Mobile as [الموبايل], Driv_Address as [العنوان] FROM  Maintenance_Drivers  WHERE Driv_Name Like N'%" & txtSearch.Text & "%' or Driv_Mobile Like N'%" & txtSearch.Text & "%'"
        End If
        S = S & " order by 1"
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

    End Sub
End Class