# NOTE: Requires **VS2019 16.7** or later

# 'MicrosoftCodeAnalysisReleaseTracking' Rules from '3.3.3' release with 'Recommended' analysis mode escalated to 'error' severity
# Description: 'MicrosoftCodeAnalysisReleaseTracking' Rules with enabled-by-default state from '3.3.3' release with 'Recommended' analysis mode. Rules that are first released in a version later than '3.3.3' are disabled. Enabled rules with 'warning' severity are escalated to 'error' severity to respect 'CodeAnalysisTreatWarningsAsErrors' MSBuild property.

is_global = true

global_level = -99


# RS2000: Add analyzer diagnostic IDs to analyzer release
dotnet_diagnostic.RS2000.severity = error

# RS2001: Ensure up-to-date entry for analyzer diagnostic IDs are added to analyzer release
dotnet_diagnostic.RS2001.severity = error

# RS2002: Do not add removed analyzer diagnostic IDs to unshipped analyzer release
dotnet_diagnostic.RS2002.severity = error

# RS2003: Shipped diagnostic IDs that are no longer reported should have an entry in the 'Removed Rules' table in unshipped file
dotnet_diagnostic.RS2003.severity = error

# RS2004: Diagnostic IDs marked as removed in analyzer release file should not be reported by analyzers
dotnet_diagnostic.RS2004.severity = error

# RS2005: Remove duplicate entries for diagnostic ID in the same analyzer release
dotnet_diagnostic.RS2005.severity = error

# RS2006: Remove duplicate entries for diagnostic ID between analyzer releases
dotnet_diagnostic.RS2006.severity = error

# RS2007: Invalid entry in analyzer release file
dotnet_diagnostic.RS2007.severity = error

# RS2008: Enable analyzer release tracking
dotnet_diagnostic.RS2008.severity = error
