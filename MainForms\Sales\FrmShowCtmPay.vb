﻿Public Class FrmShowCtmPay

    Private Sub FrmShowCtmPay_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cmbvendorname.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.<PERSON>()
        cmd.CommandText = "Select distinct Vendorname from Customers"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            Cmbvendorname.Items.Add(dr("Vendorname"))
        Loop
        GetData()

    End Sub

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.<PERSON>()
        If rdoDiscountCashPayment.Checked = True Then
            S = "Select id, Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,VND_no From vst  where VND_no <> N'جرد'"
        End If
        If rdoAddCashPayment.Checked = True Then
            S = "Select id, Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,VND_no From Vst_Receipts  where VND_no <> N'جرد'"
        End If
        If ChbAll.Checked = False Then
            S = S & " and Vendorname =N'" & Cmbvendorname.Text.Trim & "'"
        End If
        If Chek_WithoutDate.Checked = False Then
            S = S & " and VND_dt >=N'" & Cls.C_date(Dtp_from.Text) & "' and VND_dt <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by id"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by VND_dt"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by Vendorname"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

        DataGridView1.Columns(1).HeaderCell.Value = "الاسم"
        DataGridView1.Columns(2).HeaderCell.Value = "الوقت"
        DataGridView1.Columns(3).HeaderCell.Value = "التاريخ"
        DataGridView1.Columns(4).HeaderCell.Value = "المبلغ"
        DataGridView1.Columns(5).HeaderCell.Value = "طريقة الدفع"
        DataGridView1.Columns(6).HeaderCell.Value = "المستلم"
        DataGridView1.Columns(7).HeaderCell.Value = "ملاحظات"
        DataGridView1.Columns(8).HeaderCell.Value = "رقم الإيصال"
        DataGridView1.Columns(2).Width = 100
        DataGridView1.Columns(0).Width = 0
        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(3).Value)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(3).Value = SM
        Next
    End Sub


    Private Sub ChbAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChbAll.CheckedChanged
        If ChbAll.Checked = True Then
            Cmbvendorname.Enabled = False
            Cmbvendorname.SelectedIndex = -1
        ElseIf ChbAll.Checked = False Then
            Cmbvendorname.Enabled = True
        End If
        GetData()
    End Sub

    Private Sub Chek_WithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Chek_WithoutDate.CheckedChanged
        If Chek_WithoutDate.Checked = True Then
            Dtp_from.Enabled = False
            Dtp_To.Enabled = False
        ElseIf Chek_WithoutDate.Checked = False Then
            Dtp_from.Enabled = True
            Dtp_To.Enabled = True
        End If
        GetData()
    End Sub

    Private Sub BtnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnShow.Click
        GetData()

    End Sub

    Private Sub Cmbvendorname_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Cmbvendorname.SelectedIndexChanged
        GetData()
    End Sub

    Private Sub Dtp_from_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Dtp_from.ValueChanged
        GetData()
    End Sub

    Private Sub Dtp_To_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Dtp_To.ValueChanged
        GetData()
    End Sub

    Private Sub BtnPrt_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnPrt.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        connect()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub

            Dim ItmID As String = DataGridView1.SelectedRows(i).Cells(0).Value
            Dim cust As String = DataGridView1.SelectedRows(i).Cells(1).Value

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If rdoDiscountCashPayment.Checked = True Then
                cmd.CommandText = "delete From  Vst where id =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
            End If
            If rdoAddCashPayment.Checked = True Then
                cmd.CommandText = "delete From  Vst_Receipts where id =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
            End If

            cmd.CommandText = "delete From  MOVES where bill_no =N'" & ItmID & "' and MOVStatement =N'مقبوضات عملاء'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'مقبوضات عملاء'" : cmd.ExecuteNonQuery()
            cmd.ExecuteNonQuery()

            IM.CustomerAccountTotal(cust)

        Next
        GetData()

    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class
