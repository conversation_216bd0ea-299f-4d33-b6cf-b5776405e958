﻿
Public Class Frm_Data_Management

#Region "Load Form"

    Dim MaxRecoedCode As String
    Dim Result_Code As String

    Private Sub Frm_Areas_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        Bra.Fil("Government", "Gov_Name", cmbGovFind)
        Bra.Fil("Government", "Gov_Name", cmbGov_Branch)
        Bra.Fil("Government", "Gov_Name", cmbGov_GeoArea)
        Bra.Fil("Government", "Gov_Name", cmbEmpCust_Gov)
        Bra.Fil("Government", "Gov_Name", cmbGeoEmp_Gov)
        Bra.Fil("Government", "Gov_Name", cmbGovDelegateBranch)

        Bra.Fil("Branch", "Bran_Name", cmbBranch_Find)
        Bra.Fil("Geographic_Area", "GeoArea_Name", cmbGeoArea_Find)
        Bra.Fil("EmployeesTargetDetective", "TargetNumber", cmbTargetDetective)

        MAXRECORD("Government", "Gov_Code")
        txtGovernment_Code.Text = MaxRecoedCode

        MAXRECORD("Branch", "Bran_code")
        txtBranch_Code.Text = MaxRecoedCode

        MAXRECORD("Geographic_Area", "GeoArea_Code")
        txtGeoArea_Code.Text = MaxRecoedCode

        Fill_Cust_Branch_Inner()
        Fill_CustEmp_Outter()
        Fill_EmpGeoArea_Inner()
        Fill_EmpBranch_Outter()
        Fill_Delegate()
        Fill_DelegateBranch()
        Fill_CustEmpOut()
        Fill_TargetDetective()

        Dim TypeTargetRateAll As String = mykey.GetValue("TypeTargetRateAll", "RatioAll")
        If TypeTargetRateAll = "RatioAll" Then
            rdoRatioAll.Checked = True
        End If
        If TypeTargetRateAll = "TargetRate" Then
            rdoTargetRate.Checked = True
        End If
        If TypeTargetRateAll = "RatioPerEmployee" Then
            rdoRatioPerEmployee.Checked = True
        End If
    End Sub

#End Region

#Region "Government"

    Private Sub btnGovSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnGovSave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If Trim(txtGovernment.Text) = "" Then MsgBox("من فضلك ادخل اسم المحافظة", MsgBoxStyle.Exclamation) : Exit Sub
        If Trim(txtGovernment_Code.Text) = "" Then MsgBox("من فضلك ادخل كود المحافظة", MsgBoxStyle.Exclamation) : Exit Sub
        If Cls.Check_Field_Value("Government", "Gov_Name", txtGovernment.Text) Then MsgBox("عفوا بيان مسجل من قبل", MsgBoxStyle.Exclamation) : Exit Sub

        Cls.insert("Government", "Gov_Code,Gov_Name", "N'" & txtGovernment_Code.Text & "',N'" & txtGovernment.Text & "'")
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)

        txtGovernment.Text = ""
        txtGovernment_Code.Text = ""

        Bra.Fil("Government", "Gov_Name", cmbGovFind)
        Bra.Fil("Government", "Gov_Name", cmbGov_Branch)
        Bra.Fil("Government", "Gov_Name", cmbGov_GeoArea)
        Bra.Fil("Government", "Gov_Name", cmbGovDelegateBranch)
        Bra.Fil("Government", "Gov_Name", cmbGeoEmp_Gov)
        Bra.Fil("Government", "Gov_Name", cmbEmpCust_Gov)

        MAXRECORD("Government", "Gov_Code")
        txtGovernment_Code.Text = MaxRecoedCode
        txtGovernment.Focus()
    End Sub

    Private Sub btnGovEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnGov_Update.Click
        If Trim(txtGovernment.Text) = "" Then MsgBox("من فضلك ادخل اسم المحافظة", MsgBoxStyle.Exclamation) : Exit Sub
        If Trim(txtGovernment_Code.Text) = "" Then MsgBox("من فضلك ادخل كود المحافظة", MsgBoxStyle.Exclamation) : Exit Sub
        If Cls.Check_Field_Value("Government", "Gov_Name", txtGovernment.Text) Then MsgBox("عفوا بيان مسجل من قبل", MsgBoxStyle.Exclamation) : Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Government set Gov_Name =N'" & txtGovernment.Text & "' where Gov_Code =N'" & txtGovernment_Code.Text & "'" : cmd.ExecuteNonQuery()

        MsgBox("تم التعديل بنجاح", MsgBoxStyle.Information)

        txtGovernment.Text = ""
        txtGovernment_Code.Text = ""

        Bra.Fil("Government", "Gov_Name", cmbGovFind)
        Bra.Fil("Government", "Gov_Name", cmbGov_Branch)
        Bra.Fil("Government", "Gov_Name", cmbGov_GeoArea)
        Bra.Fil("Government", "Gov_Name", cmbGovDelegateBranch)
        Bra.Fil("Government", "Gov_Name", cmbGeoEmp_Gov)
        Bra.Fil("Government", "Gov_Name", cmbEmpCust_Gov)

        MAXRECORD("Government", "Gov_Code")
        txtGovernment_Code.Text = MaxRecoedCode
        txtGovernment.Focus()
    End Sub

    Private Sub btnGovDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnGovDelete.Click
        If cmbGovFind.SelectedIndex = -1 Then MsgBox("من فضلك اختر بيان صحيح", MsgBoxStyle.Exclamation) : Exit Sub

        Dim msg As New MsgBoxResult
        msg = (MsgBox("هل تريد بالفعل الحذف", MsgBoxStyle.Question + MsgBoxStyle.YesNo))
        If msg = MsgBoxResult.Yes Then
            Cls.delete("Government", "Gov_Name=N'" & cmbGovFind.Text & "'")
            MsgBox("تم الحذف بنجاح", MsgBoxStyle.Information)
            Bra.Fil("Government", "Gov_Name", cmbGovFind)
            Bra.Fil("Government", "Gov_Name", cmbGov_Branch)
            Bra.Fil("Government", "Gov_Name", cmbGov_GeoArea)
            Bra.Fil("Government", "Gov_Name", cmbGovDelegateBranch)
            Bra.Fil("Government", "Gov_Name", cmbGeoEmp_Gov)
            Bra.Fil("Government", "Gov_Name", cmbEmpCust_Gov)
        End If
        txtGovernment.Text = ""
        txtGovernment_Code.Text = ""

        MAXRECORD("Government", "Gov_Code")
        txtGovernment_Code.Text = MaxRecoedCode

        txtGovernment.Focus()
    End Sub

    Private Sub cmbGovFind_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbGovFind.SelectedIndexChanged
        If cmbGovFind.Text = "" Then Exit Sub
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * from Government where Gov_Name=N'" & cmbGovFind.Text & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            txtGovernment_Code.Text = dr("Gov_Code")
            txtGovernment.Text = dr("Gov_Name")
        End If
    End Sub

    Private Sub txtGovernorCode_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtGovernment_Code.KeyUp
        If e.KeyCode = 13 Then
            txtGovernment.Focus()
        End If
    End Sub

    Private Sub txtGovernor_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtGovernment.KeyUp
        If e.KeyCode = 13 Then
            btnGovSave.PerformClick()
        End If
    End Sub

#End Region

#Region "Branch"
    Private Sub txtBranch_Code_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtBranch_Code.KeyUp
        If e.KeyCode = 13 Then
            txtBranch_Name.Focus()
        End If
    End Sub

    Private Sub txtBranch_Name_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtBranch_Name.KeyUp
        If e.KeyCode = 13 Then
            txtBranch_Address.Focus()
        End If
    End Sub

    Private Sub txtBranch_Address_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtBranch_Address.KeyUp
        If e.KeyCode = 13 Then
            txtBranch_Phone.Focus()
        End If
    End Sub

    Private Sub txtBranch_Phone_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtBranch_Phone.KeyUp
        If e.KeyCode = 13 Then
            btnBranch_Save.PerformClick()
        End If
    End Sub

    Private Sub btnBranch_Save_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBranch_Save.Click
        If Trim(txtBranch_Name.Text) = "" Then MsgBox("من فضلك ادخل اسم الفرع", MsgBoxStyle.Exclamation) : Exit Sub
        If Trim(txtBranch_Code.Text) = "" Then MsgBox("من فضلك ادخل كود الفرع", MsgBoxStyle.Exclamation) : Exit Sub
        If Cls.Check_Field_Value("Branch", "Bran_Name", txtBranch_Name.Text) Then MsgBox("عفوا بيان مسجل من قبل", MsgBoxStyle.Exclamation) : Exit Sub
        Chack_Code("Government", "Gov_Code", "Gov_Name", cmbGov_Branch.Text)

        Cls.insert("Branch", "Bran_code,Bran_Name,Government_Code,Bran_Address,Telephone", "N'" & txtBranch_Code.Text & "',N'" & txtBranch_Name.Text & "',N'" & Result_Code & "',N'" & txtBranch_Address.Text & "',N'" & txtBranch_Phone.Text & "'")
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)

        txtBranch_Name.Text = ""
        txtBranch_Code.Text = ""
        txtBranch_Address.Text = ""
        txtBranch_Phone.Text = ""

        Bra.Fil("Branch", "Bran_Name", cmbBranch_Find)
        MAXRECORD("Branch", "Bran_code")
        txtBranch_Code.Text = MaxRecoedCode
        txtBranch_Name.Focus()
    End Sub

    Private Sub btnBranch_Update_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBranch_Update.Click
        If Trim(txtBranch_Name.Text) = "" Then MsgBox("من فضلك ادخل اسم الفرع", MsgBoxStyle.Exclamation) : Exit Sub
        If Trim(txtBranch_Code.Text) = "" Then MsgBox("من فضلك ادخل كود الفرع", MsgBoxStyle.Exclamation) : Exit Sub

        Chack_Code("Government", "Gov_Code", "Gov_Name", cmbGov_Branch.Text)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Branch set Bran_Name =N'" & txtBranch_Name.Text & "',Bran_Address =N'" & txtBranch_Address.Text & "',Telephone =N'" & txtBranch_Phone.Text & "',Government_Code =N'" & Result_Code & "' where Bran_code =N'" & txtBranch_Code.Text & "'" : cmd.ExecuteNonQuery()

        MsgBox("تم التعديل بنجاح", MsgBoxStyle.Information)

        txtBranch_Name.Text = ""
        txtBranch_Code.Text = ""
        txtBranch_Address.Text = ""
        txtBranch_Phone.Text = ""

        Bra.Fil("Branch", "Bran_Name", cmbBranch_Find)

        MAXRECORD("Branch", "Bran_code")
        txtBranch_Code.Text = MaxRecoedCode
        txtBranch_Name.Focus()
    End Sub

    Private Sub btnBranch_Delete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBranch_Delete.Click
        If cmbBranch_Find.SelectedIndex = -1 Then MsgBox("من فضلك اختر بيان صحيح", MsgBoxStyle.Exclamation) : Exit Sub

        Dim msg As New MsgBoxResult
        msg = (MsgBox("هل تريد بالفعل الحذف", MsgBoxStyle.Question + MsgBoxStyle.YesNo))
        If msg = MsgBoxResult.Yes Then
            Cls.delete("Branch", "Bran_Name=N'" & cmbBranch_Find.Text & "'")
            MsgBox("تم الحذف بنجاح", MsgBoxStyle.Information)
            Bra.Fil("Branch", "Bran_Name", cmbBranch_Find)
        End If

        txtBranch_Name.Text = ""
        txtBranch_Code.Text = ""
        txtBranch_Address.Text = ""
        txtBranch_Phone.Text = ""

        MAXRECORD("Branch", "Bran_code")
        txtBranch_Code.Text = MaxRecoedCode

        txtBranch_Name.Focus()
    End Sub

    Private Sub cmbBranch_Find_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbBranch_Find.SelectedIndexChanged
        If cmbBranch_Find.Text = "" Then Exit Sub
        Dim Government_Code As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * from Branch where Bran_Name=N'" & cmbBranch_Find.Text & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            txtBranch_Code.Text = dr("Bran_code")
            txtBranch_Name.Text = dr("Bran_Name").ToString()
            txtBranch_Address.Text = dr("Bran_Address").ToString()
            txtBranch_Phone.Text = dr("Telephone").ToString()
            Government_Code = dr("Government_Code")
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Gov_Name from Government where Gov_Code=N'" & Government_Code & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            cmbGov_Branch.Text = dr("Gov_Name")
        End If
    End Sub

#End Region

#Region "Geographic Area"

    Private Sub cmbGov_GeoArea_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbGov_GeoArea.SelectedIndexChanged
        cmbBranch_GeoArea.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT dbo.Government.Gov_Name, dbo.Branch.Bran_Name FROM dbo.Government INNER JOIN dbo.Branch ON dbo.Government.Gov_Code = dbo.Branch.Government_Code WHERE (dbo.Government.Gov_Name = N'" & cmbGov_GeoArea.Text & "')"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbBranch_GeoArea.Items.Add(Trim(dr("Bran_Name")))
        Loop
    End Sub

    Private Sub cmbGov_GeoArea_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbGov_GeoArea.KeyUp
        If e.KeyCode = 13 Then
            cmbBranch_GeoArea.Focus()
        End If
    End Sub

    Private Sub cmbBranch_GeoArea_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbBranch_GeoArea.KeyUp
        If e.KeyCode = 13 Then
            txtGeoArea_Code.Focus()
        End If
    End Sub

    Private Sub txtGeoArea_Code_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtGeoArea_Code.KeyUp
        If e.KeyCode = 13 Then
            txtGeoArea_Name.Focus()
        End If
    End Sub

    Private Sub txtGeoArea_Name_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtGeoArea_Name.KeyUp
        If e.KeyCode = 13 Then
            btnGeoArea_Save.PerformClick()
        End If
    End Sub

    Private Sub btnGeoArea_Save_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnGeoArea_Save.Click
        If Trim(txtGeoArea_Name.Text) = "" Then MsgBox("من فضلك ادخل اسم المنطقة", MsgBoxStyle.Exclamation) : Exit Sub
        If Trim(txtGeoArea_Code.Text) = "" Then MsgBox("من فضلك ادخل كود المنطقة", MsgBoxStyle.Exclamation) : Exit Sub
        If Cls.Check_Field_Value("Geographic_Area", "GeoArea_Code", txtGeoArea_Code.Text) Then MsgBox("عفوا الكود مسجل من قبل", MsgBoxStyle.Exclamation) : Exit Sub
        If Cls.Check_Field_Value("Geographic_Area", "GeoArea_Name", txtGeoArea_Name.Text) Then MsgBox("عفوا بيان مسجل من قبل", MsgBoxStyle.Exclamation) : Exit Sub
        Chack_Code("Branch", "Bran_code", "Bran_Name", cmbBranch_GeoArea.Text)

        Cls.insert("Geographic_Area", "GeoArea_Code,GeoArea_Name,Branch_Code", "N'" & txtGeoArea_Code.Text & "',N'" & txtGeoArea_Name.Text & "',N'" & Result_Code & "'")
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)

        txtGeoArea_Name.Text = ""
        txtGeoArea_Code.Text = ""
        cmbGov_GeoArea.Text = ""
        cmbBranch_GeoArea.Text = ""

        Bra.Fil("Geographic_Area", "GeoArea_Name", cmbGeoArea_Find)
        MAXRECORD("Geographic_Area", "GeoArea_Code")
        txtGeoArea_Code.Text = MaxRecoedCode
        txtGeoArea_Name.Focus()
    End Sub

    Private Sub btnGeoArea_Update_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnGeoArea_Update.Click
        If Trim(txtGeoArea_Name.Text) = "" Then MsgBox("من فضلك ادخل اسم المنطقة", MsgBoxStyle.Exclamation) : Exit Sub
        If Trim(txtGeoArea_Code.Text) = "" Then MsgBox("من فضلك ادخل كود المنطقة", MsgBoxStyle.Exclamation) : Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Geographic_Area set GeoArea_Name =N'" & txtGeoArea_Name.Text & "' where GeoArea_Code =N'" & txtGeoArea_Code.Text & "'" : cmd.ExecuteNonQuery()

        MsgBox("تم التعديل بنجاح", MsgBoxStyle.Information)

        txtGeoArea_Name.Text = ""
        txtGeoArea_Code.Text = ""
        cmbGov_GeoArea.Text = ""
        cmbBranch_GeoArea.Text = ""

        Bra.Fil("Geographic_Area", "GeoArea_Name", cmbGeoArea_Find)

        MAXRECORD("Geographic_Area", "GeoArea_Code")
        txtGeoArea_Code.Text = MaxRecoedCode
        txtBranch_Name.Focus()
    End Sub

    Private Sub btnGeoArea_Delete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnGeoArea_Delete.Click
        If cmbGeoArea_Find.SelectedIndex = -1 Then MsgBox("من فضلك اختر بيان صحيح", MsgBoxStyle.Exclamation) : Exit Sub

        Dim msg As New MsgBoxResult
        msg = (MsgBox("هل تريد بالفعل الحذف", MsgBoxStyle.Question + MsgBoxStyle.YesNo))
        If msg = MsgBoxResult.Yes Then
            Cls.delete("Geographic_Area", "GeoArea_Name=N'" & cmbGeoArea_Find.Text & "'")
            MsgBox("تم الحذف بنجاح", MsgBoxStyle.Information)
            Bra.Fil("Geographic_Area", "GeoArea_Name", cmbGeoArea_Find)
        End If

        txtGeoArea_Name.Text = ""
        txtGeoArea_Code.Text = ""
        cmbGov_GeoArea.Text = ""
        cmbBranch_GeoArea.Text = ""

        MAXRECORD("Geographic_Area", "GeoArea_Code")
        txtGeoArea_Code.Text = MaxRecoedCode

        txtGeoArea_Name.Focus()
    End Sub

    Private Sub cmbGeoArea_Find_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbGeoArea_Find.SelectedIndexChanged
        If cmbGeoArea_Find.Text = "" Then Exit Sub
        Dim Government_Code As String = ""
        Dim Branch_Code As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * from Geographic_Area where GeoArea_Name=N'" & cmbGeoArea_Find.Text & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            txtGeoArea_Code.Text = dr("GeoArea_Code")
            txtGeoArea_Name.Text = dr("GeoArea_Name").ToString()
            Branch_Code = dr("Branch_Code")
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Bran_Name,Government_Code from Branch where Bran_code=N'" & Branch_Code & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            cmbBranch_GeoArea.Text = dr("Bran_Name")
            Government_Code = dr("Government_Code")
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Gov_Name from Government where Gov_Code=N'" & Government_Code & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            cmbGov_GeoArea.Text = dr("Gov_Name")
        End If
    End Sub


#End Region

#Region "Employee Customers"

    Private Sub cmbEmpCust_Gov_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbEmpCust_Gov.SelectedIndexChanged
        cmbEmpCust_Branch.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT dbo.Government.Gov_Name, dbo.Branch.Bran_Name FROM dbo.Government INNER JOIN dbo.Branch ON dbo.Government.Gov_Code = dbo.Branch.Government_Code WHERE (dbo.Government.Gov_Name = N'" & cmbEmpCust_Gov.Text & "')"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbEmpCust_Branch.Items.Add(Trim(dr("Bran_Name")))
        Loop
    End Sub

    Private Sub cmbEmpCust_Branch_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbEmpCust_Branch.SelectedIndexChanged

        Fill_Cust_Branch_Inner()
        Fill_CustEmp_Outter()

        cmbEmpCust_Employee.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT dbo.Branch.Bran_Name, dbo.Employees.NameEmployee FROM dbo.Branch INNER JOIN dbo.Employees ON dbo.Branch.Bran_code = dbo.Employees.Bran_code WHERE (dbo.Branch.Bran_Name =N'" & cmbEmpCust_Branch.Text & "')"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbEmpCust_Employee.Items.Add(Trim(dr("NameEmployee")))
        Loop
    End Sub

    Private Sub Fill_Cust_Branch_Inner()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "Select Top(100) PERCENT dbo.Customers.Cust_Code, dbo.Customers.Vendorname, dbo.Branch.Bran_Name, dbo.Customers.Emp_Code  From dbo.Branch INNER Join   dbo.Geographic_Area ON dbo.Branch.Bran_code = dbo.Geographic_Area.Branch_Code RIGHT OUTER Join  dbo.Customers ON dbo.Geographic_Area.GeoArea_Code = dbo.Customers.GeoArea_Code  Group By dbo.Customers.Cust_Code, dbo.Customers.Vendorname, dbo.Branch.Bran_Name, dbo.Customers.Emp_Code  HAVING(dbo.Branch.Bran_Name = N'" & cmbEmpCust_Branch.Text & "') AND (dbo.Customers.Emp_Code = 0)  ORDER BY dbo.Customers.Vendorname"
        dr = cmd.ExecuteReader
        ListBox_CustBranch.Items.Clear()
        Do While dr.Read

            ListBox_CustBranch.Items.Add(dr("Vendorname"))

        Loop
        lblCountCustBranch.Text = ListBox_CustBranch.Items.Count
    End Sub

    Private Sub Fill_CustEmp_Outter()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "Select Top(100) PERCENT dbo.Customers.Cust_Code, dbo.Customers.Vendorname, dbo.Branch.Bran_Name, dbo.Employees.NameEmployee  From dbo.Employees INNER Join  dbo.Branch INNER Join  dbo.Geographic_Area ON dbo.Branch.Bran_code = dbo.Geographic_Area.Branch_Code INNER Join dbo.Customers ON dbo.Geographic_Area.GeoArea_Code = dbo.Customers.GeoArea_Code ON dbo.Employees.EMPID = dbo.Customers.Emp_Code  Group By dbo.Customers.Cust_Code, dbo.Customers.Vendorname, dbo.Branch.Bran_Name, dbo.Employees.NameEmployee  HAVING(dbo.Branch.Bran_Name = N'" & cmbEmpCust_Branch.Text & "') AND (dbo.Employees.NameEmployee =  N'" & cmbEmpCust_Employee.Text & "')  ORDER BY dbo.Customers.Vendorname"
        dr = cmd.ExecuteReader
        ListBox_CustEmp.Items.Clear()
        Do While dr.Read
            ListBox_CustEmp.Items.Add(dr("Vendorname"))
        Loop
        lblCountCustEmp.Text = ListBox_CustEmp.Items.Count
    End Sub

    Private Sub cmbEmpCust_Employee_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbEmpCust_Employee.SelectedIndexChanged
        Fill_CustEmp_Outter()
    End Sub

    Private Sub btnCustEmp_Next_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCustEmp_Next.Click
        If cmbEmpCust_Gov.Text = "" Then MsgBox("من فضلك أختر المحافظة", MsgBoxStyle.Exclamation) : cmbEmpCust_Gov.Focus() : Exit Sub
        If cmbEmpCust_Branch.Text = "" Then MsgBox("من فضلك اختر الفرع", MsgBoxStyle.Exclamation) : cmbEmpCust_Branch.Focus() : Exit Sub
        If cmbEmpCust_Employee.Text = "" Then MsgBox("من فضلك اختر المندوب", MsgBoxStyle.Exclamation) : cmbEmpCust_Employee.Focus() : Exit Sub


        Dim Employee_Code, Customers_Code As String

        Chack_Code("Employees", "EMPID", "NameEmployee", cmbEmpCust_Employee.Text)
        Employee_Code = Result_Code
        Chack_Code("Customers", "Cust_Code", "Vendorname", ListBox_CustBranch.Text)
        Customers_Code = Result_Code

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Employee_Customers(Employee_Code,Customers_Code) values (N'" & Employee_Code & "',N'" & Customers_Code & "')"
        cmd.CommandText = S
        cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Customers set Emp_Code =N'" & Employee_Code & "' where Vendorname =N'" & ListBox_CustBranch.Text & "'" : cmd.ExecuteNonQuery()


        Fill_Cust_Branch_Inner()
        Fill_CustEmp_Outter()
        Fill_EmpGeoArea_Inner()
        Fill_EmpBranch_Outter()
        Fill_Delegate()
        Fill_DelegateBranch()
        Fill_CustEmpOut()
    End Sub

    Private Sub btnCustEmp_Previous_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCustEmp_Previous.Click
        If cmbEmpCust_Gov.Text = "" Then MsgBox("من فضلك أختر المحافظة", MsgBoxStyle.Exclamation) : cmbEmpCust_Gov.Focus() : Exit Sub
        If cmbEmpCust_Branch.Text = "" Then MsgBox("من فضلك اختر الفرع", MsgBoxStyle.Exclamation) : cmbEmpCust_Branch.Focus() : Exit Sub
        If cmbEmpCust_Employee.Text = "" Then MsgBox("من فضلك اختر المندوب", MsgBoxStyle.Exclamation) : cmbEmpCust_Employee.Focus() : Exit Sub


        Dim Employee_Code, Customers_Code As String

        Chack_Code("Employees", "EMPID", "NameEmployee", cmbEmpCust_Employee.Text)
        Employee_Code = Result_Code
        Chack_Code("Customers", "Cust_Code", "Vendorname", ListBox_CustEmp.Text)
        Customers_Code = Result_Code

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete from Employee_Customers where Employee_Code =N'" & Employee_Code & "' and Customers_Code=N'" & Customers_Code & "'"
        cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Customers set Emp_Code = 0 where Vendorname =N'" & ListBox_CustEmp.Text & "'" : cmd.ExecuteNonQuery()


        Fill_Cust_Branch_Inner()
        Fill_CustEmp_Outter()
        Fill_EmpGeoArea_Inner()
        Fill_EmpBranch_Outter()
        Fill_Delegate()
        Fill_DelegateBranch()
        Fill_CustEmpOut()
    End Sub

    Private Sub Fill_CustEmpOut()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "SELECT Vendorname from Customers where GeoArea_Code = 0"
        dr = cmd.ExecuteReader
        ListBoxCustEmpOut.Items.Clear()
        Do While dr.Read
            ListBoxCustEmpOut.Items.Add(dr("Vendorname"))
        Loop
        lblCountCustEmpOut.Text = ListBoxCustEmpOut.Items.Count
    End Sub

    Private Sub btnCustEmpOutNext_Click(sender As Object, e As EventArgs) Handles btnCustEmpOutNext.Click
        If cmbEmpCust_Gov.Text = "" Then MsgBox("من فضلك أختر المحافظة", MsgBoxStyle.Exclamation) : cmbEmpCust_Gov.Focus() : Exit Sub
        If cmbEmpCust_Branch.Text = "" Then MsgBox("من فضلك اختر الفرع", MsgBoxStyle.Exclamation) : cmbEmpCust_Branch.Focus() : Exit Sub
        If cmbEmpCust_Employee.Text = "" Then MsgBox("من فضلك اختر المندوب", MsgBoxStyle.Exclamation) : cmbEmpCust_Employee.Focus() : Exit Sub

        Chack_Code("Branch", "Bran_code", "Bran_Name", cmbEmpCust_Branch.Text)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Customers set GeoArea_Code =N'" & Result_Code & "' where Vendorname =N'" & ListBoxCustEmpOut.Text & "'" : cmd.ExecuteNonQuery()

        Fill_Cust_Branch_Inner()
        Fill_CustEmp_Outter()
        Fill_EmpGeoArea_Inner()
        Fill_EmpBranch_Outter()
        Fill_Delegate()
        Fill_DelegateBranch()
        Fill_CustEmpOut()
    End Sub

    Private Sub btnCustEmpOutPrevious_Click(sender As Object, e As EventArgs) Handles btnCustEmpOutPrevious.Click
        If cmbEmpCust_Gov.Text = "" Then MsgBox("من فضلك أختر المحافظة", MsgBoxStyle.Exclamation) : cmbEmpCust_Gov.Focus() : Exit Sub
        If cmbEmpCust_Branch.Text = "" Then MsgBox("من فضلك اختر الفرع", MsgBoxStyle.Exclamation) : cmbEmpCust_Branch.Focus() : Exit Sub
        If cmbEmpCust_Employee.Text = "" Then MsgBox("من فضلك اختر المندوب", MsgBoxStyle.Exclamation) : cmbEmpCust_Employee.Focus() : Exit Sub

        Chack_Code("Branch", "Bran_code", "Bran_Name", cmbEmpCust_Branch.Text)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Customers set GeoArea_Code = 0 where Vendorname =N'" & ListBox_CustBranch.Text & "'" : cmd.ExecuteNonQuery()


        Fill_Cust_Branch_Inner()
        Fill_CustEmp_Outter()
        Fill_EmpGeoArea_Inner()
        Fill_EmpBranch_Outter()
        Fill_Delegate()
        Fill_DelegateBranch()
        Fill_CustEmpOut()
    End Sub

#End Region

#Region "Geographic Area Employee"

    Private Sub cmbGeoEmp_Gov_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbGeoEmp_Gov.SelectedIndexChanged

        cmbGeoEmp_Branch.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT dbo.Government.Gov_Name, dbo.Branch.Bran_Name FROM dbo.Government INNER JOIN dbo.Branch ON dbo.Government.Gov_Code = dbo.Branch.Government_Code WHERE (dbo.Government.Gov_Name = N'" & cmbGeoEmp_Gov.Text & "')"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbGeoEmp_Branch.Items.Add(Trim(dr("Bran_Name")))
        Loop
    End Sub

    Private Sub cmbGeoEmp_Branch_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbGeoEmp_Branch.SelectedIndexChanged
        Fill_EmpBranch_Outter()
        Fill_EmpGeoArea_Inner()
        cmbGeoEmp_GeoArea.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT dbo.Branch.Bran_Name, dbo.Geographic_Area.GeoArea_Name FROM dbo.Branch INNER JOIN dbo.Geographic_Area ON dbo.Branch.Bran_code = dbo.Geographic_Area.Branch_Code WHERE (dbo.Branch.Bran_Name = N'" & cmbGeoEmp_Branch.Text & "')"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbGeoEmp_GeoArea.Items.Add(Trim(dr("GeoArea_Name")))
        Loop
    End Sub

    Private Sub Fill_EmpGeoArea_Inner()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "SELECT dbo.Employees.NameEmployee, dbo.Geographic_Area.GeoArea_Code, dbo.Geographic_Area.GeoArea_Name, dbo.Employees.Emp_Status_Code,dbo.Employees.Emp_Type_Code FROM dbo.Employees INNER JOIN " +
            "dbo.Geographic_Area_Employee ON dbo.Employees.EMPID = dbo.Geographic_Area_Employee.Employee_Code INNER JOIN dbo.Geographic_Area ON dbo.Geographic_Area_Employee.Geographic_Area_Code = dbo.Geographic_Area.GeoArea_Code " +
            "WHERE (dbo.Employees.Emp_Status_Code = 1) AND (dbo.Employees.Emp_Type_Code = 5) AND (dbo.Geographic_Area.GeoArea_Name = N'" & cmbGeoEmp_GeoArea.Text & "')"
        dr = cmd.ExecuteReader
        ListBoxEmpGeoArea.Items.Clear()
        Do While dr.Read

            ListBoxEmpGeoArea.Items.Add(dr("NameEmployee"))

        Loop
        lblCountEmpGeoArea.Text = ListBoxEmpGeoArea.Items.Count
    End Sub

    Private Sub Fill_EmpBranch_Outter()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "SELECT dbo.Employees.EMPID, dbo.Employees.NameEmployee, dbo.Employees.Emp_Status_Code, dbo.Employees.Emp_Type_Code, dbo.Branch.Bran_Name FROM  dbo.Geographic_Area_Employee RIGHT OUTER JOIN dbo.Branch INNER JOIN " +
        "dbo.Employees ON dbo.Branch.Bran_code = dbo.Employees.Bran_code ON dbo.Geographic_Area_Employee.Employee_Code = dbo.Employees.EMPID " +
        "WHERE (dbo.Employees.Emp_Status_Code = 1) AND (dbo.Employees.Emp_Type_Code = 5) AND (dbo.Branch.Bran_Name = N'" & cmbGeoEmp_Branch.Text & "') AND (NOT (dbo.Employees.EMPID IN (SELECT Employee_Code FROM dbo.Geographic_Area_Employee AS Geographic_Area_Employee_1)))"
        dr = cmd.ExecuteReader
        ListBoxEmpBranch.Items.Clear()
        Do While dr.Read
            ListBoxEmpBranch.Items.Add(dr("NameEmployee"))
        Loop
        lblCountEmpBranch.Text = ListBoxEmpBranch.Items.Count
    End Sub

    Private Sub cmbGeoEmp_GeoArea_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbGeoEmp_GeoArea.SelectedIndexChanged
        Fill_EmpGeoArea_Inner()
    End Sub

    Private Sub btnGeoEmp_Next_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnGeoEmp_Next.Click
        If cmbGeoEmp_Gov.Text = "" Then MsgBox("من فضلك أختر المحافظة", MsgBoxStyle.Exclamation) : cmbGeoEmp_Gov.Focus() : Exit Sub
        If cmbGeoEmp_Branch.Text = "" Then MsgBox("من فضلك اختر الفرع", MsgBoxStyle.Exclamation) : cmbGeoEmp_Branch.Focus() : Exit Sub
        If cmbGeoEmp_GeoArea.Text = "" Then MsgBox("من فضلك اختر المنطقة", MsgBoxStyle.Exclamation) : cmbGeoEmp_GeoArea.Focus() : Exit Sub

        Dim Employee_Code, GeoArea_Code As String

        Chack_Code("Employees", "EMPID", "NameEmployee", ListBoxEmpBranch.Text)
        Employee_Code = Result_Code
        Chack_Code("Geographic_Area", "GeoArea_Code", "GeoArea_Name", cmbGeoEmp_GeoArea.Text)
        GeoArea_Code = Result_Code

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Geographic_Area_Employee(Employee_Code,Geographic_Area_Code) values (N'" & Employee_Code & "',N'" & GeoArea_Code & "')"
        cmd.CommandText = S
        cmd.ExecuteNonQuery()

        Fill_Cust_Branch_Inner()
        Fill_CustEmp_Outter()
        Fill_EmpGeoArea_Inner()
        Fill_EmpBranch_Outter()
        Fill_Delegate()
        Fill_DelegateBranch()
        Fill_CustEmpOut()
    End Sub

    Private Sub btnGeopEmp_Previous_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnGeopEmp_Previous.Click
        If cmbGeoEmp_Gov.Text = "" Then MsgBox("من فضلك أختر المحافظة", MsgBoxStyle.Exclamation) : cmbGeoEmp_Gov.Focus() : Exit Sub
        If cmbGeoEmp_Branch.Text = "" Then MsgBox("من فضلك اختر الفرع", MsgBoxStyle.Exclamation) : cmbGeoEmp_Branch.Focus() : Exit Sub
        If cmbGeoEmp_GeoArea.Text = "" Then MsgBox("من فضلك اختر المنطقة", MsgBoxStyle.Exclamation) : cmbGeoEmp_GeoArea.Focus() : Exit Sub

        Dim Employee_Code, GeoArea_Code As String

        Chack_Code("Employees", "EMPID", "NameEmployee", ListBoxEmpGeoArea.Text)
        Employee_Code = Result_Code
        Chack_Code("Geographic_Area", "GeoArea_Code", "GeoArea_Name", cmbGeoEmp_GeoArea.Text)
        GeoArea_Code = Result_Code

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete from Geographic_Area_Employee where Employee_Code =N'" & Employee_Code & "' and Geographic_Area_Code=N'" & GeoArea_Code & "'"
        cmd.ExecuteNonQuery()

        Fill_Cust_Branch_Inner()
        Fill_CustEmp_Outter()
        Fill_EmpGeoArea_Inner()
        Fill_EmpBranch_Outter()
        Fill_Delegate()
        Fill_DelegateBranch()
        Fill_CustEmpOut()

    End Sub
#End Region

#Region "Methode"

    Private Sub MAXRECORD(ByVal Tables As String, ByVal Code As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " & Tables & ""
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            MaxRecoedCode = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" & Code & " As float)) as mb FROM " & Tables & " where " & Code & " <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            MaxRecoedCode = sh + 1
        End If

    End Sub

    Private Sub Chack_Code(ByVal Table As String, ByVal Code As String, ByVal Name As String, ByVal TextBox As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select " & Code & " from " & Table & " where " & Name & "=N'" & TextBox & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
            Result_Code = 0
        Else
            Result_Code = dr(0)
        End If
    End Sub

#End Region

#Region "Delegate Branch"

    Private Sub Fill_Delegate()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "SELECT NameEmployee from Employees where Bran_code = 0"
        dr = cmd.ExecuteReader
        ListBoxDelegate.Items.Clear()
        Do While dr.Read
            ListBoxDelegate.Items.Add(dr("NameEmployee"))
        Loop
        lblCountDelegate.Text = ListBoxDelegate.Items.Count
    End Sub

    Private Sub Fill_DelegateBranch()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "SELECT NameEmployee from Employees where Bran_code <> 0"
        dr = cmd.ExecuteReader
        ListBoxDelegateBranch.Items.Clear()
        Do While dr.Read
            ListBoxDelegateBranch.Items.Add(dr("NameEmployee"))
        Loop
        lblCountDelegateBranch.Text = ListBoxDelegateBranch.Items.Count
    End Sub

    Private Sub cmbGovDelegateBranch_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbGovDelegateBranch.SelectedIndexChanged
        cmbBranchDelegate.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT dbo.Government.Gov_Name, dbo.Branch.Bran_Name FROM dbo.Government INNER JOIN dbo.Branch ON dbo.Government.Gov_Code = dbo.Branch.Government_Code WHERE (dbo.Government.Gov_Name = N'" & cmbGovDelegateBranch.Text & "')"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbBranchDelegate.Items.Add(Trim(dr("Bran_Name")))
        Loop
    End Sub

    Private Sub btnDelegateBranchNext_Click(sender As Object, e As EventArgs) Handles btnDelegateBranchNext.Click
        If cmbGovDelegateBranch.Text = "" Then MsgBox("من فضلك أختر المحافظة", MsgBoxStyle.Exclamation) : cmbGovDelegateBranch.Focus() : Exit Sub
        If cmbBranchDelegate.Text = "" Then MsgBox("من فضلك اختر الفرع", MsgBoxStyle.Exclamation) : cmbBranchDelegate.Focus() : Exit Sub

        Chack_Code("Branch", "Bran_code", "Bran_Name", cmbBranchDelegate.Text)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Employees set Bran_code =N'" & Result_Code & "',Emp_Type_Code = 5,Emp_Status_Code = 1 where NameEmployee =N'" & ListBoxDelegate.Text & "'" : cmd.ExecuteNonQuery()

        Chack_Code("Employees", "EMPID", "NameEmployee", ListBoxDelegate.Text)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into EmployeesAccount (Emp_Code,NameEmployee,vintinval,TotalAmount,UserName)  values("
        S = S & "N'" & Result_Code & "',N'" & ListBoxDelegate.Text & "',0,0,N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        Fill_Cust_Branch_Inner()
        Fill_CustEmp_Outter()
        Fill_EmpGeoArea_Inner()
        Fill_EmpBranch_Outter()
        Fill_Delegate()
        Fill_DelegateBranch()
        Fill_CustEmpOut()
    End Sub

    Private Sub btnDelegateBranchPrevious_Click(sender As Object, e As EventArgs) Handles btnDelegateBranchPrevious.Click
        If cmbGovDelegateBranch.Text = "" Then MsgBox("من فضلك أختر المحافظة", MsgBoxStyle.Exclamation) : cmbGovDelegateBranch.Focus() : Exit Sub
        If cmbBranchDelegate.Text = "" Then MsgBox("من فضلك اختر الفرع", MsgBoxStyle.Exclamation) : cmbBranchDelegate.Focus() : Exit Sub

        Chack_Code("Branch", "Bran_code", "Bran_Name", cmbBranchDelegate.Text)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Employees set Bran_code = 0 where NameEmployee =N'" & ListBoxDelegateBranch.Text & "'" : cmd.ExecuteNonQuery()

        Fill_Cust_Branch_Inner()
        Fill_CustEmp_Outter()
        Fill_EmpGeoArea_Inner()
        Fill_EmpBranch_Outter()
        Fill_Delegate()
        Fill_DelegateBranch()
        Fill_CustEmpOut()
    End Sub






#End Region

#Region "Target Detective"

    Private Sub btnSaveTargetDetective_Click(sender As Object, e As EventArgs) Handles btnSaveTargetDetective.Click
        If Trim(txtTargetNumber.Text) = "" Then MsgBox("من فضلك ادخل الهدف المحقق", MsgBoxStyle.Exclamation) : Exit Sub
        If Trim(txtTargetIncentive.Text) = "" Then MsgBox("من فضلك ادخل حافز الهدف", MsgBoxStyle.Exclamation) : Exit Sub
        If Trim(txtTargetRatioMinimum.Text) = "" Then MsgBox("من فضلك ادخل نسبة الحد الادنى للهدف", MsgBoxStyle.Exclamation) : Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update EmployeesTargetDetective set TargetNumber =N'" & txtTargetNumber.Text & "',TargetIncentive =N'" & txtTargetIncentive.Text & "',TargetRatioMinimum =N'" & txtTargetRatioMinimum.Text & "' where Target_ID =N'" & txtTarget_ID.Text & "'" : cmd.ExecuteNonQuery()

        If rdoRatioAll.Checked = True Then
            mykey.SetValue("TypeTargetRateAll", "RatioAll")
        ElseIf rdoTargetRate.Checked = True Then
            mykey.SetValue("TypeTargetRateAll", "TargetRate")
        ElseIf rdoRatioPerEmployee.Checked = True Then
            mykey.SetValue("TypeTargetRateAll", "RatioPerEmployee")
        End If

        MsgBox("تم التعديل بنجاح", MsgBoxStyle.Information)

        Bra.Fil("EmployeesTargetDetective", "TargetNumber", cmbTargetDetective)

    End Sub

    Private Sub txtTargetNumber_KeyUp(sender As Object, e As KeyEventArgs) Handles txtTargetNumber.KeyUp
        If e.KeyCode = 13 Then
            txtTargetIncentive.Focus()
        End If
    End Sub

    Private Sub txtTargetIncentive_KeyUp(sender As Object, e As KeyEventArgs) Handles txtTargetIncentive.KeyUp
        If e.KeyCode = 13 Then
            txtTargetRatioMinimum.Focus()
        End If
    End Sub

    Private Sub txtTargetRatioMinimum_KeyUp(sender As Object, e As KeyEventArgs) Handles txtTargetRatioMinimum.KeyUp
        If e.KeyCode = 13 Then
            btnSaveTargetDetective.PerformClick()
        End If
    End Sub

    Private Sub Fill_TargetDetective()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Target_ID,TargetNumber,TargetIncentive,TargetRatioMinimum from EmployeesTargetDetective"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            txtTarget_ID.Text = dr("Target_ID")
            txtTargetNumber.Text = dr("TargetNumber")
            txtTargetIncentive.Text = dr("TargetIncentive")
            txtTargetRatioMinimum.Text = dr("TargetRatioMinimum")
        End If
    End Sub

    Private Sub cmbTargetDetective_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbTargetDetective.SelectedIndexChanged
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select Target_ID,TargetNumber,TargetIncentive,TargetRatioMinimum from EmployeesTargetDetective where TargetNumbe =N'" & cmbTargetDetective.Text & "'"
        'dr = cmd.ExecuteReader : dr.Read()
        'If dr.HasRows = True Then
        '    txtTarget_ID.Text = dr("Target_ID")
        '    txtTargetNumber.Text = dr("TargetNumber")
        '    txtTargetIncentive.Text = dr("TargetIncentive")
        '    txtTargetRatioMinimum.Text = dr("TargetRatioMinimum")
        'End If
        Fill_TargetDetective()
    End Sub

    Private Sub rdoRatioOnly_CheckedChanged(sender As Object, e As EventArgs) Handles rdoRatioAll.CheckedChanged
        If rdoRatioAll.Checked = True Then
            txtTargetNumber.Enabled = False
            txtTargetIncentive.Enabled = False
            txtTargetRatioMinimum.Enabled = True
        End If
        If rdoTargetRate.Checked = True Then
            txtTargetNumber.Enabled = True
            txtTargetIncentive.Enabled = True
            txtTargetRatioMinimum.Enabled = True
        End If
        If rdoRatioPerEmployee.Checked = True Then
            txtTargetNumber.Enabled = False
            txtTargetIncentive.Enabled = False
            txtTargetRatioMinimum.Enabled = False
        End If
    End Sub

    Private Sub rdoTargetRate_CheckedChanged(sender As Object, e As EventArgs) Handles rdoTargetRate.CheckedChanged
        If rdoRatioAll.Checked = True Then
            txtTargetNumber.Enabled = False
            txtTargetIncentive.Enabled = False
            txtTargetRatioMinimum.Enabled = True
        End If
        If rdoTargetRate.Checked = True Then
            txtTargetNumber.Enabled = True
            txtTargetIncentive.Enabled = True
            txtTargetRatioMinimum.Enabled = True
        End If
        If rdoRatioPerEmployee.Checked = True Then
            txtTargetNumber.Enabled = False
            txtTargetIncentive.Enabled = False
            txtTargetRatioMinimum.Enabled = False
        End If
    End Sub

    Private Sub rdoRatioPerEmployee_CheckedChanged(sender As Object, e As EventArgs) Handles rdoRatioPerEmployee.CheckedChanged
        If rdoRatioAll.Checked = True Then
            txtTargetNumber.Enabled = False
            txtTargetIncentive.Enabled = False
            txtTargetRatioMinimum.Enabled = True
        End If
        If rdoTargetRate.Checked = True Then
            txtTargetNumber.Enabled = True
            txtTargetIncentive.Enabled = True
            txtTargetRatioMinimum.Enabled = True
        End If
        If rdoRatioPerEmployee.Checked = True Then
            txtTargetNumber.Enabled = False
            txtTargetIncentive.Enabled = False
            txtTargetRatioMinimum.Enabled = False
        End If
    End Sub

    Private Sub txtGovernment_Code_TextChanged(sender As Object, e As EventArgs) Handles txtGovernment_Code.TextChanged
        MyVars.CheckNumber(txtGovernment_Code)
    End Sub

    Private Sub txtBranch_Code_TextChanged(sender As Object, e As EventArgs) Handles txtBranch_Code.TextChanged
        MyVars.CheckNumber(txtBranch_Code)
    End Sub

    Private Sub txtGeoArea_Code_TextChanged(sender As Object, e As EventArgs) Handles txtGeoArea_Code.TextChanged
        MyVars.CheckNumber(txtGeoArea_Code)
    End Sub

    Private Sub txtTarget_ID_TextChanged(sender As Object, e As EventArgs) Handles txtTarget_ID.TextChanged
        MyVars.CheckNumber(txtTarget_ID)
    End Sub

    Private Sub Panel8_Paint(sender As Object, e As PaintEventArgs) Handles Panel8.Paint

    End Sub



#End Region


End Class