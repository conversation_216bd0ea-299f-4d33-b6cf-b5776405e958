﻿Imports System.Windows.Forms
Imports System.Data
Imports System.IO
Imports System.Data.SqlClient
Imports Microsoft.Win32
Imports System.Net.Mail
Imports System.Text
Imports System.Net
Imports System.Text.RegularExpressions

Module MyVars
    Friend AR_Language As New ArabicText
    Friend ENT_Language As New EnglishText
    Friend hintaName As String
    Friend UserName As String
    Friend UserID As String
    Friend PermtionName As String
    Friend CountDownPass As Integer
    Friend IM As New IMHM_Point_Sales
    Friend Fin As Boolean
    Friend H As Integer
    Friend Cn As New SqlConnection
    Friend Cn2 As New SqlConnection
    Friend Cnos As New SqlConnection
    Friend cmd As New SqlCommand
    Friend cmd2 As New SqlCommand
    Friend cmdos As New SqlCommand
    Friend dr As SqlDataReader
    Friend dr2 As SqlDataReader
    Friend dros As SqlDataReader
    Friend ad As SqlDataAdapter
    Friend trans As SqlTransaction
    Friend ds As DataSet = New DataSet
    Public TTable As New DataTable
    Friend SqlDataAdapter1 As SqlClient.SqlDataAdapter
    Friend SqlDataAdapter2 As SqlClient.SqlDataAdapter
    Friend sh As Double
    Friend ReturnbyActive As Boolean
    Friend Cls As New yasser_class
    Friend Cos As New Online_Store
    Public UserIDIdentity As Integer
    Friend Clss As New Supper_Market_Class
    Friend Bra As New braanew
    Friend S As String
    Friend constring As String
    Friend constringOnlineStore As String
    Friend constringXP As String
    Friend constringWin7 As String
    Friend constringMaster As String
    Public activ_1 As Boolean
    Friend Bol As Boolean
    Friend itmprc As String
    Friend EditItmId As String
    Friend Action As String
    Friend WithEvents BS As New BindingSource
    Friend ServerName As String
    Friend ParcodeMore As String
    Friend Unity_Name As String
    Friend NumberPieces As Double = 1
    Friend UnitySize_ID As Integer
    Friend CustomersName As String
    Friend Company_Branch_Name As String = "كل الفروع"
    Friend Company_Branch_ID As String = 0
    Friend ItemsRateVAT, TotalBeforeVAT, TotalValueVAT, DeferredCurrentDiscount As Double
    Friend TinPriceAverage, TinPrice, Price_Unity, Profits, TotalPrice As Double
    Friend ReceivingEditBillno As String
    Friend CurrentStock As String
    Friend CurrentStockTotal As String = ""
    Friend PriceOfferBill As Boolean = False
    Friend OrdersOnlineStore As Boolean = False
    Friend OrdersOnlineStoreAdd As Boolean = False
    Friend OfferNumber As String = ""
    Friend OrdersOnlineStoreNumber As String = ""
    Friend CloseProSalTotalBeforeDisc As Double = 0
    Friend ReceivingEnterImportBillNumber As String = ""
    Friend TotalValueDiscountOrder As String = ""
    Friend ActionDataAdapter As Boolean = False
    Friend ActionNumericSeparators As Boolean = False
    Friend Dismissal_Notice As Boolean = False
    Friend printNotInvoiceWidthHeight As Boolean = False

    Public Reg As Registry
    Public mykey As RegistryKey = Registry.CurrentUser.CreateSubKey("Software \ CUSTOMS \ CUSTOMERS")
    Public key As RegistryKey = Registry.CurrentUser.CreateSubKey("Software \ CUSTOMS \ CUSTOMERS")
    Public Startup As String = "Software\Microsoft\Windows\CurrentVersion\Run"
    Public Declare Function ShellExecute Lib "shell32.dll" Alias "ShellExecuteA" (ByVal hwnd As Integer, ByVal lpOperation As String, ByVal lpFile As String, ByVal lpParameters As String, ByVal lpDirectory As String, ByVal nShowCmd As Integer) As Integer
    Public Declare Function tapiRequestMakeCall Lib "tapi32" (ByVal lpszdestaddress As String, ByVal lpszappname As String, ByVal lpszcalledparty As String, ByVal lpszcomment As String) As Long
    Public Const SW_SHOWNORMAL As Short = 1
    Public Const SW_SHOWMAXIMIZED As Short = 3
    Public Declare Function SendMessage Lib "user32" Alias "SendMessageA" (ByVal hwnd As Integer, ByVal wMsg As Integer, ByVal wParam As Integer, ByVal lParam As Integer) As Integer

    Friend computer As New Microsoft.VisualBasic.Devices.Computer()
    Friend opritingsystem As String = computer.Info.OSFullName
    Friend ComputerName As String
    Friend DataBaseName As String
    Friend UserNameServer As String
    Friend PasswordServer As String

    'Friend ServerName As String
    Friend msm As String = "فضلا راجع الدعم الفني لوجود مشكلة فى الأتصال بقاعدة البيانات"
    Public Property ErrorNo As Object
    Friend NameArCompay, NameEnCompany, CmpAddress, CmpEmail, CmpTel, CmpMobile, CmpFax, CMPUnderBILL, CMPAddressBill, CMPWebsite, CMPCommercialRecord, CMPTaxCard, CMPNameDown, CMPEndorsement, CMPLogoPath, CMPBackupDatabasePath, CMPBackupDatabasePathFlash As String
    Friend NetworkName As String = mykey.GetValue("NetworkName", "LocalDB")
    Friend UseExternalServer As String = mykey.GetValue("UseExternalServer", "No")
    Friend BarcodeMore As String = mykey.GetValue("BarcodeMore", "NO")
    Friend NotUnityItemsProgram As String = mykey.GetValue("NotUnityItemsProgram", "NO")
    Friend SelectItemsComboBoxEnter As String = mykey.GetValue("SelectItemsComboBoxEnter", "NO")
    Friend Height_Width_Altitude_Density As String = mykey.GetValue("Show_Height_Width_Altitude_Density", "NO")
    Friend LastTinPriceItems As String = mykey.GetValue("LastTinPriceItems", "NO")
    Friend SelectLogoPathOther As String = mykey.GetValue("SelectLogoPathOther", "YES")
    Friend ItemAddedInvoiceSavedAutomatically As String = mykey.GetValue("ItemAddedInvoiceSavedAutomatically", "NO")
    Friend ChangeFontProgram As String = mykey.GetValue("ChangeFontProgram", "JF_Flat_Regular.ttf")
    Friend PurchaseTax As String = mykey.GetValue("PurchaseTax", "0")
    Friend ShowValueVAT As String = mykey.GetValue("ValueVAT", "NO")
    Friend ShowTax As String = mykey.GetValue("SalesTax", "0")
    Friend DefaultCurrencyProgram As String = mykey.GetValue("DefaultCurrencyProgram", "الجنية المصرى")
    Friend RunDatabaseInternet As String = mykey.GetValue("RunDatabaseInternet", "NO")
    Friend NetworkNameInternet As String = mykey.GetValue("NetworkNameInternet", "NO")
    Friend DataBaseNameInternet As String = mykey.GetValue("DatabaseElectricNetwork", "DatabaseElectricNetwork")
    Friend ServerNameInternet As String = mykey.GetValue("ServerNameInternet", ComputerName)
    Friend UserNameServerInternet As String = mykey.GetValue("UserNameInternet", "sa")
    Friend PasswordServerInternet As String = mykey.GetValue("PasswordInternet", "")
    Friend ConnectOnlineStore As String = mykey.GetValue("ConnectOnlineStore", "NO")
    Friend DataBaseNameOnlineStore As String = mykey.GetValue("DataBaseNameOnlineStore", "")
    Friend ServerNameOnlineStore As String = mykey.GetValue("ServerNameOnlineStore", "")
    Friend UserNameOnlineStore As String = mykey.GetValue("UserNameOnlineStore", "")
    Friend PasswordOnlineStore As String = mykey.GetValue("PasswordOnlineStore", "")
    Friend AllowWithdrawalAfterRequestCustomer As String = mykey.GetValue("AllowWithdrawalAfterRequestCustomer", "NO")
    Friend Treasury_Code As String = mykey.GetValue("Treasury_Code", "1")
    Friend TreasuryName As String = mykey.GetValue("TreasuryName", "الخزينة الرئيسية")
    Friend TinPriceAverageThreeDigits As String = mykey.GetValue("TinPriceAverageThreeDigits", "NO")
    Friend DateNotBeenActivatedSales As String = mykey.GetValue("DateNotBeenActivatedSales", "NO")
    Friend DefaultBarcodeViewPrinter As String = mykey.GetValue("DefaultBarcodeViewPrinter", "NO")
    Friend DefaultBarcodeShowPrice As String = mykey.GetValue("DefaultBarcodeShowPrice", "NO")
    Friend DefaultBarcodePaper As String = mykey.GetValue("DefaultBarcodePaper", "PaperHalves1")
    Friend BankPayCustNameBank As String = mykey.GetValue("BankPayCustNameBank", "البنك الاهلى")
    Friend BankPayCustNameCurrency As String = mykey.GetValue("BankPayCustNameCurrency", "الجنية المصرى")
    Friend BankPayCustAccountNumber As String = mykey.GetValue("BankPayCustAccountNumber", "0")
    Friend ActivateAddDepositBankCustomerPayments As String = mykey.GetValue("ActivateAddDepositBankCustomerPayments", "NO")
    Friend AllowCustOrSupplierReturnItemNotMovement As String = mykey.GetValue("AllowCustOrSupplierReturnItemNotMovement", "NO")
    Friend BeforeScanningBarcodeQuantityStar As String = mykey.GetValue("BeforeScanningBarcodeQuantityStar", "NO")
    Friend NonControlBillNumberSalesAutomatic As String = mykey.GetValue("NonControlBillNumberSalesAutomatic", "NO")
    Friend NonControlDriverRateOrderDelivery As String = mykey.GetValue("NonControlDriverRateOrderDelivery", "NO")
    Friend AllowingSalesPaidAndRestToCustomerCash As String = mykey.GetValue("AllowingSalesPaidAndRestToCustomerCash", "NO")
    Friend ActivationProgramProcessorNumberSystem As String = mykey.GetValue("ActivationProgramProcessorNumberSystem", "YES")
    Friend ActivationLicenseKey As String = mykey.GetValue("ActivationLicenseKey", "*********")
    Friend ActivationLicenseKeyActive As String = mykey.GetValue("ActivationLicenseKeyActive", "0")
    Friend ConfirmDelegateRegisteredSales As String = mykey.GetValue("ConfirmDelegateRegisteredSales", "NO")
    Friend DealingPharmacySystem As String = mykey.GetValue("DealingPharmacySystem", "NO")
    Friend NotControlStoreTheSales As String = mykey.GetValue("NotControlStoreTheSales", "NO")
    Friend ShowCustomerAddressSales As String = mykey.GetValue("ShowCustomerAddressSales", "NO")
    Friend HideProgramNameBill As String = mykey.GetValue("HideProgramNameBill", "NO")
    Friend PrintBillStoreExchangeWithoutPrices As String = mykey.GetValue("PrintBillStoreExchangeWithoutPrices", "NO")
    Friend PrintBillStoreExchangeWithoutGroub As String = mykey.GetValue("PrintBillStoreExchangeWithoutGroub", "NO")
    Friend ShowGroupItemSalesScreen As String = mykey.GetValue("ShowGroupItemSalesScreen", "NO")
    Friend ShowDiscountRateItemSales As String = mykey.GetValue("ShowDiscountRateItemSales", "NO")
    Friend AddNewCustomerSalesAutomatically As String = mykey.GetValue("AddNewCustomerSalesAutomatically", "NO")
    Friend SalesMenuTouch As String = mykey.GetValue("SalesMenuTouch", "NO")
    Friend SeletctFreeSearchTheListOfItems As String = mykey.GetValue("SeletctFreeSearchTheListOfItems", "NO")
    Friend UseManufacturingProduct As String = mykey.GetValue("UseManufacturingProduct", "NO")
    Friend DesignAnotherSalesInvoice As String = mykey.GetValue("DesignAnotherSalesInvoice", "تصميم رقم 1")
    Friend StoresName As String = mykey.GetValue("StoresName", "المخزن الرئيسى")
    Friend StoresID As String = mykey.GetValue("StoresID", "6269")
    Friend FocusText As String = mykey.GetValue("FocusText", "NO")
    Friend UseBalanceBarcode As String = mykey.GetValue("UseBalanceBarcode", "NO")
    Friend PrintSmall As String = mykey.GetValue("PrintSmall", "NO")
    Friend SettingPrinterAuto As String = mykey.GetValue("SettingPrinterAuto", "NO")
    Friend DefaultPrinterBill As String = mykey.GetValue("DefaultPrinterBill", "NO")
    Friend DefaultPrinterBill2 As String = mykey.GetValue("DefaultPrinterBill2", "NO")
    Friend DefaultPrinterBarcode As String = mykey.GetValue("DefaultPrinterBarcode", "NO")
    Friend DefaultPrinterA4 As String = mykey.GetValue("DefaultPrinterA4", "NO")
    Friend ParcodeEdit13Number As String = mykey.GetValue("ParcodeEdit13Number", "NO")
    Friend LanguageMainProgram As String = mykey.GetValue("LanguageMainProgram", "Arabic")
    Friend ActivateElectronicBill As String = mykey.GetValue("ActivateElectronicBill", "NO")
    Friend LastSalPriceCustomerUnitPriceByLargQuant As String = mykey.GetValue("LastSalPriceCustomerUnitPriceByLargQuant", "NO")
    Friend DealingWithSerialItems As String = mykey.GetValue("DealingWithSerialItems", "NO")
    Friend DealingWithSerialItemsSeparateConnected As String = mykey.GetValue("DealingWithSerialItemsSeparateConnected", "منفصلة")
    Friend CommercialAndIndustrialProfitsTax As String = mykey.GetValue("CommercialAndIndustrialProfitsTax", "0")
    Friend CommercialAndIndustrialProfitsTaxYESNO As String = mykey.GetValue("CommercialAndIndustrialProfitsTaxYESNO", "NO")
    Friend AddingNewItmesFromSales As String = mykey.GetValue("AddingNewItmesFromSales", "NO")
    Friend SelectTaxInvoiceNumber As String = mykey.GetValue("SelectTaxInvoiceNumber", "NO")
    Friend SelectTaxInvoiceNumberTax As String = mykey.GetValue("SelectTaxInvoiceNumberTax", "0001")
    Friend SelectTaxInvoiceNumberNotTax As String = mykey.GetValue("SelectTaxInvoiceNumberNotTax", "********")
    Friend ProgramNameBill As String = mykey.GetValue("ProgramNameBill", "برنامج اف اى تى سوفت للبرمجيات ***********")
    Friend SelectTypeMaintenanceDeviceCar As String = mykey.GetValue("SelectTypeMaintenanceDeviceCar", "مركز صيانة السيارات")
    Friend SalesInvoicePrintingLanguage As String = mykey.GetValue("SalesInvoicePrintingLanguage", "Arabic")
    Friend RateBankExpensesPaidByVisa As String = mykey.GetValue("RateBankExpensesPaidByVisa", "0")
    Friend ValueVATNumber As String = mykey.GetValue("ValueVATNumber", "0")
    Friend FilterSelect As String = mykey.GetValue("FilterSelect", "Number")
    Friend DealingSheftStatusSales As String = mykey.GetValue("DealingSheftStatusSales", "NO")
    Friend TreasuryTransferFrom As String = mykey.GetValue("TreasuryTransferFrom", "")
    Friend TreasuryTransferTo As String = mykey.GetValue("TreasuryTransferTo", "")
    Friend ActivateSpecificDeviceStore As String = mykey.GetValue("ActivateSpecificDeviceStore", "NO")
    Friend AllowAddPurchasesTinPrice As String = mykey.GetValue("AllowAddPurchasesTinPrice", "NO")
    Friend DateNotBeenActivatedPrograms As String = mykey.GetValue("DateNotBeenActivatedPrograms", "NO")
    Friend DefineCustomInvoiceHandling As String = mykey.GetValue("DefineCustomInvoiceHandling", "NO")
    Friend PrintInvoiceNumberCustomerMainInvoice As String = mykey.GetValue("PrintInvoiceNumberCustomerMainInvoice", "NO")
    Friend ChangeFileNameProgramFITSOFT As String = mykey.GetValue("ChangeFileNameProgramFITSOFT", "FIT SOFT.EXE")
    Friend ErrorLoadReportFailed As String = mykey.GetValue("ErrorLoadReportFailed", "NO")
    Friend ErrorLoadReportFailedPrint As String = mykey.GetValue("ErrorLoadReportFailedPrint", "BillSales")
    Friend ScreenNCRHomePortName As String = mykey.GetValue("ScreenNCRHomePortName", "COM5")
    Friend ScreenNCRHomeBaudRate As String = mykey.GetValue("ScreenNCRHomeBaudRate", "9600")
    Friend InvoicePrintedTwoCopies As String = mykey.GetValue("InvoicePrintedTwoCopies", "NO")
    Friend ActivateOptionsPassword As String = mykey.GetValue("ActivateOptionsPassword", "NO")
    Friend TreasuryControl As String = mykey.GetValue("TreasuryControl", "NO")
    Friend ActivatePendingBill As String = mykey.GetValue("ActivatePendingBill", "NO")
    Friend BackUpTimeFlashMemory As String = mykey.GetValue("BackUpTimeFlashMemory", "NO")
    Friend BackUpTimeFlashMemoryHour As String = mykey.GetValue("BackUpTimeFlashMemoryHour", "0")
    Friend ChangeFontSizeSalesText As String = mykey.GetValue("ChangeFontSizeSalesText", "Font4")
    Friend ShowConfirmationMessageBeforeSavingSalesInvoice As String = mykey.GetValue("ShowConfirmationMessageBeforeSavingSalesInvoice", "NO")
    Friend DateNotBeenActivatedOutcome As String = mykey.GetValue("DateNotBeenActivatedOutcome", "NO")
    Friend NumberPayNotBeenActivated As String = mykey.GetValue("NumberPayNotBeenActivated", "NO")
    Friend ActivateFormatNumberWithSeparators As String = mykey.GetValue("ActivateFormatNumberWithSeparators", "NO")
    Friend NoSalesAllowedTheMinimumQuantity As String = mykey.GetValue("NoSalesAllowedTheMinimumQuantity", "NO")
    Friend UpdateSalPriceAllStores As String = mykey.GetValue("UpdateSalPriceAllStores", "NO")
    Friend AddMoreInvoiceSeparately As String = mykey.GetValue("AddMoreInvoiceSeparately", "NO")
    Friend MoreItemsInvoice As String = mykey.GetValue("MoreItemsInvoice", "NO")
    Friend Show_Height_Width_Altitude_Density As String = mykey.GetValue("Show_Height_Width_Altitude_Density", "NO")
    Friend NotUploadingCustomerDataToTheSalesScreen As String = mykey.GetValue("NotUploadingCustomerDataToTheSalesScreen", "NO")
    Friend CustomDeliveryInvoice As String = mykey.GetValue("CustomDeliveryInvoice", "NO")
    Friend ShowBeginningBalanceAdjustmentItemScreen As String = mykey.GetValue("ShowBeginningBalanceAdjustmentItemScreen", "NO")
    Friend ActivatePaidAndRestAll As String = mykey.GetValue("ActivatePaidAndRestAll", "NO")
    Friend ShowDeliveryServiceSales As String = mykey.GetValue("ShowDeliveryServiceSales", "NO")
    Friend PrintCustomerAccountAfterPrinting As String = mykey.GetValue("PrintCustomerAccountAfterPrinting", "NO")
    Friend SelectDeferredPaymentStatusSalesScreenDefault As String = mykey.GetValue("SelectDeferredPaymentStatusSalesScreenDefault", "NO")
    Friend HideItemGroupSearchForProduct As String = mykey.GetValue("HideItemGroupSearchForProduct", "NO")
    Friend EmployeeCustody As String = mykey.GetValue("EmployeeCustody", "NO")


    Public CountLoopProgressBar As Long
    Public ValueNumberProgressBar As String
    Public ValueTextProgressBar As String
    Public ValueProgressBar As String
    Public ValueForLoop As Double
    Public ActionAddNewItems As Boolean = False
    Public Expired As String
    Friend Cash_Out_IN As String = ""
    Friend Cash_Out_IN_Button As String = ""
    Friend Sheft_Number As String = ""
    Friend ButtonAction As Boolean = False
    Friend TimeAMBMTotal As String = ""
    Friend TimeAmPm24Hour As String = ""
    Friend TimeAmBm As String
    Friend ItemsUnityNumber As Integer = 0
    Friend SN_Barcode As String
    Friend SN_Store As String
    Friend SN_Qunt As String
    Friend SN_billno As String
    Friend SN_View As String
    Friend StockOnline As String
    Friend InputBoxPassword As Boolean = False
    Friend InputBoxPasswordText As String
    Friend MsgBoxInputBox As String
    Friend Vendor_Customer As String
    Friend MaximizeButtons As Boolean = True
    Friend aray_NetworkNameDefault As New ArrayList
    Friend txtHeight As Double = 0
    Friend txtWidth As Double = 0
    Friend txtAltitude As Double = 0
    Friend txtDensity As Double = 0
    Friend TotalSquareMeter As Double = 0
    Friend TotalWidthHeight As Double = 0


    Function connect() As SqlConnection
        Dim Cn As New SqlConnection()
        NetworkName = mykey.GetValue("NetworkName", "LocalDB")

        Dim CnState As Boolean = False
        If NetworkName = "No" Then
            constring = "Data Source=" + ComputerName + ";Initial Catalog=" + DataBaseName + ";Integrated Security=True"
        End If
        If NetworkName = "Yes" Then
            constring = "Data Source=" + ComputerName + ";Initial Catalog=" + DataBaseName + ";Password=" + PasswordServer + ";Persist Security Info=True;User ID=" + UserNameServer + ";Connection Timeout=200;pooling=true;max pool size=2500"
        End If
        If NetworkName = "Source" Then
            constring = "Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\DatabaseElectric.mdf;Integrated Security=True;"
        End If
        If NetworkName = "LocalDB" Then
            constring = My.Computer.FileSystem.ReadAllText(Application.StartupPath & "\ConnectionString.txt")
        End If

        Try
            Cn.ConnectionString = constring
            cmd.Connection = Cn
            If Cn.State = ConnectionState.Closed Then
                Cn.Open()
                CnState = True
                'trans = Cn.BeginTransaction
                'cmd.Transaction = trans
                Return Cn
            End If
        Catch ex As Exception
        End Try


        If CnState = False Then
            For i As Integer = 0 To aray_NetworkNameDefault.Count - 1
                NetworkName = aray_NetworkNameDefault(i).ToString

                If NetworkName = "No" Then
                    constring = "Data Source=" + ComputerName + ";Initial Catalog=" + DataBaseName + ";Integrated Security=True"
                End If
                If NetworkName = "Yes" Then
                    constring = "Data Source=" + ComputerName + ";Initial Catalog=" + DataBaseName + ";Password=" + PasswordServer + ";Persist Security Info=True;User ID=" + UserNameServer + ";Connection Timeout=200;pooling=true;max pool size=2500"
                End If
                If NetworkName = "Source" Then
                    constring = "Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\DatabaseElectric.mdf;Integrated Security=True;"
                End If
                If NetworkName = "LocalDB" Then
                    constring = My.Computer.FileSystem.ReadAllText(Application.StartupPath & "\ConnectionString.txt")
                End If

                Try
                    Cn.ConnectionString = constring
                    cmd.Connection = Cn
                    If Cn.State = ConnectionState.Closed Then
                        Cn.Open()
                        CnState = True
                        'trans = Cn.BeginTransaction
                        'cmd.Transaction = trans
                        Return Cn
                    End If
                Catch ex As Exception
                End Try
            Next
        End If

        Try
            mykey.SetValue("FITSOFTConstring", constring)
        Catch ex As Exception
        End Try

        If CnState = False Then
            MsgBox("يوجد مشكلة بالاتصال بقاعدة البيانات" & " " & msm, MsgBoxStyle.Exclamation)
            frmSettingNetwork.ShowDialog()
            Return Nothing
        End If

    End Function

    Function ConnectNetworkInternet() As SqlConnection
        Dim Cn As New SqlConnection()
        NetworkNameInternet = mykey.GetValue("NetworkNameInternet", "Source")

        If NetworkNameInternet = "NO" Then
            constring = "Data Source=" + ServerNameInternet + ";Initial Catalog=" + DataBaseNameInternet + ";Integrated Security=True"
        End If
        If NetworkNameInternet = "YES" Then
            constring = "Data Source=" + ServerNameInternet + ";Initial Catalog=" + DataBaseNameInternet + ";Password=" + PasswordServerInternet + ";Persist Security Info=True;User ID=" + UserNameServerInternet + ";Connection Timeout=200;pooling=true;max pool size=2500"
        End If
        If NetworkNameInternet = "Source" Then
            constring = "Data Source=" + ComputerName + ";AttachDbFilename=|DataDirectory|\DatabaseElectric.mdf;Integrated Security=True;"
            'constring = "Data Source=.\SQLEXPRESS;AttachDbFilename=|DataDirectory|\" + DataBaseNameInternet + ".mdf;Integrated Security=True;User Instance=True"
        End If
        If NetworkName = "LocalDB" Then
            constring = My.Computer.FileSystem.ReadAllText(Application.StartupPath & "\ConnectionString.txt")
        End If
        mykey.SetValue("FITSOFTConstringInternet", constring)

        Try
            Cn.ConnectionString = constring
            cmd.Connection = Cn
            If Cn.State = ConnectionState.Closed Then
                Cn.Open()
                'trans = Cn.BeginTransaction
                'cmd.Transaction = trans
                Return Cn
            End If
        Catch ex As Exception
            'ConnectNetworkInternet()
            Return Nothing
        End Try
    End Function

    Function ConnectingOnlineStore() As SqlConnection
        Dim Cn As New SqlConnection()
        ConnectOnlineStore = mykey.GetValue("ConnectOnlineStore", "NO")

        If ConnectOnlineStore = "YES" Then
            constringOnlineStore = "Data Source=" + ServerNameOnlineStore + ";Initial Catalog=" + DataBaseNameOnlineStore + ";Password=" + PasswordOnlineStore + ";Persist Security Info=True;User ID=" + UserNameOnlineStore + ";Connection Timeout=200;pooling=true;max pool size=2500"

            mykey.SetValue("FITSOFTConstringOnlineStore", constringOnlineStore)

            Try
                Cn.ConnectionString = constringOnlineStore
                cmd.Connection = Cn
                If Cn.State = ConnectionState.Closed Then
                    Cn.Open()
                    'trans = Cn.BeginTransaction
                    'cmd.Transaction = trans
                    Return Cn
                End If
            Catch ex As Exception
                Return Nothing
            End Try
        End If

    End Function


    Function connectDatabaseElectric2() As SqlConnection
        'Dim Cn As New SqlConnection()
        constring = "Data Source=.\SQLEXPRESS;AttachDbFilename=|DataDirectory|\DatabaseElectric.mdf;Integrated Security=True;User Instance=True"
        Try
            Cn2.ConnectionString = constring
            cmd2.Connection = Cn2
            If Cn2.State = ConnectionState.Closed Then
                Cn2.Open()
                Return Cn2
            End If
        Catch ex As Exception
            Return Nothing
        End Try
    End Function


    Public Sub connectionStringOpen()
        If Cn.State = ConnectionState.Closed Then
            Cn.ConnectionString = constring
            cmd.Connection = Cn
            Cn.Open()
        End If
    End Sub

    Public Sub connectionStringClose()
        If Cn.State = ConnectionState.Open Then
            Cn.Close()
        End If
    End Sub

    Public Sub connectionStringTransaction()
        If Cn.State = ConnectionState.Closed Then
            Cn.ConnectionString = constring
            cmd.Connection = Cn
            Cn.Open()
            trans = Cn.BeginTransaction
            cmd.Transaction = trans
        End If
    End Sub

    Public Sub CheckNumber(ByVal txt As TextBox)
        If Not IsNumeric(txt.Text) Then
            System.Windows.Forms.SendKeys.Send("{backspace}")
        End If
    End Sub

    Friend Function Sp_SelectCountOnFastBill()
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using cmd As SqlCommand = connect.CreateCommand
                cmd.CommandType = CommandType.Text
                cmd.CommandTimeout = 180
                cmd.CommandText = "Select Count(bill_No) from Sales_Bill"
                Return cmd.ExecuteScalar
            End Using
        Else
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Function

    Friend Sub Sb_Sp_InsertA_C(ByVal Activename As String)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using cmddelete As SqlCommand = connect.CreateCommand
                cmddelete.CommandType = CommandType.Text
                cmddelete.CommandText = "delete from A_C"
                cmddelete.ExecuteNonQuery()
            End Using

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using cmd As SqlCommand = connect.CreateCommand
                cmd.CommandType = CommandType.StoredProcedure
                cmd.CommandTimeout = 180
                cmd.CommandText = "Sp_InsertA_C"
                cmd.Parameters.AddWithValue("@activename", Activename)
                cmd.ExecuteNonQuery()
            End Using
        Else
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Sub
    Friend Function Fn_Sp_SelectA_C(ByVal Activename As String) As Boolean

        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using cmd As SqlCommand = connect.CreateCommand
                cmd.CommandType = CommandType.StoredProcedure
                cmd.CommandText = "Sp_SelectA_C"
                cmd.Parameters.AddWithValue("@Activename", Activename)
                Dim dr As SqlDataReader = cmd.ExecuteReader
                dr.Read()
                If dr.HasRows = False Then
                    Return False
                Else
                    Return True
                End If

            End Using

        Else
            MsgBox(Cls_Constant.ErrMsg)
        End If

    End Function
    Friend Function fn_Sp_SelectUsertoenter(ByVal USername As String, ByVal userpassword As String) As Boolean
        Try
            ' تهيئة SecureDatabaseManager إذا لم يتم تهيئته
            If Not SecureDatabaseManager.Instance._isInitialized Then
                SecureDatabaseManager.Instance.Initialize()
            End If

            ' استخدام SecureDatabaseManager للتحقق الآمن من المستخدم
            Dim isValidUser As Boolean = SecureDatabaseManager.Instance.ValidateUser(USername, userpassword)
            If Not isValidUser Then
                ErrorHandler.LogWarning($"محاولة دخول فاشلة للمستخدم: {USername}")
                Return False
            Else
                ' الحصول على معرف المستخدم بشكل آمن
                Dim query As String = "SELECT UserID FROM users WHERE username = @username"
                Dim parameters As New Dictionary(Of String, Object) From {
                    {"@username", USername}
                }

                Dim result As Object = SecureDatabaseManager.Instance.ExecuteScalar(query, parameters)
                If result IsNot Nothing AndAlso Not IsDBNull(result) Then
                    UserIDIdentity = Convert.ToInt32(result)
                    ErrorHandler.LogInfo($"تم دخول المستخدم بنجاح: {USername}")
                    Return True
                Else
                    ErrorHandler.LogError("لم يتم العثور على معرف المستخدم", New Exception("User ID not found"))
                    Return False
                End If
            End If

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في التحقق من المستخدم", ex, $"Username: {USername}")
            Return False
        End Try
    End Function

    Friend Function fn_Sp_Selectpermtiontoenter(ByVal USerID As Integer, ByVal frm As Form)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using Insertuser As SqlCommand = connect.CreateCommand
                Insertuser.CommandType = CommandType.StoredProcedure
                Insertuser.CommandText = "SpSelectPermation"
                Insertuser.Parameters.AddWithValue("@USerID", USerID)
                Dim dr As SqlDataReader = Insertuser.ExecuteReader
                'dr.Read()
                Dim ctl As Object
                Do While dr.Read
                    For Each ctl In frm.Controls
                        If TypeOf ctl Is MenuStrip Then
                            For Each ctl1 As ToolStripMenuItem In CType(ctl, MenuStrip).Items
                                If TypeOf ctl1 Is ToolStripMenuItem Then
                                    Dim n As String = dr(0)
                                    If ctl1.Text = dr(0) Then
                                        CType(ctl1, ToolStripMenuItem).Enabled = True
                                    End If
                                    If dr(0) = "مدير" Then
                                        CType(ctl1, ToolStripMenuItem).Enabled = True
                                    End If
                                End If
                            Next
                        End If
                    Next
                Loop
            End Using
        Else
            MsgBox(Cls_Constant.ErrMsg)
            Return Nothing
        End If
    End Function
    Sub testper(ByVal userID As Integer)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using Insertuser As SqlCommand = connect.CreateCommand
                Insertuser.CommandType = CommandType.StoredProcedure
                Insertuser.CommandText = "SpSelectPermation"
                Insertuser.Parameters.AddWithValue("@USerID", userID)
                Dim dr As SqlDataReader = Insertuser.ExecuteReader
                'dr.Read()
                ' Dim ctl As Object
                Do While dr.Read
                    For Each ctl1 As ToolStripMenuItem In MDIParent1.MenuStrip.Items
                        Dim x As New ToolStripMenuItem
                        x.Name = dr(0)
                        Dim n As New ToolStripMenuItem
                        Dim y As String
                        y = dr(0)
                        If ctl1.DropDownItems.Contains(x) Then
                            x.Enabled = False
                        End If
                    Next
                Loop
            End Using
        Else
            MsgBox(Cls_Constant.ErrMsg)

        End If
    End Sub
    Friend Function fn_Sp_InsertUser(ByVal USerName As String, ByVal UserPassword As String)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using Insertuser As SqlCommand = connect.CreateCommand
                Insertuser.CommandType = CommandType.StoredProcedure
                Insertuser.CommandText = "Sp_InsertUsers"
                Insertuser.Parameters.AddWithValue("@UserName", USerName)
                Insertuser.Parameters.AddWithValue("@UserPassword", UserPassword)
                Insertuser.Parameters.Add("@identity", SqlDbType.Int).Direction = ParameterDirection.ReturnValue
                Insertuser.ExecuteNonQuery()
                Return Insertuser.Parameters("@identity").Value
            End Using
        Else
            MsgBox(Cls_Constant.ErrMsg)
            Return Nothing
        End If
    End Function
    Friend Function fn_Sp_InsertPermation(ByVal USerid As Integer, ByVal permtionname As String)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using Insertpermtion As SqlCommand = connect.CreateCommand
                Insertpermtion.CommandType = CommandType.StoredProcedure
                Insertpermtion.CommandText = "Sp_InsertPermtions"
                Insertpermtion.Parameters.AddWithValue("@UserID", USerid)
                Insertpermtion.Parameters.AddWithValue("@PermtionName", permtionname)
                Insertpermtion.ExecuteNonQuery()
            End Using
        Else
            MsgBox(Cls_Constant.ErrMsg)
            Return Nothing
        End If
    End Function
    Friend Sub Sb_Sp_FillComboUsername(ByVal cmb As ComboBox)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using SelectnameItem As SqlCommand = connect.CreateCommand
                SelectnameItem.CommandType = CommandType.Text
                SelectnameItem.CommandText = "Select UserID,UserName from Users"
                Dim Dr As SqlDataReader = SelectnameItem.ExecuteReader
                Dim Dt As New DataTable
                Dt.Load(Dr)
                cmb.DataSource = Dt
                cmb.ValueMember = Dt.Columns(0).ColumnName
                cmb.DisplayMember = Dt.Columns(1).ColumnName
            End Using
        Else
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Sub
    Friend Function fn_Sp_SelectUser(ByVal USerID As Integer) As SqlDataReader
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using Insertuser As SqlCommand = connect.CreateCommand
                Insertuser.CommandType = CommandType.StoredProcedure
                Insertuser.CommandText = "SpSelectUses"
                Insertuser.Parameters.AddWithValue("@USerID", USerID)
                Dim dr As SqlDataReader = Insertuser.ExecuteReader
                dr.Read()
                If dr.HasRows = False Then
                    Exit Function
                Else
                    Return dr
                End If

            End Using
        Else
            MsgBox(Cls_Constant.ErrMsg)
            Return Nothing
        End If
    End Function
    Friend Function fn_Sp_Selectpermtion(ByVal USerID As Integer, ByVal frm As Form)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using Insertuser As SqlCommand = connect.CreateCommand
                Insertuser.CommandType = CommandType.StoredProcedure
                Insertuser.CommandText = "SpSelectPermation"
                Insertuser.Parameters.AddWithValue("@USerID", USerID)
                Dim dr As SqlDataReader = Insertuser.ExecuteReader
                Dim ctl As Control
                Dim ctl1 As Control
                Do While dr.Read
                    For Each ctl In frm.Controls
                        If TypeOf ctl Is GroupBox Then
                            For Each ctl1 In ctl.Controls
                                If TypeOf ctl1 Is CheckBox Then
                                    If ctl1.Text = dr(0) Then
                                        CType(ctl1, CheckBox).Checked = True

                                    End If
                                End If
                            Next
                        End If
                    Next
                Loop
            End Using
        Else
            MsgBox(Cls_Constant.ErrMsg)
            Return Nothing
        End If
    End Function
    Friend Sub Sb_Sp_DeleteUsers(ByVal USerID As Integer)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using Insertuser As SqlCommand = connect.CreateCommand
                Insertuser.CommandType = CommandType.StoredProcedure
                Insertuser.CommandText = "Sp_DeleteUsers"
                Insertuser.Parameters.AddWithValue("@USerID", USerID)
                Insertuser.ExecuteNonQuery()
            End Using
        Else
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Sub

    Friend Function Fn_Sp_Insert(ByVal procedurename As String, ByVal bill_date As String, ByVal billtime As String, ByVal totalpriceafterdisc As Integer _
                                 , ByVal what As String, ByVal parm As String)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using cmdbanna As SqlCommand = connect.CreateCommand
                cmdbanna.CommandType = CommandType.StoredProcedure
                cmdbanna.CommandText = "" + procedurename + ""
                cmdbanna.Parameters.AddWithValue("@" + what + "", parm)
                'cmdbanna.Parameters.AddWithValue("@Vendorname", Vendorname)
                cmdbanna.Parameters.AddWithValue("@bill_date", bill_date)
                cmdbanna.Parameters.AddWithValue("@billtime", billtime)
                cmdbanna.Parameters.AddWithValue("@totalpriceafterdisc", totalpriceafterdisc)
                'cmdbanna.Parameters.AddWithValue("@Stat", Stat)
                cmdbanna.Parameters.Add("@identity", SqlDbType.Int).Direction = ParameterDirection.ReturnValue
                cmdbanna.ExecuteNonQuery()
                Return cmdbanna.Parameters("@identity").Value
            End Using
        Else
            MsgBox(msm, MsgBoxStyle.Exclamation)
            Return Nothing
        End If
    End Function
    Friend Sub Sb_Sp_InsertData(ByVal procedurename As String, ByVal parmtername As String, ByVal Back_SalesID As Integer _
, ByVal itm_cat As String, ByVal itm_name As String, ByVal itm_Bracode As String, ByVal price As Integer _
, ByVal qu As Integer, ByVal totalprice As Integer, ByVal Itm_ID As String)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using cmdbanna As SqlCommand = connect.CreateCommand
                cmdbanna.CommandType = CommandType.StoredProcedure
                cmdbanna.CommandText = "" + procedurename + ""
                cmdbanna.Parameters.AddWithValue("@" + parmtername + "", Back_SalesID)

                cmdbanna.Parameters.AddWithValue("@itm_cat", itm_cat)
                cmdbanna.Parameters.AddWithValue("@itm_name", itm_name)
                cmdbanna.Parameters.AddWithValue("@itm_id", itm_Bracode)
                cmdbanna.Parameters.AddWithValue("@price", price)
                cmdbanna.Parameters.AddWithValue("@qu", qu)
                cmdbanna.Parameters.AddWithValue("@totalprice", totalprice)
                cmdbanna.Parameters.AddWithValue("@Itm_ID", Itm_ID)
                cmdbanna.ExecuteNonQuery()
            End Using
        Else
            MsgBox(msm, MsgBoxStyle.Exclamation)
        End If
    End Sub
    Friend Sub Sb_Update(ByVal procedurename As String, ByVal parmtername As String, ByVal Back_ImportID As Integer, ByVal bill_date As String _
, ByVal billtime As String, ByVal totalpriceafterdisc As Integer, ByVal what As String, ByVal parm As String)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using cmdbanna As SqlCommand = connect.CreateCommand
                cmdbanna.CommandType = CommandType.StoredProcedure
                cmdbanna.CommandText = "" + procedurename + ""
                'cmdbanna.Parameters.AddWithValue("@bill_No", bill_No)
                cmdbanna.Parameters.AddWithValue("@" + what + "", parm)
                'cmdbanna.Parameters.AddWithValue("@Vendorname", Vendorname)
                cmdbanna.Parameters.AddWithValue("@bill_date", bill_date)
                cmdbanna.Parameters.AddWithValue("@billtime", billtime)
                cmdbanna.Parameters.AddWithValue("@totalpriceafterdisc", totalpriceafterdisc)
                'cmdbanna.Parameters.AddWithValue("@Stat", Stat)
                cmdbanna.Parameters.AddWithValue("@" + parmtername + "", Back_ImportID)
                cmdbanna.ExecuteNonQuery()
            End Using
        Else
            MsgBox(msm, MsgBoxStyle.Exclamation)
        End If
    End Sub

    Friend Sub Sb_Updatedata(ByVal procedurename As String, ByVal parmtername As String, ByVal Back_ImportID As Integer, ByVal itm_cat As String _
, ByVal itm_name As String, ByVal itm_Bracode As String, ByVal price As Integer _
, ByVal qu As Integer, ByVal totalprice As Integer, ByVal Itm_ID As Integer)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using cmdbanna As SqlCommand = connect.CreateCommand
                cmdbanna.CommandType = CommandType.StoredProcedure
                cmdbanna.CommandText = "" + procedurename + ""
                cmdbanna.Parameters.AddWithValue("@itm_cat", itm_cat)
                cmdbanna.Parameters.AddWithValue("@itm_name", itm_name)
                cmdbanna.Parameters.AddWithValue("@itm_Bracode", itm_Bracode)
                cmdbanna.Parameters.AddWithValue("@price", price)
                cmdbanna.Parameters.AddWithValue("@qu", qu)
                cmdbanna.Parameters.AddWithValue("@totalprice", totalprice)
                cmdbanna.Parameters.AddWithValue("@Itm_ID", Itm_ID)
                cmdbanna.Parameters.AddWithValue("@" + parmtername + "", Back_ImportID)
                cmdbanna.ExecuteNonQuery()
            End Using
        Else
            MsgBox(msm, MsgBoxStyle.Exclamation)
        End If
    End Sub
    Friend Sub Sb_Sp_Delete(ByVal procedurename As String, ByVal parmtername As String, ByVal valueint As Integer)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using cmdbanna As SqlCommand = connect.CreateCommand
                cmdbanna.CommandType = CommandType.StoredProcedure
                cmdbanna.CommandText = "" + procedurename + ""
                cmdbanna.Parameters.AddWithValue("@" + parmtername + "", valueint)
                cmdbanna.ExecuteNonQuery()
            End Using
        Else
            MsgBox(msm, MsgBoxStyle.Exclamation)
        End If
    End Sub
    Friend Function Fn_Sp_SelectByName(ByVal procedurename As String, ByVal Vendorname As String, ByVal Dtg As DataGridView) As Boolean
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using cmd As SqlCommand = connect.CreateCommand
                cmd.CommandType = CommandType.StoredProcedure
                cmd.CommandText = "" + procedurename + ""
                cmd.Parameters.AddWithValue("@Vendorname", Vendorname)
                Using dr As SqlDataReader = cmd.ExecuteReader()
                    If dr.HasRows = False Then
                        Return False
                    Else
                        Try
                            Dtg.DataSource = Nothing
                            Dim dt As New DataTable
                            dt.Load(dr)
                            For Each Datar As DataRow In dt.Rows
                                Dtg.Rows.Add()
                            Next
                            Dim r As Int16
                            For r = 0 To Dtg.Rows.Count - 1

                                Dtg.Rows(r).Cells(2).Value = dt.Rows(r)(0)
                                Dtg.Rows(r).Cells(3).Value = dt.Rows(r)(1)
                                Dtg.Rows(r).Cells(4).Value = dt.Rows(r)(2)
                                Dtg.Rows(r).Cells(5).Value = dt.Rows(r)(3)
                                Dtg.Rows(r).Cells(6).Value = dt.Rows(r)(4)
                                Dtg.Rows(r).Cells(7).Value = dt.Rows(r)(5)
                                'Dtg.Rows(r).Cells(8).Value = dt.Rows(r)(6)
                                'Dtg.Rows(r).Cells(9).Value = dt.Rows(r)(8)
                            Next
                        Catch ex As Exception
                        End Try
                        Return True
                    End If
                End Using
            End Using
        Else
            MsgBox(msm, MsgBoxStyle.Exclamation)
        End If
    End Function
    Friend Function Fn_Sp_SelectBy2date(ByVal procedurename As String, ByVal date1 As String, ByVal date2 As String, ByVal Dtg As DataGridView) As Boolean
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using cmd As SqlCommand = connect.CreateCommand
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT * FROM dbo.Back_Sales WHERE  bill_date BETWEEN '" & date1 & "' AND '" & date2 & "'"
                'cmd.Parameters.AddWithValue("@date1", date1)
                'cmd.Parameters.AddWithValue("@date2", date2)
                Using dr As SqlDataReader = cmd.ExecuteReader()
                    If dr.HasRows = False Then
                        Return False
                    Else
                        Try
                            Dtg.DataSource = Nothing
                            Dim dt As New DataTable
                            dt.Load(dr)
                            For Each Datar As DataRow In dt.Rows
                                Dtg.Rows.Add()
                            Next
                            Dim r As Int16
                            For r = 0 To Dtg.Rows.Count - 1
                                Dtg.Rows(r).Cells(2).Value = dt.Rows(r)(0)
                                Dtg.Rows(r).Cells(3).Value = dt.Rows(r)(1)
                                Dtg.Rows(r).Cells(4).Value = dt.Rows(r)(2)
                                Dtg.Rows(r).Cells(5).Value = dt.Rows(r)(3)
                                Dtg.Rows(r).Cells(6).Value = dt.Rows(r)(4)
                                Dtg.Rows(r).Cells(7).Value = dt.Rows(r)(5)
                            Next
                        Catch ex As Exception
                        End Try
                        Return True
                    End If
                End Using
            End Using
        Else
            MsgBox(msm, MsgBoxStyle.Exclamation)
        End If
    End Function
    Friend Function Fn_Sp_SelectData_Back_ImportByBack_ImportID(ByVal procedurename As String, ByVal paramtername As String, ByVal Back_ImportID As String, ByVal Dtg As DataGridView) As Boolean
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using cmd As SqlCommand = connect.CreateCommand
                cmd.CommandType = CommandType.StoredProcedure
                cmd.CommandText = "" + procedurename + ""
                cmd.Parameters.AddWithValue("@" + paramtername + "", Back_ImportID)
                Using dr As SqlDataReader = cmd.ExecuteReader()
                    If dr.HasRows = False Then
                        Return False
                    Else
                        Try
                            Dtg.DataSource = Nothing
                            Dim dt As New DataTable
                            dt.Load(dr)
                            For Each Datar As DataRow In dt.Rows
                                Dtg.Rows.Add()
                            Next
                            Dim r As Int16
                            For r = 0 To Dtg.Rows.Count - 1

                                'Frmbackimport.Fn_AddBill(dt.Rows(r)(1), dt.Rows(r)(2), dt.Rows(r)(0), dt.Rows(r)(3), dt.Rows(r)(4), dt.Rows(r)(5), dt.Rows(r)(6))
                                'itm_Bracode, itm_cat, itm_name, price, qu, totalprice, Itm_ID
                                ' itm_cat, itm_name, itm_size, price, qu, totalprice
                                Dtg.Rows(r).Cells(1).Value = dt.Rows(r)(0)
                                Dtg.Rows(r).Cells(2).Value = dt.Rows(r)(1)
                                Dtg.Rows(r).Cells(3).Value = dt.Rows(r)(2)
                                Dtg.Rows(r).Cells(4).Value = dt.Rows(r)(3)
                                Dtg.Rows(r).Cells(5).Value = dt.Rows(r)(4)
                                Dtg.Rows(r).Cells(6).Value = dt.Rows(r)(5)
                                Dtg.Rows(r).Cells(7).Value = dt.Rows(r)(6)
                                'Dtg.Rows(r).Cells(9).Value = dt.Rows(r)(7)
                            Next
                        Catch ex As Exception
                        End Try
                        Return True
                    End If
                End Using
            End Using
        Else
            MsgBox(msm, MsgBoxStyle.Exclamation)
        End If
    End Function

    Private Function AmountWord(ByVal AMOUNT As Decimal) As String
        Try
        Dim n As Decimal = 0
        Dim C1 As Decimal = 0
        Dim C2 As Decimal = 0
        Dim C3 As Decimal = 0
        Dim C4 As Decimal = 0
        Dim C5 As Decimal = 0
        Dim C6 As Decimal = 0
        Dim S1 As String = ""
        Dim S2 As String = ""
        Dim S3 As String = ""
        Dim S4 As String = ""
        Dim S5 As String = ""
        Dim S6 As String = ""
        Dim C As String = ""
        n = Int(AMOUNT)
        C = Format(n, "000000000000")
        C1 = Val(Mid(C, 12, 1))
        Select Case C1
            Case Is = 1 : S1 = "واحد"
            Case Is = 2 : S1 = "اثنان"
            Case Is = 3 : S1 = "ثلاثة"
            Case Is = 4 : S1 = "اربعة"
            Case Is = 5 : S1 = "خمسة"
            Case Is = 6 : S1 = "ستة"
            Case Is = 7 : S1 = "سبعة"
            Case Is = 8 : S1 = "ثمانية"
            Case Is = 9 : S1 = "تسعة"
        End Select

        C2 = Val(Mid(C, 11, 1))
        Select Case C2
            Case Is = 1 : S2 = "عشر"
            Case Is = 2 : S2 = "عشرون"
            Case Is = 3 : S2 = "ثلاثون"
            Case Is = 4 : S2 = "اربعون"
            Case Is = 5 : S2 = "خمسون"
            Case Is = 6 : S2 = "ستون"
            Case Is = 7 : S2 = "سبعون"
            Case Is = 8 : S2 = "ثمانون"
            Case Is = 9 : S2 = "تسعون"
        End Select

        If S1 <> "" And C2 > 1 Then S2 = S1 + " و" + S2
        If S2 = "" Then S2 = S1
        If C1 = 0 And C2 = 1 Then S2 = S2 + "ة"
        If C1 = 1 And C2 = 1 Then S2 = "احدى عشر"
        If C1 = 2 And C2 = 1 Then S2 = "اثنى عشر"
        If C1 > 2 And C2 = 1 Then S2 = S1 + " " + S2
        C3 = Val(Mid(C, 10, 1))
        Select Case C3
            Case Is = 1 : S3 = "مائة"
            Case Is = 2 : S3 = "مئتان"
            Case Is > 2 : S3 = Left(AmountWord(C3), Len(AmountWord(C3)) - 1) + "مائة"
        End Select
        If S3 <> "" And S2 <> "" Then S3 = S3 + " و" + S2
        If S3 = "" Then S3 = S2

        C4 = Val(Mid(C, 7, 3))
        Select Case C4
            Case Is = 1 : S4 = "الف"
            Case Is = 2 : S4 = "الفان"
            Case 3 To 10 : S4 = AmountWord(C4) + " آلاف"
            Case Is > 10 : S4 = AmountWord(C4) + " الف"
        End Select
        If S4 <> "" And S3 <> "" Then S4 = S4 + " و" + S3
        If S4 = "" Then S4 = S3
        C5 = Val(Mid(C, 4, 3))
        Select Case C5
            Case Is = 1 : S5 = "مليون"
            Case Is = 2 : S5 = "مليونان"
            Case 3 To 10 : S5 = AmountWord(C5) + " ملايين"
            Case Is > 10 : S5 = AmountWord(C5) + " مليون"
        End Select
        If S5 <> "" And S4 <> "" Then S5 = S5 + " و" + S4
        If S5 = "" Then S5 = S4

        C6 = Val(Mid(C, 1, 3))

        Select Case C6
            Case Is = 1 : S6 = "مليار"
            Case Is = 2 : S6 = "ملياران"
            Case Is > 2 : S6 = AmountWord(C6) + " مليار"
        End Select
        If S6 <> "" And S5 <> "" Then S6 = S6 + " و" + S5
        If S6 = "" Then S6 = S5
        AmountWord = S6

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في تحويل المبلغ إلى كلمات", ex, $"Amount: {AMOUNT}")
            Return "خطأ في التحويل"
        End Try
    End Function

    Public Function MydateWord(ByVal MYDATE As Date) As String
        Try
        Dim n As Integer = 0
        Dim C1 As Decimal = 0
        Dim C2 As Decimal = 0
        Dim C3 As Decimal = 0
        Dim S1 As String = ""
        Dim S2 As String = ""
        Dim C As String = ""
        MydateWord = ""
        C = Format(MYDATE, "dd-mm-yyyy")
        C1 = MYDATE.Day
        C2 = MYDATE.Month
        C3 = MYDATE.Year
        Select Case C1
            Case Is = 1 : S1 = "الاول"
            Case Is = 2 : S1 = "الثانى"
            Case Is = 3 : S1 = "الثالث"
            Case Is = 4 : S1 = "الرابع"
            Case Is = 5 : S1 = "الخامس"
            Case Is = 6 : S1 = "السادس"
            Case Is = 7 : S1 = "السابع"
            Case Is = 8 : S1 = "الثامن"
            Case Is = 9 : S1 = "التاسع"
            Case Is = 10 : S1 = "العاشر"
            Case Is = 11 : S1 = "الحادى عشر"
            Case Is = 12 : S1 = "الثانى عشر"
            Case Is = 13 : S1 = "الثالث عشر"
            Case Is = 14 : S1 = "الرابع عشر"
            Case Is = 15 : S1 = "الخامس عشر"
            Case Is = 16 : S1 = "السادس عشر"
            Case Is = 17 : S1 = "السابع عشر"
            Case Is = 18 : S1 = "الثامن عشر"
            Case Is = 19 : S1 = "التاسع عشر"
            Case Is = 20 : S1 = "العشرون"
            Case Is = 21 : S1 = "الواحد والعشرون"
            Case Is = 22 : S1 = " الثانى والعشرون"
            Case Is = 23 : S1 = "الثالث والعشرون"
            Case Is = 24 : S1 = " الرابع والعشرون"
            Case Is = 25 : S1 = " الخامس والعشرون"
            Case Is = 26 : S1 = "السادس والعشرون"
            Case Is = 27 : S1 = "السابع والعشرون"
            Case Is = 28 : S1 = "الثامن والعشرون"
            Case Is = 29 : S1 = "التاسع والعشرون"
            Case Is = 30 : S1 = "الثلاثون"
            Case Is = 31 : S1 = "الواحد والثلاثون"
        End Select

        Select Case C2
            Case Is = 1 : S2 = "يناير"
            Case Is = 2 : S2 = "فبراير"
            Case Is = 3 : S2 = "مارس"
            Case Is = 4 : S2 = "ابريل"
            Case Is = 5 : S2 = "مايو"
            Case Is = 6 : S2 = "يونية"
            Case Is = 7 : S2 = "يوليو"
            Case Is = 8 : S2 = "اغسطس"
            Case Is = 9 : S2 = "سبتمبر"
            Case Is = 10 : S2 = "اكتوبر"
            Case Is = 11 : S2 = "نوفمبر"
            Case Is = 12 : S2 = "ديسمبر"
        End Select
        MydateWord = Format(MYDATE, "dddd") & " الموافق " & S1 & " من  شهر " & S2 & " سنة " & AmountWord(CDec(C3)) & " ميلادية "

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في تحويل التاريخ إلى كلمات", ex, $"Date: {MYDATE}")
            Return "خطأ في تحويل التاريخ"
        End Try
    End Function
    Public Sub MYDeleteRecord(ByVal TABLE As String, ByVal FIELD As String, ByVal TXT As Object, ByVal BS As BindingSource, Optional ByVal FIELDtextornumer As Boolean = True)
        Try
            ' التحقق من صحة المدخلات
            If String.IsNullOrEmpty(TABLE) OrElse String.IsNullOrEmpty(FIELD) OrElse TXT Is Nothing Then
                ErrorHandler.LogWarning("معاملات غير صحيحة في MYDeleteRecord")
                MessageBox.Show("معاملات غير صحيحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            If BS.Count <= 0 Then
                MessageBox.Show("لايوجد سجل حالى لاتمام عملية الحذف", "حذف سجل", MessageBoxButtons.OK, MessageBoxIcon.Exclamation, MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading)
                Return
            End If

            ' تأكيد الحذف من المستخدم
            Dim result As Integer = MessageBox.Show("سيتم حذف السجل الحالى", "حذف سجل " & TABLE, MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading)
            If result <> vbYes Then
                MessageBox.Show("تم ايقاف عملية الحذف", "حذف سجل", MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading)
                Return
            End If

            ' بناء الاستعلام الآمن
            Dim query As String
            Dim parameters As New Dictionary(Of String, Object)

            If FIELDtextornumer Then
                ' للحقول الرقمية
                query = $"DELETE FROM [{TABLE}] WHERE [{FIELD}] = @fieldValue"
                parameters.Add("@fieldValue", Convert.ToInt32(TXT.Text.Trim))
            Else
                ' للحقول النصية
                query = $"DELETE FROM [{TABLE}] WHERE [{FIELD}] = @fieldValue"
                parameters.Add("@fieldValue", TXT.Text.Trim)
            End If

            ' تنفيذ الحذف بشكل آمن
            Dim rowsAffected As Integer = SecureDatabaseManager.Instance.ExecuteNonQuery(query, parameters)

            If rowsAffected > 0 Then
                ErrorHandler.LogInfo($"تم حذف {rowsAffected} سجل من جدول {TABLE}")
                MessageBox.Show("تم حذف السجل بنجاح", "حذف سجل", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                ErrorHandler.LogWarning($"لم يتم حذف أي سجل من جدول {TABLE}")
                MessageBox.Show("لم يتم العثور على السجل المطلوب حذفه", "حذف سجل", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If

        Catch ex As Exception
            ErrorHandler.LogError($"خطأ في حذف السجل من جدول {TABLE}", ex)
            MessageBox.Show("حدث خطأ أثناء حذف السجل", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Public Sub GETSERVERNAMEANDDATABASENAME(ByVal rpt As Object, ByVal DATABASENAME As String, Optional ByVal PASSWORD As String = "", Optional ByVal USER As String = "")
        Dim I As Integer
        Dim TBL1 As New CrystalDecisions.Shared.TableLogOnInfo

        If NetworkName = "Source" Then
            constring = "Data Source=.\SQLEXPRESS;AttachDbFilename=|DataDirectory|\DatabaseElectric.mdf;Integrated Security=True;User Instance=True"
            TBL1.ConnectionInfo.ServerName = ".\SQLEXPRESS"
            TBL1.ConnectionInfo.DatabaseName = DATABASENAME
            TBL1.ConnectionInfo.Password = ""
            TBL1.ConnectionInfo.UserID = ""
            TBL1.ConnectionInfo.IntegratedSecurity = False
        Else
            TBL1.ConnectionInfo.ServerName = ComputerName
            TBL1.ConnectionInfo.DatabaseName = DATABASENAME
            TBL1.ConnectionInfo.Password = PASSWORD
            TBL1.ConnectionInfo.UserID = USER
            TBL1.ConnectionInfo.IntegratedSecurity = True
        End If


        For I = 0 To rpt.Database.Tables.Count - 1
            rpt.Database.Tables(I).ApplyLogOnInfo(TBL1)
        Next I
    End Sub
    Public Sub SHOWPHOTOGeneral()
        Try

        Dim SqlDataAdapter1 As SqlClient.SqlDataAdapter
        Dim ds As DataSet = New DataSet
        Dim Cn As New SqlConnection()
        Cn.ConnectionString = constring
        If Cn.State = ConnectionState.Closed Then
            Cn.Open()
            'trans = Cn.BeginTransaction
            'cmd2.Transaction = trans
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        Dim str As String = "SELECT * FROM Company"
        SqlDataAdapter1 = New SqlClient.SqlDataAdapter(str, Cn)
        ds.Clear()
        SqlDataAdapter1.Fill(ds, "Company")
        BS.DataSource = ds
        BS.DataMember = "Company"
        ds.EnforceConstraints = True
        SqlDataAdapter1.Dispose()

        Dim sql As String
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        sql = "SELECT CMPBackgroundImage FROM COMPANY"
        Dim cmd As SqlClient.SqlCommand = New SqlClient.SqlCommand(sql, Cn)
        Dim b() As Byte
        b = cmd.ExecuteScalar()
        If (b.Length > 0) Then
            Dim stream As New MemoryStream(b, True)
            stream.Write(b, 0, b.Length)
            MDIParent1.BackgroundImage = New Bitmap(stream)
            stream.Close()
        Else
            MDIParent1.BackgroundImage = Nothing
        End If

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في تحميل صورة الخلفية العامة", ex)
            ' في حالة الخطأ، نضع صورة افتراضية أو لا شيء
            MDIParent1.BackgroundImage = Nothing
        Finally
            ' تنظيف الموارد
            If Cn IsNot Nothing AndAlso Cn.State = ConnectionState.Open Then
                Cn.Close()
            End If
        End Try
    End Sub
    Public Sub SHOWPHOTOSales()
        'On Error Resume Next

        Dim SqlDataAdapter1 As SqlClient.SqlDataAdapter
        Dim ds As DataSet = New DataSet
        Dim Cn As New SqlConnection()
        Cn.ConnectionString = constring
        If Cn.State = ConnectionState.Closed Then
            Cn.Open()
            'trans = Cn.BeginTransaction
            'cmd2.Transaction = trans
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        Dim str As String = "SELECT * FROM Company"
        SqlDataAdapter1 = New SqlClient.SqlDataAdapter(str, Cn)
        ds.Clear()
        SqlDataAdapter1.Fill(ds, "Company")
        BS.DataSource = ds
        BS.DataMember = "Company"
        ds.EnforceConstraints = True
        SqlDataAdapter1.Dispose()

        Dim sql As String
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        sql = "SELECT CMPImage FROM COMPANY"
        Dim cmd As SqlClient.SqlCommand = New SqlClient.SqlCommand(sql, Cn)
        Dim b() As Byte
        b = cmd.ExecuteScalar()
        If (b.Length > 0) Then
            Dim stream As New MemoryStream(b, True)
            stream.Write(b, 0, b.Length)
            FrmSales.PictureBox1.Image = New Bitmap(stream)
            FrmSales.picWorkShowImage.Image = New Bitmap(stream)
            stream.Close()
        Else
            FrmSales.PictureBox1.Image = Nothing
            FrmSales.picWorkShowImage.Image = Nothing
        End If

    End Sub
    Public Sub FILLCOMBOBOXITEMS(ByVal TABLE As String, ByVal FIELD As String, ByVal COMBO As Object)
        Dim I As Integer
        Dim DS As New DataSet
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        Dim str As String = "SELECT DISTINCT " & FIELD & " FROM " & TABLE
        Dim ADP As New SqlClient.SqlDataAdapter(str, Cn)
        DS.Clear()
        ADP.Fill(DS, "TBL")
        COMBO.Items.Clear()
        For I = 0 To DS.Tables("TBL").Rows.Count - 1
            COMBO.Items.Add(DS.Tables("TBL").Rows(I).Item(0))
        Next I
        ADP.Dispose()
    End Sub
    Public Sub FILLCOMBOBOX(ByVal TABLE As String, ByVal FIELD As String, ByVal COMBO As Object)
        Dim BS As New BindingSource
        Dim DS As New DataSet
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        Dim str As String = "SELECT DISTINCT " & FIELD & " FROM " & TABLE
        Dim ADP As New SqlClient.SqlDataAdapter(str, Cn)
        ADP.Fill(DS, "TBL")
        BS.DataSource = DS
        BS.DataMember = "TBL"
        COMBO.Items.Clear()
        COMBO.DataSource = BS
        COMBO.DisplayMember = FIELD
        COMBO.ValueMember = FIELD
        ADP.Dispose()
    End Sub

    Public Function GETATTACHDATABASENAME() As Boolean
        Try
        Dim DS As New DataSet
        Dim SqlConnection1 As SqlClient.SqlConnection = New SqlClient.SqlConnection("Data Source=" + ServerName & "\SQLEXPRESS" + ";Initial Catalog=tempdb;Integrated Security=SSPI;")
        'Dim SqlConnection1 As SqlClient.SqlConnection = New SqlClient.SqlConnection("Data Source=" + ServerName + ";Initial Catalog=tempdb;Password=******;Integrated Security=SSPI;User ID=sa;")
        Dim str As String = "Select DISTINCT name from master.dbo.sysdatabases where name Like '" + DataBaseName + "' and has_dbaccess(Name) = 1 "
        Dim ADP As SqlClient.SqlDataAdapter
        ADP = New SqlClient.SqlDataAdapter(str, SqlConnection1)
        DS.Clear()
        ADP.Fill(DS)
        Dim i As Integer
        If DS.Tables(0).Rows.Count = 0 Then
            GETATTACHDATABASENAME = False
            'MessageBox.Show(" قاعدة البيانات  '" + DataBaseName + "'" & "غير متصلة بالسرفر جارى عمل الاتصال" & vbCrLf & vbCrLf & " من فضلك انتظر قليلاَ                             ", My.Computer.Name, MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading)
        Else
            GETATTACHDATABASENAME = True
        End If
        ADP.Dispose()
        SqlConnection1.Dispose()

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في التحقق من قاعدة البيانات المرفقة", ex)
            Return False
        End Try
    End Function

    Public Sub ATTACHDATABASENAME(ByVal MYDBNAME As String, ByVal f1lepathprimary As String, ByVal f1lepathlog As String)
        Try
            Dim SqlConnection1 As SqlClient.SqlConnection = New SqlClient.SqlConnection("Data Source=" + ServerName & "\SQLEXPRESS" + ";Initial Catalog=tempdb;Integrated Security=True")
            'Dim SqlConnection1 As SqlClient.SqlConnection = New SqlClient.SqlConnection("Data Source=" + ServerName + ";Initial Catalog=tempdb;Password=******;Persist Security Info=True;User ID=sa")
            Dim CMD As SqlClient.SqlCommand = New SqlClient.SqlCommand
            CMD.CommandType = CommandType.Text
            CMD.Connection = SqlConnection1
            If SqlConnection1.State = ConnectionState.Open Then SqlConnection1.Close()
            SqlConnection1.Open()
            CMD.CommandText = "sp_attach_db " & MYDBNAME & ",N'" & f1lepathprimary & "N'" & ",N'" & f1lepathlog & "'"
            CMD.ExecuteNonQuery()
            SqlConnection1.Dispose()
            MessageBox.Show("تم انشاء اتصال  قاعدة البيانات '" + DataBaseName + "' بالسرفر ", "ATTCH DATABASE", MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading)
        Catch ex As Exception
            Dim result As Integer
            'result = MessageBox.Show("فشل البرنامج فى انشاء اتصال  قاعدة البيانات '" + DataBaseName + "' بالسرفر", "ATTCH DATABASE", MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading)
        End Try
    End Sub

    Public Sub set_color(ByVal BUTTON As Control, ByVal PanelControl As Control)
        Dim ctrl As Control

        For Each ctrl In PanelControl.Controls

            If TypeOf ctrl Is Button Then
                If ctrl.Name = BUTTON.Name Then
                    ctrl.BackColor = Color.FromArgb(254, 206, 105)
                Else
                    ctrl.BackColor = Color.White
                End If
            End If
        Next
    End Sub

    Public Sub SendEmail(ByVal Subject As String, ByVal Parcode As String, ByVal ItemsName As String, ByVal Qunt As String)
        Try
            Dim SmtpServerEmail As String = mykey.GetValue("SmtpServerEmail", "")
            Dim SmtpServerPassword As String = mykey.GetValue("SmtpServerPassword", "")
            Dim SendEmailFrom As String = mykey.GetValue("SendEmailFrom", "")
            Dim SendEmailTO As String = mykey.GetValue("SendEmailTO", "")

            Dim Message As New StringBuilder()
            Message.Append("عنوان الرسالة : " + Subject + Environment.NewLine + Environment.NewLine)
            Message.Append("الباركود : " + Parcode + Environment.NewLine + Environment.NewLine)
            Message.Append("أسم الصنف : " + ItemsName + Environment.NewLine + Environment.NewLine)
            Message.Append("الكمية : " + Qunt + Environment.NewLine + Environment.NewLine)

            Dim Smtp_Server As New SmtpClient
            Dim e_mail As New MailMessage()
            Smtp_Server.UseDefaultCredentials = False
            Smtp_Server.Credentials = New Net.NetworkCredential(SmtpServerEmail, SmtpServerPassword)
            Smtp_Server.Port = 587
            Smtp_Server.EnableSsl = True
            Smtp_Server.Host = "smtp.live.com"

            e_mail = New MailMessage()
            e_mail.From = New MailAddress(SendEmailFrom)
            e_mail.To.Add(SendEmailTO)
            e_mail.Subject = Subject
            e_mail.IsBodyHtml = False
            e_mail.Body = Message.ToString
            Smtp_Server.Send(e_mail)
        Catch ex As Exception
            ErrorHandling(ex, "MyVar")
        End Try
    End Sub

    Public Sub SendEmail_Sheft_Close(ByVal Subject As String, ByVal BranchName As String, ByVal UserName As String, ByVal Sheft_Number As String, ByVal SDate As String, ByVal STime As String, ByVal TotalSales As String)
        Try
            Dim SmtpServerEmail As String = mykey.GetValue("SmtpServerEmail", "")
            Dim SmtpServerPassword As String = mykey.GetValue("SmtpServerPassword", "")
            Dim SendEmailFrom As String = mykey.GetValue("SendEmailFrom", "")
            Dim SendEmailTO As String = mykey.GetValue("SendEmailTO", "")

            Dim Message As New StringBuilder()
            Message.Append("عنوان الرسالة : " + Subject + Environment.NewLine + Environment.NewLine)
            Message.Append("أسم الفرع : " + BranchName + Environment.NewLine + Environment.NewLine)
            Message.Append("المستخدم : " + UserName + Environment.NewLine + Environment.NewLine)
            Message.Append("رقم الشيفت : " + Sheft_Number + Environment.NewLine + Environment.NewLine)
            Message.Append("التاريخ : " + SDate + Environment.NewLine + Environment.NewLine)
            Message.Append("الوقت : " + STime + Environment.NewLine + Environment.NewLine)
            Message.Append("أجمالى المبيعات : " + TotalSales + Environment.NewLine + Environment.NewLine)

            Dim Smtp_Server As New SmtpClient
            Dim e_mail As New MailMessage()
            Smtp_Server.UseDefaultCredentials = False
            Smtp_Server.Credentials = New Net.NetworkCredential(SmtpServerEmail, SmtpServerPassword)
            Smtp_Server.Port = 587
            Smtp_Server.EnableSsl = True
            Smtp_Server.Host = "smtp.live.com"

            e_mail = New MailMessage()
            e_mail.From = New MailAddress(SendEmailFrom)
            e_mail.To.Add(SendEmailTO)
            e_mail.Subject = Subject
            e_mail.IsBodyHtml = False
            e_mail.Body = Message.ToString
            Smtp_Server.Send(e_mail)
        Catch error_t As Exception
            'MsgBox(error_t.ToString)
        End Try
    End Sub

    Public Sub SendEmail_Sheft_Open(ByVal Subject As String, ByVal BranchName As String, ByVal UserName As String, ByVal Sheft_Number As String, ByVal SDate As String, ByVal STime As String)
        Try
            Dim SmtpServerEmail As String = mykey.GetValue("SmtpServerEmail", "")
            Dim SmtpServerPassword As String = mykey.GetValue("SmtpServerPassword", "")
            Dim SendEmailFrom As String = mykey.GetValue("SendEmailFrom", "")
            Dim SendEmailTO As String = mykey.GetValue("SendEmailTO", "")

            Dim Message As New StringBuilder()
            Message.Append("عنوان الرسالة : " + Subject + Environment.NewLine + Environment.NewLine)
            Message.Append("أسم الفرع : " + BranchName + Environment.NewLine + Environment.NewLine)
            Message.Append("المستخدم : " + UserName + Environment.NewLine + Environment.NewLine)
            Message.Append("رقم الشيفت : " + Sheft_Number + Environment.NewLine + Environment.NewLine)
            Message.Append("التاريخ : " + SDate + Environment.NewLine + Environment.NewLine)
            Message.Append("الوقت : " + STime + Environment.NewLine + Environment.NewLine)


            Dim Smtp_Server As New SmtpClient
            Dim e_mail As New MailMessage()
            Smtp_Server.UseDefaultCredentials = False
            Smtp_Server.Credentials = New Net.NetworkCredential(SmtpServerEmail, SmtpServerPassword)
            Smtp_Server.Port = 587
            Smtp_Server.EnableSsl = True
            Smtp_Server.Host = "smtp.live.com"

            e_mail = New MailMessage()
            e_mail.From = New MailAddress(SendEmailFrom)
            e_mail.To.Add(SendEmailTO)
            e_mail.Subject = Subject
            e_mail.IsBodyHtml = False
            e_mail.Body = Message.ToString
            Smtp_Server.Send(e_mail)
        Catch error_t As Exception
            'MsgBox(error_t.ToString)
        End Try
    End Sub

    Public Sub OpenCashDrawer()
        Dim intFileNo As Integer = FreeFile()

        'Use this code if you are using LPT Port
        'FileOpen(1, "c:\escapes.txt", OpenMode.Output)
        'PrintLine(1, Chr(27) & "p" & Chr(0) & Chr(25) & Chr(250))
        'FileClose(1)

        'Shell("print /d:lpt1 c:\escapes.txt", vbNormalFocus)

        'Use this code if you are using COM Port
        FileOpen(1, AppDomain.CurrentDomain.BaseDirectory & "open.txt", OpenMode.Output)
        PrintLine(1, Chr(27) & Chr(112) & Chr(0) & Chr(25) & Chr(250))
        FileClose(1)

        Shell("print /d:com1 open.txt", AppWinStyle.Hide)
        FileClose(1)
    End Sub

    Public Sub AddReportView()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If RunDatabaseInternet = "YES" Then
            ConnectNetworkInternet()
        End If
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandTimeout = 180
            cmd.CommandText = "SELECT * FROM Company"
            dr = cmd.ExecuteReader
            dr.Read()
            If dr.HasRows = True Then
                NameArCompay = dr("CMPName").ToString
                NameEnCompany = dr("CMPNameEnglish").ToString
                CmpAddress = dr("CMPAddress").ToString
                CmpTel = dr("CMPTel").ToString
                CmpMobile = dr("CMPMobile").ToString
                CmpFax = dr("CMPFax").ToString
                CmpEmail = dr("CMPEmail").ToString
                CMPWebsite = dr("CMPWebsite").ToString
                CMPUnderBILL = dr("CMPUnderBILL").ToString
                CMPAddressBill = dr("CMPAddressBill").ToString
                CMPNameDown = dr("CMPNameDown").ToString
                CMPTaxCard = dr("CMPTaxCard").ToString
                CMPEndorsement = dr("CMPEndorsement").ToString
                CMPCommercialRecord = dr("CMPCommercialRecord").ToString
                CMPLogoPath = dr("CMPLogoPath").ToString
                CMPBackupDatabasePath = dr("CMPBackupDatabasePath").ToString
                CMPBackupDatabasePathFlash = dr("CMPBackupDatabasePathFlash").ToString

            End If
        Catch ex As Exception : ErrorHandling(ex, "MyVars") : End Try
    End Sub

    Public Sub GetBarcodeMore(ByVal ParcodeMoreText As String)
        If BarcodeMore = "YES" Then
            Dim Parcode As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id from BarcodeMore where itm_id_More=N'" & ParcodeMoreText & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                ParcodeMore = dr("itm_id").ToString
            End If
        End If
    End Sub

    Public Sub GetItemsUnity(ByVal cmbUnity As ComboBox, ByVal Parcode As String)
        cmbUnity.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Unity_Name from ItemsUnity where itm_id=N'" & Parcode & "'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            cmbUnity.Items.Add(Trim(dr(0).ToString))
            ItemsUnityNumber += 1
        Loop

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Unity_Name from ItemsUnity where itm_id=N'" & Parcode & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            cmbUnity.Text = dr(0).ToString
        End If

        'If cmbUnity.Text = "" Then
        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    S = "insert into ItemsUnity(itm_id,itm_id_Unity,NumberPieces,TinPriceUnit,SalPriceUnit,DefaultTin,DefaultSale,Unity_Name,Company_Branch_ID)  values("
        '    S = S & "N'" & Parcode & "',N'" & Parcode & "',1,0,0,1,1,N'قطعة',N'" & Company_Branch_ID & "')"
        '    cmd.CommandText = S : cmd.ExecuteNonQuery()
        '    cmbUnity.Text = "قطعة"
        'End If

    End Sub

    Public Sub AddReportViewNew()
        NameArCompay = mykey.GetValue("CMPNameArabic", "")
        NameEnCompany = mykey.GetValue("CMPNameEnglish", "")
        CmpAddress = mykey.GetValue("CMPAddress", "")
        CmpTel = mykey.GetValue("CMPTel", "")
        CmpMobile = mykey.GetValue("CMPMobile", "")
        CmpFax = mykey.GetValue("CMPFax", "")
        CmpEmail = mykey.GetValue("CMPEmail", "")
        CMPUnderBILL = mykey.GetValue("CMPUnderBILL", "")
        CMPAddressBill = mykey.GetValue("CMPAddressBill", "")
        CMPWebsite = mykey.GetValue("CMPWebsite", "")
        CMPCommercialRecord = mykey.GetValue("CMPCommercialRecord", "")
        CMPTaxCard = mykey.GetValue("CMPTaxCard", "")
        CMPNameDown = mykey.GetValue("CMPNameDown", "")
        CMPEndorsement = mykey.GetValue("CMPEndorsement", "")
        CMPLogoPath = mykey.GetValue("CMPLogoPath", "")
        CMPBackupDatabasePath = mykey.GetValue("CMPBackupDatabasePath", "")
        CMPBackupDatabasePathFlash = mykey.GetValue("CMPBackupDatabasePathFlash", "")
    End Sub

    Public Sub ErrorHandling(ByVal ex As Exception, ByVal Title As String)
        Dim trace = New Diagnostics.StackTrace(ex, True)
        Dim line As String = Strings.Right(trace.ToString, 6)
        Dim nombreMetodo As String = ""
        For Each sf As StackFrame In trace.GetFrames
            nombreMetodo = sf.GetMethod().Name & vbCrLf
        Next
        MessageBox.Show("Error Line Number: " & line & vbCrLf & ex.Message & vbCrLf & "Metodos : " & nombreMetodo & vbCrLf & "Title Form : " & Title)
        Dim ErrorMessage As String = ex.Message

        Dim Split As String() = New String() {"&"}
        Dim itemsSplitLine As String() = line.Trim.Split(Split, StringSplitOptions.None)
        Dim LineNumber As String = itemsSplitLine(0).ToString()

        Dim itemsSplitXMetodos As String() = nombreMetodo.Trim.Split(Split, StringSplitOptions.None)
        Dim Metodos As String = itemsSplitXMetodos(0).ToString()

        Dim XErrorMessage As String = ""
        Dim SplitX As String() = New String() {"'"}
        Dim itemsSplitMessage As String() = ErrorMessage.Trim.Split(SplitX, StringSplitOptions.None)
        For i As Integer = 0 To itemsSplitMessage.Count - 1
            XErrorMessage = XErrorMessage + itemsSplitMessage(i)
        Next

        Dim MaxRecord As String = MaxRecordTables("ErrorMessage", "Error_ID")
        Dim DateNow = Format(DateTime.Now, "yyyyMMdd")
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into ErrorMessage(Company_Branch_ID,Error_ID,TitleForm,LineNumber,MethodsName,ErrorMessage,ErrorDate,ErrorTime,UserName)  values (N'" & Company_Branch_ID & "',N'" & MaxRecord & "',N'" & Title & "',N'" & LineNumber & "',N'" & Metodos & "',N'" & XErrorMessage & "',N'" & DateNow & "',N'" & Cls.get_time(True) & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()
    End Sub

    Public Sub ErrorHandlingNotMessage(ByVal ex As Exception, ByVal Title As String)
        Dim trace = New Diagnostics.StackTrace(ex, True)
        Dim line As String = Strings.Right(trace.ToString, 6)
        Dim nombreMetodo As String = ""
        For Each sf As StackFrame In trace.GetFrames
            nombreMetodo = sf.GetMethod().Name & vbCrLf
        Next
        'MessageBox.Show("Error Line Number: " & line & vbCrLf & ex.Message & vbCrLf & "Metodos : " & nombreMetodo & vbCrLf & "Title Form : " & Title)
        Dim ErrorMessage As String = ex.Message

        Dim Split As String() = New String() {"&"}
        Dim itemsSplitLine As String() = line.Trim.Split(Split, StringSplitOptions.None)
        Dim LineNumber As String = itemsSplitLine(0).ToString()

        Dim itemsSplitXMetodos As String() = nombreMetodo.Trim.Split(Split, StringSplitOptions.None)
        Dim Metodos As String = itemsSplitXMetodos(0).ToString()

        Dim XErrorMessage As String = ""
        Dim SplitX As String() = New String() {"'"}
        Dim itemsSplitMessage As String() = ErrorMessage.Trim.Split(SplitX, StringSplitOptions.None)
        For i As Integer = 0 To itemsSplitMessage.Count - 1
            XErrorMessage = XErrorMessage + itemsSplitMessage(i)
        Next

        Dim MaxRecord As String = MaxRecordTables("ErrorMessage", "Error_ID")
        Dim DateNow = Format(DateTime.Now, "yyyyMMdd")
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into ErrorMessage(Company_Branch_ID,Error_ID,TitleForm,LineNumber,MethodsName,ErrorMessage,ErrorDate,ErrorTime,UserName)  values (N'" & Company_Branch_ID & "',N'" & MaxRecord & "',N'" & Title & "',N'" & LineNumber & "',N'" & Metodos & "',N'" & XErrorMessage & "',N'" & DateNow & "',N'" & Cls.get_time(True) & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()
    End Sub

    Public Sub Get_Movement_In_Out_Money(ByVal C_Date As String, ByVal Treasury_Code_ID As Integer)

        Dim split As String() = New String() {"/"} : Dim itemsSplit As String() = C_Date.Split(split, StringSplitOptions.None) : Dim MM1 As String = itemsSplit(0).ToString()
        Try
            Dim MM2 As String = itemsSplit(1).ToString() : C_Date = Cls.C_date(C_Date)
        Catch ex As Exception : End Try

        Dim XCustPay, Xvnd, Xsal, Xpurchase, Xbsal, XBvnd, XVst_disc, Xvndr_disc, XVst_disc_other, XExpenses, XNetSalary, XCashWithdrawalReward, XVst_Receipts, XVnd_Receipts, XCheck_Out, XCheck_In, XOther_Income, XCapital_Deposit, XCapital_Withdraw, XTreasury_Withdraw, XTreasury_Deposit, XTreasury_Deficit, XTreasury_Increase, XTotalNetAssets, XVst_Check_Type, XVnd_Check_Type As Double
        Try
            ' مقبوضات عملاء
            'XCustPay = Cls.Select_SUM_Value("Vst", "VND_amx", "VND_dt", C_Date)
            XCustPay = Cls.Select_SUM_Value_More("Vst", "VND_amx", "VND_dt =N'" & C_Date & "' and BillNo <> N'جرد'")

            'مدفوعات موردين
            'Xvnd = Cls.Select_SUM_Value("vnd", "VND_amx", "VND_dt", C_Date)
            Xvnd = Cls.Select_SUM_Value_More("vnd", "VND_amx", "VND_dt =N'" & C_Date & "' and BillNo <> N'جرد'")

            ' قيمة المبيعات
            Xsal = Cls.Select_SUM_Value("BillsalData", "totalprice", "bill_date", C_Date)

            ' قيمة المشتريات
            Xpurchase = Cls.Select_SUM_Value("BilltINData", "totalprice", "bill_date", C_Date)

            'قيمة مرتجعات المبيعات
            Xbsal = Cls.Select_SUM_Value("IM_Vst", "VND_amx", "VND_dt", C_Date)

            'قيمة مرتجعات المشتريات
            XBvnd = Cls.Select_SUM_Value("IM_Vnd", "VND_amx", "VND_dt", C_Date)

            ' خصومات عملاء
            XVst_disc = Cls.Select_SUM_Value("Vst_disc", "amnt", "pdate", C_Date)

            'خصومات موردين
            Xvndr_disc = Cls.Select_SUM_Value("vndr_disc", "amnt", "pdate", C_Date)

            ' خصومات اخرى عملاء
            XVst_disc_other = Cls.Select_SUM_Value("Vst_disc_other", "amnt", "pdate", C_Date)

            'مصروفات
            XExpenses = Cls.Select_SUM_Value("Expenses", "Exp_Value", "Exp_Date", C_Date)

            'مسحوبات و مكافئات
            XCashWithdrawalReward = Cls.Select_SUM_Value_More("EmployeesDiscountReward", "Amount", "Move_Date =N'" & C_Date & "' and DiscountReward_Type_ID <> N'3' AND DiscountReward_Type_ID <> N'2' AND DiscountReward_Type_ID <> N'4'")

            'مرتبات
            XNetSalary = Cls.Select_SUM_Value("Salary", "NetSalaryTreasury", "SalaryDate", C_Date)

            ' اضافة الى حساب مقبوضات العملاء
            XVst_Receipts = Cls.Select_SUM_Value("Vst_Receipts", "VND_amx", "VND_dt", C_Date)

            ' اضافة الى حساب مدفوعات الموردين
            XVnd_Receipts = Cls.Select_SUM_Value("Vnd_Receipts", "VND_amx", "VND_dt", C_Date)

            ' مسحوبات جارى الشركاء
            XCheck_Out = Cls.Select_SUM_Value_More("Capital_Partner_Withdrawals", "Amount", "Capital_Date =N'" & C_Date & "' and Capital_Type_Code =N'1'")

            ' ايداعات جارى الشركاء
            XCheck_In = Cls.Select_SUM_Value_More("Capital_Partner_Withdrawals", "Amount", "Capital_Date =N'" & C_Date & "' and Capital_Type_Code =N'2'")

            'ايرادات اخرى
            XOther_Income = Cls.Select_SUM_Value_More("Other_Income", "Amount", "Income_Date =N'" & C_Date & "'")

            ' مسحوبات رأس الشركاء
            XCapital_Withdraw = Cls.Select_SUM_Value_More("Capital_Reserve_CheckOut_Deposit", "Amount", "Capital_Date =N'" & C_Date & "' and Capital_Type_Code =N'1'")

            ' ايداعات رأس الشركاء
            XCapital_Deposit = Cls.Select_SUM_Value_More("Capital_Reserve_CheckOut_Deposit", "Amount", "Capital_Date =N'" & C_Date & "' and Capital_Type_Code =N'2'")

            ' مسحوبات الخزينة
            XTreasury_Withdraw = Cls.Select_SUM_Value_More("TreasuryMovement_Withdraw", "Treasury_Amount", "Treasury_Date =N'" & C_Date & "'")

            ' ايداعات الخزينة
            XTreasury_Deposit = Cls.Select_SUM_Value_More("TreasuryMovement_Deposit", "Treasury_Amount", "Treasury_Date =N'" & C_Date & "'")

            ' عجز الخزينة
            XTreasury_Deficit = Cls.Select_SUM_Value_More("Treasury_Deficit_Increase", "Amount", "Deficit_Increase_Date =N'" & C_Date & "' and Capital_Type_Code =N'1'")

            ' زيادة الخزينة
            XTreasury_Increase = Cls.Select_SUM_Value_More("Treasury_Deficit_Increase", "Amount", "Deficit_Increase_Date =N'" & C_Date & "' and Capital_Type_Code =N'2'")

            'مصروفات الاهلاك
            XTotalNetAssets = Cls.Select_SUM_Value_More("Assets", "NetValue", "DatePurchase =N'" & C_Date & "'")

            'أوراق القبض
            XVst_Check_Type = Cls.Select_SUM_Value_More("Vst_Check_Type", "PaymentTotal", "VND_dt =N'" & C_Date & "'")

            'أوراق الدفع
            XVnd_Check_Type = Cls.Select_SUM_Value_More("Vnd_Check_Type", "PaymentTotal", "VND_dt =N'" & C_Date & "'")


            Dim XTotal As Double = XCustPay - Xbsal - Xvnd + XBvnd - XVst_Receipts + XVnd_Receipts - XExpenses - XCashWithdrawalReward - XNetSalary - XCheck_Out + XCheck_In + XOther_Income - XCapital_Withdraw + XCapital_Deposit - XTreasury_Withdraw + XTreasury_Deposit - XTreasury_Deficit + XTreasury_Increase - XTotalNetAssets + XVst_Check_Type - XVnd_Check_Type

            XTotal = Math.Round(XTotal, 2)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select * from Date_Move_Money where Item_Date=N'" & C_Date & "' and Treasury_Code=N'" & Treasury_Code_ID & "'"
            Else
                cmd.CommandText = "select * from Date_Move_Money where Item_Date=N'" & C_Date & "' and Company_Branch_ID =N'" & Company_Branch_ID & "'  and Treasury_Code=N'" & Treasury_Code_ID & "'"
            End If
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                If PermtionName = "مدير" Then
                    cmd.CommandText = "update Date_Move_Money set CustPay = N'" & XCustPay & "',sal = N'" & Xsal & "',Bsal =N'" & Xbsal & "',vnd =N'" & Xvnd & "',expe =N'" & XExpenses & "',CashWithdrawal =N'" & XCashWithdrawalReward & "',NetSalary =N'" & XNetSalary & "',purchase =N'" & Xpurchase & "',Bvnd =N'" & XBvnd & "',Vst_disc =N'" & XVst_disc & "',vndr_disc =N'" & Xvndr_disc & "',Vst_disc_other =N'" & XVst_disc_other & "',Vst_Receipts =N'" & XVst_Receipts & "',Vnd_Receipts =N'" & XVnd_Receipts & "',Check_Out =N'" & XCheck_Out & "',Check_In =N'" & XCheck_In & "',Xtotal =N'" & XTotal & "',Treasury_Code = N'" & Treasury_Code_ID & "',Other_Income = N'" & XOther_Income & "',Treasury_Deposit = N'" & XTreasury_Deposit & "',Treasury_Withdraw = N'" & XTreasury_Withdraw & "',Treasury_Deficit = N'" & XTreasury_Deficit & "',Treasury_Increase = N'" & XTreasury_Increase & "',Capital_Deposit = N'" & XCapital_Deposit & "',Capital_Withdraw = N'" & XCapital_Withdraw & "',TotalNetAssets = N'" & XTotalNetAssets & "',Vst_Check_Type = N'" & XVst_Check_Type & "',Vnd_Check_Type = N'" & XVnd_Check_Type & "' where Item_Date =N'" & C_Date & "' and Treasury_Code=N'" & Treasury_Code_ID & "'" : cmd.ExecuteNonQuery()
                Else
                    cmd.CommandText = "update Date_Move_Money set CustPay = N'" & XCustPay & "',sal = N'" & Xsal & "',Bsal =N'" & Xbsal & "',vnd =N'" & Xvnd & "',expe =N'" & XExpenses & "',CashWithdrawal =N'" & XCashWithdrawalReward & "',NetSalary =N'" & XNetSalary & "',purchase =N'" & Xpurchase & "',Bvnd =N'" & XBvnd & "',Vst_disc =N'" & XVst_disc & "',vndr_disc =N'" & Xvndr_disc & "',Vst_disc_other =N'" & XVst_disc_other & "',Vst_Receipts =N'" & XVst_Receipts & "',Vnd_Receipts =N'" & XVnd_Receipts & "',Check_Out =N'" & XCheck_Out & "',Check_In =N'" & XCheck_In & "',Xtotal =N'" & XTotal & "',Treasury_Code = N'" & Treasury_Code_ID & "',Other_Income = N'" & XOther_Income & "',Treasury_Deposit = N'" & XTreasury_Deposit & "',Treasury_Withdraw = N'" & XTreasury_Withdraw & "',Treasury_Deficit = N'" & XTreasury_Deficit & "',Treasury_Increase = N'" & XTreasury_Increase & "',Capital_Deposit = N'" & XCapital_Deposit & "',Capital_Withdraw = N'" & XCapital_Withdraw & "',TotalNetAssets = N'" & XTotalNetAssets & "',Vst_Check_Type = N'" & XVst_Check_Type & "',Vnd_Check_Type = N'" & XVnd_Check_Type & "' where Item_Date =N'" & C_Date & "' and Company_Branch_ID =N'" & Company_Branch_ID & "' and Treasury_Code=N'" & Treasury_Code_ID & "'" : cmd.ExecuteNonQuery()
                End If
            Else
                If XCustPay = 0 And Xsal = 0 And Xbsal = 0 And Xvnd = 0 And XExpenses = 0 And XNetSalary = 0 And XCashWithdrawalReward = 0 And Xpurchase = 0 And XBvnd = 0 And XVst_disc = 0 And Xvndr_disc = 0 And XVst_disc_other = 0 And XVst_Receipts = 0 And XVnd_Receipts = 0 And XCheck_Out = 0 And XCheck_In = 0 And XOther_Income = 0 And XTreasury_Deposit = 0 And XTreasury_Withdraw = 0 And XTreasury_Deficit = 0 And XTreasury_Increase = 0 And XCapital_Deposit = 0 And XCapital_Withdraw = 0 And XTotalNetAssets = 0 And XVst_Check_Type = 0 And XVnd_Check_Type = 0 Then
                Else
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "insert into Date_Move_Money(Company_Branch_ID,Item_Date,CustPay,sal,Bsal,vnd,expe,CashWithdrawal,NetSalary,purchase,Bvnd,Vst_disc,vndr_disc,Vst_disc_other,Vst_Receipts,Vnd_Receipts,Check_Out,Check_In,Xtotal,Treasury_Code,Other_Income,Treasury_Deposit,Treasury_Withdraw,Treasury_Deficit,Treasury_Increase,Capital_Deposit,Capital_Withdraw,TotalNetAssets,Vst_Check_Type,Vnd_Check_Type) values (N'" & Company_Branch_ID & "',N'" & C_Date & "'," & XCustPay & "," & Xsal & "  ," & Xbsal & " ," & Xvnd & "," & XExpenses & "," & XCashWithdrawalReward & "," & XNetSalary & "," & Xpurchase & "," & XBvnd & "," & XVst_disc & "," & Xvndr_disc & "," & XVst_disc_other & "," & XVst_Receipts & "," & XVnd_Receipts & "," & XCheck_Out & "," & XCheck_In & "," & XTotal & "," & Treasury_Code_ID & "," & XOther_Income & "," & XTreasury_Deposit & "," & XTreasury_Withdraw & "," & XTreasury_Deficit & "," & XTreasury_Increase & "," & XCapital_Deposit & "," & XCapital_Withdraw & "," & XTotalNetAssets & "," & XVst_Check_Type & "," & XVnd_Check_Type & ")"
                    cmd.ExecuteNonQuery()
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, "MyVar Get_Movement_In_Out_Money")
        End Try
    End Sub

    Function MaxRecordTables(ByVal Tables As String, ByVal Code As String)
        Try
            Dim MaxRecoedCode As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from " & Tables & ""
            dr = cmd.ExecuteReader
            Dim dt As New DataTable
            dt.Load(dr)

            If dt.Rows.Count = 0 Then
                MaxRecoedCode = 1
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(CAST(" & Code & " As float)) as mb FROM " & Tables & " where " & Code & " <> N''"
                dr = cmd.ExecuteReader
                dr.Read()

                Dim sh As Long
                sh = dr("mb")
                MaxRecoedCode = sh + 1
            End If
            Return MaxRecoedCode
        Catch ex As Exception
        End Try
    End Function

    Public Sub MAXRECORD_SheftNumber(ByVal StateMax As Boolean)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Sheft_Status"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Sheft_Number = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(Sheft_Number As float)) as mb FROM Sheft_Status where Sheft_ID <> N''"
            dr = cmd.ExecuteReader
            dr.Read()
            If dr.HasRows = True Then
                Dim sh As Long
                sh = dr("mb").ToString()
                If StateMax = True Then
                    Sheft_Number = sh + 1
                Else
                    Sheft_Number = sh
                End If
            End If
        End If
    End Sub

    Public Sub SlpitTime()
        Dim AMBM As String = ""
        Dim Time As String = Cls.get_time(True)
        Dim split As String() = New String() {":"}
        Dim itemsSplit As String() = Time.Split(split, StringSplitOptions.None)
        Dim Hour As String = itemsSplit(0).ToString()
        Dim Minute As String = itemsSplit(1).ToString()
        Dim Second As String = itemsSplit(2).ToString()

        If Hour = 1 Then : AMBM = "AM" : End If
        If Hour = 2 Then : AMBM = "AM" : End If
        If Hour = 3 Then : AMBM = "AM" : End If
        If Hour = 4 Then : AMBM = "AM" : End If
        If Hour = 5 Then : AMBM = "AM" : End If
        If Hour = 6 Then : AMBM = "AM" : End If
        If Hour = 7 Then : AMBM = "AM" : End If
        If Hour = 8 Then : AMBM = "AM" : End If
        If Hour = 9 Then : AMBM = "AM" : End If
        If Hour = 10 Then : AMBM = "AM" : End If
        If Hour = 11 Then : AMBM = "AM" : End If
        If Hour = 12 Then : AMBM = "PM" : End If
        If Hour = 13 Then : AMBM = "PM" : Hour = "01" : End If
        If Hour = 14 Then : AMBM = "PM" : Hour = "02" : End If
        If Hour = 15 Then : AMBM = "PM" : Hour = "03" : End If
        If Hour = 16 Then : AMBM = "PM" : Hour = "04" : End If
        If Hour = 17 Then : AMBM = "PM" : Hour = "05" : End If
        If Hour = 18 Then : AMBM = "PM" : Hour = "06" : End If
        If Hour = 19 Then : AMBM = "PM" : Hour = "07" : End If
        If Hour = 20 Then : AMBM = "PM" : Hour = "08" : End If
        If Hour = 21 Then : AMBM = "PM" : Hour = "09" : End If
        If Hour = 22 Then : AMBM = "PM" : Hour = "10" : End If
        If Hour = 23 Then : AMBM = "PM" : Hour = "11" : End If
        If Hour = 24 Then : AMBM = "AM" : Hour = "12" : End If

        TimeAmBm = Hour + ":" + Minute.Trim + ":" + Second.Trim + " " + AMBM

    End Sub

    Public Function CheckForInternetConnection() As Boolean
        Try
            Using client = New WebClient()
                Using stream = client.OpenRead("http://www.google.com")
                    Return True
                End Using
            End Using
        Catch
            Return False
        End Try
    End Function

    Public Sub LanguageKeyChange()
        Try
            Dim resault2 As String
            resault2 = mykey.GetValue("LANGUAGE", "ARABIC")
            If resault2 = "ARABIC" Then
                InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages().Item(1) 'تغيير لغة لوحة المفاتيح الى اللغه العربية
            ElseIf resault2 = "ENGLISH" Then
                InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages().Item(0) 'تغيير لغة لوحة المفاتيح الى اللغه الانجليزيه
            End If
        Catch ex As Exception
        End Try
    End Sub

    Public Sub ChooseLanguageProgram(ByVal frm As Form, ByVal RightToLeftAction As Boolean)
        Try
            System.Threading.Thread.CurrentThread.CurrentUICulture = New System.Globalization.CultureInfo(My.Settings.Language)
            frm.Controls.Clear()
            If RightToLeftAction = True Then
                If My.Settings.Language = "ar" Then
                    frm.RightToLeft = RightToLeft.No
                    frm.RightToLeftLayout = False
                Else
                    frm.RightToLeft = RightToLeft.Yes
                    frm.RightToLeftLayout = True
                End If
            End If
        Catch ex As Exception
        End Try
    End Sub

    Function GetCurrentBalanceCustVnd(ByVal CustVnd As String)
        Dim CurrentCust As Double = 0
        Dim CurrentVnd As Double = 0
        Dim XvnamntcreditCust As String = Cls.Get_Code_Value_Branch_More("Customers", "vnamntcredit", "Vendorname=N'" & CustVnd & "'")
        Dim XvnamntdebitCust As String = Cls.Get_Code_Value_Branch_More("Customers", "vnamntdebit", "Vendorname=N'" & CustVnd & "'")
        Dim XvnamntcreditVnd As String = Cls.Get_Code_Value_Branch_More("vendors", "vnamntcredit", "Vendorname=N'" & CustVnd & "'")
        Dim XvnamntdebitVnd As String = Cls.Get_Code_Value_Branch_More("vendors", "vnamntdebit", "Vendorname=N'" & CustVnd & "'")


        'If XvnamntdebitCust = 0 Then
        CurrentCust = Val(XvnamntcreditCust) - Val(XvnamntdebitCust)
        'Else
        '    CurrentCust = Val(XvnamntdebitCust) - Val(XvnamntcreditCust)
        'End If
        'If XvnamntdebitVnd = 0 Then
        CurrentVnd = Val(XvnamntcreditVnd) - Val(XvnamntdebitVnd)
        'Else
        '    CurrentVnd = Val(XvnamntdebitVnd) - Val(XvnamntcreditVnd)
        'End If
        Dim CurrentCustVnd As Double = CurrentVnd - CurrentCust
        Return CurrentCustVnd
    End Function

    Function GetCurrentBalanceCustVndCredit(ByVal CustVnd As String)
        Dim CurrentCust As Double = 0
        Dim CurrentVnd As Double = 0
        Dim XvnamntcreditCust As String = Cls.Get_Code_Value_Branch_More("Customers", "vnamntcredit", "Vendorname=N'" & CustVnd & "'")
        Dim XvnamntdebitCust As String = Cls.Get_Code_Value_Branch_More("Customers", "vnamntdebit", "Vendorname=N'" & CustVnd & "'")
        Dim XvnamntcreditVnd As String = Cls.Get_Code_Value_Branch_More("vendors", "vnamntcredit", "Vendorname=N'" & CustVnd & "'")
        Dim XvnamntdebitVnd As String = Cls.Get_Code_Value_Branch_More("vendors", "vnamntdebit", "Vendorname=N'" & CustVnd & "'")


        'If XvnamntdebitCust = 0 Then
        CurrentCust = Val(XvnamntcreditCust) - Val(XvnamntdebitCust)
        'Else
        '    CurrentCust = Val(XvnamntdebitCust) - Val(XvnamntcreditCust)
        'End If
        'If XvnamntdebitVnd = 0 Then
        CurrentVnd = Val(XvnamntcreditVnd) - Val(XvnamntdebitVnd)
        'Else
        '    CurrentVnd = Val(XvnamntdebitVnd) - Val(XvnamntcreditVnd)
        'End If
        Dim CurrentCustVnd As Double = CurrentVnd - CurrentCust
        Return CurrentCustVnd
    End Function

    Function GetDateNotBeenActivatedPrograms(ByVal Dates As DateTimePicker) As Boolean
        Dim Xdate As Boolean = False
        If PermtionName <> "مدير" Then
            If DateNotBeenActivatedPrograms = "NO" Then
                Dates.Enabled = True
                Xdate = True
            Else
                Dates.Enabled = False
                Xdate = False
            End If
        End If
        Return Xdate
    End Function

    Function TrimSpaceBeforeWord(ByVal text As String) As String
        Dim regexPattern As String = "\s+(?=\w)" ' نمط الاستدلال على النص للعثور على المسافة قبل الكلمة
        Dim trimmedText As String = Regex.Replace(text, regexPattern, "") ' إزالة المسافة قبل الكلمة باستخدام الاستدلال على النمط
        Return trimmedText
    End Function

    Function TrimSpaceBeforeSentence(ByVal text As String) As String
        Dim regexPattern As String = "(\.\s+)(\p{Ll})" ' نمط الاستدلال على النص للعثور على المسافة قبل بداية الجملة النصية
        Dim trimmedText As String = Regex.Replace(text, regexPattern, "$2") ' إزالة المسافة قبل بداية الجملة النصية باستخدام الاستدلال على النمط
        Return trimmedText
    End Function

    Function FormatNumberWithSeparators(number As Double) As String
        If ActivateFormatNumberWithSeparators = "YES" Then
            If Double.TryParse(number, New Double()) Then
                Return Double.Parse(number).ToString("N0")
            Else
                Return number
            End If
        Else
            Return number.ToString()
        End If


        'If ActivateFormatNumberWithSeparators = "NO" Then
        '    Return number.ToString()
        'Else
        '    Return String.Format("{0:N0}", number)
        '    'Return number.ToString("N0").Replace(",", ".")
        'End If
    End Function

    Public Function FormatNumberWithThousandSeparator(ByVal inputText As String) As String
        If Double.TryParse(inputText, New Double()) Then
            Return Double.Parse(inputText).ToString("N0")
        Else
            Return inputText
        End If
    End Function

    Public Function GetRemoveNumericSeparatorsValue(ByVal formattedText As String) As Double
        Return Double.Parse(formattedText.Replace(",", ""))
    End Function

End Module
