﻿Public Class Frm_Active
    Dim C_Bisns As New Cls_Constant
    Dim C_Validate As New Cls_Validation
    Dim sear As String
    Dim searhdd As String
    Dim seartotal As String
    Dim C_Data As New Cls_Users
    Dim cp As New Cet_Cpu
    Dim Serial_HDD As New Cet_SerialNumber_HDD
    Dim x(16) As String
    Dim txt As String
    Dim mm As Integer
    Dim cc As Integer

    Private Sub Btn_Active_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Btn_Active.Click

        Try
            If TextBox2.Text = "" Then : MsgBox("برجاء ادخال الكود المقابل او كود التفعيل") : Exit Sub : End If
            covert_()

            If Val(TextBox2.Text) = seartotal Then
                C_Data.Sb_Sp_InsertA_C(TextBox2.Text)
                activ_1 = True
                MDIParent1.Show()
                Me.Close()
            End If

            If Val(TextBox2.Text) <> sear Then
                'Dim x As Integer = C_Data.Sp_SelectCountOnFastBill
                'Dim m As Integer = C_Data.Sp_SelectCountOnFastItems
                Dim Bill As Integer = C_Data.Sp_SelectCountOnFastALL("Sales_Bill", "bill_No")
                Dim Items As Integer = C_Data.Sp_SelectCountOnFastALL("Items", "itm_id")
                Dim Emp As Integer = C_Data.Sp_SelectCountOnFastALL("Employees", "EMPID")
                Dim Exp As Integer = C_Data.Sp_SelectCountOnFastALL("Expenses", "id")
                Dim Cust As Integer = C_Data.Sp_SelectCountOnFastALL("Customers", "id")
                Dim Vendor As Integer = C_Data.Sp_SelectCountOnFastALL("vendors", "id")
                Dim MOR As Integer = C_Data.Sp_SelectCountOnFastALL("MaintenanceOrderRunning", "OrderDayID")
                Dim MCD As Integer = C_Data.Sp_SelectCountOnFastALL("Maintenance_Car_Data", "Car_Data_ID")
                Dim MFP As Integer = C_Data.Sp_SelectCountOnFastALL("ManufacturingProduct", "Manufacturing_ID")
                Dim MDN As Integer = C_Data.Sp_SelectCountOnFastALL("ManufacturingDismissalNotice", "id")
                Dim MOVES As Integer = C_Data.Sp_SelectCountOnFastALL("MOVES", "MOVID")

                'If Bill > 350 Or Items > 350 Or Emp > 10 Or Exp > 50 Or Cust > 60 Or Vendor > 60 Or MOR > 50 Or MCD > 50 Or MFP > 50 Or MDN > 50 Or MOVES > 50 Then
                'If Bill > 250 Or Items > 250 Or Emp > 10 Or Exp > 50 Or Cust > 60 Or Vendor > 20 Or MOR > 50 Or MCD > 50 Or MFP > 50 Or MDN > 50 Or MOVES > 50 Then
                'If Bill > 100 Or Items > 250 Or Emp > 10 Or Exp > 50 Or Cust > 60 Or Vendor > 10 Or MOR > 50 Or MCD > 50 Or MFP > 50 Or MDN > 50 Or MOVES > 50 Then
                'If Bill > 50 Or Items > 8000 Or Emp > 10 Or Exp > 50 Or Cust > 10 Or Vendor > 10 Or MOR > 50 Or MCD > 50 Or MFP > 50 Or MDN > 50 Or MOVES > 50 Then

                If Bill > 50 Or Items > 50 Or Emp > 10 Or Exp > 50 Or Cust > 10 Or Vendor > 10 Or MOR > 50 Or MCD > 50 Or MFP > 50 Or MDN > 50 Or MOVES > 50 Then
                    'MsgBox("هذا الرقم غير صحيح أتصل بصاحب البرنامج", MsgBoxStyle.Critical, "")
                    MsgBox(" لم تقم بشراء النسخه النهائية من البرنامج برجاء الاتصال بشركة فيت سوفت للبرمجيات م/ 01004052561  - 01156608276", MsgBoxStyle.Critical, "")
                    End
                    End If
                    'C_Validate.banna(frm_Main)
                    activ_1 = False
                    'MDIParent1.Text = "demo"
                    'MDIParent1.Show()
                    'Me.Dispose()
                End If
        Catch ex As Exception

        End Try

        'برجاء عدم محاولة تشغيل البرنامج باحدي الطرق الغير شرعيه لن تستطيع استخراج القيم لاتحاول لانه بيسجل كل القيم ومراقب حتي لاتتعرض للمسألة القانونية
    End Sub

    Sub covert_()
        txt = TextBox1.Text
        x(1) = Mid(txt, 1, 1)
        x(2) = Mid(txt, 2, 1)
        x(3) = Mid(txt, 3, 1)
        x(4) = Mid(txt, 4, 1)
        x(5) = Mid(txt, 5, 1)
        x(6) = Mid(txt, 6, 1)
        x(7) = Mid(txt, 7, 1)
        x(8) = Mid(txt, 8, 1)
        x(9) = Mid(txt, 9, 1)
        x(10) = Mid(txt, 10, 1)
        x(11) = Mid(txt, 11, 1)
        x(12) = Mid(txt, 12, 1)
        x(13) = Mid(txt, 13, 1)
        x(14) = Mid(txt, 14, 1)
        x(15) = Mid(txt, 15, 1)
        x(16) = Mid(txt, 16, 1)
        mm = Val(Asc(x(1))) + Val(Asc(x(2))) + Val(Asc(x(3))) + Val(Asc(x(4))) + Val(Asc(x(5))) + Val(Asc(x(6))) + Val(Asc(x(7))) + Val(Asc(x(8))) + Val(Asc(x(9))) + Val(Asc(x(10))) + Val(Asc(x(11))) + Val(Asc(x(12))) + Val(Asc(x(13))) + Val(Asc(x(14))) + Val(Asc(x(15))) + Val(Asc(x(16)))
        cc = Len(txt)
        sear = cc * mm * mm + cc * 10405256558860 + 10 * 10405256558860 / 100

        '=============================================================================

        'txt = txtSerialNumberHDD.Text
        'x(1) = Mid(txt, 1, 1)
        'x(2) = Mid(txt, 2, 1)
        'x(3) = Mid(txt, 3, 1)
        'x(4) = Mid(txt, 4, 1)
        'x(5) = Mid(txt, 5, 1)
        'x(6) = Mid(txt, 6, 1)
        'x(7) = Mid(txt, 7, 1)
        'x(8) = Mid(txt, 8, 1)
        'x(9) = Mid(txt, 9, 1)
        'x(10) = Mid(txt, 10, 1)
        'x(11) = Mid(txt, 11, 1)
        'x(12) = Mid(txt, 12, 1)
        'x(13) = Mid(txt, 13, 1)
        'x(14) = Mid(txt, 14, 1)
        'mm = Val(Asc(x(1))) + Val(Asc(x(2))) + Val(Asc(x(3))) + Val(Asc(x(4))) + Val(Asc(x(5))) + Val(Asc(x(6))) + Val(Asc(x(7))) + Val(Asc(x(8))) + Val(Asc(x(9))) + Val(Asc(x(10))) + Val(Asc(x(11))) + Val(Asc(x(12))) + Val(Asc(x(13))) + Val(Asc(x(14)))
        'cc = Len(txt)
        'searhdd = cc * mm * mm + cc * 10405256558860 + 10 * 10405256558860 / 100

        'seartotal = sear - searhdd
        seartotal = sear
    End Sub

    Private Sub Frm_Active_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing

    End Sub
    Private Sub Frm_Active_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Try
            'Dim SerialNumberHDD As String = mykey.GetValue("SerialNumberHDD", "")
            'If SerialNumberHDD = "" Then
            '    txtSerialNumberHDD.Text = Serial_HDD.SerialNumber_HDD
            'Else
            '    txtSerialNumberHDD.Text = SerialNumberHDD
            'End If

            TextBox1.Text = cp.ProcessorId
            covert_()
            TextBox2.Select()
            Dim x As Boolean = C_Data.Fn_Sp_SelectA_C(seartotal)
            If x = True Then
                Me.Close()
                activ_1 = True
            Else
                MsgBox(" لم تقم بشراء النسخه النهائية من البرنامج برجاء الاتصال بشركة فيت سوفت للبرمجيات  م/ 01004052561 ", MsgBoxStyle.Critical, "")
            End If
            ReturnbyActive = x
        Catch ex As Exception

        End Try
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        End
    End Sub

    Private Sub TextBox2_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles TextBox2.KeyUp
        If e.KeyCode = 13 Then
            Btn_Active.PerformClick()
        End If

    End Sub

End Class
