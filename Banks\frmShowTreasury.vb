﻿Imports CrystalDecisions.CrystalReports.Engine
Public Class frmShowTreasury
    Dim WithEvents BS As New BindingSource

    Private Sub frmShowTreasury_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        FillComboBox()
        GetData()
    End Sub

    Private Sub FillComboBox()
        cmbBank.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select distinct Bank from Banks"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            cmbBank.Items.Add(dr("Bank"))
        Loop

        cmbDocumentNumber.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select distinct DocumentNumber from Banks"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            cmbDocumentNumber.Items.Add(dr("DocumentNumber"))
        Loop


    End Sub

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT CSHID AS [مسلسل],DateMovement AS [تاريخ الحركة], TypeMovement AS [نوع الحركة], PaymentMethod AS [طريقة الدفع],DocumentNumber as [رقم المستند],Debtor as [صرف],Creditor as [ايداع],CurrentBalance as [الرصيد الحالى],DateCollection as [تاريخ التحصيل],Bank as [البنك],Notes as [ملاحظات] FROM Cashier where CSHID <> N''"

        If cmbBank.Text <> Nothing Then
            S = S & "and Bank =N'" & cmbBank.Text.Trim & "'"
        End If
        If cmbDocumentNumber.Text <> Nothing Then
            S = S & "and DocumentNumber =N'" & cmbDocumentNumber.Text.Trim & "'"
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & "and DateMovement >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and DateMovement <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

        'DataGridView1.Columns(0).Width = 40
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub btnEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnEdit.Click
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        EditItmId = DataGridView1.SelectedRows(0).Cells(0).Value
        Action = "Edit"
        frmTreasury.Show()
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim NumberID As String
            NumberID = DataGridView1.SelectedRows(0).Cells(0).Value


            cmd.CommandText = "delete from Cashier where CSHID =N'" & NumberID & "'" : cmd.ExecuteNonQuery()
        Next
        GetData()
    End Sub

    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click
        Dim txtFROMDate As String
        Dim txtToDate As String
        Dim f As New Frm_PrintReports
        Dim rpt As New rptTreasury
        Dim txt As TextObject
        txtFROMDate = Format(Me.DateTimePicker1.Value, "yyy, MM, dd, 00, 00, 000")
        txtToDate = Format(Me.DateTimePicker2.Value, "yyy, MM, dd, 00, 00, 00")
        'On Error Resume Next
        GETSERVERNAMEANDDATABASENAME(rpt, DataBaseName, PasswordServer, UserNameServer)
        If ChkWithoutDate.Checked = False Then
            If cmbBank.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{Cashier.DateMovement} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")AND{Cashier.Bank} =N'" & cmbBank.Text & "'"
            End If
            If cmbDocumentNumber.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{Cashier.DateMovement} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")AND{Cashier.DocumentNumber} =N'" & cmbDocumentNumber.Text & "'"
            End If
            txt = rpt.Section1.ReportObjects("txtDate")
            txt.Text = "خلال الفترة من" & Format(Me.DateTimePicker1.Value, "dd - MM - yyyy") & " الى " & Format(Me.DateTimePicker2.Value, "dd - MM - yyyy")
        Else
            If cmbBank.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{Cashier.Bank} =N'" & cmbBank.Text & "'"
            End If
            If cmbDocumentNumber.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{Cashier.DocumentNumber} =N'" & cmbDocumentNumber.Text & "'"
            End If
        End If
        AddReportView()
        Dim txtname, txtNameAr, txtNameEn As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير بحركة الخزينة"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        f.CrystalReportViewer1.ReportSource = rpt
        f.CrystalReportViewer1.Zoom(95%)
        f.CrystalReportViewer1.RefreshReport()
        f.Text = "تقرير بحركة الخزينة"
        f.Show()
    End Sub

    Private Sub btnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnShow.Click
        GetData()
    End Sub
End Class