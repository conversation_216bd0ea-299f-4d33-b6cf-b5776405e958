﻿Imports System.IO

Public Class FrmCats
    Dim RateDiscTinPriceAfter, RateDiscSalPriceAfter, RateDiscWholePriceAfter, RateDiscWholeWholePriceAfter As Double

    Private Sub btnsave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnsave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If txtcats.Text.Trim = "" Then MsgBox("من فضلا أدخل اسم المجموع", MsgBoxStyle.Exclamation) : txtcats.Focus() : Exit Sub

        REM للتاكد من عدم التكرار
        Dim TableName, FieldName, StringFind As String
        Dim S As String
        TableName = "groups"
        FieldName = "g_name"
        StringFind = txtcats.Text
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select * From " & TableName & " Where " & FieldName & "=N'" & StringFind & "'"

        cmd.CommandType = CommandType.Text
        cmd.CommandText = S
        dr = cmd.ExecuteReader
        'dr.Read()

        If dr.Has<PERSON>ows = True Then
            MsgBox("تم اضافه هذه المجموعه مسبقا", MsgBoxStyle.Exclamation)
            txtcats.Focus()
            Exit Sub
        End If


        Dim QuickSearch As String
        If chkGroupsQuickSearch.Checked = False Then
            QuickSearch = "0"
        Else
            QuickSearch = "1"
        End If

        Dim TypePlusDiscRateSalPrice, TypePlusDiscRateWholePrice, TypePlusDiscRateWholeWholePrice As Double
        If rdoDiscRateSalPrice.Checked = True Then
            TypePlusDiscRateSalPrice = 0
        Else
            TypePlusDiscRateSalPrice = 1
        End If
        If rdoDiscRateWholePrice.Checked = True Then
            TypePlusDiscRateWholePrice = 0
        Else
            TypePlusDiscRateWholePrice = 1
        End If
        If rdoDiscRateWholeWholePrice.Checked = True Then
            TypePlusDiscRateWholeWholePrice = 0
        Else
            TypePlusDiscRateWholeWholePrice = 1
        End If

        Dim IsHideGroup As String
        If chkIsHideGroup.Checked = True Then
            IsHideGroup = 1
        Else
            IsHideGroup = 0
        End If

        REM لحفظ المجموعه
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into groups(Company_Branch_ID,g_name,UserName,QuickSearch,RateDiscTinPrice,RateDiscSalPrice,RateDiscWholePrice,RateDiscWholeWholePrice,TypePlusDiscRateSalPrice,TypePlusDiscRateWholePrice,TypePlusDiscRateWholeWholePrice,IsHide) values (N'" & Company_Branch_ID & "',N'" & StringFind & "',N'" & UserName & "',N'" & QuickSearch & "',N'" & txtRateDiscTinPrice.Text & "',N'" & txtRateDiscSalPrice.Text & "',N'" & txtRateDiscWholePrice.Text & "',N'" & txtRateDiscWholeWholePrice.Text & "',N'" & TypePlusDiscRateSalPrice & "',N'" & TypePlusDiscRateWholePrice & "',N'" & TypePlusDiscRateWholePrice & "',N'" & IsHideGroup & "')"
        cmd.CommandText = S
        cmd.ExecuteNonQuery()

        If ConnectOnlineStore = "YES" Then
            Cos.AddCategory(StringFind, IsHideGroup)
        End If

        Header()

        If OpenFileDialog1.FileName <> "" Then
            ImageUpdate()
            PicGroup.Image = Nothing
        End If
        txtcats.Text = ""
        txtRateDiscSalPrice.Text = ""
        txtRateDiscTinPrice.Text = ""
        txtRateDiscWholePrice.Text = ""
        txtRateDiscWholeWholePrice.Text = ""
        Bra.Fil("groups", "g_name", FrmItemsNew.cmbcats)
        'Bra.Fil("groups", "g_name", Frmimport.cmbcats)

    End Sub

    Private Sub ImageUpdate()
        connectionStringOpen()
        Dim cmd1 As SqlClient.SqlCommand = New SqlClient.SqlCommand
        cmd1.CommandType = CommandType.Text
        cmd1.Connection = Cn

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        Dim cmd2 As SqlClient.SqlCommand = New SqlClient.SqlCommand
        S = " Update Groups SET  Groups_Image = @Groups_Image WHERE G_name =N'" & txtcats.Text & "'"
        cmd2.CommandType = CommandType.Text
        cmd2.Connection = Cn
        Dim fs As FileStream = New FileStream(OpenFileDialog1.FileName, FileMode.Open, FileAccess.Read)
        Dim r As BinaryReader = New BinaryReader(fs)
        Dim FileByteArray(fs.Length - 1) As Byte
        r.Read(FileByteArray, 0, CInt(fs.Length))
        With cmd2
            .CommandType = CommandType.Text
            .Connection = Cn
            .Parameters.Add("@Groups_Image", SqlDbType.Image).Value = FileByteArray
            .CommandText = S
        End With
        cmd2.ExecuteNonQuery()
    End Sub

    Private Sub txtcats_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtcats.KeyUp
        If e.KeyCode = 13 Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub FrmCats_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Header()
        PanelItemDiscountRate.Top = 5000

        If LanguageMainProgram = "العربية" Then
            'SetArabic()
        ElseIf LanguageMainProgram = "English" Then
            SetEnglish()
        End If
    End Sub

    Private Sub btndelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btndelete.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView2.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If

        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        For i As Integer = 0 To DataGridView2.SelectedRows.Count - 1
            If DataGridView2.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView2.SelectedRows.Count) = 0 Then Beep() : Exit Sub

            Dim id As String = DataGridView2.SelectedRows(i).Cells(0).Value
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete  from Groups where id =N'" & id & "'" : cmd.ExecuteNonQuery()
        Next

        If ConnectOnlineStore = "YES" Then
            Cos.DeleteCategory(txtcats.Text)
        End If

        Header()
        txtcats.Text = ""
        txtRateDiscSalPrice.Text = ""
        txtRateDiscTinPrice.Text = ""
        txtRateDiscWholePrice.Text = ""
        txtRateDiscWholeWholePrice.Text = ""
        txtcats.Focus()

    End Sub

    Private Sub txtsearch_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtsearch.TextChanged
        'If txtsearch.Text.Trim = "" Then
        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    If PermtionName = "مدير" Then
        '        cmd.CommandText = "select G_name from groups"
        '    Else
        '        cmd.CommandText = "select G_name from groups where Company_Branch_ID =N'" & Company_Branch_ID & "'"
        '    End If
        '    dr = cmd.ExecuteReader
        '    Do While dr.Read = True
        '        ListBox1.Items.Add(dr(0))
        '    Loop
        'End If
        'Dim X As String = txtsearch.Text & "%"
        'ListBox1.Items.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'If PermtionName = "مدير" Then
        '    cmd.CommandText = "select G_name from groups where g_name like N'" & X & "'"
        'Else
        '    cmd.CommandText = "select G_name from groups where g_name like N'" & X & "' and Company_Branch_ID =N'" & Company_Branch_ID & "'"
        'End If
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    ListBox1.Items.Add(dr(0))
        'Loop





        If txtsearch.Text.Trim = "" Then
            Header()
        Else
            Dim X As String = txtsearch.Text & "%"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                S = "select id as [كود],G_name as [أسم المجموعة] from Groups where g_name like N'" & X & "'"
            Else
                S = "select id as [كود],G_name as [أسم المجموعة] from Groups where g_name like N'" & X & "' and Company_Branch_ID =N'" & Company_Branch_ID & "'"
            End If
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DataGridView2.DataSource = Cls.PopulateDataView(dr)
            DataGridView2.Columns(0).Visible = False
        End If
    End Sub

    Private Sub btnChooseImage_Click(sender As Object, e As EventArgs) Handles btnChooseImage.Click
        On Error Resume Next
        OpenFileDialog1.Filter = "Image Files (*.png *.jpg *.bmp *.JPE *.JPEG) |*.png; *.jpg; *.bmp; *.JPE; *.JPEG|All Files(*.*) |*.*"
        With Me.OpenFileDialog1
            .FilterIndex = 1
            .Title = "أختر صورة المجموعة"
            .ShowDialog()
            If Len(.FileName) > 0 Then
                PicGroup.Image = Image.FromFile(OpenFileDialog1.FileName)
            End If
        End With
    End Sub

    Private Sub ListBox1_SelectedIndexChanged(sender As Object, e As EventArgs)
        SHOWPHOTO()
    End Sub

    Private Sub SHOWPHOTO()
        Try
            Dim G_name As String = ""
            For i As Integer = 0 To DataGridView2.SelectedRows.Count - 1
                If DataGridView2.RowCount = 0 Then Beep() : Exit Sub
                If (DataGridView2.SelectedRows.Count) = 0 Then Beep() : Exit Sub
                G_name = DataGridView2.SelectedRows(i).Cells(1).Value
            Next

            connectionStringOpen()
            Dim sql As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            sql = "SELECT Groups_Image FROM Groups WHERE G_name =N'" & G_name & "'"
            Dim cmd As SqlClient.SqlCommand = New SqlClient.SqlCommand(sql, Cn)
            Dim by() As Byte
            by = cmd.ExecuteScalar()
            If (by.Length > 0) Then
                Dim stream As New MemoryStream(by, True)
                stream.Write(by, 0, by.Length)
                PicGroup.Image = New Bitmap(stream)
                stream.Close()
            Else
                Me.PicGroup.Image = Nothing
            End If
        Catch ex As Exception
            Me.PicGroup.Image = Nothing
        End Try
    End Sub

    Private Sub btnItemDiscountRate_Click(sender As Object, e As EventArgs) Handles btnItemDiscountRate.Click
        txtRateDiscTinPrice.Focus()
        txtRateDiscTinPrice.Select()
        PanelItemDiscountRate.Top = 100
        PanelItemDiscountRate.Size = New System.Drawing.Size(450, 210)
    End Sub

    Private Sub btnCloseItemDiscountRate_Click(sender As Object, e As EventArgs) Handles btnCloseItemDiscountRate.Click
        PanelItemDiscountRate.Top = 5000
    End Sub

    Private Sub txtRateDiscTinPrice_KeyUp(sender As Object, e As KeyEventArgs) Handles txtRateDiscTinPrice.KeyUp
        If e.KeyCode = 13 Then
            txtRateDiscSalPrice.Focus()
            txtRateDiscSalPrice.Select()
        End If
    End Sub

    Private Sub txtRateDiscSalPrice_KeyUp(sender As Object, e As KeyEventArgs) Handles txtRateDiscSalPrice.KeyUp
        If e.KeyCode = 13 Then
            txtRateDiscWholePrice.Focus()
            txtRateDiscWholePrice.Select()
        End If
    End Sub

    Private Sub txtRateDiscWholePrice_KeyUp(sender As Object, e As KeyEventArgs) Handles txtRateDiscWholePrice.KeyUp
        If e.KeyCode = 13 Then
            txtRateDiscWholeWholePrice.Focus()
            txtRateDiscWholeWholePrice.Select()
        End If
    End Sub

    Private Sub txtRateDiscWholeWholePrice_KeyUp(sender As Object, e As KeyEventArgs) Handles txtRateDiscWholeWholePrice.KeyUp
        If e.KeyCode = 13 Then
            PanelItemDiscountRate.Top = 5000
        End If
    End Sub

    Private Sub txtRateDiscTinPrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateDiscTinPrice.TextChanged
        MyVars.CheckNumber(txtRateDiscTinPrice)
    End Sub

    Private Sub txtRateDiscSalPrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateDiscSalPrice.TextChanged
        MyVars.CheckNumber(txtRateDiscSalPrice)
    End Sub

    Private Sub txtRateDiscWholePrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateDiscWholePrice.TextChanged
        MyVars.CheckNumber(txtRateDiscWholePrice)
    End Sub

    Private Sub txtRateDiscWholeWholePrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateDiscWholeWholePrice.TextChanged
        MyVars.CheckNumber(txtRateDiscWholeWholePrice)
    End Sub

    Private Sub Header()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If LanguageMainProgram = "العربية" Then
            If PermtionName = "مدير" Then
                S = "select id as [كود],G_name as [" & AR_Language.GroupName & "] from Groups order by G_name"
            Else
                S = "select id as [كود],G_name as [" & AR_Language.GroupName & "] from Groups where Company_Branch_ID =N'" & Company_Branch_ID & "' order by G_name"
            End If
        ElseIf LanguageMainProgram = "English" Then
            If PermtionName = "مدير" Then
                S = "select id as [كود],G_name as [" & ENT_Language.GroupName & "] from Groups order by G_name"
            Else
                S = "select id as [كود],G_name as [" & ENT_Language.GroupName & "] from Groups where Company_Branch_ID =N'" & Company_Branch_ID & "' order by G_name"
            End If
        End If


        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView2.DataSource = Cls.PopulateDataView(dr)
        DataGridView2.Columns(0).Visible = False
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs) Handles btnEdit.Click
        If txtcats.Text.Trim = "" Then MsgBox("من فضلا أدخل اسم المجموعة", MsgBoxStyle.Exclamation) : txtcats.Focus() : Exit Sub

        Dim QuickSearch As String
        If chkGroupsQuickSearch.Checked = False Then
            QuickSearch = "0"
        Else
            QuickSearch = "1"
        End If

        Dim TypePlusDiscRateSalPrice, TypePlusDiscRateWholePrice, TypePlusDiscRateWholeWholePrice As Double
        If rdoDiscRateSalPrice.Checked = True Then
            TypePlusDiscRateSalPrice = 0
        Else
            TypePlusDiscRateSalPrice = 1
        End If
        If rdoDiscRateWholePrice.Checked = True Then
            TypePlusDiscRateWholePrice = 0
        Else
            TypePlusDiscRateWholePrice = 1
        End If
        If rdoDiscRateWholeWholePrice.Checked = True Then
            TypePlusDiscRateWholeWholePrice = 0
        Else
            TypePlusDiscRateWholeWholePrice = 1
        End If

        Dim IsHideGroup As String
        If chkIsHideGroup.Checked = True Then
            IsHideGroup = 1
        Else
            IsHideGroup = 0
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update Groups set "
        S = S & "G_name =N'" & txtcats.Text & "',QuickSearch =N'" & QuickSearch & "',RateDiscTinPrice =N'" & txtRateDiscTinPrice.Text & "',RateDiscSalPrice =N'" & txtRateDiscSalPrice.Text & "',RateDiscWholePrice =N'" & txtRateDiscWholePrice.Text & "',RateDiscWholeWholePrice =N'" & txtRateDiscWholeWholePrice.Text & "',TypePlusDiscRateSalPrice =N'" & TypePlusDiscRateSalPrice & "',TypePlusDiscRateWholePrice =N'" & TypePlusDiscRateWholePrice & "',TypePlusDiscRateWholeWholePrice =N'" & TypePlusDiscRateWholeWholePrice & "',IsHide =N'" & IsHideGroup & "' where id =N'" & lblG_name.Text & "'"
        cmd.CommandText = S : H = cmd.ExecuteNonQuery()



        Dim aray_id As New ArrayList : Dim aray_itm_id As New ArrayList : Dim aray_TinPrice As New ArrayList : Dim aray_SalPrice As New ArrayList : Dim aray_WholePrice As New ArrayList : Dim aray_WholeWholePrice As New ArrayList
        Dim itm_id As String = "" : Dim itm_cat As String = ""
        aray_id.Clear() : aray_itm_id.Clear() : aray_TinPrice.Clear() : aray_SalPrice.Clear() : aray_WholePrice.Clear() : aray_WholeWholePrice.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select id,itm_id,TinPrice,SalPrice,WholePrice,WholeWholePrice from  Items where group_name =N'" & txtcats.Text & "'" : dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_id.Add(dr("id")) : aray_itm_id.Add(dr("itm_id")) : aray_TinPrice.Add(dr("TinPrice")) : aray_SalPrice.Add(dr("SalPrice")) : aray_WholePrice.Add(dr("WholePrice")) : aray_WholeWholePrice.Add(dr("WholeWholePrice"))
        Loop

        For i As Integer = 0 To aray_id.Count - 1
            itm_id = aray_itm_id(i).ToString

            GetRateDiscPriceAfter(aray_TinPrice(i).ToString, aray_SalPrice(i).ToString, aray_WholePrice(i).ToString, aray_WholeWholePrice(i).ToString)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "update Items set RateDiscTinPrice =N'" & txtRateDiscTinPrice.Text & "',RateDiscSalPrice =N'" & txtRateDiscSalPrice.Text & "',RateDiscWholePrice =N'" & txtRateDiscWholePrice.Text & "',RateDiscWholeWholePrice =N'" & txtRateDiscWholeWholePrice.Text & "',RateDiscTinPriceAfter =N'" & RateDiscTinPriceAfter & "',RateDiscSalPriceAfter =N'" & RateDiscSalPriceAfter & "',RateDiscWholePriceAfter =N'" & RateDiscWholePriceAfter & "',RateDiscWholeWholePriceAfter =N'" & RateDiscWholeWholePriceAfter & "',TypePlusDiscRateSalPrice =N'" & TypePlusDiscRateSalPrice & "',TypePlusDiscRateWholePrice =N'" & TypePlusDiscRateWholePrice & "',TypePlusDiscRateWholeWholePrice =N'" & TypePlusDiscRateWholeWholePrice & "' where itm_id =N'" & itm_id & "'"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        If OpenFileDialog1.FileName <> "" Then
            ImageUpdate()
            PicGroup.Image = Nothing
        End If

        If ConnectOnlineStore = "YES" Then
            Cos.UpdateCategory(txtcats.Text, IsHideGroup)
        End If

        Header()
        txtcats.Text = ""
        txtRateDiscSalPrice.Text = ""
        txtRateDiscTinPrice.Text = ""
        txtRateDiscWholePrice.Text = ""
        txtRateDiscWholeWholePrice.Text = ""
        txtcats.Focus()
    End Sub

    Private Sub DataGridView2_DoubleClick(sender As Object, e As EventArgs) Handles DataGridView2.DoubleClick
        Dim Code As String
        Dim QuickSearch As String = ""

        Code = DataGridView2.SelectedRows(0).Cells(0).Value

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select  id,G_name,QuickSearch,RateDiscTinPrice,RateDiscSalPrice,RateDiscWholePrice,RateDiscWholeWholePrice,TypePlusDiscRateSalPrice,TypePlusDiscRateWholePrice,TypePlusDiscRateWholeWholePrice,IsHide from Groups where id = " & Code & ""
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            lblG_name.Text = dr(0).ToString()
            txtcats.Text = dr(1).ToString()
            QuickSearch = dr(2).ToString()
            txtRateDiscTinPrice.Text = dr(3).ToString()
            txtRateDiscSalPrice.Text = dr(4).ToString()
            txtRateDiscWholePrice.Text = dr(5).ToString()
            txtRateDiscWholeWholePrice.Text = dr(6).ToString()
            If dr(7).ToString = "0" Then
                rdoDiscRateSalPrice.Checked = True
            Else
                rdoPlusRateSalPrice.Checked = True
            End If
            If dr(8).ToString = "0" Then
                rdoDiscRateWholePrice.Checked = True
            Else
                rdoPlusRateWholePrice.Checked = True
            End If
            If dr(9).ToString = "0" Then
                rdoDiscRateWholeWholePrice.Checked = True
            Else
                rdoPlusRateWholeWholePrice.Checked = True
            End If
            If dr(10).ToString = "False" Then
                chkIsHideGroup.Checked = False
            Else
                chkIsHideGroup.Checked = True
            End If
        End If

        If QuickSearch = "0" Then
            chkGroupsQuickSearch.Checked = False
        Else
            chkGroupsQuickSearch.Checked = True
        End If

    End Sub

    Private Sub GetRateDiscPriceAfter(ByVal tinprice As Double, ByVal priseSal As Double, ByVal WholePrice As Double, ByVal WholeWholePrice As Double)

        RateDiscTinPriceAfter = Val((Val(tinprice) * (100 - Val(txtRateDiscTinPrice.Text))) / 100)
        RateDiscTinPriceAfter = Math.Round(Val(RateDiscTinPriceAfter), 2)

        If rdoDiscRateSalPrice.Checked = True Then
            RateDiscSalPriceAfter = Val((Val(priseSal) * (100 - Val(txtRateDiscSalPrice.Text))) / 100)
            RateDiscSalPriceAfter = Math.Round(Val(RateDiscSalPriceAfter), 2)
        Else
            RateDiscSalPriceAfter = Val((Val(priseSal) * (100 + Val(txtRateDiscSalPrice.Text))) / 100)
            RateDiscSalPriceAfter = Math.Round(Val(RateDiscSalPriceAfter), 2)
        End If
        If rdoDiscRateWholePrice.Checked = True Then
            RateDiscWholePriceAfter = Val((Val(WholePrice) * (100 - Val(txtRateDiscWholePrice.Text))) / 100)
            RateDiscWholePriceAfter = Math.Round(Val(RateDiscWholePriceAfter), 2)
        Else
            RateDiscWholePriceAfter = Val((Val(WholePrice) * (100 + Val(txtRateDiscWholePrice.Text))) / 100)
            RateDiscWholePriceAfter = Math.Round(Val(RateDiscWholePriceAfter), 2)
        End If
        If rdoDiscRateWholeWholePrice.Checked = True Then
            RateDiscWholeWholePriceAfter = Val((Val(WholeWholePrice) * (100 - Val(txtRateDiscWholeWholePrice.Text))) / 100)
            RateDiscWholeWholePriceAfter = Math.Round(Val(RateDiscWholeWholePriceAfter), 2)
        Else
            RateDiscWholeWholePriceAfter = Val((Val(WholeWholePrice) * (100 + Val(txtRateDiscWholeWholePrice.Text))) / 100)
            RateDiscWholeWholePriceAfter = Math.Round(Val(RateDiscWholeWholePriceAfter), 2)
        End If
    End Sub

    Private Sub SetEnglish()
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.RightToLeftLayout = True
        Label26.Text = ENT_Language.RegisterGroupsItems
        btnsave.Text = ENT_Language.Added
        btndelete.Text = ENT_Language.Delete
        btnEdit.Text = ENT_Language.Edit
        chkGroupsQuickSearch.Text = ENT_Language.HideInSearch
        chkIsHideGroup.Text = ENT_Language.HideTheGroup
        btnChooseImage.Text = ENT_Language.ChooseGroupPhoto
        Me.Text = ENT_Language.RegisterGroupsItems
    End Sub

    Private Sub SetArabic()
        Me.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.RightToLeftLayout = True
        Label26.Text = AR_Language.RegisterGroupsItems
        btnsave.Text = AR_Language.Added
        btndelete.Text = AR_Language.Delete
        btnEdit.Text = AR_Language.Edit
        chkGroupsQuickSearch.Text = AR_Language.HideInSearch
        chkIsHideGroup.Text = AR_Language.HideTheGroup
        btnChooseImage.Text = AR_Language.ChooseGroupPhoto
        Me.Text = AR_Language.RegisterGroupsItems
        btnChooseImage.Text = AR_Language.ChooseGroupPhoto


    End Sub
End Class