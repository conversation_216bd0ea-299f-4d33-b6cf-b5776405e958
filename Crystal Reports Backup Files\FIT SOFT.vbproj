﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{F7756509-821A-4041-A713-01AFCB9A7426}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>FIT_SOFT.My.MyApplication</StartupObject>
    <RootNamespace>FIT_SOFT</RootNamespace>
    <AssemblyName>FIT SOFT</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsForms</MyType>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <OptionExplicit>On</OptionExplicit>
    <OptionCompare>Binary</OptionCompare>
    <OptionStrict>Off</OptionStrict>
    <OptionInfer>On</OptionInfer>
    <SignManifests>true</SignManifests>
    <ManifestCertificateThumbprint>179A451EBD0D2E8E87F75F7C9D55F2750E57250F</ManifestCertificateThumbprint>
    <ManifestKeyFile>FIT SOFT_TemporaryKey.pfx</ManifestKeyFile>
    <ApplicationIcon>Resources\Network.ico</ApplicationIcon>
    <GenerateManifests>true</GenerateManifests>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <TargetFrameworkProfile />
    <PublishUrl>D:\Setup\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <AutorunEnabled>true</AutorunEnabled>
    <ApplicationRevision>4</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <BootstrapperComponentsLocation>Relative</BootstrapperComponentsLocation>
    <BootstrapperComponentsUrl>C:\Users\<USER>\Desktop\Setup FIT</BootstrapperComponentsUrl>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>FIT SOFT.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>FIT SOFT.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>My Project\app.manifest</ApplicationManifest>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BarcodeLib.Barcode.CrystalReports, Version=5.0.0.50227, Culture=neutral, PublicKeyToken=16333e246fee2590, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\BarcodeLib.Barcode.CrystalReports.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.CrystalReports.Design, Version=13.0.3500.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL" />
    <Reference Include="CrystalDecisions.CrystalReports.Engine, Version=13.0.3500.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL" />
    <Reference Include="CrystalDecisions.Enterprise.Viewing.ReportSource, Version=13.0.3500.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="CrystalDecisions.ReportSource, Version=13.0.3500.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL" />
    <Reference Include="CrystalDecisions.Shared, Version=13.0.3500.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL" />
    <Reference Include="CrystalDecisions.Windows.Forms, Version=13.0.3500.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL" />
    <Reference Include="IrisSkin2, Version=2006.3.22.45, Culture=neutral, PublicKeyToken=0ed4c5f2bbf81ac0, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\IrisSkin2.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.ConnectionInfo, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Microsoft.SqlServer.ConnectionInfo.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.Sdk.Sfc, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Microsoft.SqlServer.Management.Sdk.Sfc.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Smo, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Microsoft.SqlServer.Smo.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.SmoExtended, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Microsoft.SqlServer.SmoExtended.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.SqlEnum, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Microsoft.SqlServer.SqlEnum.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Design" />
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Messaging" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\WINDOWS\assembly\GAC_MSIL\System.Web.Services\2.0.0.0__b03f5f7f11d50a3a\System.Web.Services.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AboutBox1.Designer.vb">
      <DependentUpon>AboutBox1.vb</DependentUpon>
    </Compile>
    <Compile Include="AboutBox1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Accounts\frmAccounts.Designer.vb">
      <DependentUpon>frmAccounts.vb</DependentUpon>
    </Compile>
    <Compile Include="Accounts\frmAccounts.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Cust_Pay_CheckType.designer.vb">
      <DependentUpon>Cust_Pay_CheckType.vb</DependentUpon>
    </Compile>
    <Compile Include="Cust_Pay_CheckType.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmPriceOffer.designer.vb">
      <DependentUpon>FrmPriceOffer.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmPriceOffer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmUpdateDataBase.Designer.vb">
      <DependentUpon>FrmUpdateDataBase.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmUpdateDataBase.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmUpdateDataBaseProgressBar.Designer.vb">
      <DependentUpon>FrmUpdateDataBaseProgressBar.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmUpdateDataBaseProgressBar.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Employees\rptSalaryEmp.vb">
      <DependentUpon>rptSalaryEmp.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Frm_AdjustmentsStores_List.Designer.vb">
      <DependentUpon>Frm_AdjustmentsStores_List.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_AdjustmentsStores_List.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Maintenance\Frm_Maintenance_ReceivingCar.designer.vb">
      <DependentUpon>Frm_Maintenance_ReceivingCar.vb</DependentUpon>
    </Compile>
    <Compile Include="Maintenance\Frm_Maintenance_ReceivingCar.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Maintenance\Frm_Maintenance_Recipient.designer.vb">
      <DependentUpon>Frm_Maintenance_Recipient.vb</DependentUpon>
    </Compile>
    <Compile Include="Maintenance\Frm_Maintenance_Recipient.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Maintenance\Frm_Maintenance_Supervisor.designer.vb">
      <DependentUpon>Frm_Maintenance_Supervisor.vb</DependentUpon>
    </Compile>
    <Compile Include="Maintenance\Frm_Maintenance_Supervisor.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Maintenance\Frm_Maintenance_Car_Data.designer.vb">
      <DependentUpon>Frm_Maintenance_Car_Data.vb</DependentUpon>
    </Compile>
    <Compile Include="Maintenance\Frm_Maintenance_Car_Data.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Maintenance\Frm_Other_Income.designer.vb">
      <DependentUpon>Frm_Other_Income.vb</DependentUpon>
    </Compile>
    <Compile Include="Maintenance\Frm_Other_Income.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Maintenance\Frm_Other_Income_Category.designer.vb">
      <DependentUpon>Frm_Other_Income_Category.vb</DependentUpon>
    </Compile>
    <Compile Include="Maintenance\Frm_Other_Income_Category.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Maintenance\Rpt_SalesNotTax_2.vb">
      <DependentUpon>Rpt_SalesNotTax_2.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Maintenance\Rpt_Sales_2.vb">
      <DependentUpon>Rpt_Sales_2.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Manufacturing\Frm_Product_Dismissal_Notice_Factory.Designer.vb">
      <DependentUpon>Frm_Product_Dismissal_Notice_Factory.vb</DependentUpon>
    </Compile>
    <Compile Include="Manufacturing\Frm_Product_Dismissal_Notice_Factory.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Manufacturing\Frm_Product_Order_Filling_Manufacturing.designer.vb">
      <DependentUpon>Frm_Product_Order_Filling_Manufacturing.vb</DependentUpon>
    </Compile>
    <Compile Include="Manufacturing\Frm_Product_Order_Filling_Manufacturing.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmSales.designer.vb">
      <DependentUpon>FrmSales.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmSales.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Capital_Owner.Designer.vb">
      <DependentUpon>Frm_Capital_Owner.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_Capital_Owner.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Accounts\frmAccountsGroub.Designer.vb">
      <DependentUpon>frmAccountsGroub.vb</DependentUpon>
    </Compile>
    <Compile Include="Accounts\frmAccountsGroub.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Accounts\frmAccountsMain.designer.vb">
      <DependentUpon>frmAccountsMain.vb</DependentUpon>
    </Compile>
    <Compile Include="Accounts\frmAccountsMain.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Accounts\FrmBalanceReview.designer.vb">
      <DependentUpon>FrmBalanceReview.vb</DependentUpon>
    </Compile>
    <Compile Include="Accounts\FrmBalanceReview.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Accounts\frmLinkingAccountsTree.designer.vb">
      <DependentUpon>frmLinkingAccountsTree.vb</DependentUpon>
    </Compile>
    <Compile Include="Accounts\frmLinkingAccountsTree.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Accounts\frmShowAccounts.Designer.vb">
      <DependentUpon>frmShowAccounts.vb</DependentUpon>
    </Compile>
    <Compile Include="Accounts\frmShowAccounts.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Accounts\frmAssets.Designer.vb">
      <DependentUpon>frmAssets.vb</DependentUpon>
    </Compile>
    <Compile Include="Accounts\frmAssets.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Accounts\frmDailyWages.Designer.vb">
      <DependentUpon>frmDailyWages.vb</DependentUpon>
    </Compile>
    <Compile Include="Accounts\frmDailyWages.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Accounts\frmShowAssets.Designer.vb">
      <DependentUpon>frmShowAssets.vb</DependentUpon>
    </Compile>
    <Compile Include="Accounts\frmShowAssets.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Accounts\frmShowDailyWages.Designer.vb">
      <DependentUpon>frmShowDailyWages.vb</DependentUpon>
    </Compile>
    <Compile Include="Accounts\frmShowDailyWages.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Accounts\rptAccounts2.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptAccounts2.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Accounts\rptAssets.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptAssets.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Accounts\rptBalanceReview.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptBalanceReview.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Accounts\rptDailyWagesAll.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptDailyWagesAll.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Banks\frmAppropriations.Designer.vb">
      <DependentUpon>frmAppropriations.vb</DependentUpon>
    </Compile>
    <Compile Include="Banks\frmAppropriations.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Banks\frmBanks.Designer.vb">
      <DependentUpon>frmBanks.vb</DependentUpon>
    </Compile>
    <Compile Include="Banks\frmBanks.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Banks\frmShowAppropriations.Designer.vb">
      <DependentUpon>frmShowAppropriations.vb</DependentUpon>
    </Compile>
    <Compile Include="Banks\frmShowAppropriations.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Banks\frmShowBanks.Designer.vb">
      <DependentUpon>frmShowBanks.vb</DependentUpon>
    </Compile>
    <Compile Include="Banks\frmShowBanks.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Banks\frmShowTreasury.Designer.vb">
      <DependentUpon>frmShowTreasury.vb</DependentUpon>
    </Compile>
    <Compile Include="Banks\frmShowTreasury.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Banks\frmTreasury.Designer.vb">
      <DependentUpon>frmTreasury.vb</DependentUpon>
    </Compile>
    <Compile Include="Banks\frmTreasury.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Banks\rptAppropriations.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptAppropriations.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Banks\rptBanks.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptBanks.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Banks\rptLetterCredits.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptLetterCredits.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Banks\rptTreasury.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptTreasury.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Frm_Capital_Withdrawals.designer.vb">
      <DependentUpon>Frm_Capital_Withdrawals.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_Capital_Withdrawals.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Delegate\Frm_Deficit_Increase_Delegate.designer.vb">
      <DependentUpon>Frm_Deficit_Increase_Delegate.vb</DependentUpon>
    </Compile>
    <Compile Include="Delegate\Frm_Deficit_Increase_Delegate.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Delegate\Frm_ViewDebitCustomersDelegate.designer.vb">
      <DependentUpon>Frm_ViewDebitCustomersDelegate.vb</DependentUpon>
    </Compile>
    <Compile Include="Delegate\Frm_ViewDebitCustomersDelegate.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Delegate\Frm_CalculateTargit.Designer.vb">
      <DependentUpon>Frm_CalculateTargit.vb</DependentUpon>
    </Compile>
    <Compile Include="Delegate\Frm_CalculateTargit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Delegate\Rpt_CustomerDelegateAcount.vb">
      <DependentUpon>Rpt_CustomerDelegateAcount.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Employees\frmAdditional.Designer.vb">
      <DependentUpon>frmAdditional.vb</DependentUpon>
    </Compile>
    <Compile Include="Employees\frmAdditional.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Employees\frmEmployees.Designer.vb">
      <DependentUpon>frmEmployees.vb</DependentUpon>
    </Compile>
    <Compile Include="Employees\frmEmployees.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Employees\frmSalary.Designer.vb">
      <DependentUpon>frmSalary.vb</DependentUpon>
    </Compile>
    <Compile Include="Employees\frmSalary.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Employees\frmShowAdditional.Designer.vb">
      <DependentUpon>frmShowAdditional.vb</DependentUpon>
    </Compile>
    <Compile Include="Employees\frmShowAdditional.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Employees\frmShowEmployees.Designer.vb">
      <DependentUpon>frmShowEmployees.vb</DependentUpon>
    </Compile>
    <Compile Include="Employees\frmShowEmployees.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Employees\frmShowSalary.Designer.vb">
      <DependentUpon>frmShowSalary.vb</DependentUpon>
    </Compile>
    <Compile Include="Employees\frmShowSalary.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Employees\FrmEmployeesDiscountReward.Designer.vb">
      <DependentUpon>FrmEmployeesDiscountReward.vb</DependentUpon>
    </Compile>
    <Compile Include="Employees\FrmEmployeesDiscountReward.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Employees\rptAdditional.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptAdditional.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Employees\rptEmployees.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptEmployees.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Employees\rptSalary.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptSalary.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="frmBackupRestore.designer.vb">
      <DependentUpon>frmBackupRestore.vb</DependentUpon>
    </Compile>
    <Compile Include="frmBackupRestore.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frmdecayed.designer.vb">
      <DependentUpon>Frmdecayed.vb</DependentUpon>
    </Compile>
    <Compile Include="Frmdecayed.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmDiscOther.designer.vb">
      <DependentUpon>FrmDiscOther.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmDiscOther.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmItemsNewExcel.designer.vb">
      <DependentUpon>FrmItemsNewExcel.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmItemsNewExcel.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmItemsTransfer.designer.vb">
      <DependentUpon>FrmItemsTransfer.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmItemsTransfer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmSettingPrinter.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmItemsNew.designer.vb">
      <DependentUpon>FrmItemsNew.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmItemsNew.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmItemsOffersDiscounts.designer.vb">
      <DependentUpon>frmItemsOffersDiscounts.vb</DependentUpon>
    </Compile>
    <Compile Include="frmItemsOffersDiscounts.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSettingNetwork.Designer.vb">
      <DependentUpon>frmSettingNetwork.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSettingNetwork.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmSettingPrinterDefault.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Checks_Payable.Designer.vb">
      <DependentUpon>Frm_Checks_Payable.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_Checks_Payable.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Checks_Payable_Show.Designer.vb">
      <DependentUpon>Frm_Checks_Payable_Show.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_Checks_Payable_Show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Delegate\Frm_Data_Management.designer.vb">
      <DependentUpon>Frm_Data_Management.vb</DependentUpon>
    </Compile>
    <Compile Include="Delegate\Frm_Data_Management.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_EMailMessageSetting.Designer.vb">
      <DependentUpon>Frm_EMailMessageSetting.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_EMailMessageSetting.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Group_Branch.Designer.vb">
      <DependentUpon>Frm_Group_Branch.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_Group_Branch.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_IM_Decayed_Edit.designer.vb">
      <DependentUpon>Frm_IM_Decayed_Edit.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_IM_Decayed_Edit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_IM_Decayed.designer.vb">
      <DependentUpon>Frm_IM_Decayed.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_IM_Decayed.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Items_Prices.designer.vb">
      <DependentUpon>Frm_Items_Prices.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_Items_Prices.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Manufacturing\Frm_Product_Manufacturing_Show.Designer.vb">
      <DependentUpon>Frm_Product_Manufacturing_Show.vb</DependentUpon>
    </Compile>
    <Compile Include="Manufacturing\Frm_Product_Manufacturing_Show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Manufacturing\Frm_Product_Manufacturing.Designer.vb">
      <DependentUpon>Frm_Product_Manufacturing.vb</DependentUpon>
    </Compile>
    <Compile Include="Manufacturing\Frm_Product_Manufacturing.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Sheft_Status.designer.vb">
      <DependentUpon>Frm_Sheft_Status.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_Sheft_Status.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Languages\EnglishText.vb" />
    <Compile Include="Languages\ArabicText.vb" />
    <Compile Include="Maintenance\FrmMaintenanceOrderRunning.Designer.vb">
      <DependentUpon>FrmMaintenanceOrderRunning.vb</DependentUpon>
    </Compile>
    <Compile Include="Maintenance\FrmMaintenanceOrderRunning.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Maintenance\FrmMaintenanceTrakingMation.Designer.vb">
      <DependentUpon>FrmMaintenanceTrakingMation.vb</DependentUpon>
    </Compile>
    <Compile Include="Maintenance\FrmMaintenanceTrakingMation.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Maintenance\Frm_MaintenanceDeviceModel.designer.vb">
      <DependentUpon>Frm_MaintenanceDeviceModel.vb</DependentUpon>
    </Compile>
    <Compile Include="Maintenance\Frm_MaintenanceDeviceModel.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Maintenance\Frm_MaintenanceDeviceBrand.designer.vb">
      <DependentUpon>Frm_MaintenanceDeviceBrand.vb</DependentUpon>
    </Compile>
    <Compile Include="Maintenance\Frm_MaintenanceDeviceBrand.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Maintenance\Frm_MaintenanceTypeProduct.designer.vb">
      <DependentUpon>Frm_MaintenanceTypeProduct.vb</DependentUpon>
    </Compile>
    <Compile Include="Maintenance\Frm_MaintenanceTypeProduct.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Maintenance\Frm_MaintenanceType.designer.vb">
      <DependentUpon>Frm_MaintenanceType.vb</DependentUpon>
    </Compile>
    <Compile Include="Maintenance\Frm_MaintenanceType.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Maintenance\Frm_Drivers.designer.vb">
      <DependentUpon>Frm_Drivers.vb</DependentUpon>
    </Compile>
    <Compile Include="Maintenance\Frm_Drivers.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Maintenance\Rpt_All_TrakingMation.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_All_TrakingMation.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Maintenance\Rpt_MaintenanceOrderRunningDevicesGuarantee.vb">
      <DependentUpon>Rpt_MaintenanceOrderRunningDevicesGuarantee.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Maintenance\Rpt_MaintenanceOrderRunningDevicesReceipt.vb">
      <DependentUpon>Rpt_MaintenanceOrderRunningDevicesReceipt.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Maintenance\Rpt_MaintenanceOrderRunningDevices.vb">
      <DependentUpon>Rpt_MaintenanceOrderRunningDevices.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Maintenance\Rpt_MaintenanceOrderRunning1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_MaintenanceOrderRunning.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Maintenance\Rpt_MaintenanceOrderRunningSmall1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_MaintenanceOrderRunningSmall.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Maintenance\Rpt_Sales1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_Sales.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Maintenance\Rpt_SalesNotTax1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_SalesNotTax.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Manufacturing\Frm_Product_Dismissal_Notice_Factory_Show.Designer.vb">
      <DependentUpon>Frm_Product_Dismissal_Notice_Factory_Show.vb</DependentUpon>
    </Compile>
    <Compile Include="Manufacturing\Frm_Product_Dismissal_Notice_Factory_Show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Manufacturing\Frm_Product_Dismissal_Notice_Factory_Total.Designer.vb">
      <DependentUpon>Frm_Product_Dismissal_Notice_Factory_Total.vb</DependentUpon>
    </Compile>
    <Compile Include="Manufacturing\Frm_Product_Dismissal_Notice_Factory_Total.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Manufacturing\Frm_Product_Order_Filling_Manufacturing_Total.Designer.vb">
      <DependentUpon>Frm_Product_Order_Filling_Manufacturing_Total.vb</DependentUpon>
    </Compile>
    <Compile Include="Manufacturing\Frm_Product_Order_Filling_Manufacturing_Total.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Manufacturing\Frm_Product_Order_Filling_Manufacturing_Show.Designer.vb">
      <DependentUpon>Frm_Product_Order_Filling_Manufacturing_Show.vb</DependentUpon>
    </Compile>
    <Compile Include="Manufacturing\Frm_Product_Order_Filling_Manufacturing_Show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Manufacturing\Rpt_Product_Manufacturing_Total.vb">
      <DependentUpon>Rpt_Product_Manufacturing_Total.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="MDIParent1.designer.vb">
      <DependentUpon>MDIParent1.vb</DependentUpon>
    </Compile>
    <Compile Include="MDIParent1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MY Class\Class_Altfiqith.vb" />
    <Compile Include="MY Class\mdFunciones.vb" />
    <Compile Include="MY Class\mdVariables.vb" />
    <Compile Include="MY Class\MenuRenderer.vb" />
    <Compile Include="MY Class\RestoreBackup.vb" />
    <Compile Include="MY Class\Update_DataBase.vb" />
    <Compile Include="Report Show\ControlPriceItems\rpt_ControlPriceItems2SalWholeWhole.vb">
      <DependentUpon>rpt_ControlPriceItems2SalWholeWhole.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Report Show\ControlPriceItems\rpt_ControlPriceItems2TinWholeWhole.vb">
      <DependentUpon>rpt_ControlPriceItems2TinWholeWhole.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Report Show\ControlPriceItems\rpt_ControlPriceItems2WholeWhole.vb">
      <DependentUpon>rpt_ControlPriceItems2WholeWhole.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Report Show\ControlPriceItems\rpt_ControlPriceItems2SalWhole.vb">
      <DependentUpon>rpt_ControlPriceItems2SalWhole.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Report Show\ControlPriceItems\rpt_ControlPriceItems2TinWhole.vb">
      <DependentUpon>rpt_ControlPriceItems2TinWhole.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Report Show\ControlPriceItems\rpt_ControlPriceItems2TinSal.vb">
      <DependentUpon>rpt_ControlPriceItems2TinSal.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Report Show\ControlPriceItems\rpt_ControlPriceItemsNotPrice1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_ControlPriceItemsNotPrice.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Report Show\ControlPriceItems\rpt_ControlPriceItemsWholeWholePrice.vb">
      <DependentUpon>rpt_ControlPriceItemsWholeWholePrice.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Report Show\ControlPriceItems\rpt_ControlPriceItemsWholePrice.vb">
      <DependentUpon>rpt_ControlPriceItemsWholePrice.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Report Show\ControlPriceItems\rpt_ControlPriceItemsSalPrice.vb">
      <DependentUpon>rpt_ControlPriceItemsSalPrice.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Report Show\ControlPriceItems\FrmControlPriceItems.designer.vb">
      <DependentUpon>FrmControlPriceItems.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\ControlPriceItems\FrmControlPriceItems.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\ControlPriceItems\rpt_ControlPriceItemsTinPrice.vb">
      <DependentUpon>rpt_ControlPriceItemsTinPrice.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Report Show\FrmDaysAllDataDetails.Designer.vb">
      <DependentUpon>FrmDaysAllDataDetails.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmDaysAllDataDetails.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmItemGroupShowSal.designer.vb">
      <DependentUpon>FrmItemGroupShowSal.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmItemGroupShowSal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmItemsMovement.designer.vb">
      <DependentUpon>FrmItemsMovement.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmItemsMovement.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmShowTransfer.designer.vb">
      <DependentUpon>FrmShowTransfer.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmShowTransfer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmVendorAllData.designer.vb">
      <DependentUpon>FrmVendorAllData.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmVendorAllData.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\frm_Account_Vendor_Customer.Designer.vb">
      <DependentUpon>frm_Account_Vendor_Customer.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\frm_Account_Vendor_Customer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\frm_dayPurchase.Designer.vb">
      <DependentUpon>frm_dayPurchase.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\frm_dayPurchase.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\frm_dayPurchase_IM_Btin.Designer.vb">
      <DependentUpon>frm_dayPurchase_IM_Btin.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\frm_dayPurchase_IM_Btin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_daySales_IM_Bsal.Designer.vb">
      <DependentUpon>Frm_daySales_IM_Bsal.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_daySales_IM_Bsal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_Expiration_Date.designer.vb">
      <DependentUpon>Frm_Expiration_Date.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_Expiration_Date.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_Expiration_Date_Closest.designer.vb">
      <DependentUpon>Frm_Expiration_Date_Closest.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_Expiration_Date_Closest.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_Final_Global_Profits.Designer.vb">
      <DependentUpon>Frm_Final_Global_Profits.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_Final_Global_Profits.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_Financial_Position_Monthly.Designer.vb">
      <DependentUpon>Frm_Financial_Position_Monthly.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_Financial_Position_Monthly.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_Finan_Show_Itm_Move_Date.designer.vb">
      <DependentUpon>Frm_Finan_Show_Itm_Move_Date.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_Finan_Show_Itm_Move_Date.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_FirstTimeGoods.designer.vb">
      <DependentUpon>Frm_FirstTimeGoods.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_FirstTimeGoods.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_SalesPricesCustomer.Designer.vb">
      <DependentUpon>Frm_SalesPricesCustomer.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_SalesPricesCustomer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\frm_rptItemsAllStores.Designer.vb">
      <DependentUpon>frm_rptItemsAllStores.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\frm_rptItemsAllStores.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\frm_rpt_AllCustomer_Detail.Designer.vb">
      <DependentUpon>frm_rpt_AllCustomer_Detail.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\frm_rpt_AllCustomer_Detail.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\frm_rpt_All_Vendors_Detail.Designer.vb">
      <DependentUpon>frm_rpt_All_Vendors_Detail.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\frm_rpt_All_Vendors_Detail.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\frm_rpt_Balance_First_stand_Customer.Designer.vb">
      <DependentUpon>frm_rpt_Balance_First_stand_Customer.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\frm_rpt_Balance_First_stand_Customer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\frm_rpt_Balance_First_stand_Vendors.Designer.vb">
      <DependentUpon>frm_rpt_Balance_First_stand_Vendors.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\frm_rpt_Balance_First_stand_Vendors.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\frm_rpt_Balance_Treasurer.Designer.vb">
      <DependentUpon>frm_rpt_Balance_Treasurer.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\frm_rpt_Balance_Treasurer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_Show_Date_ProfitAndLoss.designer.vb">
      <DependentUpon>Frm_Show_Date_ProfitAndLoss.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_Show_Date_ProfitAndLoss.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_Custpay.Designer.vb">
      <DependentUpon>Frm_Users_Show_Custpay.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_Custpay.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCall.Designer.vb">
      <DependentUpon>frmCall.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCall.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmOptions.Designer.vb">
      <DependentUpon>frmOptions.vb</DependentUpon>
    </Compile>
    <Compile Include="frmOptions.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_DiscCust.Designer.vb">
      <DependentUpon>Frm_Users_Show_DiscCust.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_DiscCust.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MY Class\braanew.vb" />
    <Compile Include="MY Class\Cls_Constant.vb" />
    <Compile Include="MY Class\Cls_Users.vb" />
    <Compile Include="MY Class\Cls_Validation.vb" />
    <Compile Include="Customer.Designer.vb">
      <DependentUpon>Customer.vb</DependentUpon>
    </Compile>
    <Compile Include="Customer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCompany.Designer.vb">
      <DependentUpon>frmCompany.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCompany.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmItemSearch.Designer.vb">
      <DependentUpon>FrmItemSearch.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmItemSearch.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmShowCustomerPay.designer.vb">
      <DependentUpon>FrmShowCustomerPay.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmShowCustomerPay.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmShowImport.designer.vb">
      <DependentUpon>FrmShowImport.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmShowImport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_daySales.designer.vb">
      <DependentUpon>Frm_daySales.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_daySales.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_PrintReports.designer.vb">
      <DependentUpon>Frm_PrintReports.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_PrintReports.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\frm_Vendor_Customer_All_Account.Designer.vb">
      <DependentUpon>frm_Vendor_Customer_All_Account.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\frm_Vendor_Customer_All_Account.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\In_Str.Designer.vb">
      <DependentUpon>In_Str.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\In_Str.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Login.Designer.vb">
      <DependentUpon>Login.vb</DependentUpon>
    </Compile>
    <Compile Include="Login.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Out_str.designer.vb">
      <DependentUpon>Out_str.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Out_str.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_5.vb">
      <DependentUpon>Rpt_SalesBill_5.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_A5_5.vb">
      <DependentUpon>Rpt_SalesBill_A5_5.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_BalanceCust_A5_PricePublic.vb">
      <DependentUpon>Rpt_SalesBill_BalanceCust_A5_PricePublic.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_BalanceCust_A5.vb">
      <DependentUpon>Rpt_SalesBill_BalanceCust_A5.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_2.vb">
      <DependentUpon>Rpt_SalesBill_2.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SoldSmall_6.vb">
      <DependentUpon>Rpt_SoldSmall_6.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SoldSmall_5.vb">
      <DependentUpon>Rpt_SoldSmall_5.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SoldSmall_4.vb">
      <DependentUpon>Rpt_SoldSmall_4.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_NotDiscountBill.vb">
      <DependentUpon>Rpt_SalesBill_NotDiscountBill.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_Width_Height.vb">
      <DependentUpon>Rpt_SalesBill_Width_Height.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SoldSmall_2.vb">
      <DependentUpon>Rpt_SoldSmall_2.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_Tax_A5.vb">
      <DependentUpon>Rpt_SalesBill_Tax_A5.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_A5_4.vb">
      <DependentUpon>Rpt_SalesBill_A5_4.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_Delegate.vb">
      <DependentUpon>Rpt_SalesBill_Delegate.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill.vb">
      <DependentUpon>Rpt_SalesBill.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_NotDiscount.vb">
      <DependentUpon>Rpt_SalesBill_NotDiscount.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_A5_PricePublic.vb">
      <DependentUpon>Rpt_SalesBill_A5_PricePublic.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_A5_Nubaria_Tax.vb">
      <DependentUpon>Rpt_SalesBill_A5_Nubaria_Tax.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_Cash.vb">
      <DependentUpon>Rpt_SalesBill_Cash.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_4.vb">
      <DependentUpon>Rpt_SalesBill_4.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_A5_1.vb">
      <DependentUpon>Rpt_SalesBill_A5_1.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_OrderDelivery.vb">
      <DependentUpon>Rpt_SalesBill_OrderDelivery.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SoldSmall_OrderDelivery.vb">
      <DependentUpon>Rpt_SoldSmall_OrderDelivery.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SoldSmall_3.vb">
      <DependentUpon>Rpt_SoldSmall_3.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SoldSmall_EN.vb">
      <DependentUpon>Rpt_SoldSmall_EN.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Chart\Frm_More_Items_Sales.Designer.vb">
      <DependentUpon>Frm_More_Items_Sales.vb</DependentUpon>
    </Compile>
    <Compile Include="Reports\Chart\Frm_More_Items_Sales.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\Chart\rpt_Chart_Less_Selling_Items.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_Chart_Less_Selling_Items.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Chart\rpt_Chart_Less_Selling_Profits.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_Chart_Less_Selling_Profits.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Chart\rpt_Chart_More_Customer_Sales.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_Chart_More_Customer_Sales.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Chart\rpt_Chart_More_Items_Profits.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_Chart_More_Items_Profits.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Chart\rpt_More_Less_Items_Sales.vb">
      <DependentUpon>rpt_More_Less_Items_Sales.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Chart\rpt_Customer_Sales.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_Customer_Sales.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_PaperHalves13Number1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_PaperHalves13Number.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_Parcode13NumberPrice.vb">
      <DependentUpon>rpt_Parcode13NumberPrice.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_Parcode13Number.vb">
      <DependentUpon>rpt_Parcode13Number.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_Parcode10.vb">
      <DependentUpon>rpt_Parcode10.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_Parcode2.vb">
      <DependentUpon>rpt_Parcode2.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_ParcodeA4_17_4.vb">
      <DependentUpon>rpt_ParcodeA4_17_4.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_ParcodeMediumBarCode_Price2.vb">
      <DependentUpon>rpt_ParcodeMediumBarCode_Price2.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_Parcode_Customer2.vb">
      <DependentUpon>rpt_Parcode_Customer2.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_ParcodeMediumBarCode_5_37_ProDate.vb">
      <DependentUpon>rpt_ParcodeMediumBarCode_5_37_ProDate.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_Parcode_Customer.vb">
      <DependentUpon>rpt_Parcode_Customer.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_ParcodeMediumBarCode_5_37.vb">
      <DependentUpon>rpt_ParcodeMediumBarCode_5_37.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_ParcodeMediumBarCode2.vb">
      <DependentUpon>rpt_ParcodeMediumBarCode2.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_ParcodeExpirationDate.vb">
      <DependentUpon>rpt_ParcodeExpirationDate.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_ParcodePaperHalves31.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_ParcodePaperHalves3.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rptItemsPriceList1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptItemsPriceList.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rptItemsQunt.vb">
      <DependentUpon>rptItemsQunt.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\rptItemsPriceSalesQunt.vb">
      <DependentUpon>rptItemsPriceSalesQunt.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Rpt_Cust_Pay_Sales2.vb">
      <DependentUpon>Rpt_Cust_Pay_Sales2.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Rpt_Cust_Pay_Sales1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_Cust_Pay_Sales.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_Cust_Pay_Small2.vb">
      <DependentUpon>Rpt_Cust_Pay_Small2.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Rpt_DataCustomer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_DataCustomer.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_DaySald_Delivery.vb">
      <DependentUpon>Rpt_DaySald_Delivery.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Rpt_PriceOffer.vb">
      <DependentUpon>Rpt_PriceOffer.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Rpt_Items_Transfer_Data.vb">
      <DependentUpon>Rpt_Items_Transfer_Data.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_Print_Money_All.vb">
      <DependentUpon>Rpt_Print_Money_All.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Rpt_Print_Money_Detailed.vb">
      <DependentUpon>Rpt_Print_Money_Detailed.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\rpt_FirstTimeGoods.vb">
      <DependentUpon>rpt_FirstTimeGoods.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\rpt_AssetsItems_WholePrice.vb">
      <DependentUpon>rpt_AssetsItems_WholePrice.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Rpt_CustomerAcountDebtOnly.vb">
      <DependentUpon>Rpt_CustomerAcountDebtOnly.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Rpt_Expiration_Date.vb">
      <DependentUpon>Rpt_Expiration_Date.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Rpt_DaySales_Small2.vb">
      <DependentUpon>Rpt_DaySales_Small2.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Chart\rpt_Chart_More_Items_Sales.vb">
      <DependentUpon>rpt_Chart_More_Items_Sales.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Manufacturing\Rpt_Product_Manufacturing.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_Product_Manufacturing.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_SalesItemsGroup.vb">
      <DependentUpon>Rpt_SalesItemsGroup.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Rpt_SalesItems1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_SalesItems.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_SalesPricesCustomer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_SalesPricesCustomer.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_SalesPurchasesTax.vb">
      <DependentUpon>Rpt_SalesPurchasesTax.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Rpt_Cust_Pay_Small.vb">
      <DependentUpon>Rpt_Cust_Pay_Small.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\rpt_Vendor_Customer_Account.vb">
      <DependentUpon>rpt_Vendor_Customer_Account.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\rpt_DetailedSalesTotal.vb">
      <DependentUpon>rpt_DetailedSalesTotal.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_Parcode81.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_Parcode8.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_Parcode9.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_Parcode9.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rpt_ItemsMovement1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_ItemsMovement.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_Items_Transfer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_Items_Transfer.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SoldSmall.vb">
      <DependentUpon>Rpt_SoldSmall.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\rpt_DetailedCustomerAccountTotal.vb">
      <DependentUpon>rpt_DetailedCustomerAccountTotal.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_Parcode61.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_Parcode6.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_Parcode71.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_Parcode7.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\BILL\Rpt_SalesBill_3.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_SalesBill_3.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_ParcodeEN.vb">
      <DependentUpon>rpt_ParcodeEN.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\rptCarriedOutItems.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptCarriedOutItems.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rptItemsAllStores.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptItemsAllStores.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rptItemsPriceSales.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptItemsPriceSales.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rptPrintPrice21.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptPrintPrice2.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rptPrintPrice31.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptPrintPrice3.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rptPrintPrice41.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptPrintPrice4.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rptPrintPriceOne.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptPrintPriceOne.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_Accounts.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_Accounts.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_Account_Vendor_Customer1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_Account_Vendor_Customer.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_AcountCtmAll.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_AcountCtmAll.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rpt_AllCustomer_Detail.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_AllCustomer_Detail.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rpt_All_Vendors_Detail.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_All_Vendors_Detail.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rpt_AssetsItems.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_AssetsItems.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rpt_Balance_First_stand_Vendors.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_Balance_First_stand_Vendors.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_Checks_Payable1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_Checks_Payable.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_Date_Finance_All1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_Date_Finance_All.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_DaySales_Small.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_DaySales_Small.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rpt_DetailedCustomerAccount.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_DetailedCustomerAccount.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rpt_DetailedDaysAccount1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_DetailedDaysAccount.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rpt_Balance_First_stand_Customer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_Balance_First_stand_Customer.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rpt_Balance_Treasurer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_Balance_Treasurer.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_buyReturns.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_buyReturns.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_Date_Finance.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_Date_Finance.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_DaySald1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_DaySald.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_DaySales.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_DaySales.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_DiscCtm.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_DiscCtm.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_Parcode51.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_Parcode5.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_ParcodeMores.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_ParcodeMores.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_PayCtm1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_PayCtm.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_PeriodSal.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_PeriodSal.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rpt_InOut.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_InOut.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_Print_Finan.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_Print_Finan.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_Print_Money.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_Print_Money.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\RptExp.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RptExp.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_CustomerAcount.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_CustomerAcount.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_ItemOutlet.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_ItemOutlet.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Cust_Pay.designer.vb">
      <DependentUpon>Cust_Pay.vb</DependentUpon>
    </Compile>
    <Compile Include="Cust_Pay.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmDiscCustomers.Designer.vb">
      <DependentUpon>FrmDiscCustomers.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmDiscCustomers.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmPeriodBsal.designer.vb">
      <DependentUpon>FrmPeriodBsal.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmPeriodBsal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmPeriodBTin.designer.vb">
      <DependentUpon>FrmPeriodBTin.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmPeriodBTin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmPeriodSal.designer.vb">
      <DependentUpon>FrmPeriodSal.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmPeriodSal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmPeriodTin.designer.vb">
      <DependentUpon>FrmPeriodTin.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmPeriodTin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmThems.designer.vb">
      <DependentUpon>FrmThems.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmThems.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmEditimportad.Designer.vb">
      <DependentUpon>FrmEditimportad.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmEditimportad.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmEditsaleAd.Designer.vb">
      <DependentUpon>FrmEditsaleAd.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmEditsaleAd.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmShowDiscCustomers.designer.vb">
      <DependentUpon>FrmShowDiscCustomers.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmShowDiscCustomers.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmShowCustomerData.designer.vb">
      <DependentUpon>FrmShowCustomerData.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmShowCustomerData.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmUsers.designer.vb">
      <DependentUpon>FrmUsers.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmUsers.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmShowProfits.designer.vb">
      <DependentUpon>FrmShowProfits.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmShowProfits.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmShowCtmPay.designer.vb">
      <DependentUpon>FrmShowCtmPay.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmShowCtmPay.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmShowVnPay.designer.vb">
      <DependentUpon>FrmShowVnPay.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmShowVnPay.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmItemShowdecayed.designer.vb">
      <DependentUpon>FrmItemShowdecayed.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmItemShowdecayed.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmItemShowBsal.designer.vb">
      <DependentUpon>FrmItemShowBsal.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmItemShowBsal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmShowItemBtin.designer.vb">
      <DependentUpon>FrmShowItemBtin.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmShowItemBtin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmItemShowSal.designer.vb">
      <DependentUpon>FrmItemShowSal.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmItemShowSal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmShowItemsTin.designer.vb">
      <DependentUpon>FrmShowItemsTin.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmShowItemsTin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmShowItemAllData.designer.vb">
      <DependentUpon>FrmShowItemAllData.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmShowItemAllData.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmShowVendors_discount.designer.vb">
      <DependentUpon>FrmShowVendors_discount.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmShowVendors_discount.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmVendors_discount.designer.vb">
      <DependentUpon>FrmVendors_discount.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmVendors_discount.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmShowVendorPay.designer.vb">
      <DependentUpon>FrmShowVendorPay.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmShowVendorPay.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmShowSales.designer.vb">
      <DependentUpon>FrmShowSales.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmShowSales.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmEditimport.designer.vb">
      <DependentUpon>FrmEditimport.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmEditimport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmEditSales.designer.vb">
      <DependentUpon>FrmEditSales.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmEditSales.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmItemsFunds.designer.vb">
      <DependentUpon>FrmItemsFunds.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmItemsFunds.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmItemsMin.designer.vb">
      <DependentUpon>FrmItemsMin.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmItemsMin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmFinishItems.Designer.vb">
      <DependentUpon>FrmFinishItems.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmFinishItems.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frmimport.designer.vb">
      <DependentUpon>Frmimport.vb</DependentUpon>
    </Compile>
    <Compile Include="Frmimport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\FrmShowOutCoum.Designer.vb">
      <DependentUpon>FrmShowOutCoum.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\FrmShowOutCoum.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmvendors.Designer.vb">
      <DependentUpon>frmvendors.vb</DependentUpon>
    </Compile>
    <Compile Include="frmvendors.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmCats.Designer.vb">
      <DependentUpon>FrmCats.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmCats.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Active.designer.vb">
      <DependentUpon>Frm_Active.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_Active.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Edit_Item.Designer.vb">
      <DependentUpon>Frm_Edit_Item.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_Edit_Item.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Prc.Designer.vb">
      <DependentUpon>Frm_Prc.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_Prc.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Roles.designer.vb">
      <DependentUpon>Frm_Roles.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_Roles.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_IM_BTin.designer.vb">
      <DependentUpon>Frm_IM_BTin.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_IM_BTin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_IM_BTin_Edit.designer.vb">
      <DependentUpon>Frm_IM_BTin_Edit.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_IM_BTin_Edit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_IM_BSal.designer.vb">
      <DependentUpon>Frm_IM_BSal.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_IM_BSal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_IM_BSal_Edit.designer.vb">
      <DependentUpon>Frm_IM_BSal_Edit.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_IM_BSal_Edit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_IM_BTin_Show.designer.vb">
      <DependentUpon>Frm_IM_BTin_Show.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_IM_BTin_Show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_IM_BTin_Edit_Ad.designer.vb">
      <DependentUpon>Frm_IM_BTin_Edit_Ad.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_IM_BTin_Edit_Ad.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_IM_BSal_Edit_Ad.designer.vb">
      <DependentUpon>Frm_IM_BSal_Edit_Ad.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_IM_BSal_Edit_Ad.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_IM_Bsal_Show.designer.vb">
      <DependentUpon>Frm_IM_Bsal_Show.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_IM_Bsal_Show.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_Finan_Show_Itm_Move.designer.vb">
      <DependentUpon>Frm_Finan_Show_Itm_Move.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_Finan_Show_Itm_Move.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_IM_in_out_Money.designer.vb">
      <DependentUpon>Frm_IM_in_out_Money.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_IM_in_out_Money.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_IM_Outcome_Cat.designer.vb">
      <DependentUpon>Frm_IM_Outcome_Cat.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_IM_Outcome_Cat.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_Discount.designer.vb">
      <DependentUpon>Frm_Users_Show_Discount.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_Discount.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_outcome.designer.vb">
      <DependentUpon>Frm_Users_Show_outcome.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_outcome.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_Vnd.designer.vb">
      <DependentUpon>Frm_Users_Show_Vnd.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_Vnd.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_Bsal.designer.vb">
      <DependentUpon>Frm_Users_Show_Bsal.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_Bsal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_Btin.designer.vb">
      <DependentUpon>Frm_Users_Show_Btin.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_Btin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_Sal.designer.vb">
      <DependentUpon>Frm_Users_Show_Sal.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_Sal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_Tin.designer.vb">
      <DependentUpon>Frm_Users_Show_Tin.vb</DependentUpon>
    </Compile>
    <Compile Include="Report Show\Frm_Users_Show_Tin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MY Class\IMHM_Point_Sales.vb" />
    <Compile Include="MY Class\ModuleCpuserial.vb" />
    <Compile Include="MY Class\yasser_class.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="MY Class\MyVars.vb" />
    <Compile Include="outcome.designer.vb">
      <DependentUpon>outcome.vb</DependentUpon>
    </Compile>
    <Compile Include="outcome.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_Parcode.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_Parcode.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_SalesPurchases1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_SalesPurchases.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_SalseItemPerfect.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_SalseItemPerfect.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_SalseItemPerfectAll.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_SalseItemPerfectAll.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_Users_DiscVn.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_Users_DiscVn.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_Users_outcome.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_Users_outcome.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_Users_Tin_Sal.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_Users_Tin_Sal.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Rpt_Users_Vnd.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_Users_Vnd.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Parcode\rpt_ParcodeA4_17_4_Price.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rpt_ParcodeA4_17_4_Price.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="SplashScreen1.Designer.vb">
      <DependentUpon>SplashScreen1.vb</DependentUpon>
    </Compile>
    <Compile Include="SplashScreen1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Stores.designer.vb">
      <DependentUpon>Stores.vb</DependentUpon>
    </Compile>
    <Compile Include="Stores.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MY Class\Supper_Market_Class.vb" />
    <Compile Include="Treasury\Frm_AddTreasury.designer.vb">
      <DependentUpon>Frm_AddTreasury.vb</DependentUpon>
    </Compile>
    <Compile Include="Treasury\Frm_AddTreasury.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Vendor_pay.designer.vb">
      <DependentUpon>Vendor_pay.vb</DependentUpon>
    </Compile>
    <Compile Include="Vendor_pay.vb">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="AboutBox1.resx">
      <DependentUpon>AboutBox1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\frmAccounts.resx">
      <DependentUpon>frmAccounts.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Cust_Pay_CheckType.resx">
      <DependentUpon>Cust_Pay_CheckType.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmPriceOffer.resx">
      <DependentUpon>FrmPriceOffer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmUpdateDataBase.resx">
      <DependentUpon>FrmUpdateDataBase.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmUpdateDataBaseProgressBar.resx">
      <DependentUpon>FrmUpdateDataBaseProgressBar.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Employees\rptSalaryEmp.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSalaryEmp.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_AdjustmentsStores_List.resx">
      <DependentUpon>Frm_AdjustmentsStores_List.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Frm_Maintenance_ReceivingCar.resx">
      <DependentUpon>Frm_Maintenance_ReceivingCar.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Frm_Maintenance_Recipient.resx">
      <DependentUpon>Frm_Maintenance_Recipient.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Frm_Maintenance_Supervisor.resx">
      <DependentUpon>Frm_Maintenance_Supervisor.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Frm_Maintenance_Car_Data.resx">
      <DependentUpon>Frm_Maintenance_Car_Data.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Frm_Other_Income.resx">
      <DependentUpon>Frm_Other_Income.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Frm_Other_Income_Category.resx">
      <DependentUpon>Frm_Other_Income_Category.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Rpt_SalesNotTax_2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesNotTax_2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Rpt_Sales_2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Sales_2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Manufacturing\Frm_Product_Dismissal_Notice_Factory.resx">
      <DependentUpon>Frm_Product_Dismissal_Notice_Factory.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Manufacturing\Frm_Product_Order_Filling_Manufacturing.resx">
      <DependentUpon>Frm_Product_Order_Filling_Manufacturing.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmSales.resx">
      <DependentUpon>FrmSales.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Capital_Owner.resx">
      <DependentUpon>Frm_Capital_Owner.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\frmAccountsGroub.resx">
      <DependentUpon>frmAccountsGroub.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\frmAccountsMain.resx">
      <DependentUpon>frmAccountsMain.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\FrmBalanceReview.resx">
      <DependentUpon>FrmBalanceReview.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\frmLinkingAccountsTree.resx">
      <DependentUpon>frmLinkingAccountsTree.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\frmShowAccounts.resx">
      <DependentUpon>frmShowAccounts.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\frmAssets.resx">
      <DependentUpon>frmAssets.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\frmDailyWages.resx">
      <DependentUpon>frmDailyWages.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\frmShowAssets.resx">
      <DependentUpon>frmShowAssets.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\frmShowDailyWages.resx">
      <DependentUpon>frmShowDailyWages.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\rptAccounts2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptAccounts2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\rptAssets.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptAssets.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\rptBalanceReview.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptBalanceReview.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\rptDailyWagesAll.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptDailyWagesAll.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Banks\frmAppropriations.resx">
      <DependentUpon>frmAppropriations.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Banks\frmBanks.resx">
      <DependentUpon>frmBanks.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Banks\frmShowAppropriations.resx">
      <DependentUpon>frmShowAppropriations.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Banks\frmShowBanks.resx">
      <DependentUpon>frmShowBanks.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Banks\frmShowTreasury.resx">
      <DependentUpon>frmShowTreasury.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Banks\frmTreasury.resx">
      <DependentUpon>frmTreasury.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Banks\rptAppropriations.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptAppropriations.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Banks\rptBanks.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptBanks.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Banks\rptLetterCredits.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptLetterCredits.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Banks\rptTreasury.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptTreasury.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Capital_Withdrawals.resx">
      <DependentUpon>Frm_Capital_Withdrawals.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Delegate\Frm_Deficit_Increase_Delegate.resx">
      <DependentUpon>Frm_Deficit_Increase_Delegate.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Delegate\Frm_ViewDebitCustomersDelegate.resx">
      <DependentUpon>Frm_ViewDebitCustomersDelegate.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Delegate\Frm_CalculateTargit.resx">
      <DependentUpon>Frm_CalculateTargit.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Delegate\Rpt_CustomerDelegateAcount.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_CustomerDelegateAcount.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Employees\frmAdditional.resx">
      <DependentUpon>frmAdditional.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Employees\frmEmployees.resx">
      <DependentUpon>frmEmployees.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Employees\FrmEmployeesDiscountReward.resx">
      <DependentUpon>FrmEmployeesDiscountReward.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Employees\frmSalary.resx">
      <DependentUpon>frmSalary.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Employees\frmShowAdditional.resx">
      <DependentUpon>frmShowAdditional.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Employees\frmShowEmployees.resx">
      <DependentUpon>frmShowEmployees.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Employees\frmShowSalary.resx">
      <DependentUpon>frmShowSalary.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Employees\rptAdditional.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptAdditional.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Employees\rptEmployees.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptEmployees.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Employees\rptSalary.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSalary.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmBackupRestore.resx">
      <DependentUpon>frmBackupRestore.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frmdecayed.resx">
      <DependentUpon>Frmdecayed.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmDiscOther.resx">
      <DependentUpon>FrmDiscOther.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmItemsNewExcel.resx">
      <DependentUpon>FrmItemsNewExcel.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmItemsTransfer.resx">
      <DependentUpon>FrmItemsTransfer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmSettingPrinter.resx">
      <DependentUpon>FrmSettingPrinter.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmItemsNew.resx">
      <DependentUpon>FrmItemsNew.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmItemsOffersDiscounts.resx">
      <DependentUpon>frmItemsOffersDiscounts.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSettingNetwork.resx">
      <DependentUpon>frmSettingNetwork.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmSettingPrinterDefault.resx">
      <DependentUpon>FrmSettingPrinterDefault.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Checks_Payable.resx">
      <DependentUpon>Frm_Checks_Payable.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Checks_Payable_Show.resx">
      <DependentUpon>Frm_Checks_Payable_Show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Delegate\Frm_Data_Management.resx">
      <DependentUpon>Frm_Data_Management.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_EMailMessageSetting.resx">
      <DependentUpon>Frm_EMailMessageSetting.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Group_Branch.resx">
      <DependentUpon>Frm_Group_Branch.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_IM_Decayed_Edit.resx">
      <DependentUpon>Frm_IM_Decayed_Edit.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_IM_Decayed.resx">
      <DependentUpon>Frm_IM_Decayed.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Items_Prices.resx">
      <DependentUpon>Frm_Items_Prices.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Manufacturing\Frm_Product_Manufacturing_Show.resx">
      <DependentUpon>Frm_Product_Manufacturing_Show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Manufacturing\Frm_Product_Manufacturing.resx">
      <DependentUpon>Frm_Product_Manufacturing.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Sheft_Status.resx">
      <DependentUpon>Frm_Sheft_Status.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\FrmMaintenanceOrderRunning.resx">
      <DependentUpon>FrmMaintenanceOrderRunning.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\FrmMaintenanceTrakingMation.resx">
      <DependentUpon>FrmMaintenanceTrakingMation.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Frm_MaintenanceDeviceModel.resx">
      <DependentUpon>Frm_MaintenanceDeviceModel.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Frm_MaintenanceDeviceBrand.resx">
      <DependentUpon>Frm_MaintenanceDeviceBrand.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Frm_MaintenanceTypeProduct.resx">
      <DependentUpon>Frm_MaintenanceTypeProduct.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Frm_MaintenanceType.resx">
      <DependentUpon>Frm_MaintenanceType.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Frm_Drivers.resx">
      <DependentUpon>Frm_Drivers.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Rpt_All_TrakingMation.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_All_TrakingMation.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Rpt_MaintenanceOrderRunningDevicesGuarantee.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_MaintenanceOrderRunningDevicesGuarantee.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Rpt_MaintenanceOrderRunningDevicesReceipt.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_MaintenanceOrderRunningDevicesReceipt.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Rpt_MaintenanceOrderRunningDevices.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_MaintenanceOrderRunningDevices.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Rpt_MaintenanceOrderRunning.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_MaintenanceOrderRunning1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Rpt_MaintenanceOrderRunningSmall.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_MaintenanceOrderRunningSmall1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Rpt_Sales.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Sales1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Maintenance\Rpt_SalesNotTax.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesNotTax1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Manufacturing\Frm_Product_Dismissal_Notice_Factory_Show.resx">
      <DependentUpon>Frm_Product_Dismissal_Notice_Factory_Show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Manufacturing\Frm_Product_Dismissal_Notice_Factory_Total.resx">
      <DependentUpon>Frm_Product_Dismissal_Notice_Factory_Total.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Manufacturing\Frm_Product_Order_Filling_Manufacturing_Total.resx">
      <DependentUpon>Frm_Product_Order_Filling_Manufacturing_Total.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Manufacturing\Frm_Product_Order_Filling_Manufacturing_Show.resx">
      <DependentUpon>Frm_Product_Order_Filling_Manufacturing_Show.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Manufacturing\Rpt_Product_Manufacturing_Total.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Product_Manufacturing_Total.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="MDIParent1.resx">
      <DependentUpon>MDIParent1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\ControlPriceItems\rpt_ControlPriceItems2SalWholeWhole.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ControlPriceItems2SalWholeWhole.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\ControlPriceItems\rpt_ControlPriceItems2TinWholeWhole.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ControlPriceItems2TinWholeWhole.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\ControlPriceItems\rpt_ControlPriceItems2WholeWhole.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ControlPriceItems2WholeWhole.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\ControlPriceItems\rpt_ControlPriceItems2SalWhole.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ControlPriceItems2SalWhole.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\ControlPriceItems\rpt_ControlPriceItems2TinWhole.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ControlPriceItems2TinWhole.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\ControlPriceItems\rpt_ControlPriceItems2TinSal.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ControlPriceItems2TinSal.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\ControlPriceItems\rpt_ControlPriceItemsNotPrice.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ControlPriceItemsNotPrice1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\ControlPriceItems\rpt_ControlPriceItemsWholeWholePrice.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ControlPriceItemsWholeWholePrice.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\ControlPriceItems\rpt_ControlPriceItemsWholePrice.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ControlPriceItemsWholePrice.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\ControlPriceItems\rpt_ControlPriceItemsSalPrice.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ControlPriceItemsSalPrice.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\ControlPriceItems\FrmControlPriceItems.resx">
      <DependentUpon>FrmControlPriceItems.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\ControlPriceItems\rpt_ControlPriceItemsTinPrice.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ControlPriceItemsTinPrice.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmDaysAllDataDetails.resx">
      <DependentUpon>FrmDaysAllDataDetails.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmItemGroupShowSal.resx">
      <DependentUpon>FrmItemGroupShowSal.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmItemsMovement.resx">
      <DependentUpon>FrmItemsMovement.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmShowTransfer.resx">
      <DependentUpon>FrmShowTransfer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmVendorAllData.resx">
      <DependentUpon>FrmVendorAllData.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\frm_Account_Vendor_Customer.resx">
      <DependentUpon>frm_Account_Vendor_Customer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\frm_dayPurchase.resx">
      <DependentUpon>frm_dayPurchase.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\frm_dayPurchase_IM_Btin.resx">
      <DependentUpon>frm_dayPurchase_IM_Btin.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_daySales_IM_Bsal.resx">
      <DependentUpon>Frm_daySales_IM_Bsal.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_Expiration_Date.resx">
      <DependentUpon>Frm_Expiration_Date.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_Expiration_Date_Closest.resx">
      <DependentUpon>Frm_Expiration_Date_Closest.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_Final_Global_Profits.resx">
      <DependentUpon>Frm_Final_Global_Profits.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_Financial_Position_Monthly.resx">
      <DependentUpon>Frm_Financial_Position_Monthly.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_Finan_Show_Itm_Move_Date.resx">
      <DependentUpon>Frm_Finan_Show_Itm_Move_Date.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_FirstTimeGoods.resx">
      <DependentUpon>Frm_FirstTimeGoods.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_SalesPricesCustomer.resx">
      <DependentUpon>Frm_SalesPricesCustomer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\frm_rptItemsAllStores.resx">
      <DependentUpon>frm_rptItemsAllStores.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\frm_rpt_AllCustomer_Detail.resx">
      <DependentUpon>frm_rpt_AllCustomer_Detail.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\frm_rpt_All_Vendors_Detail.resx">
      <DependentUpon>frm_rpt_All_Vendors_Detail.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\frm_rpt_Balance_First_stand_Customer.resx">
      <DependentUpon>frm_rpt_Balance_First_stand_Customer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\frm_rpt_Balance_First_stand_Vendors.resx">
      <DependentUpon>frm_rpt_Balance_First_stand_Vendors.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\frm_rpt_Balance_Treasurer.resx">
      <DependentUpon>frm_rpt_Balance_Treasurer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_Show_Date_ProfitAndLoss.resx">
      <DependentUpon>Frm_Show_Date_ProfitAndLoss.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_Users_Show_Custpay.resx">
      <DependentUpon>Frm_Users_Show_Custpay.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Customer.resx">
      <DependentUpon>Customer.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCall.resx">
      <DependentUpon>frmCall.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCompany.resx">
      <DependentUpon>frmCompany.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmItemSearch.resx">
      <DependentUpon>FrmItemSearch.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmOptions.resx">
      <DependentUpon>frmOptions.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmShowCustomerPay.resx">
      <DependentUpon>FrmShowCustomerPay.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmShowImport.resx">
      <DependentUpon>FrmShowImport.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_daySales.resx">
      <DependentUpon>Frm_daySales.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_PrintReports.resx">
      <DependentUpon>Frm_PrintReports.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_Users_Show_DiscCust.resx">
      <DependentUpon>Frm_Users_Show_DiscCust.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\frm_Vendor_Customer_All_Account.resx">
      <DependentUpon>frm_Vendor_Customer_All_Account.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\In_Str.resx">
      <DependentUpon>In_Str.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Login.resx">
      <DependentUpon>Login.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Out_str.resx">
      <DependentUpon>Out_str.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_5.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_5.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_A5_5.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_A5_5.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_BalanceCust_A5_PricePublic.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_BalanceCust_A5_PricePublic.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_BalanceCust_A5.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_BalanceCust_A5.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SoldSmall_6.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SoldSmall_6.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SoldSmall_5.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SoldSmall_5.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SoldSmall_4.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SoldSmall_4.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_NotDiscountBill.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_NotDiscountBill.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_Width_Height.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_Width_Height.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SoldSmall_2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SoldSmall_2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_Tax_A5.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_Tax_A5.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_A5_4.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_A5_4.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_Delegate.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_Delegate.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_NotDiscount.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_NotDiscount.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_A5_PricePublic.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_A5_PricePublic.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_A5_Nubaria_Tax.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_A5_Nubaria_Tax.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_Cash.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_Cash.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_4.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_4.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_A5_1.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_A5_1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_OrderDelivery.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_OrderDelivery.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SoldSmall_OrderDelivery.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SoldSmall_OrderDelivery.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SoldSmall_3.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SoldSmall_3.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SoldSmall_EN.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SoldSmall_EN.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Chart\Frm_More_Items_Sales.resx">
      <DependentUpon>Frm_More_Items_Sales.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Chart\rpt_Chart_Less_Selling_Items.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Chart_Less_Selling_Items.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Chart\rpt_Chart_Less_Selling_Profits.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Chart_Less_Selling_Profits.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Chart\rpt_Chart_More_Customer_Sales.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Chart_More_Customer_Sales.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Chart\rpt_Chart_More_Items_Profits.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Chart_More_Items_Profits.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Chart\rpt_More_Less_Items_Sales.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_More_Less_Items_Sales.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Chart\rpt_Customer_Sales.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Customer_Sales.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_PaperHalves13Number.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_PaperHalves13Number1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_Parcode13NumberPrice.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Parcode13NumberPrice.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_Parcode13Number.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Parcode13Number.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_Parcode10.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Parcode10.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_Parcode2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Parcode2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_ParcodeA4_17_4.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ParcodeA4_17_4.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_ParcodeMediumBarCode_Price2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ParcodeMediumBarCode_Price2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_Parcode_Customer2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Parcode_Customer2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_ParcodeMediumBarCode_5_37_ProDate.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ParcodeMediumBarCode_5_37_ProDate.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_Parcode_Customer.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Parcode_Customer.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_ParcodeMediumBarCode_5_37.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ParcodeMediumBarCode_5_37.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_ParcodeMediumBarCode2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ParcodeMediumBarCode2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_ParcodeExpirationDate.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ParcodeExpirationDate.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_ParcodePaperHalves3.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ParcodePaperHalves31.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rptItemsPriceList.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptItemsPriceList1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rptItemsQunt.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptItemsQunt.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rptItemsPriceSalesQunt.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptItemsPriceSalesQunt.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Cust_Pay_Sales2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Cust_Pay_Sales2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Cust_Pay_Sales.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Cust_Pay_Sales1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Cust_Pay_Small2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Cust_Pay_Small2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_DataCustomer.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_DataCustomer.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_DaySald_Delivery.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_DaySald_Delivery.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_PriceOffer.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_PriceOffer.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Items_Transfer_Data.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Items_Transfer_Data.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Print_Money_All.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Print_Money_All.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Print_Money_Detailed.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Print_Money_Detailed.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_FirstTimeGoods.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_FirstTimeGoods.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_AssetsItems_WholePrice.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_AssetsItems_WholePrice.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_CustomerAcountDebtOnly.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_CustomerAcountDebtOnly.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Expiration_Date.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Expiration_Date.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_DaySales_Small2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_DaySales_Small2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Chart\rpt_Chart_More_Items_Sales.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Chart_More_Items_Sales.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Manufacturing\Rpt_Product_Manufacturing.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Product_Manufacturing.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_SalesItemsGroup.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesItemsGroup.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_SalesItems.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesItems1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_SalesPricesCustomer.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesPricesCustomer.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_SalesPurchasesTax.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesPurchasesTax.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Cust_Pay_Small.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Cust_Pay_Small.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_Vendor_Customer_Account.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Vendor_Customer_Account.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_DetailedSalesTotal.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_DetailedSalesTotal.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_Parcode8.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Parcode81.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_Parcode9.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Parcode9.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_ItemsMovement.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ItemsMovement1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Items_Transfer.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Items_Transfer.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SoldSmall.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SoldSmall.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_DetailedCustomerAccountTotal.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_DetailedCustomerAccountTotal.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_Parcode6.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Parcode61.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_Parcode7.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Parcode71.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BILL\Rpt_SalesBill_3.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesBill_3.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_ParcodeEN.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ParcodeEN.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rptCarriedOutItems.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptCarriedOutItems.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rptItemsAllStores.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptItemsAllStores.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rptItemsPriceSales.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptItemsPriceSales.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rptPrintPrice2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptPrintPrice21.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rptPrintPrice3.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptPrintPrice31.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rptPrintPrice4.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptPrintPrice41.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rptPrintPriceOne.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptPrintPriceOne.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Accounts.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Accounts.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Account_Vendor_Customer.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Account_Vendor_Customer1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_AcountCtmAll.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_AcountCtmAll.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_AllCustomer_Detail.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_AllCustomer_Detail.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_All_Vendors_Detail.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_All_Vendors_Detail.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_AssetsItems.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_AssetsItems.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_Balance_First_stand_Vendors.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Balance_First_stand_Vendors.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Checks_Payable.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Checks_Payable1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Date_Finance_All.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Date_Finance_All1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_DaySales_Small.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_DaySales_Small.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_DetailedCustomerAccount.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_DetailedCustomerAccount.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_DetailedDaysAccount.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_DetailedDaysAccount1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_Balance_First_stand_Customer.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Balance_First_stand_Customer.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_Balance_Treasurer.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Balance_Treasurer.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_buyReturns.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_buyReturns.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Date_Finance.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Date_Finance.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_DaySald.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_DaySald1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_DaySales.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_DaySales.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_DiscCtm.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_DiscCtm.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_Parcode5.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Parcode51.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_ParcodeMores.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ParcodeMores.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_PayCtm.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_PayCtm1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_PeriodSal.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_PeriodSal.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_InOut.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_InOut.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Print_Finan.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Print_Finan.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Print_Money.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Print_Money.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\RptExp.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>RptExp.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_CustomerAcount.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_CustomerAcount.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_ItemOutlet.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_ItemOutlet.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Cust_Pay.resx">
      <DependentUpon>Cust_Pay.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmDiscCustomers.resx">
      <DependentUpon>FrmDiscCustomers.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmPeriodBsal.resx">
      <DependentUpon>FrmPeriodBsal.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmPeriodBTin.resx">
      <DependentUpon>FrmPeriodBTin.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmPeriodSal.resx">
      <DependentUpon>FrmPeriodSal.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmPeriodTin.resx">
      <DependentUpon>FrmPeriodTin.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmThems.resx">
      <DependentUpon>FrmThems.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmEditimportad.resx">
      <DependentUpon>FrmEditimportad.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmEditsaleAd.resx">
      <DependentUpon>FrmEditsaleAd.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmShowDiscCustomers.resx">
      <DependentUpon>FrmShowDiscCustomers.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmShowCustomerData.resx">
      <DependentUpon>FrmShowCustomerData.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmUsers.resx">
      <DependentUpon>FrmUsers.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmShowProfits.resx">
      <DependentUpon>FrmShowProfits.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmShowCtmPay.resx">
      <DependentUpon>FrmShowCtmPay.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmShowVnPay.resx">
      <DependentUpon>FrmShowVnPay.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmItemShowdecayed.resx">
      <DependentUpon>FrmItemShowdecayed.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmItemShowBsal.resx">
      <DependentUpon>FrmItemShowBsal.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmShowItemBtin.resx">
      <DependentUpon>FrmShowItemBtin.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmItemShowSal.resx">
      <DependentUpon>FrmItemShowSal.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmShowItemsTin.resx">
      <DependentUpon>FrmShowItemsTin.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmShowItemAllData.resx">
      <DependentUpon>FrmShowItemAllData.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmShowVendors_discount.resx">
      <DependentUpon>FrmShowVendors_discount.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmVendors_discount.resx">
      <DependentUpon>FrmVendors_discount.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmShowVendorPay.resx">
      <DependentUpon>FrmShowVendorPay.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmShowSales.resx">
      <DependentUpon>FrmShowSales.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmEditimport.resx">
      <DependentUpon>FrmEditimport.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmEditSales.resx">
      <DependentUpon>FrmEditSales.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmItemsFunds.resx">
      <DependentUpon>FrmItemsFunds.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmItemsMin.resx">
      <DependentUpon>FrmItemsMin.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmFinishItems.resx">
      <DependentUpon>FrmFinishItems.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Frmimport.resx">
      <DependentUpon>Frmimport.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\FrmShowOutCoum.resx">
      <DependentUpon>FrmShowOutCoum.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmvendors.resx">
      <DependentUpon>frmvendors.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmCats.resx">
      <DependentUpon>FrmCats.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Active.resx">
      <DependentUpon>Frm_Active.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Edit_Item.resx">
      <DependentUpon>Frm_Edit_Item.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_Finan_Show_Itm_Move.resx">
      <DependentUpon>Frm_Finan_Show_Itm_Move.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_IM_BSal.resx">
      <DependentUpon>Frm_IM_BSal.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_IM_BSal_Edit.resx">
      <DependentUpon>Frm_IM_BSal_Edit.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_IM_BSal_Edit_Ad.resx">
      <DependentUpon>Frm_IM_BSal_Edit_Ad.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_IM_Bsal_Show.resx">
      <DependentUpon>Frm_IM_Bsal_Show.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_IM_BTin.resx">
      <DependentUpon>Frm_IM_BTin.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_IM_BTin_Edit.resx">
      <DependentUpon>Frm_IM_BTin_Edit.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_IM_BTin_Edit_Ad.resx">
      <DependentUpon>Frm_IM_BTin_Edit_Ad.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_IM_BTin_Show.resx">
      <DependentUpon>Frm_IM_BTin_Show.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_IM_in_out_Money.resx">
      <DependentUpon>Frm_IM_in_out_Money.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_IM_Outcome_Cat.resx">
      <DependentUpon>Frm_IM_Outcome_Cat.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Prc.resx">
      <DependentUpon>Frm_Prc.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Roles.resx">
      <DependentUpon>Frm_Roles.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_Users_Show_Bsal.resx">
      <DependentUpon>Frm_Users_Show_Bsal.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_Users_Show_Btin.resx">
      <DependentUpon>Frm_Users_Show_Btin.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_Users_Show_Discount.resx">
      <DependentUpon>Frm_Users_Show_Discount.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_Users_Show_outcome.resx">
      <DependentUpon>Frm_Users_Show_outcome.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_Users_Show_Sal.resx">
      <DependentUpon>Frm_Users_Show_Sal.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_Users_Show_Tin.resx">
      <DependentUpon>Frm_Users_Show_Tin.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Report Show\Frm_Users_Show_Vnd.resx">
      <DependentUpon>Frm_Users_Show_Vnd.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="outcome.resx">
      <DependentUpon>outcome.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_Parcode.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_Parcode.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_SalesPurchases.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesPurchases1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_SalseItemPerfect.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalseItemPerfect.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_SalseItemPerfectAll.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalseItemPerfectAll.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Users_DiscVn.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Users_DiscVn.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Users_outcome.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Users_outcome.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Users_Tin_Sal.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Users_Tin_Sal.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Rpt_Users_Vnd.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Users_Vnd.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Parcode\rpt_ParcodeA4_17_4_Price.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rpt_ParcodeA4_17_4_Price.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="SplashScreen1.resx">
      <DependentUpon>SplashScreen1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Stores.resx">
      <DependentUpon>Stores.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Treasury\Frm_AddTreasury.resx">
      <DependentUpon>Frm_AddTreasury.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Vendor_pay.resx">
      <DependentUpon>Vendor_pay.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="FIT SOFT_TemporaryKey.pfx" />
    <None Include="My Project\app.manifest" />
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{967B4E0D-AD0C-4609-AB67-0FA40C0206D8}" />
    <Service Include="{C0C07587-41A7-46C8-8FBD-3F9C8EBE2DDC}" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="bin\Debug\Microsoft.SqlServer.ConnectionInfo.dll" />
    <Content Include="bin\Debug\Microsoft.SqlServer.Management.Sdk.Sfc.dll" />
    <Content Include="bin\Debug\Microsoft.SqlServer.Smo.dll" />
    <Content Include="bin\Debug\Microsoft.SqlServer.SmoExtended.dll" />
    <Content Include="bin\Debug\Microsoft.SqlServer.SqlEnum.dll" />
    <Content Include="Resources\173044 - list.png" />
    <Content Include="Resources\173113 - list th.png" />
    <Content Include="Resources\AdjustmentsStores.png" />
    <Content Include="Resources\Aggregation.png" />
    <None Include="Resources\Aggregation48.png" />
    <Content Include="Resources\Chart_Blue_Area  up.png" />
    <Content Include="Resources\Chevron Left.png" />
    <Content Include="Resources\Chevron Right.png" />
    <Content Include="Resources\circle_Blue.png" />
    <Content Include="Resources\Circle_Green.png" />
    <Content Include="Resources\Circle_Red.png" />
    <Content Include="Resources\Combany.png" />
    <Content Include="Resources\EquationWeights.png" />
    <None Include="Resources\Finances48.png" />
    <Content Include="Resources\Indent Left.png" />
    <Content Include="Resources\Indent Right.ico" />
    <Content Include="Resources\Loading.gif" />
    <Content Include="Resources\login-background-images-2.jpg" />
    <Content Include="Resources\login_Icone.png" />
    <Content Include="Resources\order_production_pms-min.png" />
    <Content Include="Resources\Other_Income.png" />
    <Content Include="Resources\packground.png" />
    <Content Include="Resources\view-hide.png" />
    <Content Include="Resources\withdrawal-deposit.png" />
    <None Include="Resources\1473532297_file_add1.png" />
    <None Include="Resources\1473545934_file_edit1.png" />
    <Content Include="Resources\Add_2.png" />
    <Content Include="Resources\Add_Bill.png" />
    <Content Include="Resources\AdvancedSearch.png" />
    <None Include="Resources\Asset_Balances.png" />
    <Content Include="Resources\background-1.png" />
    <Content Include="Resources\backup-restore.png" />
    <Content Include="Resources\BackUpDatat.PNG" />
    <Content Include="Resources\bank.png" />
    <Content Include="Resources\calculator.png" />
    <Content Include="Resources\category_add.png" />
    <Content Include="Resources\Close_Box_Red.png" />
    <Content Include="Resources\delegate.png" />
    <Content Include="Resources\Discount.png" />
    <Content Include="Resources\Discount1.png" />
    <None Include="Resources\email.png" />
    <Content Include="Resources\Edit_1.png" />
    <Content Include="Resources\Employees.png" />
    <Content Include="Resources\Expired.png" />
    <Content Include="Resources\finance.png" />
    <Content Include="Resources\Financial_Position.png" />
    <Content Include="Resources\find_1.png" />
    <None Include="Resources\going1.png" />
    <Content Include="Resources\Form-add-1.png" />
    <Content Include="Resources\Form-back-1.png" />
    <Content Include="Resources\Form-delete-1.png" />
    <Content Include="Resources\Form-edit-1.png" />
    <Content Include="Resources\Form-save-1.png" />
    <Content Include="Resources\Form-search-1.png" />
    <Content Include="Resources\gtk-refresh.PNG" />
    <Content Include="Resources\link_red.png" />
    <Content Include="Resources\MAR2012-main.jpg" />
    <Content Include="Resources\next_3.png" />
    <Content Include="Resources\Offers_Items.png" />
    <Content Include="Resources\previous_3.png" />
    <Content Include="Resources\Print_1.png" />
    <Content Include="Resources\Print_bill.png" />
    <None Include="Resources\Print_Smoll.png" />
    <Content Include="Resources\refresh.png" />
    <Content Include="Resources\reports.png" />
    <Content Include="Resources\1473532297_file_add.png" />
    <Content Include="Resources\1473545928_file_delete.png" />
    <Content Include="Resources\1473545934_file_edit.png" />
    <Content Include="Resources\Add-Male-User.png" />
    <Content Include="Resources\Add.png" />
    <Content Include="Resources\Add_1.png" />
    <Content Include="Resources\add_images.png" />
    <Content Include="Resources\barcode.png" />
    <Content Include="Resources\barcode_scanner.png" />
    <Content Include="Resources\close.png" />
    <Content Include="Resources\Delete.png" />
    <Content Include="Resources\delete1.png" />
    <Content Include="Resources\delete2.png" />
    <Content Include="Resources\empty.png" />
    <Content Include="Resources\Finances.png" />
    <Content Include="Resources\Items-add-1.png" />
    <Content Include="Resources\Items-add-2.png" />
    <Content Include="Resources\Items-add-5.png" />
    <Content Include="Resources\items-edit-1.png" />
    <Content Include="Resources\items-edit-2.png" />
    <Content Include="Resources\items_add_3.png" />
    <Content Include="Resources\items_add_4.png" />
    <Content Include="Resources\items_search_1.png" />
    <Content Include="Resources\MR.jpg" />
    <Content Include="Resources\Network.ico" />
    <Content Include="Resources\photos.png" />
    <Content Include="Resources\print_parcode.png" />
    <Content Include="Resources\print_price.png" />
    <Content Include="Resources\Purchases.png" />
    <Content Include="Resources\Purchases1.png" />
    <Content Include="Resources\Recyclebin.png" />
    <Content Include="Resources\save_32.png" />
    <Content Include="Resources\Save_bill.png" />
    <Content Include="Resources\Search3.png" />
    <Content Include="Resources\Settings.png" />
    <Content Include="Resources\Store_Entry.png" />
    <Content Include="Resources\Text-edit.png" />
    <None Include="ClassDiagram1.cd" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.5.2">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.5.2 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.2.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 2.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="obj\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>