﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class FrmSalesTouch
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FrmSalesTouch))
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.Panel5 = New System.Windows.Forms.Panel()
        Me.FlowLay_Group = New System.Windows.Forms.FlowLayoutPanel()
        Me.Panel52 = New System.Windows.Forms.Panel()
        Me.Panel14 = New System.Windows.Forms.Panel()
        Me.Panel6 = New System.Windows.Forms.Panel()
        Me.PanelPayingStaying = New System.Windows.Forms.Panel()
        Me.btnOKPayingStaying = New System.Windows.Forms.Button()
        Me.txtpayingWindow = New System.Windows.Forms.TextBox()
        Me.txtstayingWindow = New System.Windows.Forms.TextBox()
        Me.Label29 = New System.Windows.Forms.Label()
        Me.Label31 = New System.Windows.Forms.Label()
        Me.Panel21 = New System.Windows.Forms.Panel()
        Me.Panel23 = New System.Windows.Forms.Panel()
        Me.Panel24 = New System.Windows.Forms.Panel()
        Me.Panel25 = New System.Windows.Forms.Panel()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.btnClosePanelPayingStaying = New System.Windows.Forms.Button()
        Me.PanelSelectPrinter = New System.Windows.Forms.Panel()
        Me.txtTreasuryName = New System.Windows.Forms.TextBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.chkActivatePaidAndRest = New System.Windows.Forms.CheckBox()
        Me.chkprint = New System.Windows.Forms.CheckBox()
        Me.rdoPrintPreview = New System.Windows.Forms.RadioButton()
        Me.rdoPrintDirect = New System.Windows.Forms.RadioButton()
        Me.Panel45 = New System.Windows.Forms.Panel()
        Me.cmbTreasuryName = New System.Windows.Forms.ComboBox()
        Me.lblTreasuryName = New System.Windows.Forms.Label()
        Me.Panel46 = New System.Windows.Forms.Panel()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Panel47 = New System.Windows.Forms.Panel()
        Me.cmbStores = New System.Windows.Forms.ComboBox()
        Me.Panel48 = New System.Windows.Forms.Panel()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.btnCloseSelectPrinter = New System.Windows.Forms.Button()
        Me.Dgv_Add = New System.Windows.Forms.DataGridView()
        Me.Panel22 = New System.Windows.Forms.Panel()
        Me.Panel20 = New System.Windows.Forms.Panel()
        Me.Panel19 = New System.Windows.Forms.Panel()
        Me.Panel16 = New System.Windows.Forms.Panel()
        Me.cmbEmployees = New System.Windows.Forms.ComboBox()
        Me.txtSheft_Number = New System.Windows.Forms.TextBox()
        Me.picQRCode = New System.Windows.Forms.PictureBox()
        Me.TxtHour = New System.Windows.Forms.TextBox()
        Me.cmbname = New System.Windows.Forms.ComboBox()
        Me.txtprc = New System.Windows.Forms.TextBox()
        Me.cmbvendores = New System.Windows.Forms.ComboBox()
        Me.txtTimeAMPM = New System.Windows.Forms.TextBox()
        Me.DateTimePicker1 = New System.Windows.Forms.DateTimePicker()
        Me.TxtDay = New System.Windows.Forms.TextBox()
        Me.Panel11 = New System.Windows.Forms.Panel()
        Me.btnChackEnd = New System.Windows.Forms.Button()
        Me.FlowLayoutPanel3 = New System.Windows.Forms.FlowLayoutPanel()
        Me.btn9 = New System.Windows.Forms.Button()
        Me.btn8 = New System.Windows.Forms.Button()
        Me.btn7 = New System.Windows.Forms.Button()
        Me.btn6 = New System.Windows.Forms.Button()
        Me.btn5 = New System.Windows.Forms.Button()
        Me.btn4 = New System.Windows.Forms.Button()
        Me.btn3 = New System.Windows.Forms.Button()
        Me.btn2 = New System.Windows.Forms.Button()
        Me.btn1 = New System.Windows.Forms.Button()
        Me.btn0 = New System.Windows.Forms.Button()
        Me.btnDecimal = New System.Windows.Forms.Button()
        Me.Panel13 = New System.Windows.Forms.Panel()
        Me.btnClearTextbox = New System.Windows.Forms.Button()
        Me.txttotalpeforedisc = New System.Windows.Forms.TextBox()
        Me.btnDelete_Items = New System.Windows.Forms.Button()
        Me.Panel15 = New System.Windows.Forms.Panel()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.txtdisc = New System.Windows.Forms.TextBox()
        Me.Panel17 = New System.Windows.Forms.Panel()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.txttotalafterdisc = New System.Windows.Forms.TextBox()
        Me.Panel18 = New System.Windows.Forms.Panel()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.btnPrintChackEnd = New System.Windows.Forms.Button()
        Me.Panel10 = New System.Windows.Forms.Panel()
        Me.Panel8 = New System.Windows.Forms.Panel()
        Me.Panel9 = New System.Windows.Forms.Panel()
        Me.Panel7 = New System.Windows.Forms.Panel()
        Me.FlowLay_Items = New System.Windows.Forms.FlowLayoutPanel()
        Me.Panel12 = New System.Windows.Forms.Panel()
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.lblSheft_Number = New System.Windows.Forms.Label()
        Me.Label52 = New System.Windows.Forms.Label()
        Me.btnSelectPrinter = New System.Windows.Forms.Button()
        Me.txtCountItems = New System.Windows.Forms.Label()
        Me.lblPermtionName = New System.Windows.Forms.Label()
        Me.lblEmpName = New System.Windows.Forms.Label()
        Me.lblTypeOrders = New System.Windows.Forms.Label()
        Me.lblOrderNO = New System.Windows.Forms.Label()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.lblAutobillnoDay = New System.Windows.Forms.Label()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.lblUserName = New System.Windows.Forms.Label()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.Button8 = New System.Windows.Forms.Button()
        Me.Button7 = New System.Windows.Forms.Button()
        Me.Button6 = New System.Windows.Forms.Button()
        Me.Button5 = New System.Windows.Forms.Button()
        Me.Button4 = New System.Windows.Forms.Button()
        Me.Button3 = New System.Windows.Forms.Button()
        Me.Button2 = New System.Windows.Forms.Button()
        Me.Button1 = New System.Windows.Forms.Button()
        Me.Panel4 = New System.Windows.Forms.Panel()
        Me.Panel30 = New System.Windows.Forms.Panel()
        Me.btnMinimized = New System.Windows.Forms.Button()
        Me.btnClose = New System.Windows.Forms.Button()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.Panel5.SuspendLayout()
        Me.Panel6.SuspendLayout()
        Me.PanelPayingStaying.SuspendLayout()
        Me.Panel25.SuspendLayout()
        Me.PanelSelectPrinter.SuspendLayout()
        Me.Panel48.SuspendLayout()
        CType(Me.Dgv_Add, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel22.SuspendLayout()
        CType(Me.picQRCode, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel11.SuspendLayout()
        Me.FlowLayoutPanel3.SuspendLayout()
        Me.Panel13.SuspendLayout()
        Me.Panel15.SuspendLayout()
        Me.Panel17.SuspendLayout()
        Me.Panel18.SuspendLayout()
        Me.Panel7.SuspendLayout()
        Me.Panel2.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'Panel5
        '
        Me.Panel5.BackColor = System.Drawing.Color.FromArgb(CType(CType(8, Byte), Integer), CType(CType(62, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel5.Controls.Add(Me.FlowLay_Group)
        Me.Panel5.Controls.Add(Me.Panel52)
        Me.Panel5.Controls.Add(Me.Panel14)
        Me.Panel5.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel5.Location = New System.Drawing.Point(0, 77)
        Me.Panel5.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel5.Name = "Panel5"
        Me.Panel5.Size = New System.Drawing.Size(297, 698)
        Me.Panel5.TabIndex = 10
        '
        'FlowLay_Group
        '
        Me.FlowLay_Group.AutoScroll = True
        Me.FlowLay_Group.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.FlowLay_Group.Dock = System.Windows.Forms.DockStyle.Left
        Me.FlowLay_Group.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.FlowLay_Group.Location = New System.Drawing.Point(0, 0)
        Me.FlowLay_Group.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.FlowLay_Group.Name = "FlowLay_Group"
        Me.FlowLay_Group.Size = New System.Drawing.Size(291, 692)
        Me.FlowLay_Group.TabIndex = 306
        '
        'Panel52
        '
        Me.Panel52.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel52.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel52.Location = New System.Drawing.Point(0, 692)
        Me.Panel52.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel52.Name = "Panel52"
        Me.Panel52.Size = New System.Drawing.Size(294, 6)
        Me.Panel52.TabIndex = 6
        '
        'Panel14
        '
        Me.Panel14.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel14.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel14.Location = New System.Drawing.Point(294, 0)
        Me.Panel14.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel14.Name = "Panel14"
        Me.Panel14.Size = New System.Drawing.Size(3, 698)
        Me.Panel14.TabIndex = 3
        '
        'Panel6
        '
        Me.Panel6.BackColor = System.Drawing.Color.FromArgb(CType(CType(239, Byte), Integer), CType(CType(240, Byte), Integer), CType(CType(241, Byte), Integer))
        Me.Panel6.Controls.Add(Me.PanelSelectPrinter)
        Me.Panel6.Controls.Add(Me.PanelPayingStaying)
        Me.Panel6.Controls.Add(Me.Dgv_Add)
        Me.Panel6.Controls.Add(Me.Panel22)
        Me.Panel6.Controls.Add(Me.Panel11)
        Me.Panel6.Controls.Add(Me.Panel10)
        Me.Panel6.Controls.Add(Me.Panel8)
        Me.Panel6.Controls.Add(Me.Panel9)
        Me.Panel6.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel6.Location = New System.Drawing.Point(717, 77)
        Me.Panel6.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel6.Name = "Panel6"
        Me.Panel6.Size = New System.Drawing.Size(491, 698)
        Me.Panel6.TabIndex = 11
        '
        'PanelPayingStaying
        '
        Me.PanelPayingStaying.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.PanelPayingStaying.Controls.Add(Me.btnOKPayingStaying)
        Me.PanelPayingStaying.Controls.Add(Me.txtpayingWindow)
        Me.PanelPayingStaying.Controls.Add(Me.txtstayingWindow)
        Me.PanelPayingStaying.Controls.Add(Me.Label29)
        Me.PanelPayingStaying.Controls.Add(Me.Label31)
        Me.PanelPayingStaying.Controls.Add(Me.Panel21)
        Me.PanelPayingStaying.Controls.Add(Me.Panel23)
        Me.PanelPayingStaying.Controls.Add(Me.Panel24)
        Me.PanelPayingStaying.Controls.Add(Me.Panel25)
        Me.PanelPayingStaying.Location = New System.Drawing.Point(53, 381)
        Me.PanelPayingStaying.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.PanelPayingStaying.Name = "PanelPayingStaying"
        Me.PanelPayingStaying.Size = New System.Drawing.Size(384, 235)
        Me.PanelPayingStaying.TabIndex = 373
        '
        'btnOKPayingStaying
        '
        Me.btnOKPayingStaying.BackColor = System.Drawing.Color.FromArgb(CType(CType(14, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(144, Byte), Integer))
        Me.btnOKPayingStaying.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnOKPayingStaying.Font = New System.Drawing.Font("JF Flat", 10.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnOKPayingStaying.ForeColor = System.Drawing.Color.White
        Me.btnOKPayingStaying.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btnOKPayingStaying.Location = New System.Drawing.Point(64, 172)
        Me.btnOKPayingStaying.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnOKPayingStaying.Name = "btnOKPayingStaying"
        Me.btnOKPayingStaying.Size = New System.Drawing.Size(173, 53)
        Me.btnOKPayingStaying.TabIndex = 224
        Me.btnOKPayingStaying.Text = "إغلاق"
        Me.btnOKPayingStaying.UseVisualStyleBackColor = False
        '
        'txtpayingWindow
        '
        Me.txtpayingWindow.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.txtpayingWindow.Font = New System.Drawing.Font("Arial", 18.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtpayingWindow.ForeColor = System.Drawing.Color.FromArgb(CType(CType(172, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.txtpayingWindow.Location = New System.Drawing.Point(64, 69)
        Me.txtpayingWindow.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtpayingWindow.Name = "txtpayingWindow"
        Me.txtpayingWindow.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtpayingWindow.Size = New System.Drawing.Size(173, 42)
        Me.txtpayingWindow.TabIndex = 222
        Me.txtpayingWindow.Text = "0"
        Me.txtpayingWindow.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'txtstayingWindow
        '
        Me.txtstayingWindow.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.txtstayingWindow.Enabled = False
        Me.txtstayingWindow.Font = New System.Drawing.Font("Arial", 18.0!, System.Drawing.FontStyle.Bold)
        Me.txtstayingWindow.ForeColor = System.Drawing.Color.FromArgb(CType(CType(172, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.txtstayingWindow.Location = New System.Drawing.Point(64, 119)
        Me.txtstayingWindow.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtstayingWindow.Name = "txtstayingWindow"
        Me.txtstayingWindow.ReadOnly = True
        Me.txtstayingWindow.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtstayingWindow.Size = New System.Drawing.Size(173, 42)
        Me.txtstayingWindow.TabIndex = 223
        Me.txtstayingWindow.Text = "0"
        Me.txtstayingWindow.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label29
        '
        Me.Label29.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label29.AutoSize = True
        Me.Label29.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label29.ForeColor = System.Drawing.SystemColors.Desktop
        Me.Label29.Location = New System.Drawing.Point(243, 128)
        Me.Label29.Name = "Label29"
        Me.Label29.Size = New System.Drawing.Size(61, 26)
        Me.Label29.TabIndex = 220
        Me.Label29.Text = "الباقي"
        '
        'Label31
        '
        Me.Label31.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label31.AutoSize = True
        Me.Label31.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label31.ForeColor = System.Drawing.SystemColors.Desktop
        Me.Label31.Location = New System.Drawing.Point(243, 76)
        Me.Label31.Name = "Label31"
        Me.Label31.Size = New System.Drawing.Size(75, 26)
        Me.Label31.TabIndex = 219
        Me.Label31.Text = "المدفوع"
        '
        'Panel21
        '
        Me.Panel21.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel21.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel21.Location = New System.Drawing.Point(0, 58)
        Me.Panel21.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel21.Name = "Panel21"
        Me.Panel21.Size = New System.Drawing.Size(6, 171)
        Me.Panel21.TabIndex = 205
        '
        'Panel23
        '
        Me.Panel23.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel23.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel23.Location = New System.Drawing.Point(378, 58)
        Me.Panel23.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel23.Name = "Panel23"
        Me.Panel23.Size = New System.Drawing.Size(6, 171)
        Me.Panel23.TabIndex = 204
        '
        'Panel24
        '
        Me.Panel24.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel24.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel24.Location = New System.Drawing.Point(0, 229)
        Me.Panel24.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel24.Name = "Panel24"
        Me.Panel24.Size = New System.Drawing.Size(384, 6)
        Me.Panel24.TabIndex = 203
        '
        'Panel25
        '
        Me.Panel25.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel25.Controls.Add(Me.Label3)
        Me.Panel25.Controls.Add(Me.btnClosePanelPayingStaying)
        Me.Panel25.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel25.Location = New System.Drawing.Point(0, 0)
        Me.Panel25.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel25.Name = "Panel25"
        Me.Panel25.Size = New System.Drawing.Size(384, 58)
        Me.Panel25.TabIndex = 202
        '
        'Label3
        '
        Me.Label3.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("JF Flat", 12.0!, System.Drawing.FontStyle.Bold)
        Me.Label3.ForeColor = System.Drawing.Color.White
        Me.Label3.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.Label3.Location = New System.Drawing.Point(82, 12)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(196, 34)
        Me.Label3.TabIndex = 222
        Me.Label3.Text = "المدفوع والمتبقى"
        '
        'btnClosePanelPayingStaying
        '
        Me.btnClosePanelPayingStaying.BackColor = System.Drawing.Color.Transparent
        Me.btnClosePanelPayingStaying.BackgroundImage = CType(resources.GetObject("btnClosePanelPayingStaying.BackgroundImage"), System.Drawing.Image)
        Me.btnClosePanelPayingStaying.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnClosePanelPayingStaying.Dock = System.Windows.Forms.DockStyle.Right
        Me.btnClosePanelPayingStaying.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btnClosePanelPayingStaying.Location = New System.Drawing.Point(326, 0)
        Me.btnClosePanelPayingStaying.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnClosePanelPayingStaying.Name = "btnClosePanelPayingStaying"
        Me.btnClosePanelPayingStaying.Size = New System.Drawing.Size(58, 58)
        Me.btnClosePanelPayingStaying.TabIndex = 206
        Me.btnClosePanelPayingStaying.UseVisualStyleBackColor = False
        '
        'PanelSelectPrinter
        '
        Me.PanelSelectPrinter.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.PanelSelectPrinter.Controls.Add(Me.txtTreasuryName)
        Me.PanelSelectPrinter.Controls.Add(Me.Label1)
        Me.PanelSelectPrinter.Controls.Add(Me.chkActivatePaidAndRest)
        Me.PanelSelectPrinter.Controls.Add(Me.chkprint)
        Me.PanelSelectPrinter.Controls.Add(Me.rdoPrintPreview)
        Me.PanelSelectPrinter.Controls.Add(Me.rdoPrintDirect)
        Me.PanelSelectPrinter.Controls.Add(Me.Panel45)
        Me.PanelSelectPrinter.Controls.Add(Me.cmbTreasuryName)
        Me.PanelSelectPrinter.Controls.Add(Me.lblTreasuryName)
        Me.PanelSelectPrinter.Controls.Add(Me.Panel46)
        Me.PanelSelectPrinter.Controls.Add(Me.Label8)
        Me.PanelSelectPrinter.Controls.Add(Me.Panel47)
        Me.PanelSelectPrinter.Controls.Add(Me.cmbStores)
        Me.PanelSelectPrinter.Controls.Add(Me.Panel48)
        Me.PanelSelectPrinter.Location = New System.Drawing.Point(38, 152)
        Me.PanelSelectPrinter.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.PanelSelectPrinter.Name = "PanelSelectPrinter"
        Me.PanelSelectPrinter.Size = New System.Drawing.Size(384, 256)
        Me.PanelSelectPrinter.TabIndex = 372
        '
        'txtTreasuryName
        '
        Me.txtTreasuryName.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtTreasuryName.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.txtTreasuryName.Enabled = False
        Me.txtTreasuryName.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.txtTreasuryName.ForeColor = System.Drawing.Color.White
        Me.txtTreasuryName.Location = New System.Drawing.Point(59, 204)
        Me.txtTreasuryName.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtTreasuryName.Name = "txtTreasuryName"
        Me.txtTreasuryName.ReadOnly = True
        Me.txtTreasuryName.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtTreasuryName.Size = New System.Drawing.Size(216, 36)
        Me.txtTreasuryName.TabIndex = 421
        Me.txtTreasuryName.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label1
        '
        Me.Label1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label1.ForeColor = System.Drawing.Color.Black
        Me.Label1.Location = New System.Drawing.Point(278, 209)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(63, 26)
        Me.Label1.TabIndex = 420
        Me.Label1.Text = "الخزينة"
        '
        'chkActivatePaidAndRest
        '
        Me.chkActivatePaidAndRest.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.chkActivatePaidAndRest.AutoSize = True
        Me.chkActivatePaidAndRest.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.chkActivatePaidAndRest.ForeColor = System.Drawing.Color.Black
        Me.chkActivatePaidAndRest.Location = New System.Drawing.Point(111, 61)
        Me.chkActivatePaidAndRest.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.chkActivatePaidAndRest.Name = "chkActivatePaidAndRest"
        Me.chkActivatePaidAndRest.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkActivatePaidAndRest.Size = New System.Drawing.Size(212, 30)
        Me.chkActivatePaidAndRest.TabIndex = 418
        Me.chkActivatePaidAndRest.Text = "تفعيل المدفوع والباقى"
        Me.chkActivatePaidAndRest.UseVisualStyleBackColor = True
        '
        'chkprint
        '
        Me.chkprint.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.chkprint.AutoSize = True
        Me.chkprint.Checked = True
        Me.chkprint.CheckState = System.Windows.Forms.CheckState.Checked
        Me.chkprint.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.chkprint.ForeColor = System.Drawing.Color.Black
        Me.chkprint.Location = New System.Drawing.Point(181, 94)
        Me.chkprint.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.chkprint.Name = "chkprint"
        Me.chkprint.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkprint.Size = New System.Drawing.Size(142, 30)
        Me.chkprint.TabIndex = 376
        Me.chkprint.Text = "طباعة الفاتورة"
        Me.chkprint.UseVisualStyleBackColor = True
        '
        'rdoPrintPreview
        '
        Me.rdoPrintPreview.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.rdoPrintPreview.AutoSize = True
        Me.rdoPrintPreview.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.rdoPrintPreview.ForeColor = System.Drawing.Color.Black
        Me.rdoPrintPreview.Location = New System.Drawing.Point(60, 126)
        Me.rdoPrintPreview.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.rdoPrintPreview.Name = "rdoPrintPreview"
        Me.rdoPrintPreview.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.rdoPrintPreview.Size = New System.Drawing.Size(115, 30)
        Me.rdoPrintPreview.TabIndex = 375
        Me.rdoPrintPreview.Text = "عرض طباعة"
        Me.rdoPrintPreview.UseVisualStyleBackColor = True
        '
        'rdoPrintDirect
        '
        Me.rdoPrintDirect.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.rdoPrintDirect.AutoSize = True
        Me.rdoPrintDirect.Checked = True
        Me.rdoPrintDirect.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.rdoPrintDirect.ForeColor = System.Drawing.Color.Black
        Me.rdoPrintDirect.Location = New System.Drawing.Point(189, 126)
        Me.rdoPrintDirect.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.rdoPrintDirect.Name = "rdoPrintDirect"
        Me.rdoPrintDirect.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.rdoPrintDirect.Size = New System.Drawing.Size(133, 30)
        Me.rdoPrintDirect.TabIndex = 374
        Me.rdoPrintDirect.TabStop = True
        Me.rdoPrintDirect.Text = "طباعة مباشرة"
        Me.rdoPrintDirect.UseVisualStyleBackColor = True
        '
        'Panel45
        '
        Me.Panel45.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel45.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel45.Location = New System.Drawing.Point(0, 58)
        Me.Panel45.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel45.Name = "Panel45"
        Me.Panel45.Size = New System.Drawing.Size(6, 192)
        Me.Panel45.TabIndex = 205
        '
        'cmbTreasuryName
        '
        Me.cmbTreasuryName.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbTreasuryName.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbTreasuryName.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbTreasuryName.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.cmbTreasuryName.FormattingEnabled = True
        Me.cmbTreasuryName.Location = New System.Drawing.Point(58, 253)
        Me.cmbTreasuryName.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbTreasuryName.Name = "cmbTreasuryName"
        Me.cmbTreasuryName.Size = New System.Drawing.Size(216, 34)
        Me.cmbTreasuryName.TabIndex = 413
        '
        'lblTreasuryName
        '
        Me.lblTreasuryName.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblTreasuryName.AutoSize = True
        Me.lblTreasuryName.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.lblTreasuryName.Location = New System.Drawing.Point(281, 257)
        Me.lblTreasuryName.Name = "lblTreasuryName"
        Me.lblTreasuryName.Size = New System.Drawing.Size(56, 24)
        Me.lblTreasuryName.TabIndex = 414
        Me.lblTreasuryName.Text = "الخزينة"
        Me.lblTreasuryName.Visible = False
        '
        'Panel46
        '
        Me.Panel46.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel46.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel46.Location = New System.Drawing.Point(378, 58)
        Me.Panel46.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel46.Name = "Panel46"
        Me.Panel46.Size = New System.Drawing.Size(6, 192)
        Me.Panel46.TabIndex = 204
        '
        'Label8
        '
        Me.Label8.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label8.AutoSize = True
        Me.Label8.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label8.ForeColor = System.Drawing.Color.Black
        Me.Label8.Location = New System.Drawing.Point(278, 168)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(60, 26)
        Me.Label8.TabIndex = 417
        Me.Label8.Text = "المخزن"
        '
        'Panel47
        '
        Me.Panel47.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel47.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel47.Location = New System.Drawing.Point(0, 250)
        Me.Panel47.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel47.Name = "Panel47"
        Me.Panel47.Size = New System.Drawing.Size(384, 6)
        Me.Panel47.TabIndex = 203
        '
        'cmbStores
        '
        Me.cmbStores.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbStores.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbStores.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbStores.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.cmbStores.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.cmbStores.FormattingEnabled = True
        Me.cmbStores.Location = New System.Drawing.Point(59, 163)
        Me.cmbStores.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbStores.Name = "cmbStores"
        Me.cmbStores.Size = New System.Drawing.Size(216, 36)
        Me.cmbStores.TabIndex = 416
        '
        'Panel48
        '
        Me.Panel48.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel48.Controls.Add(Me.Label10)
        Me.Panel48.Controls.Add(Me.btnCloseSelectPrinter)
        Me.Panel48.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel48.Location = New System.Drawing.Point(0, 0)
        Me.Panel48.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel48.Name = "Panel48"
        Me.Panel48.Size = New System.Drawing.Size(384, 58)
        Me.Panel48.TabIndex = 202
        '
        'Label10
        '
        Me.Label10.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label10.AutoSize = True
        Me.Label10.Font = New System.Drawing.Font("JF Flat", 12.0!, System.Drawing.FontStyle.Bold)
        Me.Label10.ForeColor = System.Drawing.Color.White
        Me.Label10.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.Label10.Location = New System.Drawing.Point(127, 11)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(148, 34)
        Me.Label10.TabIndex = 222
        Me.Label10.Text = "تحديد الطابعة"
        '
        'btnCloseSelectPrinter
        '
        Me.btnCloseSelectPrinter.BackColor = System.Drawing.Color.Transparent
        Me.btnCloseSelectPrinter.BackgroundImage = CType(resources.GetObject("btnCloseSelectPrinter.BackgroundImage"), System.Drawing.Image)
        Me.btnCloseSelectPrinter.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnCloseSelectPrinter.Dock = System.Windows.Forms.DockStyle.Right
        Me.btnCloseSelectPrinter.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btnCloseSelectPrinter.Location = New System.Drawing.Point(326, 0)
        Me.btnCloseSelectPrinter.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnCloseSelectPrinter.Name = "btnCloseSelectPrinter"
        Me.btnCloseSelectPrinter.Size = New System.Drawing.Size(58, 58)
        Me.btnCloseSelectPrinter.TabIndex = 206
        Me.btnCloseSelectPrinter.UseVisualStyleBackColor = False
        '
        'Dgv_Add
        '
        Me.Dgv_Add.AllowUserToAddRows = False
        Me.Dgv_Add.AllowUserToDeleteRows = False
        Me.Dgv_Add.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill
        Me.Dgv_Add.BackgroundColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        DataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle1.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle1.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.Dgv_Add.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle1
        Me.Dgv_Add.ColumnHeadersHeight = 40
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window
        DataGridViewCellStyle2.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.Dgv_Add.DefaultCellStyle = DataGridViewCellStyle2
        Me.Dgv_Add.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Dgv_Add.Location = New System.Drawing.Point(3, 174)
        Me.Dgv_Add.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Dgv_Add.Name = "Dgv_Add"
        Me.Dgv_Add.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        DataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle3.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle3.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle3.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle3.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.Dgv_Add.RowHeadersDefaultCellStyle = DataGridViewCellStyle3
        Me.Dgv_Add.RowHeadersWidth = 20
        DataGridViewCellStyle4.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.SteelBlue
        Me.Dgv_Add.RowsDefaultCellStyle = DataGridViewCellStyle4
        Me.Dgv_Add.RowTemplate.Height = 32
        Me.Dgv_Add.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.Dgv_Add.Size = New System.Drawing.Size(482, 217)
        Me.Dgv_Add.TabIndex = 369
        '
        'Panel22
        '
        Me.Panel22.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.Panel22.Controls.Add(Me.Panel20)
        Me.Panel22.Controls.Add(Me.Panel19)
        Me.Panel22.Controls.Add(Me.Panel16)
        Me.Panel22.Controls.Add(Me.cmbEmployees)
        Me.Panel22.Controls.Add(Me.txtSheft_Number)
        Me.Panel22.Controls.Add(Me.picQRCode)
        Me.Panel22.Controls.Add(Me.TxtHour)
        Me.Panel22.Controls.Add(Me.cmbname)
        Me.Panel22.Controls.Add(Me.txtprc)
        Me.Panel22.Controls.Add(Me.cmbvendores)
        Me.Panel22.Controls.Add(Me.txtTimeAMPM)
        Me.Panel22.Controls.Add(Me.DateTimePicker1)
        Me.Panel22.Controls.Add(Me.TxtDay)
        Me.Panel22.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel22.Location = New System.Drawing.Point(3, 0)
        Me.Panel22.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel22.Name = "Panel22"
        Me.Panel22.Size = New System.Drawing.Size(482, 174)
        Me.Panel22.TabIndex = 367
        '
        'Panel20
        '
        Me.Panel20.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Find_Customers
        Me.Panel20.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.Panel20.Location = New System.Drawing.Point(436, 46)
        Me.Panel20.Name = "Panel20"
        Me.Panel20.Size = New System.Drawing.Size(40, 36)
        Me.Panel20.TabIndex = 495
        '
        'Panel19
        '
        Me.Panel19.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Find_Parcode
        Me.Panel19.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.Panel19.Location = New System.Drawing.Point(436, 131)
        Me.Panel19.Name = "Panel19"
        Me.Panel19.Size = New System.Drawing.Size(40, 36)
        Me.Panel19.TabIndex = 493
        '
        'Panel16
        '
        Me.Panel16.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Find_Items
        Me.Panel16.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.Panel16.Location = New System.Drawing.Point(436, 90)
        Me.Panel16.Name = "Panel16"
        Me.Panel16.Size = New System.Drawing.Size(40, 36)
        Me.Panel16.TabIndex = 492
        '
        'cmbEmployees
        '
        Me.cmbEmployees.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbEmployees.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbEmployees.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbEmployees.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.cmbEmployees.FormattingEnabled = True
        Me.cmbEmployees.Location = New System.Drawing.Point(148, 302)
        Me.cmbEmployees.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbEmployees.Name = "cmbEmployees"
        Me.cmbEmployees.Size = New System.Drawing.Size(216, 34)
        Me.cmbEmployees.TabIndex = 491
        '
        'txtSheft_Number
        '
        Me.txtSheft_Number.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtSheft_Number.BackColor = System.Drawing.SystemColors.Window
        Me.txtSheft_Number.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.txtSheft_Number.ForeColor = System.Drawing.Color.White
        Me.txtSheft_Number.Location = New System.Drawing.Point(19, 230)
        Me.txtSheft_Number.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtSheft_Number.Name = "txtSheft_Number"
        Me.txtSheft_Number.ReadOnly = True
        Me.txtSheft_Number.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtSheft_Number.Size = New System.Drawing.Size(158, 36)
        Me.txtSheft_Number.TabIndex = 490
        Me.txtSheft_Number.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'picQRCode
        '
        Me.picQRCode.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.picQRCode.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.picQRCode.Location = New System.Drawing.Point(200, 286)
        Me.picQRCode.Name = "picQRCode"
        Me.picQRCode.Size = New System.Drawing.Size(53, 53)
        Me.picQRCode.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.picQRCode.TabIndex = 489
        Me.picQRCode.TabStop = False
        Me.picQRCode.Visible = False
        '
        'TxtHour
        '
        Me.TxtHour.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TxtHour.BackColor = System.Drawing.SystemColors.Window
        Me.TxtHour.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.TxtHour.ForeColor = System.Drawing.Color.White
        Me.TxtHour.Location = New System.Drawing.Point(19, 186)
        Me.TxtHour.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.TxtHour.Name = "TxtHour"
        Me.TxtHour.ReadOnly = True
        Me.TxtHour.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.TxtHour.Size = New System.Drawing.Size(158, 36)
        Me.TxtHour.TabIndex = 415
        Me.TxtHour.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'cmbname
        '
        Me.cmbname.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbname.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbname.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbname.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.cmbname.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.cmbname.FormattingEnabled = True
        Me.cmbname.Location = New System.Drawing.Point(6, 90)
        Me.cmbname.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbname.Name = "cmbname"
        Me.cmbname.Size = New System.Drawing.Size(424, 36)
        Me.cmbname.TabIndex = 366
        '
        'txtprc
        '
        Me.txtprc.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtprc.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.txtprc.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.txtprc.Location = New System.Drawing.Point(6, 131)
        Me.txtprc.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtprc.Name = "txtprc"
        Me.txtprc.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.txtprc.Size = New System.Drawing.Size(424, 36)
        Me.txtprc.TabIndex = 365
        Me.txtprc.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'cmbvendores
        '
        Me.cmbvendores.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbvendores.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbvendores.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbvendores.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.cmbvendores.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.cmbvendores.FormattingEnabled = True
        Me.cmbvendores.Items.AddRange(New Object() {"نقدا"})
        Me.cmbvendores.Location = New System.Drawing.Point(6, 50)
        Me.cmbvendores.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbvendores.Name = "cmbvendores"
        Me.cmbvendores.Size = New System.Drawing.Size(424, 36)
        Me.cmbvendores.TabIndex = 358
        Me.cmbvendores.Text = "بحث بأسم العميل ..."
        '
        'txtTimeAMPM
        '
        Me.txtTimeAMPM.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtTimeAMPM.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.txtTimeAMPM.Enabled = False
        Me.txtTimeAMPM.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.txtTimeAMPM.ForeColor = System.Drawing.Color.White
        Me.txtTimeAMPM.Location = New System.Drawing.Point(329, 8)
        Me.txtTimeAMPM.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtTimeAMPM.Name = "txtTimeAMPM"
        Me.txtTimeAMPM.ReadOnly = True
        Me.txtTimeAMPM.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtTimeAMPM.Size = New System.Drawing.Size(147, 36)
        Me.txtTimeAMPM.TabIndex = 356
        Me.txtTimeAMPM.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'DateTimePicker1
        '
        Me.DateTimePicker1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DateTimePicker1.CalendarMonthBackground = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.DateTimePicker1.CustomFormat = "dd/MM/yyyy"
        Me.DateTimePicker1.Enabled = False
        Me.DateTimePicker1.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.DateTimePicker1.Format = System.Windows.Forms.DateTimePickerFormat.[Short]
        Me.DateTimePicker1.Location = New System.Drawing.Point(6, 8)
        Me.DateTimePicker1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.DateTimePicker1.Name = "DateTimePicker1"
        Me.DateTimePicker1.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.DateTimePicker1.RightToLeftLayout = True
        Me.DateTimePicker1.Size = New System.Drawing.Size(171, 36)
        Me.DateTimePicker1.TabIndex = 350
        '
        'TxtDay
        '
        Me.TxtDay.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TxtDay.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.TxtDay.Enabled = False
        Me.TxtDay.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.TxtDay.ForeColor = System.Drawing.Color.White
        Me.TxtDay.Location = New System.Drawing.Point(183, 8)
        Me.TxtDay.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.TxtDay.Name = "TxtDay"
        Me.TxtDay.ReadOnly = True
        Me.TxtDay.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.TxtDay.Size = New System.Drawing.Size(140, 36)
        Me.TxtDay.TabIndex = 352
        Me.TxtDay.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Panel11
        '
        Me.Panel11.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.Panel11.Controls.Add(Me.btnChackEnd)
        Me.Panel11.Controls.Add(Me.FlowLayoutPanel3)
        Me.Panel11.Controls.Add(Me.Panel13)
        Me.Panel11.Controls.Add(Me.btnPrintChackEnd)
        Me.Panel11.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel11.Location = New System.Drawing.Point(3, 391)
        Me.Panel11.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel11.Name = "Panel11"
        Me.Panel11.Size = New System.Drawing.Size(482, 301)
        Me.Panel11.TabIndex = 368
        '
        'btnChackEnd
        '
        Me.btnChackEnd.BackColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(173, Byte), Integer))
        Me.btnChackEnd.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnChackEnd.Font = New System.Drawing.Font("JF Flat", 10.8!, System.Drawing.FontStyle.Bold)
        Me.btnChackEnd.ForeColor = System.Drawing.Color.White
        Me.btnChackEnd.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btnChackEnd.Location = New System.Drawing.Point(12, 240)
        Me.btnChackEnd.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnChackEnd.Name = "btnChackEnd"
        Me.btnChackEnd.Size = New System.Drawing.Size(232, 53)
        Me.btnChackEnd.TabIndex = 80
        Me.btnChackEnd.Text = "F6 حفظ"
        Me.btnChackEnd.UseVisualStyleBackColor = False
        '
        'FlowLayoutPanel3
        '
        Me.FlowLayoutPanel3.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.FlowLayoutPanel3.AutoScroll = True
        Me.FlowLayoutPanel3.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.FlowLayoutPanel3.Controls.Add(Me.btn9)
        Me.FlowLayoutPanel3.Controls.Add(Me.btn8)
        Me.FlowLayoutPanel3.Controls.Add(Me.btn7)
        Me.FlowLayoutPanel3.Controls.Add(Me.btn6)
        Me.FlowLayoutPanel3.Controls.Add(Me.btn5)
        Me.FlowLayoutPanel3.Controls.Add(Me.btn4)
        Me.FlowLayoutPanel3.Controls.Add(Me.btn3)
        Me.FlowLayoutPanel3.Controls.Add(Me.btn2)
        Me.FlowLayoutPanel3.Controls.Add(Me.btn1)
        Me.FlowLayoutPanel3.Controls.Add(Me.btn0)
        Me.FlowLayoutPanel3.Controls.Add(Me.btnDecimal)
        Me.FlowLayoutPanel3.Location = New System.Drawing.Point(271, 13)
        Me.FlowLayoutPanel3.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.FlowLayoutPanel3.Name = "FlowLayoutPanel3"
        Me.FlowLayoutPanel3.Size = New System.Drawing.Size(205, 215)
        Me.FlowLayoutPanel3.TabIndex = 79
        '
        'btn9
        '
        Me.btn9.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.btn9.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btn9.Font = New System.Drawing.Font("Arial Narrow", 13.8!, System.Drawing.FontStyle.Bold)
        Me.btn9.ForeColor = System.Drawing.Color.Black
        Me.btn9.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btn9.Location = New System.Drawing.Point(3, 4)
        Me.btn9.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btn9.Name = "btn9"
        Me.btn9.Size = New System.Drawing.Size(61, 44)
        Me.btn9.TabIndex = 72
        Me.btn9.Text = "9"
        Me.btn9.UseVisualStyleBackColor = False
        '
        'btn8
        '
        Me.btn8.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.btn8.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btn8.Font = New System.Drawing.Font("Arial Narrow", 13.8!, System.Drawing.FontStyle.Bold)
        Me.btn8.ForeColor = System.Drawing.Color.Black
        Me.btn8.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btn8.Location = New System.Drawing.Point(70, 4)
        Me.btn8.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btn8.Name = "btn8"
        Me.btn8.Size = New System.Drawing.Size(61, 44)
        Me.btn8.TabIndex = 71
        Me.btn8.Text = "8"
        Me.btn8.UseVisualStyleBackColor = False
        '
        'btn7
        '
        Me.btn7.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.btn7.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btn7.Font = New System.Drawing.Font("Arial Narrow", 13.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btn7.ForeColor = System.Drawing.Color.Black
        Me.btn7.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btn7.Location = New System.Drawing.Point(137, 4)
        Me.btn7.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btn7.Name = "btn7"
        Me.btn7.Size = New System.Drawing.Size(61, 44)
        Me.btn7.TabIndex = 70
        Me.btn7.Text = "7"
        Me.btn7.UseVisualStyleBackColor = False
        '
        'btn6
        '
        Me.btn6.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.btn6.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btn6.Font = New System.Drawing.Font("Arial Narrow", 13.8!, System.Drawing.FontStyle.Bold)
        Me.btn6.ForeColor = System.Drawing.Color.Black
        Me.btn6.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btn6.Location = New System.Drawing.Point(3, 56)
        Me.btn6.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btn6.Name = "btn6"
        Me.btn6.Size = New System.Drawing.Size(61, 44)
        Me.btn6.TabIndex = 75
        Me.btn6.Text = "6"
        Me.btn6.UseVisualStyleBackColor = False
        '
        'btn5
        '
        Me.btn5.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.btn5.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btn5.Font = New System.Drawing.Font("Arial Narrow", 13.8!, System.Drawing.FontStyle.Bold)
        Me.btn5.ForeColor = System.Drawing.Color.Black
        Me.btn5.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btn5.Location = New System.Drawing.Point(70, 56)
        Me.btn5.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btn5.Name = "btn5"
        Me.btn5.Size = New System.Drawing.Size(61, 44)
        Me.btn5.TabIndex = 74
        Me.btn5.Text = "5"
        Me.btn5.UseVisualStyleBackColor = False
        '
        'btn4
        '
        Me.btn4.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.btn4.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btn4.Font = New System.Drawing.Font("Arial Narrow", 13.8!, System.Drawing.FontStyle.Bold)
        Me.btn4.ForeColor = System.Drawing.Color.Black
        Me.btn4.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btn4.Location = New System.Drawing.Point(137, 56)
        Me.btn4.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btn4.Name = "btn4"
        Me.btn4.Size = New System.Drawing.Size(61, 44)
        Me.btn4.TabIndex = 73
        Me.btn4.Text = "4"
        Me.btn4.UseVisualStyleBackColor = False
        '
        'btn3
        '
        Me.btn3.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.btn3.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btn3.Font = New System.Drawing.Font("Arial Narrow", 13.8!, System.Drawing.FontStyle.Bold)
        Me.btn3.ForeColor = System.Drawing.Color.Black
        Me.btn3.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btn3.Location = New System.Drawing.Point(3, 108)
        Me.btn3.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btn3.Name = "btn3"
        Me.btn3.Size = New System.Drawing.Size(61, 44)
        Me.btn3.TabIndex = 78
        Me.btn3.Text = "3"
        Me.btn3.UseVisualStyleBackColor = False
        '
        'btn2
        '
        Me.btn2.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.btn2.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btn2.Font = New System.Drawing.Font("Arial Narrow", 13.8!, System.Drawing.FontStyle.Bold)
        Me.btn2.ForeColor = System.Drawing.Color.Black
        Me.btn2.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btn2.Location = New System.Drawing.Point(70, 108)
        Me.btn2.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btn2.Name = "btn2"
        Me.btn2.Size = New System.Drawing.Size(61, 44)
        Me.btn2.TabIndex = 77
        Me.btn2.Text = "2"
        Me.btn2.UseVisualStyleBackColor = False
        '
        'btn1
        '
        Me.btn1.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.btn1.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btn1.Font = New System.Drawing.Font("Arial Narrow", 13.8!, System.Drawing.FontStyle.Bold)
        Me.btn1.ForeColor = System.Drawing.Color.Black
        Me.btn1.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btn1.Location = New System.Drawing.Point(137, 108)
        Me.btn1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btn1.Name = "btn1"
        Me.btn1.Size = New System.Drawing.Size(61, 44)
        Me.btn1.TabIndex = 76
        Me.btn1.Text = "1"
        Me.btn1.UseVisualStyleBackColor = False
        '
        'btn0
        '
        Me.btn0.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.btn0.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btn0.Font = New System.Drawing.Font("Arial Narrow", 13.8!, System.Drawing.FontStyle.Bold)
        Me.btn0.ForeColor = System.Drawing.Color.Black
        Me.btn0.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btn0.Location = New System.Drawing.Point(3, 160)
        Me.btn0.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btn0.Name = "btn0"
        Me.btn0.Size = New System.Drawing.Size(128, 44)
        Me.btn0.TabIndex = 79
        Me.btn0.Text = "0"
        Me.btn0.UseVisualStyleBackColor = False
        '
        'btnDecimal
        '
        Me.btnDecimal.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.btnDecimal.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnDecimal.Font = New System.Drawing.Font("Arial Narrow", 13.8!, System.Drawing.FontStyle.Bold)
        Me.btnDecimal.ForeColor = System.Drawing.Color.Black
        Me.btnDecimal.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btnDecimal.Location = New System.Drawing.Point(137, 160)
        Me.btnDecimal.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnDecimal.Name = "btnDecimal"
        Me.btnDecimal.Size = New System.Drawing.Size(61, 44)
        Me.btnDecimal.TabIndex = 80
        Me.btnDecimal.Text = "."
        Me.btnDecimal.UseVisualStyleBackColor = False
        '
        'Panel13
        '
        Me.Panel13.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.Panel13.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.Panel13.Controls.Add(Me.btnClearTextbox)
        Me.Panel13.Controls.Add(Me.txttotalpeforedisc)
        Me.Panel13.Controls.Add(Me.btnDelete_Items)
        Me.Panel13.Controls.Add(Me.Panel15)
        Me.Panel13.Controls.Add(Me.txtdisc)
        Me.Panel13.Controls.Add(Me.Panel17)
        Me.Panel13.Controls.Add(Me.txttotalafterdisc)
        Me.Panel13.Controls.Add(Me.Panel18)
        Me.Panel13.Location = New System.Drawing.Point(6, 8)
        Me.Panel13.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel13.Name = "Panel13"
        Me.Panel13.Size = New System.Drawing.Size(262, 220)
        Me.Panel13.TabIndex = 79
        '
        'btnClearTextbox
        '
        Me.btnClearTextbox.BackColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(173, Byte), Integer))
        Me.btnClearTextbox.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnClearTextbox.Font = New System.Drawing.Font("Arial Narrow", 13.8!, System.Drawing.FontStyle.Bold)
        Me.btnClearTextbox.ForeColor = System.Drawing.Color.White
        Me.btnClearTextbox.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btnClearTextbox.Location = New System.Drawing.Point(6, 6)
        Me.btnClearTextbox.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnClearTextbox.Name = "btnClearTextbox"
        Me.btnClearTextbox.Size = New System.Drawing.Size(131, 46)
        Me.btnClearTextbox.TabIndex = 61
        Me.btnClearTextbox.Text = "CE"
        Me.btnClearTextbox.UseVisualStyleBackColor = False
        '
        'txttotalpeforedisc
        '
        Me.txttotalpeforedisc.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txttotalpeforedisc.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.txttotalpeforedisc.Font = New System.Drawing.Font("Arial", 18.0!, System.Drawing.FontStyle.Bold)
        Me.txttotalpeforedisc.ForeColor = System.Drawing.Color.FromArgb(CType(CType(172, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.txttotalpeforedisc.Location = New System.Drawing.Point(6, 61)
        Me.txttotalpeforedisc.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txttotalpeforedisc.Name = "txttotalpeforedisc"
        Me.txttotalpeforedisc.ReadOnly = True
        Me.txttotalpeforedisc.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txttotalpeforedisc.Size = New System.Drawing.Size(131, 42)
        Me.txttotalpeforedisc.TabIndex = 53
        Me.txttotalpeforedisc.Text = "0"
        Me.txttotalpeforedisc.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'btnDelete_Items
        '
        Me.btnDelete_Items.BackColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(173, Byte), Integer))
        Me.btnDelete_Items.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnDelete_Items.Font = New System.Drawing.Font("JF Flat", 10.8!, System.Drawing.FontStyle.Bold)
        Me.btnDelete_Items.ForeColor = System.Drawing.Color.White
        Me.btnDelete_Items.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btnDelete_Items.Location = New System.Drawing.Point(139, 6)
        Me.btnDelete_Items.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnDelete_Items.Name = "btnDelete_Items"
        Me.btnDelete_Items.Size = New System.Drawing.Size(119, 46)
        Me.btnDelete_Items.TabIndex = 78
        Me.btnDelete_Items.Text = "حذف"
        Me.btnDelete_Items.UseVisualStyleBackColor = False
        '
        'Panel15
        '
        Me.Panel15.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel15.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.Panel15.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel15.Controls.Add(Me.Label12)
        Me.Panel15.Location = New System.Drawing.Point(139, 60)
        Me.Panel15.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel15.Name = "Panel15"
        Me.Panel15.Size = New System.Drawing.Size(119, 44)
        Me.Panel15.TabIndex = 54
        '
        'Label12
        '
        Me.Label12.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label12.AutoSize = True
        Me.Label12.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.Label12.ForeColor = System.Drawing.Color.Black
        Me.Label12.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.Label12.Location = New System.Drawing.Point(16, 9)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(82, 24)
        Me.Label12.TabIndex = 39
        Me.Label12.Text = "قبل الخصم"
        '
        'txtdisc
        '
        Me.txtdisc.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.txtdisc.Font = New System.Drawing.Font("Arial", 18.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtdisc.ForeColor = System.Drawing.Color.FromArgb(CType(CType(172, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.txtdisc.Location = New System.Drawing.Point(6, 112)
        Me.txtdisc.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtdisc.Name = "txtdisc"
        Me.txtdisc.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtdisc.Size = New System.Drawing.Size(131, 42)
        Me.txtdisc.TabIndex = 55
        Me.txtdisc.Text = "0"
        Me.txtdisc.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Panel17
        '
        Me.Panel17.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.Panel17.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel17.Controls.Add(Me.Label2)
        Me.Panel17.Location = New System.Drawing.Point(139, 112)
        Me.Panel17.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel17.Name = "Panel17"
        Me.Panel17.Size = New System.Drawing.Size(119, 43)
        Me.Panel17.TabIndex = 56
        '
        'Label2
        '
        Me.Label2.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.Label2.ForeColor = System.Drawing.Color.Black
        Me.Label2.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.Label2.Location = New System.Drawing.Point(30, 10)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(53, 24)
        Me.Label2.TabIndex = 39
        Me.Label2.Text = "الخصم"
        '
        'txttotalafterdisc
        '
        Me.txttotalafterdisc.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.txttotalafterdisc.Enabled = False
        Me.txttotalafterdisc.Font = New System.Drawing.Font("Arial", 18.0!, System.Drawing.FontStyle.Bold)
        Me.txttotalafterdisc.ForeColor = System.Drawing.Color.FromArgb(CType(CType(172, Byte), Integer), CType(CType(27, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.txttotalafterdisc.Location = New System.Drawing.Point(6, 164)
        Me.txttotalafterdisc.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txttotalafterdisc.Name = "txttotalafterdisc"
        Me.txttotalafterdisc.ReadOnly = True
        Me.txttotalafterdisc.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txttotalafterdisc.Size = New System.Drawing.Size(131, 42)
        Me.txttotalafterdisc.TabIndex = 59
        Me.txttotalafterdisc.Text = "0"
        Me.txttotalafterdisc.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Panel18
        '
        Me.Panel18.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.Panel18.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel18.Controls.Add(Me.Label7)
        Me.Panel18.Location = New System.Drawing.Point(140, 163)
        Me.Panel18.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel18.Name = "Panel18"
        Me.Panel18.Size = New System.Drawing.Size(118, 45)
        Me.Panel18.TabIndex = 60
        '
        'Label7
        '
        Me.Label7.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.Label7.ForeColor = System.Drawing.Color.Black
        Me.Label7.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.Label7.Location = New System.Drawing.Point(24, 10)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(68, 24)
        Me.Label7.TabIndex = 39
        Me.Label7.Text = "الاجمالى"
        '
        'btnPrintChackEnd
        '
        Me.btnPrintChackEnd.BackColor = System.Drawing.Color.FromArgb(CType(CType(14, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(144, Byte), Integer))
        Me.btnPrintChackEnd.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnPrintChackEnd.Font = New System.Drawing.Font("JF Flat", 10.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnPrintChackEnd.ForeColor = System.Drawing.Color.White
        Me.btnPrintChackEnd.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btnPrintChackEnd.Location = New System.Drawing.Point(246, 241)
        Me.btnPrintChackEnd.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnPrintChackEnd.Name = "btnPrintChackEnd"
        Me.btnPrintChackEnd.Size = New System.Drawing.Size(230, 53)
        Me.btnPrintChackEnd.TabIndex = 74
        Me.btnPrintChackEnd.Text = "F5 حفظ وطباعة"
        Me.btnPrintChackEnd.UseVisualStyleBackColor = False
        '
        'Panel10
        '
        Me.Panel10.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel10.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel10.Location = New System.Drawing.Point(0, 0)
        Me.Panel10.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel10.Name = "Panel10"
        Me.Panel10.Size = New System.Drawing.Size(3, 692)
        Me.Panel10.TabIndex = 8
        '
        'Panel8
        '
        Me.Panel8.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel8.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel8.Location = New System.Drawing.Point(0, 692)
        Me.Panel8.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel8.Name = "Panel8"
        Me.Panel8.Size = New System.Drawing.Size(485, 6)
        Me.Panel8.TabIndex = 6
        '
        'Panel9
        '
        Me.Panel9.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel9.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel9.Location = New System.Drawing.Point(485, 0)
        Me.Panel9.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel9.Name = "Panel9"
        Me.Panel9.Size = New System.Drawing.Size(6, 698)
        Me.Panel9.TabIndex = 3
        '
        'Panel7
        '
        Me.Panel7.BackColor = System.Drawing.Color.FromArgb(CType(CType(233, Byte), Integer), CType(CType(200, Byte), Integer), CType(CType(115, Byte), Integer))
        Me.Panel7.Controls.Add(Me.FlowLay_Items)
        Me.Panel7.Controls.Add(Me.Panel12)
        Me.Panel7.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel7.Location = New System.Drawing.Point(297, 77)
        Me.Panel7.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel7.Name = "Panel7"
        Me.Panel7.Size = New System.Drawing.Size(420, 698)
        Me.Panel7.TabIndex = 309
        '
        'FlowLay_Items
        '
        Me.FlowLay_Items.AutoScroll = True
        Me.FlowLay_Items.BackColor = System.Drawing.Color.FromArgb(CType(CType(246, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.FlowLay_Items.Dock = System.Windows.Forms.DockStyle.Fill
        Me.FlowLay_Items.Location = New System.Drawing.Point(0, 0)
        Me.FlowLay_Items.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.FlowLay_Items.Name = "FlowLay_Items"
        Me.FlowLay_Items.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.FlowLay_Items.Size = New System.Drawing.Size(420, 692)
        Me.FlowLay_Items.TabIndex = 309
        '
        'Panel12
        '
        Me.Panel12.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel12.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel12.Location = New System.Drawing.Point(0, 692)
        Me.Panel12.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel12.Name = "Panel12"
        Me.Panel12.Size = New System.Drawing.Size(420, 6)
        Me.Panel12.TabIndex = 6
        '
        'Timer1
        '
        Me.Timer1.Enabled = True
        '
        'Panel2
        '
        Me.Panel2.BackColor = System.Drawing.Color.FromArgb(CType(CType(8, Byte), Integer), CType(CType(62, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel2.BackgroundImage = CType(resources.GetObject("Panel2.BackgroundImage"), System.Drawing.Image)
        Me.Panel2.Controls.Add(Me.lblSheft_Number)
        Me.Panel2.Controls.Add(Me.Label52)
        Me.Panel2.Controls.Add(Me.btnSelectPrinter)
        Me.Panel2.Controls.Add(Me.txtCountItems)
        Me.Panel2.Controls.Add(Me.lblPermtionName)
        Me.Panel2.Controls.Add(Me.lblEmpName)
        Me.Panel2.Controls.Add(Me.lblTypeOrders)
        Me.Panel2.Controls.Add(Me.lblOrderNO)
        Me.Panel2.Controls.Add(Me.Label18)
        Me.Panel2.Controls.Add(Me.lblAutobillnoDay)
        Me.Panel2.Controls.Add(Me.Label16)
        Me.Panel2.Controls.Add(Me.lblUserName)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel2.Location = New System.Drawing.Point(0, 775)
        Me.Panel2.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(1208, 38)
        Me.Panel2.TabIndex = 9
        '
        'lblSheft_Number
        '
        Me.lblSheft_Number.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblSheft_Number.AutoSize = True
        Me.lblSheft_Number.BackColor = System.Drawing.Color.Transparent
        Me.lblSheft_Number.Font = New System.Drawing.Font("JF Flat", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblSheft_Number.ForeColor = System.Drawing.Color.White
        Me.lblSheft_Number.Location = New System.Drawing.Point(810, 1)
        Me.lblSheft_Number.Name = "lblSheft_Number"
        Me.lblSheft_Number.Size = New System.Drawing.Size(27, 34)
        Me.lblSheft_Number.TabIndex = 379
        Me.lblSheft_Number.Text = "0"
        '
        'Label52
        '
        Me.Label52.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label52.AutoSize = True
        Me.Label52.BackColor = System.Drawing.Color.Transparent
        Me.Label52.Font = New System.Drawing.Font("JF Flat", 12.0!, System.Drawing.FontStyle.Bold)
        Me.Label52.ForeColor = System.Drawing.Color.White
        Me.Label52.Location = New System.Drawing.Point(684, 1)
        Me.Label52.Name = "Label52"
        Me.Label52.Size = New System.Drawing.Size(116, 34)
        Me.Label52.TabIndex = 378
        Me.Label52.Text = "Sheft NO |"
        '
        'btnSelectPrinter
        '
        Me.btnSelectPrinter.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSelectPrinter.BackColor = System.Drawing.Color.Transparent
        Me.btnSelectPrinter.FlatAppearance.BorderSize = 0
        Me.btnSelectPrinter.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnSelectPrinter.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.btnSelectPrinter.ForeColor = System.Drawing.Color.White
        Me.btnSelectPrinter.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btnSelectPrinter.Location = New System.Drawing.Point(417, 1)
        Me.btnSelectPrinter.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnSelectPrinter.Name = "btnSelectPrinter"
        Me.btnSelectPrinter.Size = New System.Drawing.Size(75, 37)
        Me.btnSelectPrinter.TabIndex = 377
        Me.btnSelectPrinter.Text = "الطابعة"
        Me.btnSelectPrinter.UseVisualStyleBackColor = False
        '
        'txtCountItems
        '
        Me.txtCountItems.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtCountItems.AutoSize = True
        Me.txtCountItems.BackColor = System.Drawing.Color.Transparent
        Me.txtCountItems.Font = New System.Drawing.Font("JF Flat", 12.0!, System.Drawing.FontStyle.Bold)
        Me.txtCountItems.ForeColor = System.Drawing.Color.White
        Me.txtCountItems.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.txtCountItems.Location = New System.Drawing.Point(199, 1)
        Me.txtCountItems.Name = "txtCountItems"
        Me.txtCountItems.Size = New System.Drawing.Size(27, 34)
        Me.txtCountItems.TabIndex = 376
        Me.txtCountItems.Text = "0"
        Me.txtCountItems.Visible = False
        '
        'lblPermtionName
        '
        Me.lblPermtionName.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblPermtionName.AutoSize = True
        Me.lblPermtionName.Font = New System.Drawing.Font("JF Flat", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblPermtionName.ForeColor = System.Drawing.Color.White
        Me.lblPermtionName.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.lblPermtionName.Location = New System.Drawing.Point(-63, 1)
        Me.lblPermtionName.Name = "lblPermtionName"
        Me.lblPermtionName.Size = New System.Drawing.Size(27, 34)
        Me.lblPermtionName.TabIndex = 375
        Me.lblPermtionName.Text = "0"
        Me.lblPermtionName.Visible = False
        '
        'lblEmpName
        '
        Me.lblEmpName.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblEmpName.AutoSize = True
        Me.lblEmpName.Font = New System.Drawing.Font("JF Flat", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblEmpName.ForeColor = System.Drawing.Color.White
        Me.lblEmpName.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.lblEmpName.Location = New System.Drawing.Point(-96, 1)
        Me.lblEmpName.Name = "lblEmpName"
        Me.lblEmpName.Size = New System.Drawing.Size(27, 34)
        Me.lblEmpName.TabIndex = 372
        Me.lblEmpName.Text = "0"
        Me.lblEmpName.Visible = False
        '
        'lblTypeOrders
        '
        Me.lblTypeOrders.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblTypeOrders.AutoSize = True
        Me.lblTypeOrders.Font = New System.Drawing.Font("JF Flat", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblTypeOrders.ForeColor = System.Drawing.Color.White
        Me.lblTypeOrders.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.lblTypeOrders.Location = New System.Drawing.Point(-129, 1)
        Me.lblTypeOrders.Name = "lblTypeOrders"
        Me.lblTypeOrders.Size = New System.Drawing.Size(27, 34)
        Me.lblTypeOrders.TabIndex = 371
        Me.lblTypeOrders.Text = "0"
        Me.lblTypeOrders.Visible = False
        '
        'lblOrderNO
        '
        Me.lblOrderNO.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblOrderNO.AutoSize = True
        Me.lblOrderNO.BackColor = System.Drawing.Color.Transparent
        Me.lblOrderNO.Font = New System.Drawing.Font("JF Flat", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblOrderNO.ForeColor = System.Drawing.Color.White
        Me.lblOrderNO.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.lblOrderNO.Location = New System.Drawing.Point(1009, 1)
        Me.lblOrderNO.Name = "lblOrderNO"
        Me.lblOrderNO.Size = New System.Drawing.Size(27, 34)
        Me.lblOrderNO.TabIndex = 370
        Me.lblOrderNO.Text = "0"
        '
        'Label18
        '
        Me.Label18.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label18.AutoSize = True
        Me.Label18.BackColor = System.Drawing.Color.Transparent
        Me.Label18.Font = New System.Drawing.Font("JF Flat", 12.0!, System.Drawing.FontStyle.Bold)
        Me.Label18.ForeColor = System.Drawing.Color.White
        Me.Label18.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.Label18.Location = New System.Drawing.Point(890, 1)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(119, 34)
        Me.Label18.TabIndex = 369
        Me.Label18.Text = "Order NO. |"
        '
        'lblAutobillnoDay
        '
        Me.lblAutobillnoDay.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblAutobillnoDay.AutoSize = True
        Me.lblAutobillnoDay.BackColor = System.Drawing.Color.Transparent
        Me.lblAutobillnoDay.Font = New System.Drawing.Font("JF Flat", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblAutobillnoDay.ForeColor = System.Drawing.Color.White
        Me.lblAutobillnoDay.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.lblAutobillnoDay.Location = New System.Drawing.Point(1173, 1)
        Me.lblAutobillnoDay.Name = "lblAutobillnoDay"
        Me.lblAutobillnoDay.Size = New System.Drawing.Size(27, 34)
        Me.lblAutobillnoDay.TabIndex = 6
        Me.lblAutobillnoDay.Text = "0"
        '
        'Label16
        '
        Me.Label16.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label16.AutoSize = True
        Me.Label16.BackColor = System.Drawing.Color.Transparent
        Me.Label16.Font = New System.Drawing.Font("JF Flat", 12.0!, System.Drawing.FontStyle.Bold)
        Me.Label16.ForeColor = System.Drawing.Color.White
        Me.Label16.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.Label16.Location = New System.Drawing.Point(1075, 1)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(99, 34)
        Me.Label16.TabIndex = 5
        Me.Label16.Text = ".NO Day |"
        '
        'lblUserName
        '
        Me.lblUserName.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblUserName.AutoSize = True
        Me.lblUserName.BackColor = System.Drawing.Color.Transparent
        Me.lblUserName.Font = New System.Drawing.Font("JF Flat", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblUserName.ForeColor = System.Drawing.Color.White
        Me.lblUserName.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.lblUserName.Location = New System.Drawing.Point(12, 1)
        Me.lblUserName.Name = "lblUserName"
        Me.lblUserName.Size = New System.Drawing.Size(47, 34)
        Me.lblUserName.TabIndex = 2
        Me.lblUserName.Text = "FIT"
        '
        'Panel1
        '
        Me.Panel1.BackColor = System.Drawing.Color.FromArgb(CType(CType(8, Byte), Integer), CType(CType(62, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel1.BackgroundImage = CType(resources.GetObject("Panel1.BackgroundImage"), System.Drawing.Image)
        Me.Panel1.Controls.Add(Me.Button8)
        Me.Panel1.Controls.Add(Me.Button7)
        Me.Panel1.Controls.Add(Me.Button6)
        Me.Panel1.Controls.Add(Me.Button5)
        Me.Panel1.Controls.Add(Me.Button4)
        Me.Panel1.Controls.Add(Me.Button3)
        Me.Panel1.Controls.Add(Me.Button2)
        Me.Panel1.Controls.Add(Me.Button1)
        Me.Panel1.Controls.Add(Me.Panel4)
        Me.Panel1.Controls.Add(Me.Panel30)
        Me.Panel1.Controls.Add(Me.btnMinimized)
        Me.Panel1.Controls.Add(Me.btnClose)
        Me.Panel1.Controls.Add(Me.Panel3)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1208, 77)
        Me.Panel1.TabIndex = 8
        '
        'Button8
        '
        Me.Button8.BackColor = System.Drawing.Color.Transparent
        Me.Button8.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.Button8.Dock = System.Windows.Forms.DockStyle.Left
        Me.Button8.Enabled = False
        Me.Button8.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button8.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.Button8.Location = New System.Drawing.Point(890, 0)
        Me.Button8.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button8.Name = "Button8"
        Me.Button8.Size = New System.Drawing.Size(89, 71)
        Me.Button8.TabIndex = 220
        Me.Button8.Text = "مرتجعات مبيعات"
        Me.Button8.UseVisualStyleBackColor = False
        '
        'Button7
        '
        Me.Button7.BackColor = System.Drawing.Color.Transparent
        Me.Button7.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.Button7.Dock = System.Windows.Forms.DockStyle.Left
        Me.Button7.Enabled = False
        Me.Button7.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button7.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.Button7.Location = New System.Drawing.Point(801, 0)
        Me.Button7.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button7.Name = "Button7"
        Me.Button7.Size = New System.Drawing.Size(89, 71)
        Me.Button7.TabIndex = 219
        Me.Button7.Text = "تقرير اليومية"
        Me.Button7.UseVisualStyleBackColor = False
        '
        'Button6
        '
        Me.Button6.BackColor = System.Drawing.Color.Transparent
        Me.Button6.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.Button6.Dock = System.Windows.Forms.DockStyle.Left
        Me.Button6.Enabled = False
        Me.Button6.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button6.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.Button6.Location = New System.Drawing.Point(712, 0)
        Me.Button6.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button6.Name = "Button6"
        Me.Button6.Size = New System.Drawing.Size(89, 71)
        Me.Button6.TabIndex = 218
        Me.Button6.Text = "الخزينة"
        Me.Button6.UseVisualStyleBackColor = False
        '
        'Button5
        '
        Me.Button5.BackColor = System.Drawing.Color.Transparent
        Me.Button5.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.Button5.Dock = System.Windows.Forms.DockStyle.Left
        Me.Button5.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button5.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.Button5.Location = New System.Drawing.Point(623, 0)
        Me.Button5.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button5.Name = "Button5"
        Me.Button5.Size = New System.Drawing.Size(89, 71)
        Me.Button5.TabIndex = 217
        Me.Button5.Text = "إنهاء شيفت"
        Me.Button5.UseVisualStyleBackColor = False
        '
        'Button4
        '
        Me.Button4.BackColor = System.Drawing.Color.Transparent
        Me.Button4.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.Button4.Dock = System.Windows.Forms.DockStyle.Left
        Me.Button4.Enabled = False
        Me.Button4.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button4.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.Button4.Location = New System.Drawing.Point(526, 0)
        Me.Button4.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button4.Name = "Button4"
        Me.Button4.Size = New System.Drawing.Size(97, 71)
        Me.Button4.TabIndex = 216
        Me.Button4.Text = "مدفوعات موردين"
        Me.Button4.UseVisualStyleBackColor = False
        '
        'Button3
        '
        Me.Button3.BackColor = System.Drawing.Color.Transparent
        Me.Button3.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.Button3.Dock = System.Windows.Forms.DockStyle.Left
        Me.Button3.Enabled = False
        Me.Button3.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button3.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.Button3.Location = New System.Drawing.Point(437, 0)
        Me.Button3.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button3.Name = "Button3"
        Me.Button3.Size = New System.Drawing.Size(89, 71)
        Me.Button3.TabIndex = 215
        Me.Button3.Text = "مشتريات"
        Me.Button3.UseVisualStyleBackColor = False
        '
        'Button2
        '
        Me.Button2.BackColor = System.Drawing.Color.Transparent
        Me.Button2.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.Button2.Dock = System.Windows.Forms.DockStyle.Left
        Me.Button2.Enabled = False
        Me.Button2.Font = New System.Drawing.Font("JF Flat", 10.2!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button2.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.Button2.Location = New System.Drawing.Point(367, 0)
        Me.Button2.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button2.Name = "Button2"
        Me.Button2.Size = New System.Drawing.Size(70, 71)
        Me.Button2.TabIndex = 214
        Me.Button2.Text = "تعديل صنف"
        Me.Button2.UseVisualStyleBackColor = False
        '
        'Button1
        '
        Me.Button1.BackColor = System.Drawing.Color.Transparent
        Me.Button1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.Button1.Dock = System.Windows.Forms.DockStyle.Left
        Me.Button1.Enabled = False
        Me.Button1.Font = New System.Drawing.Font("JF Flat", 10.2!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button1.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.Button1.Location = New System.Drawing.Point(297, 0)
        Me.Button1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(70, 71)
        Me.Button1.TabIndex = 213
        Me.Button1.Text = "صنف" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "جديد"
        Me.Button1.UseVisualStyleBackColor = False
        '
        'Panel4
        '
        Me.Panel4.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel4.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel4.Location = New System.Drawing.Point(291, 0)
        Me.Panel4.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel4.Name = "Panel4"
        Me.Panel4.Size = New System.Drawing.Size(6, 71)
        Me.Panel4.TabIndex = 212
        '
        'Panel30
        '
        Me.Panel30.BackColor = System.Drawing.Color.Transparent
        Me.Panel30.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.Panel30.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel30.Location = New System.Drawing.Point(0, 0)
        Me.Panel30.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel30.Name = "Panel30"
        Me.Panel30.Size = New System.Drawing.Size(291, 71)
        Me.Panel30.TabIndex = 211
        Me.Panel30.Visible = False
        '
        'btnMinimized
        '
        Me.btnMinimized.BackColor = System.Drawing.Color.Transparent
        Me.btnMinimized.BackgroundImage = CType(resources.GetObject("btnMinimized.BackgroundImage"), System.Drawing.Image)
        Me.btnMinimized.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnMinimized.Dock = System.Windows.Forms.DockStyle.Right
        Me.btnMinimized.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btnMinimized.Location = New System.Drawing.Point(1068, 0)
        Me.btnMinimized.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnMinimized.Name = "btnMinimized"
        Me.btnMinimized.Size = New System.Drawing.Size(70, 71)
        Me.btnMinimized.TabIndex = 210
        Me.btnMinimized.UseVisualStyleBackColor = False
        '
        'btnClose
        '
        Me.btnClose.BackColor = System.Drawing.Color.Transparent
        Me.btnClose.BackgroundImage = CType(resources.GetObject("btnClose.BackgroundImage"), System.Drawing.Image)
        Me.btnClose.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.btnClose.Dock = System.Windows.Forms.DockStyle.Right
        Me.btnClose.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btnClose.Location = New System.Drawing.Point(1138, 0)
        Me.btnClose.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnClose.Name = "btnClose"
        Me.btnClose.Size = New System.Drawing.Size(70, 71)
        Me.btnClose.TabIndex = 209
        Me.btnClose.UseVisualStyleBackColor = False
        '
        'Panel3
        '
        Me.Panel3.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel3.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel3.Location = New System.Drawing.Point(0, 71)
        Me.Panel3.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(1208, 6)
        Me.Panel3.TabIndex = 6
        '
        'FrmSalesTouch
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1208, 813)
        Me.Controls.Add(Me.Panel7)
        Me.Controls.Add(Me.Panel6)
        Me.Controls.Add(Me.Panel5)
        Me.Controls.Add(Me.Panel2)
        Me.Controls.Add(Me.Panel1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None
        Me.Name = "FrmSalesTouch"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "FrmSalesTouch"
        Me.WindowState = System.Windows.Forms.FormWindowState.Maximized
        Me.Panel5.ResumeLayout(False)
        Me.Panel6.ResumeLayout(False)
        Me.PanelPayingStaying.ResumeLayout(False)
        Me.PanelPayingStaying.PerformLayout()
        Me.Panel25.ResumeLayout(False)
        Me.Panel25.PerformLayout()
        Me.PanelSelectPrinter.ResumeLayout(False)
        Me.PanelSelectPrinter.PerformLayout()
        Me.Panel48.ResumeLayout(False)
        Me.Panel48.PerformLayout()
        CType(Me.Dgv_Add, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel22.ResumeLayout(False)
        Me.Panel22.PerformLayout()
        CType(Me.picQRCode, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel11.ResumeLayout(False)
        Me.FlowLayoutPanel3.ResumeLayout(False)
        Me.Panel13.ResumeLayout(False)
        Me.Panel13.PerformLayout()
        Me.Panel15.ResumeLayout(False)
        Me.Panel15.PerformLayout()
        Me.Panel17.ResumeLayout(False)
        Me.Panel17.PerformLayout()
        Me.Panel18.ResumeLayout(False)
        Me.Panel18.PerformLayout()
        Me.Panel7.ResumeLayout(False)
        Me.Panel2.ResumeLayout(False)
        Me.Panel2.PerformLayout()
        Me.Panel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel1 As Panel
    Friend WithEvents Panel3 As Panel
    Friend WithEvents Panel2 As Panel
    Friend WithEvents lblPermtionName As Label
    Friend WithEvents lblEmpName As Label
    Friend WithEvents lblTypeOrders As Label
    Friend WithEvents lblOrderNO As Label
    Friend WithEvents Label18 As Label
    Friend WithEvents lblAutobillnoDay As Label
    Friend WithEvents Label16 As Label
    Friend WithEvents lblUserName As Label
    Friend WithEvents Panel5 As Panel
    Friend WithEvents Panel52 As Panel
    Friend WithEvents Panel6 As Panel
    Friend WithEvents Panel8 As Panel
    Friend WithEvents Panel9 As Panel
    Friend WithEvents FlowLay_Group As FlowLayoutPanel
    Friend WithEvents Panel7 As Panel
    Friend WithEvents FlowLay_Items As FlowLayoutPanel
    Friend WithEvents Panel12 As Panel
    Friend WithEvents Dgv_Add As DataGridView
    Friend WithEvents Panel11 As Panel
    Friend WithEvents btnPrintChackEnd As Button
    Friend WithEvents Panel22 As Panel
    Friend WithEvents cmbname As ComboBox
    Friend WithEvents txtprc As TextBox
    Friend WithEvents cmbvendores As ComboBox
    Friend WithEvents txtTimeAMPM As TextBox
    Friend WithEvents DateTimePicker1 As DateTimePicker
    Friend WithEvents TxtDay As TextBox
    Friend WithEvents btnDelete_Items As Button
    Friend WithEvents ToolTip1 As ToolTip
    Friend WithEvents Panel13 As Panel
    Friend WithEvents txttotalpeforedisc As TextBox
    Friend WithEvents Panel15 As Panel
    Friend WithEvents Label12 As Label
    Friend WithEvents txtdisc As TextBox
    Friend WithEvents Panel17 As Panel
    Friend WithEvents Label2 As Label
    Friend WithEvents txttotalafterdisc As TextBox
    Friend WithEvents Panel18 As Panel
    Friend WithEvents Label7 As Label
    Friend WithEvents FlowLayoutPanel3 As FlowLayoutPanel
    Friend WithEvents btnClearTextbox As Button
    Friend WithEvents btn9 As Button
    Friend WithEvents btn8 As Button
    Friend WithEvents btn7 As Button
    Friend WithEvents btn6 As Button
    Friend WithEvents btn5 As Button
    Friend WithEvents btn4 As Button
    Friend WithEvents btn3 As Button
    Friend WithEvents btn2 As Button
    Friend WithEvents btn1 As Button
    Friend WithEvents btn0 As Button
    Friend WithEvents btnDecimal As Button
    Friend WithEvents Panel14 As Panel
    Friend WithEvents btnMinimized As Button
    Friend WithEvents btnClose As Button
    Friend WithEvents Panel30 As Panel
    Friend WithEvents Panel4 As Panel
    Friend WithEvents txtCountItems As Label
    Friend WithEvents cmbTreasuryName As ComboBox
    Friend WithEvents lblTreasuryName As Label
    Friend WithEvents Timer1 As Timer
    Friend WithEvents TxtHour As TextBox
    Friend WithEvents cmbStores As ComboBox
    Friend WithEvents Label8 As Label
    Friend WithEvents PanelSelectPrinter As Panel
    Friend WithEvents Panel45 As Panel
    Friend WithEvents Panel46 As Panel
    Friend WithEvents Panel47 As Panel
    Friend WithEvents Panel48 As Panel
    Friend WithEvents Label10 As Label
    Friend WithEvents btnCloseSelectPrinter As Button
    Friend WithEvents chkprint As CheckBox
    Friend WithEvents rdoPrintPreview As RadioButton
    Friend WithEvents rdoPrintDirect As RadioButton
    Friend WithEvents btnSelectPrinter As Button
    Private WithEvents picQRCode As PictureBox
    Friend WithEvents txtSheft_Number As TextBox
    Friend WithEvents cmbEmployees As ComboBox
    Friend WithEvents Panel10 As Panel
    Friend WithEvents Panel16 As Panel
    Friend WithEvents Panel19 As Panel
    Friend WithEvents lblSheft_Number As Label
    Friend WithEvents Label52 As Label
    Friend WithEvents btnChackEnd As Button
    Friend WithEvents chkActivatePaidAndRest As CheckBox
    Friend WithEvents PanelPayingStaying As Panel
    Friend WithEvents Panel21 As Panel
    Friend WithEvents Panel23 As Panel
    Friend WithEvents Panel24 As Panel
    Friend WithEvents Panel25 As Panel
    Friend WithEvents Label3 As Label
    Friend WithEvents btnClosePanelPayingStaying As Button
    Friend WithEvents btnOKPayingStaying As Button
    Friend WithEvents txtpayingWindow As TextBox
    Friend WithEvents txtstayingWindow As TextBox
    Friend WithEvents Label29 As Label
    Friend WithEvents Label31 As Label
    Friend WithEvents Label1 As Label
    Friend WithEvents txtTreasuryName As TextBox
    Friend WithEvents Button3 As Button
    Friend WithEvents Button2 As Button
    Friend WithEvents Button1 As Button
    Friend WithEvents Button4 As Button
    Friend WithEvents Button5 As Button
    Friend WithEvents Button6 As Button
    Friend WithEvents Button7 As Button
    Friend WithEvents Button8 As Button
    Friend WithEvents Panel20 As Panel
End Class
