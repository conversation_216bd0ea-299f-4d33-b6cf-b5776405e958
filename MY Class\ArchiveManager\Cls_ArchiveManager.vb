﻿Imports Microsoft.SqlServer.Management.Smo
Imports Microsoft.SqlServer.Management.Common
Imports System.Data.SqlClient

Public Class Cls_ArchiveManager

    Public Sub ArchiveAndDeleteCustomer(ByVal TransactionType As String, ByVal Vendorname As String, ByVal DeletedBy As String, ByVal DeleteReason As String)
        Try

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "INSERT INTO Arch_Customers (
    TransactionType,
    OriginalID,
    DeletedDate,
    DeletedBy,
    DeleteReason,
    Cust_Code,
    Vendorname,
    addr,
    tel1,
    notes,
    VstDiscOther,
    BVstPay,
    BVstDiscount,
    valuereturns,
    vintinval,
    vndiscount,
    VnPay,
    VnReceipts,
    vnamntcredit,
    vnamntdebit,
    addvintinval,
    ValueVAT,
    Limit_DrawDowns_Price,
    UserName,
    GeoArea_Code,
    Emp_Code,
    Mobile,
    Apartment,
    Role,
    Region,
    Mark,
    PriceType_ID,
    Company_Branch_ID,
    Total_Qunt,
    taxn,
    tradn,
    Bran_code
) 
SELECT 
    N'" & TransactionType & "' as TransactionType,
    Cust_Code as OriginalID,
    GETDATE() as DeletedDate,
    N'" & UserName & "' as DeletedBy,
    N'تم حذف العميل لعدم النشاط' as DeleteReason,
    Cust_Code,
    Vendorname,
    addr,
    tel1,
    notes,
    VstDiscOther,
    BVstPay,
    BVstDiscount,
    valuereturns,
    vintinval,
    vndiscount,
    VnPay,
    VnReceipts,
    vnamntcredit,
    vnamntdebit,
    addvintinval,
    ValueVAT,
    Limit_DrawDowns_Price,
    UserName,
    GeoArea_Code,
    Emp_Code,
    Mobile,
    Apartment,
    Role,
    Region,
    Mark,
    PriceType_ID,
    Company_Branch_ID,
    Total_Qunt,
    taxn,
    tradn,
    Bran_code
FROM Customers 
WHERE Vendorname = N'" & Vendorname & "'"

            cmd.ExecuteNonQuery()

        Catch ex As SqlException
            Console.WriteLine("حدث خطأ في قاعدة البيانات: " & ex.Message)
        Catch ex As Exception
            Console.WriteLine("حدث خطأ عام: " & ex.Message)
        End Try
    End Sub

    Public Sub ArchiveAndDeleteVst(ByVal TransactionType As String, ByVal Vst_ID As String, ByVal Vendorname As String, ByVal DeletedBy As String, ByVal DeleteReason As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "INSERT INTO Arch_Vst (
    TransactionType,
    OriginalID,
    DeletedDate,
    DeletedBy,
    DeleteReason,
    Vst_ID,
    Vendorname,
    VND_XTM,
    VND_dt,
    VND_Date_Maturity,
    VND_amx,
    VND_ho,
    VND_rcv,
    VND_dec,
    VND_no,
    BillNo,
    UserName,
    Check_Type,
    PaymentTotal,
    BillNo_Check,
    EmpName,
    OrderID,
    Company_Branch_ID,
    Treasury_Code,
    CashBank,
    CreditPrevious,
    DebitPrevious,
    CreditCurrent,
    DebitCurrent,
    CurrentBalanceCustVnd
) 
SELECT 
    N'" & TransactionType & "' as TransactionType,
    Vst_ID as OriginalID,
    GETDATE() as DeletedDate,
    N'" & DeletedBy & "' as DeletedBy,
    N'" & DeleteReason & "' as DeleteReason,
    Vst_ID,
    Vendorname,
    VND_XTM,
    VND_dt,
    VND_Date_Maturity,
    VND_amx,
    VND_ho,
    VND_rcv,
    VND_dec,
    VND_no,
    BillNo,
    UserName,
    Check_Type,
    PaymentTotal,
    BillNo_Check,
    EmpName,
    OrderID,
    Company_Branch_ID,
    Treasury_Code,
    CashBank,
    CreditPrevious,
    DebitPrevious,
    CreditCurrent,
    DebitCurrent,
    CurrentBalanceCustVnd
FROM Vst 
WHERE BillNo = N'" & Vst_ID & "' and Vendorname = N'" & Vendorname & "'"
            cmd.ExecuteNonQuery()
        Catch ex As SqlException
            Console.WriteLine("حدث خطأ في قاعدة البيانات: " & ex.Message)
        Catch ex As Exception
            Console.WriteLine("حدث خطأ عام: " & ex.Message)
        End Try
    End Sub

    Public Sub ArchiveAndDeleteVnd(ByVal TransactionType As String, ByVal VND_no As String, ByVal Vendorname As String, ByVal DeletedBy As String, ByVal DeleteReason As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "INSERT INTO Arch_Vnd (
    TransactionType,
    OriginalID,
    DeletedDate,
    DeletedBy,
    DeleteReason,
    Vendorname,
    VND_XTM,
    VND_dt,
    VND_amx,
    VND_ho,
    VND_rcv,
    VND_dec,
    VND_no,
    BillNo,
    Sales_Bill_NO,
    UserName,
    Company_Branch_ID,
    Treasury_Code,
    CreditPrevious,
    DebitPrevious,
    CreditCurrent,
    DebitCurrent,
    VND_Date_Maturity,
    Check_Number,
    Check_Type,
    BillNo_Check,
    CurrentBalanceCustVnd
) 
SELECT 
    N'" & TransactionType & "' as TransactionType,
    VND_no as OriginalID,
    GETDATE() as DeletedDate,
    N'" & DeletedBy & "' as DeletedBy,
    N'" & DeleteReason & "' as DeleteReason,
    Vendorname,
    VND_XTM,
    VND_dt,
    VND_amx,
    VND_ho,
    VND_rcv,
    VND_dec,
    VND_no,
    BillNo,
    Sales_Bill_NO,
    UserName,
    Company_Branch_ID,
    Treasury_Code,
    CreditPrevious,
    DebitPrevious,
    CreditCurrent,
    DebitCurrent,
    VND_Date_Maturity,
    Check_Number,
    Check_Type,
    BillNo_Check,
    CurrentBalanceCustVnd
FROM Vnd 
WHERE BillNo = N'" & VND_no & "' and Vendorname = N'" & Vendorname & "'"
            cmd.ExecuteNonQuery()
        Catch ex As SqlException
            Console.WriteLine("حدث خطأ في قاعدة البيانات: " & ex.Message)
        Catch ex As Exception
            Console.WriteLine("حدث خطأ عام: " & ex.Message)
        End Try
    End Sub

    Public Sub ArchiveAndDeleteSalesBill(ByVal TransactionType As String, ByVal bill_No As String, ByVal Vendorname As String, ByVal DeletedBy As String, ByVal DeleteReason As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "INSERT INTO Arch_Sales_Bill (
    TransactionType,
    OriginalID,
    DeletedDate,
    DeletedBy,
    DeleteReason,
    bill_No,
    OrderID,
    Sheft_Number,
    Vendorname,
    bill_date,
    billtime,
    totalpricebeforedisc,
    disc,
    totalpriceafterdisc,
    Stat,
    STAYING,
    BEY,
    SalesTax,
    Notes,
    UserName,
    Image_Bill,
    EmpName,
    Driv_Name,
    Driv_CarNumber,
    KiloMeter,
    Supervisor_Reform,
    Recipient,
    Received_Date,
    Delivery_Date,
    DiscountTax,
    bill_NoTax,
    DeliveryService,
    ExpensesBill,
    RateValues,
    CreditPrevious,
    DebitPrevious,
    CreditCurrent,
    DebitCurrent,
    RateBankExpensesVisa,
    ValueBankExpensesVisa,
    StatusOrder,
    Company_Branch_ID,
    disc_type,
    ValueVAT,
    Treasury_Code,
    DiscountsValue,
    RateDriverDelivery,
    CloseSheft,
    BillTimeAmBm,
    QRCodeBill,
    CommercialIndustrialProfitsTax,
    PendingBill,
    CurrentBalanceCustVnd,
    PriceBeforeDiscValue,
    AutoSeriesVATActive
) 
SELECT 
    N'" & TransactionType & "' as TransactionType,
    bill_No as OriginalID,
    GETDATE() as DeletedDate,
    N'" & DeletedBy & "' as DeletedBy,
    N'" & DeleteReason & "' as DeleteReason,
    bill_No,
    OrderID,
    Sheft_Number,
    Vendorname,
    bill_date,
    billtime,
    totalpricebeforedisc,
    disc,
    totalpriceafterdisc,
    Stat,
    STAYING,
    BEY,
    SalesTax,
    Notes,
    UserName,
    Image_Bill,
    EmpName,
    Driv_Name,
    Driv_CarNumber,
    KiloMeter,
    Supervisor_Reform,
    Recipient,
    Received_Date,
    Delivery_Date,
    DiscountTax,
    bill_NoTax,
    DeliveryService,
    ExpensesBill,
    RateValues,
    CreditPrevious,
    DebitPrevious,
    CreditCurrent,
    DebitCurrent,
    RateBankExpensesVisa,
    ValueBankExpensesVisa,
    StatusOrder,
    Company_Branch_ID,
    disc_type,
    ValueVAT,
    Treasury_Code,
    DiscountsValue,
    RateDriverDelivery,
    CloseSheft,
    BillTimeAmBm,
    QRCodeBill,
    CommercialIndustrialProfitsTax,
    PendingBill,
    CurrentBalanceCustVnd,
    PriceBeforeDiscValue,
    AutoSeriesVATActive
FROM Sales_Bill 
WHERE bill_No = N'" & bill_No & "' and Vendorname = N'" & Vendorname & "'"
            cmd.ExecuteNonQuery()
        Catch ex As SqlException
        Console.WriteLine("حدث خطأ في قاعدة البيانات: " & ex.Message)
        Catch ex As Exception
        Console.WriteLine("حدث خطأ عام: " & ex.Message)
        End Try
    End Sub

    Public Sub ArchiveAndDeletePurchaseBill(ByVal TransactionType As String, ByVal bill_No As String, ByVal Vendorname As String, ByVal DeletedBy As String, ByVal DeleteReason As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "INSERT INTO Arch_purchase_bill (
   TransactionType,
   OriginalID,
   DeletedDate,
   DeletedBy,
   DeleteReason,
   bill_No,
   Vendorname,
   bill_date,
   billtime,
   totalpricebeforedisc,
   disc,
   totalpriceafterdisc,
   Stat,
   BEY,
   STAYING,
   PurchaseTax,
   PurchaseTaxValue,
   UserName,
   Status,
   Image_Bill,
   Currency_Name,
   Sales_Bill_NO,
   RateValues,
   Company_Branch_ID,
   ValueVAT,
   Treasury_Code,
   DiscountsValue,
   CreditPrevious,
   DebitPrevious,
   CreditCurrent,
   DebitCurrent,
   DeferredCurrentDiscount,
   CurrentBalanceCustVnd,
   Notes
) 
SELECT 
   N'" & TransactionType & "' as TransactionType,
   bill_No as OriginalID,
   GETDATE() as DeletedDate,
   N'" & DeletedBy & "' as DeletedBy,
   N'" & DeleteReason & "' as DeleteReason,
   bill_No,
   Vendorname,
   bill_date,
   billtime,
   totalpricebeforedisc,
   disc,
   totalpriceafterdisc,
   Stat,
   BEY,
   STAYING,
   PurchaseTax,
   PurchaseTaxValue,
   UserName,
   Status,
   Image_Bill,
   Currency_Name,
   Sales_Bill_NO,
   RateValues,
   Company_Branch_ID,
   ValueVAT,
   Treasury_Code,
   DiscountsValue,
   CreditPrevious,
   DebitPrevious,
   CreditCurrent,
   DebitCurrent,
   DeferredCurrentDiscount,
   CurrentBalanceCustVnd,
   Notes
FROM purchase_bill 
WHERE bill_No = N'" & bill_No & "' and Vendorname = N'" & Vendorname & "'"
            cmd.ExecuteNonQuery()
        Catch ex As SqlException
        Console.WriteLine("حدث خطأ في قاعدة البيانات: " & ex.Message)
        Catch ex As Exception
        Console.WriteLine("حدث خطأ عام: " & ex.Message)
        End Try
    End Sub

    Public Sub ArchiveAndDeleteItems(ByVal TransactionType As String, ByVal itm_id As String, ByVal Stores As String, ByVal DeletedBy As String, ByVal DeleteReason As String)
        'Try
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "INSERT INTO Arch_Items (
    TransactionType, OriginalID, DeletedDate, DeletedBy, DeleteReason,
    IDTM, itm_id, group_name, group_branch, sname, Unity, Pack, rng,
    TinPrice, TinPriceAverage, SalPrice, WholePrice, WholeWholePrice,
    MinimumSalPrice, RatePriceOffers, RateWholePrice, RateWholeWholePrice,
    RateMinimumSalPrice, RateDiscTinPrice, RateDiscSalPrice, RateDiscWholePrice,
    RateDiscWholeWholePrice, PriceOffers, PriceBiggest, PriceMedium, PriceSmall,
    tin, sal, btin, bsal, decayed, tinpricetotal, salpricetotal, saltinpricetotal,
    btinpricetotal, bsalpricetotal, decayedpricetotal, store, StoreCarton,
    ValStore, profits, UserName, Stores, ITMINDEX, QuickSearch, Items_Images,
    NotViewItems, Height, Width, Altitude, Density, Company_Branch_ID,
    BalanceBarcode, RateVAT, Vendorname, PriceIncludesVAT, RateDiscTinPriceAfter,
    RateDiscSalPriceAfter, RateDiscWholePriceAfter, RateDiscWholeWholePriceAfter,
    TypePlusDiscRateSalPrice, TypePlusDiscRateWholePrice, TypePlusDiscRateWholeWholePrice,
    CodeEAN13Barre, DeferredCurrentDiscount, InventoryDone, CompaniesID,
    CompaniesName, Tag, Description, LimitQuantity, DiscountedPrice,
    DiscountedPrice2, DiscountedPrice3
) 
SELECT 
    N'" & TransactionType & "' , id, GETDATE(), N'" & DeletedBy & "', N'" & DeleteReason & "',
    IDTM, itm_id, group_name, group_branch, sname, Unity, Pack, rng,
    TinPrice, TinPriceAverage, SalPrice, WholePrice, WholeWholePrice,
    MinimumSalPrice, RatePriceOffers, RateWholePrice, RateWholeWholePrice,
    RateMinimumSalPrice, RateDiscTinPrice, RateDiscSalPrice, RateDiscWholePrice,
    RateDiscWholeWholePrice, PriceOffers, PriceBiggest, PriceMedium, PriceSmall,
    tin, sal, btin, bsal, decayed, tinpricetotal, salpricetotal, saltinpricetotal,
    btinpricetotal, bsalpricetotal, decayedpricetotal, store, StoreCarton,
    ValStore, profits, UserName, Stores, ITMINDEX, QuickSearch, Items_Images,
    NotViewItems, Height, Width, Altitude, Density, Company_Branch_ID,
    BalanceBarcode, RateVAT, Vendorname, PriceIncludesVAT, RateDiscTinPriceAfter,
    RateDiscSalPriceAfter, RateDiscWholePriceAfter, RateDiscWholeWholePriceAfter,
    TypePlusDiscRateSalPrice, TypePlusDiscRateWholePrice, TypePlusDiscRateWholeWholePrice,
    CodeEAN13Barre, DeferredCurrentDiscount, InventoryDone, CompaniesID,
    CompaniesName, Tag, Description, LimitQuantity, DiscountedPrice,
    DiscountedPrice2, DiscountedPrice3
FROM Items 
WHERE itm_id = N'" & itm_id & "' and Stores = N'" & Stores & "'"
        cmd.ExecuteNonQuery()
        'Catch ex As SqlException
        '    Console.WriteLine("حدث خطأ في قاعدة البيانات: " & ex.Message)
        'Catch ex As Exception
        '    Console.WriteLine("حدث خطأ عام: " & ex.Message)
        'End Try
    End Sub

    Public Sub ArchiveAndDeleteItemsUnity(ByVal TransactionType As String, ByVal Unity_ID As String, ByVal itm_id As String, ByVal DeletedBy As String, ByVal DeleteReason As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "INSERT INTO Arch_ItemsUnity (
    TransactionType,
    OriginalID,
    DeletedDate,
    DeletedBy,
    DeleteReason,
    Unity_ID,
    itm_id,
    Unity_Name,
    NumberPieces,
    TinPriceUnit,
    SalPriceUnit,
    DefaultTin,
    DefaultSale,
    UnitySize_ID,
    itm_id_Unity,
    Company_Branch_ID
) 
SELECT 
    N'" & TransactionType & "' as TransactionType,
    Unity_ID as OriginalID,
    GETDATE() as DeletedDate,
    N'" & DeletedBy & "' as DeletedBy,
    N'" & DeleteReason & "' as DeleteReason,
    Unity_ID,
    itm_id,
    Unity_Name,
    NumberPieces,
    TinPriceUnit,
    SalPriceUnit,
    DefaultTin,
    DefaultSale,
    UnitySize_ID,
    itm_id_Unity,
    Company_Branch_ID
FROM ItemsUnity 
WHERE Unity_ID = N'" & Unity_ID & "' AND itm_id = N'" & itm_id & "'"
            cmd.ExecuteNonQuery()

        Catch ex As SqlException
            Console.WriteLine("حدث خطأ في قاعدة البيانات: " & ex.Message)
        Catch ex As Exception
            Console.WriteLine("حدث خطأ عام: " & ex.Message)
        End Try
    End Sub
    Public Sub ArchiveAndDeleteBilltINData(ByVal TransactionType As String, ByVal bill_no As String, ByVal itm_id As String, ByVal Stores As String, ByVal DeletedBy As String, ByVal DeleteReason As String)
        'Try
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "INSERT INTO Arch_BilltINData (
    TransactionType, OriginalID, DeletedDate, DeletedBy, DeleteReason,
    bill_no, IDTM, itm_id, itm_cat, group_branch, itm_name, itm_Unity,
    Pack, price, Price_Unity, TinPriceAverage, qu, qu_unity, totalprice,
    UserName, bill_date, Stores, CurrentStock, bill_EndDate, Expired,
    Discounts, StateDisc, ITMINDEX, Sales_Bill_NO, bill_ProductionDate,
    Company_Branch_ID, RateVAT, BeforeVAT, ValueVAT, Vendorname,
    qu_expired, qu_expired_carton, Treasury_Code, bill_no_Expired,
    Discount_Price_After, DiscountsValue, PriceIncludesVAT, StoreCurrent,
    StoreCurrentTotal, CurrentStockTotal, DeferredCurrentDiscount, totalpriceNet
) 
SELECT 
    N'" & TransactionType & "', bill_no, GETDATE(), N'" & DeletedBy & "', N'" & DeleteReason & "',
    bill_no, IDTM, itm_id, itm_cat, group_branch, itm_name, itm_Unity,
    Pack, price, Price_Unity, TinPriceAverage, qu, qu_unity, totalprice,
    UserName, bill_date, Stores, CurrentStock, bill_EndDate, Expired,
    Discounts, StateDisc, ITMINDEX, Sales_Bill_NO, bill_ProductionDate,
    Company_Branch_ID, RateVAT, BeforeVAT, ValueVAT, Vendorname,
    qu_expired, qu_expired_carton, Treasury_Code, bill_no_Expired,
    Discount_Price_After, DiscountsValue, PriceIncludesVAT, StoreCurrent,
    StoreCurrentTotal, CurrentStockTotal, DeferredCurrentDiscount, totalpriceNet
FROM BilltINData 
WHERE bill_no = N'" & bill_no & "' and itm_id = N'" & itm_id & "' and Stores = N'" & Stores & "'"
        cmd.ExecuteNonQuery()
        'Catch ex As SqlException
        '    Console.WriteLine("حدث خطأ في قاعدة البيانات: " & ex.Message)
        'Catch ex As Exception
        '    Console.WriteLine("حدث خطأ عام: " & ex.Message)
        'End Try
    End Sub

    Public Sub ArchiveAndDeleteBilltINData2(ByVal TransactionType As String, ByVal bill_no As String, ByVal DeletedBy As String, ByVal DeleteReason As String)
        'Try
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "INSERT INTO Arch_BilltINData (
    TransactionType, OriginalID, DeletedDate, DeletedBy, DeleteReason,
    bill_no, IDTM, itm_id, itm_cat, group_branch, itm_name, itm_Unity,
    Pack, price, Price_Unity, TinPriceAverage, qu, qu_unity, totalprice,
    UserName, bill_date, Stores, CurrentStock, bill_EndDate, Expired,
    Discounts, StateDisc, ITMINDEX, Sales_Bill_NO, bill_ProductionDate,
    Company_Branch_ID, RateVAT, BeforeVAT, ValueVAT, Vendorname,
    qu_expired, qu_expired_carton, Treasury_Code, bill_no_Expired,
    Discount_Price_After, DiscountsValue, PriceIncludesVAT, StoreCurrent,
    StoreCurrentTotal, CurrentStockTotal, DeferredCurrentDiscount, totalpriceNet
) 
SELECT 
    N'" & TransactionType & "', bill_no, GETDATE(), N'" & DeletedBy & "', N'" & DeleteReason & "',
    bill_no, IDTM, itm_id, itm_cat, group_branch, itm_name, itm_Unity,
    Pack, price, Price_Unity, TinPriceAverage, qu, qu_unity, totalprice,
    UserName, bill_date, Stores, CurrentStock, bill_EndDate, Expired,
    Discounts, StateDisc, ITMINDEX, Sales_Bill_NO, bill_ProductionDate,
    Company_Branch_ID, RateVAT, BeforeVAT, ValueVAT, Vendorname,
    qu_expired, qu_expired_carton, Treasury_Code, bill_no_Expired,
    Discount_Price_After, DiscountsValue, PriceIncludesVAT, StoreCurrent,
    StoreCurrentTotal, CurrentStockTotal, DeferredCurrentDiscount, totalpriceNet
FROM BilltINData 
WHERE bill_no = N'" & bill_no & "'"
        cmd.ExecuteNonQuery()
        'Catch ex As SqlException
        '    Console.WriteLine("حدث خطأ في قاعدة البيانات: " & ex.Message)
        'Catch ex As Exception
        '    Console.WriteLine("حدث خطأ عام: " & ex.Message)
        'End Try
    End Sub

    Public Sub ArchiveAndDeleteBillsalData(ByVal TransactionType As String, ByVal bill_no As String, ByVal DeletedBy As String, ByVal DeleteReason As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "INSERT INTO Arch_BillsalData (
    TransactionType, OriginalID, DeletedDate, DeletedBy, DeleteReason,
    bill_no, OrderID, Sheft_Number, IDTM, itm_id, itm_cat, itm_name,
    itm_Unity, itm_Notes, price, Price_Unity, TinPrice, TinPriceAverage,
    qu, qu_unity, totalprice, SalTinTotalprice, UserName, bill_date,
    billtime, Stores, CurrentStock, Discounts, DiscountsValue, Profits,
    EmpName, StatusOrder, Company_Branch_ID, TotalExpenses, TotalIncome,
    RateVAT, BeforeVAT, ValueVAT, Vendorname, Stat, ResourceName,
    Treasury_Code, bill_EndDate, bill_no_Expired, Discount_Price_After,
    StateDisc, DiscountsTin, CloseSheft, BillTimeAmBm, PriceIncludesVAT,
    StoreCurrent, StoreCurrentTotal, CurrentStockTotal
) 
SELECT 
    N'" & TransactionType & "', bill_no, GETDATE(), N'" & DeletedBy & "', N'" & DeleteReason & "',
    bill_no, OrderID, Sheft_Number, IDTM, itm_id, itm_cat, itm_name,
    itm_Unity, itm_Notes, price, Price_Unity, TinPrice, TinPriceAverage,
    qu, qu_unity, totalprice, SalTinTotalprice, UserName, bill_date,
    billtime, Stores, CurrentStock, Discounts, DiscountsValue, Profits,
    EmpName, StatusOrder, Company_Branch_ID, TotalExpenses, TotalIncome,
    RateVAT, BeforeVAT, ValueVAT, Vendorname, Stat, ResourceName,
    Treasury_Code, bill_EndDate, bill_no_Expired, Discount_Price_After,
    StateDisc, DiscountsTin, CloseSheft, BillTimeAmBm, PriceIncludesVAT,
    StoreCurrent, StoreCurrentTotal, CurrentStockTotal
FROM BillsalData 
WHERE bill_no = N'" & bill_no & "'"
            cmd.ExecuteNonQuery()
        Catch ex As SqlException
            Console.WriteLine("حدث خطأ في قاعدة البيانات: " & ex.Message)
        Catch ex As Exception
            Console.WriteLine("حدث خطأ عام: " & ex.Message)
        End Try
    End Sub

    Public Sub ArchiveAndDeleteEmployeeData(ByVal TransactionType As String, ByVal EMPID As String, ByVal DeletedBy As String, ByVal DeleteReason As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "INSERT INTO Arch_Employees (
    TransactionType, OriginalID, DeletedDate, DeletedBy, DeleteReason,
    EMPID, NameEmployee, Address, Phone, MaritalStatus, Qualification,
    Job, Administration, DateBirth, DateAppointment, DateEndService,
    NationalID, NumberInsurance, MilitaryService, BalanceLeavePrior,
    BalanceAnnualLeave, TotalBalanceLeave, DaysAbsence, DaysSanctions,
    LeaveWithoutPay, UserName, Bran_code, Emp_Type_Code, Emp_Status_Code,
    Emp_Salary, Emp_Rate_Hour, Emp_Count_Hour, Company_Branch_ID
) 
SELECT 
    N'" & TransactionType & "', EMPID, GETDATE(), N'" & DeletedBy & "', N'" & DeleteReason & "',
    EMPID, NameEmployee, Address, Phone, MaritalStatus, Qualification,
    Job, Administration, DateBirth, DateAppointment, DateEndService,
    NationalID, NumberInsurance, MilitaryService, BalanceLeavePrior,
    BalanceAnnualLeave, TotalBalanceLeave, DaysAbsence, DaysSanctions,
    LeaveWithoutPay, UserName, Bran_code, Emp_Type_Code, Emp_Status_Code,
    Emp_Salary, Emp_Rate_Hour, Emp_Count_Hour, Company_Branch_ID
FROM Employees 
WHERE EMPID = N'" & EMPID & "'"
            cmd.ExecuteNonQuery()
        Catch ex As SqlException
            Console.WriteLine("حدث خطأ في قاعدة البيانات: " & ex.Message)
        Catch ex As Exception
            Console.WriteLine("حدث خطأ عام: " & ex.Message)
        End Try
    End Sub

    Public Sub ArchiveAndDeleteExpensesData(ByVal TransactionType As String, ByVal Expenses_ID As String, ByVal DeletedBy As String, ByVal DeleteReason As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "INSERT INTO Arch_Expenses (
    TransactionType, OriginalID, DeletedDate, DeletedBy, DeleteReason,
    Expenses_ID, Exp_Name, Exp_Value, Exp_Date, Exp_Notes, Exp_Date_,
    UserName, Cats, OrderID, Company_Branch_ID, AfterValueVAT, RateVAT,
    ValueVAT, Treasury_Code, CloseSheft, Payment_Status, Vendorname
) 
SELECT 
    N'" & TransactionType & "', id, GETDATE(), N'" & DeletedBy & "', N'" & DeleteReason & "',
    id, Exp_Name, Exp_Value, Exp_Date, Exp_Notes, Exp_Date_,
    UserName, Cats, OrderID, Company_Branch_ID, AfterValueVAT, RateVAT,
    ValueVAT, Treasury_Code, CloseSheft, Payment_Status, Vendorname
FROM Expenses 
WHERE id = N'" & Expenses_ID & "'"
            cmd.ExecuteNonQuery()
        Catch ex As SqlException
            Console.WriteLine("حدث خطأ في قاعدة البيانات: " & ex.Message)
        Catch ex As Exception
            Console.WriteLine("حدث خطأ عام: " & ex.Message)
        End Try
    End Sub

    Public Sub ArchiveAndDeleteGroupsData(ByVal TransactionType As String, ByVal Groups_ID As String, ByVal DeletedBy As String, ByVal DeleteReason As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "INSERT INTO Arch_Groups (
    TransactionType, OriginalID, DeletedDate, DeletedBy, DeleteReason,
    Groups_ID, G_name, Groups_Image, UserName, Company_Branch_ID,
    QuickSearch, RateDiscTinPrice, RateDiscSalPrice, RateDiscWholePrice,
    RateDiscWholeWholePrice, TypePlusDiscRateSalPrice, TypePlusDiscRateWholePrice,
    TypePlusDiscRateWholeWholePrice, IsHide, CategoryId
) 
SELECT 
    N'" & TransactionType & "', Groups_ID, GETDATE(), N'" & DeletedBy & "', N'" & DeleteReason & "',
    Groups_ID, G_name, Groups_Image, UserName, Company_Branch_ID,
    QuickSearch, RateDiscTinPrice, RateDiscSalPrice, RateDiscWholePrice,
    RateDiscWholeWholePrice, TypePlusDiscRateSalPrice, TypePlusDiscRateWholePrice,
    TypePlusDiscRateWholeWholePrice, IsHide, CategoryId
FROM Groups 
WHERE Groups_ID = N'" & Groups_ID & "'"
            cmd.ExecuteNonQuery()
        Catch ex As SqlException
            Console.WriteLine("حدث خطأ في قاعدة البيانات: " & ex.Message)
        Catch ex As Exception
            Console.WriteLine("حدث خطأ عام: " & ex.Message)
        End Try
    End Sub

    Public Sub ArchiveAndDeleteVendorsData(ByVal TransactionType As String, ByVal Vendorname As String, ByVal DeletedBy As String, ByVal DeleteReason As String)
        'Try
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "INSERT INTO Arch_vendors (
    TransactionType, OriginalID, DeletedDate, DeletedBy, DeleteReason,
    vendors_ID, Vendorname, addr, mob1, tel1, fax1, taxn, tradn, resp,
    rtel, rmob, notes, BVndPay, BVndDiscount, valuereturns, vintinval,
    vndiscount, VnPay, VnReceipts, TaxValue, vnamntcredit, vnamntdebit,
    UserName, Company_Branch_ID, BVstPay, BVstDiscount, ValueVAT,
    Total_Qunt, VendorExpenses
) 
SELECT 
    N'" & TransactionType & "', id, GETDATE(), N'" & DeletedBy & "', N'" & DeleteReason & "',
    id, Vendorname, addr, mob1, tel1, fax1, taxn, tradn, resp,
    rtel, rmob, notes, BVndPay, BVndDiscount, valuereturns, vintinval,
    vndiscount, VnPay, VnReceipts, TaxValue, vnamntcredit, vnamntdebit,
    UserName, Company_Branch_ID, BVstPay, BVstDiscount, ValueVAT,
    Total_Qunt, VendorExpenses
FROM vendors 
WHERE Vendorname = N'" & Vendorname & "'"
        cmd.ExecuteNonQuery()
        'Catch ex As SqlException
        '    Console.WriteLine("حدث خطأ في قاعدة البيانات: " & ex.Message)
        'Catch ex As Exception
        '    Console.WriteLine("حدث خطأ عام: " & ex.Message)
        'End Try
    End Sub

End Class
