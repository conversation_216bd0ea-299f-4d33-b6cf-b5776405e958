﻿Imports CrystalDecisions.CrystalReports.Engine
Public Class Frm_Daily_Report_Factory
    Dim WithEvents BS As New BindingSource
    Dim Custamntcredit, Custamntdebit, DateCustamntcredit, DateCustamntdebit, CountFirstamntcredit, CountFirstamntdebit, CountLastamntcredit, CountLastamntdebit As Double
    Dim CustMainDateAll As String
    Dim CustMainDate As Long
    Dim TotalBtin As Integer
    Dim TotalCreditor, TotalDebtor As String

    Private Sub FrmDaysAllDataDetails_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
    End Sub

    Public Sub GetDataTin()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "select totalpricebeforedisc from IM_Btin where Vendorname <> N''"

        If Chek_WithoutDate.Checked = False Then
            S = S & " and bill_date >=N'" & Cls.C_date(Dtp_from.Text) & "' and bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        cmd.CommandText = S
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            TotalBtin = 0
        Else
            If dr(0) Is DBNull.Value Then
            Else
                TotalBtin = dr(0)
            End If
        End If


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT BILL_NO AS [رقم الفاتورة],Vendorname AS [اسم المورد],UserName AS [بيان],itm_name as [الصنف],qu_unity as [الكمية],price as [السعر],totalprice as [الاجمالى], bill_date AS [تاريخ ], vnamntcredit AS [دائن], itm_Unity AS [الوحدة], Stat AS [الحالة],itm_id as [الباركود],CurrentStock as [الرصيد الحالى] FROM View_purchase_bill where BILL_NO <> N'جرد1'"

        If Chek_WithoutDate.Checked = False Then
            S = S & " and bill_date >=N'" & Cls.C_date(Dtp_from.Text) & "' and bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridViewTin.DataSource = Cls.PopulateDataView(dr)

        'SumDGV()
        Dim SM As String
        For i As Integer = 0 To DataGridViewTin.RowCount - 1
            DataGridViewTin.Rows(i).Cells(2).Value = "مشتريات"
            SM = Val(DataGridViewTin.Rows(i).Cells(7).Value)
            SM = Cls.R_date(SM)
            DataGridViewTin.Rows(i).Cells(7).Value = SM
        Next

    End Sub

    Private Sub GetDataBTin()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT bill_no AS [رقم الفاتورة],Vendorname AS [اسم المورد],UserName AS [بيان],itm_name as [الصنف],price as [السعر],qu_unity as [الكمية], totalprice AS [المبلغ], bill_date AS [تاريخ ], vnamntdebit AS [مدين ], itm_Unity AS [الوحدة], Stat AS [الحالة],itm_id as [الباركود],CurrentStock as [الرصيد الحالى] FROM View_IM_BTin where bill_no <> N''"

        If Chek_WithoutDate.Checked = False Then
            S = S & " and bill_date >=N'" & Cls.C_date(Dtp_from.Text) & "' and bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridViewBtin.DataSource = Cls.PopulateDataView(dr)
        DataGridViewBtin.Columns(1).Visible = False

        Dim SM As String
        For i As Integer = 0 To DataGridViewBtin.RowCount - 1
            DataGridViewBtin.Rows(i).Cells(2).Value = "مرتجعات مشتريات"
            SM = Val(DataGridViewBtin.Rows(i).Cells(7).Value)
            SM = Cls.R_date(SM)
            DataGridViewBtin.Rows(i).Cells(7).Value = SM
        Next
    End Sub

    Private Sub GetDataPay()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT BillNo AS [رقم الفاتورة],Vendorname AS [اسم المورد],Vnd_State AS [بيان], VND_amx AS [المبلغ], VND_dt AS [تاريخ ], vnamntcredit AS [دائن ] FROM View_Vnd where BillNo <> N''"

        If Chek_WithoutDate.Checked = False Then
            S = S & " and VND_dt >=N'" & Cls.C_date(Dtp_from.Text) & "' and VND_dt <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridViewPay.DataSource = Cls.PopulateDataView(dr)
        DataGridViewPay.Columns(1).Visible = False

        Dim SM As String
        For i As Integer = 0 To DataGridViewPay.RowCount - 1
            SM = Val(DataGridViewPay.Rows(i).Cells(4).Value)
            SM = Cls.R_date(SM)
            DataGridViewPay.Rows(i).Cells(4).Value = SM
        Next
    End Sub

    Private Sub GetDataVnd_disc()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT TIN_NO AS [رقم الفاتورة],Vendorname AS [اسم المورد],State AS [بيان], amnt AS [المبلغ], pdate AS [تاريخ ], vnamntdebit AS [مدين ] FROM View_Vnd_disc where TIN_NO <> N''"

        If Chek_WithoutDate.Checked = False Then
            S = S & " and pdate >=N'" & Cls.C_date(Dtp_from.Text) & "' and pdate <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridViewDisc.DataSource = Cls.PopulateDataView(dr)
        DataGridViewDisc.Columns(1).Visible = False

        Dim SM As String
        For i As Integer = 0 To DataGridViewDisc.RowCount - 1
            SM = Val(DataGridViewDisc.Rows(i).Cells(4).Value)
            SM = Cls.R_date(SM)
            DataGridViewDisc.Rows(i).Cells(4).Value = SM
        Next
    End Sub

    Private Sub GetDataSales()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "select totalpriceafterdisc from IM_Bsal where Vendorname <> N''"

        If Chek_WithoutDate.Checked = False Then
            S = S & " and bill_date >=N'" & Cls.C_date(Dtp_from.Text) & "' and bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        cmd.CommandText = S
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            TotalBtin = 0
        Else
            If dr(0) Is DBNull.Value Then
            Else
                TotalBtin = dr(0)
            End If
        End If
        '==============================================================================================
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT BILL_NO AS [رقم الفاتورة],Vendorname AS [اسم العميل],UserName AS [بيان],itm_name as [الصنف],qu_unity as [الكمية],price as [السعر],totalprice as [الاجمالى], bill_date AS [تاريخ ], totalprice AS [مدين] , itm_Unity AS [الوحدة], Stat AS [الحالة],itm_id as [الباركود],CurrentStock as [الرصيد الحالى] FROM BillsalData where BILL_NO <> N'جرد'"

        If Chek_WithoutDate.Checked = False Then
            S = S & " and bill_date >=N'" & Cls.C_date(Dtp_from.Text) & "' and bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridViewSal.DataSource = Cls.PopulateDataView(dr)
        DataGridViewSal.Columns(1).Visible = False

        Dim SM As String
        For i As Integer = 0 To DataGridViewSal.RowCount - 1
            DataGridViewSal.Rows(i).Cells(2).Value = "مبيعات"
            SM = Val(DataGridViewSal.Rows(i).Cells(7).Value)
            SM = Cls.R_date(SM)
            DataGridViewSal.Rows(i).Cells(7).Value = SM
        Next
    End Sub

    Private Sub Chek_WithoutDate_CheckedChanged(sender As Object, e As EventArgs) Handles Chek_WithoutDate.CheckedChanged
        If Chek_WithoutDate.Checked = True Then
            Dtp_from.Enabled = False
            Dtp_To.Enabled = False
        Else
            Dtp_from.Enabled = True
            Dtp_To.Enabled = True
        End If
    End Sub

    Private Sub GetDataBSales()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT bill_no AS [رقم الفاتورة],Vendorname AS [اسم العميل],UserName AS [بيان],itm_name as [الصنف],price as [السعر],qu_unity as [الكمية], totalprice AS [المبلغ], bill_date AS [تاريخ ], vnamntdebit AS [دائن ], itm_Unity AS [الوحدة], Stat AS [الحالة],itm_id as [الباركود],CurrentStock as [الرصيد الحالى]  FROM View_IM_Bsal where bill_no <> N''"

        If Chek_WithoutDate.Checked = False Then
            S = S & " and bill_date >=N'" & Cls.C_date(Dtp_from.Text) & "' and bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridViewBsal.DataSource = Cls.PopulateDataView(dr)
        DataGridViewBsal.Columns(1).Visible = False

        Dim SM As String
        For i As Integer = 0 To DataGridViewBsal.RowCount - 1
            DataGridViewBsal.Rows(i).Cells(2).Value = "مرتجعات مبيعات"
            SM = Val(DataGridViewBsal.Rows(i).Cells(7).Value)
            SM = Cls.R_date(SM)
            DataGridViewBsal.Rows(i).Cells(7).Value = SM
        Next

    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub

    Private Sub GetDataPaySales()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT BillNo AS [رقم الفاتورة],Vendorname AS [اسم العميل],Vst_State AS [بيان], VND_amx AS [المبلغ], VND_dt AS [تاريخ ], vnamntcredit AS [مدين ] FROM View_Vst where BillNo <> N''"

        If Chek_WithoutDate.Checked = False Then
            S = S & " and VND_dt >=N'" & Cls.C_date(Dtp_from.Text) & "' and VND_dt <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridViewPaySal.DataSource = Cls.PopulateDataView(dr)
        DataGridViewPaySal.Columns(1).Visible = False

        Dim SM As String
        For i As Integer = 0 To DataGridViewPaySal.RowCount - 1
            SM = Val(DataGridViewPaySal.Rows(i).Cells(4).Value)
            SM = Cls.R_date(SM)
            DataGridViewPaySal.Rows(i).Cells(4).Value = SM
        Next
    End Sub

    Private Sub GetDataDiscSales()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT TIN_NO AS [رقم الفاتورة],Vendorname AS [اسم العميل],State AS [بيان], amnt AS [المبلغ], pdate AS [تاريخ ], vnamntdebit AS [دائن ] FROM View_Vst_disc where TIN_NO <> N''"

        If Chek_WithoutDate.Checked = False Then
            S = S & " and pdate >=N'" & Cls.C_date(Dtp_from.Text) & "' and pdate <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridViewDiscSal.DataSource = Cls.PopulateDataView(dr)
        DataGridViewDiscSal.Columns(1).Visible = False

        Dim SM As String
        For i As Integer = 0 To DataGridViewDiscSal.RowCount - 1
            SM = Val(DataGridViewDiscSal.Rows(i).Cells(4).Value)
            SM = Cls.R_date(SM)
            DataGridViewDiscSal.Rows(i).Cells(4).Value = SM
        Next
    End Sub

    Private Sub GetDataExpenses()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT id AS [رقم],Exp_Name AS [بيان المصروف],Expenses AS [بيان], Exp_Value AS [المبلغ], Exp_Date AS [تاريخ ], vnamntcredit AS [دائن ] FROM View_Expenses where id <> N''"

        If Chek_WithoutDate.Checked = False Then
            S = S & " and Exp_Date >=N'" & Cls.C_date(Dtp_from.Text) & "' and Exp_Date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridViewExpenses.DataSource = Cls.PopulateDataView(dr)
        DataGridViewExpenses.Columns(1).Visible = False

        Dim SM As String
        For i As Integer = 0 To DataGridViewExpenses.RowCount - 1
            SM = Val(DataGridViewExpenses.Rows(i).Cells(4).Value)
            SM = Cls.R_date(SM)
            DataGridViewExpenses.Rows(i).Cells(4).Value = SM
        Next
    End Sub

    Private Sub GetDataChecks_Payable()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT Check_Number AS [رقم الشيك],Cust_Name AS [اسم العميل],Checks_Payable AS [بيان], Amount AS [المبلغ], Due_Date AS [تاريخ ], vnamntcredit AS [دائن ] FROM View_Checks_Payable where State_Check =N'لم يتم السداد'"

        If Chek_WithoutDate.Checked = False Then
            S = S & " and Due_Date >=N'" & Cls.C_date(Dtp_from.Text) & "' and Due_Date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridViewChecks_Payable.DataSource = Cls.PopulateDataView(dr)
        DataGridViewChecks_Payable.Columns(1).Visible = False

        Dim SM As String
        For i As Integer = 0 To DataGridViewChecks_Payable.RowCount - 1
            SM = Val(DataGridViewChecks_Payable.Rows(i).Cells(4).Value)
            SM = Cls.R_date(SM)
            DataGridViewChecks_Payable.Rows(i).Cells(4).Value = SM
        Next
    End Sub

    Private Sub GetDataItemsMaterials()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT bill_noNotice AS [رقم الفاتورة],UserName AS [بيان],sname as [الصنف],TinPrice as [السعر],qu_unity as [الكمية], totalprice AS [المبلغ], bill_date AS [تاريخ ], totalprice AS [مدين ], itm_Unity AS [الوحدة],itm_id as [الباركود] FROM View_ManufacturingDismissalNoticeData where id <> N''"

        If Chek_WithoutDate.Checked = False Then
            S = S & " and bill_date >=N'" & Cls.C_date(Dtp_from.Text) & "' and bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        dgvItemsMaterials.DataSource = Cls.PopulateDataView(dr)
        dgvItemsMaterials.Columns(1).Visible = False

        Dim SM As String
        For i As Integer = 0 To dgvItemsMaterials.RowCount - 1
            dgvItemsMaterials.Rows(i).Cells(1).Value = "خامات"
            SM = Val(dgvItemsMaterials.Rows(i).Cells(6).Value)
            SM = Cls.R_date(SM)
            dgvItemsMaterials.Rows(i).Cells(6).Value = SM
        Next
    End Sub

    Private Sub GetDataItemManufactured()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT bill_no AS [رقم الفاتورة],UserName AS [بيان],sname as [الصنف],qunt_Manufacturing as [الكمية], TotalCostPrice AS [المبلغ], bill_date AS [تاريخ ],itm_id_Manufacturing as [الباركود] FROM View_ManufacturingDismissalNotice where bill_no <> N''"

        If Chek_WithoutDate.Checked = False Then
            S = S & " and bill_date >=N'" & Cls.C_date(Dtp_from.Text) & "' and bill_date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        dgvItemManufactured.DataSource = Cls.PopulateDataView(dr)
        dgvItemManufactured.Columns(1).Visible = False

        Dim SM As String
        For i As Integer = 0 To dgvItemManufactured.RowCount - 1
            dgvItemManufactured.Rows(i).Cells(1).Value = "منتج مصنع"
            SM = Val(dgvItemManufactured.Rows(i).Cells(5).Value)
            SM = Cls.R_date(SM)
            dgvItemManufactured.Rows(i).Cells(5).Value = SM
        Next
    End Sub

    Private Sub SumDGVAll()
        Dim SM, SM14 As Double
        For i As Integer = 0 To DataGridViewTin.Rows.Count - 1
            SM = SM + Val(DataGridViewTin.Rows(i).Cells(8).Value.ToString)
            SM14 = SM14 + Val(DataGridViewTin.Rows(i).Cells(4).Value.ToString)
        Next
        txtPurchases.Text = SM
        txtPurchasesQunt.Text = SM14


        Dim SM1 As Double
        For i As Integer = 0 To DataGridViewBtin.Rows.Count - 1
            SM1 = SM1 + Val(DataGridViewBtin.Rows(i).Cells(8).Value.ToString)
        Next
        txtPurchasesReturns.Text = SM1


        Dim SM2 As Double
        For i As Integer = 0 To DataGridViewPay.Rows.Count - 1
            SM2 = SM2 + Val(DataGridViewPay.Rows(i).Cells(5).Value.ToString)
        Next
        txtPayPurchases.Text = SM2

        Dim SM3 As Double
        For i As Integer = 0 To DataGridViewDisc.Rows.Count - 1
            SM3 = SM3 + Val(DataGridViewDisc.Rows(i).Cells(5).Value.ToString)
        Next
        txtDsicPurchases.Text = SM3

        Dim SM4 As Double
        For i As Integer = 0 To DataGridViewExpenses.Rows.Count - 1
            SM4 = SM4 + Val(DataGridViewExpenses.Rows(i).Cells(5).Value.ToString)
        Next
        txtExpenses.Text = SM4

        Dim SM5, SM55 As Double
        For i As Integer = 0 To DataGridViewSal.Rows.Count - 1
            SM5 = SM5 + Val(DataGridViewSal.Rows(i).Cells(8).Value.ToString)
            SM55 = SM55 + Val(DataGridViewSal.Rows(i).Cells(4).Value.ToString)
        Next
        txtSales.Text = SM5
        txtSalesQunt.Text = SM55

        Dim SM6 As Double
        For i As Integer = 0 To DataGridViewBsal.Rows.Count - 1
            SM6 = SM6 + Val(DataGridViewBsal.Rows(i).Cells(8).Value.ToString)
        Next
        txtSalesReturns.Text = SM6

        Dim SM7 As Double
        For i As Integer = 0 To DataGridViewPaySal.Rows.Count - 1
            SM7 = SM7 + Val(DataGridViewPaySal.Rows(i).Cells(5).Value.ToString)
        Next
        txtSalesPay.Text = SM7

        Dim SM8 As Double
        For i As Integer = 0 To DataGridViewDiscSal.Rows.Count - 1
            SM8 = SM8 + Val(DataGridViewDiscSal.Rows(i).Cells(5).Value.ToString)
        Next
        txtSalesDisc.Text = SM8

        Dim SM9 As Double
        For i As Integer = 0 To DataGridViewChecks_Payable.Rows.Count - 1
            SM9 = SM9 + Val(DataGridViewChecks_Payable.Rows(i).Cells(5).Value.ToString)
        Next
        txtChecks_Payable.Text = SM9

        Dim SM10, SM12 As Double
        For i As Integer = 0 To dgvItemsMaterials.Rows.Count - 1
            SM10 = SM10 + Val(dgvItemsMaterials.Rows(i).Cells(5).Value.ToString)
            SM12 = SM12 + Val(dgvItemsMaterials.Rows(i).Cells(4).Value.ToString)
        Next
        txtItemsMaterials.Text = SM10
        txtItemsMaterialsQunt.Text = SM12

        Dim SM11, SM13 As Double
        For i As Integer = 0 To dgvItemManufactured.Rows.Count - 1
            SM11 = SM11 + Val(dgvItemManufactured.Rows(i).Cells(4).Value.ToString)
            SM13 = SM13 + Val(dgvItemManufactured.Rows(i).Cells(3).Value.ToString)
        Next
        txtItemManufactured.Text = SM11
        txtItemManufacturedQunt.Text = SM13



    End Sub

    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click

        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        GetDataItemsMaterials()
        GetDataItemManufactured()
        GetDataTin()
        GetDataBTin()
        GetDataPay()
        GetDataVnd_disc()
        GetDataSales()
        GetDataBSales()
        GetDataPaySales()
        GetDataDiscSales()
        GetDataExpenses()
        GetDataChecks_Payable()

        SumDGVAll()

        '========================================================

        If DataGridViewTin.Rows.Count = 0 And DataGridViewBtin.Rows.Count = 0 And DataGridViewPay.Rows.Count = 0 And DataGridViewDisc.Rows.Count = 0 And DataGridViewExpenses.Rows.Count = 0 And DataGridViewSal.Rows.Count = 0 And DataGridViewBsal.Rows.Count = 0 And DataGridViewPaySal.Rows.Count = 0 And DataGridViewDiscSal.Rows.Count = 0 And DataGridViewChecks_Payable.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To dgvItemManufactured.Rows.Count - 1
            S = "insert into PrintSalesPurchases (BILL_NO,Stat,itm_name,qu,totalprice,bill_date,itm_id,vintinval,valuereturns,VnPay,vndiscount,vnamntdebit,totalpricebeforedisc,totalpriceafterdisc,TotalBay,Totalstaying,vnamntcredit,Driv_Name,Driv_CarNumber,KiloMeter,Supervisor_Reform,Recipient,Received_Date,Number1,Number2)  values("
            S = S & "N'" & dgvItemManufactured.Rows(i).Cells(0).Value & "',N'" & dgvItemManufactured.Rows(i).Cells(1).Value & "',N'" & dgvItemManufactured.Rows(i).Cells(2).Value & "',N'" & dgvItemManufactured.Rows(i).Cells(3).Value & "',N'" & dgvItemManufactured.Rows(i).Cells(4).Value & "',N'" & dgvItemManufactured.Rows(i).Cells(5).Value & "',N'" & dgvItemManufactured.Rows(i).Cells(6).Value & "',N'" & txtPurchases.Text & "',N'" & txtPurchasesReturns.Text & "',N'" & txtPayPurchases.Text & "',N'" & txtDsicPurchases.Text & "',N'" & txtExpenses.Text & "',N'" & txtSales.Text & "',N'" & txtSalesDisc.Text & "',N'" & txtSalesReturns.Text & "',N'" & txtSalesPay.Text & "',N'" & txtChecks_Payable.Text & "',N'" & txtItemManufacturedQunt.Text & "',N'" & txtItemsMaterials.Text & "',N'" & txtPurchasesQunt.Text & "',N'" & txtSalesQunt.Text & "',N'" & txtItemManufactured.Text & "',N'" & txtItemsMaterials.Text & "',N'" & dgvItemManufactured.Rows(i).Cells(0).Value & "',N'" & Cls.C_date(dgvItemManufactured.Rows(i).Cells(5).Value) & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To dgvItemsMaterials.Rows.Count - 1
            S = "insert into PrintSalesPurchases (BILL_NO,Stat,itm_name,qu,price,totalprice,bill_date,TotalCreditor,TotalDebtor,Unity,itm_id,vintinval,valuereturns,VnPay,vndiscount,vnamntdebit,totalpricebeforedisc,totalpriceafterdisc,TotalBay,Totalstaying,vnamntcredit,Driv_Name,Driv_CarNumber,KiloMeter,Supervisor_Reform,Recipient,Received_Date,Number1,Number2)  values("
            S = S & "N'" & dgvItemsMaterials.Rows(i).Cells(0).Value & "',N'" & dgvItemsMaterials.Rows(i).Cells(1).Value & "',N'" & dgvItemsMaterials.Rows(i).Cells(2).Value & "',N'" & dgvItemsMaterials.Rows(i).Cells(3).Value & "',N'" & dgvItemsMaterials.Rows(i).Cells(4).Value & "',N'" & dgvItemsMaterials.Rows(i).Cells(5).Value & "',N'" & dgvItemsMaterials.Rows(i).Cells(6).Value & "',N'" & dgvItemsMaterials.Rows(i).Cells(7).Value & "',N'" & dgvItemsMaterials.Rows(i).Cells(8).Value & "',0,N'" & dgvItemsMaterials.Rows(i).Cells(9).Value & "',N'" & txtPurchases.Text & "',N'" & txtPurchasesReturns.Text & "',N'" & txtPayPurchases.Text & "',N'" & txtDsicPurchases.Text & "',N'" & txtExpenses.Text & "',N'" & txtSales.Text & "',N'" & txtSalesDisc.Text & "',N'" & txtSalesReturns.Text & "',N'" & txtSalesPay.Text & "',N'" & txtChecks_Payable.Text & "',N'" & txtItemManufacturedQunt.Text & "',N'" & txtItemsMaterials.Text & "',N'" & txtPurchasesQunt.Text & "',N'" & txtSalesQunt.Text & "',N'" & txtItemManufactured.Text & "',N'" & txtItemsMaterials.Text & "',N'" & dgvItemsMaterials.Rows(i).Cells(0).Value & "',N'" & Cls.C_date(dgvItemsMaterials.Rows(i).Cells(6).Value) & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridViewTin.Rows.Count - 1
            TotalCreditor += Val(DataGridViewTin.Rows(i).Cells(8).Value.ToString)
            S = "insert into PrintSalesPurchases (BILL_NO,Vendorname,Stat,itm_name,price,qu,totalprice,bill_date,TotalCreditor,TotalDebtor,vintinval,valuereturns,VnPay,vndiscount,vnamntdebit,totalpricebeforedisc,totalpriceafterdisc,TotalBay,Totalstaying,vnamntcredit,Unity,det,itm_id,store,Driv_Name,Driv_CarNumber,KiloMeter,Supervisor_Reform,Recipient,Received_Date,Number1,Number2)  values("
            S = S & "N'" & DataGridViewTin.Rows(i).Cells(0).Value & "',N'" & DataGridViewTin.Rows(i).Cells(1).Value & "',N'" & DataGridViewTin.Rows(i).Cells(2).Value & "',N'" & DataGridViewTin.Rows(i).Cells(3).Value & "',N'" & DataGridViewTin.Rows(i).Cells(4).Value & "',N'" & DataGridViewTin.Rows(i).Cells(5).Value & "',N'" & DataGridViewTin.Rows(i).Cells(6).Value & "',N'" & DataGridViewTin.Rows(i).Cells(7).Value & "',N'" & DataGridViewTin.Rows(i).Cells(8).Value & "',0,N'" & txtPurchases.Text & "',N'" & txtPurchasesReturns.Text & "',N'" & txtPayPurchases.Text & "',N'" & txtDsicPurchases.Text & "',N'" & txtExpenses.Text & "',N'" & txtSales.Text & "',N'" & txtSalesDisc.Text & "',N'" & txtSalesReturns.Text & "',N'" & txtSalesPay.Text & "',N'" & txtChecks_Payable.Text & "',N'" & DataGridViewTin.Rows(i).Cells(9).Value & "',N'" & DataGridViewTin.Rows(i).Cells(10).Value & "',N'" & DataGridViewTin.Rows(i).Cells(11).Value & "',N'" & DataGridViewTin.Rows(i).Cells(12).Value & "',N'" & txtItemManufacturedQunt.Text & "',N'" & txtItemsMaterials.Text & "',N'" & txtPurchasesQunt.Text & "',N'" & txtSalesQunt.Text & "',N'" & txtItemManufactured.Text & "',N'" & txtItemsMaterials.Text & "',N'" & DataGridViewTin.Rows(i).Cells(0).Value & "',N'" & Cls.C_date(DataGridViewTin.Rows(i).Cells(7).Value) & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridViewBtin.Rows.Count - 1
            TotalDebtor += Val(DataGridViewBtin.Rows(i).Cells(8).Value.ToString)
            S = "insert into PrintSalesPurchases (BILL_NO,Vendorname,Stat,itm_name,qu,price,totalprice,bill_date,TotalDebtor,TotalCreditor,vintinval,valuereturns,VnPay,vndiscount,vnamntdebit,totalpricebeforedisc,totalpriceafterdisc,TotalBay,Totalstaying,vnamntcredit,Unity,det,itm_id,store,Driv_Name,Driv_CarNumber,KiloMeter,Supervisor_Reform,Recipient,Received_Date,Number1,Number2)  values("
            S = S & "N'" & DataGridViewBtin.Rows(i).Cells(0).Value & "',N'" & DataGridViewBtin.Rows(i).Cells(1).Value & "',N'" & DataGridViewBtin.Rows(i).Cells(2).Value & "',N'" & DataGridViewBtin.Rows(i).Cells(3).Value & "',N'" & DataGridViewBtin.Rows(i).Cells(4).Value & "',N'" & DataGridViewBtin.Rows(i).Cells(5).Value & "',N'" & DataGridViewBtin.Rows(i).Cells(6).Value & "',N'" & DataGridViewBtin.Rows(i).Cells(7).Value & "',N'" & DataGridViewBtin.Rows(i).Cells(8).Value & "',0,N'" & txtPurchases.Text & "',N'" & txtPurchasesReturns.Text & "',N'" & txtPayPurchases.Text & "',N'" & txtDsicPurchases.Text & "',N'" & txtExpenses.Text & "',N'" & txtSales.Text & "',N'" & txtSalesDisc.Text & "',N'" & txtSalesReturns.Text & "',N'" & txtSalesPay.Text & "',N'" & txtChecks_Payable.Text & "',N'" & DataGridViewBtin.Rows(i).Cells(9).Value & "',N'" & DataGridViewBtin.Rows(i).Cells(10).Value & "',N'" & DataGridViewBtin.Rows(i).Cells(11).Value & "',N'" & DataGridViewTin.Rows(i).Cells(12).Value & "',N'" & txtItemManufacturedQunt.Text & "',N'" & txtItemsMaterials.Text & "',N'" & txtPurchasesQunt.Text & "',N'" & txtSalesQunt.Text & "',N'" & txtItemManufactured.Text & "',N'" & txtItemsMaterials.Text & "',N'" & DataGridViewBtin.Rows(i).Cells(0).Value & "',N'" & Cls.C_date(DataGridViewBtin.Rows(i).Cells(7).Value) & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridViewPay.Rows.Count - 1
            TotalDebtor += Val(DataGridViewPay.Rows(i).Cells(5).Value.ToString)
            S = "insert into PrintSalesPurchases (BILL_NO,Vendorname,Stat,qu,price,totalprice,bill_date,TotalDebtor,TotalCreditor,vintinval,valuereturns,VnPay,vndiscount,vnamntdebit,totalpricebeforedisc,totalpriceafterdisc,TotalBay,Totalstaying,vnamntcredit,Driv_Name,Driv_CarNumber,KiloMeter,Supervisor_Reform,Recipient,Received_Date,Number1,Number2)  values("
            S = S & "N'" & DataGridViewPay.Rows(i).Cells(0).Value & "',N'" & DataGridViewPay.Rows(i).Cells(1).Value & "',N'" & DataGridViewPay.Rows(i).Cells(2).Value & "',0,0,N'" & DataGridViewPay.Rows(i).Cells(3).Value & "',N'" & DataGridViewPay.Rows(i).Cells(4).Value & "',N'" & DataGridViewPay.Rows(i).Cells(5).Value & "',0,N'" & txtPurchases.Text & "',N'" & txtPurchasesReturns.Text & "',N'" & txtPayPurchases.Text & "',N'" & txtDsicPurchases.Text & "',N'" & txtExpenses.Text & "',N'" & txtSales.Text & "',N'" & txtSalesDisc.Text & "',N'" & txtSalesReturns.Text & "',N'" & txtSalesPay.Text & "',N'" & txtChecks_Payable.Text & "',N'" & txtItemManufacturedQunt.Text & "',N'" & txtItemsMaterials.Text & "',N'" & txtPurchasesQunt.Text & "',N'" & txtSalesQunt.Text & "',N'" & txtItemManufactured.Text & "',N'" & txtItemsMaterials.Text & "',N'" & DataGridViewPay.Rows(i).Cells(0).Value & "',N'" & Cls.C_date(DataGridViewPay.Rows(i).Cells(6).Value) & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridViewDisc.Rows.Count - 1
            TotalDebtor += Val(DataGridViewDisc.Rows(i).Cells(5).Value.ToString)
            S = "insert into PrintSalesPurchases (BILL_NO,Vendorname,Stat,qu,price,totalprice,bill_date,TotalDebtor,TotalCreditor,vintinval,valuereturns,VnPay,vndiscount,vnamntdebit,totalpricebeforedisc,totalpriceafterdisc,TotalBay,Totalstaying,vnamntcredit,Driv_Name,Driv_CarNumber,KiloMeter,Supervisor_Reform,Recipient,Received_Date,Number1,Number2)  values("
            S = S & "N'" & DataGridViewDisc.Rows(i).Cells(0).Value & "',N'" & DataGridViewDisc.Rows(i).Cells(1).Value & "',N'" & DataGridViewDisc.Rows(i).Cells(2).Value & "',0,0,N'" & DataGridViewDisc.Rows(i).Cells(3).Value & "',N'" & DataGridViewDisc.Rows(i).Cells(4).Value & "',N'" & DataGridViewDisc.Rows(i).Cells(5).Value & "',0,N'" & txtPurchases.Text & "',N'" & txtPurchasesReturns.Text & "',N'" & txtPayPurchases.Text & "',N'" & txtDsicPurchases.Text & "',N'" & txtExpenses.Text & "',N'" & txtSales.Text & "',N'" & txtSalesDisc.Text & "',N'" & txtSalesReturns.Text & "',N'" & txtSalesPay.Text & "',N'" & txtChecks_Payable.Text & "',N'" & txtItemManufacturedQunt.Text & "',N'" & txtItemsMaterials.Text & "',N'" & txtPurchasesQunt.Text & "',N'" & txtSalesQunt.Text & "',N'" & txtItemManufactured.Text & "',N'" & txtItemsMaterials.Text & "',N'" & DataGridViewDisc.Rows(i).Cells(0).Value & "',N'" & Cls.C_date(DataGridViewDisc.Rows(i).Cells(6).Value) & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridViewExpenses.Rows.Count - 1
            TotalDebtor += Val(DataGridViewExpenses.Rows(i).Cells(5).Value.ToString)
            S = "insert into PrintSalesPurchases (BILL_NO,Vendorname,Stat,qu,price,totalprice,bill_date,TotalDebtor,TotalCreditor,vintinval,valuereturns,VnPay,vndiscount,vnamntdebit,totalpricebeforedisc,totalpriceafterdisc,TotalBay,Totalstaying,vnamntcredit,Driv_Name,Driv_CarNumber,KiloMeter,Supervisor_Reform,Recipient,Received_Date,Number1,Number2)  values("
            S = S & "N'" & DataGridViewExpenses.Rows(i).Cells(0).Value & "',N'" & DataGridViewExpenses.Rows(i).Cells(1).Value & "',N'" & DataGridViewExpenses.Rows(i).Cells(2).Value & "',0,0,N'" & DataGridViewExpenses.Rows(i).Cells(3).Value & "',N'" & DataGridViewExpenses.Rows(i).Cells(4).Value & "',N'" & DataGridViewExpenses.Rows(i).Cells(5).Value & "',0,N'" & txtPurchases.Text & "',N'" & txtPurchasesReturns.Text & "',N'" & txtPayPurchases.Text & "',N'" & txtDsicPurchases.Text & "',N'" & txtExpenses.Text & "',N'" & txtSales.Text & "',N'" & txtSalesDisc.Text & "',N'" & txtSalesReturns.Text & "',N'" & txtSalesPay.Text & "',N'" & txtChecks_Payable.Text & "',N'" & txtItemManufacturedQunt.Text & "',N'" & txtItemsMaterials.Text & "',N'" & txtPurchasesQunt.Text & "',N'" & txtSalesQunt.Text & "',N'" & txtItemManufactured.Text & "',N'" & txtItemsMaterials.Text & "',N'" & DataGridViewExpenses.Rows(i).Cells(0).Value & "',N'" & Cls.C_date(DataGridViewExpenses.Rows(i).Cells(6).Value) & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        '========================================================================================
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridViewSal.Rows.Count - 1
            TotalCreditor += Val(DataGridViewSal.Rows(i).Cells(5).Value.ToString)
            S = "insert into PrintSalesPurchases (BILL_NO,Vendorname,Stat,itm_name,qu,price,totalprice,bill_date,TotalCreditor,TotalDebtor,vintinval,valuereturns,VnPay,vndiscount,vnamntdebit,totalpricebeforedisc,totalpriceafterdisc,TotalBay,Totalstaying,vnamntcredit,Unity,det,itm_id,store,Driv_Name,Driv_CarNumber,KiloMeter,Supervisor_Reform,Recipient,Received_Date,Number1,Number2)  values("
            S = S & "N'" & DataGridViewSal.Rows(i).Cells(0).Value & "',N'" & DataGridViewSal.Rows(i).Cells(1).Value & "',N'" & DataGridViewSal.Rows(i).Cells(2).Value & "',N'" & DataGridViewSal.Rows(i).Cells(3).Value & "',N'" & DataGridViewSal.Rows(i).Cells(4).Value & "',N'" & DataGridViewSal.Rows(i).Cells(5).Value & "',N'" & DataGridViewSal.Rows(i).Cells(6).Value & "',N'" & DataGridViewSal.Rows(i).Cells(7).Value & "',N'" & DataGridViewSal.Rows(i).Cells(8).Value & "',0,N'" & txtPurchases.Text & "',N'" & txtPurchasesReturns.Text & "',N'" & txtPayPurchases.Text & "',N'" & txtDsicPurchases.Text & "',N'" & txtExpenses.Text & "',N'" & txtSales.Text & "',N'" & txtSalesDisc.Text & "',N'" & txtSalesReturns.Text & "',N'" & txtSalesPay.Text & "',N'" & txtChecks_Payable.Text & "',N'" & DataGridViewSal.Rows(i).Cells(9).Value & "',N'" & DataGridViewSal.Rows(i).Cells(10).Value & "',N'" & DataGridViewSal.Rows(i).Cells(11).Value & "',N'" & DataGridViewSal.Rows(i).Cells(12).Value & "',N'" & txtItemManufacturedQunt.Text & "',N'" & txtItemsMaterials.Text & "',N'" & txtPurchasesQunt.Text & "',N'" & txtSalesQunt.Text & "',N'" & txtItemManufactured.Text & "',N'" & txtItemsMaterials.Text & "',N'" & DataGridViewSal.Rows(i).Cells(0).Value & "',N'" & Cls.C_date(DataGridViewSal.Rows(i).Cells(7).Value) & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridViewBsal.Rows.Count - 1
            TotalDebtor += Val(DataGridViewBsal.Rows(i).Cells(8).Value.ToString)
            S = "insert into PrintSalesPurchases (BILL_NO,Vendorname,Stat,itm_name,qu,price,totalprice,bill_date,TotalDebtor,TotalCreditor,vintinval,valuereturns,VnPay,vndiscount,vnamntdebit,totalpricebeforedisc,totalpriceafterdisc,TotalBay,Totalstaying,vnamntcredit,Unity,det,itm_id,store,Driv_Name,Driv_CarNumber,KiloMeter,Supervisor_Reform,Recipient,Received_Date,Number1,Number2)  values("
            S = S & "N'" & DataGridViewBsal.Rows(i).Cells(0).Value & "',N'" & DataGridViewBsal.Rows(i).Cells(1).Value & "',N'" & DataGridViewBsal.Rows(i).Cells(2).Value & "',N'" & DataGridViewBsal.Rows(i).Cells(3).Value & "',N'" & DataGridViewBsal.Rows(i).Cells(4).Value & "',N'" & DataGridViewBsal.Rows(i).Cells(5).Value & "',N'" & DataGridViewBsal.Rows(i).Cells(6).Value & "',N'" & DataGridViewBsal.Rows(i).Cells(7).Value & "',N'" & DataGridViewBsal.Rows(i).Cells(8).Value & "',0,N'" & txtPurchases.Text & "',N'" & txtPurchasesReturns.Text & "',N'" & txtPayPurchases.Text & "',N'" & txtDsicPurchases.Text & "',N'" & txtExpenses.Text & "',N'" & txtSales.Text & "',N'" & txtSalesDisc.Text & "',N'" & txtSalesReturns.Text & "',N'" & txtSalesPay.Text & "',N'" & txtChecks_Payable.Text & "',N'" & DataGridViewBsal.Rows(i).Cells(9).Value & "',N'" & DataGridViewBsal.Rows(i).Cells(10).Value & "',N'" & DataGridViewBsal.Rows(i).Cells(11).Value & "',N'" & DataGridViewBsal.Rows(i).Cells(12).Value & "',N'" & txtItemManufacturedQunt.Text & "',N'" & txtItemsMaterials.Text & "',N'" & txtPurchasesQunt.Text & "',N'" & txtSalesQunt.Text & "',N'" & txtItemManufactured.Text & "',N'" & txtItemsMaterials.Text & "',N'" & DataGridViewBsal.Rows(i).Cells(0).Value & "',N'" & Cls.C_date(DataGridViewBsal.Rows(i).Cells(7).Value) & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        For i As Integer = 0 To DataGridViewPaySal.Rows.Count - 1
            TotalCreditor += Val(DataGridViewPaySal.Rows(i).Cells(5).Value.ToString)
            S = "insert into PrintSalesPurchases (BILL_NO,Vendorname,Stat,qu,price,totalprice,bill_date,TotalCreditor,TotalDebtor,vintinval,valuereturns,VnPay,vndiscount,vnamntdebit,totalpricebeforedisc,totalpriceafterdisc,TotalBay,Totalstaying,vnamntcredit,Driv_Name,Driv_CarNumber,KiloMeter,Supervisor_Reform,Recipient,Received_Date,Number1,Number2)  values("
            S = S & "N'" & DataGridViewPaySal.Rows(i).Cells(0).Value & "',N'" & DataGridViewPaySal.Rows(i).Cells(1).Value & "',N'" & DataGridViewPaySal.Rows(i).Cells(2).Value & "',0,0,N'" & DataGridViewPaySal.Rows(i).Cells(3).Value & "',N'" & DataGridViewPaySal.Rows(i).Cells(4).Value & "',N'" & DataGridViewPaySal.Rows(i).Cells(5).Value & "',0,N'" & txtPurchases.Text & "',N'" & txtPurchasesReturns.Text & "',N'" & txtPayPurchases.Text & "',N'" & txtDsicPurchases.Text & "',N'" & txtExpenses.Text & "',N'" & txtSales.Text & "',N'" & txtSalesDisc.Text & "',N'" & txtSalesReturns.Text & "',N'" & txtSalesPay.Text & "',N'" & txtChecks_Payable.Text & "',N'" & txtItemManufacturedQunt.Text & "',N'" & txtItemsMaterials.Text & "',N'" & txtPurchasesQunt.Text & "',N'" & txtSalesQunt.Text & "',N'" & txtItemManufactured.Text & "',N'" & txtItemsMaterials.Text & "',N'" & DataGridViewPaySal.Rows(i).Cells(0).Value & "',N'" & Cls.C_date(DataGridViewPaySal.Rows(i).Cells(6).Value) & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridViewDiscSal.Rows.Count - 1
            TotalDebtor += Val(DataGridViewDiscSal.Rows(i).Cells(5).Value.ToString)
            S = "insert into PrintSalesPurchases (BILL_NO,Vendorname,Stat,qu,price,totalprice,bill_date,TotalDebtor,TotalCreditor,vintinval,valuereturns,VnPay,vndiscount,vnamntdebit,totalpricebeforedisc,totalpriceafterdisc,TotalBay,Totalstaying,vnamntcredit,Driv_Name,Driv_CarNumber,KiloMeter,Supervisor_Reform,Recipient,Received_Date,Number1,Number2)  values("
            S = S & "N'" & DataGridViewDiscSal.Rows(i).Cells(0).Value & "',N'" & DataGridViewDiscSal.Rows(i).Cells(1).Value & "',N'" & DataGridViewDiscSal.Rows(i).Cells(2).Value & "',0,0,N'" & DataGridViewDiscSal.Rows(i).Cells(3).Value & "',N'" & DataGridViewDiscSal.Rows(i).Cells(4).Value & "',N'" & DataGridViewDiscSal.Rows(i).Cells(5).Value & "',0,N'" & txtPurchases.Text & "',N'" & txtPurchasesReturns.Text & "',N'" & txtPayPurchases.Text & "',N'" & txtDsicPurchases.Text & "',N'" & txtExpenses.Text & "',N'" & txtSales.Text & "',N'" & txtSalesDisc.Text & "',N'" & txtSalesReturns.Text & "',N'" & txtSalesPay.Text & "',N'" & txtChecks_Payable.Text & "',N'" & txtItemManufacturedQunt.Text & "',N'" & txtItemsMaterials.Text & "',N'" & txtPurchasesQunt.Text & "',N'" & txtSalesQunt.Text & "',N'" & txtItemManufactured.Text & "',N'" & txtItemsMaterials.Text & "',N'" & DataGridViewDiscSal.Rows(i).Cells(0).Value & "',N'" & Cls.C_date(DataGridViewDiscSal.Rows(i).Cells(6).Value) & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridViewChecks_Payable.Rows.Count - 1
            TotalDebtor += Val(DataGridViewChecks_Payable.Rows(i).Cells(5).Value.ToString)
            S = "insert into PrintSalesPurchases (BILL_NO,Vendorname,Stat,qu,price,totalprice,bill_date,TotalDebtor,TotalCreditor,vintinval,valuereturns,VnPay,vndiscount,vnamntdebit,totalpricebeforedisc,totalpriceafterdisc,TotalBay,Totalstaying,vnamntcredit,Driv_Name,Driv_CarNumber,KiloMeter,Supervisor_Reform,Recipient,Received_Date,Number1,Number2)  values("
            S = S & "N'" & DataGridViewChecks_Payable.Rows(i).Cells(0).Value & "',N'" & DataGridViewChecks_Payable.Rows(i).Cells(1).Value & "',N'" & DataGridViewChecks_Payable.Rows(i).Cells(2).Value & "',0,0,N'" & DataGridViewChecks_Payable.Rows(i).Cells(3).Value & "',N'" & DataGridViewChecks_Payable.Rows(i).Cells(4).Value & "',N'" & DataGridViewChecks_Payable.Rows(i).Cells(5).Value & "',0,N'" & txtPurchases.Text & "',N'" & txtPurchasesReturns.Text & "',N'" & txtPayPurchases.Text & "',N'" & txtDsicPurchases.Text & "',N'" & txtExpenses.Text & "',N'" & txtSales.Text & "',N'" & txtSalesDisc.Text & "',N'" & txtSalesReturns.Text & "',N'" & txtSalesPay.Text & "',N'" & txtChecks_Payable.Text & "',N'" & txtItemManufacturedQunt.Text & "',N'" & txtItemsMaterials.Text & "',N'" & txtPurchasesQunt.Text & "',N'" & txtSalesQunt.Text & "',N'" & txtItemManufactured.Text & "',N'" & txtItemsMaterials.Text & "',N'" & DataGridViewChecks_Payable.Rows(i).Cells(0).Value & "',N'" & Cls.C_date(DataGridViewChecks_Payable.Rows(i).Cells(6).Value) & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update PrintSalesPurchases set TotalBeforeDisc =N'" & TotalCreditor & "',TotalDisc = N'" & TotalDebtor & "'" : cmd.ExecuteNonQuery()


        AddReportView()
        Dim rpt
        rpt = New rpt_DailyReportFactory
        Dim txt, txtNameAr, txtNameEn As TextObject

        If FilterSelect = "Number" Then
            Cls.Select_More_Data_Branch_Print_Orderby("PrintSalesPurchases", "*", "Number1")
        End If
        If FilterSelect = "Date" Then
            Cls.Select_More_Data_Branch_Print_Orderby("PrintSalesPurchases", "*", "Number2")
        End If
        If FilterSelect = "Name" Then
            Cls.Select_More_Data_Branch_Print_Orderby("PrintSalesPurchases", "*", "Vendorname")
        End If
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txt = rpt.Section1.ReportObjects("txtReportTitel")
        txt.Text = "كشف حساب التصنيع"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "كشف حساب التصنبع"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If

    End Sub

End Class