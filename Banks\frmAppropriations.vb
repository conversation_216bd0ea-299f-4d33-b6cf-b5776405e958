﻿Public Class frmAppropriations
    Dim Dt_AddBill As New DataTable
    Dim InvoiceNumber As Integer
    Private Sub frmAppropriations_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        'On Error Resume Next
        Cls.fill_combo("vendors", "Vendorname", cmbSupplier)
        'Cls.fill_combo("Items", "sname", cmbname)
        Cls.FillComboDataAdapterQuickSearch("Items", "sname", cmbname)

        Cls.fill_combo_Branch("stores", "store", cmbStores)
        FillComboBoxBank()
        If Action = "Edit" Then
            FillData()
            btnSave.Text = "تعديل"
        Else
            MAXRECORD()
            txtApprovalNumber.Focus()
        End If
    End Sub

    Private Sub FillComboBoxBank()
        cmbBank.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select distinct Bank from Banks"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            cmbBank.Items.Add(dr("Bank"))
        Loop
    End Sub

    Private Sub MAXRECORD()
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select * from LetterCredits"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Me.txtSeries.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(LOCID As float)) as mb FROM LetterCredits"
            dr = cmd.ExecuteReader
            dr.Read()
            Dim sh As Long
            sh = dr("mb")
            Me.txtSeries.Text = sh + 1
        End If
    End Sub

    Private Sub FillData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id AS [الباركود], itm_cat as [المجموعة] ,itm_name as [اسم الصنف],itm_Unity as [الوحدة],price as [السعر] ,qu as [الكمية] ,totalprice as [الأجمالي],Stores as [أسم المخزن] from LetterCreditsItems where ApprovalNumber =N'" & EditItmId & "'"
        dr = cmd.ExecuteReader
        Dgv_Add.DataSource = Cls.PopulateDataView(dr)
        DTV_Grid()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select ApprovalNumber,LOCID,LOCDate,Currency,Supplier,Bank,BankAddress,InvoiceNumber,PolicyNumber,TotalItems,ValueEgyptian,TotalValueEgyptian,BankCharges,ChargeCustoms,ChargeSendDocument,ChargeTransport,ExpenseEnvironment,OtherCharge,TotalAppearTax,SalesTax,TotalSalesTax,TotalAllSum from LetterCredits where ApprovalNumber =N'" & EditItmId & "'"
        dr = cmd.ExecuteReader : dr.Read()
        Dim XApprovalNumber, XLOCID, XLOCDate, XCurrency, XSupplier, XBank, XBankAddress, XInvoiceNumber, XPolicyNumber, XTotalItems, XValueEgyptian, XTotalValueEgyptian, XBankCharges, XChargeCustoms, XChargeSendDocument, XChargeTransport, XExpenseEnvironment, XOtherCharge, XTotalAppearTax, XSalesTax, XTotalSalesTax, XTotalAllSum As String
        XApprovalNumber = dr("ApprovalNumber")
        XLOCID = dr("LOCID")
        XLOCDate = dr("LOCDate")
        XCurrency = dr("Currency")
        XSupplier = dr("Supplier")
        XBank = dr("Bank")
        XBankAddress = dr("BankAddress")
        XInvoiceNumber = dr("InvoiceNumber")
        XPolicyNumber = dr("PolicyNumber")
        XTotalItems = dr("TotalItems")
        XValueEgyptian = dr("ValueEgyptian")
        XTotalValueEgyptian = dr("TotalValueEgyptian")
        XBankCharges = dr("BankCharges")
        XChargeCustoms = dr("ChargeCustoms")
        XChargeSendDocument = dr("ChargeSendDocument")
        XChargeTransport = dr("ChargeTransport")
        XExpenseEnvironment = dr("ExpenseEnvironment")
        XOtherCharge = dr("OtherCharge")
        XTotalAppearTax = dr("TotalAppearTax")
        XSalesTax = dr("SalesTax")
        XTotalSalesTax = dr("TotalSalesTax")
        XTotalAllSum = dr("TotalAllSum")


        txtApprovalNumber.Text = XApprovalNumber
        txtSeries.Text = XLOCID
        DtpDate.Text = XLOCDate
        cmbCurrency.Text = XCurrency
        cmbSupplier.Text = XSupplier
        cmbBank.Text = XBank
        txtAddressBank.Text = XBankAddress
        txtbillno.Text = XInvoiceNumber
        txtPolicy.Text = XPolicyNumber
        txtTotalItems.Text = XTotalItems
        txtValuEgyption.Text = XValueEgyptian
        txtTotalValuEgyption.Text = XTotalValueEgyptian
        txtChargeBank.Text = XBankCharges
        txtChargeCustoms.Text = XChargeCustoms
        txtChargeSendDocument.Text = XChargeSendDocument
        txtChargeTransport.Text = XChargeTransport
        txtEnvironment.Text = XExpenseEnvironment
        txtOtherCharge.Text = XOtherCharge
        txtTotalAppearTax.Text = XTotalAppearTax
        txtSalesTax.Text = XSalesTax
        txtTotalSalesTax.Text = XTotalSalesTax
        txtSumAllTotal.Text = XTotalAllSum

        InvoiceNumber = XInvoiceNumber
    End Sub

    Private Sub txtApprovalNumber_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtApprovalNumber.KeyUp
        If e.KeyCode = 13 Then
            DtpDate.Focus()
        End If
    End Sub

    Private Sub txtApprovalNumber_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtApprovalNumber.TextChanged
        MyVars.CheckNumber(txtApprovalNumber)
    End Sub

    Private Sub txtbillno_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtbillno.TextChanged
        MyVars.CheckNumber(txtbillno)
    End Sub

    Private Sub txtPolicy_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtPolicy.TextChanged
        MyVars.CheckNumber(txtPolicy)
    End Sub

    Private Sub txtqunt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtqunt.TextChanged
        MyVars.CheckNumber(txtqunt)
    End Sub

    Private Sub txtprice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtprice.TextChanged
        MyVars.CheckNumber(txtprice)
    End Sub

    Private Sub txtValuEgyption_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtValuEgyption.TextChanged
        MyVars.CheckNumber(txtValuEgyption)
        sumdisc()
    End Sub

    Private Sub txtChargeBank_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtChargeBank.TextChanged
        MyVars.CheckNumber(txtChargeBank)
        sumdisc()
    End Sub

    Private Sub txtChargeCustoms_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtChargeCustoms.TextChanged
        MyVars.CheckNumber(txtChargeCustoms)
        sumdisc()
    End Sub

    Private Sub txtChargeSendDocument_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtChargeSendDocument.TextChanged
        MyVars.CheckNumber(txtChargeSendDocument)
        sumdisc()
    End Sub

    Private Sub txtChargeTransport_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtChargeTransport.TextChanged
        MyVars.CheckNumber(txtChargeTransport)
        sumdisc()
    End Sub

    Private Sub txtEnvironment_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtEnvironment.TextChanged
        MyVars.CheckNumber(txtEnvironment)
        sumdisc()
    End Sub

    Private Sub txtOtherCharge_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtOtherCharge.TextChanged
        MyVars.CheckNumber(txtOtherCharge)
        sumdisc()
    End Sub

    Private Sub txtSalesTax_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSalesTax.TextChanged
        MyVars.CheckNumber(txtSalesTax)
        sumdisc()
    End Sub

    Private Sub DtpDate_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles DtpDate.KeyUp
        If e.KeyCode = 13 Then
            cmbCurrency.Focus()
        End If
    End Sub

    Private Sub cmbCurrency_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCurrency.KeyUp
        If e.KeyCode = 13 Then
            cmbSupplier.Focus()
        End If
    End Sub

    Private Sub cmbSupplier_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbSupplier.KeyUp
        If e.KeyCode = 13 Then
            cmbBank.Focus()
        End If
    End Sub

    Private Sub cmbBank_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbBank.KeyUp
        If e.KeyCode = 13 Then
            txtAddressBank.Focus()
        End If
    End Sub

    Private Sub txtAddressBank_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtAddressBank.KeyUp
        If e.KeyCode = 13 Then
            txtbillno.Focus()
        End If
    End Sub

    Private Sub txtbillno_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtbillno.KeyUp
        If e.KeyCode = 13 Then
            txtPolicy.Focus()
        End If
    End Sub

    Private Sub txtPolicy_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtPolicy.KeyUp
        If e.KeyCode = 13 Then
            cmbname.Focus()
        End If
    End Sub

    Private Sub cmbname_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbname.KeyUp
        If e.KeyCode = 13 Then
            GetDataImport()
        End If
    End Sub

    Private Sub txtqunt_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtqunt.KeyUp
        If e.KeyCode = 13 Then
            txtprice.Focus()
        End If
    End Sub

    Private Sub txtprice_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtprice.KeyUp
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub txtValuEgyption_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtValuEgyption.KeyUp
        If e.KeyCode = 13 Then
            txtChargeBank.Focus()
        End If
    End Sub

    Private Sub txtChargeBank_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtChargeBank.KeyUp
        If e.KeyCode = 13 Then
            txtChargeCustoms.Focus()
        End If
    End Sub

    Private Sub txtChargeCustoms_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtChargeCustoms.KeyUp
        If e.KeyCode = 13 Then
            txtChargeSendDocument.Focus()
        End If
    End Sub

    Private Sub txtChargeSendDocument_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtChargeSendDocument.KeyUp
        If e.KeyCode = 13 Then
            txtChargeTransport.Focus()
        End If
    End Sub

    Private Sub txtChargeTransport_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtChargeTransport.KeyUp
        If e.KeyCode = 13 Then
            txtEnvironment.Focus()
        End If
    End Sub

    Private Sub txtEnvironment_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtEnvironment.KeyUp
        If e.KeyCode = 13 Then
            txtOtherCharge.Focus()
        End If
    End Sub

    Private Sub txtOtherCharge_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtOtherCharge.KeyUp
        If e.KeyCode = 13 Then
            txtSalesTax.Focus()
        End If
    End Sub

    Private Sub txtSalesTax_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtSalesTax.KeyUp
        If e.KeyCode = 13 Then
            btnSave.PerformClick()
        End If
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub txtprc_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtprc.TextChanged
        MyVars.CheckNumber(txtprc)
    End Sub

    Private Sub BtnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAdd.Click
        If ValidateTextAdd() = False Then Exit Sub
        If Action = "Edit" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete from TmpBillsalData where itm_id =N'" & txtprc.Text.Trim & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into TmpBillsalData (bill_no,itm_id,itm_cat,itm_name,itm_Unity,price,qu,totalprice,Stores,UserName,bill_date)"
            S = S & " values (N'" & txtApprovalNumber.Text & "',N'" & txtprc.Text & "',N'" & cmbcats.Text.Trim & "',N'" & cmbname.Text.Trim & "',"
            S = S & "N'" & cmbUnity.Text.Trim & "',N'" & txtprice.Text & "'," & Val(txtqunt.Text) & "," & Val(txtprice.Text) * Val(txtqunt.Text) & ","
            S = S & "N'" & cmbStores.Text.Trim & "',N'" & UserName & "',N'" & Cls.C_date(DtpDate.Text) & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ITM_ID AS [الباركود], itm_cat as [المجموعة] ,itm_name as [اسم الصنف],itm_Unity as [الوحدة],price as [السعر] ,qu as [الكمية] ,totalprice as [الأجمالي],Stores as [أسم المخزن] from TmpBillsalData"
            dr = cmd.ExecuteReader
            Dgv_Add.DataSource = Cls.PopulateDataView(dr) : dr.Close()
        Else
            Dgv_Add.DataSource = Fn_AddBill(txtprc.Text, cmbcats.Text, cmbname.Text, cmbUnity.Text, txtprice.Text, txtqunt.Text, Val(txtprice.Text) * Val(txtqunt.Text), cmbStores.Text)
        End If
        DTV_Grid()
        ClearAdd()
        SumAllPrice()
    End Sub

    Friend Function Fn_AddBill(ByVal Col_Prc As String, ByVal Col_Cats As String, ByVal Col_Name As String, ByVal Col_Unity As String _
, ByVal Col_Price As Double, ByVal Col_Quant As Double, ByVal Col_Total As Double, ByVal Col_Stores As String) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("الباركود", GetType(String))
            Dt_AddBill.Columns.Add("المجموعة", GetType(String))
            Dt_AddBill.Columns.Add("الصنف", GetType(String))
            Dt_AddBill.Columns.Add("الوحدة", GetType(String))
            Dt_AddBill.Columns.Add("سعر الشراء", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية", GetType(Double))
            Dt_AddBill.Columns.Add("الإجمالي", GetType(Double))
            Dt_AddBill.Columns.Add("المخزن", GetType(String))
        End If

        DTV_Width()
        Dt_AddBill.Rows.Add(Col_Prc, Col_Cats, Col_Name, Col_Unity, Col_Price, Col_Quant, Col_Total, Col_Stores)
        Return Dt_AddBill
    End Function

    Private Sub ClearAdd()
        cmbcats.Text = ""
        cmbname.Text = ""
        cmbUnity.Text = ""
        txtprice.Text = ""
        txtqunt.Text = ""
        txtprc.Text = ""
        cmbStores.Text = ""
        cmbname.Focus()
    End Sub

    Private Sub SumAllPrice()
        Dim SM As Double
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            SM = SM + Dgv_Add.Rows(i).Cells(6).Value
        Next
        txtTotalItems.Text = SM
    End Sub

    Friend Sub DTV_Width()
        If Dgv_Add.Rows.Count > 0 Then
            DTV_Grid()
        End If
    End Sub

    Private Sub sumdisc()
        On Error Resume Next
        txtTotalValuEgyption.Text = Format(Val(txtTotalItems.Text) * Val(txtValuEgyption.Text), "Fixed")
        txtTotalSalesTax.Text = Format(Val(txtTotalValuEgyption.Text) * Val(txtSalesTax.Text) / 100, "Fixed")
        txtTotalAppearTax.Text = Format(Val(txtTotalValuEgyption.Text) + Val(txtChargeBank.Text) + Val(txtChargeCustoms.Text) + Val(txtChargeSendDocument.Text) + Val(txtChargeTransport.Text) + Val(txtEnvironment.Text) + Val(txtOtherCharge.Text), "Fixed")
        txtSumAllTotal.Text = Format(Val(txtTotalAppearTax.Text) + Val(txtTotalSalesTax.Text), "Fixed")
    End Sub

    Private Sub DTV_Grid()
        Dgv_Add.Columns(0).Width = 70
        Dgv_Add.Columns(1).Width = 90
        Dgv_Add.Columns(2).Width = 130
        Dgv_Add.Columns(3).Width = 60
        Dgv_Add.Columns(4).Width = 60
        Dgv_Add.Columns(5).Width = 75
        Dgv_Add.Columns(6).Width = 70
        Dgv_Add.Columns(7).Width = 70
    End Sub

    Function ValidateTextAdd() As Boolean
        If txtApprovalNumber.Text = "" Then MsgBox("فضلا أدخل رقم الاعتماد", MsgBoxStyle.Exclamation) : txtApprovalNumber.Focus() : Return False
        If cmbCurrency.Text = "" Then MsgBox("فضلا ادخل العملة", MsgBoxStyle.Exclamation) : cmbCurrency.Focus() : Return False
        If cmbSupplier.Text = "" Then MsgBox("فضلا ادخل أسم المورد", MsgBoxStyle.Exclamation) : cmbSupplier.Focus() : Return False
        If cmbBank.Text = "" Then MsgBox("فضلا ادخل أسم البنك", MsgBoxStyle.Exclamation) : cmbBank.Focus() : Return False
        If txtbillno.Text = "" Then MsgBox("فضلا ادخل رقم الفاتورة", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from LetterCredits where ApprovalNumber =N'" & txtApprovalNumber.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H > 0 Then
            If Action = "Edit" Then
                If txtApprovalNumber.Text.Trim <> EditItmId Then
                    MsgBox("رقم الاعتماد مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtApprovalNumber.Focus() : Return False
                End If
            Else
                MsgBox("رقم الاعتماد مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtApprovalNumber.Focus() : Return False
            End If
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from LetterCredits where InvoiceNumber =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H > 0 Then
            If Action = "Edit" Then
                If txtbillno.Text.Trim <> InvoiceNumber Then
                    MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
                End If
            Else
                MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
            End If
        End If

        If Action <> "Edit" Then
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                If Dgv_Add.Rows(i).Cells(0).Value = txtprc.Text.Trim Then MsgBox("صنف مكرر بنفس الفاتورة", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
            Next
     End If
        Return True
    End Function

    Private Sub txtTotalItems_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTotalItems.TextChanged
        sumdisc()
    End Sub

    Private Sub txtTotalValuEgyption_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTotalValuEgyption.TextChanged
        sumdisc()
    End Sub

    Private Sub txtTotalAppearTax_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTotalAppearTax.TextChanged
        sumdisc()
    End Sub

    Private Sub txtTotalSalesTax_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTotalSalesTax.TextChanged
        sumdisc()
    End Sub

    Private Sub txtSumAllTotal_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSumAllTotal.TextChanged
        sumdisc()
    End Sub

    Private Sub GetDataImport()
        Bol = True

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id,group_name,Unity,Stores,tinPrice,SalPrice from items where sname=N'" & cmbname.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then dr.Close() : Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            txtprc.Text = dr(0)
        End If
        If dr(1) Is DBNull.Value Then
        Else
            cmbcats.Text = dr(1)
        End If
        If dr(2) Is DBNull.Value Then
        Else
            cmbUnity.Text = dr(2)
        End If
        If dr(3) Is DBNull.Value Then
        Else
            cmbStores.Text = dr(3)
        End If
        If dr(4) Is DBNull.Value Then
            dr.Close() : txtprice.Text = 0
        Else
            txtprice.Text = dr(4)
        End If


        txtqunt.Focus()
        txtqunt.SelectAll()
        txtqunt.Text = 1

        Bol = False
    End Sub

    Private Sub cmbname_MouseClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles cmbname.MouseClick
        GetDataImport()
    End Sub

    Private Sub cmbname_DropDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbname.DropDown
        cmbcats.Text = ""
        cmbname.Text = ""
        cmbUnity.Text = ""
        txtprice.Text = ""
        txtqunt.Text = ""
        txtprc.Text = ""
        cmbStores.Text = ""
    End Sub

    Private Sub txtprc_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtprc.KeyUp
        If e.KeyCode = 13 Then
            Bol = True
            If txtprc.Text.Trim = "" Then Exit Sub
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select group_name , sname ,Unity,tinPrice,Stores from items itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                cmbcats.Text = dr(0).ToString
                cmbname.Text = dr(1).ToString
                cmbUnity.Text = dr(2).ToString
                If dr(3) Is DBNull.Value Then
                    txtprice.Text = 0
                    GoTo 1
                End If
                txtprice.Text = dr(3).ToString
                cmbStores.Text = dr(4).ToString
            End If
1:
            txtqunt.Focus()
            txtqunt.SelectAll()
            txtqunt.Text = 1
            Bol = False
        End If
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        If ValidateTextSave() = False Then Exit Sub

        If Action = "Edit" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete From  LetterCredits where ApprovalNumber =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  LetterCreditsItems where ApprovalNumber =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
        Else
            Dim x As String = MsgBox("هل تريد بالفعل إتمام عملية الاعتمادات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
            If x = vbCancel Then Exit Sub
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into LetterCredits(LOCID,ApprovalNumber,LOCDate,Currency,Supplier,Bank,BankAddress,InvoiceNumber,PolicyNumber,TotalItems,ValueEgyptian,TotalValueEgyptian,BankCharges,ChargeCustoms,ChargeSendDocument,ChargeTransport,ExpenseEnvironment,OtherCharge,TotalAppearTax,SalesTax,TotalSalesTax,TotalAllSum,UserName) values ("
        S = S & "N'" & txtSeries.Text.Trim & "' ,N'" & txtApprovalNumber.Text.Trim & "' ,N'" & Cls.C_date(DtpDate.Text) & "',"
        S = S & "N'" & cmbCurrency.Text.Trim & "',N'" & cmbSupplier.Text.Trim & "',N'" & cmbBank.Text.Trim & "',N'" & txtAddressBank.Text & "',"
        S = S & "N'" & txtbillno.Text & "',N'" & txtPolicy.Text & "',N'" & txtTotalItems.Text & "',N'" & txtValuEgyption.Text & "',"
        S = S & "N'" & txtTotalValuEgyption.Text & "',N'" & txtChargeBank.Text & "',N'" & txtChargeCustoms.Text & "',N'" & txtChargeSendDocument.Text & "',"
        S = S & "N'" & txtChargeTransport.Text & "',N'" & txtEnvironment.Text & "',N'" & txtOtherCharge.Text & "',N'" & txtTotalAppearTax.Text & "',"
        S = S & "N'" & txtSalesTax.Text & "',N'" & txtTotalSalesTax.Text & "',N'" & txtSumAllTotal.Text & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            S = "insert into LetterCreditsItems(ApprovalNumber,bill_no,itm_id,itm_cat,itm_name,itm_Unity,price,qu,totalprice,Stores,bill_date,username) values ("
            S = S & "N'" & txtApprovalNumber.Text.Trim & "',N'" & txtbillno.Text.Trim & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',"
            S = S & "N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Dgv_Add.Rows(i).Cells(3).Value & "',"
            S = S & "N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',"
            S = S & "N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & Cls.C_date(DtpDate.Text) & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        If Action = "Edit" Then
            Action = "Add"
            Me.Close()
            frmShowAppropriations.btnShow.PerformClick()
        Else
            MsgBox("تمت عملية الحفظ بنجاح", MsgBoxStyle.Information)
            ClearSave()
            MAXRECORD()
            txtApprovalNumber.Focus()
        End If
        If chkprint.Checked = True Then
            PrintReport()
        End If
    End Sub

    Private Sub PrintReport()

    End Sub

    Function ValidateTextSave() As Boolean
        If txtApprovalNumber.Text = "" Then MsgBox("فضلا أدخل رقم الاعتماد", MsgBoxStyle.Exclamation) : txtApprovalNumber.Focus() : Return False
        If cmbCurrency.Text = "" Then MsgBox("فضلا ادخل العملة", MsgBoxStyle.Exclamation) : cmbCurrency.Focus() : Return False
        If cmbSupplier.Text = "" Then MsgBox("فضلا ادخل أسم المورد", MsgBoxStyle.Exclamation) : cmbSupplier.Focus() : Return False
        If cmbBank.Text = "" Then MsgBox("فضلا ادخل أسم البنك", MsgBoxStyle.Exclamation) : cmbBank.Focus() : Return False
        If txtbillno.Text = "" Then MsgBox("فضلا ادخل رقم الفاتورة", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        If txtValuEgyption.Text = "" Then MsgBox("فضلا ادخل القيمة بالمصرى", MsgBoxStyle.Exclamation) : txtValuEgyption.Focus() : Return False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from LetterCredits where ApprovalNumber =N'" & txtApprovalNumber.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H > 0 Then
            If Action = "Edit" Then
                If txtApprovalNumber.Text.Trim <> EditItmId Then
                    MsgBox("رقم الاعتماد مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtApprovalNumber.Focus() : Return False
                End If
            Else
                MsgBox("رقم الاعتماد مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtApprovalNumber.Focus() : Return False
            End If
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from LetterCredits where InvoiceNumber =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H > 0 Then
            If Action = "Edit" Then
                If txtbillno.Text.Trim <> InvoiceNumber Then
                    MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
                End If
            Else
                MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
            End If
        End If
        Return True
    End Function

    Private Sub ClearSave()
        Cls.clear(Me)
        Dt_AddBill.Rows.Clear()
    End Sub

    Private Sub txtSeries_TextChanged(sender As Object, e As EventArgs) Handles txtSeries.TextChanged
        MyVars.CheckNumber(txtSeries)
    End Sub
End Class