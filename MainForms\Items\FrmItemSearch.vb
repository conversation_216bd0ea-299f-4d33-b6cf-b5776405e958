﻿Imports System.IO
Imports CrystalDecisions.CrystalReports.Engine
Imports Microsoft.Office.Interop
Public Class FrmItemSearch
    Dim WithEvents BS As New BindingSource
    Dim StoreCarton As String
    Dim ActiveForm As Boolean = False
    Dim GridTinPriceAverage As String = ""
    Dim SortItems As String = ""
    Dim ParcodeUnity As String = ""

    Private Sub FrmItemSearch_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ActivateFormatNumberWithSeparators = "YES" Then
            If LastTinPriceItems = "YES" Then
                GridTinPriceAverage = "CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء]"
            Else
                GridTinPriceAverage = "CAST(TinPriceAverage AS NVARCHAR(50)) as [متوسط سعر الشراء]"
            End If
        Else
            If LastTinPriceItems = "YES" Then
                GridTinPriceAverage = "TinPrice as [سعر الشراء]"
            Else
                GridTinPriceAverage = "TinPriceAverage as [متوسط سعر الشراء]"
            End If
        End If

        txtsearsh.Text = " بحث ...." : txtsearsh.Font = New Font(txtsearsh.Font, FontStyle.Italic)
        txtsearsh.Focus()
        Bra.Fil("groups", "g_name", cmbcats)
        Cls.fill_combo_Branch("stores", "store", cmbStores)
        Cls.fill_combo("vendors", "Vendorname", cmbvendores)
        Headerx()
        DTGVColumnsWidth()
        txtsearsh.Focus()
        PanelAdvancedSearch.Top = 10000
        PanelPrint.Top = 10000
        PanelBarcodeMore.Visible = False
        GroupBranch()

        If PermtionName <> "مدير" Then
            txtNumberItems.Visible = False
            txtTotalQunt.Visible = False
            txt_Total.Visible = False
            txtWholePrice.Visible = False
            txtSalPrice.Visible = False
            txtStoreCarton.Visible = False
            Label2.Visible = False
            Label7.Visible = False
            LblTotal.Visible = False
            Label3.Visible = False
            Label9.Visible = False
            Label5.Visible = False
        End If


    End Sub

    Private Sub DTGVColumnsWidth()
        DTGV.Columns(0).Width = 10
        DTGV.Columns(1).Width = 90
        DTGV.Columns(2).Width = 90
        DTGV.Columns(3).Width = 90
        DTGV.Columns(4).Width = 250
        DTGV.Columns(5).Width = 100
        DTGV.Columns(6).Width = 75
        DTGV.Columns(7).Width = 75
        DTGV.Columns(8).Width = 75
        DTGV.Columns(9).Width = 100
    End Sub
    Private Sub Headerx()
        Try
            If rdoSortParcode.Checked = True Then : SortItems = "itm_id" : End If
            If rdoSortgroup_name.Checked = True Then : SortItems = "group_name" : End If
            If rdoSortItemsName.Checked = True Then : SortItems = "sname" : End If
            If rdoSortID.Checked = True Then : SortItems = "id" : End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If ActivateFormatNumberWithSeparators = "YES" Then
                S = Cls.Get_Select_Grid_S_OrderBy_Name("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الاسم]," + GridTinPriceAverage + ",CAST(SalPrice AS NVARCHAR(50)) as [سعر البيع],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],rng as [الحد الأدنى] ,CAST(store AS NVARCHAR(50)) as [المخزون],StoreCarton as [المخزون بالكرتونة],Stores as [اسم المخزن],RateVAT as [الضريبة],Unity as [الوحدة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة]", "items", "id<>''", SortItems)
            Else
                S = Cls.Get_Select_Grid_S_OrderBy_Name("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الاسم]," + GridTinPriceAverage + ",SalPrice as [سعر البيع],WholePrice as [سعر الجملة],rng as [الحد الأدنى] ,store as [المخزون],StoreCarton as [المخزون بالكرتونة],Stores as [اسم المخزن],RateVAT as [الضريبة],Unity as [الوحدة],WholeWholePrice as [سعر جملة الجملة]", "items", "id<>''", SortItems)
            End If
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)

            GetSearch()

            GetFormatNumberWithSeparators()

        Catch ex As Exception
        End Try
    End Sub

    Private Sub txtsearsh_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles txtsearsh.MouseClick
        If txtsearsh.Text = " بحث ...." Then txtsearsh.SelectAll() : txtsearsh.Font = New Font(txtsearsh.Font, FontStyle.Regular)

    End Sub

    Private Sub txtsearsh_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtsearsh.TextChanged
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        GetUP_ParcodeItemsUnity()

        If rdoSortParcode.Checked = True Then : SortItems = "itm_id" : End If
        If rdoSortgroup_name.Checked = True Then : SortItems = "group_name" : End If
        If rdoSortItemsName.Checked = True Then : SortItems = "sname" : End If
        If rdoSortID.Checked = True Then : SortItems = "id" : End If


        ParcodeMore = ""
        GetBarcodeMore(txtsearsh.Text) : If ParcodeMore <> "" Then : txtsearsh.Text = ParcodeMore : End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If ActivateFormatNumberWithSeparators = "YES" Then
            If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
                S = Cls.Get_Select_Grid_S_OrderBy_Name("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الاسم]," + GridTinPriceAverage + ",CAST(SalPrice AS NVARCHAR(50)) as [سعر البيع],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],rng as [الحد الأدنى] ,CAST(store AS NVARCHAR(50)) as [المخزون],StoreCarton as [المخزون بالكرتونة],Stores as [اسم المخزن],RateVAT as [الضريبة],Unity as [الوحدة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة]", "items", "id<>''", SortItems)
            Else
                If rdoItemsName.Checked = True Then
                    S = Cls.Get_Select_Grid_S_OrderBy_Name("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة] , group_branch as [مجموعة فرعية],sname as [الاسم]," + GridTinPriceAverage + ",CAST(SalPrice AS NVARCHAR(50)) as [سعر البيع],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],rng as [الحد الأدنى] ,CAST(store AS NVARCHAR(50)) as [المخزون],StoreCarton as [المخزون بالكرتونة],Stores as [اسم المخزن],RateVAT as [الضريبة],Unity as [الوحدة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة]", "items", "sname Like N'%" & txtsearsh.Text & "%'", SortItems)
                End If
                If rdoParcode.Checked = True Then
                    S = Cls.Get_Select_Grid_S_OrderBy_Name("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة] , group_branch as [مجموعة فرعية],sname as [الاسم]," + GridTinPriceAverage + ",CAST(SalPrice AS NVARCHAR(50)) as [سعر البيع],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],rng as [الحد الأدنى] ,CAST(store AS NVARCHAR(50)) as [المخزون],StoreCarton as [المخزون بالكرتونة],Stores as [اسم المخزن],RateVAT as [الضريبة],Unity as [الوحدة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة]", "items", "itm_id =N'" & txtsearsh.Text & "'", SortItems)
                End If
            End If
        Else
            If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
                S = Cls.Get_Select_Grid_S_OrderBy_Name("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الاسم]," + GridTinPriceAverage + ",SalPrice as [سعر البيع],WholePrice as [سعر الجملة],rng as [الحد الأدنى] ,store as [المخزون],StoreCarton as [المخزون بالكرتونة],Stores as [اسم المخزن],RateVAT as [الضريبة],Unity as [الوحدة],WholeWholePrice as [سعر جملة الجملة]", "items", "id<>''", SortItems)
            Else
                If rdoItemsName.Checked = True Then
                    S = Cls.Get_Select_Grid_S_OrderBy_Name("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة] , group_branch as [مجموعة فرعية],sname as [الاسم]," + GridTinPriceAverage + ",SalPrice as [سعر البيع],WholePrice as [سعر الجملة],rng as [الحد الأدنى] ,store as [المخزون],StoreCarton as [المخزون بالكرتونة],Stores as [اسم المخزن],RateVAT as [الضريبة],Unity as [الوحدة],WholeWholePrice as [سعر جملة الجملة]", "items", "sname Like N'%" & txtsearsh.Text & "%'", SortItems)
                End If
                If rdoParcode.Checked = True Then
                    S = Cls.Get_Select_Grid_S_OrderBy_Name("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة] , group_branch as [مجموعة فرعية],sname as [الاسم]," + GridTinPriceAverage + ",SalPrice as [سعر البيع],WholePrice as [سعر الجملة],rng as [الحد الأدنى] ,store as [المخزون],StoreCarton as [المخزون بالكرتونة],Stores as [اسم المخزن],RateVAT as [الضريبة],Unity as [الوحدة],WholeWholePrice as [سعر جملة الجملة]", "items", "itm_id =N'" & txtsearsh.Text & "'", SortItems)
                End If
            End If
        End If


        cmd.CommandText = S : dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)

        GetSearch()

        GetFormatNumberWithSeparators()
    End Sub

    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DTGV.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintAllItems")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            S = "insert into PrintAllItems(Company_Branch_ID,itm_id,group_name,sname,TinPrice,SalPrice,rng,qunt,Stores,tinpricetotal)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DTGV.Rows(i).Cells(1).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(5).Value & "',N'" & DTGV.Rows(i).Cells(6).Value & "',N'" & DTGV.Rows(i).Cells(7).Value & "',N'" & DTGV.Rows(i).Cells(9).Value & "',N'" & DTGV.Rows(i).Cells(11).Value & "',N'" & txt_Total.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New rptItemsPriceSales

        Cls.Select_More_Data_Branch_Print_Orderby("PrintAllItems", "*", SortItems)
        Dim dt As New DataTable
        dt.Load(dr)

        rpt.SetDataSource(dt)


        Dim txtname, txtNameAr, txtNameEn As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير بالاصناف"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()
        Frm_PrintReports.Text = "تقرير بالاصناف"

        If RunDatabaseInternet = "YES" Then : connect() : End If

    End Sub

    Sub InsertForPrint(ByVal itm_id As String, ByVal group_name As String, ByVal sname As String, ByVal Unity As String, ByVal rng As String,
                        ByVal qunt As String, ByVal ValStore As String)
        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "PrintCarriedOutItems"
        cmd.Parameters.Clear()

        cmd.Parameters.AddWithValue("@itm_id", itm_id)
        cmd.Parameters.AddWithValue("@group_name", group_name)
        cmd.Parameters.AddWithValue("@sname", sname)
        cmd.Parameters.AddWithValue("@Unity", Unity)
        cmd.Parameters.AddWithValue("@rng", rng)
        cmd.Parameters.AddWithValue("@qunt", qunt)
        cmd.Parameters.AddWithValue("@ValStore", ValStore)
        cmd.ExecuteNonQuery()

    End Sub

    Private Sub btnClose_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClose.Click
        PanelAdvancedSearch.Top = 10000
    End Sub

    Private Sub cmbcats_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbcats.SelectedIndexChanged
        GetAdvancedSearch()
    End Sub

    Private Sub cmbGroup_Branch_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbGroup_Branch.SelectedIndexChanged
        GetAdvancedSearch()
    End Sub

    Private Sub cmbStores_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbStores.SelectedIndexChanged
        GetAdvancedSearch()
    End Sub

    Private Sub btnAdvancedSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAdvancedSearch.Click
        PanelAdvancedSearch.Top = 60
        PanelAdvancedSearch.Left = 123
    End Sub

    Private Sub btnPriceList_Click(sender As Object, e As EventArgs) Handles btnPriceList.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DTGV.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintAllItems")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            S = "insert into PrintAllItems(Company_Branch_ID,itm_id,group_name,sname,SalPrice,tinpricetotal)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DTGV.Rows(i).Cells(1).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(6).Value & "',N'" & txt_Total.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New rptItemsPriceList

        Cls.Select_More_Data_Branch_Print_Orderby("PrintAllItems", "*", SortItems)
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        Dim txtname, txtNameAr, txtNameEn, txtCmpAddress, txtCmpEmail, txtCmpTel, txtCmpMobile, txtCommercialRecord, txtTaxCard As TextObject

        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "أسعار سعر البيع لللاصناف"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        txtCmpAddress = rpt.Section1.ReportObjects("txtAddress")
        txtCmpAddress.Text = CmpAddress
        txtCmpEmail = rpt.Section1.ReportObjects("txtEmail")
        txtCmpEmail.Text = CmpEmail
        txtCmpTel = rpt.Section1.ReportObjects("txtTel")
        txtCmpTel.Text = CmpTel
        txtCmpMobile = rpt.Section1.ReportObjects("txtMobile")
        txtCmpMobile.Text = CmpMobile
        txtCommercialRecord = rpt.Section1.ReportObjects("txtCommercialRecord")
        txtCommercialRecord.Text = CMPCommercialRecord
        txtTaxCard = rpt.Section1.ReportObjects("txtTaxCard")
        txtTaxCard.Text = CMPTaxCard
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()
        Frm_PrintReports.Text = "أسعار سعر البيع لللاصناف"

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub btnPrintPriceSalqu_Click(sender As Object, e As EventArgs) Handles btnPrintPriceSalqu.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DTGV.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintAllItems")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            S = "insert into PrintAllItems(Company_Branch_ID,itm_id,group_name,sname,SalPrice,qunt,Stores,tinpricetotal)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DTGV.Rows(i).Cells(1).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(6).Value & "',N'" & DTGV.Rows(i).Cells(9).Value & "',N'" & DTGV.Rows(i).Cells(11).Value & "',N'" & txt_Total.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New rptItemsPriceSalesQunt

        Cls.Select_More_Data_Branch_Print_Orderby("PrintAllItems", "*", SortItems)
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        Dim txtname, txtNameAr, txtNameEn As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير أسعار البيع والكميات"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()
        Frm_PrintReports.Text = "تقرير أسعار البيع والكميات"

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub btnPrintqunt_Click(sender As Object, e As EventArgs) Handles btnPrintqunt.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DTGV.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintAllItems")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            S = "insert into PrintAllItems(Company_Branch_ID,itm_id,group_name,sname,qunt,Stores,tinpricetotal)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DTGV.Rows(i).Cells(1).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(9).Value & "',N'" & DTGV.Rows(i).Cells(11).Value & "',N'" & txtTotalQunt.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New rptItemsQunt

        Cls.Select_More_Data_Branch_Print_Orderby("PrintAllItems", "*", SortItems)
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        Dim txtname, txtNameAr, txtNameEn As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير كميات الاصناف"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()
        Frm_PrintReports.Text = "تقرير كميات الاصناف"

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub GroupBranch()
        Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")

        If ShowGroupBranch = "NO" Then
            cmbGroup_Branch.Visible = False
            Label16.Visible = False
        Else
            cmbGroup_Branch.Visible = True
            Label16.Visible = True
        End If
    End Sub

    Private Sub btnPrintWholePrice_Click(sender As Object, e As EventArgs) Handles btnPrintWholePrice.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DTGV.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintAllItems")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            S = "insert into PrintAllItems(Company_Branch_ID,itm_id,group_name,sname,SalPrice,tinpricetotal)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DTGV.Rows(i).Cells(1).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(7).Value & "',N'" & txtWholePrice.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New rptItemsPriceList

        Cls.Select_More_Data_Branch_Print_Orderby("PrintAllItems", "*", SortItems)
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)

        Dim txtname, txtNameAr, txtNameEn, txtWholePriceX, txtCmpAddress, txtCmpEmail, txtCmpTel, txtCmpMobile, txtCommercialRecord, txtTaxCard As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "أسعار سعر الجملة لللاصناف"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        txtCmpAddress = rpt.Section1.ReportObjects("txtAddress")
        txtCmpAddress.Text = CmpAddress
        txtCmpEmail = rpt.Section1.ReportObjects("txtEmail")
        txtCmpEmail.Text = CmpEmail
        txtCmpTel = rpt.Section1.ReportObjects("txtTel")
        txtCmpTel.Text = CmpTel
        txtCmpMobile = rpt.Section1.ReportObjects("txtMobile")
        txtCmpMobile.Text = CmpMobile
        txtCommercialRecord = rpt.Section1.ReportObjects("txtCommercialRecord")
        txtCommercialRecord.Text = CMPCommercialRecord
        txtTaxCard = rpt.Section1.ReportObjects("txtTaxCard")
        txtTaxCard.Text = CMPTaxCard
        txtWholePriceX = rpt.Section1.ReportObjects("Text5")
        txtWholePriceX.Text = "سعر الجملة"

        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()
        Frm_PrintReports.Text = "أسعار سعر الجملة لللاصناف"

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub btnClosePrint_Click(sender As Object, e As EventArgs) Handles btnClosePrint.Click
        PanelPrint.Top = 10000
    End Sub

    Private Sub btnPrintView_Click(sender As Object, e As EventArgs) Handles btnPrintView.Click
        
        If SettingPrinterAuto = "YES" Then
            Dim DefaultPrinterBill As String = mykey.GetValue("DefaultPrinterA4", "DefaultPrinterA4")
            If Not Cls.SetDefaultSystemPrinter4(DefaultPrinterBill) Then
                MessageBox.Show("Error occured will trying to set the default printer!")
            End If
        End If

        PanelPrint.Top = 177

    End Sub

    Private Sub GetSearch()
        Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
        If ShowGroupBranch = "NO" Then
            DTGV.Columns(3).Visible = False
        Else
            DTGV.Columns(3).Visible = True
        End If

        If PermtionName = "مدير" Then
            DTGV.Columns(5).Visible = True
            DTGV.Columns(7).Visible = True
            DTGV.Columns(14).Visible = True
        Else
            DTGV.Columns(5).Visible = False
            DTGV.Columns(7).Visible = True
            DTGV.Columns(14).Visible = True
        End If
        If ShowValueVAT = "NO" Then
            DTGV.Columns(13).Visible = False
        End If

        Dim SM As Double
        For i As Integer = 0 To DTGV.Rows.Count - 1
            SM = SM + Val(DTGV.Rows(i).Cells(5).Value.ToString) * Val(DTGV.Rows(i).Cells(9).Value)
        Next
        txt_Total.Text = FormatNumberWithSeparators(SM)

        Dim SM2 As Double
        For i As Integer = 0 To DTGV.Rows.Count - 1
            SM2 = SM2 + Val(DTGV.Rows(i).Cells(7).Value.ToString) * Val(DTGV.Rows(i).Cells(9).Value)
        Next
        txtWholePrice.Text = FormatNumberWithSeparators(SM2)

        Dim SM4 As Double
        For i As Integer = 0 To DTGV.Rows.Count - 1
            SM4 = SM4 + Val(DTGV.Rows(i).Cells(6).Value.ToString) * Val(DTGV.Rows(i).Cells(9).Value)
        Next
        txtSalPrice.Text = FormatNumberWithSeparators(SM4)


        Dim SM1 As Double
        For i As Integer = 0 To DTGV.Rows.Count - 1
            SM1 = SM1 + Val(DTGV.Rows(i).Cells(9).Value)
        Next
        txtTotalQunt.Text = FormatNumberWithSeparators(SM1)

        Dim SM3 As Double
        For i As Integer = 0 To DTGV.Rows.Count - 1
            SM3 = SM3 + Val(DTGV.Rows(i).Cells(10).Value.ToString)
        Next
        txtStoreCarton.Text = FormatNumberWithSeparators(SM3)

        txtNumberItems.Text = DTGV.RowCount

        DTGV.Columns(0).Visible = False
    End Sub

    Private Sub rdoItemsName_CheckedChanged(sender As Object, e As EventArgs) Handles rdoItemsName.CheckedChanged
        txtsearsh.Focus()
    End Sub

    Private Sub rdoParcode_CheckedChanged(sender As Object, e As EventArgs) Handles rdoParcode.CheckedChanged
        txtsearsh.Focus()
    End Sub

    Private Sub cmbvendores_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbvendores.SelectedIndexChanged
        GetAdvancedSearch()
    End Sub

    Private Sub DTGV_DoubleClick(sender As Object, e As EventArgs) Handles DTGV.DoubleClick
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim ItmID As String

        ItmID = DTGV.SelectedRows(0).Cells(1).Value
        PanelBarcodeMore.Visible = True

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id_More as [الباركود] from BarcodeMore where itm_id =N'" & ItmID & "'"
        dr = cmd.ExecuteReader
        dgvBarcodeMore.DataSource = Cls.PopulateDataView(dr)
        txtNumberBarcodeMore.Text = dgvBarcodeMore.RowCount
    End Sub

    Private Sub btnCloseBarcodeMore_Click(sender As Object, e As EventArgs) Handles btnCloseBarcodeMore.Click
        PanelBarcodeMore.Visible = False
    End Sub

    Private Sub btnPrint2_Click(sender As Object, e As EventArgs) Handles btnPrint2.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DTGV.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintAllItems")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            S = "insert into PrintAllItems(Company_Branch_ID,itm_id,group_name,tin,sname,TinPrice,SalPrice,btin,qunt,Valu_Store,Stores,tinpricetotal,Valu_purch,bsalpricetotal,btinpricetotal,salpricetotal,decayedpricetotal,Unity)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DTGV.Rows(i).Cells(1).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(3).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(5).Value & "',N'" & DTGV.Rows(i).Cells(6).Value & "',N'" & DTGV.Rows(i).Cells(7).Value & "',N'" & DTGV.Rows(i).Cells(9).Value & "',N'" & DTGV.Rows(i).Cells(10).Value & "',N'" & DTGV.Rows(i).Cells(11).Value & "',N'" & txt_Total.Text & "',N'" & txtNumberItems.Text & "',N'" & txtTotalQunt.Text & "',N'" & txtWholePrice.Text & "',N'" & txtSalPrice.Text & "',N'" & txtStoreCarton.Text & "',N'" & DTGV.Rows(i).Cells(13).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New rptItemsPriceSalesALL

        Cls.Select_More_Data_Branch_Print_Orderby("PrintAllItems", "*", SortItems)

        Dim dt As New DataTable
        dt.Load(dr)

        rpt.SetDataSource(dt)


        Dim txtname, txtNameAr, txtNameEn As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير بكل الاصناف"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()
        Frm_PrintReports.Text = "تقرير بكل الاصناف"

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub rdoSortParcode_CheckedChanged(sender As Object, e As EventArgs) Handles rdoSortParcode.CheckedChanged
        If ActiveForm = True Then
            txtsearsh_TextChanged(sender, e)
        End If
        ActiveForm = True
    End Sub

    Private Sub rdoSortgroup_name_CheckedChanged(sender As Object, e As EventArgs) Handles rdoSortgroup_name.CheckedChanged
        If ActiveForm = True Then
            txtsearsh_TextChanged(sender, e)
        End If
        ActiveForm = True
    End Sub

    Private Sub rdoSortItemsName_CheckedChanged(sender As Object, e As EventArgs) Handles rdoSortItemsName.CheckedChanged
        If ActiveForm = True Then
            txtsearsh_TextChanged(sender, e)
        End If
        ActiveForm = True
    End Sub

    Private Sub btnPrint3_Click(sender As Object, e As EventArgs) Handles btnPrint3.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DTGV.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintAllItems")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            S = "insert into PrintAllItems(Company_Branch_ID,itm_id,group_name,tin,sname,TinPrice,SalPrice,btin,qunt,Valu_Store,Stores,tinpricetotal,Valu_purch,bsalpricetotal,btinpricetotal,salpricetotal,decayedpricetotal,ValStore)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DTGV.Rows(i).Cells(1).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(3).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(5).Value & "',N'" & DTGV.Rows(i).Cells(6).Value & "',N'" & DTGV.Rows(i).Cells(7).Value & "',N'" & DTGV.Rows(i).Cells(9).Value & "',N'" & DTGV.Rows(i).Cells(10).Value & "',N'" & DTGV.Rows(i).Cells(11).Value & "',N'" & txt_Total.Text & "',N'" & txtNumberItems.Text & "',N'" & txtTotalQunt.Text & "',N'" & txtWholePrice.Text & "',N'" & txtSalPrice.Text & "',N'" & txtStoreCarton.Text & "',N'" & DTGV.Rows(i).Cells(12).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New rptItemsPriceSalesALLVAT

        Cls.Select_More_Data_Branch_Print_Orderby("PrintAllItems", "*", SortItems)

        Dim dt As New DataTable
        dt.Load(dr)

        rpt.SetDataSource(dt)


        Dim txtname, txtNameAr, txtNameEn As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير بكل الاصناف"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()
        Frm_PrintReports.Text = "تقرير بكل الاصناف"

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub btnExportExcel_Click(sender As Object, e As EventArgs) Handles btnExportExcel.Click
        Dim rowsTotal, colsTotal As Short
        Dim I, j, iC As Short
        System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.WaitCursor
        Dim xlApp As New Excel.Application
        Try
            Dim excelBook As Excel.Workbook = xlApp.Workbooks.Add
            Dim excelWorksheet As Excel.Worksheet = CType(excelBook.Worksheets(1), Excel.Worksheet)
            xlApp.Visible = True

            rowsTotal = DTGV.RowCount
            colsTotal = DTGV.Columns.Count - 1
            With excelWorksheet
                .Cells.Select()
                .Cells.Delete()
                For iC = 0 To colsTotal
                    .Cells(1, iC + 1).Value = DTGV.Columns(iC).HeaderText
                Next
                For I = 0 To rowsTotal - 1
                    For j = 0 To colsTotal
                        .Cells(I + 2, j + 1).value = DTGV.Rows(I).Cells(j).Value
                    Next j
                Next I
                .Rows("1:1").Font.FontStyle = "Bold"
                .Rows("1:1").Font.Size = 12

                .Cells.Columns.AutoFit()
                .Cells.Select()
                .Cells.EntireColumn.AutoFit()
                .Cells(1, 1).Select()
            End With
        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            'RELEASE ALLOACTED RESOURCES
            System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.Default
            xlApp = Nothing
        End Try
    End Sub

    Private Sub btnGroupItemsImage_Click(sender As Object, e As EventArgs) Handles btnGroupItemsImage.Click
        connect()
        Dim rpt = New rpt_ItemsAllImages
        Cls.Select_More_Data_Branch_Print("Items", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        Frm_PrintReports_3.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports_3.Text = "تقرير مجموعات الاصناف بصورة الصنف"
        Frm_PrintReports_3.Show()
    End Sub

    Private Sub GetUP_ParcodeItemsUnity()
        If rdoParcode.Checked = True Then
            If NotUnityItemsProgram = "YES" Then
                ParcodeUnity = txtsearsh.Text
                Dim PrcUnity As String = Cls.Get_Code_Value("ItemsUnity", "itm_id", "itm_id_Unity", ParcodeUnity)
                If PrcUnity <> "0" Then
                    txtsearsh.Text = PrcUnity
                End If
            End If
        End If
    End Sub

    Private Sub btnPrintWholeWholePrice_Click(sender As Object, e As EventArgs) Handles btnPrintWholeWholePrice.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DTGV.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintAllItems")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            S = "insert into PrintAllItems(Company_Branch_ID,itm_id,group_name,sname,SalPrice,tinpricetotal)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DTGV.Rows(i).Cells(1).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(14).Value & "',N'" & txtWholePrice.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New rptItemsPriceList

        Cls.Select_More_Data_Branch_Print_Orderby("PrintAllItems", "*", SortItems)
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)


        Dim txtname, txtNameAr, txtNameEn, txtWholePriceX, txtCmpAddress, txtCmpEmail, txtCmpTel, txtCmpMobile, txtCommercialRecord, txtTaxCard As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "أسعار سعر جملة الجملة لللاصناف"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        txtCmpAddress = rpt.Section1.ReportObjects("txtAddress")
        txtCmpAddress.Text = CmpAddress
        txtCmpEmail = rpt.Section1.ReportObjects("txtEmail")
        txtCmpEmail.Text = CmpEmail
        txtCmpTel = rpt.Section1.ReportObjects("txtTel")
        txtCmpTel.Text = CmpTel
        txtCmpMobile = rpt.Section1.ReportObjects("txtMobile")
        txtCmpMobile.Text = CmpMobile
        txtCommercialRecord = rpt.Section1.ReportObjects("txtCommercialRecord")
        txtCommercialRecord.Text = CMPCommercialRecord
        txtTaxCard = rpt.Section1.ReportObjects("txtTaxCard")
        txtTaxCard.Text = CMPTaxCard
        txtWholePriceX = rpt.Section1.ReportObjects("Text5")
        txtWholePriceX.Text = "سعر جملة الجملة"

        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()
        Frm_PrintReports.Text = "أسعار سعر جملة الجملة لللاصناف"

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub GetFormatNumberWithSeparators()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            DTGV.Rows(i).Cells(5).Value = FormatNumberWithSeparators(Double.Parse(DTGV.Rows(i).Cells(5).Value))
            DTGV.Rows(i).Cells(6).Value = FormatNumberWithSeparators(Double.Parse(DTGV.Rows(i).Cells(6).Value))
            DTGV.Rows(i).Cells(7).Value = FormatNumberWithSeparators(Double.Parse(DTGV.Rows(i).Cells(7).Value))
            DTGV.Rows(i).Cells(9).Value = FormatNumberWithSeparators(Double.Parse(DTGV.Rows(i).Cells(9).Value))
            DTGV.Rows(i).Cells(14).Value = FormatNumberWithSeparators(Double.Parse(DTGV.Rows(i).Cells(14).Value))
        Next
    End Sub

    Private Sub GetAdvancedSearch()
        If rdoSortParcode.Checked = True Then : SortItems = "itm_id" : End If
        If rdoSortgroup_name.Checked = True Then : SortItems = "group_name" : End If
        If rdoSortItemsName.Checked = True Then : SortItems = "sname" : End If
        If rdoSortID.Checked = True Then : SortItems = "id" : End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If ActivateFormatNumberWithSeparators = "YES" Then
            S = "Select id as [رقم],itm_id  as [الباركود], group_name as [المجموعة] , group_branch as [مجموعة فرعية],sname as [الاسم]," + GridTinPriceAverage + ",CAST(SalPrice AS NVARCHAR(50)) as [سعر البيع],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],rng as [الحد الأدنى] ,CAST(store AS NVARCHAR(50)) as [المخزون],StoreCarton as [المخزون بالكرتونة],Stores as [اسم المخزن],RateVAT as [الضريبة],Unity as [الوحدة],WholeWholePrice as [سعر جملة الجملة] from items where id <> ''"
            If cmbvendores.Text <> "" Then
                S = S & " and Vendorname =N'" & cmbvendores.Text & "'"
            End If
            If cmbStores.Text <> "" Then
                S = S & " and Stores =N'" & cmbStores.Text & "'"
            End If
            If cmbcats.Text <> "" Then
                S = S & " and group_name =N'" & cmbcats.Text & "'"
            End If
            If cmbGroup_Branch.Text <> "" Then
                S = S & " and group_branch =N'" & cmbGroup_Branch.Text & "'"
            End If
        Else
            S = "Select id as [رقم],itm_id  as [الباركود], group_name as [المجموعة] , group_branch as [مجموعة فرعية],sname as [الاسم]," + GridTinPriceAverage + ",SalPrice as [سعر البيع],WholePrice as [سعر الجملة],rng as [الحد الأدنى] ,store as [المخزون],StoreCarton as [المخزون بالكرتونة],Stores as [اسم المخزن],RateVAT as [الضريبة],Unity as [الوحدة],WholeWholePrice as [سعر جملة الجملة] from items where id <> ''"
            If cmbvendores.Text <> "" Then
                S = S & " and Vendorname =N'" & cmbvendores.Text & "'"
            End If
            If cmbStores.Text <> "" Then
                S = S & " and Stores =N'" & cmbStores.Text & "'"
            End If
            If cmbcats.Text <> "" Then
                S = S & " and group_name =N'" & cmbcats.Text & "'"
            End If
            If cmbGroup_Branch.Text <> "" Then
                S = S & " and group_branch =N'" & cmbGroup_Branch.Text & "'"
            End If
        End If

        S = S & " order by " & SortItems & ""
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)

        GetSearch()

        GetFormatNumberWithSeparators()
    End Sub
End Class