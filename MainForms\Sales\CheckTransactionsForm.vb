﻿Imports System.Data
Imports System.Data.SqlClient

Public Class CheckTransactionsForm
    Private Sub CheckTransactionsForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadCheckStatuses()
        LoadTransactionTypes()
        LoadBanks()
        LoadTreasuries()
    End Sub

    Private Sub LoadCheckStatuses()
        Try
            Using conn As New SqlConnection(constring)
                Dim query As String = "SELECT [StatusId], [StatusNameAr] FROM [Check_Status] WHERE [IsActive] = 1"
                Dim da As New SqlDataAdapter(query, conn)
                Dim dt As New DataTable()
                da.Fill(dt)

                cmbStatus.DataSource = dt
                cmbStatus.DisplayMember = "StatusNameAr"
                cmbStatus.ValueMember = "StatusId"
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل حالات الشيكات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadTransactionTypes()
        Try
            Using conn As New SqlConnection(constring)
                Dim query As String = "SELECT [TypeCode], [TypeNameAr] FROM [Check_Transaction_Types]"
                Dim da As New SqlDataAdapter(query, conn)
                Dim dt As New DataTable()
                da.Fill(dt)

                cmbTransactionType.DataSource = dt
                cmbTransactionType.DisplayMember = "TypeNameAr"
                cmbTransactionType.ValueMember = "TypeCode"
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل أنواع الحركات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadBanks()
        Try
            Using conn As New SqlConnection(constring)
                Dim query As String = "SELECT [BankCode], [BankName] FROM [BankData]"
                Dim da As New SqlDataAdapter(query, conn)
                Dim dt As New DataTable()
                da.Fill(dt)

                cmbBank.DataSource = dt
                cmbBank.DisplayMember = "BankName"
                cmbBank.ValueMember = "BankCode"
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل بيانات البنوك: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadTreasuries()
        Try
            Using conn As New SqlConnection(constring)
                Dim query As String = "SELECT [Treasury_Code], [Treasury_Name] FROM [Treasury] WHERE [IsActive] = 1"
                Dim da As New SqlDataAdapter(query, conn)
                Dim dt As New DataTable()
                da.Fill(dt)

                cmbTreasury.DataSource = dt
                cmbTreasury.DisplayMember = "Treasury_Name"
                cmbTreasury.ValueMember = "Treasury_Code"
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل بيانات الخزائن: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        Try
            Using conn As New SqlConnection(constring)
                Dim query As String = "SELECT c.[id], c.[Vendorname], c.[VND_dt], c.[VND_Date_Maturity], " &
                                   "c.[VND_amx], c.[VND_ho], c.[VND_no], c.[PaymentTotal], " &
                                   "s.[StatusNameAr] AS CheckStatus " &
                                   "FROM [Vst_Check_Type] c " &
                                   "INNER JOIN [Check_Status] s ON c.[Check_Status_ID] = s.[StatusId] " &
                                   "WHERE c.[VND_no] LIKE @CheckNo OR c.[Vendorname] LIKE @VendorName"

                Dim cmd As New SqlCommand(query, conn)
                cmd.Parameters.AddWithValue("@CheckNo", "%" & txtCheckNo.Text & "%")
                cmd.Parameters.AddWithValue("@VendorName", "%" & txtVendorName.Text & "%")

                Dim da As New SqlDataAdapter(cmd)
                Dim dt As New DataTable()
                da.Fill(dt)

                dgvChecks.DataSource = dt

                If dt.Rows.Count = 0 Then
                    MessageBox.Show("لا توجد شيكات مطابقة لمعايير البحث", "معلومة", MessageBoxButtons.OK, MessageBoxIcon.Information)
                End If
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في البحث: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub dgvChecks_SelectionChanged(sender As Object, e As EventArgs) Handles dgvChecks.SelectionChanged
        If dgvChecks.SelectedRows.Count > 0 Then
            Dim row As DataGridViewRow = dgvChecks.SelectedRows(0)
            Dim checkId As Integer = Convert.ToInt32(row.Cells("id").Value)

            LoadCheckDetails(checkId)
            LoadCheckTransactions(checkId)
        End If
    End Sub

    Private Sub LoadCheckDetails(checkId As Integer)
        Try
            Using conn As New SqlConnection(constring)
                Dim query As String = "SELECT c.*, s.[StatusNameAr] " &
                                   "FROM [Vst_Check_Type] c " &
                                   "INNER JOIN [Check_Status] s ON c.[Check_Status_ID] = s.[StatusId] " &
                                   "WHERE c.[id] = @CheckId"

                Dim cmd As New SqlCommand(query, conn)
                cmd.Parameters.AddWithValue("@CheckId", checkId)

                conn.Open()
                Dim reader As SqlDataReader = cmd.ExecuteReader()

                If reader.Read() Then
                    txtCheckId.Text = reader("id").ToString()
                    txtVendor.Text = reader("Vendorname").ToString()
                    txtCheckNumber.Text = reader("VND_no").ToString()
                    txtAmount.Text = Convert.ToDecimal(reader("VND_amx")).ToString("N2")
                    dtpIssueDate.Value = Cls.C_date(reader("VND_dt"))
                    dtpMaturityDate.Value = Cls.C_date(reader("VND_Date_Maturity"))
                    txtBankName.Text = reader("VND_ho").ToString()
                    txtStatus.Text = reader("StatusNameAr").ToString()
                    cmbStatus.SelectedValue = Convert.ToInt32(reader("Check_Status_ID"))

                End If

                reader.Close()
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل تفاصيل الشيك: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadCheckTransactions(checkId As Integer)
        Try
            Using conn As New SqlConnection(constring)
                Dim query As String = "SELECT t.[TransactionId], t.[TransactionDate], " &
                                   "t.[TransactionTypeAr] AS [نوع الحركة], " &
                                   "t.[Amount] AS [المبلغ], " &
                                   "t.[UserName] AS [المستخدم], " &
                                   "t.[TreasuryCode] AS [الخزينة], " &
                                   "t.[BankCode] AS [البنك], " &
                                   "t.[Notes] AS [ملاحظات], " &
                                   "CASE WHEN t.[IsReversed] = 1 THEN 'معكوسة' ELSE 'فعالة' END AS [الحالة] " &
                                   "FROM [Check_Transactions] t " &
                                   "WHERE t.[CheckId] = @CheckId " &
                                   "ORDER BY t.[TransactionDate] DESC"

                Dim cmd As New SqlCommand(query, conn)
                cmd.Parameters.AddWithValue("@CheckId", checkId)

                Dim da As New SqlDataAdapter(cmd)
                Dim dt As New DataTable()
                da.Fill(dt)

                dgvTransactions.DataSource = dt
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل حركات الشيك: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnAddTransaction_Click(sender As Object, e As EventArgs) Handles btnAddTransaction.Click
        If String.IsNullOrEmpty(txtCheckId.Text) Then
            MessageBox.Show("يجب اختيار شيك أولاً", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If cmbTransactionType.SelectedIndex = -1 Then
            MessageBox.Show("يجب اختيار نوع الحركة", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim amount As Decimal
        If Not Decimal.TryParse(txtTransactionAmount.Text, amount) OrElse amount <= 0 Then
            MessageBox.Show("يجب إدخال مبلغ صحيح", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            Using conn As New SqlConnection(constring)
                Dim cmd As New SqlCommand("sp_AddCheckTransaction", conn)
                cmd.CommandType = CommandType.StoredProcedure

                cmd.Parameters.AddWithValue("@CheckId", Convert.ToInt32(txtCheckId.Text))
                cmd.Parameters.AddWithValue("@TransactionTypeCode", cmbTransactionType.SelectedValue.ToString())
                cmd.Parameters.AddWithValue("@Amount", amount)
                cmd.Parameters.AddWithValue("@UserId", UserID) ' افترضنا وجود متغير عام للمستخدم الحالي

                If cmbTreasury.SelectedIndex <> -1 Then
                    cmd.Parameters.AddWithValue("@TreasuryCode", cmbTreasury.SelectedValue.ToString())
                Else
                    cmd.Parameters.AddWithValue("@TreasuryCode", Treasury_Code)
                End If

                If cmbBank.SelectedIndex <> -1 Then
                    cmd.Parameters.AddWithValue("@BankCode", cmbBank.SelectedValue.ToString())
                Else
                    cmd.Parameters.AddWithValue("@BankCode", DBNull.Value)
                End If

                cmd.Parameters.AddWithValue("@Notes", txtNotes.Text)

                conn.Open()
                cmd.ExecuteNonQuery()

                MessageBox.Show("تم إضافة الحركة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information)

                ' تحديث البيانات
                Dim checkId As Integer = Convert.ToInt32(txtCheckId.Text)
                LoadCheckDetails(checkId)
                LoadCheckTransactions(checkId)
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في إضافة الحركة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnUpdateStatus_Click(sender As Object, e As EventArgs) Handles btnUpdateStatus.Click
        If String.IsNullOrEmpty(txtCheckId.Text) Then
            MessageBox.Show("يجب اختيار شيك أولاً", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            Using conn As New SqlConnection(constring)
                Dim query As String = "UPDATE [Vst_Check_Type] SET [Check_Status_ID] = @StatusId WHERE [id] = @CheckId"

                Dim cmd As New SqlCommand(query, conn)
                cmd.Parameters.AddWithValue("@StatusId", cmbStatus.SelectedValue)
                cmd.Parameters.AddWithValue("@CheckId", Convert.ToInt32(txtCheckId.Text))

                conn.Open()
                cmd.ExecuteNonQuery()

                MessageBox.Show("تم تحديث حالة الشيك بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information)

                ' تحديث البيانات
                Dim checkId As Integer = Convert.ToInt32(txtCheckId.Text)
                LoadCheckDetails(checkId)
                LoadCheckTransactions(checkId)
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في تحديث حالة الشيك: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnReverseTransaction_Click(sender As Object, e As EventArgs) Handles btnReverseTransaction.Click
        If dgvTransactions.SelectedRows.Count = 0 Then
            MessageBox.Show("يجب اختيار حركة لعكسها", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim transactionId As Integer = Convert.ToInt32(dgvTransactions.SelectedRows(0).Cells("TransactionId").Value)

        Dim reverseForm As New CheckReverseTransaction()
        If reverseForm.ShowDialog() = DialogResult.OK Then

            Try
                Using conn As New SqlConnection(constring)
                    Dim cmd As New SqlCommand("sp_ReverseCheckTransaction", conn)
                    cmd.CommandType = CommandType.StoredProcedure

                    cmd.Parameters.AddWithValue("@TransactionId", transactionId)
                    cmd.Parameters.AddWithValue("@UserId", UserID)
                    cmd.Parameters.AddWithValue("@Reason", reverseForm.Reason)

                    conn.Open()
                    cmd.ExecuteNonQuery()

                    MessageBox.Show("تم عكس الحركة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information)

                    ' تحديث البيانات
                    Dim checkId As Integer = Convert.ToInt32(txtCheckId.Text)
                    LoadCheckTransactions(checkId)
                    LoadCheckDetails(checkId)
                End Using
            Catch ex As Exception
                MessageBox.Show("خطأ في عكس الحركة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try

        End If
    End Sub
End Class