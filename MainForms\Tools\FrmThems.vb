﻿Public Class FrmThems

    Private Sub Skins_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        ListBox1.Items.Clear()
        Dim fls As String
        For Each fls In IO.Directory.GetFiles(Application.StartupPath + "\skins")
            Dim x As String
            x = (IO.Path.GetFileName(fls))
            ListBox1.Items.Add(x)
        Next
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        Me.Close()
    End Sub

    Private Sub ListBox1_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles ListBox1.MouseClick

        S = (Application.StartupPath + "\skins\" + ListBox1.Text)
        MDIParent1.SkinEngine1.SkinFile = S
    End Sub

    Private Sub ListBox1_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ListBox1.SelectedIndexChanged

        'Me.SkinEngine1.SkinFile = (Application.StartupPath + "\office2007.ssk")


    End Sub
End Class