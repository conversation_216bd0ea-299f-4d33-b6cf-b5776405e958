﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class FrmMaintenanceOrderRunningDevices
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FrmMaintenanceOrderRunningDevices))
        Dim DataGridViewCellStyle5 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle6 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle7 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle8 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.rdoPrintPreview = New System.Windows.Forms.RadioButton()
        Me.rdoPrintDirect = New System.Windows.Forms.RadioButton()
        Me.btnSettingPrint = New System.Windows.Forms.Button()
        Me.GroupBox7 = New System.Windows.Forms.GroupBox()
        Me.chkprint = New System.Windows.Forms.CheckBox()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.lblTitel = New System.Windows.Forms.Label()
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.Label8 = New System.Windows.Forms.Label()
        Me.TxtHour = New System.Windows.Forms.TextBox()
        Me.lblMaintenanceType = New System.Windows.Forms.Label()
        Me.lblNameItems = New System.Windows.Forms.Label()
        Me.dtpTime = New System.Windows.Forms.DateTimePicker()
        Me.cmbCustomer = New System.Windows.Forms.ComboBox()
        Me.btnAdd = New System.Windows.Forms.Button()
        Me.Dgv_Add = New System.Windows.Forms.DataGridView()
        Me.dtpDate = New System.Windows.Forms.DateTimePicker()
        Me.lblDate = New System.Windows.Forms.Label()
        Me.BtnDelete = New System.Windows.Forms.Button()
        Me.BtnClear = New System.Windows.Forms.Button()
        Me.Panel8 = New System.Windows.Forms.Panel()
        Me.Panel7 = New System.Windows.Forms.Panel()
        Me.Panel6 = New System.Windows.Forms.Panel()
        Me.Panel5 = New System.Windows.Forms.Panel()
        Me.txtOrderID = New System.Windows.Forms.TextBox()
        Me.lblOrderID = New System.Windows.Forms.Label()
        Me.lblFatherName = New System.Windows.Forms.Label()
        Me.Panel4 = New System.Windows.Forms.Panel()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.bgHeader = New System.Windows.Forms.Panel()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.btnSave = New System.Windows.Forms.Button()
        Me.lblTypeProduct = New System.Windows.Forms.Label()
        Me.cmbTypeProduct = New System.Windows.Forms.ComboBox()
        Me.cmbMaintenanceType = New System.Windows.Forms.ComboBox()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.cmbRequiredRepair = New System.Windows.Forms.ComboBox()
        Me.cmbSupervisor = New System.Windows.Forms.ComboBox()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.rdoLarge = New System.Windows.Forms.RadioButton()
        Me.rdoSmall = New System.Windows.Forms.RadioButton()
        Me.cmbRecipient = New System.Windows.Forms.ComboBox()
        Me.cmbDeviceModel = New System.Windows.Forms.ComboBox()
        Me.cmbDeviceBrand = New System.Windows.Forms.ComboBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.txtCarNumber = New System.Windows.Forms.TextBox()
        Me.Label19 = New System.Windows.Forms.Label()
        Me.txtDescription = New System.Windows.Forms.TextBox()
        Me.Panel9 = New System.Windows.Forms.Panel()
        Me.btnAddReceivingCar = New System.Windows.Forms.Button()
        Me.cmbReceivingCar = New System.Windows.Forms.ComboBox()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.DGVReceivingCar = New System.Windows.Forms.DataGridView()
        Me.Panel10 = New System.Windows.Forms.Panel()
        Me.btnDeleteReceivingCar = New System.Windows.Forms.Button()
        Me.GroupBox7.SuspendLayout()
        CType(Me.Dgv_Add, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.bgHeader.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        CType(Me.DGVReceivingCar, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'rdoPrintPreview
        '
        Me.rdoPrintPreview.AutoSize = True
        Me.rdoPrintPreview.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdoPrintPreview.ForeColor = System.Drawing.SystemColors.ControlText
        Me.rdoPrintPreview.Location = New System.Drawing.Point(22, 37)
        Me.rdoPrintPreview.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.rdoPrintPreview.Name = "rdoPrintPreview"
        Me.rdoPrintPreview.Size = New System.Drawing.Size(115, 30)
        Me.rdoPrintPreview.TabIndex = 348
        Me.rdoPrintPreview.Text = "عرض طباعة"
        Me.rdoPrintPreview.UseVisualStyleBackColor = True
        '
        'rdoPrintDirect
        '
        Me.rdoPrintDirect.AutoSize = True
        Me.rdoPrintDirect.Checked = True
        Me.rdoPrintDirect.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdoPrintDirect.ForeColor = System.Drawing.SystemColors.ControlText
        Me.rdoPrintDirect.Location = New System.Drawing.Point(5, 9)
        Me.rdoPrintDirect.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.rdoPrintDirect.Name = "rdoPrintDirect"
        Me.rdoPrintDirect.Size = New System.Drawing.Size(133, 30)
        Me.rdoPrintDirect.TabIndex = 347
        Me.rdoPrintDirect.TabStop = True
        Me.rdoPrintDirect.Text = "طباعة مباشرة"
        Me.rdoPrintDirect.UseVisualStyleBackColor = True
        '
        'btnSettingPrint
        '
        Me.btnSettingPrint.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSettingPrint.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.btnSettingPrint.Location = New System.Drawing.Point(1132, 657)
        Me.btnSettingPrint.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnSettingPrint.Name = "btnSettingPrint"
        Me.btnSettingPrint.Size = New System.Drawing.Size(128, 38)
        Me.btnSettingPrint.TabIndex = 354
        Me.btnSettingPrint.Text = "خصائص الطابعة"
        Me.btnSettingPrint.UseVisualStyleBackColor = True
        '
        'GroupBox7
        '
        Me.GroupBox7.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox7.Controls.Add(Me.rdoPrintPreview)
        Me.GroupBox7.Controls.Add(Me.rdoPrintDirect)
        Me.GroupBox7.Location = New System.Drawing.Point(810, 639)
        Me.GroupBox7.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.GroupBox7.Name = "GroupBox7"
        Me.GroupBox7.Padding = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.GroupBox7.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.GroupBox7.Size = New System.Drawing.Size(146, 74)
        Me.GroupBox7.TabIndex = 353
        Me.GroupBox7.TabStop = False
        '
        'chkprint
        '
        Me.chkprint.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.chkprint.AutoSize = True
        Me.chkprint.Checked = True
        Me.chkprint.CheckState = System.Windows.Forms.CheckState.Checked
        Me.chkprint.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.chkprint.Location = New System.Drawing.Point(954, 659)
        Me.chkprint.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.chkprint.Name = "chkprint"
        Me.chkprint.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkprint.Size = New System.Drawing.Size(168, 34)
        Me.chkprint.TabIndex = 352
        Me.chkprint.Text = "طباعة أمر الشغل"
        Me.chkprint.UseVisualStyleBackColor = True
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel1.Location = New System.Drawing.Point(10, 324)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1283, 1)
        Me.Panel1.TabIndex = 349
        '
        'lblTitel
        '
        Me.lblTitel.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblTitel.AutoSize = True
        Me.lblTitel.BackColor = System.Drawing.Color.Transparent
        Me.lblTitel.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblTitel.Font = New System.Drawing.Font("JF Flat", 18.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.lblTitel.ForeColor = System.Drawing.Color.White
        Me.lblTitel.Location = New System.Drawing.Point(525, 9)
        Me.lblTitel.Name = "lblTitel"
        Me.lblTitel.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.lblTitel.Size = New System.Drawing.Size(290, 52)
        Me.lblTitel.TabIndex = 10
        Me.lblTitel.Text = "أوامر تشغيل الصيانة"
        '
        'Timer1
        '
        Me.Timer1.Enabled = True
        '
        'Label8
        '
        Me.Label8.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label8.AutoSize = True
        Me.Label8.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.Label8.ForeColor = System.Drawing.SystemColors.Desktop
        Me.Label8.Location = New System.Drawing.Point(61, 86)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(63, 30)
        Me.Label8.TabIndex = 359
        Me.Label8.Text = "الوقت"
        '
        'TxtHour
        '
        Me.TxtHour.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TxtHour.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.TxtHour.Location = New System.Drawing.Point(27, 117)
        Me.TxtHour.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.TxtHour.Name = "TxtHour"
        Me.TxtHour.Size = New System.Drawing.Size(115, 34)
        Me.TxtHour.TabIndex = 358
        Me.TxtHour.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'lblMaintenanceType
        '
        Me.lblMaintenanceType.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblMaintenanceType.AutoSize = True
        Me.lblMaintenanceType.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.lblMaintenanceType.Location = New System.Drawing.Point(1137, 330)
        Me.lblMaintenanceType.Name = "lblMaintenanceType"
        Me.lblMaintenanceType.Size = New System.Drawing.Size(98, 30)
        Me.lblMaintenanceType.TabIndex = 343
        Me.lblMaintenanceType.Text = "نوع العطل"
        Me.lblMaintenanceType.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblNameItems
        '
        Me.lblNameItems.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblNameItems.AutoSize = True
        Me.lblNameItems.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.lblNameItems.Location = New System.Drawing.Point(772, 330)
        Me.lblNameItems.Name = "lblNameItems"
        Me.lblNameItems.Size = New System.Drawing.Size(145, 30)
        Me.lblNameItems.TabIndex = 342
        Me.lblNameItems.Text = "المطلوب أصلاحة"
        Me.lblNameItems.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'dtpTime
        '
        Me.dtpTime.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.dtpTime.CustomFormat = "dd/MM/yyyy"
        Me.dtpTime.Font = New System.Drawing.Font("Microsoft Sans Serif", 11.25!, System.Drawing.FontStyle.Bold)
        Me.dtpTime.Format = System.Windows.Forms.DateTimePickerFormat.Time
        Me.dtpTime.Location = New System.Drawing.Point(127, 18)
        Me.dtpTime.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.dtpTime.Name = "dtpTime"
        Me.dtpTime.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.dtpTime.RightToLeftLayout = True
        Me.dtpTime.Size = New System.Drawing.Size(121, 29)
        Me.dtpTime.TabIndex = 306
        '
        'cmbCustomer
        '
        Me.cmbCustomer.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbCustomer.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbCustomer.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbCustomer.BackColor = System.Drawing.Color.White
        Me.cmbCustomer.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.cmbCustomer.ForeColor = System.Drawing.Color.Black
        Me.cmbCustomer.FormattingEnabled = True
        Me.cmbCustomer.Location = New System.Drawing.Point(647, 117)
        Me.cmbCustomer.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbCustomer.Name = "cmbCustomer"
        Me.cmbCustomer.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.cmbCustomer.Size = New System.Drawing.Size(453, 34)
        Me.cmbCustomer.TabIndex = 334
        '
        'btnAdd
        '
        Me.btnAdd.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnAdd.BackColor = System.Drawing.Color.Transparent
        Me.btnAdd.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.btnAdd.Location = New System.Drawing.Point(549, 358)
        Me.btnAdd.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnAdd.Name = "btnAdd"
        Me.btnAdd.Size = New System.Drawing.Size(70, 49)
        Me.btnAdd.TabIndex = 327
        Me.btnAdd.Text = "إضافة"
        Me.btnAdd.UseVisualStyleBackColor = False
        '
        'Dgv_Add
        '
        Me.Dgv_Add.AllowUserToAddRows = False
        Me.Dgv_Add.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Dgv_Add.BackgroundColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle1.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle1.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.Dgv_Add.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle1
        Me.Dgv_Add.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window
        DataGridViewCellStyle2.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.Dgv_Add.DefaultCellStyle = DataGridViewCellStyle2
        Me.Dgv_Add.Location = New System.Drawing.Point(476, 412)
        Me.Dgv_Add.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Dgv_Add.Name = "Dgv_Add"
        Me.Dgv_Add.ReadOnly = True
        Me.Dgv_Add.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        DataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle3.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle3.Font = New System.Drawing.Font("Tahoma", 8.0!)
        DataGridViewCellStyle3.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle3.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.Dgv_Add.RowHeadersDefaultCellStyle = DataGridViewCellStyle3
        DataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.SteelBlue
        Me.Dgv_Add.RowsDefaultCellStyle = DataGridViewCellStyle4
        Me.Dgv_Add.RowTemplate.Height = 25
        Me.Dgv_Add.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.Dgv_Add.Size = New System.Drawing.Size(819, 224)
        Me.Dgv_Add.TabIndex = 328
        '
        'dtpDate
        '
        Me.dtpDate.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.dtpDate.CustomFormat = "dd/MM/yyyy"
        Me.dtpDate.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.dtpDate.Format = System.Windows.Forms.DateTimePickerFormat.[Short]
        Me.dtpDate.Location = New System.Drawing.Point(147, 117)
        Me.dtpDate.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.dtpDate.Name = "dtpDate"
        Me.dtpDate.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.dtpDate.RightToLeftLayout = True
        Me.dtpDate.Size = New System.Drawing.Size(176, 34)
        Me.dtpDate.TabIndex = 312
        '
        'lblDate
        '
        Me.lblDate.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblDate.AutoSize = True
        Me.lblDate.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.lblDate.Location = New System.Drawing.Point(173, 86)
        Me.lblDate.Name = "lblDate"
        Me.lblDate.Size = New System.Drawing.Size(120, 30)
        Me.lblDate.TabIndex = 326
        Me.lblDate.Text = "تاريخ الاستلام"
        Me.lblDate.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'BtnDelete
        '
        Me.BtnDelete.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.BtnDelete.BackColor = System.Drawing.Color.Transparent
        Me.BtnDelete.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.BtnDelete.ForeColor = System.Drawing.SystemColors.Desktop
        Me.BtnDelete.Location = New System.Drawing.Point(476, 358)
        Me.BtnDelete.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.BtnDelete.Name = "BtnDelete"
        Me.BtnDelete.Size = New System.Drawing.Size(70, 49)
        Me.BtnDelete.TabIndex = 325
        Me.BtnDelete.Text = "حذف"
        Me.BtnDelete.UseVisualStyleBackColor = False
        '
        'BtnClear
        '
        Me.BtnClear.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.BtnClear.BackColor = System.Drawing.Color.Transparent
        Me.BtnClear.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.BtnClear.ForeColor = System.Drawing.SystemColors.Desktop
        Me.BtnClear.Location = New System.Drawing.Point(264, 652)
        Me.BtnClear.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.BtnClear.Name = "BtnClear"
        Me.BtnClear.Size = New System.Drawing.Size(101, 47)
        Me.BtnClear.TabIndex = 324
        Me.BtnClear.Text = "تفريغ"
        Me.BtnClear.UseVisualStyleBackColor = False
        '
        'Panel8
        '
        Me.Panel8.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel8.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel8.Location = New System.Drawing.Point(12, 720)
        Me.Panel8.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel8.Name = "Panel8"
        Me.Panel8.Size = New System.Drawing.Size(1283, 6)
        Me.Panel8.TabIndex = 323
        '
        'Panel7
        '
        Me.Panel7.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel7.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel7.Location = New System.Drawing.Point(6, 74)
        Me.Panel7.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel7.Name = "Panel7"
        Me.Panel7.Size = New System.Drawing.Size(6, 652)
        Me.Panel7.TabIndex = 322
        '
        'Panel6
        '
        Me.Panel6.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel6.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel6.Location = New System.Drawing.Point(1295, 74)
        Me.Panel6.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel6.Name = "Panel6"
        Me.Panel6.Size = New System.Drawing.Size(6, 652)
        Me.Panel6.TabIndex = 321
        '
        'Panel5
        '
        Me.Panel5.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel5.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel5.Location = New System.Drawing.Point(6, 68)
        Me.Panel5.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel5.Name = "Panel5"
        Me.Panel5.Size = New System.Drawing.Size(1295, 6)
        Me.Panel5.TabIndex = 320
        '
        'txtOrderID
        '
        Me.txtOrderID.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtOrderID.BackColor = System.Drawing.Color.White
        Me.txtOrderID.Enabled = False
        Me.txtOrderID.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.txtOrderID.ForeColor = System.Drawing.SystemColors.ControlText
        Me.txtOrderID.Location = New System.Drawing.Point(1106, 117)
        Me.txtOrderID.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtOrderID.Name = "txtOrderID"
        Me.txtOrderID.Size = New System.Drawing.Size(180, 34)
        Me.txtOrderID.TabIndex = 311
        Me.txtOrderID.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'lblOrderID
        '
        Me.lblOrderID.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblOrderID.AutoSize = True
        Me.lblOrderID.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.lblOrderID.Location = New System.Drawing.Point(1156, 86)
        Me.lblOrderID.Name = "lblOrderID"
        Me.lblOrderID.Size = New System.Drawing.Size(87, 30)
        Me.lblOrderID.TabIndex = 319
        Me.lblOrderID.Text = "رقم الاذن"
        Me.lblOrderID.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblFatherName
        '
        Me.lblFatherName.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblFatherName.AutoSize = True
        Me.lblFatherName.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.lblFatherName.ForeColor = System.Drawing.SystemColors.ControlText
        Me.lblFatherName.Location = New System.Drawing.Point(820, 86)
        Me.lblFatherName.Name = "lblFatherName"
        Me.lblFatherName.Size = New System.Drawing.Size(107, 30)
        Me.lblFatherName.TabIndex = 318
        Me.lblFatherName.Text = "أسم العميل"
        Me.lblFatherName.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Panel4
        '
        Me.Panel4.BackColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(122, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.Panel4.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel4.Location = New System.Drawing.Point(6, 726)
        Me.Panel4.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel4.Name = "Panel4"
        Me.Panel4.Size = New System.Drawing.Size(1295, 12)
        Me.Panel4.TabIndex = 317
        '
        'Panel3
        '
        Me.Panel3.BackColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(122, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.Panel3.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel3.Location = New System.Drawing.Point(0, 68)
        Me.Panel3.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(6, 670)
        Me.Panel3.TabIndex = 316
        '
        'Panel2
        '
        Me.Panel2.BackColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(122, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel2.Location = New System.Drawing.Point(1301, 68)
        Me.Panel2.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(6, 670)
        Me.Panel2.TabIndex = 315
        '
        'bgHeader
        '
        Me.bgHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(122, Byte), Integer), CType(CType(105, Byte), Integer))
        Me.bgHeader.Controls.Add(Me.PictureBox1)
        Me.bgHeader.Controls.Add(Me.dtpTime)
        Me.bgHeader.Controls.Add(Me.lblTitel)
        Me.bgHeader.Cursor = System.Windows.Forms.Cursors.Default
        Me.bgHeader.Dock = System.Windows.Forms.DockStyle.Top
        Me.bgHeader.Font = New System.Drawing.Font("Arial", 8.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bgHeader.ForeColor = System.Drawing.SystemColors.WindowText
        Me.bgHeader.Location = New System.Drawing.Point(0, 0)
        Me.bgHeader.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.bgHeader.Name = "bgHeader"
        Me.bgHeader.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.bgHeader.Size = New System.Drawing.Size(1307, 68)
        Me.bgHeader.TabIndex = 314
        Me.bgHeader.TabStop = True
        '
        'PictureBox1
        '
        Me.PictureBox1.BackgroundImage = CType(resources.GetObject("PictureBox1.BackgroundImage"), System.Drawing.Image)
        Me.PictureBox1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.PictureBox1.Location = New System.Drawing.Point(1170, 4)
        Me.PictureBox1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(117, 62)
        Me.PictureBox1.TabIndex = 307
        Me.PictureBox1.TabStop = False
        '
        'btnSave
        '
        Me.btnSave.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnSave.BackColor = System.Drawing.Color.Transparent
        Me.btnSave.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.btnSave.Location = New System.Drawing.Point(19, 652)
        Me.btnSave.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnSave.Name = "btnSave"
        Me.btnSave.Size = New System.Drawing.Size(239, 47)
        Me.btnSave.TabIndex = 313
        Me.btnSave.Text = "حفظ"
        Me.btnSave.UseVisualStyleBackColor = False
        '
        'lblTypeProduct
        '
        Me.lblTypeProduct.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblTypeProduct.AutoSize = True
        Me.lblTypeProduct.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.lblTypeProduct.Location = New System.Drawing.Point(1140, 160)
        Me.lblTypeProduct.Name = "lblTypeProduct"
        Me.lblTypeProduct.Size = New System.Drawing.Size(98, 30)
        Me.lblTypeProduct.TabIndex = 366
        Me.lblTypeProduct.Text = "نوع الجهاز"
        Me.lblTypeProduct.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'cmbTypeProduct
        '
        Me.cmbTypeProduct.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbTypeProduct.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbTypeProduct.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbTypeProduct.BackColor = System.Drawing.Color.White
        Me.cmbTypeProduct.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.cmbTypeProduct.ForeColor = System.Drawing.Color.Black
        Me.cmbTypeProduct.FormattingEnabled = True
        Me.cmbTypeProduct.Location = New System.Drawing.Point(1106, 193)
        Me.cmbTypeProduct.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbTypeProduct.Name = "cmbTypeProduct"
        Me.cmbTypeProduct.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.cmbTypeProduct.Size = New System.Drawing.Size(180, 34)
        Me.cmbTypeProduct.TabIndex = 367
        '
        'cmbMaintenanceType
        '
        Me.cmbMaintenanceType.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbMaintenanceType.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbMaintenanceType.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbMaintenanceType.BackColor = System.Drawing.Color.White
        Me.cmbMaintenanceType.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.cmbMaintenanceType.ForeColor = System.Drawing.Color.Black
        Me.cmbMaintenanceType.FormattingEnabled = True
        Me.cmbMaintenanceType.Location = New System.Drawing.Point(1089, 365)
        Me.cmbMaintenanceType.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbMaintenanceType.Name = "cmbMaintenanceType"
        Me.cmbMaintenanceType.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.cmbMaintenanceType.Size = New System.Drawing.Size(200, 34)
        Me.cmbMaintenanceType.TabIndex = 368
        '
        'Label3
        '
        Me.Label3.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.Label3.Location = New System.Drawing.Point(396, 160)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(180, 30)
        Me.Label3.TabIndex = 371
        Me.Label3.Text = "المشرف على الاصلاح"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label4
        '
        Me.Label4.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.Label4.Location = New System.Drawing.Point(134, 159)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(123, 30)
        Me.Label4.TabIndex = 373
        Me.Label4.Text = "مستلم الجهاز"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'cmbRequiredRepair
        '
        Me.cmbRequiredRepair.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbRequiredRepair.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbRequiredRepair.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbRequiredRepair.BackColor = System.Drawing.Color.White
        Me.cmbRequiredRepair.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.cmbRequiredRepair.ForeColor = System.Drawing.Color.Black
        Me.cmbRequiredRepair.FormattingEnabled = True
        Me.cmbRequiredRepair.Location = New System.Drawing.Point(625, 365)
        Me.cmbRequiredRepair.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbRequiredRepair.Name = "cmbRequiredRepair"
        Me.cmbRequiredRepair.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.cmbRequiredRepair.Size = New System.Drawing.Size(460, 34)
        Me.cmbRequiredRepair.TabIndex = 380
        '
        'cmbSupervisor
        '
        Me.cmbSupervisor.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbSupervisor.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbSupervisor.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbSupervisor.BackColor = System.Drawing.Color.White
        Me.cmbSupervisor.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.cmbSupervisor.ForeColor = System.Drawing.Color.Black
        Me.cmbSupervisor.FormattingEnabled = True
        Me.cmbSupervisor.Location = New System.Drawing.Point(333, 194)
        Me.cmbSupervisor.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbSupervisor.Name = "cmbSupervisor"
        Me.cmbSupervisor.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.cmbSupervisor.Size = New System.Drawing.Size(306, 34)
        Me.cmbSupervisor.TabIndex = 381
        '
        'GroupBox1
        '
        Me.GroupBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox1.Controls.Add(Me.rdoLarge)
        Me.GroupBox1.Controls.Add(Me.rdoSmall)
        Me.GroupBox1.Location = New System.Drawing.Point(657, 639)
        Me.GroupBox1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Padding = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.GroupBox1.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.GroupBox1.Size = New System.Drawing.Size(146, 74)
        Me.GroupBox1.TabIndex = 382
        Me.GroupBox1.TabStop = False
        '
        'rdoLarge
        '
        Me.rdoLarge.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.rdoLarge.AutoSize = True
        Me.rdoLarge.Checked = True
        Me.rdoLarge.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.rdoLarge.ForeColor = System.Drawing.SystemColors.ControlText
        Me.rdoLarge.Location = New System.Drawing.Point(14, 11)
        Me.rdoLarge.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.rdoLarge.Name = "rdoLarge"
        Me.rdoLarge.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.rdoLarge.Size = New System.Drawing.Size(124, 30)
        Me.rdoLarge.TabIndex = 384
        Me.rdoLarge.TabStop = True
        Me.rdoLarge.Text = "فاتورة كبيرة"
        Me.rdoLarge.UseVisualStyleBackColor = True
        '
        'rdoSmall
        '
        Me.rdoSmall.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.rdoSmall.AutoSize = True
        Me.rdoSmall.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.rdoSmall.ForeColor = System.Drawing.SystemColors.ControlText
        Me.rdoSmall.Location = New System.Drawing.Point(8, 39)
        Me.rdoSmall.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.rdoSmall.Name = "rdoSmall"
        Me.rdoSmall.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.rdoSmall.Size = New System.Drawing.Size(130, 30)
        Me.rdoSmall.TabIndex = 383
        Me.rdoSmall.Text = "فاتورة صغيرة"
        Me.rdoSmall.UseVisualStyleBackColor = True
        '
        'cmbRecipient
        '
        Me.cmbRecipient.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbRecipient.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbRecipient.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbRecipient.BackColor = System.Drawing.Color.White
        Me.cmbRecipient.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.cmbRecipient.ForeColor = System.Drawing.Color.Black
        Me.cmbRecipient.FormattingEnabled = True
        Me.cmbRecipient.Location = New System.Drawing.Point(27, 193)
        Me.cmbRecipient.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbRecipient.Name = "cmbRecipient"
        Me.cmbRecipient.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.cmbRecipient.Size = New System.Drawing.Size(296, 34)
        Me.cmbRecipient.TabIndex = 383
        '
        'cmbDeviceModel
        '
        Me.cmbDeviceModel.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbDeviceModel.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbDeviceModel.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbDeviceModel.BackColor = System.Drawing.Color.White
        Me.cmbDeviceModel.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.cmbDeviceModel.ForeColor = System.Drawing.Color.Black
        Me.cmbDeviceModel.FormattingEnabled = True
        Me.cmbDeviceModel.Location = New System.Drawing.Point(647, 193)
        Me.cmbDeviceModel.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbDeviceModel.Name = "cmbDeviceModel"
        Me.cmbDeviceModel.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.cmbDeviceModel.Size = New System.Drawing.Size(255, 34)
        Me.cmbDeviceModel.TabIndex = 377
        '
        'cmbDeviceBrand
        '
        Me.cmbDeviceBrand.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbDeviceBrand.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbDeviceBrand.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbDeviceBrand.BackColor = System.Drawing.Color.White
        Me.cmbDeviceBrand.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.cmbDeviceBrand.ForeColor = System.Drawing.Color.Black
        Me.cmbDeviceBrand.FormattingEnabled = True
        Me.cmbDeviceBrand.Location = New System.Drawing.Point(908, 193)
        Me.cmbDeviceBrand.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbDeviceBrand.Name = "cmbDeviceBrand"
        Me.cmbDeviceBrand.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.cmbDeviceBrand.Size = New System.Drawing.Size(192, 34)
        Me.cmbDeviceBrand.TabIndex = 384
        '
        'Label2
        '
        Me.Label2.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.Label2.Location = New System.Drawing.Point(948, 160)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(113, 30)
        Me.Label2.TabIndex = 331
        Me.Label2.Text = "ماركة الجهاز"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label6
        '
        Me.Label6.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.Label6.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label6.Location = New System.Drawing.Point(715, 160)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(118, 30)
        Me.Label6.TabIndex = 376
        Me.Label6.Text = "موديل الجهاز"
        Me.Label6.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label18
        '
        Me.Label18.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label18.AutoSize = True
        Me.Label18.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.Label18.Location = New System.Drawing.Point(428, 84)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(117, 30)
        Me.Label18.TabIndex = 404
        Me.Label18.Text = "سيريال الجهاز"
        Me.Label18.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'txtCarNumber
        '
        Me.txtCarNumber.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtCarNumber.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.txtCarNumber.Location = New System.Drawing.Point(331, 117)
        Me.txtCarNumber.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtCarNumber.MaxLength = 50
        Me.txtCarNumber.Name = "txtCarNumber"
        Me.txtCarNumber.Size = New System.Drawing.Size(310, 34)
        Me.txtCarNumber.TabIndex = 403
        Me.txtCarNumber.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        '
        'Label19
        '
        Me.Label19.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label19.AutoSize = True
        Me.Label19.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.Label19.Location = New System.Drawing.Point(1140, 230)
        Me.Label19.Name = "Label19"
        Me.Label19.Size = New System.Drawing.Size(111, 30)
        Me.Label19.TabIndex = 406
        Me.Label19.Text = "وصف الجهاز"
        Me.Label19.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'txtDescription
        '
        Me.txtDescription.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtDescription.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.txtDescription.Location = New System.Drawing.Point(27, 263)
        Me.txtDescription.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtDescription.MaxLength = 10000
        Me.txtDescription.Multiline = True
        Me.txtDescription.Name = "txtDescription"
        Me.txtDescription.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.txtDescription.Size = New System.Drawing.Size(1259, 54)
        Me.txtDescription.TabIndex = 405
        Me.txtDescription.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        '
        'Panel9
        '
        Me.Panel9.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel9.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel9.Location = New System.Drawing.Point(469, 324)
        Me.Panel9.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel9.Name = "Panel9"
        Me.Panel9.Size = New System.Drawing.Size(1, 318)
        Me.Panel9.TabIndex = 407
        '
        'btnAddReceivingCar
        '
        Me.btnAddReceivingCar.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnAddReceivingCar.BackColor = System.Drawing.Color.Transparent
        Me.btnAddReceivingCar.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.btnAddReceivingCar.Location = New System.Drawing.Point(90, 358)
        Me.btnAddReceivingCar.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnAddReceivingCar.Name = "btnAddReceivingCar"
        Me.btnAddReceivingCar.Size = New System.Drawing.Size(70, 49)
        Me.btnAddReceivingCar.TabIndex = 409
        Me.btnAddReceivingCar.Text = "إضافة"
        Me.btnAddReceivingCar.UseVisualStyleBackColor = False
        '
        'cmbReceivingCar
        '
        Me.cmbReceivingCar.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbReceivingCar.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbReceivingCar.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbReceivingCar.BackColor = System.Drawing.Color.White
        Me.cmbReceivingCar.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.cmbReceivingCar.ForeColor = System.Drawing.Color.Black
        Me.cmbReceivingCar.FormattingEnabled = True
        Me.cmbReceivingCar.Location = New System.Drawing.Point(165, 365)
        Me.cmbReceivingCar.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbReceivingCar.Name = "cmbReceivingCar"
        Me.cmbReceivingCar.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.cmbReceivingCar.Size = New System.Drawing.Size(296, 34)
        Me.cmbReceivingCar.TabIndex = 411
        '
        'Label7
        '
        Me.Label7.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.Label7.Location = New System.Drawing.Point(243, 330)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(131, 30)
        Me.Label7.TabIndex = 410
        Me.Label7.Text = "ملحقات الجهاز"
        Me.Label7.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'DGVReceivingCar
        '
        Me.DGVReceivingCar.AllowUserToAddRows = False
        Me.DGVReceivingCar.AllowUserToDeleteRows = False
        Me.DGVReceivingCar.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DGVReceivingCar.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill
        Me.DGVReceivingCar.BackgroundColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle5.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle5.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle5.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle5.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle5.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DGVReceivingCar.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle5
        Me.DGVReceivingCar.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        DataGridViewCellStyle6.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle6.BackColor = System.Drawing.SystemColors.Window
        DataGridViewCellStyle6.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle6.ForeColor = System.Drawing.SystemColors.ControlText
        DataGridViewCellStyle6.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle6.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle6.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.DGVReceivingCar.DefaultCellStyle = DataGridViewCellStyle6
        Me.DGVReceivingCar.Location = New System.Drawing.Point(21, 412)
        Me.DGVReceivingCar.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.DGVReceivingCar.Name = "DGVReceivingCar"
        Me.DGVReceivingCar.ReadOnly = True
        Me.DGVReceivingCar.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        DataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle7.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle7.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle7.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle7.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle7.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DGVReceivingCar.RowHeadersDefaultCellStyle = DataGridViewCellStyle7
        DataGridViewCellStyle8.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.DGVReceivingCar.RowsDefaultCellStyle = DataGridViewCellStyle8
        Me.DGVReceivingCar.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.DGVReceivingCar.Size = New System.Drawing.Size(443, 224)
        Me.DGVReceivingCar.TabIndex = 412
        '
        'Panel10
        '
        Me.Panel10.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel10.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Panel10.Location = New System.Drawing.Point(16, 641)
        Me.Panel10.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel10.Name = "Panel10"
        Me.Panel10.Size = New System.Drawing.Size(1283, 1)
        Me.Panel10.TabIndex = 413
        '
        'btnDeleteReceivingCar
        '
        Me.btnDeleteReceivingCar.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnDeleteReceivingCar.BackColor = System.Drawing.Color.Transparent
        Me.btnDeleteReceivingCar.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.btnDeleteReceivingCar.ForeColor = System.Drawing.SystemColors.Desktop
        Me.btnDeleteReceivingCar.Location = New System.Drawing.Point(19, 358)
        Me.btnDeleteReceivingCar.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnDeleteReceivingCar.Name = "btnDeleteReceivingCar"
        Me.btnDeleteReceivingCar.Size = New System.Drawing.Size(70, 49)
        Me.btnDeleteReceivingCar.TabIndex = 414
        Me.btnDeleteReceivingCar.Text = "حذف"
        Me.btnDeleteReceivingCar.UseVisualStyleBackColor = False
        '
        'FrmMaintenanceOrderRunningDevices
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1307, 738)
        Me.Controls.Add(Me.btnDeleteReceivingCar)
        Me.Controls.Add(Me.Panel10)
        Me.Controls.Add(Me.DGVReceivingCar)
        Me.Controls.Add(Me.cmbReceivingCar)
        Me.Controls.Add(Me.Label7)
        Me.Controls.Add(Me.btnAddReceivingCar)
        Me.Controls.Add(Me.Panel9)
        Me.Controls.Add(Me.Label19)
        Me.Controls.Add(Me.txtDescription)
        Me.Controls.Add(Me.Label18)
        Me.Controls.Add(Me.txtCarNumber)
        Me.Controls.Add(Me.cmbDeviceModel)
        Me.Controls.Add(Me.cmbDeviceBrand)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.Label6)
        Me.Controls.Add(Me.cmbRecipient)
        Me.Controls.Add(Me.GroupBox1)
        Me.Controls.Add(Me.cmbSupervisor)
        Me.Controls.Add(Me.cmbRequiredRepair)
        Me.Controls.Add(Me.Label4)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.cmbMaintenanceType)
        Me.Controls.Add(Me.cmbTypeProduct)
        Me.Controls.Add(Me.lblTypeProduct)
        Me.Controls.Add(Me.btnSettingPrint)
        Me.Controls.Add(Me.GroupBox7)
        Me.Controls.Add(Me.chkprint)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.Label8)
        Me.Controls.Add(Me.TxtHour)
        Me.Controls.Add(Me.lblMaintenanceType)
        Me.Controls.Add(Me.lblNameItems)
        Me.Controls.Add(Me.cmbCustomer)
        Me.Controls.Add(Me.btnAdd)
        Me.Controls.Add(Me.Dgv_Add)
        Me.Controls.Add(Me.dtpDate)
        Me.Controls.Add(Me.lblDate)
        Me.Controls.Add(Me.BtnDelete)
        Me.Controls.Add(Me.BtnClear)
        Me.Controls.Add(Me.Panel8)
        Me.Controls.Add(Me.Panel7)
        Me.Controls.Add(Me.Panel6)
        Me.Controls.Add(Me.Panel5)
        Me.Controls.Add(Me.txtOrderID)
        Me.Controls.Add(Me.lblOrderID)
        Me.Controls.Add(Me.lblFatherName)
        Me.Controls.Add(Me.Panel4)
        Me.Controls.Add(Me.Panel3)
        Me.Controls.Add(Me.Panel2)
        Me.Controls.Add(Me.bgHeader)
        Me.Controls.Add(Me.btnSave)
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Name = "FrmMaintenanceOrderRunningDevices"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "أوامر تشغيل الصيانة"
        Me.GroupBox7.ResumeLayout(False)
        Me.GroupBox7.PerformLayout()
        CType(Me.Dgv_Add, System.ComponentModel.ISupportInitialize).EndInit()
        Me.bgHeader.ResumeLayout(False)
        Me.bgHeader.PerformLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        CType(Me.DGVReceivingCar, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents rdoPrintPreview As System.Windows.Forms.RadioButton
    Friend WithEvents rdoPrintDirect As System.Windows.Forms.RadioButton
    Friend WithEvents btnSettingPrint As System.Windows.Forms.Button
    Friend WithEvents GroupBox7 As System.Windows.Forms.GroupBox
    Public WithEvents chkprint As System.Windows.Forms.CheckBox
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Public WithEvents lblTitel As System.Windows.Forms.Label
    Friend WithEvents Timer1 As System.Windows.Forms.Timer
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents TxtHour As System.Windows.Forms.TextBox
    Friend WithEvents lblMaintenanceType As System.Windows.Forms.Label
    Friend WithEvents lblNameItems As System.Windows.Forms.Label
    Friend WithEvents dtpTime As System.Windows.Forms.DateTimePicker
    Friend WithEvents cmbCustomer As System.Windows.Forms.ComboBox
    Friend WithEvents btnAdd As System.Windows.Forms.Button
    Friend WithEvents Dgv_Add As System.Windows.Forms.DataGridView
    Friend WithEvents dtpDate As System.Windows.Forms.DateTimePicker
    Friend WithEvents lblDate As System.Windows.Forms.Label
    Friend WithEvents BtnDelete As System.Windows.Forms.Button
    Friend WithEvents BtnClear As System.Windows.Forms.Button
    Friend WithEvents Panel8 As System.Windows.Forms.Panel
    Friend WithEvents Panel7 As System.Windows.Forms.Panel
    Friend WithEvents Panel6 As System.Windows.Forms.Panel
    Friend WithEvents Panel5 As System.Windows.Forms.Panel
    Friend WithEvents txtOrderID As System.Windows.Forms.TextBox
    Friend WithEvents lblOrderID As System.Windows.Forms.Label
    Friend WithEvents lblFatherName As System.Windows.Forms.Label
    Friend WithEvents Panel4 As System.Windows.Forms.Panel
    Friend WithEvents Panel3 As System.Windows.Forms.Panel
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Public WithEvents bgHeader As System.Windows.Forms.Panel
    Friend WithEvents btnSave As System.Windows.Forms.Button
    Friend WithEvents lblTypeProduct As System.Windows.Forms.Label
    Friend WithEvents cmbTypeProduct As System.Windows.Forms.ComboBox
    Friend WithEvents cmbMaintenanceType As System.Windows.Forms.ComboBox
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents cmbRequiredRepair As System.Windows.Forms.ComboBox
    Friend WithEvents cmbSupervisor As ComboBox
    Friend WithEvents GroupBox1 As GroupBox
    Friend WithEvents rdoLarge As RadioButton
    Friend WithEvents rdoSmall As RadioButton
    Friend WithEvents cmbRecipient As ComboBox
    Friend WithEvents cmbDeviceModel As ComboBox
    Friend WithEvents cmbDeviceBrand As ComboBox
    Friend WithEvents Label2 As Label
    Friend WithEvents Label6 As Label
    Friend WithEvents Label18 As Label
    Friend WithEvents txtCarNumber As TextBox
    Friend WithEvents Label19 As Label
    Friend WithEvents txtDescription As TextBox
    Friend WithEvents Panel9 As Panel
    Friend WithEvents btnAddReceivingCar As Button
    Friend WithEvents cmbReceivingCar As ComboBox
    Friend WithEvents Label7 As Label
    Friend WithEvents DGVReceivingCar As DataGridView
    Friend WithEvents Panel10 As Panel
    Friend WithEvents btnDeleteReceivingCar As Button
End Class
