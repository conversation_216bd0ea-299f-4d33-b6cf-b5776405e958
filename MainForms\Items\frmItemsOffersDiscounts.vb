﻿Public Class frmItemsOffersDiscounts

    Dim AlaertParcode As Boolean
    Dim ActivUpdate As Boolean = False

    Private Sub frmItemsOffersDiscounts_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        Cls.fill_combo("Stores", "store", cmbStores)
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Bra.Fil("groups", "g_name", cmbcats)
        MAXRECORD()
        cmbStores.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")

        If cmbStores.Text = "" Then
            Cls.FillComboDataAdapterQuickSearch("Items", "sname", cmbname)
        Else
            Cls.fill_combo_Stores_Where_More("Items", "sname", "Stores=N'" & cmbStores.Text & "'", cmbname)
        End If
        If cmbStores.Text = "" Then
            cmbStores.SelectedIndex = 0
        End If
        txtOffersStatement.Focus()
        txtOffersStatement.SelectAll()
        PanelViewPriceOffer.Top = 5000
        AddDay()
    End Sub

    Private Sub MAXRECORD()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from ItemsOffersDiscountsTotal"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Me.txtbillno.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(bill_No As float)) as mb FROM ItemsOffersDiscountsTotal where bill_No <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            Me.txtbillno.Text = sh + 1
        End If

    End Sub

    Private Sub txtbillno_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtbillno.TextChanged
        MyVars.CheckNumber(txtbillno)
    End Sub

    Private Sub ChkBillNo_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkBillNo.CheckedChanged
        If ChkBillNo.Checked = True Then
            txtbillno.Enabled = False
        Else
            txtbillno.Enabled = True
        End If
    End Sub

    Private Sub txtprc_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtprc.KeyDown
        If ((e.KeyCode = Keys.S) AndAlso (e.Modifiers = (Keys.Control))) Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub txtprc_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtprc.KeyUp
        If e.KeyCode = 13 Then
            AlaertParcode = True
            If txtprc.Text.Trim = "" Then
            Else
                If txtprc.Text.Trim = "" Then Exit Sub
                If Bol = False Then
                    Bol = True
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select itm_id,group_name,sname,SalPrice from items where itm_id=N'" & txtprc.Text.Trim & "' and Stores =N'" & cmbStores.Text.Trim & "'"
                    dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then
                        txtprc.Text = dr("itm_id").ToString()
                        cmbcats.Text = dr("group_name").ToString()
                        cmbname.Text = dr("sname").ToString()
                        txtprice.Text = dr("SalPrice").ToString()
                    End If

                    Bol = False
                End If
            End If

            txtRateDisc.Text = 0
            txtAfterRateDisc.Text = 0

            txtRateDisc.Focus()
            txtRateDisc.SelectAll()

            AlaertParcode = False
        End If
    End Sub

    Private Sub cmbname_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbname.SelectedIndexChanged
        If AlaertParcode = False Then
            GetDataSales()
        End If
    End Sub

    Private Sub GetDataSales()
        Bol = True

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id,group_name,SalPrice from items where sname=N'" & cmbname.Text.Trim & "' and Stores =N'" & cmbStores.Text.Trim & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            txtprc.Text = dr("itm_id").ToString()
            cmbcats.Text = dr("group_name").ToString()
            txtprice.Text = dr("SalPrice").ToString()
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id,group_name,SalPrice,Stores from items where sname=N'" & cmbname.Text.Trim & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                txtprc.Text = dr("itm_id").ToString()
                cmbcats.Text = dr("group_name").ToString()
                txtprice.Text = dr("SalPrice").ToString()
                cmbStores.Text = dr("Stores").ToString()
            End If
        End If

        txtRateDisc.Text = 0
        txtAfterRateDisc.Text = 0

        txtRateDisc.Focus()
        txtRateDisc.SelectAll()
        Bol = False
    End Sub

    Private Sub CmbMeasure_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            txtRateDisc.Focus()
            txtRateDisc.SelectAll()
        End If
    End Sub

    Private Sub txtRateDisc_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtRateDisc.KeyUp
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub txtRateDisc_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtRateDisc.TextChanged
        MyVars.CheckNumber(txtRateDisc)
        sumdisc()
    End Sub

    Private Sub txtAfterRateDisc_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtAfterRateDisc.KeyUp
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub txtAfterRateDisc_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtAfterRateDisc.TextChanged
        MyVars.CheckNumber(txtAfterRateDisc)
        sumdisc()
    End Sub

    Private Sub sumdisc()

        Dim DiscVal As Double
        If ChkCent.Checked = True Then
            DiscVal = Val((Val(txtprice.Text) * (100 - Val(txtRateDisc.Text))) / 100)
            DiscVal = Math.Round(DiscVal, 4)
        ElseIf ChkVal.Checked = True Then
            DiscVal = Val(txtprice.Text) - Val(txtRateDisc.Text)
        End If
        txtAfterRateDisc.Text = DiscVal

    End Sub

    Private Sub txtprice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtprice.TextChanged
        sumdisc()
    End Sub

    Private Sub BtnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAdd.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        If ValidateTextAdd() = False Then Exit Sub

        Dim StateDisc As String = ""
        If ChkCent.Checked = True Then
            StateDisc = "نسبة"
        Else
            StateDisc = "قيمة"
        End If

        Dgv_Add.DataSource = Fn_AddBill(txtprc.Text, cmbcats.Text, cmbname.Text, txtprice.Text, StateDisc, txtRateDisc.Text, txtAfterRateDisc.Text)
        ClearAdd()
        cmbname.Focus()
        Dgv_Add.Columns(0).Visible = False
        Dgv_Add.Columns(1).Visible = False
        Dgv_Add.Columns(5).Visible = False

    End Sub

    Dim Dt_AddBill As New DataTable
    Friend Function Fn_AddBill(ByVal Col_itm_id As String, ByVal Col_itm_cat As String, ByVal Col_Name As String, ByVal Col_Price As Double, ByVal Col_StateDisc As String, ByVal Col_RatePriceOffers As Double, ByVal Col_PriceOffers As Double) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("الباركود", GetType(String))
            Dt_AddBill.Columns.Add("المجموعة", GetType(String))
            Dt_AddBill.Columns.Add("أسم الصنف", GetType(String))
            Dt_AddBill.Columns.Add("قبل الخصم", GetType(Double))
            Dt_AddBill.Columns.Add("نوع الخصم", GetType(String))
            Dt_AddBill.Columns.Add("نسبة الخصم", GetType(Double))
            Dt_AddBill.Columns.Add("بعد الخصم", GetType(Double))
        End If

        Dt_AddBill.Rows.Add(Col_itm_id, Col_itm_cat, Col_Name, Col_Price, Col_StateDisc, Col_RatePriceOffers, Col_PriceOffers)
        Return Dt_AddBill
    End Function

    Private Sub ClearAdd()
        cmbname.Text = ""
        txtprice.Text = "0"
        txtprc.Text = ""
        txtprice.Text = "0"
        txtRateDisc.Text = "0"
        txtAfterRateDisc.Text = "0"
    End Sub

    Function ValidateTextAdd() As Boolean
        If txtprc.Text = "" Then MsgBox("فضلا أدخل الباركود", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
        If cmbStores.Text = "" Then MsgBox("فضلا أختر أسم المخزن", MsgBoxStyle.Exclamation) : cmbStores.Focus() : Return False
        If cmbcats.Text = "" Then MsgBox("فضلا أختر مجموعة الصنف", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbname.Text = "" Then MsgBox("فضلا أختر الصنف", MsgBoxStyle.Exclamation) : cmbname.Focus() : Return False
        If txtbillno.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الفاتورة", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        If txtRateDisc.Text.Trim = "" Then MsgBox("فضلا أدخل نسبة او قيمة الخصم", MsgBoxStyle.Exclamation) : txtRateDisc.Focus() : Return False
        If txtOffersStatement.Text = "" Then MsgBox("فضلا أدخل بيان العروض", MsgBoxStyle.Exclamation) : txtOffersStatement.Focus() : Return False
        If Val(txtqu_total.Text) <= 0 Then MsgBox("فضلا أدخل كمية العرض", MsgBoxStyle.Exclamation) : txtqu_total.Focus() : Return False

        If txtqu_total.Text = 1 Or txtqu_total.Text = 0 Then
            If Dgv_Add.Rows.Count = 1 Then
                MsgBox("لايمكن اضافة صنف اخر فى العرض لانه تم تحديد كمية واحد للصنف ولا ينطبق علية عرض على الكميات", MsgBoxStyle.Exclamation)
                cmbname.Focus() : Return False
            End If
        End If


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from ItemsOffersDiscounts where itm_id =N'" & txtprc.Text.Trim & "' and sname =N'" & cmbname.Text.Trim & "' and ActionOffers=0" : H = cmd.ExecuteScalar
        If H = 1 Then
            MsgBox("هذا الصنف بالفعل يوجد علية عرض  لم تنتهى مدتة", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
        End If

        Return True
        End Function


    Function ValidateTextSave() As Boolean
        If txtbillno.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الفاتورة", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        If txtOffersStatement.Text.Trim = "" Then MsgBox("فضلا أدخل بيان العروض", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        If Val(txtqu_total.Text) <= 0 Then MsgBox("فضلا أدخل كمية العرض", MsgBoxStyle.Exclamation) : txtqu_total.Focus() : Return False

        Return True
    End Function

    Private Sub ClearSave()
        cmbname.Text = ""
        txtprice.Text = "0"
        txtprc.Text = ""
        txtqu_total.Text = "0"
        txtprice.Text = "0"
        txtRateDisc.Text = "0"
        txtAfterRateDisc.Text = "0"
        txtOffersStatement.Text = ""
    End Sub

    Private Sub ChkVal_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkVal.CheckedChanged
        sumdisc()
    End Sub

    Private Sub ChkCent_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkCent.CheckedChanged
        sumdisc()
    End Sub


    Dim RNXD As Integer
    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        On Error Resume Next

        If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        RNXD = Dgv_Add.CurrentRow.Index
        Dgv_Add.Rows.RemoveAt(RNXD)

        'If NetworkName = "Yes" Then
        '    If UseExternalServer = "Yes" Then
        '        connect()
        '    End If
        'End If
        'If Dgv_Add.Rows.Count = 0 Then
        '    MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
        '    Exit Sub
        'End If

        'Dim x As String = MsgBox("هل تريد بالفعل حذف العرض", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        'If x = vbCancel Then Exit Sub

        'For i As Integer = 0 To Dgv_Add.SelectedRows.Count - 1

        '    If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        '    If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        '    Dim ItmID As String
        '    ItmID = Dgv_Add.SelectedRows(i).Cells(0).Value
        '    dr.Close()

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "delete from ItemsOffersDiscounts where bill_No =N'" & ItmID & "'" : cmd.ExecuteNonQuery()

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    S = "update Items set RatePriceOffers =0, PriceOffers =0 where sname =N'" & cmbname.Text & "'"
        '    cmd.CommandText = S : cmd.ExecuteNonQuery()

        'Next
        'Heddier()
    End Sub

    Private Sub cmbcats_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbcats.DropDown
        cmbname.Text = ""
        cmbcats.Text = ""
        txtprc.Text = ""
        txtAfterRateDisc.Text = 0
        txtRateDisc.Text = 0
        txtprice.Text = 0
    End Sub

    Private Sub cmbcats_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbcats.SelectedIndexChanged
        If AlaertParcode = False Then
            If Bol = False Then
                If cmbcats.Text.Trim = "" Then Exit Sub
                Cls.fill_combo_Stores_Where_More("Items", "sname", "group_name =N'" & cmbcats.Text.Trim & "'", cmbname)
                cmbname.Text = ""
            End If
        End If
    End Sub

    Private Sub cmbStores_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbStores.SelectedIndexChanged
        If txtprc.Text = "" Then
            Cls.fill_combo_Stores_Where_More("Items", "sname", "Stores=N'" & cmbStores.Text & "'", cmbname)
        End If
    End Sub

    Private Sub cmbname_DropDown(sender As Object, e As EventArgs) Handles cmbname.DropDown
        cmbname.Text = ""
        txtprice.Text = "0"
        txtRateDisc.Text = "0"
        txtAfterRateDisc.Text = "0"
        txtprc.Text = ""
    End Sub

    Private Sub btnSaveAll_Click(sender As Object, e As EventArgs) Handles btnSaveAll.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateTextSave() = False Then Exit Sub

        If ActivUpdate = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete From  ItemsOffersDiscounts where bill_no =N'" & txtbillno.Text.Trim & "'" : cmd.ExecuteNonQuery()
        End If

        If ActivUpdate = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsOffersDiscountsTotal set OffersStatement =N'" & txtOffersStatement.Text.Trim & "',Company_Branch_ID =N'" & Company_Branch_ID & "',bill_date =N'" & Cls.C_date(dtpbill_date_Start.Text) & "',bill_date_End =N'" & Cls.C_date(dtpbill_date_End.Text) & "',qu_total =N'" & Val(txtqu_total.Text.Trim) & "',UserName =N'" & UserName & "',ActionOffers =N'0' where bill_no =N'" & txtbillno.Text.Trim & "'" : cmd.ExecuteNonQuery()
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into ItemsOffersDiscountsTotal (Company_Branch_ID,OffersStatement,bill_no,qu_total,bill_date,bill_date_End,UserName,ActionOffers)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & txtOffersStatement.Text.Trim & "',N'" & txtbillno.Text.Trim & "',N'" & txtqu_total.Text.Trim & "',N'" & Cls.C_date(dtpbill_date_Start.Text) & "',N'" & Cls.C_date(dtpbill_date_End.Text) & "',N'" & UserName & "',0)"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If

        For i As Integer = 0 To Dgv_Add.Rows.Count - 1

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into ItemsOffersDiscounts (Company_Branch_ID,bill_no,qu,itm_id,sname,PriceOffersBefore,StateDisc,RatePriceOffers,PriceOffers,bill_date,bill_date_End,UserName,ActionOffers)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "',N'" & Val(txtqu_total.Text) & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Val(Dgv_Add.Rows(i).Cells(3).Value) & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Val(Dgv_Add.Rows(i).Cells(5).Value) & "',N'" & Val(Dgv_Add.Rows(i).Cells(6).Value) & "',N'" & Cls.C_date(dtpbill_date_Start.Text) & "',N'" & Cls.C_date(dtpbill_date_End.Text) & "',N'" & UserName & "',0)"
            cmd.CommandText = S : cmd.ExecuteNonQuery()


            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "update Items set RatePriceOffers = " & Dgv_Add.Rows(i).Cells(5).Value & " , PriceOffers = " & Dgv_Add.Rows(i).Cells(6).Value & " where sname =N'" & cmbname.Text & "'"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

        Next

        Dt_AddBill.Rows.Clear()
        btnSaveAll.Text = "حــــفظ"
        MAXRECORD()
        MsgBox("تمت عملية تسجيل عروض الخصومات بنجاح", MsgBoxStyle.Information)
        cmbname.Focus()

    End Sub

    Private Sub btnView_Click(sender As Object, e As EventArgs) Handles btnView.Click
        PanelViewPriceOffer.Top = 20
        PanelViewPriceOffer.Dock = DockStyle.Fill
        btnShow_Click(sender, e)
    End Sub

    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        ActivUpdate = False
        btnSaveAll.Text = "حــــفظ"
        ClearSave()
        MAXRECORD()
        Dt_AddBill.Rows.Clear()
    End Sub

    Private Sub txtqu_KeyUp(sender As Object, e As KeyEventArgs) Handles txtqu_total.KeyUp
        If e.KeyCode = 13 Then
            cmbname.Focus()
            cmbname.SelectAll()
        End If
    End Sub

    Private Sub txtqu_total_TextChanged(sender As Object, e As EventArgs) Handles txtqu_total.TextChanged
        MyVars.CheckNumber(txtqu_total)
    End Sub

    Private Sub btnShow_Click(sender As Object, e As EventArgs) Handles btnShow.Click
        DataGridView1.DataSource = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = Cls.Get_Select_Grid_S("id as [ID],bill_no as [رقم العملية],OffersStatement as [بيان العروض],bill_date as [تاريخ بداية العرض],bill_date_End as [تاريخ نهاية العرض],qu_total as [الكمية الاجمالية]", "ItemsOffersDiscountsTotal", "id <>N''")
        If chkAll.Checked = False Then
            If txtbillnoView.Text <> "" Then
                S = S & " and bill_no =N'" & txtbillnoView.Text.Trim & "'"
            End If
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم العملية]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [تاريخ بداية العرض]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [بيان العروض]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
        DataGridView1.Columns(1).Width = 90
        DataGridView1.Columns(2).Width = 120

        DataGridView1.Columns(0).Visible = False

        ActivUpdate = True
        btnSaveAll.Text = "تـعديل"
    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            txtbillnoView.Enabled = False
        Else
            txtbillnoView.Enabled = True
        End If
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(sender As Object, e As EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub btnDeleteAll_Click(sender As Object, e As EventArgs) Handles btnDeleteAll.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim bill_no As String
        Dim ItemName As String
        Dim aray_1 As New ArrayList

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            bill_no = DataGridView1.SelectedRows(i).Cells(1).Value


            aray_1.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select sname from ItemsOffersDiscounts where bill_no =N'" & bill_no & "'"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_1.Add(dr(0))
            Loop

            For M As Integer = 0 To aray_1.Count - 1
                ItemName = aray_1(M).ToString
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update Items set RatePriceOffers =0, PriceOffers =0 where sname =N'" & ItemName & "'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete From  ItemsOffersDiscounts where bill_no =N'" & bill_no & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  ItemsOffersDiscountsTotal where bill_no =N'" & bill_no & "'" : cmd.ExecuteNonQuery()
        Next

        btnShow_Click(sender, e)
    End Sub

    Private Sub DataGridView1_DoubleClick(sender As Object, e As EventArgs) Handles DataGridView1.DoubleClick
        Try
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            txtbillno.Text = DataGridView1.SelectedRows(0).Cells(1).Value

            PanelViewPriceOffer.Dock = DockStyle.None
            PanelViewPriceOffer.Top = 5000

            Dt_AddBill.Rows.Clear()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from ItemsOffersDiscountsTotal where bill_no =N'" & txtbillno.Text & "'"
            dr = cmd.ExecuteReader
            If dr.Read Then
                txtOffersStatement.Text = dr("OffersStatement").ToString
                dtpbill_date_Start.Text = Cls.R_date(dr("bill_date").ToString())
                dtpbill_date_End.Text = Cls.R_date(dr("bill_date_End").ToString())
                txtqu_total.Text = dr("qu_total").ToString
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select itm_id,group_name,sname,PriceOffersBefore,StateDisc,RatePriceOffers,PriceOffers from View_ItemsOffersDiscounts where bill_no =N'" & txtbillno.Text & "'  order by 1"
            dr = cmd.ExecuteReader
            Do While dr.Read
                Dgv_Add.DataSource = Fn_AddBill(dr("itm_id").ToString, dr("group_name").ToString, dr("sname").ToString, dr("PriceOffersBefore").ToString, dr("StateDisc").ToString, dr("RatePriceOffers").ToString, dr("PriceOffers").ToString)
            Loop

            Dgv_Add.Columns(0).Visible = False
            Dgv_Add.Columns(1).Visible = False
            Dgv_Add.Columns(5).Visible = False

            ActivUpdate = True
            btnSaveAll.Text = "تـعديل"
        Catch ex As Exception
        End Try
    End Sub

    Private Sub btnCloseAdjustmentsStores_Click(sender As Object, e As EventArgs) Handles btnCloseAdjustmentsStores.Click
        ActivUpdate = False
        btnSaveAll.Text = "حــــفظ"
        PanelViewPriceOffer.Dock = DockStyle.None
        PanelViewPriceOffer.Top = 5000
    End Sub

    Private Sub AddDay()
        Dim today As DateTime = dtpbill_date_Start.Text
        Dim dueDate As DateTime = today.AddDays(1)
        dtpbill_date_End.Text = dueDate
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class