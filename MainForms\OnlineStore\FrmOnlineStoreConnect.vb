﻿Public Class FrmOnlineStoreConnect
    Private Sub FrmConnectOnlineStore_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Dim ConnectOnlineStore As String = mykey.GetValue("ConnectOnlineStore", "NO")
        If ConnectOnlineStore = "NO" Then
            rdoOnlineStoreNO.Checked = True
        End If
        If ConnectOnlineStore = "YES" Then
            rdoOnlineStoreYES.Checked = True
        End If

        cmbAllowWithdrawalAfterRequestCustomer.Text = mykey.GetValue("AllowWithdrawalAfterRequestCustomer", "NO")
        txtDataBaseNameOnlineStore.Text = mykey.GetValue("DataBaseNameOnlineStore", "")
        txtServerNameOnlineStore.Text = mykey.GetValue("ServerNameOnlineStore", "")
        txtUserNameOnlineStore.Text = mykey.GetValue("UserNameOnlineStore", "")
        txtPasswordOnlineStore.Text = mykey.GetValue("PasswordOnlineStore", "")

        GetServerData()
        PanelServerData.Top = 5000
    End Sub

    Private Sub ButtonAPPLY_Click(sender As Object, e As EventArgs) Handles ButtonAPPLY.Click
        'If CheckForInternetConnection() = False Then
        '    MsgBox("تأكد من الاتصال بالانترنت", MsgBoxStyle.Information)
        '    Exit Sub
        'End If

        If rdoOnlineStoreYES.Checked = True Then
            mykey.SetValue("ConnectOnlineStore", "YES")
            NetworkNameInternet = "Yes"
        ElseIf rdoOnlineStoreNO.Checked = True Then
            mykey.SetValue("ConnectOnlineStore", "NO")
            NetworkNameInternet = "NO"
        End If

        mykey.SetValue("AllowWithdrawalAfterRequestCustomer", cmbAllowWithdrawalAfterRequestCustomer.Text)
        AllowWithdrawalAfterRequestCustomer = cmbAllowWithdrawalAfterRequestCustomer.Text

        mykey.SetValue("DataBaseNameOnlineStore", txtDataBaseNameOnlineStore.Text)
        DataBaseNameOnlineStore = txtDataBaseNameOnlineStore.Text
        mykey.SetValue("ServerNameOnlineStore", txtServerNameOnlineStore.Text)
        ServerNameOnlineStore = txtServerNameOnlineStore.Text
        mykey.SetValue("UserNameOnlineStore", txtUserNameOnlineStore.Text)
        UserNameOnlineStore = txtUserNameOnlineStore.Text
        mykey.SetValue("PasswordOnlineStore", txtPasswordOnlineStore.Text)
        PasswordOnlineStore = txtPasswordOnlineStore.Text

        Me.Close()
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Me.Close()
    End Sub

    Private Sub btnSyncPricesAndStore_Click(sender As Object, e As EventArgs) Handles btnSyncPricesAndStore.Click
        FrmOnlineStoreProductUpdate.ShowDialog()
    End Sub

    Private Sub Image1_Click(sender As Object, e As EventArgs) Handles Image1.Click
        PanelServerData.Top = 80
        PanelServerData.Left = 12
        PanelServerData.Size = New System.Drawing.Size(750, 334)
    End Sub

    Private Sub btnAddServerData_Click(sender As Object, e As EventArgs) Handles btnAddServerData.Click
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from ConnectOnlineStore where DataBaseName =N'" & txtDataBaseNameOnlineStore.Text.Trim & "' and ServerName =N'" & txtServerNameOnlineStore.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H > 0 Then
            MsgBox(" البيانات مسجلة مسبقا", MsgBoxStyle.Exclamation) : txtDataBaseNameOnlineStore.Focus() : Exit Sub
        End If

        Dim OnlineStoreActive As String = ""
        If rdoOnlineStoreYES.Checked = True Then
            OnlineStoreActive = "YES"
        Else
            OnlineStoreActive = "NO"
        End If
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into ConnectOnlineStore (DataBaseName,ServerName,UserName,Password,OnlineStoreActive)"
        S = S & " values (N'" & txtDataBaseNameOnlineStore.Text & "',N'" & txtServerNameOnlineStore.Text & "',N'" & txtUserNameOnlineStore.Text & "',N'" & txtPasswordOnlineStore.Text.Trim & "',N'" & OnlineStoreActive & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        GetServerData()

    End Sub

    Private Sub GetServerData()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = Cls.Get_Select_Grid_S_OrderBy_Name("id as [رقم],DataBaseName  as [قاعدة البيانات], ServerName as [أسم السيرفر], UserName as [أسم المستخدم] ,Password as [الباسورد],OnlineStoreActive as [تفعيل]", "ConnectOnlineStore", "id<>''", "Id")
            cmd.CommandText = S : dr = cmd.ExecuteReader
            dgvServerData.DataSource = Cls.PopulateDataView(dr)
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub btnDelServerData_Click(sender As Object, e As EventArgs) Handles btnDelServerData.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If dgvServerData.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If

        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To dgvServerData.SelectedRows.Count - 1
            If dgvServerData.RowCount = 0 Then Beep() : Exit Sub
            If (dgvServerData.SelectedRows.Count) = 0 Then Beep() : Exit Sub

            Dim DataBaseName As String = dgvServerData.SelectedRows(i).Cells(0).Value
            Dim ServerName As String = dgvServerData.SelectedRows(i).Cells(1).Value

            Try
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete from Items where DataBaseName =N'" & DataBaseName & "' and ServerName =N'" & ServerName & "'" : cmd.ExecuteNonQuery()

            Catch ex As Exception
                ErrorHandling(ex, Me.Text)
            End Try
        Next
        GetServerData()
    End Sub

    Private Sub btnCloseServerData_Click(sender As Object, e As EventArgs) Handles btnCloseServerData.Click
        PanelServerData.Top = 5000
    End Sub

    Private Sub dgvServerData_DoubleClick(sender As Object, e As EventArgs) Handles dgvServerData.DoubleClick
        If dgvServerData.RowCount = 0 Then Beep() : Exit Sub
        If (dgvServerData.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        txtDataBaseNameOnlineStore.Text = dgvServerData.SelectedRows(0).Cells(1).Value
        txtServerNameOnlineStore.Text = dgvServerData.SelectedRows(0).Cells(2).Value
        txtUserNameOnlineStore.Text = dgvServerData.SelectedRows(0).Cells(3).Value
        txtPasswordOnlineStore.Text = dgvServerData.SelectedRows(0).Cells(4).Value
        Dim OnlineStoreActive As String = dgvServerData.SelectedRows(0).Cells(5).Value
        If OnlineStoreActive = "YES" Then
            rdoOnlineStoreYES.Checked = True
        Else
            rdoOnlineStoreNO.Checked = True
        End If
    End Sub

    Private Sub btnOrdersOnlineStore_Click(sender As Object, e As EventArgs) Handles btnOrdersOnlineStore.Click
        FrmOnlineStoreOrders.Show()
    End Sub

    Private Sub FrmOnlineStoreConnect_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        Cn.Close()
        connect()
    End Sub
End Class