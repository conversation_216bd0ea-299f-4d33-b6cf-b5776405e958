﻿Public Class FrmOnlineStoreGovernorate
    Dim Area_ID As String
    Dim ActivEdit As Boolean = False

    Private Sub Frm_Group_Branch_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Cos.Fill_ComboBox_OnlineStore("[Governorate] Where (IsDelete = 0)", "Name", cmbGovernorate)
        Cos.Fill_ComboBox_OnlineStore("[Governorate] Where (IsDelete = 0)", "Name", cmbGovernorateView)
        Headerx()
        PanelAddNew.Dock = DockStyle.None
        PanelAddNew.Top = 5000
    End Sub

    Private Sub btnsave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnsave.Click
        If ValidateSave() = False Then Exit Sub

        Dim HideGovernorate As Integer
        If chkIsHide.Checked = True Then
            HideGovernorate = 1
        Else
            HideGovernorate = 0
        End If

        If Not ConnectingOnlineStore() Is Nothing Then
            If ActivEdit = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Governorate set Name =N'" & cmbGovernorate.Text & "',HideGovernorate =N'" & HideGovernorate & "' where Id= '" & txtID.Text & "'"
                cmd.ExecuteNonQuery()
                Cn.Close()
                connect()
            Else
                MAXRECORD()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Governorate (Id,Name,HideGovernorate,IsDelete,CountryId)"
                S = S & " values (N'" & txtID.Text.Trim & "',N'" & cmbGovernorate.Text.Trim & "',N'" & HideGovernorate & "',N'0',N'1')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
                Cn.Close()
                connect()
            End If
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If

        cmbGovernorate.Text = ""
        cmbGovernorate.Text = ""
        ActivEdit = False
        btnsave.Text = "إضافة"
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.save_32

        PanelAddNew.Dock = DockStyle.None
        PanelAddNew.Top = 5000
        Headerx()

    End Sub

    Private Sub MAXRECORD()
        If Not ConnectingOnlineStore() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from Governorate"
            dr = cmd.ExecuteReader

            Dim dt As New DataTable
            dt.Load(dr)

            If dt.Rows.Count = 0 Then
                txtID.Text = 1
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(CAST(Id As float)) as mb FROM Governorate where Id <> N''"
                dr = cmd.ExecuteReader
                dr.Read()

                Dim sh As Long
                sh = dr("mb")
                txtID.Text = sh + 1
            End If
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Sub

    Function ValidateSave() As Boolean

        If cmbGovernorate.Text = "" Then MsgBox("فضلا أدخل المحافظة", MsgBoxStyle.Exclamation) : cmbGovernorate.Focus() : Return False

        If Not ConnectingOnlineStore() Is Nothing Then
            If ActivEdit = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select count(*) from Governorate where Name =N'" & cmbGovernorate.Text.Trim & "'" : H = cmd.ExecuteScalar
                If H > 0 Then
                    MsgBox(" المحافظة مسجلة مسبقاً بنفس الاسم", MsgBoxStyle.Exclamation) : cmbGovernorate.Focus() : Return False
                End If
            End If
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If
        Return True
    End Function

    Private Sub Headerx()
        If Not ConnectingOnlineStore() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "Select Id As [كود المحافظة], Name As [المحافظة], HideGovernorate As [المحافظة المخفية], IsDelete From dbo.Governorate Where (Id <> '')"
            If chkAll.Checked = False Then
                If cmbGovernorateView.Text <> "" Then
                    S = S & " And Governorate.Name =N'" & cmbGovernorateView.Text.Trim & "'"
                End If
            End If

            If chkIsDelete.Checked = True Then
                S = S & " and IsDelete =N'True'"
            Else
                S = S & " and IsDelete =N'False'"
            End If
            S = S & " order by Id"
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)
            'DTGV.Columns(0).Width = 80
            'DTGV.Columns(1).Width = 100
            'DTGV.Columns(2).Width = 100
            'DTGV.Columns(3).Width = 100
            DTGV.Columns(3).Visible = False
            txtNumber.Text = DTGV.RowCount
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Sub

    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click

        If Not ConnectingOnlineStore() Is Nothing Then
            If DTGV.Rows.Count = 0 Then
                MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
                Exit Sub
            End If
            Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
            If x = vbCancel Then Exit Sub
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To DTGV.SelectedRows.Count - 1
                If DTGV.RowCount = 0 Then Beep() : Exit Sub
                If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
                Dim ItmID As String
                ItmID = DTGV.SelectedRows(i).Cells(0).Value

                cmd.CommandText = "update Governorate set IsDelete =N'1' where Id= '" & ItmID & "'"
                cmd.ExecuteNonQuery()
                'cmd.CommandText = "delete from Governorate where Id =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
            Next
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If

        Headerx()
        ActivEdit = False
        btnsave.Text = "إضافة"
    End Sub

    Private Sub cmbcats_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbGovernorate.KeyUp
        If e.KeyCode = 13 Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub cmbbranch_Name_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub chkIsDelete_CheckedChanged(sender As Object, e As EventArgs) Handles chkIsDelete.CheckedChanged
        Headerx()
    End Sub

    Private Sub DTGV_DoubleClick(sender As Object, e As EventArgs) Handles DTGV.DoubleClick
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        ActivEdit = True
        txtID.Text = DTGV.SelectedRows(0).Cells(0).Value
        cmbGovernorate.Text = DTGV.SelectedRows(0).Cells(1).Value
        Dim HideGovernorate As String = DTGV.SelectedRows(0).Cells(2).Value
        If HideGovernorate = "True" Then
            chkIsHide.Checked = True
        Else
            chkIsHide.Checked = False
        End If
        btnsave.Text = "تعديل"
        ActivEdit = True
        PanelAddNew.Top = 20
        PanelAddNew.Dock = DockStyle.Fill
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.Edit_1

    End Sub

    Private Sub cmbArea_DropDown(sender As Object, e As EventArgs)
        ActivEdit = False
        btnsave.Text = "إضافة"
    End Sub

    Private Sub cmbArea_SelectedIndexChanged(sender As Object, e As EventArgs)
        Headerx()
    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbGovernorateView.Enabled = False
        Else
            cmbGovernorateView.Enabled = True
        End If
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        PanelAddNew.Dock = DockStyle.None
        PanelAddNew.Top = 5000
        ActivEdit = False
        btnsave.Text = "إضافة"
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.save_32
    End Sub

    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        PanelAddNew.Top = 20
        PanelAddNew.Dock = DockStyle.Fill
        cmbGovernorate.Focus()
        ActivEdit = False
        btnsave.Text = "إضافة"
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.save_32
        cmbGovernorate.Text = ""
        cmbGovernorate.Text = ""
        MAXRECORD()
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs) Handles btnEdit.Click
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        ActivEdit = True
        txtID.Text = DTGV.SelectedRows(0).Cells(0).Value
        cmbGovernorate.Text = DTGV.SelectedRows(0).Cells(1).Value
        Dim HideGovernorate As String = DTGV.SelectedRows(0).Cells(2).Value
        If HideGovernorate = "True" Then
            chkIsHide.Checked = True
        Else
            chkIsHide.Checked = False
        End If

        btnsave.Text = "تعديل"
        ActivEdit = True
        PanelAddNew.Top = 20
        PanelAddNew.Dock = DockStyle.Fill
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.Edit_1
    End Sub

    Private Sub btnShow_Click(sender As Object, e As EventArgs) Handles btnShow.Click
        Headerx()
    End Sub

End Class