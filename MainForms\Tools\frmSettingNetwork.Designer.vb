﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmSettingNetwork
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmSettingNetwork))
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.txtDataBaseName = New System.Windows.Forms.ComboBox()
        Me.btnDeleteDataBaseName = New System.Windows.Forms.Button()
        Me.btnDeleteServerName = New System.Windows.Forms.Button()
        Me.btnAddDataBaseName = New System.Windows.Forms.Button()
        Me.txtServerName = New System.Windows.Forms.ComboBox()
        Me.btnAddServerName = New System.Windows.Forms.Button()
        Me.chkSettingNetworkDefault = New System.Windows.Forms.CheckBox()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.rdoLocalDB = New System.Windows.Forms.RadioButton()
        Me.rdoSource = New System.Windows.Forms.RadioButton()
        Me.rdoYesNetwork = New System.Windows.Forms.RadioButton()
        Me.rdoNoNetwork = New System.Windows.Forms.RadioButton()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.rdoYES_ExternalServer = New System.Windows.Forms.RadioButton()
        Me.rdoNO_ExternalServer = New System.Windows.Forms.RadioButton()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label21 = New System.Windows.Forms.Label()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.txtUserName = New System.Windows.Forms.TextBox()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.btnAttachDatabase = New System.Windows.Forms.Button()
        Me.OpenFileDialog1 = New System.Windows.Forms.OpenFileDialog()
        Me.Button1 = New System.Windows.Forms.Button()
        Me.ButtonAPPLY = New System.Windows.Forms.Button()
        Me.btnSettingNetworkDefaultBack = New System.Windows.Forms.Button()
        Me.MenuStrip = New System.Windows.Forms.MenuStrip()
        Me.شToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripMenuItemRolesTrue = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripMenuItemRolesFalse = New System.Windows.Forms.ToolStripMenuItem()
        Me.btnHelp = New System.Windows.Forms.Button()
        Me.PanelHelp = New System.Windows.Forms.Panel()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label23 = New System.Windows.Forms.Label()
        Me.Panel19 = New System.Windows.Forms.Panel()
        Me.Panel20 = New System.Windows.Forms.Panel()
        Me.Panel21 = New System.Windows.Forms.Panel()
        Me.Panel22 = New System.Windows.Forms.Panel()
        Me.Label24 = New System.Windows.Forms.Label()
        Me.btnCloseHelp = New System.Windows.Forms.Button()
        Me.btnLocalDB = New System.Windows.Forms.Button()
        Me.PanelLocalDB = New System.Windows.Forms.Panel()
        Me.txtLocalDB = New System.Windows.Forms.TextBox()
        Me.Panel4 = New System.Windows.Forms.Panel()
        Me.Panel5 = New System.Windows.Forms.Panel()
        Me.Panel6 = New System.Windows.Forms.Panel()
        Me.Panel7 = New System.Windows.Forms.Panel()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.btnCloseLocalDB = New System.Windows.Forms.Button()
        Me.PanelServerData = New System.Windows.Forms.Panel()
        Me.btnDelServerData = New System.Windows.Forms.Button()
        Me.btnAddServerData = New System.Windows.Forms.Button()
        Me.dgvServerData = New System.Windows.Forms.DataGridView()
        Me.Panel8 = New System.Windows.Forms.Panel()
        Me.Panel9 = New System.Windows.Forms.Panel()
        Me.Panel10 = New System.Windows.Forms.Panel()
        Me.Panel11 = New System.Windows.Forms.Panel()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.btnCloseServerData = New System.Windows.Forms.Button()
        Me.txtPassword = New System.Windows.Forms.TextBox()
        Me.GroupBox2.SuspendLayout()
        Me.Panel2.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.MenuStrip.SuspendLayout()
        Me.PanelHelp.SuspendLayout()
        Me.Panel22.SuspendLayout()
        Me.PanelLocalDB.SuspendLayout()
        Me.Panel7.SuspendLayout()
        Me.PanelServerData.SuspendLayout()
        CType(Me.dgvServerData, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel11.SuspendLayout()
        Me.SuspendLayout()
        '
        'GroupBox2
        '
        Me.GroupBox2.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox2.Controls.Add(Me.txtPassword)
        Me.GroupBox2.Controls.Add(Me.txtDataBaseName)
        Me.GroupBox2.Controls.Add(Me.btnDeleteDataBaseName)
        Me.GroupBox2.Controls.Add(Me.btnDeleteServerName)
        Me.GroupBox2.Controls.Add(Me.btnAddDataBaseName)
        Me.GroupBox2.Controls.Add(Me.txtServerName)
        Me.GroupBox2.Controls.Add(Me.btnAddServerName)
        Me.GroupBox2.Controls.Add(Me.chkSettingNetworkDefault)
        Me.GroupBox2.Controls.Add(Me.Panel2)
        Me.GroupBox2.Controls.Add(Me.Panel1)
        Me.GroupBox2.Controls.Add(Me.Label1)
        Me.GroupBox2.Controls.Add(Me.Label7)
        Me.GroupBox2.Controls.Add(Me.Label21)
        Me.GroupBox2.Controls.Add(Me.Label14)
        Me.GroupBox2.Controls.Add(Me.Label16)
        Me.GroupBox2.Controls.Add(Me.txtUserName)
        Me.GroupBox2.Controls.Add(Me.Label15)
        Me.GroupBox2.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.GroupBox2.Location = New System.Drawing.Point(25, 27)
        Me.GroupBox2.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Padding = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.GroupBox2.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.GroupBox2.Size = New System.Drawing.Size(776, 340)
        Me.GroupBox2.TabIndex = 471
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "خصائص الدخول على السيرفر"
        '
        'txtDataBaseName
        '
        Me.txtDataBaseName.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.txtDataBaseName.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.txtDataBaseName.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.txtDataBaseName.FormattingEnabled = True
        Me.txtDataBaseName.Location = New System.Drawing.Point(87, 127)
        Me.txtDataBaseName.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtDataBaseName.Name = "txtDataBaseName"
        Me.txtDataBaseName.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.txtDataBaseName.Size = New System.Drawing.Size(396, 36)
        Me.txtDataBaseName.TabIndex = 563
        '
        'btnDeleteDataBaseName
        '
        Me.btnDeleteDataBaseName.BackColor = System.Drawing.Color.Transparent
        Me.btnDeleteDataBaseName.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Delete3
        Me.btnDeleteDataBaseName.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnDeleteDataBaseName.Location = New System.Drawing.Point(13, 131)
        Me.btnDeleteDataBaseName.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnDeleteDataBaseName.Name = "btnDeleteDataBaseName"
        Me.btnDeleteDataBaseName.Size = New System.Drawing.Size(33, 34)
        Me.btnDeleteDataBaseName.TabIndex = 562
        Me.btnDeleteDataBaseName.UseVisualStyleBackColor = False
        '
        'btnDeleteServerName
        '
        Me.btnDeleteServerName.BackColor = System.Drawing.Color.Transparent
        Me.btnDeleteServerName.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Delete3
        Me.btnDeleteServerName.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnDeleteServerName.Location = New System.Drawing.Point(13, 170)
        Me.btnDeleteServerName.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnDeleteServerName.Name = "btnDeleteServerName"
        Me.btnDeleteServerName.Size = New System.Drawing.Size(33, 34)
        Me.btnDeleteServerName.TabIndex = 561
        Me.btnDeleteServerName.UseVisualStyleBackColor = False
        '
        'btnAddDataBaseName
        '
        Me.btnAddDataBaseName.BackColor = System.Drawing.Color.Transparent
        Me.btnAddDataBaseName.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Items_add_5
        Me.btnAddDataBaseName.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnAddDataBaseName.Location = New System.Drawing.Point(50, 131)
        Me.btnAddDataBaseName.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnAddDataBaseName.Name = "btnAddDataBaseName"
        Me.btnAddDataBaseName.Size = New System.Drawing.Size(33, 34)
        Me.btnAddDataBaseName.TabIndex = 560
        Me.btnAddDataBaseName.UseVisualStyleBackColor = False
        '
        'txtServerName
        '
        Me.txtServerName.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.txtServerName.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.txtServerName.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.txtServerName.FormattingEnabled = True
        Me.txtServerName.Location = New System.Drawing.Point(87, 169)
        Me.txtServerName.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtServerName.Name = "txtServerName"
        Me.txtServerName.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.txtServerName.Size = New System.Drawing.Size(396, 36)
        Me.txtServerName.TabIndex = 559
        '
        'btnAddServerName
        '
        Me.btnAddServerName.BackColor = System.Drawing.Color.Transparent
        Me.btnAddServerName.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Items_add_5
        Me.btnAddServerName.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnAddServerName.Location = New System.Drawing.Point(49, 170)
        Me.btnAddServerName.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnAddServerName.Name = "btnAddServerName"
        Me.btnAddServerName.Size = New System.Drawing.Size(33, 34)
        Me.btnAddServerName.TabIndex = 557
        Me.btnAddServerName.UseVisualStyleBackColor = False
        '
        'chkSettingNetworkDefault
        '
        Me.chkSettingNetworkDefault.AutoSize = True
        Me.chkSettingNetworkDefault.Checked = True
        Me.chkSettingNetworkDefault.CheckState = System.Windows.Forms.CheckState.Checked
        Me.chkSettingNetworkDefault.Location = New System.Drawing.Point(113, 296)
        Me.chkSettingNetworkDefault.Name = "chkSettingNetworkDefault"
        Me.chkSettingNetworkDefault.Size = New System.Drawing.Size(222, 30)
        Me.chkSettingNetworkDefault.TabIndex = 475
        Me.chkSettingNetworkDefault.Text = "إعدادت الشبكة الافتراضية"
        Me.chkSettingNetworkDefault.UseVisualStyleBackColor = True
        Me.chkSettingNetworkDefault.Visible = False
        '
        'Panel2
        '
        Me.Panel2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel2.Controls.Add(Me.rdoLocalDB)
        Me.Panel2.Controls.Add(Me.rdoSource)
        Me.Panel2.Controls.Add(Me.rdoYesNetwork)
        Me.Panel2.Controls.Add(Me.rdoNoNetwork)
        Me.Panel2.Location = New System.Drawing.Point(87, 36)
        Me.Panel2.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(396, 41)
        Me.Panel2.TabIndex = 474
        '
        'rdoLocalDB
        '
        Me.rdoLocalDB.AutoSize = True
        Me.rdoLocalDB.Checked = True
        Me.rdoLocalDB.Location = New System.Drawing.Point(-1, 5)
        Me.rdoLocalDB.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.rdoLocalDB.Name = "rdoLocalDB"
        Me.rdoLocalDB.Size = New System.Drawing.Size(90, 30)
        Me.rdoLocalDB.TabIndex = 472
        Me.rdoLocalDB.TabStop = True
        Me.rdoLocalDB.Text = "LocalDB"
        Me.rdoLocalDB.UseVisualStyleBackColor = True
        '
        'rdoSource
        '
        Me.rdoSource.AutoSize = True
        Me.rdoSource.Location = New System.Drawing.Point(98, 5)
        Me.rdoSource.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.rdoSource.Name = "rdoSource"
        Me.rdoSource.Size = New System.Drawing.Size(129, 30)
        Me.rdoSource.TabIndex = 471
        Me.rdoSource.Text = "مسار البرنامج"
        Me.rdoSource.UseVisualStyleBackColor = True
        '
        'rdoYesNetwork
        '
        Me.rdoYesNetwork.AutoSize = True
        Me.rdoYesNetwork.Location = New System.Drawing.Point(312, 5)
        Me.rdoYesNetwork.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.rdoYesNetwork.Name = "rdoYesNetwork"
        Me.rdoYesNetwork.Size = New System.Drawing.Size(75, 30)
        Me.rdoYesNetwork.TabIndex = 469
        Me.rdoYesNetwork.Text = "شبكة"
        Me.rdoYesNetwork.UseVisualStyleBackColor = True
        '
        'rdoNoNetwork
        '
        Me.rdoNoNetwork.AutoSize = True
        Me.rdoNoNetwork.Location = New System.Drawing.Point(231, 5)
        Me.rdoNoNetwork.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.rdoNoNetwork.Name = "rdoNoNetwork"
        Me.rdoNoNetwork.Size = New System.Drawing.Size(77, 30)
        Me.rdoNoNetwork.TabIndex = 470
        Me.rdoNoNetwork.Text = "محلى"
        Me.rdoNoNetwork.UseVisualStyleBackColor = True
        '
        'Panel1
        '
        Me.Panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel1.Controls.Add(Me.rdoYES_ExternalServer)
        Me.Panel1.Controls.Add(Me.rdoNO_ExternalServer)
        Me.Panel1.Location = New System.Drawing.Point(87, 82)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(396, 41)
        Me.Panel1.TabIndex = 473
        '
        'rdoYES_ExternalServer
        '
        Me.rdoYES_ExternalServer.AutoSize = True
        Me.rdoYES_ExternalServer.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.rdoYES_ExternalServer.Location = New System.Drawing.Point(248, 5)
        Me.rdoYES_ExternalServer.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.rdoYES_ExternalServer.Name = "rdoYES_ExternalServer"
        Me.rdoYES_ExternalServer.Size = New System.Drawing.Size(60, 30)
        Me.rdoYES_ExternalServer.TabIndex = 472
        Me.rdoYES_ExternalServer.Text = "YES"
        Me.rdoYES_ExternalServer.UseVisualStyleBackColor = True
        '
        'rdoNO_ExternalServer
        '
        Me.rdoNO_ExternalServer.AutoSize = True
        Me.rdoNO_ExternalServer.Checked = True
        Me.rdoNO_ExternalServer.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.rdoNO_ExternalServer.Location = New System.Drawing.Point(335, 4)
        Me.rdoNO_ExternalServer.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.rdoNO_ExternalServer.Name = "rdoNO_ExternalServer"
        Me.rdoNO_ExternalServer.Size = New System.Drawing.Size(52, 30)
        Me.rdoNO_ExternalServer.TabIndex = 471
        Me.rdoNO_ExternalServer.TabStop = True
        Me.rdoNO_ExternalServer.Text = "NO"
        Me.rdoNO_ExternalServer.UseVisualStyleBackColor = True
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Location = New System.Drawing.Point(486, 90)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(275, 26)
        Me.Label1.TabIndex = 472
        Me.Label1.Text = "أستخدام سيرفر خارجى على الانترنت"
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Location = New System.Drawing.Point(609, 132)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(152, 26)
        Me.Label7.TabIndex = 460
        Me.Label7.Text = "أسم قاعدة البيانات"
        '
        'Label21
        '
        Me.Label21.AutoSize = True
        Me.Label21.Location = New System.Drawing.Point(609, 44)
        Me.Label21.Name = "Label21"
        Me.Label21.Size = New System.Drawing.Size(152, 26)
        Me.Label21.TabIndex = 468
        Me.Label21.Text = "نوع الاتصال بالسرفر"
        '
        'Label14
        '
        Me.Label14.AutoSize = True
        Me.Label14.Location = New System.Drawing.Point(559, 174)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(202, 26)
        Me.Label14.TabIndex = 461
        Me.Label14.Text = "أسم الدخول  على السيرفر"
        '
        'Label16
        '
        Me.Label16.AutoSize = True
        Me.Label16.Location = New System.Drawing.Point(689, 257)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(72, 26)
        Me.Label16.TabIndex = 466
        Me.Label16.Text = "الباسورد"
        '
        'txtUserName
        '
        Me.txtUserName.Enabled = False
        Me.txtUserName.Font = New System.Drawing.Font("Tahoma", 10.2!, System.Drawing.FontStyle.Bold)
        Me.txtUserName.Location = New System.Drawing.Point(87, 210)
        Me.txtUserName.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtUserName.Multiline = True
        Me.txtUserName.Name = "txtUserName"
        Me.txtUserName.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtUserName.Size = New System.Drawing.Size(395, 34)
        Me.txtUserName.TabIndex = 465
        Me.txtUserName.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.Location = New System.Drawing.Point(637, 215)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(124, 26)
        Me.Label15.TabIndex = 464
        Me.Label15.Text = "أسم المستخدم"
        '
        'btnAttachDatabase
        '
        Me.btnAttachDatabase.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.btnAttachDatabase.Location = New System.Drawing.Point(127, 388)
        Me.btnAttachDatabase.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnAttachDatabase.Name = "btnAttachDatabase"
        Me.btnAttachDatabase.Size = New System.Drawing.Size(156, 69)
        Me.btnAttachDatabase.TabIndex = 474
        Me.btnAttachDatabase.Text = "رفع قاعدة البيانات (Attach)"
        Me.btnAttachDatabase.UseVisualStyleBackColor = True
        '
        'Button1
        '
        Me.Button1.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Button1.Image = Global.FIT_SOFT.My.Resources.Resources.Delete3
        Me.Button1.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Button1.Location = New System.Drawing.Point(490, 388)
        Me.Button1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(116, 69)
        Me.Button1.TabIndex = 473
        Me.Button1.Text = "خروج من البرنامج"
        Me.Button1.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.Button1.UseVisualStyleBackColor = True
        '
        'ButtonAPPLY
        '
        Me.ButtonAPPLY.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.ButtonAPPLY.Image = Global.FIT_SOFT.My.Resources.Resources.Accept
        Me.ButtonAPPLY.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.ButtonAPPLY.Location = New System.Drawing.Point(609, 388)
        Me.ButtonAPPLY.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.ButtonAPPLY.Name = "ButtonAPPLY"
        Me.ButtonAPPLY.Size = New System.Drawing.Size(112, 69)
        Me.ButtonAPPLY.TabIndex = 472
        Me.ButtonAPPLY.Text = "تطبيق"
        Me.ButtonAPPLY.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.ButtonAPPLY.UseVisualStyleBackColor = True
        '
        'btnSettingNetworkDefaultBack
        '
        Me.btnSettingNetworkDefaultBack.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.btnSettingNetworkDefaultBack.Location = New System.Drawing.Point(285, 388)
        Me.btnSettingNetworkDefaultBack.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnSettingNetworkDefaultBack.Name = "btnSettingNetworkDefaultBack"
        Me.btnSettingNetworkDefaultBack.Size = New System.Drawing.Size(156, 69)
        Me.btnSettingNetworkDefaultBack.TabIndex = 475
        Me.btnSettingNetworkDefaultBack.Text = "رجوع للاعدادات الشبكة الافتراضية"
        Me.btnSettingNetworkDefaultBack.UseVisualStyleBackColor = True
        '
        'MenuStrip
        '
        Me.MenuStrip.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.MenuStrip.ImageScalingSize = New System.Drawing.Size(20, 20)
        Me.MenuStrip.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.شToolStripMenuItem})
        Me.MenuStrip.Location = New System.Drawing.Point(0, 0)
        Me.MenuStrip.Name = "MenuStrip"
        Me.MenuStrip.Padding = New System.Windows.Forms.Padding(7, 2, 0, 2)
        Me.MenuStrip.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.MenuStrip.Size = New System.Drawing.Size(707, 38)
        Me.MenuStrip.TabIndex = 476
        Me.MenuStrip.Text = "MenuStrip"
        Me.MenuStrip.Visible = False
        '
        'شToolStripMenuItem
        '
        Me.شToolStripMenuItem.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.ToolStripMenuItemRolesTrue, Me.ToolStripMenuItemRolesFalse})
        Me.شToolStripMenuItem.Name = "شToolStripMenuItem"
        Me.شToolStripMenuItem.Size = New System.Drawing.Size(70, 34)
        Me.شToolStripMenuItem.Text = "ادوات"
        '
        'ToolStripMenuItemRolesTrue
        '
        Me.ToolStripMenuItemRolesTrue.Name = "ToolStripMenuItemRolesTrue"
        Me.ToolStripMenuItemRolesTrue.ShortcutKeys = CType((System.Windows.Forms.Keys.Control Or System.Windows.Forms.Keys.F1), System.Windows.Forms.Keys)
        Me.ToolStripMenuItemRolesTrue.Size = New System.Drawing.Size(426, 34)
        Me.ToolStripMenuItemRolesTrue.Text = "إظهار إعدادت الشبكة الافتراضية"
        Me.ToolStripMenuItemRolesTrue.Visible = False
        '
        'ToolStripMenuItemRolesFalse
        '
        Me.ToolStripMenuItemRolesFalse.Name = "ToolStripMenuItemRolesFalse"
        Me.ToolStripMenuItemRolesFalse.ShortcutKeys = CType((System.Windows.Forms.Keys.Control Or System.Windows.Forms.Keys.F2), System.Windows.Forms.Keys)
        Me.ToolStripMenuItemRolesFalse.Size = New System.Drawing.Size(426, 34)
        Me.ToolStripMenuItemRolesFalse.Text = "إخفاء إعدادت الشبكة الافتراضية"
        '
        'btnHelp
        '
        Me.btnHelp.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.btnHelp.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnHelp.Location = New System.Drawing.Point(3, 2)
        Me.btnHelp.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnHelp.Name = "btnHelp"
        Me.btnHelp.Size = New System.Drawing.Size(35, 30)
        Me.btnHelp.TabIndex = 477
        Me.btnHelp.Text = "؟"
        Me.btnHelp.UseVisualStyleBackColor = True
        '
        'PanelHelp
        '
        Me.PanelHelp.Controls.Add(Me.Panel3)
        Me.PanelHelp.Controls.Add(Me.Label4)
        Me.PanelHelp.Controls.Add(Me.Label3)
        Me.PanelHelp.Controls.Add(Me.Label2)
        Me.PanelHelp.Controls.Add(Me.Label23)
        Me.PanelHelp.Controls.Add(Me.Panel19)
        Me.PanelHelp.Controls.Add(Me.Panel20)
        Me.PanelHelp.Controls.Add(Me.Panel21)
        Me.PanelHelp.Controls.Add(Me.Panel22)
        Me.PanelHelp.Location = New System.Drawing.Point(1, 285)
        Me.PanelHelp.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.PanelHelp.Name = "PanelHelp"
        Me.PanelHelp.Size = New System.Drawing.Size(106, 76)
        Me.PanelHelp.TabIndex = 478
        '
        'Panel3
        '
        Me.Panel3.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Panel3.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel3.Location = New System.Drawing.Point(-256, 136)
        Me.Panel3.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(626, 2)
        Me.Panel3.TabIndex = 212
        '
        'Label4
        '
        Me.Label4.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label4.ForeColor = System.Drawing.Color.Firebrick
        Me.Label4.Location = New System.Drawing.Point(-189, 151)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(108, 26)
        Me.Label4.TabIndex = 211
        Me.Label4.Text = "Control + F2"
        '
        'Label3
        '
        Me.Label3.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label3.ForeColor = System.Drawing.Color.Firebrick
        Me.Label3.Location = New System.Drawing.Point(-189, 89)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(106, 26)
        Me.Label3.TabIndex = 210
        Me.Label3.Text = "Control + F1"
        '
        'Label2
        '
        Me.Label2.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label2.Location = New System.Drawing.Point(-1, 151)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(372, 26)
        Me.Label2.TabIndex = 209
        Me.Label2.Text = "إخفاء إعدادات الشبكة الافتراضية يتم الضغط على"
        '
        'Label23
        '
        Me.Label23.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label23.AutoSize = True
        Me.Label23.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label23.Location = New System.Drawing.Point(-1, 88)
        Me.Label23.Name = "Label23"
        Me.Label23.Size = New System.Drawing.Size(370, 26)
        Me.Label23.TabIndex = 208
        Me.Label23.Text = "إظهار إعدادات الشبكة الافتراضية يتم الضغط على"
        '
        'Panel19
        '
        Me.Panel19.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel19.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel19.Location = New System.Drawing.Point(0, 34)
        Me.Panel19.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel19.Name = "Panel19"
        Me.Panel19.Size = New System.Drawing.Size(6, 36)
        Me.Panel19.TabIndex = 205
        '
        'Panel20
        '
        Me.Panel20.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel20.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel20.Location = New System.Drawing.Point(100, 34)
        Me.Panel20.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel20.Name = "Panel20"
        Me.Panel20.Size = New System.Drawing.Size(6, 36)
        Me.Panel20.TabIndex = 204
        '
        'Panel21
        '
        Me.Panel21.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel21.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel21.Location = New System.Drawing.Point(0, 70)
        Me.Panel21.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel21.Name = "Panel21"
        Me.Panel21.Size = New System.Drawing.Size(106, 6)
        Me.Panel21.TabIndex = 203
        '
        'Panel22
        '
        Me.Panel22.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel22.Controls.Add(Me.Label24)
        Me.Panel22.Controls.Add(Me.btnCloseHelp)
        Me.Panel22.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel22.Location = New System.Drawing.Point(0, 0)
        Me.Panel22.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel22.Name = "Panel22"
        Me.Panel22.Size = New System.Drawing.Size(106, 34)
        Me.Panel22.TabIndex = 202
        '
        'Label24
        '
        Me.Label24.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label24.AutoSize = True
        Me.Label24.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label24.ForeColor = System.Drawing.Color.White
        Me.Label24.Location = New System.Drawing.Point(13, 5)
        Me.Label24.Name = "Label24"
        Me.Label24.Size = New System.Drawing.Size(72, 26)
        Me.Label24.TabIndex = 207
        Me.Label24.Text = "تعليمات"
        '
        'btnCloseHelp
        '
        Me.btnCloseHelp.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCloseHelp.BackColor = System.Drawing.Color.Transparent
        Me.btnCloseHelp.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Delete
        Me.btnCloseHelp.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnCloseHelp.Location = New System.Drawing.Point(74, 1)
        Me.btnCloseHelp.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnCloseHelp.Name = "btnCloseHelp"
        Me.btnCloseHelp.Size = New System.Drawing.Size(29, 31)
        Me.btnCloseHelp.TabIndex = 206
        Me.btnCloseHelp.UseVisualStyleBackColor = False
        '
        'btnLocalDB
        '
        Me.btnLocalDB.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.btnLocalDB.Location = New System.Drawing.Point(12, 388)
        Me.btnLocalDB.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnLocalDB.Name = "btnLocalDB"
        Me.btnLocalDB.Size = New System.Drawing.Size(112, 69)
        Me.btnLocalDB.TabIndex = 479
        Me.btnLocalDB.Text = "LocalDB"
        Me.btnLocalDB.UseVisualStyleBackColor = True
        '
        'PanelLocalDB
        '
        Me.PanelLocalDB.Controls.Add(Me.txtLocalDB)
        Me.PanelLocalDB.Controls.Add(Me.Panel4)
        Me.PanelLocalDB.Controls.Add(Me.Panel5)
        Me.PanelLocalDB.Controls.Add(Me.Panel6)
        Me.PanelLocalDB.Controls.Add(Me.Panel7)
        Me.PanelLocalDB.Location = New System.Drawing.Point(46, 312)
        Me.PanelLocalDB.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.PanelLocalDB.Name = "PanelLocalDB"
        Me.PanelLocalDB.Size = New System.Drawing.Size(654, 78)
        Me.PanelLocalDB.TabIndex = 480
        '
        'txtLocalDB
        '
        Me.txtLocalDB.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtLocalDB.Font = New System.Drawing.Font("Tahoma", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtLocalDB.Location = New System.Drawing.Point(12, 38)
        Me.txtLocalDB.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtLocalDB.Multiline = True
        Me.txtLocalDB.Name = "txtLocalDB"
        Me.txtLocalDB.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtLocalDB.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.txtLocalDB.Size = New System.Drawing.Size(630, 26)
        Me.txtLocalDB.TabIndex = 475
        Me.txtLocalDB.Text = "Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\DatabaseElect" &
    "ric.mdf;Integrated Security=True;"
        '
        'Panel4
        '
        Me.Panel4.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel4.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel4.Location = New System.Drawing.Point(0, 34)
        Me.Panel4.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel4.Name = "Panel4"
        Me.Panel4.Size = New System.Drawing.Size(6, 38)
        Me.Panel4.TabIndex = 205
        '
        'Panel5
        '
        Me.Panel5.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel5.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel5.Location = New System.Drawing.Point(648, 34)
        Me.Panel5.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel5.Name = "Panel5"
        Me.Panel5.Size = New System.Drawing.Size(6, 38)
        Me.Panel5.TabIndex = 204
        '
        'Panel6
        '
        Me.Panel6.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel6.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel6.Location = New System.Drawing.Point(0, 72)
        Me.Panel6.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel6.Name = "Panel6"
        Me.Panel6.Size = New System.Drawing.Size(654, 6)
        Me.Panel6.TabIndex = 203
        '
        'Panel7
        '
        Me.Panel7.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel7.Controls.Add(Me.Label5)
        Me.Panel7.Controls.Add(Me.btnCloseLocalDB)
        Me.Panel7.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel7.Location = New System.Drawing.Point(0, 0)
        Me.Panel7.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel7.Name = "Panel7"
        Me.Panel7.Size = New System.Drawing.Size(654, 34)
        Me.Panel7.TabIndex = 202
        '
        'Label5
        '
        Me.Label5.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label5.ForeColor = System.Drawing.Color.White
        Me.Label5.Location = New System.Drawing.Point(287, 5)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(69, 26)
        Me.Label5.TabIndex = 207
        Me.Label5.Text = "LocalDB"
        '
        'btnCloseLocalDB
        '
        Me.btnCloseLocalDB.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCloseLocalDB.BackColor = System.Drawing.Color.Transparent
        Me.btnCloseLocalDB.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Delete
        Me.btnCloseLocalDB.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnCloseLocalDB.Location = New System.Drawing.Point(622, 1)
        Me.btnCloseLocalDB.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnCloseLocalDB.Name = "btnCloseLocalDB"
        Me.btnCloseLocalDB.Size = New System.Drawing.Size(29, 31)
        Me.btnCloseLocalDB.TabIndex = 206
        Me.btnCloseLocalDB.UseVisualStyleBackColor = False
        '
        'PanelServerData
        '
        Me.PanelServerData.Controls.Add(Me.btnDelServerData)
        Me.PanelServerData.Controls.Add(Me.btnAddServerData)
        Me.PanelServerData.Controls.Add(Me.dgvServerData)
        Me.PanelServerData.Controls.Add(Me.Panel8)
        Me.PanelServerData.Controls.Add(Me.Panel9)
        Me.PanelServerData.Controls.Add(Me.Panel10)
        Me.PanelServerData.Controls.Add(Me.Panel11)
        Me.PanelServerData.Location = New System.Drawing.Point(635, 439)
        Me.PanelServerData.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.PanelServerData.Name = "PanelServerData"
        Me.PanelServerData.Size = New System.Drawing.Size(178, 94)
        Me.PanelServerData.TabIndex = 555
        Me.PanelServerData.Visible = False
        '
        'btnDelServerData
        '
        Me.btnDelServerData.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnDelServerData.BackColor = System.Drawing.Color.Transparent
        Me.btnDelServerData.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Delete
        Me.btnDelServerData.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnDelServerData.Location = New System.Drawing.Point(13, 49)
        Me.btnDelServerData.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnDelServerData.Name = "btnDelServerData"
        Me.btnDelServerData.Size = New System.Drawing.Size(154, 34)
        Me.btnDelServerData.TabIndex = 214
        Me.btnDelServerData.UseVisualStyleBackColor = False
        '
        'btnAddServerData
        '
        Me.btnAddServerData.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnAddServerData.BackColor = System.Drawing.Color.Transparent
        Me.btnAddServerData.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Items_add_1
        Me.btnAddServerData.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnAddServerData.Location = New System.Drawing.Point(12, 42)
        Me.btnAddServerData.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnAddServerData.Name = "btnAddServerData"
        Me.btnAddServerData.Size = New System.Drawing.Size(154, 34)
        Me.btnAddServerData.TabIndex = 209
        Me.btnAddServerData.UseVisualStyleBackColor = False
        '
        'dgvServerData
        '
        Me.dgvServerData.AllowUserToAddRows = False
        Me.dgvServerData.AllowUserToDeleteRows = False
        Me.dgvServerData.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.dgvServerData.BackgroundColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle1.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle1.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgvServerData.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle1
        Me.dgvServerData.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window
        DataGridViewCellStyle2.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.dgvServerData.DefaultCellStyle = DataGridViewCellStyle2
        Me.dgvServerData.Location = New System.Drawing.Point(10, 82)
        Me.dgvServerData.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.dgvServerData.Name = "dgvServerData"
        Me.dgvServerData.ReadOnly = True
        Me.dgvServerData.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        DataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle3.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle3.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle3.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle3.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgvServerData.RowHeadersDefaultCellStyle = DataGridViewCellStyle3
        Me.dgvServerData.RowTemplate.Height = 26
        Me.dgvServerData.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.dgvServerData.Size = New System.Drawing.Size(157, 0)
        Me.dgvServerData.TabIndex = 206
        '
        'Panel8
        '
        Me.Panel8.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel8.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel8.Location = New System.Drawing.Point(0, 34)
        Me.Panel8.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel8.Name = "Panel8"
        Me.Panel8.Size = New System.Drawing.Size(6, 54)
        Me.Panel8.TabIndex = 205
        '
        'Panel9
        '
        Me.Panel9.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel9.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel9.Location = New System.Drawing.Point(172, 34)
        Me.Panel9.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel9.Name = "Panel9"
        Me.Panel9.Size = New System.Drawing.Size(6, 54)
        Me.Panel9.TabIndex = 204
        '
        'Panel10
        '
        Me.Panel10.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel10.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel10.Location = New System.Drawing.Point(0, 88)
        Me.Panel10.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel10.Name = "Panel10"
        Me.Panel10.Size = New System.Drawing.Size(178, 6)
        Me.Panel10.TabIndex = 203
        '
        'Panel11
        '
        Me.Panel11.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel11.Controls.Add(Me.Label6)
        Me.Panel11.Controls.Add(Me.btnCloseServerData)
        Me.Panel11.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel11.Location = New System.Drawing.Point(0, 0)
        Me.Panel11.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel11.Name = "Panel11"
        Me.Panel11.Size = New System.Drawing.Size(178, 34)
        Me.Panel11.TabIndex = 202
        '
        'Label6
        '
        Me.Label6.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.Label6.ForeColor = System.Drawing.Color.White
        Me.Label6.Location = New System.Drawing.Point(29, 6)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(100, 24)
        Me.Label6.TabIndex = 207
        Me.Label6.Text = "بيانات السيرفر"
        '
        'btnCloseServerData
        '
        Me.btnCloseServerData.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCloseServerData.BackColor = System.Drawing.Color.Transparent
        Me.btnCloseServerData.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Delete
        Me.btnCloseServerData.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnCloseServerData.Location = New System.Drawing.Point(146, 1)
        Me.btnCloseServerData.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnCloseServerData.Name = "btnCloseServerData"
        Me.btnCloseServerData.Size = New System.Drawing.Size(29, 31)
        Me.btnCloseServerData.TabIndex = 206
        Me.btnCloseServerData.UseVisualStyleBackColor = False
        '
        'txtPassword
        '
        Me.txtPassword.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtPassword.Enabled = False
        Me.txtPassword.Font = New System.Drawing.Font("Tahoma", 10.2!, System.Drawing.FontStyle.Bold)
        Me.txtPassword.Location = New System.Drawing.Point(87, 249)
        Me.txtPassword.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtPassword.Multiline = True
        Me.txtPassword.Name = "txtPassword"
        Me.txtPassword.Size = New System.Drawing.Size(395, 34)
        Me.txtPassword.TabIndex = 564
        Me.txtPassword.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.txtPassword.UseSystemPasswordChar = True
        '
        'frmSettingNetwork
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None
        Me.ClientSize = New System.Drawing.Size(822, 493)
        Me.Controls.Add(Me.PanelServerData)
        Me.Controls.Add(Me.PanelLocalDB)
        Me.Controls.Add(Me.btnLocalDB)
        Me.Controls.Add(Me.PanelHelp)
        Me.Controls.Add(Me.btnHelp)
        Me.Controls.Add(Me.MenuStrip)
        Me.Controls.Add(Me.btnSettingNetworkDefaultBack)
        Me.Controls.Add(Me.btnAttachDatabase)
        Me.Controls.Add(Me.Button1)
        Me.Controls.Add(Me.ButtonAPPLY)
        Me.Controls.Add(Me.GroupBox2)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "frmSettingNetwork"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "أعدادات الشبكة"
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        Me.Panel2.ResumeLayout(False)
        Me.Panel2.PerformLayout()
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        Me.MenuStrip.ResumeLayout(False)
        Me.MenuStrip.PerformLayout()
        Me.PanelHelp.ResumeLayout(False)
        Me.PanelHelp.PerformLayout()
        Me.Panel22.ResumeLayout(False)
        Me.Panel22.PerformLayout()
        Me.PanelLocalDB.ResumeLayout(False)
        Me.PanelLocalDB.PerformLayout()
        Me.Panel7.ResumeLayout(False)
        Me.Panel7.PerformLayout()
        Me.PanelServerData.ResumeLayout(False)
        CType(Me.dgvServerData, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel11.ResumeLayout(False)
        Me.Panel11.PerformLayout()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents Label21 As System.Windows.Forms.Label
    Friend WithEvents Label14 As System.Windows.Forms.Label
    Friend WithEvents Label16 As System.Windows.Forms.Label
    Friend WithEvents txtUserName As System.Windows.Forms.TextBox
    Friend WithEvents Label15 As System.Windows.Forms.Label
    Friend WithEvents rdoNoNetwork As System.Windows.Forms.RadioButton
    Friend WithEvents rdoYesNetwork As System.Windows.Forms.RadioButton
    Friend WithEvents Button1 As System.Windows.Forms.Button
    Friend WithEvents ButtonAPPLY As System.Windows.Forms.Button
    Friend WithEvents rdoSource As System.Windows.Forms.RadioButton
    Friend WithEvents Panel1 As Panel
    Friend WithEvents rdoYES_ExternalServer As RadioButton
    Friend WithEvents rdoNO_ExternalServer As RadioButton
    Friend WithEvents Label1 As Label
    Friend WithEvents Panel2 As Panel
    Friend WithEvents btnAttachDatabase As Button
    Friend WithEvents OpenFileDialog1 As OpenFileDialog
    Friend WithEvents chkSettingNetworkDefault As CheckBox
    Friend WithEvents btnSettingNetworkDefaultBack As Button
    Friend WithEvents MenuStrip As MenuStrip
    Friend WithEvents شToolStripMenuItem As ToolStripMenuItem
    Friend WithEvents ToolStripMenuItemRolesTrue As ToolStripMenuItem
    Friend WithEvents ToolStripMenuItemRolesFalse As ToolStripMenuItem
    Friend WithEvents btnHelp As Button
    Friend WithEvents PanelHelp As Panel
    Friend WithEvents Label4 As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents Label23 As Label
    Friend WithEvents Panel19 As Panel
    Friend WithEvents Panel20 As Panel
    Friend WithEvents Panel21 As Panel
    Friend WithEvents Panel22 As Panel
    Friend WithEvents Label24 As Label
    Friend WithEvents btnCloseHelp As Button
    Friend WithEvents Panel3 As Panel
    Friend WithEvents btnLocalDB As Button
    Friend WithEvents PanelLocalDB As Panel
    Friend WithEvents txtLocalDB As TextBox
    Friend WithEvents Panel4 As Panel
    Friend WithEvents Panel5 As Panel
    Friend WithEvents Panel6 As Panel
    Friend WithEvents Panel7 As Panel
    Friend WithEvents Label5 As Label
    Friend WithEvents btnCloseLocalDB As Button
    Friend WithEvents rdoLocalDB As RadioButton
    Friend WithEvents PanelServerData As Panel
    Friend WithEvents btnDelServerData As Button
    Friend WithEvents btnAddServerData As Button
    Friend WithEvents dgvServerData As DataGridView
    Friend WithEvents Panel8 As Panel
    Friend WithEvents Panel9 As Panel
    Friend WithEvents Panel10 As Panel
    Friend WithEvents Panel11 As Panel
    Friend WithEvents Label6 As Label
    Friend WithEvents btnCloseServerData As Button
    Friend WithEvents btnAddServerName As Button
    Friend WithEvents txtServerName As ComboBox
    Friend WithEvents btnDeleteDataBaseName As Button
    Friend WithEvents btnDeleteServerName As Button
    Friend WithEvents btnAddDataBaseName As Button
    Friend WithEvents txtDataBaseName As ComboBox
    Friend WithEvents txtPassword As TextBox
End Class
