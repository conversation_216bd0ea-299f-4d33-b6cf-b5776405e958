﻿Imports System.Windows.Forms
Imports System.Data.SqlClient
Imports vb = Microsoft.VisualBasic
Imports System.IO

Public Class frmCompany
    Inherits System.Windows.Forms.Form

    Dim Memo As New MemoryStream
    Dim WithEvents BS As New BindingSource
    Dim constring As String
    Dim SqlDataAdapter1 As SqlClient.SqlDataAdapter
    Dim ds As DataSet = New DataSet
    Dim MaxRecoedCode As String
    Dim ActionGrid As Boolean = False

    Private Sub frmCompany_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        connectionStringOpen()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandTimeout = 180
        cmd.CommandText = "SELECT * FROM Company"
        dr = cmd.ExecuteReader
        dr.Read()
        DISPLAYRECORD()

        Dim ShowBackground As String = mykey.GetValue("ShowBackground", "NO")
        If ShowBackground = "NO" Then
            chkShowBackground.Checked = False
        End If
        If ShowBackground = "YES" Then
            chkShowBackground.Checked = True
        End If

        Dim SelectLogoPathOther As String = mykey.GetValue("SelectLogoPathOther", "YES")
        If SelectLogoPathOther = "NO" Then
            rdoLogoPathOther.Checked = True
        End If
        If SelectLogoPathOther = "YES" Then
            rdoLogoPath.Checked = True
        End If

    End Sub

    Private Sub DISPLAYRECORD()
        'On Error Resume Next
        If dr.HasRows = True Then
            txtserial.Text = dr("CMPSerial").ToString
            txtName.Text = dr("CMPName").ToString
            txtNameEnglish.Text = dr("CMPNameEnglish").ToString
            txtAddress.Text = dr("CMPAddress").ToString
            txtTel.Text = dr("CMPTel").ToString
            txtMobile.Text = dr("CMPMobile").ToString
            txtFax.Text = dr("CMPFax").ToString
            txtEmail.Text = dr("CMPEmail").ToString
            txtTitelArDown.Text = dr("CMPNameDown").ToString
            txtAddressBill.Text = dr("CMPAddressBill").ToString
            txtWebsite.Text = dr("CMPWebsite").ToString
            txtUnderBILL.Text = dr("CMPUnderBILL").ToString
            txtCommercialRecord.Text = dr("CMPCommercialRecord").ToString
            txtTaxCard.Text = dr("CMPTaxCard").ToString
            txtEndorsement.Text = dr("CMPEndorsement").ToString
        End If

        'With Me
        '    .txtserial.Text = ds.Tables("COMPANY").Rows(Me.BS.Position)("CMPSerial").ToString
        '    .txtName.Text = ds.Tables("COMPANY").Rows(Me.BS.Position)("CMPName").ToString
        '    .txtNameEnglish.Text = ds.Tables("COMPANY").Rows(Me.BS.Position)("CMPNameEnglish").ToString
        '    .txtAddress.Text = ds.Tables("COMPANY").Rows(Me.BS.Position)("CMPAddress").ToString
        '    .txtTel.Text = ds.Tables("COMPANY").Rows(Me.BS.Position)("CMPTel").ToString
        '    .txtMobile.Text = ds.Tables("COMPANY").Rows(Me.BS.Position)("CMPMobile").ToString
        '    .txtFax.Text = ds.Tables("COMPANY").Rows(Me.BS.Position)("CMPFax").ToString
        '    .txtEmail.Text = ds.Tables("COMPANY").Rows(Me.BS.Position)("CMPEmail").ToString
        '    .txtUnderBILL.Text = ds.Tables("COMPANY").Rows(Me.BS.Position)("CMPUnderBILL").ToString
        '    .txtAddressBill.Text = ds.Tables("COMPANY").Rows(Me.BS.Position)("CMPAddressBill").ToString
        '    .txtWebsite.Text = ds.Tables("COMPANY").Rows(Me.BS.Position)("CMPWebsite").ToString
        '    .txtTitelArDown.Text = ds.Tables("COMPANY").Rows(Me.BS.Position)("CMPNameDown").ToString
        'End With
        SHOWPHOTO()
        SHOWPHOTOBackgroundImage()
    End Sub

    Private Sub SHOWPHOTO()
        On Error Resume Next
        Dim sql As String
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If cmbName.Text = "" Then
            sql = "SELECT CMPImage FROM COMPANY WHERE CMPSerial =N'" & txtserial.Text & "'"
        Else
            sql = "SELECT CMPImage FROM COMPANY WHERE CMPName =N'" & cmbName.Text & "'"
        End If
        Dim cmd As SqlClient.SqlCommand = New SqlClient.SqlCommand(sql, Cn)
        Dim b() As Byte
        b = cmd.ExecuteScalar()
        If (b.Length > 0) Then
            Dim stream As New MemoryStream(b, True)
            stream.Write(b, 0, b.Length)
            PicLogo.Image = New Bitmap(stream)
            stream.Close()
        Else
            Me.PicLogo.Image = Nothing
        End If
    End Sub

    Private Sub SHOWPHOTOBackgroundImage()
        On Error Resume Next
        Dim sql As String
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If cmbName.Text = "" Then
            sql = "SELECT CMPBackgroundImage FROM COMPANY WHERE CMPSerial =N'" & txtserial.Text & "'"
        Else
            sql = "SELECT CMPBackgroundImage FROM COMPANY WHERE CMPName =N'" & cmbName.Text & "'"
        End If
        Dim cmd As SqlClient.SqlCommand = New SqlClient.SqlCommand(sql, Cn)
        Dim b() As Byte
        b = cmd.ExecuteScalar()
        If (b.Length > 0) Then
            Dim stream As New MemoryStream(b, True)
            stream.Write(b, 0, b.Length)
            PicBackgroundImage.Image = New Bitmap(stream)
            stream.Close()
        Else
            PicBackgroundImage.Image = Nothing
        End If
    End Sub

    Private Sub SAVEBUTTON_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles SAVEBUTTON.Click
        On Error Resume Next
        If Validate_Text() = False Then Exit Sub
        SAVERECORD()
        frmCompany_Load(sender, e)
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)
        SHOWPHOTOGeneral()
    End Sub

    Private Sub SAVERECORD()
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        Dim SQL As String = "INSERT INTO COMPANY(CMPSerial,CMPName,CMPNameEnglish,CMPAddress,CMPTel,CMPMobile,CMPFax,CMPEmail,CMPImage,CMPBackgroundImage,CMPUnderBILL,CMPAddressBill,CMPNameDown,CMPCommercialRecord,CMPTaxCard,CMPEndorsement) VALUES     (@CMPSerial,@CMPName,@CMPNameEnglish,@CMPAddress,@CMPTel,@CMPMobile,@CMPFax,@CMPEmail,@CMPImage,@CMPBackgroundImage,@CMPUnderBILL,@CMPAddressBill,@CMPNameDown,@CMPCommercialRecord,@CMPTaxCard,@CMPEndorsement)"
        Dim fs As FileStream = New FileStream(OpenFileDialog1.FileName, FileMode.Open, FileAccess.Read)
        Dim r As BinaryReader = New BinaryReader(fs)
        Dim FileByteArray(fs.Length - 1) As Byte
        r.Read(FileByteArray, 0, CInt(fs.Length))

        Dim fsBg As FileStream = New FileStream(OpenFileDialog2.FileName, FileMode.Open, FileAccess.Read)
        Dim rBg As BinaryReader = New BinaryReader(fsBg)
        Dim FileByteArrayBg(fsBg.Length - 1) As Byte
        rBg.Read(FileByteArrayBg, 0, CInt(fsBg.Length))
        With cmd
            .CommandType = CommandType.Text
            .Connection = Cn
            .Parameters.Add("@CMPSerial", SqlDbType.NVarChar).Value = Me.txtserial.Text.Trim
            .Parameters.Add("@CMPName", SqlDbType.NVarChar).Value = Me.txtName.Text.Trim
            .Parameters.Add("@CMPNameEnglish", SqlDbType.NVarChar).Value = Me.txtNameEnglish.Text.Trim
            .Parameters.Add("@CMPAddress", SqlDbType.NVarChar).Value = Me.txtAddress.Text.Trim
            .Parameters.Add("@CMPTel", SqlDbType.NVarChar).Value = Me.txtTel.Text.Trim
            .Parameters.Add("@CMPMobile", SqlDbType.NVarChar).Value = Me.txtMobile.Text.Trim
            .Parameters.Add("@CMPFax", SqlDbType.NVarChar).Value = Me.txtFax.Text.Trim
            .Parameters.Add("@CMPEmail", SqlDbType.NVarChar).Value = Me.txtEmail.Text.Trim
            .Parameters.Add("@CMPImage", SqlDbType.Image).Value = FileByteArray
            .Parameters.Add("@CMPBackgroundImage", SqlDbType.Image).Value = FileByteArrayBg
            .Parameters.Add("@CMPUnderBILL", SqlDbType.NVarChar).Value = Me.txtUnderBILL.Text.Trim
            .Parameters.Add("@CMPAddressBill", SqlDbType.NVarChar).Value = Me.txtAddressBill.Text.Trim
            .Parameters.Add("@CMPNameDown", SqlDbType.NVarChar).Value = Me.txtTitelArDown.Text.Trim
            .Parameters.Add("@CMPCommercialRecord", SqlDbType.NVarChar).Value = Me.txtCommercialRecord.Text.Trim
            .Parameters.Add("@CMPTaxCard", SqlDbType.NVarChar).Value = Me.txtTaxCard.Text.Trim
            .Parameters.Add("@CMPEndorsement", SqlDbType.NVarChar).Value = Me.txtEndorsement.Text.Trim
            .CommandText = SQL
        End With
        connectionStringOpen()
        cmd.ExecuteNonQuery()
    End Sub

    Private Sub LOGOBUTTON_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles LOGOBUTTON.Click
        On Error Resume Next
        OpenFileDialog1.Filter = "Image Files (*.png *.jpg *.bmp *.JPE *.JPEG) |*.png; *.jpg; *.bmp; *.JPE; *.JPEG|All Files(*.*) |*.*"
        With Me.OpenFileDialog1
            .FilterIndex = 1
            .Title = "حدد شعار الشركة"
            .ShowDialog()
            If Len(.FileName) > 0 Then
                PicLogo.Image = Image.FromFile(OpenFileDialog1.FileName)

            End If
        End With
    End Sub

    Private Sub LOGOBUTTON2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles LOGOBUTTON2.Click
        'On Error Resume Next
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If OpenFileDialog1.FileName <> "" Then
            S = " Update Company SET  CMPImage = @CMPImage,CMPLogoPath = @CMPLogoPath WHERE CMPSerial = @CMPSerial"
            Dim CMD As SqlClient.SqlCommand = New SqlClient.SqlCommand
            CMD.CommandType = CommandType.Text
            CMD.Connection = Cn
            Dim fs As FileStream = New FileStream(OpenFileDialog1.FileName, FileMode.Open, FileAccess.Read)
            Dim r As BinaryReader = New BinaryReader(fs)
            Dim FileByteArray(fs.Length - 1) As Byte
            r.Read(FileByteArray, 0, CInt(fs.Length))
            With CMD
                .CommandType = CommandType.Text
                .Connection = Cn
                .Parameters.Add("@CMPSerial", SqlDbType.NVarChar).Value = txtserial.Text.Trim
                If rdoLogoPath.Checked = True Then
                    .Parameters.Add("@CMPLogoPath", SqlDbType.NVarChar).Value = OpenFileDialog1.SafeFileName
                End If
                If rdoLogoPathOther.Checked = True Then
                    .Parameters.Add("@CMPLogoPath", SqlDbType.NVarChar).Value = OpenFileDialog1.FileName
                End If
                .Parameters.Add("@CMPImage", SqlDbType.Image).Value = FileByteArray
                .CommandText = S
            End With
            CMD.ExecuteNonQuery()


            If rdoLogoPath.Checked = True Then
                mykey.SetValue("CMPLogoPath", OpenFileDialog1.SafeFileName)
                mykey.SetValue("SelectLogoPathOther", "YES")
                SelectLogoPathOther = "YES"
            End If
            If rdoLogoPathOther.Checked = True Then
                mykey.SetValue("CMPLogoPath", OpenFileDialog1.FileName)
                mykey.SetValue("SelectLogoPathOther", "NO")
                SelectLogoPathOther = "NO"
            End If
            MsgBox("تم تعديل الشعار بنجاح", MsgBoxStyle.Information)
            MsgBox("سيتم اعادة تشغيل البرنامج", MsgBoxStyle.Information)
            System.Windows.Forms.Application.Restart()
        End If
    End Sub

    Private Sub EDITBUTTON_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles EDITBUTTON.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        UPDATERECORD()
        frmCompany_Load(sender, e)
        MsgBox("تم التعديل بنجاح", MsgBoxStyle.Information)
        SHOWPHOTOGeneral()

        mykey.SetValue("CMPNameArabic", txtName.Text)
        mykey.SetValue("CMPNameEnglish", txtNameEnglish.Text)
        mykey.SetValue("CMPAddress", txtAddress.Text)
        mykey.SetValue("CMPTel", txtTel.Text)
        mykey.SetValue("CMPMobile", txtMobile.Text)
        mykey.SetValue("CMPFax", txtFax.Text)
        mykey.SetValue("CMPEmail", txtEmail.Text)
        mykey.SetValue("CMPUnderBILL", txtUnderBILL.Text)
        mykey.SetValue("CMPAddressBill", txtAddressBill.Text)
        mykey.SetValue("CMPWebsite", txtWebsite.Text)
        mykey.SetValue("CMPCommercialRecord", txtCommercialRecord.Text)
        mykey.SetValue("CMPTaxCard", txtTaxCard.Text)
        mykey.SetValue("CMPNameDown", txtTitelArDown.Text)
        mykey.SetValue("CMPEndorsement", txtEndorsement.Text)

    End Sub

    Private Sub UPDATERECORD()
        'On Error Resume Next
        connect()

        If Trim(txtserial.Text) = "" Then : MsgBox("فضلاً ادخل رقم المسلسل ", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب") : txtserial.Focus() : Exit Sub : End If
        If PicBackgroundImage.Image Is Nothing Then : MsgBox("فضلاً أختار خلفية البرنامج", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب") : Exit Sub : End If
        If PicLogo.Image Is Nothing Then : MsgBox("فضلاً أختار الشعار", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب") : Exit Sub : End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Company set CMPName=N'" & txtName.Text & "',CMPNameEnglish=N'" & txtNameEnglish.Text & "',CMPAddress =N'" & txtAddress.Text & "', CMPTel=N'" & txtTel.Text & "', CMPMobile=N'" & txtMobile.Text & "',CMPFax=N'" & txtFax.Text & "',CMPEmail=N'" & txtEmail.Text & "',CMPUnderBILL=N'" & txtUnderBILL.Text & "',CMPAddressBill=N'" & txtAddressBill.Text & "',CMPWebsite=N'" & txtWebsite.Text & "',CMPNameDown=N'" & txtTitelArDown.Text & "',CMPCommercialRecord=N'" & txtCommercialRecord.Text & "',CMPTaxCard=N'" & txtTaxCard.Text & "',CMPEndorsement=N'" & txtEndorsement.Text & "' where CMPSerial =N'" & txtserial.Text & "'"
        connectionStringOpen()
        cmd.ExecuteNonQuery()
    End Sub

    Function Validate_Text() As Boolean
        On Error Resume Next
        If Trim(txtserial.Text) = "" Then
            MsgBox("فضلاً ادخل رقم المسلسل ", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtserial.Focus() : Return False : Exit Function
        End If
        If PicBackgroundImage.Image Is Nothing Then
            MsgBox("فضلاً أختار خلفية البرنامج", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            Return False : Exit Function
        End If
        If PicLogo.Image Is Nothing Then
            MsgBox("فضلاً أختار الشعار", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            Return False : Exit Function
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * From Company where CMPSerial =N'" & txtserial.Text & "'"
        dr = cmd.ExecuteReader()
        If dr.HasRows = True Then
            MessageBox.Show("تم تسجيل البيانات سابقاً", "تكرار بيانات", MessageBoxButtons.OK, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading)
            Return False : Exit Function
        End If
        Return True
    End Function

    Private Sub DELETEBUTTON_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DELETEBUTTON.Click
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        If Trim(txtserial.Text) = "" Then
            MsgBox("فضلاً ادخل رقم المسلسل ", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtserial.Focus() : Exit Sub
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete  from Company where CMPSerial =N'" & txtserial.Text & "'" : cmd.ExecuteNonQuery()
        SHOWPHOTOSales()
        SHOWPHOTOGeneral()
        CLEARALL()
    End Sub
    Sub CLEARALL()
        txtserial.Text = ""
        txtName.Text = ""
        txtNameEnglish.Text = ""
        txtAddress.Text = ""
        txtTel.Text = ""
        txtMobile.Text = ""
        txtFax.Text = ""
        txtEmail.Text = ""
        PicBackgroundImage.Image = Nothing
        PicLogo.Image = Nothing
    End Sub
    Private Sub btnSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearch.Click
        connectionStringOpen()
        ds.EnforceConstraints = False
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        Dim str As String = "SELECT * FROM Company where CMPName =N'" & cmbName.Text & "'"
        SqlDataAdapter1 = New SqlClient.SqlDataAdapter(str, Cn)
        ds.Clear()
        SqlDataAdapter1.Fill(ds, "Company")
        BS.DataSource = ds
        BS.DataMember = "Company"
        ds.EnforceConstraints = True
        SqlDataAdapter1.Dispose()
        DISPLAYRECORD()
    End Sub

    Private Sub BtnLogoBackgroundImage_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnLogoBackgroundImage.Click
        On Error Resume Next
        OpenFileDialog2.Filter = "Image Files (*.png *.jpg *.bmp *.JPE *.JPEG) |*.png; *.jpg; *.bmp; *.JPE; *.JPEG|All Files(*.*) |*.*"
        With Me.OpenFileDialog2
            .FilterIndex = 1
            .Title = "حدد خلفية البرنامج"
            .ShowDialog()
            If Len(.FileName) > 0 Then
                PicBackgroundImage.Image = Image.FromFile(OpenFileDialog2.FileName)
            End If
        End With
    End Sub
    Private Sub BtnEditBackgroundImage_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnEditBackgroundImage.Click
        On Error Resume Next
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        Dim SQL As String = " Update Company SET  CMPBackgroundImage = @CMPBackgroundImage WHERE CMPSerial = @CMPSerial"
        Dim CMD As SqlClient.SqlCommand = New SqlClient.SqlCommand
        CMD.CommandType = CommandType.Text
        CMD.Connection = Cn
        Dim fs As FileStream = New FileStream(OpenFileDialog2.FileName, FileMode.Open, FileAccess.Read)
        Dim r As BinaryReader = New BinaryReader(fs)
        Dim FileByteArray(fs.Length - 1) As Byte
        r.Read(FileByteArray, 0, CInt(fs.Length))
        With CMD
            .CommandType = CommandType.Text
            .Connection = Cn
            .Parameters.Add("@CMPSerial", SqlDbType.NVarChar).Value = txtserial.Text.Trim
            .Parameters.Add("@CMPBackgroundImage", SqlDbType.Image).Value = FileByteArray
            .CommandText = SQL
        End With
        connectionStringOpen()
        CMD.ExecuteNonQuery()
        MsgBox("تم التعديل الخلفية بنجاح", MsgBoxStyle.Information)
        SHOWPHOTOGeneral()
    End Sub

    Private Sub chkShowBackground_CheckedChanged(sender As Object, e As EventArgs) Handles chkShowBackground.CheckedChanged
        If chkShowBackground.Checked = True Then
            mykey.SetValue("ShowBackground", "YES")
        ElseIf chkShowBackground.Checked = False Then
            mykey.SetValue("ShowBackground", "NO")
        End If
    End Sub

    Private Sub btnCompany_Branch_Click(sender As Object, e As EventArgs) Handles btnCompany_Branch.Click
        frmCompanyBranch.ShowDialog()
    End Sub

    Private Sub MAXRECORD(ByVal Tables As String, ByVal Code As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " & Tables & ""
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            MaxRecoedCode = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" & Code & " As float)) as mb FROM " & Tables & " where " & Code & " <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            MaxRecoedCode = sh + 1
        End If

    End Sub


End Class