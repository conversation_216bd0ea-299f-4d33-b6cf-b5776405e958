Imports System.Data.SqlClient

''' <summary>
''' فئة اختبار للتأكد من عمل التحسينات الأمنية بشكل صحيح
''' </summary>
Public Class SecurityTests

    ''' <summary>
    ''' اختبار SecureDatabaseManager
    ''' </summary>
    Public Shared Sub TestSecureDatabaseManager()
        Try
            ErrorHandler.LogInfo("بدء اختبار SecureDatabaseManager")

            ' تهيئة المدير
            SecureDatabaseManager.Instance.Initialize()

            ' اختبار إنشاء اتصال
            Using connection As SqlConnection = SecureDatabaseManager.Instance.CreateConnection()
                ErrorHandler.LogInfo("تم إنشاء الاتصال بنجاح")
            End Using

            ' اختبار استعلام آمن
            Dim query As String = "SELECT COUNT(*) FROM users WHERE username = @username"
            Dim parameters As New Dictionary(Of String, Object) From {
                {"@username", "test"}
            }

            Dim result As Object = SecureDatabaseManager.Instance.ExecuteScalar(query, parameters)
            ErrorHandler.LogInfo($"نتيجة الاستعلام الآمن: {result}")

            ErrorHandler.LogInfo("تم اجتياز اختبار SecureDatabaseManager بنجاح")

        Catch ex As Exception
            ErrorHandler.LogError("فشل في اختبار SecureDatabaseManager", ex)
        End Try
    End Sub

    ''' <summary>
    ''' اختبار ErrorHandler
    ''' </summary>
    Public Shared Sub TestErrorHandler()
        Try
            ErrorHandler.LogInfo("بدء اختبار ErrorHandler")

            ' اختبار تسجيل المعلومات
            ErrorHandler.LogInfo("هذه رسالة معلومات اختبارية")

            ' اختبار تسجيل التحذير
            ErrorHandler.LogWarning("هذا تحذير اختباري")

            ' اختبار SafeExecute
            Dim result As String = ErrorHandler.SafeExecute(
                Function() As String
                    Return "عملية ناجحة"
                End Function,
                "قيمة افتراضية",
                "اختبار SafeExecute"
            )

            ErrorHandler.LogInfo($"نتيجة SafeExecute: {result}")

            ' اختبار SafeExecute مع خطأ
            ErrorHandler.SafeExecute(
                Sub()
                    Throw New Exception("خطأ اختباري")
                End Sub,
                "اختبار SafeExecute مع خطأ"
            )

            ErrorHandler.LogInfo("تم اجتياز اختبار ErrorHandler بنجاح")

        Catch ex As Exception
            Console.WriteLine($"فشل في اختبار ErrorHandler: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' اختبار مقاومة SQL Injection
    ''' </summary>
    Public Shared Sub TestSQLInjectionResistance()
        Try
            ErrorHandler.LogInfo("بدء اختبار مقاومة SQL Injection")

            ' محاولة SQL Injection
            Dim maliciousUsername As String = "admin'; DROP TABLE users; --"
            Dim maliciousPassword As String = "' OR '1'='1"

            ' هذا يجب أن يفشل بأمان
            Dim isValid As Boolean = SecureDatabaseManager.Instance.ValidateUser(maliciousUsername, maliciousPassword)

            If Not isValid Then
                ErrorHandler.LogInfo("تم صد محاولة SQL Injection بنجاح")
            Else
                ErrorHandler.LogError("فشل في صد محاولة SQL Injection", New Exception("Security vulnerability detected"))
            End If

            ErrorHandler.LogInfo("تم اجتياز اختبار مقاومة SQL Injection")

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في اختبار مقاومة SQL Injection", ex)
        End Try
    End Sub

    ''' <summary>
    ''' تشغيل جميع الاختبارات
    ''' </summary>
    Public Shared Sub RunAllTests()
        Try
            ErrorHandler.Initialize()
            ErrorHandler.LogInfo("بدء تشغيل جميع الاختبارات الأمنية")

            TestErrorHandler()
            TestSecureDatabaseManager()
            TestSQLInjectionResistance()

            ErrorHandler.LogInfo("تم الانتهاء من جميع الاختبارات الأمنية")

        Catch ex As Exception
            Console.WriteLine($"خطأ في تشغيل الاختبارات: {ex.Message}")
        End Try
    End Sub
End Class
