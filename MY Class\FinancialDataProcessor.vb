﻿Imports System
Imports System.Data.SqlClient

Public Class FinancialDataProcessor

    Private connectionString As String = "Your_Connection_String_Here"
    Private gridView As DataGridView

    Public Sub New(ByVal grid As DataGridView)
        Me.gridView = grid
    End Sub

    Public Sub ProcessFinancialData(ByVal startDate As Date, ByVal endDate As Date, ByVal treasuryCode As String)
        Using connection As New SqlConnection(connectionString)
            connection.Open()

            ' Delete existing data
            DeleteExistingData(connection)

            ' Process data for each day
            'Dim currentDate As Date
            'For currentDate = startDate To endDate
            '    Dim financialData As FinancialDayData = GetFinancialDataForDay(connection, currentDate, treasuryCode)
            '    If financialData.HasTransactions Then
            '        InsertOrUpdateFinancialData(connection, financialData)
            '    End If
            'Next

            ' Load data to GridView
            LoadDataToGridView(connection)
        End Using
    End Sub

    Private Sub DeleteExistingData(ByVal connection As SqlConnection)
        Using cmd As New SqlCommand("DELETE FROM Date_Move_Money", connection)
            cmd.ExecuteNonQuery()
        End Using
    End Sub

    Private Function GetFinancialDataForDay(ByVal connection As SqlConnection, ByVal dateValue As Date, ByVal treasuryCode As String) As FinancialDayData
        Dim data As New FinancialDayData With {.Date = dateValue, .TreasuryCode = treasuryCode}

        ' Helper function to get sum for a specific table and condition
        Dim getSum = Function(tableName As String, columnName As String, dateColumnName As String) As Double
                         Using cmd As New SqlCommand($"SELECT COALESCE(SUM({columnName}), 0) FROM {tableName} WHERE {dateColumnName} = @Date", connection)
                             cmd.Parameters.AddWithValue("@Date", dateValue)
                             Return Convert.ToDouble(cmd.ExecuteScalar())
                         End Using
                     End Function

        ' Get sums for various financial metrics
        With data
            .CustPay = getSum("Vst", "VND_amx", "VND_dt")
            .Vnd = getSum("vnd", "VND_amx", "VND_dt")
            .Sal = getSum("BillsalData", "totalprice", "bill_date")
            .Purchase = getSum("BilltINData", "totalprice", "bill_date")
            .Bsal = getSum("IM_Vst", "VND_amx", "VND_dt")
            .Bvnd = getSum("IM_Vnd", "VND_amx", "VND_dt")
            .Vst_disc = getSum("Vst_disc", "amnt", "pdate")
            .Vndr_disc = getSum("vndr_disc", "amnt", "pdate")
            .Vst_disc_other = getSum("Vst_disc_other", "amnt", "pdate")
            .Expenses = getSum("Expenses", "Exp_Value", "Exp_Date")
            .CashWithdrawal = getSum("EmployeesDiscountReward", "Amount", "Move_Date")
            .NetSalary = getSum("Salary", "NetSalary", "SalaryDate")
            .Vst_Receipts = getSum("Vst_Receipts", "VND_amx", "VND_dt")
            .Vnd_Receipts = getSum("Vnd_Receipts", "VND_amx", "VND_dt")
            .Check_Out = getSum("Capital_Partner_Withdrawals", "Amount", "Capital_Date") ' Where Capital_Type_Code = '1'
            .Check_In = getSum("Capital_Partner_Withdrawals", "Amount", "Capital_Date") ' Where Capital_Type_Code = '2'
            .Other_Income = getSum("Other_Income", "Amount", "Income_Date")
            .Capital_Withdraw = getSum("Capital_Reserve_CheckOut_Deposit", "Amount", "Capital_Date") ' Where Capital_Type_Code = '1'
            .Capital_Deposit = getSum("Capital_Reserve_CheckOut_Deposit", "Amount", "Capital_Date") ' Where Capital_Type_Code = '2'
            .Treasury_Withdraw = getSum("TreasuryMovement_Withdraw", "Treasury_Amount", "Treasury_Date")
            .Treasury_Deposit = getSum("TreasuryMovement_Deposit", "Treasury_Amount", "Treasury_Date")
            .TotalNetAssets = getSum("Assets", "NetValue", "DatePurchase")

            ' Calculate total
            .Total = .CustPay - .Bsal - .Vnd + .Bvnd - .Vst_Receipts + .Vnd_Receipts - .Expenses -
                     .CashWithdrawal - .NetSalary - .Check_Out + .Check_In + .Other_Income -
                     .Capital_Withdraw + .Capital_Deposit - .Treasury_Withdraw + .Treasury_Deposit - .TotalNetAssets
            .Total = Math.Round(.Total, 2)
        End With

        Return data
    End Function

    Private Sub InsertOrUpdateFinancialData(ByVal connection As SqlConnection, ByVal data As FinancialDayData)
        Dim sql As String = "
            IF EXISTS (SELECT 1 FROM Date_Move_Money WHERE Item_Date = @Date)
                UPDATE Date_Move_Money SET
                    CustPay = @CustPay, sal = @Sal, Bsal = @Bsal, vnd = @Vnd, expe = @Expenses,
                    CashWithdrawal = @CashWithdrawal, NetSalary = @NetSalary, purchase = @Purchase,
                    Bvnd = @Bvnd, Vst_disc = @Vst_disc, vndr_disc = @Vndr_disc, Vst_disc_other = @Vst_disc_other,
                    Vst_Receipts = @Vst_Receipts, Vnd_Receipts = @Vnd_Receipts, Check_Out = @Check_Out,
                    Check_In = @Check_In, Xtotal = @Total, Treasury_Code = @TreasuryCode,
                    Other_Income = @Other_Income, Treasury_Deposit = @Treasury_Deposit,
                    Treasury_Withdraw = @Treasury_Withdraw, Capital_Deposit = @Capital_Deposit,
                    Capital_Withdraw = @Capital_Withdraw, TotalNetAssets = @TotalNetAssets
                WHERE Item_Date = @Date
            ELSE
                INSERT INTO Date_Move_Money (
                    Item_Date, CustPay, sal, Bsal, vnd, expe, CashWithdrawal, NetSalary, purchase,
                    Bvnd, Vst_disc, vndr_disc, Vst_disc_other, Vst_Receipts, Vnd_Receipts,
                    Check_Out, Check_In, Xtotal, Treasury_Code, Other_Income, Treasury_Deposit,
                    Treasury_Withdraw, Capital_Deposit, Capital_Withdraw, TotalNetAssets
                ) VALUES (
                    @Date, @CustPay, @Sal, @Bsal, @Vnd, @Expenses, @CashWithdrawal, @NetSalary,
                    @Purchase, @Bvnd, @Vst_disc, @Vndr_disc, @Vst_disc_other, @Vst_Receipts,
                    @Vnd_Receipts, @Check_Out, @Check_In, @Total, @TreasuryCode, @Other_Income,
                    @Treasury_Deposit, @Treasury_Withdraw, @Capital_Deposit, @Capital_Withdraw, @TotalNetAssets
                )"

        Using cmd As New SqlCommand(sql, connection)
            With cmd.Parameters
                .AddWithValue("@Date", data.Date)
                .AddWithValue("@CustPay", data.CustPay)
                .AddWithValue("@Sal", data.Sal)
                .AddWithValue("@Bsal", data.Bsal)
                .AddWithValue("@Vnd", data.Vnd)
                .AddWithValue("@Expenses", data.Expenses)
                .AddWithValue("@CashWithdrawal", data.CashWithdrawal)
                .AddWithValue("@NetSalary", data.NetSalary)
                .AddWithValue("@Purchase", data.Purchase)
                .AddWithValue("@Bvnd", data.Bvnd)
                .AddWithValue("@Vst_disc", data.Vst_disc)
                .AddWithValue("@Vndr_disc", data.Vndr_disc)
                .AddWithValue("@Vst_disc_other", data.Vst_disc_other)
                .AddWithValue("@Vst_Receipts", data.Vst_Receipts)
                .AddWithValue("@Vnd_Receipts", data.Vnd_Receipts)
                .AddWithValue("@Check_Out", data.Check_Out)
                .AddWithValue("@Check_In", data.Check_In)
                .AddWithValue("@Total", data.Total)
                .AddWithValue("@TreasuryCode", data.TreasuryCode)
                .AddWithValue("@Other_Income", data.Other_Income)
                .AddWithValue("@Treasury_Deposit", data.Treasury_Deposit)
                .AddWithValue("@Treasury_Withdraw", data.Treasury_Withdraw)
                .AddWithValue("@Capital_Deposit", data.Capital_Deposit)
                .AddWithValue("@Capital_Withdraw", data.Capital_Withdraw)
                .AddWithValue("@TotalNetAssets", data.TotalNetAssets)
            End With
            cmd.ExecuteNonQuery()
        End Using
    End Sub

    Private Sub LoadDataToGridView(ByVal connection As SqlConnection)
        Dim sql As String = "SELECT * FROM Date_Move_Money ORDER BY Item_Date"
        Using adapter As New SqlDataAdapter(sql, connection)
            Dim dataTable As New DataTable()
            adapter.Fill(dataTable)
            gridView.DataSource = dataTable
        End Using
    End Sub
End Class

Public Class FinancialDayData
    Public Property [Date] As Date
    Public Property CustPay As Double
    Public Property Vnd As Double
    Public Property Sal As Double
    Public Property Purchase As Double
    Public Property Bsal As Double
    Public Property Bvnd As Double
    Public Property Vst_disc As Double
    Public Property Vndr_disc As Double
    Public Property Vst_disc_other As Double
    Public Property Expenses As Double
    Public Property CashWithdrawal As Double
    Public Property NetSalary As Double
    Public Property Vst_Receipts As Double
    Public Property Vnd_Receipts As Double
    Public Property Check_Out As Double
    Public Property Check_In As Double
    Public Property Other_Income As Double
    Public Property Capital_Withdraw As Double
    Public Property Capital_Deposit As Double
    Public Property Treasury_Withdraw As Double
    Public Property Treasury_Deposit As Double
    Public Property TotalNetAssets As Double
    Public Property Total As Double
    Public Property TreasuryCode As String

    Public ReadOnly Property HasTransactions As Boolean
        Get
            Return CustPay <> 0 OrElse Vnd <> 0 OrElse Sal <> 0 OrElse Purchase <> 0 OrElse
                   Bsal <> 0 OrElse Bvnd <> 0 OrElse Vst_disc <> 0 OrElse Vndr_disc <> 0 OrElse
                   Vst_disc_other <> 0 OrElse Expenses <> 0 OrElse CashWithdrawal <> 0 OrElse
                   NetSalary <> 0 OrElse Vst_Receipts <> 0 OrElse Vnd_Receipts <> 0 OrElse
                   Check_Out <> 0 OrElse Check_In <> 0 OrElse Other_Income <> 0 OrElse
                   Capital_Withdraw <> 0 OrElse Capital_Deposit <> 0 OrElse Treasury_Withdraw <> 0 OrElse
                   Treasury_Deposit <> 0 OrElse TotalNetAssets <> 0
        End Get
    End Property
End Class