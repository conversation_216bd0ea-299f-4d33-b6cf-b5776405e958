﻿Imports CrystalDecisions.CrystalReports.Engine
Public Class frmShowDailyWages
    Dim WithEvents BS As New BindingSource
    Private Sub frmShowDailyWages_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Bra.Fil("MOVES", "MOVRegNumber", cmbRegNumber)
        GroupBox2.Top = 600
        GetData()
    End Sub

    Private Sub btnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnShow.Click
        GetData()
    End Sub

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT MOVRegNumber AS [رقم القيد],MOVDate AS [التاريخ], MOVStatement AS [بيان القيد], MOVDebtor AS [اجمالى المدين], MOVCreditor AS [اجمالى الدائن] FROM MOVES where MOVRegNumber <> N''"

        If ChbAll.Checked = False Then
            S = S & "and MOVRegNumber =N'" & cmbRegNumber.Text.Trim & "'"
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & "and MOVDate >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and MOVDate <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

        'DataGridView1.Columns(0).Width = 40
        'DataGridView1.Columns(1).Width = 40
        'DataGridView1.Columns(2).Width = 60
        'DataGridView1.Columns(3).Width = 60
        'DataGridView1.Columns(4).Width = 60
        SumDGV()
    End Sub
    Private Sub SumDGV()
        Dim SM As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM = SM + DataGridView1.Rows(i).Cells(3).Value
        Next
        txtTotalDebtor.Text = SM

        Dim SM2 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM2 = SM2 + DataGridView1.Rows(i).Cells(4).Value
        Next
        txtTotalCreditor.Text = SM2

    End Sub
    Private Sub ChbAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChbAll.CheckedChanged
        If ChbAll.Checked = True Then
            cmbRegNumber.Enabled = False
            cmbRegNumber.SelectedIndex = -1
        ElseIf ChbAll.Checked = False Then
            cmbRegNumber.Enabled = True
        End If
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click
        If ChbAll.Checked = False Then
            If cmbRegNumber.Text = "" Then MsgBox("فضلا أدخل رقم القيد", MsgBoxStyle.Exclamation) : cmbRegNumber.Focus() : Exit Sub
        End If
        Dim txtFROMDate As String
        Dim txtToDate As String
        Dim f As New Frm_PrintReports
        Dim rpt As New rptAccounts2
        Dim txt As TextObject
        txtFROMDate = Format(Me.DateTimePicker1.Value, "yyy, MM, dd, 00, 00, 000")
        txtToDate = Format(Me.DateTimePicker2.Value, "yyy, MM, dd, 00, 00, 00")
        'On Error Resume Next
        GETSERVERNAMEANDDATABASENAME(rpt, DataBaseName, PasswordServer, UserNameServer)
        If ChkWithoutDate.Checked = False Or ChbAll.Checked = False Then
            If ChbAll.Checked = False Then
                rpt.RecordSelectionFormula = "{MOVES.MOVDate} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")AND{MOVES.MOVRegNumber} =" & cmbRegNumber.Text
                txt = rpt.Section1.ReportObjects("Text9")
                txt.Text = "خلال الفترة من" & Format(Me.DateTimePicker1.Value, "dd - MM - yyyy") & " الى " & Format(Me.DateTimePicker2.Value, "dd - MM - yyyy")
            Else
                rpt.RecordSelectionFormula = "{MOVES.MOVDate} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")"
                txt = rpt.Section1.ReportObjects("Text9")
                txt.Text = "خلال الفترة من" & Format(Me.DateTimePicker1.Value, "dd - MM - yyyy") & " الى " & Format(Me.DateTimePicker2.Value, "dd - MM - yyyy")
            End If
        End If
        AddReportView()
        Dim txtname, txtNameAr, txtNameEn As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير بالقيود اليومية"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        f.CrystalReportViewer1.ReportSource = rpt
        f.CrystalReportViewer1.Zoom(95%)
        f.CrystalReportViewer1.RefreshReport()
        f.Text = "تقرير بالقيود اليومية"
        f.Show()
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim NumberID As String
            NumberID = DataGridView1.SelectedRows(0).Cells(0).Value


            cmd.CommandText = "delete from MOVES where MOVRegNumber =N'" & NumberID & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete from MOVESDATA where MOVRegNumber =N'" & NumberID & "'" : cmd.ExecuteNonQuery()
        Next
        GetData()
    End Sub

    Private Sub btnBack_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBack.Click
        GroupBox2.Top = 600
        GroupBox2.Enabled = True
        GroupBox1.Enabled = True
    End Sub

    Private Sub GetDetails()
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim NumberID As String
        NumberID = DataGridView1.SelectedRows(0).Cells(0).Value

        GroupBox1.Enabled = False
        GroupBox2.Top = 140

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select MOVDNameAccount as [أسم الحساب],MOVNumberAccount as [رقم الحساب] ,MOVDDebtor as [مدين],MOVDCreditor as [دائن],MOVDNAMStatement as [بيان الحركة] from MOVESDATA where MOVRegNumber =N'" & NumberID & "'"
        dr = cmd.ExecuteReader
        DataGridView2.DataSource = Cls.PopulateDataView(dr)
        SumDGVDetails()
    End Sub

    Private Sub DataGridView1_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles DataGridView1.DoubleClick
        GetDetails()
    End Sub

    Private Sub SumDGVDetails()
        Dim SM As Double
        For i As Integer = 0 To DataGridView2.Rows.Count - 1
            SM = SM + DataGridView2.Rows(i).Cells(2).Value
        Next
        txtDebtor.Text = SM

        Dim SM2 As Double
        For i As Integer = 0 To DataGridView2.Rows.Count - 1
            SM2 = SM2 + DataGridView2.Rows(i).Cells(3).Value
        Next
        txtCreditor.Text = SM2

    End Sub

    Private Sub btnPrintStat_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrintStat.Click
        If ChbAll.Checked = False Then
            If cmbRegNumber.Text = "" Then MsgBox("فضلا أدخل رقم القيد", MsgBoxStyle.Exclamation) : cmbRegNumber.Focus() : Exit Sub
        End If
        Dim txtFROMDate As String
        Dim txtToDate As String
        Dim f As New Frm_PrintReports
        Dim rpt As New rptDailyWagesAll
        Dim txt As TextObject
        txtFROMDate = Format(Me.DateTimePicker1.Value, "yyy, MM, dd, 00, 00, 000")
        txtToDate = Format(Me.DateTimePicker2.Value, "yyy, MM, dd, 00, 00, 00")
        'On Error Resume Next
        GETSERVERNAMEANDDATABASENAME(rpt, DataBaseName, PasswordServer, UserNameServer)
        If ChkWithoutDate.Checked = False Or ChbAll.Checked = False Then
            If ChbAll.Checked = False Then
                rpt.RecordSelectionFormula = "{MOVESDATA.MOVDDate} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")AND{MOVESDATA.MOVRegNumber} =" & cmbRegNumber.Text
                txt = rpt.Section1.ReportObjects("Text9")
                txt.Text = "خلال الفترة من" & Format(Me.DateTimePicker1.Value, "dd - MM - yyyy") & " الى " & Format(Me.DateTimePicker2.Value, "dd - MM - yyyy")
            Else
                rpt.RecordSelectionFormula = "{MOVESDATA.MOVDate} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")"
                txt = rpt.Section1.ReportObjects("Text9")
                txt.Text = "خلال الفترة من" & Format(Me.DateTimePicker1.Value, "dd - MM - yyyy") & " الى " & Format(Me.DateTimePicker2.Value, "dd - MM - yyyy")
            End If
        End If
        AddReportView()
        Dim txtname, txtNameAr, txtNameEn As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير بحسابات القيود اليومية"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        f.CrystalReportViewer1.ReportSource = rpt
        f.CrystalReportViewer1.Zoom(95%)
        f.CrystalReportViewer1.RefreshReport()
        f.Show()
    End Sub

    Private Sub btnEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnEdit.Click
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        EditItmId = DataGridView1.SelectedRows(0).Cells(0).Value

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete from TmpBillsalData" : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "insert into TmpBillsalData (bill_no,itm_cat,itm_id,price,totalprice,itm_Unity) select MOVRegNumber,MOVDNameAccount,MOVNumberAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement from MOVESDATA where MOVRegNumber =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
        Action = "Edit"
        Me.Close()
        frmDailyWages.Show()
    End Sub
End Class