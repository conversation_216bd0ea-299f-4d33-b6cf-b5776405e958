﻿Imports CrystalDecisions.CrystalReports.Engine

Public Class Frm_Product_Manufacturing_Show
    Dim Dt_AddBill As New DataTable
    Dim RNXD As Integer
    Dim itm_id As String = ""
    Dim TinPrice As String = ""
    Dim Manufacturing_ID As String
    Dim ActionRead As Boolean = False
    Dim WithEvents BS As New BindingSource


#Region "View Items"
    Private Sub Frm_Offers_Items_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Bra.Fil("groups", "g_name", cmbCatsManufacturing)
        Cls.fill_combo_Branch("stores", "store", cmbStoreManufacturing)
        Cls.fill_combo_Branch("stores", "store", cmbStoreManufacturingEdit)
        Cls.fill_combo_Branch("stores", "store", cmbStoreMaterial)
        PanelEdit.Top = 5000
        PanelquntManufacturing.Top = 5000
    End Sub

    Private Sub cmbStoreManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbStoreManufacturing.SelectedIndexChanged
        If cmbStoreManufacturing.Text.Trim = "" Then Exit Sub
        cmbCatsManufacturing.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select distinct group_name from Items where Stores =N'" & cmbStoreManufacturing.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbCatsManufacturing.Items.Add(Trim(dr(0)))
        Loop
        cmbCatsManufacturing.Text = ""
        cmbCatsManufacturing.Focus()
    End Sub

    Private Sub cmbCatsManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbCatsManufacturing.SelectedIndexChanged
        If cmbCatsManufacturing.Text.Trim = "" Then Exit Sub
        cmbItemsManufacturing.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sname from Items where group_name =N'" & cmbCatsManufacturing.Text & "' and Stores =N'" & cmbStoreManufacturing.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbItemsManufacturing.Items.Add(Trim(dr(0)))
        Loop
        cmbItemsManufacturing.Text = ""
        cmbItemsManufacturing.Focus()
    End Sub

    Private Sub cmbStoreManufacturing_DropDown(sender As Object, e As EventArgs) Handles cmbStoreManufacturing.DropDown
        cmbCatsManufacturing.Text = ""
        cmbItemsManufacturing.Text = ""
    End Sub

    Private Sub cmbCatsManufacturing_DropDown(sender As Object, e As EventArgs) Handles cmbCatsManufacturing.DropDown
        cmbItemsManufacturing.Text = ""
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        GetData()
        GetDetails()
    End Sub

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()

        S = "SELECT Manufacturing_ID AS [رقم العملية],CAST(itm_id_Manufacturing AS float) AS [الباركود], sname AS [أسم الصنف], CostPrice AS [سعر التكلفة], Weightqunt AS [الوزن],Manufacturing_Allowance as [بدل تصنيع],Filling_Allowance as [بدل تعبئة], Stores_Manufacturing AS [المخزن] FROM View_Product_Manufacturing_Show where Manufacturing_ID <> N''"
        If chkAll.Checked = False Then
            If cmbStoreManufacturing.Text <> "" Then
                S = S & " and  Stores_Manufacturing =N'" & cmbStoreManufacturing.Text & "'"
            End If
            If cmbCatsManufacturing.Text <> "" Then
                S = S & " and  group_name =N'" & cmbCatsManufacturing.Text & "'"
            End If
            If cmbItemsManufacturing.Text <> "" Then
                S = S & " and  sname =N'" & cmbItemsManufacturing.Text & "'"
            End If
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم العملية]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [الباركود]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [أسم الصنف]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        dgv_Manufacturing.DataSource = Cls.PopulateDataView(dr)

        'Dim SM As String
        'For i As Integer = 0 To dgv_Manufacturing.RowCount - 1
        '    SM = Val(dgv_Manufacturing.Rows(i).Cells(3).Value) * Val(dgv_Manufacturing.Rows(i).Cells(4).Value)
        '    dgv_Manufacturing.Rows(i).Cells(5).Value = SM
        'Next

        'Dim SM1 As String
        'For i As Integer = 0 To dgv_Manufacturing.RowCount - 1
        '    SM1 = Val(dgv_Manufacturing.Rows(i).Cells(5).Value) + Val(dgv_Manufacturing.Rows(i).Cells(6).Value) + Val(dgv_Manufacturing.Rows(i).Cells(7).Value)
        '    dgv_Manufacturing.Rows(i).Cells(5).Value = SM1
        'Next


        dgv_Manufacturing.Columns(2).Width = 350
        dgv_Manufacturing.Columns(7).Width = 100
    End Sub

    Private Sub dgv_Manufacturing_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgv_Manufacturing.CellClick
        GetDetails()
    End Sub

    Private Sub GetDetails()
        If dgv_Manufacturing.RowCount = 0 Then Beep() : Exit Sub
        If (dgv_Manufacturing.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim Manufacturing_ID As String
        Manufacturing_ID = dgv_Manufacturing.SelectedRows(0).Cells(0).Value
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id as [الباركود] ,sname as [اسم الصنف],TinPrice as [سعر التكلفة] ,qu_unity as [الكمية],UserName as [إجمالى التكلفة],Stores as [المخزن],AutoNumber as [AutoNumber] from View_Product_Manufacturing where Manufacturing_ID =N'" & Manufacturing_ID & "' ORDER BY AutoNumber"
        dr = cmd.ExecuteReader
        dgv_Material.DataSource = Cls.PopulateDataView(dr)
        dgv_Material.Columns(1).Width = 400
        dgv_Material.Columns(6).Visible = False

        Dim SM2 As String
        For i As Integer = 0 To dgv_Material.RowCount - 1
            SM2 = Val(dgv_Material.Rows(i).Cells(2).Value.ToString()) * Val(dgv_Material.Rows(i).Cells(3).Value.ToString())
            dgv_Material.Rows(i).Cells(4).Value = SM2
        Next

        Dim SM As Double
        For i As Integer = 0 To dgv_Manufacturing.Rows.Count - 1
            SM = SM + dgv_Manufacturing.Rows(i).Cells(3).Value.ToString()
        Next
        txtTotalCostPrice.Text = SM

        Dim SM1 As Double
        For i As Integer = 0 To dgv_Manufacturing.Rows.Count - 1
            SM1 = SM1 + dgv_Manufacturing.Rows(i).Cells(4).Value.ToString()
        Next
        txtTotalWeight.Text = SM1

    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbCatsManufacturing.Enabled = False
            cmbItemsManufacturing.Enabled = False
            cmbStoreManufacturing.Enabled = False
        Else
            cmbCatsManufacturing.Enabled = True
            cmbItemsManufacturing.Enabled = True
            cmbStoreManufacturing.Enabled = True
        End If
    End Sub
#End Region

#Region "Delete Items"
    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If dgv_Manufacturing.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If dgv_Manufacturing.RowCount = 0 Then Beep() : Exit Sub
        If (dgv_Manufacturing.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim ItmID As String

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To dgv_Manufacturing.SelectedRows.Count - 1
            ItmID = dgv_Manufacturing.SelectedRows(i).Cells(0).Value

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete From  ManufacturingProduct where Manufacturing_ID =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  ManufacturingProductAdd where Manufacturing_ID =N'" & ItmID & "'" : cmd.ExecuteNonQuery()

        Next
        GetData()
        GetDetails()
    End Sub

#End Region

#Region "Print"
    Private Sub btnPrint_Click(sender As Object, e As EventArgs) Handles btnPrint.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If dgv_Manufacturing.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If
        S = "SELECT Manufacturing_ID AS [رقم العملية],CAST(itm_id_Manufacturing AS float) AS [الباركود], sname AS [أسم الصنف], CostPrice AS [سعر التكلفة], Weightqunt AS [الوزن],Manufacturing_Allowance as [بدل تصنيع],Filling_Allowance as [بدل تعبئة], Stores_Manufacturing AS [المخزن] FROM View_Product_Manufacturing_Show where Manufacturing_ID <> N''"

        If dgv_Manufacturing.RowCount = 0 Then Beep() : Exit Sub
        If (dgv_Manufacturing.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim SManufacturingID As String = dgv_Manufacturing.SelectedRows(0).Cells(0).Value
        Dim itmidManufacturing As String = dgv_Manufacturing.SelectedRows(0).Cells(1).Value
        Dim sname As String = dgv_Manufacturing.SelectedRows(0).Cells(2).Value
        Dim CostPrice As String = dgv_Manufacturing.SelectedRows(0).Cells(3).Value
        Dim Weightqunt As String = dgv_Manufacturing.SelectedRows(0).Cells(4).Value
        Dim TotalCostPrice As String = Val(CostPrice) * Val(Weightqunt)
        Dim Manufacturing_Allowance As String = dgv_Manufacturing.SelectedRows(0).Cells(5).Value
        Dim Filling_Allowance As String = dgv_Manufacturing.SelectedRows(0).Cells(6).Value
        Dim StoresManufacturing As String = dgv_Manufacturing.SelectedRows(0).Cells(7).Value

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To dgv_Material.Rows.Count - 1
            S = "insert into PrintSalesPurchases(Company_Branch_ID,itm_id,itm_name,price,qu,totalprice,store)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & dgv_Material.Rows(i).Cells(0).Value & "',N'" & dgv_Material.Rows(i).Cells(1).Value & "',N'" & dgv_Material.Rows(i).Cells(2).Value & "',N'" & dgv_Material.Rows(i).Cells(3).Value & "',N'" & dgv_Material.Rows(i).Cells(4).Value & "',N'" & dgv_Material.Rows(i).Cells(5).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        AddReportView()
        Dim rpt As New Rpt_Product_Manufacturing

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        AddReportView()
        Dim txtname, txtNameAr, txtNameEn, itmidManufacturing_Object, sname_Object, CostPrice_Object, Weightqunt_Object, Stores_Manufacturing_Object, TotalCostPrice_Object, Manufacturing_Allowance_Object, Filling_Allowance_Object As TextObject

        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير بالمنتج المصنع"
        txtNameAr = rpt.Section1.ReportObjects("txtTitelNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelNameEn")
        txtNameEn.Text = NameEnCompany


        'itmidManufacturing_Object = rpt.Section1.ReportObjects("txtitm_id")
        'itmidManufacturing_Object.Text = itmidManufacturing
        sname_Object = rpt.Section1.ReportObjects("txtsname")
        sname_Object.Text = sname
        CostPrice_Object = rpt.Section1.ReportObjects("txtCostPrice")
        CostPrice_Object.Text = CostPrice
        Weightqunt_Object = rpt.Section1.ReportObjects("txtWeightqunt")
        Weightqunt_Object.Text = Weightqunt
        Stores_Manufacturing_Object = rpt.Section1.ReportObjects("txtStores")
        Stores_Manufacturing_Object.Text = StoresManufacturing
        TotalCostPrice_Object = rpt.Section1.ReportObjects("txtTotalCostPrice")
        TotalCostPrice_Object.Text = TotalCostPrice
        Manufacturing_Allowance_Object = rpt.Section1.ReportObjects("txtManufacturing_Allowance")
        Manufacturing_Allowance_Object.Text = Manufacturing_Allowance
        Filling_Allowance_Object = rpt.Section1.ReportObjects("txtFilling_Allowance")
        Filling_Allowance_Object.Text = Filling_Allowance

        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "تقرير بالمنتج المصنع"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

#End Region

#Region "Edit Items"
    Private Sub cmbStoreManufacturingEdit_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbStoreManufacturingEdit.SelectedIndexChanged
        If ActionRead = False Then
            If cmbStoreManufacturingEdit.Text.Trim = "" Then Exit Sub
            cmbCatsManufacturingEdit.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct group_name from Items where Stores =N'" & cmbStoreManufacturingEdit.Text & "' order by 1"
            dr = cmd.ExecuteReader
            Do While dr.Read
                cmbCatsManufacturingEdit.Items.Add(Trim(dr(0)))
            Loop
            cmbCatsManufacturingEdit.Text = ""
        End If
    End Sub

    Private Sub cmbCatsManufacturingEdit_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbCatsManufacturingEdit.SelectedIndexChanged
        If ActionRead = False Then
            If cmbCatsManufacturingEdit.Text.Trim = "" Then Exit Sub
            cmbItemsManufacturingEdit.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sname from Items where group_name =N'" & cmbCatsManufacturingEdit.Text & "' and Stores =N'" & cmbStoreManufacturingEdit.Text & "' order by 1"
            dr = cmd.ExecuteReader
            Do While dr.Read
                cmbItemsManufacturingEdit.Items.Add(Trim(dr(0)))
            Loop
            cmbItemsManufacturingEdit.Text = ""
        End If

    End Sub

    Private Sub cmbStoreManufacturingEdit_DropDown(sender As Object, e As EventArgs) Handles cmbStoreManufacturingEdit.DropDown
        cmbCatsManufacturingEdit.Text = ""
        cmbItemsManufacturingEdit.Text = ""
    End Sub

    Private Sub cmbStoreManufacturingEdit_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbStoreManufacturingEdit.KeyUp
        If e.KeyCode = 13 Then
            cmbCatsManufacturingEdit.Focus()
        End If
    End Sub

    Private Sub cmbItemsManufacturingEdit_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbItemsManufacturingEdit.SelectedIndexChanged
        If ActionRead = False Then
            Get_ITMID(cmbItemsManufacturingEdit.Text, cmbCatsManufacturingEdit.Text, cmbStoreManufacturingEdit.Text)
            txtItm_idManufacturing.Text = itm_id
            txtManufacturing_Allowance.Focus()
        End If
    End Sub

    Private Sub Get_ITMID(ByVal Items As String, ByVal Cats As String, ByVal Store As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id,TinPrice from items where sname=N'" & Items & "' and group_name=N'" & Cats & "' and Stores=N'" & Store & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            itm_id = dr("itm_id")
            TinPrice = dr("TinPrice")
        End If
    End Sub

    Private Sub cmbStoreMaterial_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbStoreMaterial.SelectedIndexChanged
        If cmbStoreMaterial.Text.Trim = "" Then Exit Sub
        cmbCatsMaterial.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select distinct group_name from Items where Stores =N'" & cmbStoreMaterial.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbCatsMaterial.Items.Add(Trim(dr(0)))
        Loop
        cmbCatsMaterial.Text = ""
    End Sub

    Private Sub cmbStoreMaterial_DropDown(sender As Object, e As EventArgs) Handles cmbStoreMaterial.DropDown
        cmbCatsMaterial.Text = ""
        cmbItemsMaterial.Text = ""
        txtquntUnity.Text = ""
        cmbUnity.Text = ""
        txtqunt.Text = ""
        txtTinPrice.Text = ""
    End Sub

    Private Sub cmbStoreMaterial_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbStoreMaterial.KeyUp
        If e.KeyCode = 13 Then
            cmbCatsMaterial.Focus()
        End If
    End Sub

    Private Sub cmbCatsMaterial_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbCatsMaterial.SelectedIndexChanged
        If cmbCatsMaterial.Text.Trim = "" Then Exit Sub
        cmbItemsMaterial.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sname from Items where group_name =N'" & cmbCatsMaterial.Text & "' and Stores =N'" & cmbStoreMaterial.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbItemsMaterial.Items.Add(Trim(dr(0)))
        Loop
        cmbItemsMaterial.Text = ""
    End Sub

    Private Sub cmbItemsMaterial_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbItemsMaterial.SelectedIndexChanged
        Get_ITMID(cmbItemsMaterial.Text, cmbCatsMaterial.Text, cmbStoreMaterial.Text)
        txtItm_idMaterial.Text = itm_id
        txtTinPrice.Text = TinPrice

        GetItemsUnity(cmbUnity, txtItm_idMaterial.Text)

        txtqunt.Text = 1
        txtquntUnity.Text = 1
        txtquntUnity.Focus()
        txtquntUnity.SelectAll()
    End Sub

    Private Sub cmbItemsMaterial_DropDown(sender As Object, e As EventArgs) Handles cmbItemsMaterial.DropDown
        cmbItemsMaterial.Text = ""
        txtItm_idMaterial.Text = ""
        txtqunt.Text = ""
    End Sub

    Private Sub cmbItemsMaterial_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbItemsMaterial.KeyUp
        If e.KeyCode = 13 Then
            txtqunt.Focus()
        End If
    End Sub

    Private Sub txtTinPrice_TextChanged(sender As Object, e As EventArgs) Handles txtTinPrice.TextChanged
        MyVars.CheckNumber(txtTinPrice)
    End Sub

    Private Sub txtqunt_TextChanged(sender As Object, e As EventArgs) Handles txtqunt.TextChanged
        MyVars.CheckNumber(txtqunt)
    End Sub

    Private Sub txtManufacturing_Allowance_TextChanged(sender As Object, e As EventArgs) Handles txtManufacturing_Allowance.TextChanged
        MyVars.CheckNumber(txtManufacturing_Allowance)
        SumTotal()
    End Sub

    Private Sub txtFilling_Allowance_TextChanged(sender As Object, e As EventArgs) Handles txtFilling_Allowance.TextChanged
        MyVars.CheckNumber(txtFilling_Allowance)
        SumTotal()
    End Sub

    Private Sub txtqunt_KeyUp(sender As Object, e As KeyEventArgs) Handles txtqunt.KeyUp
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub BtnAdd_Click(sender As Object, e As EventArgs) Handles BtnAdd.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateTextAdd() = False Then Exit Sub

        txtAutoNumber.Text = 1
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            txtAutoNumber.Text += 1
        Next

        Dgv_Add.DataSource = Fn_AddBill(txtItm_idMaterial.Text, cmbCatsMaterial.Text, cmbItemsMaterial.Text, txtTinPrice.Text, txtqunt.Text, txtquntUnity.Text, cmbUnity.Text, Val(txtTinPrice.Text) * Val(txtqunt.Text), cmbStoreMaterial.Text, txtAutoNumber.Text)

        cmbItemsMaterial.Text = ""
        txtqunt.Text = ""
        txtquntUnity.Text = ""
        cmbUnity.Text = ""
        txtItm_idMaterial.Text = ""
        txtTinPrice.Text = ""
        cmbItemsMaterial.Focus()

        SumTotal()

        Dgv_Add.Columns(0).ReadOnly = True
        Dgv_Add.Columns(1).ReadOnly = True
        Dgv_Add.Columns(2).ReadOnly = True
        Dgv_Add.Columns(3).ReadOnly = True
        Dgv_Add.Columns(5).ReadOnly = True
        Dgv_Add.Columns(6).ReadOnly = True
        Dgv_Add.Columns(8).ReadOnly = True
        Dgv_Add.Columns(8).ReadOnly = True
        Dgv_Add.Columns(4).Visible = False
        'Dgv_Add.Columns(9).Visible = False

    End Sub

    Function ValidateTextAdd() As Boolean
        If cmbStoreMaterial.Text = "" Then MsgBox("فضلا أختر أسم المخزن", MsgBoxStyle.Exclamation) : cmbStoreMaterial.Focus() : Return False
        If cmbCatsMaterial.Text = "" Then MsgBox("فضلا أختر أسم المجموعة", MsgBoxStyle.Exclamation) : cmbCatsMaterial.Focus() : Return False
        If cmbItemsMaterial.Text = "" Then MsgBox("فضلا أدخل أسم الخامة", MsgBoxStyle.Exclamation) : cmbItemsMaterial.Focus() : Return False
        If cmbStoreManufacturingEdit.Text = "" Then MsgBox("فضلا أختر أسم المخزن", MsgBoxStyle.Exclamation) : cmbStoreManufacturing.Focus() : Return False
        If cmbCatsManufacturingEdit.Text = "" Then MsgBox("فضلا أختر أسم المجموعة", MsgBoxStyle.Exclamation) : cmbCatsManufacturing.Focus() : Return False
        If cmbItemsManufacturingEdit.Text = "" Then MsgBox("فضلا أدخل أسم الخامة", MsgBoxStyle.Exclamation) : cmbItemsManufacturing.Focus() : Return False
        If txtqunt.Text = "" Then MsgBox("فضلا أدخل الكمية", MsgBoxStyle.Exclamation) : txtqunt.Focus() : Return False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from Items where group_name =N'" & cmbCatsMaterial.Text.Trim & "'and sname =N'" & cmbItemsMaterial.Text.Trim & "'and Stores =N'" & cmbStoreMaterial.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H = 0 Then
            MsgBox("الصنف غير مطابق من فضلك راجع الصنف ", MsgBoxStyle.Exclamation) : cmbItemsMaterial.Focus() : Return False
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from Items where group_name =N'" & cmbCatsManufacturingEdit.Text.Trim & "'and sname =N'" & cmbItemsManufacturingEdit.Text.Trim & "'and Stores =N'" & cmbStoreManufacturingEdit.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H = 0 Then
            MsgBox("الصنف غير مطابق من فضلك راجع الصنف ", MsgBoxStyle.Exclamation) : cmbItemsManufacturing.Focus() : Return False
        End If

        'For i As Integer = 0 To Dt_AddBill.Rows.Count - 1
        '    If Dgv_Add.Rows(i).Cells(2).Value = cmbItemsMaterial.Text.Trim Then MsgBox("صنف مكرر بنفس القائمة", MsgBoxStyle.Exclamation) : cmbItemsMaterial.Focus() : cmbItemsMaterial.SelectAll() : Return False
        'Next
        Return True
    End Function

    Friend Function Fn_AddBill(ByVal Col_Prc As String, ByVal Col_Cats As String, ByVal Col_Name As String, ByVal Col_Price As Double, ByVal Col_qunt As Double, ByVal Col_qu_unity As Double, ByVal Col_itm_Unity As String, ByVal Col_Total As Double, ByVal Col_Store As String, Col_AutoNumber As Integer) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("الباركود", GetType(String))
            Dt_AddBill.Columns.Add("أسم المجموعة", GetType(String))
            Dt_AddBill.Columns.Add("أسم الصنف", GetType(String))
            Dt_AddBill.Columns.Add("السعر", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية1", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية", GetType(Double))
            Dt_AddBill.Columns.Add("الوحدة", GetType(String))
            Dt_AddBill.Columns.Add("الاجمالى", GetType(Double))
            Dt_AddBill.Columns.Add("المخزن", GetType(String))
            Dt_AddBill.Columns.Add("مسلسل", GetType(Integer))
        End If

        DTV_Width()

        Dt_AddBill.Rows.Add(Col_Prc, Col_Cats, Col_Name, Col_Price, Col_qunt, Col_qu_unity, Col_itm_Unity, Col_Total, Col_Store, Col_AutoNumber)
        Return Dt_AddBill
    End Function

    Friend Sub DTV_Width()
        If Dgv_Add.Rows.Count > 10000 Then
            Dgv_Add.Columns(0).Width = 90
            Dgv_Add.Columns(1).Width = 100
            Dgv_Add.Columns(2).Width = 150
            Dgv_Add.Columns(3).Width = 90
            Dgv_Add.Columns(4).Width = 90
            Dgv_Add.Columns(5).Width = 90
            Dgv_Add.Columns(6).Width = 90
        End If
    End Sub

    Private Sub SumTotal()
        Dim SM As Double
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            SM = SM + Dgv_Add.Rows(i).Cells(7).Value
        Next
        txtCostPrice.Text = SM + Val(txtFilling_Allowance.Text) + Val(txtManufacturing_Allowance.Text)

        Dim SM1 As Double
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            SM1 = SM1 + Dgv_Add.Rows(i).Cells(5).Value
        Next
        txtWeight.Text = SM1

        txtNumberMaterial.Text = Dgv_Add.RowCount
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        If ValidateTextSave() = False Then Exit Sub


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ManufacturingProduct set itm_id_Manufacturing =N'" & txtItm_idManufacturing.Text & "',Stores_Manufacturing =N'" & cmbStoreManufacturingEdit.Text & "',Manufacturing_Allowance =N'" & txtManufacturing_Allowance.Text & "',Filling_Allowance =N'" & txtFilling_Allowance.Text & "',CostPrice =N'" & txtCostPrice.Text & "',Weightqunt =N'" & txtWeight.Text & "',NumberMaterial =N'" & txtNumberMaterial.Text & "',UserName =N'" & UserName & "' where Manufacturing_ID =N'" & txtManufacturing_ID.Text & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete From  ManufacturingProductAdd where Manufacturing_ID =N'" & txtManufacturing_ID.Text & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                S = "insert into ManufacturingProductAdd(Manufacturing_ID,itm_id,qu,qu_unity,itm_Unity,totalprice,Stores,UserName,AutoNumber)  values("
                S = S & "N'" & txtManufacturing_ID.Text & "',N'" & Dgv_Add.Rows(i).Cells(0).Value.Trim & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "',N'" & UserName & "',N'" & Dgv_Add.Rows(i).Cells(9).Value & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next

            ClearSave()

            MsgBox("تم تعديل بيانات المنتج بنجاح", MsgBoxStyle.Information)

            GetData()
            GetDetails()
            PanelEdit.Top = 5000

        Catch ex As Exception
            MsgBox(ex.ToString)
        End Try

    End Sub

    Function ValidateTextSave() As Boolean
        If cmbStoreManufacturingEdit.Text = "" Then MsgBox("فضلا أختر أسم المخزن", MsgBoxStyle.Exclamation) : cmbStoreManufacturingEdit.Focus() : Return False
        If cmbCatsManufacturingEdit.Text = "" Then MsgBox("فضلا أختر أسم المجموعة", MsgBoxStyle.Exclamation) : cmbCatsManufacturingEdit.Focus() : Return False
        If cmbItemsManufacturingEdit.Text = "" Then MsgBox("فضلا أدخل أسم الخامة", MsgBoxStyle.Exclamation) : cmbItemsManufacturingEdit.Focus() : Return False


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where group_name =N'" & cmbCatsManufacturingEdit.Text.Trim & "'and sname =N'" & cmbItemsManufacturingEdit.Text.Trim & "'and Stores =N'" & cmbStoreManufacturingEdit.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H = 0 Then
                MsgBox("الصنف غير مطابق من فضلك راجع الصنف ", MsgBoxStyle.Exclamation) : cmbItemsManufacturing.Focus() : Return False
            End If
        Catch ex As Exception
            MsgBox(ex.ToString)
        End Try
        Return True
    End Function

    Private Sub ClearSave()
        cmbCatsMaterial.Text = ""
        cmbCatsManufacturingEdit.Text = ""
        cmbStoreMaterial.Text = ""
        cmbStoreManufacturingEdit.Text = ""
        cmbItemsMaterial.Text = ""
        cmbItemsManufacturingEdit.Text = ""
        txtNumberMaterial.Text = ""
        txtqunt.Text = ""
        txtquntUnity.Text = ""
        cmbUnity.Text = ""
        txtItm_idMaterial.Text = ""
        txtItm_idManufacturing.Text = ""
        txtWeight.Text = ""
        txtCostPrice.Text = ""
        txtNumberMaterial.Text = ""
        txtFilling_Allowance.Text = ""
        txtManufacturing_Allowance.Text = ""
        txtTinPrice.Text = ""
        txtAutoNumber.Text = 1
        Dt_AddBill.Rows.Clear()
    End Sub

    Private Sub dgv_Manufacturing_DoubleClick(sender As Object, e As EventArgs) Handles dgv_Manufacturing.DoubleClick
        If dgv_Manufacturing.RowCount = 0 Then Beep() : Exit Sub
        If (dgv_Manufacturing.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        txtManufacturing_ID.Text = dgv_Manufacturing.SelectedRows(0).Cells(0).Value

        PanelEdit.Top = 80
        PanelEdit.Dock = DockStyle.Fill

        Dt_AddBill.Rows.Clear()

        ActionRead = True

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Top(100) PERCENT dbo.ManufacturingProductAdd.Manufacturing_ID, dbo.ManufacturingProductAdd.itm_id, dbo.Items.group_name, dbo.Items.sname, dbo.ManufacturingProductAdd.itm_Unity, dbo.Items.TinPrice,  dbo.ManufacturingProductAdd.qu, dbo.ManufacturingProductAdd.qu_unity, dbo.ManufacturingProductAdd.totalprice, dbo.ManufacturingProductAdd.Stores, dbo.ManufacturingProductAdd.AutoNumber  From dbo.ManufacturingProductAdd INNER Join dbo.Items ON dbo.ManufacturingProductAdd.itm_id = dbo.Items.itm_id Group By dbo.ManufacturingProductAdd.Manufacturing_ID, dbo.ManufacturingProductAdd.itm_id, dbo.Items.group_name, dbo.Items.sname, dbo.Items.TinPrice, dbo.ManufacturingProductAdd.qu,   dbo.ManufacturingProductAdd.totalprice, dbo.ManufacturingProductAdd.Stores, dbo.ManufacturingProductAdd.itm_Unity, dbo.ManufacturingProductAdd.qu_unity, dbo.ManufacturingProductAdd.AutoNumber HAVING(dbo.ManufacturingProductAdd.Manufacturing_ID = N'" & txtManufacturing_ID.Text & "')  ORDER BY dbo.ManufacturingProductAdd.AutoNumber"
        dr = cmd.ExecuteReader
        Do While dr.Read
            Dgv_Add.DataSource = Fn_AddBill(dr("itm_id"), dr("group_name"), dr("sname"), Val(dr("TinPrice")), Val(dr("qu")), dr("qu_unity").ToString, dr("itm_Unity"), Val(dr("totalprice").ToString), dr("Stores"), dr("AutoNumber"))
        Loop
        'Dgv_Add.Columns(9).Visible = False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select dbo.ManufacturingProduct.Manufacturing_ID, dbo.ManufacturingProduct.itm_id_Manufacturing, dbo.ManufacturingProduct.Stores_Manufacturing, dbo.Items.group_name, dbo.Items.sname,   dbo.ManufacturingProduct.Manufacturing_Allowance, dbo.ManufacturingProduct.Filling_Allowance, dbo.ManufacturingProduct.CostPrice, dbo.ManufacturingProduct.Weightqunt, dbo.ManufacturingProduct.NumberMaterial From dbo.Items INNER Join dbo.ManufacturingProduct On dbo.Items.itm_id = dbo.ManufacturingProduct.itm_id_Manufacturing Where (dbo.ManufacturingProduct.Manufacturing_ID =N'" & txtManufacturing_ID.Text & "')"
        dr = cmd.ExecuteReader
        If dr.Read Then
            txtItm_idManufacturing.Text = dr("itm_id_Manufacturing")
            cmbStoreManufacturingEdit.Text = dr("Stores_Manufacturing")
            cmbCatsManufacturingEdit.Text = dr("group_name")
            cmbItemsManufacturingEdit.Text = dr("sname")
            txtManufacturing_Allowance.Text = dr("Manufacturing_Allowance").ToString
            txtFilling_Allowance.Text = dr("Filling_Allowance").ToString
            txtCostPrice.Text = dr("CostPrice").ToString
            txtWeight.Text = dr("Weightqunt").ToString
            txtNumberMaterial.Text = dr("NumberMaterial").ToString
        End If
        Dgv_Add.Columns(4).Visible = False

        SumTotal()
        ActionRead = False
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        PanelEdit.Dock = DockStyle.None
        PanelEdit.Top = 1000
    End Sub

    Private Sub BtnClear_Click(sender As Object, e As EventArgs) Handles BtnClear.Click
        ClearSave()
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click

        Dim x As String = MsgBox("هل تريد بالفعل حذف البيان المحدد", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        For i As Integer = 0 To Dgv_Add.SelectedRows.Count - 1
            RNXD = Dgv_Add.CurrentRow.Index
            Dgv_Add.Rows.RemoveAt(RNXD)
        Next
        SumTotal()

        txtAutoNumber.Text = 1
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            txtAutoNumber.Text += 1
        Next
    End Sub

    Private Sub Dgv_Add_CellValueChanged(sender As Object, e As DataGridViewCellEventArgs) Handles Dgv_Add.CellValueChanged
        If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        Dgv_Add.SelectedRows(0).Cells(4).Value = Dgv_Add.SelectedRows(0).Cells(5).Value

        Dim Total As Double
        Dim Price As Double = Dgv_Add.SelectedRows(0).Cells(3).Value
        Dim Qunt As Double = Dgv_Add.SelectedRows(0).Cells(5).Value
        Total = Val(Price) * Val(Qunt)

        Dgv_Add.SelectedRows(0).Cells(7).Value = Total


        Dim SM As Double
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            SM = SM + Dgv_Add.Rows(i).Cells(7).Value
        Next
        txtCostPrice.Text = SM + Val(txtFilling_Allowance.Text) + Val(txtManufacturing_Allowance.Text)

        Dim SM1 As Double
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            SM1 = SM1 + Dgv_Add.Rows(i).Cells(5).Value
        Next
        txtWeight.Text = SM1

        txtNumberMaterial.Text = Dgv_Add.RowCount
    End Sub

    Private Sub cmbUnity_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbUnity.SelectedIndexChanged
        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & txtItm_idMaterial.Text & "' and Unity_Name=N'" & cmbUnity.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 0 : End If
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(txtquntUnity.Text)
        End If
    End Sub

    Private Sub txtquntUnity_TextChanged(sender As Object, e As EventArgs) Handles txtquntUnity.TextChanged
        MyVars.CheckNumber(txtquntUnity)
        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If
    End Sub

    Private Sub btnViewquntManufacturing_Click(sender As Object, e As EventArgs) Handles btnViewquntManufacturing.Click
        txtquntManufacturing.Focus()
        txtquntManufacturing.SelectAll()
        PanelquntManufacturing.Top = 300
    End Sub

    Private Sub btnClosequntManufacturing_Click(sender As Object, e As EventArgs) Handles btnClosequntManufacturing.Click
        PanelquntManufacturing.Top = 5000
    End Sub

    Private Sub btnPrintquntManufacturing_Click(sender As Object, e As EventArgs) Handles btnPrintquntManufacturing.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If dgv_Manufacturing.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If dgv_Manufacturing.RowCount = 0 Then Beep() : Exit Sub
        If (dgv_Manufacturing.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim SManufacturingID As String = dgv_Manufacturing.SelectedRows(0).Cells(0).Value
        Dim itmidManufacturing As String = dgv_Manufacturing.SelectedRows(0).Cells(1).Value
        Dim sname As String = dgv_Manufacturing.SelectedRows(0).Cells(2).Value
        Dim CostPrice As String = dgv_Manufacturing.SelectedRows(0).Cells(3).Value
        Dim Weightqunt As String = dgv_Manufacturing.SelectedRows(0).Cells(4).Value
        Dim TotalCostPrice As String = Val(CostPrice) * Val(Weightqunt)
        Dim Manufacturing_Allowance As String = dgv_Manufacturing.SelectedRows(0).Cells(5).Value
        Dim Filling_Allowance As String = dgv_Manufacturing.SelectedRows(0).Cells(6).Value
        Dim StoresManufacturing As String = dgv_Manufacturing.SelectedRows(0).Cells(7).Value

        Dim qunt_total_total As Double = 0
        Dim qunt_total As Double = 0
        Dim total_total As Double = 0
        Dim total_total_All As Double = 0

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        For i As Integer = 0 To dgv_Material.Rows.Count - 1
            qunt_total = Val(dgv_Material.Rows(i).Cells(3).Value) * Val(txtquntManufacturing.Text)
            total_total = Val(dgv_Material.Rows(i).Cells(2).Value) * Val(qunt_total)
            qunt_total_total += qunt_total
            total_total_All += total_total

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases(Company_Branch_ID,itm_id,itm_name,price,qu,totalprice,store)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & dgv_Material.Rows(i).Cells(0).Value & "',N'" & dgv_Material.Rows(i).Cells(1).Value & "',N'" & dgv_Material.Rows(i).Cells(2).Value & "',N'" & qunt_total & "',N'" & total_total & "',N'" & dgv_Material.Rows(i).Cells(5).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        AddReportView()
        Dim rpt As New Rpt_Product_Manufacturing

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        AddReportView()
        Dim txtname, txtNameAr, txtNameEn, sname_Object, CostPrice_Object, Weightqunt_Object, Stores_Manufacturing_Object, TotalCostPrice_Object, Manufacturing_Allowance_Object, Filling_Allowance_Object As TextObject

        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير بالمنتج المصنع"
        txtNameAr = rpt.Section1.ReportObjects("txtTitelNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelNameEn")
        txtNameEn.Text = NameEnCompany

        sname_Object = rpt.Section1.ReportObjects("txtsname")
        sname_Object.Text = sname
        CostPrice_Object = rpt.Section1.ReportObjects("txtCostPrice")
        CostPrice_Object.Text = CostPrice
        Weightqunt_Object = rpt.Section1.ReportObjects("txtWeightqunt")
        Weightqunt_Object.Text = qunt_total_total
        Stores_Manufacturing_Object = rpt.Section1.ReportObjects("txtStores")
        Stores_Manufacturing_Object.Text = StoresManufacturing
        TotalCostPrice_Object = rpt.Section1.ReportObjects("txtTotalCostPrice")
        TotalCostPrice_Object.Text = total_total_All
        Manufacturing_Allowance_Object = rpt.Section1.ReportObjects("txtManufacturing_Allowance")
        Manufacturing_Allowance_Object.Text = Manufacturing_Allowance
        Filling_Allowance_Object = rpt.Section1.ReportObjects("txtFilling_Allowance")
        Filling_Allowance_Object.Text = Filling_Allowance
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "تقرير بالمنتج المصنع"
        Frm_PrintReports.Show()
        PanelquntManufacturing.Top = 5000

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub brnAddNew_Click(sender As Object, e As EventArgs) Handles brnAddNew.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If


        Try
            If ValidateTextSave() = False Then Exit Sub

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from ManufacturingProduct where itm_id_Manufacturing =N'" & txtItm_idManufacturing.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H <> 0 Then
                MsgBox("الصنف مسجل مسبقا فى المنتجات المصنعة", MsgBoxStyle.Exclamation) : cmbItemsManufacturing.Focus() : Exit Sub
            End If

            MAXRECORD()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into ManufacturingProduct(Manufacturing_ID,itm_id_Manufacturing,Stores_Manufacturing,Manufacturing_Allowance,Filling_Allowance,CostPrice,Weightqunt,NumberMaterial,UserName) values ("
            S = S & "N'" & Manufacturing_ID & "' ,N'" & txtItm_idManufacturing.Text.Trim & "' ,N'" & cmbStoreManufacturingEdit.Text & "',N'" & txtManufacturing_Allowance.Text & "',N'" & txtFilling_Allowance.Text & "',N'" & txtCostPrice.Text & "',N'" & txtWeight.Text & "',N'" & txtNumberMaterial.Text & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1

                S = "insert into ManufacturingProductAdd(Manufacturing_ID,itm_id,qu,qu_unity,itm_Unity,totalprice,Stores,UserName,AutoNumber)  values("
                S = S & "N'" & Manufacturing_ID & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "',N'" & UserName & "',N'" & Dgv_Add.Rows(i).Cells(9).Value & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                IM.Store(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value)

                If ConnectOnlineStore = "YES" Then
                    EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
                    StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
                    Cos.UpdateProductStock(StockOnline, Dgv_Add.Rows(i).Cells(0).Value, EditItmId)
                End If
            Next

            ClearSave()
            MsgBox("تم حفظ بيانات المنتج بنجاح", MsgBoxStyle.Information)
            PanelEdit.Top = 5000
        Catch ex As Exception
            MsgBox(ex.ToString)
        End Try

    End Sub

    Private Sub MAXRECORD()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from ManufacturingProduct"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Manufacturing_ID = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(Manufacturing_ID As float)) as mb FROM ManufacturingProduct where Manufacturing_ID <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            Manufacturing_ID = sh + 1
        End If

    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub



#End Region



End Class
