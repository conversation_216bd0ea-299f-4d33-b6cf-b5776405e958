﻿Imports CrystalDecisions.CrystalReports.Engine
Imports System.Windows.Forms
Imports System.Data
Imports System.Data.SqlClient
Imports vb = Microsoft.VisualBasic
Imports System.IO
Public Class Frm_IM_BSal
    Dim WithEvents BS As New BindingSource
    Dim AmntcreditAfter, AmntdebitAfter, AmntcreditPrevious, AmntdebitPrevious As Double
    Dim R() As String
    Dim Mnm As String
    Dim _totalItem As Double
    Dim StateRateTax As Boolean = False
    Dim pay As String = "0"
    Dim AddBillAuto As String = mykey.GetValue("AddBillAuto", "NO")
    Dim SalesBillNotDiscount As String = mykey.GetValue("SalesBillNotDiscount", "NO")
    Dim SalesBillNotDiscountBill As String = mykey.GetValue("SalesBillNotDiscountBill", "NO")
    Dim ShowCustomerBalanceSalesScreen As String = mykey.GetValue("ShowCustomerBalanceSalesScreen", "NO")
    Dim SalesPricePublic As String = mykey.GetValue("SalesPricePublic", "NO")
    Dim ValueDiscountTax As String = mykey.GetValue("ValueDiscountTax", "0")
    Dim Cls_Altfiqith As New Class_Altfiqith
    Dim CustomerAddress As String = ""
    Dim CustomerTel As String = ""
    Dim DiscountsTin As Double = 0
    Dim DiscountsValue As Double = 0
    Dim TotalDiscountsValue As Double = 0
    Dim StateDisc As String = ""
    Dim DiscTotal As Double
    Dim AmountDebitCreditPrevious, AmountDebitCreditAfter As String


    Private Sub sales_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", cmbvendores)
        End If
        Cls.FillComboDataAdapterQuickSearch("Items", "sname", cmbname)
        ClearSave()

        Bra.Fil("groups", "g_name", cmbcats)
        Cls.fill_combo_Branch("stores", "store", cmbStores)
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployees)
        cmbvendores.Items.Add("نقداً")
        cmbvendores.Text = "نقداً"
        txtSalestax.Text = mykey.GetValue("SalesTax", "0")
        MAXRECORD()
        cmbStores.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        PanelEmployees.Top = 10000
        Panel_Prices.Top = 10000
        PaneldiscBill.Top = 10000
        PanelSearch.Top = 5000
        SearchForProduct()
        ShowSalesTax()
        GetShowDiscountRateItemSales()
        GetDateNotBeenActivatedPrograms(DTPDate)
        GetDateNotBeenActivatedOutcome()
    End Sub

    Private Sub GetDateNotBeenActivatedOutcome()
        If PermtionName <> "مدير" Then
            If DateNotBeenActivatedOutcome = "NO" Then
                DTPDate.Enabled = True
            Else
                DTPDate.Enabled = False
            End If
        End If
    End Sub

    Private Sub txtbillno_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            DTPDate.Focus()
        End If
    End Sub

    Private Sub cmbcats_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            If cmbcats.Text.Trim = "" Then
                txtdisc.Focus()
            Else
                cmbname.Focus()
            End If
        End If
    End Sub
    Private Sub txtprice_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtprice.KeyUp
        If e.KeyCode = 13 Then
            txtquntUnity.Focus()
            txtquntUnity.SelectAll()
        End If
    End Sub

    Private Sub txtprice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtprice.TextChanged
        MyVars.CheckNumber(txtprice)

        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If
        TotalBeforeVAT = Val(txtprice.Text) * Val(txtquntUnity.Text)
        If ShowValueVAT = "YES" Then
            TotalValueVAT = Format(Val(TotalBeforeVAT) * Val(ItemsRateVAT) / 100, "Fixed")
        End If
        txtTotalTotal.Text = Math.Round(TotalValueVAT, 2) + Math.Round(TotalBeforeVAT, 2)
        If ShowTax = "0" Then
            If StateRateTax = False Then
                txtSalestax.Text = ItemsRateVAT
            End If
            StateRateTax = True
        End If
        Try
            txtTotalTotal.Text = Math.Round(Convert.ToDouble(txtTotalTotal.Text), 2)
        Catch ex As Exception
        End Try
        sumdisc1()
    End Sub
    Private Sub txtqunt_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtqunt.KeyUp
        If e.KeyCode = 13 Then
            txtprice.Focus()
        End If
    End Sub

    Private Sub txtqunt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtqunt.TextChanged
        MyVars.CheckNumber(txtqunt)
        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If
        TotalBeforeVAT = Val(txtprice.Text) * Val(txtquntUnity.Text)
        If ShowValueVAT = "YES" Then
            TotalValueVAT = Format(Val(TotalBeforeVAT) * Val(ItemsRateVAT) / 100, "Fixed")
        End If
        txtTotalTotal.Text = Math.Round(TotalValueVAT, 2) + Math.Round(TotalBeforeVAT, 2)
        If ShowTax = "0" Then
            If StateRateTax = False Then
                txtSalestax.Text = ItemsRateVAT
            End If
            StateRateTax = True
        End If
        Try
            txtTotalTotal.Text = Math.Round(Convert.ToDouble(txtTotalTotal.Text), 2)
        Catch ex As Exception
        End Try
    End Sub
    Function ValidateTextAdd() As Boolean
        If cmbvendores.Text = "" Then MsgBox("فضلا أختر العميل", MsgBoxStyle.Exclamation) : cmbvendores.Focus() : Return False
        If txtprc.Text = "" Then MsgBox("فضلا ادخل الباركود", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
        If txtbillno.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الفاتورة", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        If cmbcats.Text = "" Then MsgBox("فضلا حدد المجموعة", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbname.Text = "" Then MsgBox("فضلا أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbname.Focus() : Return False
        If cmbUnityItems.Text = "" Then MsgBox("فضلا أدخل وحدة القياس", MsgBoxStyle.Exclamation) : cmbUnityItems.Focus() : Return False
        If cmbStores.Text = "" Then MsgBox("من فضلك اختر المخزن المرتجع اليه", MsgBoxStyle.Exclamation) : cmbStores.Focus() : Return False
        If Val(txtprice.Text.Trim) = 0 Then MsgBox("فضلا أدخل السعر", MsgBoxStyle.Exclamation) : txtprice.Focus() : Return False
        If Val(txtquntUnity.Text.Trim) = 0 Then MsgBox("فضلا أدخل الكمية ", MsgBoxStyle.Exclamation) : txtquntUnity.Focus() : Return False

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where itm_id =N'" & txtprc.Text.Trim & "'and group_name =N'" & cmbcats.Text.Trim & "'and sname =N'" & cmbname.Text.Trim & "'and Stores =N'" & cmbStores.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H = 0 Then
                MsgBox("الصنف غير مطابق من فضلك راجع الصنف ", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
            End If


            Dim ReturnInvoice As String = mykey.GetValue("ReturnInvoice", "NO")
            If ReturnInvoice = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select count(*) from Sales_Bill where bill_no =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
                If H = 0 Then
                    MsgBox("رقم الفاتورة غير مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
                ElseIf H > 0 Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select count(*) from Sales_Bill where bill_no =N'" & txtbillno.Text.Trim & "' and Vendorname =N'" & cmbvendores.Text.Trim & "'" : H = cmd.ExecuteScalar
                    If H = 0 Then
                        MsgBox("رقم الفاتورة غير مسجل باسم هذا العميل", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
                    End If
                End If
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from IM_Bsal where bill_no =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
            End If

            Dim MoreItemsInvoice As String = mykey.GetValue("MoreItemsInvoice", "NO")
            If MoreItemsInvoice = "NO" Then
                Dim AddMoreInvoiceSeparately As String = mykey.GetValue("AddMoreInvoiceSeparately", "YES")
                If AddMoreInvoiceSeparately = "NO" Then
                    For i As Integer = 0 To Dt_AddBill.Rows.Count - 1
                        If Dgv_Add.Rows(i).Cells(0).Value = txtprc.Text.Trim Then MsgBox("صنف مكرر بنفس الفاتورة", MsgBoxStyle.Exclamation) : txtprc.Focus() : txtprc.SelectAll() : Return False
                    Next
                End If
            End If

            Dim MinimumSalPrice As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select MinimumSalPrice from items where sname=N'" & cmbname.Text & "' and Stores=N'" & cmbStores.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
                dr.Close() : Return False
            Else
                If dr(0) Is DBNull.Value Then
                Else
                    MinimumSalPrice = dr(0)
                End If

                If txtprice.Text < MinimumSalPrice Then
                    MsgBox("سعر البيع وصل للحد الادنى", MsgBoxStyle.Information)
                    Return False
                End If
            End If

            If cmbvendores.Text <> "نقداً" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select Vendorname from Customers where Vendorname=N'" & cmbvendores.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = False Then
                    MsgBox("هذا العميل غير مسجل مسبقا", MsgBoxStyle.Exclamation)
                    cmbvendores.Focus() : Return False
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        If AllowCustOrSupplierReturnItemNotMovement = "NO" Then
            If cmbvendores.Text <> "نقداً" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select itm_id from View_BillsalData where Vendorname=N'" & cmbvendores.Text & "' and itm_id=N'" & txtprc.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = False Then
                    MsgBox("هذا العميل لم يتم بيع له هذا الصنف الذى تم اختيارة", MsgBoxStyle.Exclamation)
                    Return False
                End If
            End If
        End If

        Return True
    End Function
    Function ValidateTextSave() As Boolean
        If Dgv_Add.Rows.Count < 1 Then MsgBox("فضلا أكمل بيانات الفاتورة", MsgBoxStyle.Exclamation) : cmbvendores.Focus() : Return False
        If cmbvendores.Text = "" Then MsgBox("فضلا أدخل أسم العميل", MsgBoxStyle.Exclamation) : cmbvendores.Focus() : Return False
        If txtdisc.Text = "" Then
            txtdisc.Text = "0"
        End If
        If txtpaying.Text = "" Then
            txtpaying.Text = "0"
        End If

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from IM_Bsal where bill_no =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
            End If
            For i As Integer = 0 To Dt_AddBill.Rows.Count - 1

                If Dgv_Add.Rows(i).Cells(0).Value = txtprc.Text.Trim Then MsgBox("صنف مكرر بنفس الفاتورة", MsgBoxStyle.Exclamation) : txtprc.Focus() : txtprc.SelectAll() : Return False
            Next

            If cmbvendores.Text <> "نقداً" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select Vendorname from Customers where Vendorname=N'" & cmbvendores.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = False Then
                    MsgBox("هذا العميل غير مسجل مسبقا", MsgBoxStyle.Exclamation)
                    cmbvendores.Focus() : Return False
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        Return True
    End Function
    Friend Sub DTV_Width()
        Try
            If Dgv_Add.Rows.Count > 0 Then
                Dgv_Add.Columns(0).Width = 70
                Dgv_Add.Columns(1).Width = 90
                Dgv_Add.Columns(2).Width = 130
                Dgv_Add.Columns(3).Width = 60
                Dgv_Add.Columns(4).Width = 60
                Dgv_Add.Columns(5).Width = 75
                Dgv_Add.Columns(6).Width = 70
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
    Private Sub GetDataByPrc()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select group_name , sname ,SalPrice from items where itm_id=N'" & txtprc.Text & "'  and Stores=N'" & cmbStores.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then Exit Sub
            If dr(0) Is DBNull.Value Or dr(1) Is DBNull.Value Then
            Else
                Dim xcmbcats, xcmbname As String
                xcmbcats = dr(0)
                xcmbname = dr(1)
                If dr(2) Is DBNull.Value Then
                    txtprice.Text = 0
                    cmbcats.Text = xcmbcats
                    cmbname.Text = xcmbname
                    GoTo 1
                End If
                txtprice.Text = dr(2)
                cmbcats.Text = xcmbcats
                cmbname.Text = xcmbname
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
1:
        txtprice.SelectAll()
        txtqunt.Text = 1 : txtqunt.SelectAll() : txtprice.SelectAll() : txtprice.Focus()
    End Sub
    Private Sub BtnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAdd.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        'txtbillno.Text = Clss.GenerateItmId_Or_Parcode()
        If ValidateTextAdd() = False Then Exit Sub
        'IM.Store(txtprc.Text.Trim, cmbStores.Text)

        If ConnectOnlineStore = "YES" Then
            EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", txtprc.Text)
            StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", txtprc.Text)
            Cos.UpdateProductStock(StockOnline, txtprc.Text, EditItmId)
        End If

        Try
            Cls.Get_Value_Count_More("items", "itm_id =N'" & txtprc.Text.Trim & "' and group_name =N'" & cmbcats.Text.Trim & "' and sname =N'" & cmbname.Text.Trim & "'")
            If H = 0 Then
                GetDataByPrc()
            End If

            If cmbcats.Text = "" Or cmbname.Text = "" Or txtprice.Text = "" Or txtquntUnity.Text = "" Then
                MsgBox("اكمل البيانات")
                Exit Sub
            End If

            Dim SalPrice As Double = 0
            If ShowDiscountRateItemSales = "NO" Then
                SalPrice = txtprice.Text
            Else
                SalPrice = txtRateDiscPriceAfter.Text
            End If

            Dim MoreItemsInvoice As String = mykey.GetValue("MoreItemsInvoice", "YES")
            If MoreItemsInvoice = "YES" Then
                Dim ItmID, Indexqunt, IndexquntUnity As String
                For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                    If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
                    ItmID = Dgv_Add.Rows(i).Cells(0).Value
                    If txtprc.Text = ItmID Then
                        Indexqunt = Dgv_Add.Rows(i).Cells(4).Value
                        IndexquntUnity = Dgv_Add.Rows(i).Cells(5).Value
                        RNXD = Dgv_Add.Rows(i).Cells(0).RowIndex
                        Dgv_Add.Rows.RemoveAt(RNXD)

                        Dim XTotalqu As String = Val(txtqunt.Text) + Val(Indexqunt)
                        Dim XXTotalquUnity As String = Val(txtquntUnity.Text) + Val(IndexquntUnity)
                        Dim TotalTotal As String = Val(XTotalqu) * Val(SalPrice)

                        Dgv_Add.DataSource = Fn_AddBill(txtprc.Text, cmbcats.Text, cmbname.Text, Val(SalPrice), Val(XTotalqu), Val(XXTotalquUnity), cmbUnityItems.Text, Val(TotalTotal), cmbStores.Text, TotalValueVAT, TotalBeforeVAT, ItemsRateVAT, Val(txtdiscBill.Text), DiscountsValue, lblDiscount_Price_After.Text)

                        GoTo 2
                    End If
                Next
            End If

            Dgv_Add.DataSource = Fn_AddBill(txtprc.Text, cmbcats.Text, cmbname.Text, Val(txtprice.Text), Val(txtqunt.Text), Val(txtquntUnity.Text), cmbUnityItems.Text, Val(txtTotalTotal.Text), cmbStores.Text, TotalValueVAT, TotalBeforeVAT, ItemsRateVAT, Val(txtdiscBill.Text), DiscountsValue, lblDiscount_Price_After.Text)

2:
            ClearAdd() : SumAllPrice() : FocusText()
            txtCountItems.Text = Dgv_Add.Rows.Count
            Dgv_Add.Columns(0).ReadOnly = True
            Dgv_Add.Columns(1).ReadOnly = True
            Dgv_Add.Columns(2).ReadOnly = True
            Dgv_Add.Columns(4).ReadOnly = True
            Dgv_Add.Columns(6).ReadOnly = True
            Dgv_Add.Columns(7).ReadOnly = True
            Dgv_Add.Columns(8).ReadOnly = True
            Dgv_Add.Columns(9).ReadOnly = True
            Dgv_Add.Columns(10).ReadOnly = True
            Dgv_Add.Columns(11).ReadOnly = True
            Dgv_Add.Columns(13).ReadOnly = True
            Dgv_Add.Columns(14).ReadOnly = True


            Dgv_Add.Columns(1).Visible = False
            Dgv_Add.Columns(4).Visible = False
            Dgv_Add.Columns(9).Visible = False
            Dgv_Add.Columns(10).Visible = False
            Dgv_Add.Columns(11).Visible = False
            'Dgv_Add.Columns(13).Visible = False
            Dgv_Add.Columns(14).Visible = False

            If ShowDiscountRateItemSales = "NO" Then
                Dgv_Add.Columns(3).Visible = True
                Dgv_Add.Columns(14).Visible = False
            Else
                Dgv_Add.Columns(3).Visible = False
                Dgv_Add.Columns(14).Visible = True
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        ChkCash_CheckedChanged(sender, e)
    End Sub
    Private Sub FocusText()
        Dim FocusText As String = mykey.GetValue("FocusText", "NO")
        If FocusText = "YES" Then
            txtprc.Focus()
        Else
            cmbcats.Focus()
        End If
    End Sub
    Private Sub ClearAdd()
        cmbUnityItems.Text = ""
        'cmbcats.Text = ""
        cmbname.Text = ""
        txtqunt.Text = ""
        txtprice.Text = ""
        txtprc.Text = ""
        txtTotalTotal.Text = ""
        txtStoreTotal.Text = ""
        txtquntUnity.Text = ""
        txtRateDiscPriceAfter.Text = ""
    End Sub
    Private Sub chkAll()
        If ChkState.Checked = True Or chkVisa.Checked = True Then
            txtpaying.Text = txttotalafterdisc.Text : txtpaying.Enabled = False
        Else
            txtpaying.Enabled = True
        End If
    End Sub
    Dim Dt_AddBill As New DataTable
    Friend Function Fn_AddBill(ByVal Col_Prc As String, ByVal Col_Cats As String, ByVal Col_Name As String _
, ByVal Col_Price As Double, ByVal Col_Quant As Double, ByVal Col_qu_unity As Double, ByVal Col_itm_Unity As String, ByVal Col_Total As Double, ByVal Col_Stores As String, ByVal Col_ValueVAT As Double, ByVal Col_TotalBeforeVAT As Double, ByVal Col_ItemsRateVAT As Double, ByVal Col_Dsic As Double, ByVal Col_DiscountsValue As Double, ByVal Col_UnitPriceAfterDisc As Double) As DataTable
        Try
            If Dt_AddBill.Columns.Count = 0 Then
                Dt_AddBill.Columns.Add("الباركود", GetType(String))
                Dt_AddBill.Columns.Add("أسم المجموعة", GetType(String))
                Dt_AddBill.Columns.Add("أسم الصنف", GetType(String))
                Dt_AddBill.Columns.Add("السعر", GetType(Double))
                Dt_AddBill.Columns.Add("الكمية1", GetType(Double))
                Dt_AddBill.Columns.Add("الكمية", GetType(Double))
                Dt_AddBill.Columns.Add("الوحدة", GetType(String))
                Dt_AddBill.Columns.Add("إجمالي", GetType(Double))
                Dt_AddBill.Columns.Add("المخزن", GetType(String))
                Dt_AddBill.Columns.Add("ValueVAT", GetType(Double))
                Dt_AddBill.Columns.Add("TotalBeforeVAT", GetType(Double))
                Dt_AddBill.Columns.Add("ItemsRateVAT", GetType(Double))
                Dt_AddBill.Columns.Add("الخصم", GetType(Double))
                Dt_AddBill.Columns.Add("قيمة الخصم", GetType(Double))
                Dt_AddBill.Columns.Add("سعر البيع", GetType(Double))
            End If

            DTV_Width()
            Dt_AddBill.Rows.Add(Col_Prc, Col_Cats, Col_Name, Col_Price, Col_Quant, Col_qu_unity, Col_itm_Unity, Col_Total, Col_Stores, Col_ValueVAT, Col_TotalBeforeVAT, Col_ItemsRateVAT, Col_Dsic, Col_DiscountsValue, Col_UnitPriceAfterDisc)

            Return Dt_AddBill
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Function

    Private Sub Dgv_Add_CellContentClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles Dgv_Add.CellContentClick
        On Error Resume Next
        If e.ColumnIndex = 0 Then
            txtprc.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Prc").Value.ToString
            cmbcats.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Cats").Value.ToString
            cmbname.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Name").Value.ToString
            txtprice.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Price").Value
            txtqunt.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Quant").Value
            Dgv_Add.Rows.RemoveAt(e.RowIndex)
        End If
        SumAllPrice()
    End Sub

    Private Sub BtnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnSave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        SumAllPrice()

        If ValidateTextSave() = False Then Exit Sub

        Dim x As String = MsgBox("هل تريد بالفعل إتمام عملية البيع", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        Dim STAT As String
        If ChkCash.Checked = True Then
            STAT = "نقداً"
        End If
        If ChkState.Checked = True Then
            STAT = "آجل"
        End If
        If chkVisa.Checked = True Then
            STAT = "فيزا"
        End If


        If ChkCent.Checked = True Then
            DiscTotal = Val(txttotalpeforedisc.Text) - Val(txttotalafterdisc.Text)
            DiscTotal = Math.Round(DiscTotal, 1)
        Else
            DiscTotal = txtdisc.Text
        End If

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into IM_Bsal(Company_Branch_ID,bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,SalesTax,Notes,UserName,EmpName,ValueVAT,Treasury_Code,DiscountsValue) values ("
            S = S & "N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "' ,N'" & cmbvendores.Text.Trim & "' ,N'" & Cls.C_date(DTPDate.Text) & "' ,N'" & Cls.get_time(True) & "' ," & Val(txttotalpeforedisc.Text.Trim) & "," & Val(DiscTotal) & " ," & Val(txttotalafterdisc.Text.Trim) & " ,N'" & STAT & "',N'" & txtpaying.Text.Trim & "',N'" & txtstaying.Text.Trim & "',N'" & txtSalestax.Text & "',N'" & txtNotes.Text & "',N'" & UserName & "',N'" & cmbEmployees.Text & "',N'" & txtTotalValueVAT.Text & "',N'" & Treasury_Code & "',N'" & TotalDiscountsValue & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            Dim bill_EndDate As String = "" : Dim bill_no_Expired As String = ""
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1

                DiscountsTin = Cls.Get_Code_Value_More_ALL_Select("Select Top(100) PERCENT Discounts From dbo.BilltINData Where (itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "') Order By id DESC")

                AverageTinPrice(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value, Dgv_Add.Rows(i).Cells(3).Value, Dgv_Add.Rows(i).Cells(5).Value, Dgv_Add.Rows(i).Cells(6).Value, Dgv_Add.Rows(i).Cells(12).Value, Dgv_Add.Rows(i).Cells(14).Value)

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select Top(100) PERCENT MIN(bill_no_Expired) As Exbill_no_Expired, bill_EndDate, qu_expired, itm_id, Stores From dbo.BilltINData Group By itm_id, Stores, bill_EndDate, qu_expired HAVING(itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "') AND (Stores = N'" & Dgv_Add.Rows(i).Cells(8).Value & "') AND (qu_expired <> 0)  ORDER BY MIN(bill_no_Expired)" : dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    bill_no_Expired = dr(0).ToString
                    bill_EndDate = dr(1).ToString
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into IM_Bsal_Data (Company_Branch_ID,bill_no,itm_id,itm_cat,itm_name,price,qu,qu_unity,itm_Unity,totalprice,Stores,UserName,bill_date,TinPriceAverage,Profits,EmpName,ValueVAT,RateVAT,BeforeVAT,TinPrice,Treasury_Code,bill_EndDate,bill_no_Expired,Price_Unity,Discounts,DiscountsValue,Discount_Price_After)  values (N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Dgv_Add.Rows(i).Cells(3).Value & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "',N'" & UserName & "',N'" & Cls.C_date(DTPDate.Text) & "',N'" & TinPriceAverage & "',N'" & Profits & "',N'" & cmbEmployees.Text & "',N'" & Dgv_Add.Rows(i).Cells(9).Value & "',N'" & Dgv_Add.Rows(i).Cells(10).Value & "',N'" & Dgv_Add.Rows(i).Cells(11).Value & "',N'" & TinPrice & "',N'" & Treasury_Code & "',N'" & bill_EndDate & "',N'" & bill_no_Expired & "',N'" & Price_Unity & "',N'" & Dgv_Add.Rows(i).Cells(12).Value & "',N'" & Dgv_Add.Rows(i).Cells(13).Value & "',N'" & Dgv_Add.Rows(i).Cells(14).Value & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                IM.StoreExpired(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value, bill_EndDate.ToString(), bill_no_Expired)

            Next
            If Val(txtpaying.Text) > 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into IM_Vst (Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,billno,VND_no,UserName,Treasury_Code) values"
                S = S & " (N'" & Company_Branch_ID & "',N'" & cmbvendores.Text & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(DTPDate.Text) & "'," & txtpaying.Text.Trim & ",N'بفاتورة',N'بفاتورة',N'بفاتورة',N'" & txtbillno.Text & "',N'دفعة نقدية',N'" & UserName & "',N'" & Treasury_Code & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
            If Val(txtdisc.Text) > 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into IM_Vst_disc (Company_Branch_ID,Vendorname,amnt,pdate,det,TIN_NO,UserName,Treasury_Code) values (N'" & Company_Branch_ID & "',N'" & cmbvendores.Text.Trim & "'," & Val(DiscTotal) & ",N'" & Cls.C_date(DTPDate.Text) & "',N'خصم على فاتورة مباشرة',N'" & txtbillno.Text.Trim & "',N'" & UserName & "',N'" & Treasury_Code & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If

            ImageUpdateBill()
            '===============================================================================
            Dim UpdateSalPrice As String = mykey.GetValue("UpdateSalPrice", "YES")
            If UpdateSalPrice = "YES" Then
                UpdatePriceItems()
            End If
            '===============================================================================
            Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
            If UseAccounts = "YES" Then
                Daily_Restrictions()
            End If
            '===============================================================================

            GetDebtorlCreditorPrevious()

            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                IM.Store(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value)

                If ConnectOnlineStore = "YES" Then
                    EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
                    StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
                    Cos.UpdateProductStock(StockOnline, Dgv_Add.Rows(i).Cells(0).Value, EditItmId)
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update IM_Bsal_Data set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbillno.Text & "' and itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "' and Stores =N'" & Dgv_Add.Rows(i).Cells(8).Value & "'" : cmd.ExecuteNonQuery()
            Next
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        Get_Movement_In_Out_Money(DTPDate.Text, Treasury_Code)

        GetDebtorlCreditor()

        AmntcreditAfter = AmntcreditAfter - AmntdebitAfter
        AmntcreditPrevious = AmntcreditPrevious - AmntdebitPrevious

        Try
            Dim CurrentBalanceCustVnd As String = GetCurrentBalanceCustVnd(cmbvendores.Text)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal set CreditPrevious = " & Val(AmntcreditPrevious) & ",DebitPrevious = " & Val(AmntdebitPrevious) & ",CreditCurrent = " & Val(AmntcreditAfter) & ",DebitCurrent = " & Val(AmntdebitAfter) & ",CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where bill_No =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()

            If Val(txtpaying.Text) > 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update IM_Vst set CreditPrevious = " & Val(AmntcreditPrevious) & ",DebitPrevious = " & Val(AmntdebitPrevious) & ",CreditCurrent = " & Val(AmntcreditAfter) & ",DebitCurrent = " & Val(AmntdebitAfter) & ",CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where BillNo =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
            End If
            If Val(txtdisc.Text) > 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update IM_Vst_disc set CreditPrevious = " & Val(AmntcreditPrevious) & ",DebitPrevious = " & Val(AmntdebitPrevious) & ",CreditCurrent = " & Val(AmntcreditAfter) & ",DebitCurrent = " & Val(AmntdebitAfter) & ",CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where TIN_NO =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        MsgBox("تمت عملية مرتجع المبيعات بنجاح", MsgBoxStyle.Information) : txtprc.Focus()
        If chkprintX.Checked = True Then
            PrintReport()
        End If

        IM.CustomerAccountTotal(cmbvendores.Text)
        IM.EmployeesAccountTotal(cmbEmployees.Text)

        ClearSave()
        Dt_AddBill.Rows.Clear()
        defoult()
        BtnClear.PerformClick()
        MAXRECORD()
    End Sub
    Private Sub UpdatePriceItems()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                cmd.CommandText = "update items set SalPrice = " & Val(Dgv_Add.Rows(i).Cells(3).Value) & " where itm_id =N'" & Dgv_Add.Rows(i).Cells(0).Value & "'" : cmd.ExecuteNonQuery()
            Next
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub PrintReport()

        Try
            Dim txt, txtname, txtNameAr, txtNameEn, txtCmpAddress, txtCmpEmail, txtCmpTel, txtCmpMobile, txtCustomerAddressSales, txtCommercialRecord, txtTaxCard, txtSaleTax, txtDiscountTax, txtCMPNameDown, txtAltfiqith, txtCmpFax, txtEndorsement, txtDelegateName, txtCustomerTel, txtProgramNameBill, txtObjectUserName, txtObjectCommercialAndIndustrialProfitsTax As TextObject

            Cls.delete_Branch_All("PrintSalesPurchases")

            Dim XTotalBill As String
            If txtpaying.Text = 0 Then
                XTotalBill = txtstaying.Text
            Else
                XTotalBill = txttotalafterdisc.Text
            End If

            Dim RateDiscount As String
            If ChkCent2.Checked = True Then : RateDiscount = "%" : Else RateDiscount = "$" : End If

            Dim TotalAfterDisc As Double
            If TotalDiscountsValue <> 0 Then
                TotalAfterDisc = Val(txttotalpeforedisc.Text) + TotalDiscountsValue
            Else
                TotalAfterDisc = Val(txttotalpeforedisc.Text)
            End If

            If ShowCustomerAddressSales = "YES" Then
                GetCustomerAddress()
            End If

            If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

            Dim BillSerialNumber As Double = 0
            Dim SalPrice As String = ""

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                BillSerialNumber += 1

                If ShowDiscountRateItemSales = "NO" Then
                    SalPrice = Dgv_Add.Rows(i).Cells(3).Value.ToString
                Else
                    SalPrice = Dgv_Add.Rows(i).Cells(14).Value.ToString
                End If
                S = "insert into PrintSalesPurchases(Company_Branch_ID,itm_id,itm_cat,itm_name,price,qu,Unity,totalprice,store,BILL_NO,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,BEY,STAYING,vnamntcredit,vnamntdebit,TotalCreditor,TotalDebtor,UserName,Totalreturns,Delivery_Date,det,Supervisor_Reform,TotalDisc,Recipient,Received_Date,Name1,Name2,Name8,Name9,NumberInt1,TotalBeforeDisc,Name10)  values("
                S = S & "N'" & Company_Branch_ID & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & SalPrice & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "',N'" & txtbillno.Text & "',N'" & cmbvendores.Text & "',N'" & DateTimePicker1.Text & "',N'" & Cls.Get_Time_AM_PM(TxtHour.Text.ToString) & "',N'" & TotalAfterDisc & "',N'" & txtdisc.Text & "',N'" & txttotalafterdisc.Text & "',N'" & txtpaying.Text & "',N'" & txtstaying.Text & "',N'" & AmntcreditAfter & "',N'" & AmntdebitAfter & "',N'" & AmntcreditPrevious & "',N'" & AmntdebitPrevious & "',N'" & XTotalBill & "',N'" & Val(txtTotalValueVAT.Text) & "',N'" & DefaultCurrencyProgram & "',N'" & Dgv_Add.Rows(i).Cells(12).Value & "',N'" & TotalDiscountsValue & "',N'" & Dgv_Add.Rows.Count & "',N'" & UserName & "',N'" & RateDiscount & "',N'" & CustomerTel & "',N'" & CustomerAddress & "',N'" & AmntcreditPrevious & "',N'" & AmntcreditAfter & "',N'" & BillSerialNumber & "',N'" & BillSerialNumber & "',N'" & txtTotalCountItems.Text & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next

            AddReportView()
            Dim rpt

            Cls.GetDefaultPrinterA4()

            If PrintSmall = "YES" Then
                Cls.GetDefaultPrinterBill()
                If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                    rpt = New Rpt_SoldSmall
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                    rpt = New Rpt_SoldSmall_2
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                    rpt = New Rpt_SoldSmall_3
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                    rpt = New Rpt_SoldSmall_4
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                    rpt = New Rpt_SoldSmall_5
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                    rpt = New Rpt_SoldSmall_6
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                    rpt = New Rpt_SoldSmall_7
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                    rpt = New Rpt_SoldSmall_8
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                    rpt = New Rpt_SoldSmall_9
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                    rpt = New Rpt_SoldSmall_10
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                    rpt = New Rpt_SoldSmall_11
                    If ShowCustomerAddressSales = "YES" Then
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                        End If
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                    rpt = New Rpt_SoldSmall_12
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                    rpt = New Rpt_SoldSmall_13
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                    rpt = New Rpt_SoldSmall_14
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                    rpt = New Rpt_SoldSmall_15
                    txtObjectUserName = rpt.Section1.ReportObjects("UserName")
                    txtObjectUserName.Text = UserName
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                    rpt = New Rpt_SoldSmall_16
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                    rpt = New Rpt_SoldSmall_17
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                    rpt = New Rpt_SoldSmall_18
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                    rpt = New Rpt_SoldSmall_19
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                    rpt = New Rpt_SoldSmall_20
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                    rpt = New Rpt_SoldSmall_21
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                    rpt = New Rpt_SoldSmall_10
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                    rpt = New Rpt_SoldSmall_10
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                    rpt = New Rpt_SoldSmall_24
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                    rpt = New Rpt_SoldSmall_10
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                    rpt = New Rpt_SoldSmall_24
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                    rpt = New Rpt_SoldSmall_24
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                    rpt = New Rpt_SoldSmall_24
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                    rpt = New Rpt_SoldSmall_24
                End If
                If SalesInvoicePrintingLanguage = "English" Then
                    rpt = New Rpt_SoldSmall_EN
                End If
            End If

            If PrintSmall = "NO" Then

                If SalesBillNotDiscountBill = "YES" Then
                    If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                        rpt = New Rpt_SalesBill_NotDiscountBill
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                        rpt = New Rpt_SalesBill_2
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                        rpt = New Rpt_SalesBill_3
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                        rpt = New Rpt_SalesBill_4
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                        rpt = New Rpt_SalesBill_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                        rpt = New Rpt_SalesBill_6
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                        rpt = New Rpt_SalesBill_7
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                        rpt = New Rpt_SalesBill_8
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                        rpt = New Rpt_SalesBill_9
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                        rpt = New Rpt_SalesBill_10
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                        rpt = New Rpt_SalesBill_11
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                        rpt = New Rpt_SalesBill_Delegate
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                        rpt = New Rpt_SalesBill_Delegate_2
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                        rpt = New Rpt_SalesBill_14
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                        rpt = New Rpt_SalesBill_15
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                        rpt = New Rpt_SalesBill_16
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                        rpt = New Rpt_SalesBill_17
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                        rpt = New Rpt_SalesBill_18
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                            txtDelegateName.Text = cmbEmployees.Text
                        End If
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                        rpt = New Rpt_SalesBill_19
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                            txtDelegateName.Text = cmbEmployees.Text
                        End If
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                        rpt = New Rpt_SalesBill_20
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                            txtDelegateName.Text = cmbEmployees.Text
                        End If
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                        rpt = New Rpt_SoldSmall_21
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                        rpt = New Rpt_SoldSmall_10
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                        rpt = New Rpt_SoldSmall_10
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                        rpt = New Rpt_SoldSmall_24
                    End If
                    If SalesInvoicePrintingLanguage = "English" Then
                        rpt = New Rpt_SoldSmall_EN
                    End If
                    GoTo 10
                End If
                If SalesBillNotDiscount = "YES" Then
                    If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                        rpt = New Rpt_SalesBill_NotDiscount
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                        rpt = New Rpt_SalesBill_2
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                        rpt = New Rpt_SalesBill_3
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                        rpt = New Rpt_SalesBill_4
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                        rpt = New Rpt_SalesBill_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                        rpt = New Rpt_SalesBill_6
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                        rpt = New Rpt_SalesBill_7
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                        rpt = New Rpt_SalesBill_8
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                        rpt = New Rpt_SalesBill_9
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                        rpt = New Rpt_SalesBill_10
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                        rpt = New Rpt_SalesBill_11
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                        rpt = New Rpt_SalesBill_Delegate
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                        rpt = New Rpt_SalesBill_Delegate_2
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                        rpt = New Rpt_SalesBill_14
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                        rpt = New Rpt_SalesBill_15
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                        rpt = New Rpt_SalesBill_16
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                        rpt = New Rpt_SalesBill_17
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                        rpt = New Rpt_SalesBill_18
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                            txtDelegateName.Text = cmbEmployees.Text
                        End If
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                        rpt = New Rpt_SalesBill_19
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                            txtDelegateName.Text = cmbEmployees.Text
                        End If
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                        rpt = New Rpt_SalesBill_20
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                            txtDelegateName.Text = cmbEmployees.Text
                        End If
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                        rpt = New Rpt_SalesBill_21
                        If CommercialAndIndustrialProfitsTax = 0 Then
                            txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                            txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                        End If
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                        rpt = New Rpt_SalesBill_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                        rpt = New Rpt_SalesBill_23
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                        rpt = New Rpt_SalesBill_24
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                        rpt = New Rpt_SalesBill_25
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                Else
                    If ShowCustomerBalanceSalesScreen = "YES" Then
                        If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                            rpt = New Rpt_SalesBill
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                            rpt = New Rpt_SalesBill_2
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                            rpt = New Rpt_SalesBill_3
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                            rpt = New Rpt_SalesBill_4
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                            rpt = New Rpt_SalesBill_5
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                            rpt = New Rpt_SalesBill_6
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                            rpt = New Rpt_SalesBill_7
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                            rpt = New Rpt_SalesBill_8
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                            rpt = New Rpt_SalesBill_9
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                            rpt = New Rpt_SalesBill_10
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                            rpt = New Rpt_SalesBill_11
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                            rpt = New Rpt_SalesBill_Delegate
                            txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                            txtEndorsement.Text = CMPEndorsement
                            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                            txtDelegateName.Text = cmbEmployees.Text
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                            rpt = New Rpt_SalesBill_Delegate_2
                            txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                            txtEndorsement.Text = CMPEndorsement
                            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                            txtDelegateName.Text = cmbEmployees.Text
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                            rpt = New Rpt_SalesBill_14
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                            rpt = New Rpt_SalesBill_15
                            txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                            txtEndorsement.Text = CMPEndorsement
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                            rpt = New Rpt_SalesBill_16
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                            rpt = New Rpt_SalesBill_17
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                            rpt = New Rpt_SalesBill_18
                            If CustomerAddress <> "" Then
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = CustomerTel
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                            End If
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                            rpt = New Rpt_SalesBill_19
                            If CustomerAddress <> "" Then
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = CustomerTel
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                            End If
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                            rpt = New Rpt_SalesBill_20
                            If CustomerAddress <> "" Then
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = CustomerTel
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                            End If
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                            rpt = New Rpt_SalesBill_21
                            If CommercialAndIndustrialProfitsTax = 0 Then
                                txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                                txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                            End If
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                            rpt = New Rpt_SalesBill_22
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                            rpt = New Rpt_SalesBill_23
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                            rpt = New Rpt_SalesBill_24
                        End If
                    Else
                        If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                            rpt = New Rpt_SalesBill_Cash
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                            rpt = New Rpt_SalesBill_2
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                            rpt = New Rpt_SalesBill_3
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                            rpt = New Rpt_SalesBill_4
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                            rpt = New Rpt_SalesBill_5
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                            rpt = New Rpt_SalesBill_6
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                            rpt = New Rpt_SalesBill_7
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                            rpt = New Rpt_SalesBill_8
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                            rpt = New Rpt_SalesBill_9
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                            rpt = New Rpt_SalesBill_10
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                            rpt = New Rpt_SalesBill_11
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                            rpt = New Rpt_SalesBill_Delegate
                            txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                            txtEndorsement.Text = CMPEndorsement
                            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                            txtDelegateName.Text = cmbEmployees.Text
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                            rpt = New Rpt_SalesBill_Delegate_2
                            txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                            txtEndorsement.Text = CMPEndorsement
                            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                            txtDelegateName.Text = cmbEmployees.Text
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                            rpt = New Rpt_SalesBill_14
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                            rpt = New Rpt_SalesBill_15
                            txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                            txtEndorsement.Text = CMPEndorsement
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                            rpt = New Rpt_SalesBill_16
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                            rpt = New Rpt_SalesBill_17
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                            rpt = New Rpt_SalesBill_18
                            If CustomerAddress <> "" Then
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = CustomerTel
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                            End If
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                            rpt = New Rpt_SalesBill_19
                            If CustomerAddress <> "" Then
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = CustomerTel
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                            End If
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                            rpt = New Rpt_SalesBill_20
                            If CustomerAddress <> "" Then
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = CustomerTel
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                            End If
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                            rpt = New Rpt_SalesBill_21
                            If CommercialAndIndustrialProfitsTax = 0 Then
                                txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                                txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                            End If
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                            rpt = New Rpt_SalesBill_22
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                            rpt = New Rpt_SalesBill_23
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                            rpt = New Rpt_SalesBill_24
                        End If
                    End If

10:
                End If
                txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
                txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(txttotalafterdisc.Text)

                txtCMPNameDown = rpt.Section1.ReportObjects("txtCMPNameDown")
                txtCMPNameDown.Text = CMPNameDown
                txtCmpFax = rpt.Section1.ReportObjects("txtFax")
                txtCmpFax.Text = CmpFax
                'If ShowCustomerAddressSales = "YES" Then
                '    If CustomerAddress <> "" Then
                '        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                '        txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                '        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                '        txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                '    End If
                'End If
                'txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                'txtDelegateName.Text = cmbEmployees.Text
            End If

            If PrintSmall = "A5" Then
                If ShowCustomerBalanceSalesScreen = "YES" Then
                    If SalesPricePublic = "YES" Then
                        rpt = New Rpt_SalesBill_A5_PricePublic
                    Else
                        rpt = New Rpt_SalesBill_A5_1
                    End If
                Else
                    If SalesPricePublic = "YES" Then
                        rpt = New Rpt_SalesBill_BalanceCust_A5_PricePublic
                    Else
                        rpt = New Rpt_SalesBill_BalanceCust_A5
                    End If
                End If

                txtCMPNameDown = rpt.Section1.ReportObjects("txtCMPNameDown")
                txtCMPNameDown.Text = CMPNameDown
                txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
                txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(txttotalafterdisc.Text)
                If ShowTax = "YES" Then
                    txtSaleTax = rpt.Section1.ReportObjects("txtSaleTax")
                    txtSaleTax.Text = txtSalestax.Text & " : ضريبة المبيعات"
                    txtDiscountTax = rpt.Section1.ReportObjects("txtDiscountTax")
                    txtDiscountTax.Text = ValueDiscountTax & " : ضريبة الخصم"
                End If
            End If

            If ShowValueVAT = "YES" Then
                If PrintSmall = "YES" Then
                    rpt = New Rpt_SoldSmall_VAT
                End If
                If PrintSmall = "NO" Then
                    rpt = New Rpt_SalesBill_Cash_VAT
                End If
                If PrintSmall = "A5" Then
                    rpt = New Rpt_SalesBill_BalanceCust_A5_VAT
                End If
            End If


            Cls.Select_More_Data_Branch_Print_Orderby("PrintSalesPurchases", "*", "NumberInt1")
            Dim dt As New DataTable
            dt.Load(dr)
            rpt.SetDataSource(dt)
            txt = rpt.Section1.ReportObjects("txtTitelAddress")
            txt.Text = "فاتورة مرتجعات مبيعات"
            txtname = rpt.Section1.ReportObjects("txtName")
            txtname.Text = "أسم العميل"
            txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
            txtNameAr.Text = NameArCompay.ToString
            txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
            txtNameEn.Text = NameEnCompany.ToString

            txtCmpAddress = rpt.Section1.ReportObjects("txtAddress")
            txtCmpAddress.Text = CmpAddress
            txtCmpEmail = rpt.Section1.ReportObjects("txtEmail")
            txtCmpEmail.Text = CmpEmail.ToString
            txtCmpTel = rpt.Section1.ReportObjects("txtTel")
            txtCmpTel.Text = CmpTel.ToString
            txtCmpMobile = rpt.Section1.ReportObjects("txtMobile")
            txtCmpMobile.Text = CmpMobile.ToString
            txtCommercialRecord = rpt.Section1.ReportObjects("txtCommercialRecord")
            txtCommercialRecord.Text = CMPCommercialRecord.ToString
            txtTaxCard = rpt.Section1.ReportObjects("txtTaxCard")
            txtTaxCard.Text = CMPTaxCard.ToString
            If PrintSmall <> "YES" Then
                txtCmpFax = rpt.Section1.ReportObjects("txtFax")
                txtCmpFax.Text = CmpFax
            End If

            If HideProgramNameBill = "YES" Then
                txtProgramNameBill = rpt.Section1.ReportObjects("txtProgramNameBill")
                txtProgramNameBill.Text = ""
            Else
                txtProgramNameBill = rpt.Section1.ReportObjects("txtProgramNameBill")
                txtProgramNameBill.Text = ProgramNameBill
            End If

            If SelectLogoPathOther = "YES" Then
                rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
            Else
                rpt.SetParameterValue("ImageURL", CMPLogoPath)
            End If
            Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
            Frm_PrintReports.Text = "تقرير مرتجعات مبيعات"
            Frm_PrintReports.Show()

            If RunDatabaseInternet = "YES" Then : connect() : End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
    Sub defoult()
        cmbvendores.Enabled = True
        txtbillno.Enabled = True
        ChkCash.Checked = True
    End Sub
    Sub ForPrintAll(ByVal itm_id As String, ByVal itm_cat As String, ByVal itm_name As String, ByVal price As String, ByVal qu As String, ByVal totalprice As String, ByVal stors As String,
                    ByVal BILL_NO As String, ByVal Vendorname As String, ByVal bill_date As String, ByVal billtime As String,
                    ByVal totalpricebeforedisc As String, ByVal disc As String, ByVal totalpriceafterdisc As String, ByVal BEY As String, ByVal STAYING As String)

        Try
            cmd.CommandType = CommandType.StoredProcedure
            cmd.CommandText = "Sp_PrintSalesPurchases"
            cmd.Parameters.Clear()

            cmd.Parameters.AddWithValue("@itm_id", itm_id)
            cmd.Parameters.AddWithValue("@itm_cat", itm_cat)
            cmd.Parameters.AddWithValue("@itm_name", itm_name)
            cmd.Parameters.AddWithValue("@price", price)
            cmd.Parameters.AddWithValue("@qu", qu)
            cmd.Parameters.AddWithValue("@totalprice", totalprice)
            cmd.Parameters.AddWithValue("@store", stors)
            cmd.Parameters.AddWithValue("@BILL_NO", BILL_NO)
            cmd.Parameters.AddWithValue("@Vendorname", Vendorname)
            cmd.Parameters.AddWithValue("@bill_date", bill_date)
            cmd.Parameters.AddWithValue("@billtime", billtime)
            cmd.Parameters.AddWithValue("@totalpricebeforedisc", totalpricebeforedisc)
            cmd.Parameters.AddWithValue("@disc", disc)
            cmd.Parameters.AddWithValue("@totalpriceafterdisc", totalpriceafterdisc)
            cmd.Parameters.AddWithValue("@BEY", BEY)
            cmd.Parameters.AddWithValue("@STAYING", STAYING)

            cmd.ExecuteNonQuery()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
    Private Sub ClearSave()
        GrpMain.Enabled = True
        cmbvendores.SelectedIndex = -1
        cmbUnityItems.Text = ""
        cmbStores.Text = ""
        cmbname.Text = ""
        cmbcats.Text = ""
        txtprice.Text = ""
        txtqunt.Text = ""
        txtbillno.Text = ""
        txtprc.Text = ""
        txttotalafterdisc.Text = "0"
        txttotalpeforedisc.Text = "0"
        txtdisc.Text = "0"
        txtpaying.Text = "0"
        txtbillno.Text = ""
        txtprc.Text = ""
        cmbvendores.Text = "نقداً"
        AmntcreditPrevious = "0"
        AmntdebitPrevious = "0"
        AmntdebitAfter = "0"
        AmntcreditAfter = "0"
        txtTotalValueVAT.Text = "0"
        txtquntUnity.Text = ""
        cmbUnityItems.Text = ""
        mykey.SetValue("DiscountItems", "0")
        txtdiscBill.Text = ""
        lblDiscount_Price_After.Text = "0"
    End Sub
    Private Sub SumAllPrice()

        Dim SM, SMVAT As Double
        Dim TotalCountItems As Integer
        Dim Price, Qunt, Total, XTotal, DiscVal, TotalRateVAT, TotalRateVATFor, TotalVAT, RateVAT, DiscountsValue, TotaIItemDiscount As Double
        For i As Integer = 0 To Dgv_Add.RowCount - 1
            Price = Dgv_Add.Rows(i).Cells(3).Value
            Qunt = Dgv_Add.Rows(i).Cells(5).Value
            RateVAT = Dgv_Add.Rows(i).Cells(11).Value
            Total = Val(Price) * Val(Qunt)
            XTotal = Total


            If ShowValueVAT = "YES" Then
                TotalRateVAT += Format(Val(Total) * Val(RateVAT) / 100, "Fixed")
                TotalRateVATFor = Format(Val(Total) * Val(RateVAT) / 100, "Fixed")
                TotalVAT = Math.Round(TotalRateVATFor, 2) + Math.Round(Total, 2)
            End If
            Total = Math.Round(TotalRateVATFor, 2) + Math.Round(Total, 2)

            If ChkCent2.Checked = True Then
                If ShowDiscountRateItemSales = "NO" Then
                    DiscVal = Val((Val(XTotal) * (100 - Val(Dgv_Add.Rows(i).Cells(12).Value))) / 100)
                Else
                    DiscVal = Val(XTotal)
                End If
                DiscVal = Math.Round(DiscVal, 2)
            ElseIf ChkVal2.Checked = True Then
                DiscVal = Val(XTotal) - Val(Dgv_Add.Rows(i).Cells(12).Value)
            End If
            Dgv_Add.Rows(i).Cells(7).Value = DiscVal

            SM = SM + DiscVal
            SMVAT = SMVAT + Dgv_Add.Rows(i).Cells(9).Value
            Dgv_Add.Rows(i).Cells(9).Value = TotalVAT
            Dgv_Add.Rows(i).Cells(10).Value = XTotal
            DiscountsValue = DiscountsValue + Dgv_Add.Rows(i).Cells(13).Value

            If ChkCent2.Checked = True Then
                StateDisc = "نسبة"
                If ShowDiscountRateItemSales = "NO" Then
                    Dgv_Add.Rows(i).Cells(14).Value = Val((Val(Val(Price) * Val(1)) * (100 - Val(Dgv_Add.Rows(i).Cells(12).Value))) / 100)
                    Dim XVal As String = Format(Val(Price) * Val(Dgv_Add.Rows(i).Cells(12).Value) / 100, "Fixed")
                    DiscountsValue = Val(XVal) * Val(Qunt)
                Else
                    Dgv_Add.Rows(i).Cells(14).Value = Val((Val(Val(Price) * Val(1)) * (100 + Val(Dgv_Add.Rows(i).Cells(12).Value))) / 100)
                    Dim XVal As String = Format(Val(Price) * Val(txtdiscBill.Text) / 100, "Fixed")
                    DiscountsValue = Val(XVal) * Val(Qunt)
                End If
            Else
                StateDisc = "قيمة"
                Dgv_Add.Rows(i).Cells(14).Value = Val(Price) - Val(Dgv_Add.Rows(i).Cells(12).Value)
                DiscountsValue = Val(Dgv_Add.Rows(i).Cells(12).Value)
            End If
            Dgv_Add.Rows(i).Cells(13).Value = DiscountsValue
            TotaIItemDiscount += Dgv_Add.Rows(i).Cells(13).Value
            TotalCountItems += Dgv_Add.Rows(i).Cells(5).Value
        Next
        txtTotalCountItems.Text = TotalCountItems
        txttotalpeforedisc.Text = SM
        txtTotalValueVAT.Text = Math.Round(TotalRateVAT, 2)
        CloseProSalTotalBeforeDisc = txttotalpeforedisc.Text




        'Dim SM, SMVAT As Double
        'Dim Price, Qunt, Total, TotalRateVAT, RateVAT, TotalRateVATFor, TotalVAT, DiscVal, DiscountsValue As Double
        'For i As Integer = 0 To Dgv_Add.RowCount - 1
        '    Price = Dgv_Add.Rows(i).Cells(3).Value
        '    Qunt = Dgv_Add.Rows(i).Cells(5).Value
        '    RateVAT = Dgv_Add.Rows(i).Cells(11).Value
        '    Total = Val(Price) * Val(Qunt)

        '    If ShowValueVAT = "YES" Then
        '        TotalRateVAT += Format(Val(Total) * Val(RateVAT) / 100, "Fixed")
        '        TotalRateVATFor = Format(Val(Total) * Val(RateVAT) / 100, "Fixed")
        '        TotalVAT = Math.Round(TotalRateVATFor, 2) + Math.Round(Total, 2)
        '    End If
        '    Total = Math.Round(TotalRateVATFor, 2) + Math.Round(Total, 2)

        '    If ChkCent2.Checked = True Then
        '        If ShowDiscountRateItemSales = "NO" Then
        '            DiscVal = Val((Val(Total) * (100 - Val(Dgv_Add.Rows(i).Cells(12).Value))) / 100)
        '        Else
        '            DiscVal = Val(Total)
        '        End If
        '        DiscVal = Math.Round(DiscVal, 2)
        '    ElseIf ChkVal2.Checked = True Then
        '        DiscVal = Val(Total) - Val(Dgv_Add.Rows(i).Cells(12).Value)
        '    End If

        '    Dgv_Add.Rows(i).Cells(7).Value = DiscVal

        '    SM = SM + Dgv_Add.Rows(i).Cells(7).Value - TotalVAT
        '    SMVAT = SMVAT + Dgv_Add.Rows(i).Cells(9).Value

        '    DiscountsValue = DiscountsValue + Dgv_Add.Rows(i).Cells(13).Value

        '    If ChkCent2.Checked = True Then
        '        StateDisc = "نسبة"
        '        If ShowDiscountRateItemSales = "NO" Then
        '            Dgv_Add.Rows(i).Cells(14).Value = Val((Val(Val(Price) * Val(1)) * (100 - Val(Dgv_Add.Rows(i).Cells(12).Value))) / 100)
        '            Dim XVal As String = Format(Val(Price) * Val(Dgv_Add.Rows(i).Cells(12).Value) / 100, "Fixed")
        '            DiscountsValue = Val(XVal) * Val(Qunt)
        '        Else
        '            Dgv_Add.Rows(i).Cells(14).Value = Val((Val(Val(Price) * Val(1)) * (100 + Val(Dgv_Add.Rows(i).Cells(12).Value))) / 100)
        '            Dim XVal As String = Format(Val(Price) * Val(txtdiscBill.Text) / 100, "Fixed")
        '            DiscountsValue = Val(XVal) * Val(Qunt)
        '        End If
        '    Else
        '        StateDisc = "قيمة"
        '        Dgv_Add.Rows(i).Cells(14).Value = Val(Price) - Val(Dgv_Add.Rows(i).Cells(12).Value)
        '        DiscountsValue = Val(Dgv_Add.Rows(i).Cells(12).Value)
        '    End If
        'Next

        'txttotalpeforedisc.Text = SM
        'txtTotalValueVAT.Text = Math.Round(TotalRateVAT, 2)
        'TotalDiscountsValue = DiscountsValue
    End Sub

    Private Sub sumdisc()
        'Dim DiscVal, TaxVal, TotalVal As Double

        'If ShowTax <> "0" Then
        '    TaxVal = Format(Val(txttotalpeforedisc.Text) * Val(txtSalestax.Text) / 100, "Fixed")
        'End If

        'If ChkCent.Checked = True Then
        '    DiscVal = Val((Val(txttotalpeforedisc.Text) * (100 - Val(txtdisc.Text))) / 100)
        '    DiscVal = Math.Round(DiscVal, 2)
        'ElseIf ChkVal.Checked = True Then
        '    DiscVal = Val(txttotalpeforedisc.Text) - Val(txtdisc.Text)
        'End If
        'TotalVal = TaxVal + DiscVal
        'txttotalafterdisc.Text = TotalVal + Val(txtTotalValueVAT.Text)

        'If ChkState2.Checked = True Then
        '    txtstaying.Text = Val(txttotalafterdisc.Text) - Val(txtpaying.Text)
        'Else
        '    txtstaying.Text = "0"
        'End If
        'If txtstaying.Text = "0" Then
        '    txtpaying.Text = Val(txttotalafterdisc.Text) - Val(txtstaying.Text)
        'End If

        Dim DiscVal, TaxVal As Double

        If ShowTax <> "0" Then
            TaxVal = Format(Val(txttotalpeforedisc.Text) * Val(txtSalestax.Text) / 100, "Fixed")
        End If

        If ChkCent.Checked = True Then
            DiscVal = Val((Val(txttotalpeforedisc.Text) * (100 - Val(txtdisc.Text))) / 100)
            DiscVal = Math.Round(DiscVal, 4)
        ElseIf ChkVal.Checked = True Then
            DiscVal = Val(txttotalpeforedisc.Text) - Val(txtdisc.Text)
        End If
        txttotalafterdisc.Text = DiscVal + TaxVal + Val(txtTotalValueVAT.Text)

        If ChkState.Checked = True Then
            txtstaying.Text = Val(txttotalafterdisc.Text) - Val(txtpaying.Text)
        Else
            txtstaying.Text = "0"
        End If

        If txtstaying.Text = "0" Then
            txtpaying.Text = Val(txttotalafterdisc.Text) - Val(txtstaying.Text)
        End If

    End Sub

    Private Sub txtbillno_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        MyVars.CheckNumber(txtbillno)
    End Sub

    Private Sub txtprc_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtprc.KeyUp

        If e.KeyCode = 13 Then
            If txtprc.Text.Trim = "" Then
                BtnSave.PerformClick()
            Else
                Bol = True
                ItemsUnityNumber = 0
                If txtprc.Text.Trim = "" Then Exit Sub

                ParcodeMore = txtprc.Text
                GetBarcodeMore(txtprc.Text) : If ParcodeMore <> "" Then : If ParcodeMore <> 0 Then : txtprc.Text = ParcodeMore : End If : End If
                If ParcodeMore = "0" Then
                    Dim PrcUnity As String = Cls.Get_Code_Value("ItemsUnity", "itm_id", "itm_id_Unity", ParcodeMore)
                    If PrcUnity <> "0" Then
                        txtprc.Text = PrcUnity
                    End If
                End If

                'Try
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select group_name , sname,SalPrice,RateVAT from items where itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "' and QuickSearch=0"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    cmbcats.Text = dr(0).ToString
                    cmbname.Text = dr(1).ToString
                    txtprice.Text = Val(dr(2).ToString)
                    ItemsRateVAT = Val(dr(3).ToString)
                Else
                    Cls.Select_More_Data_Stores("items", "group_name,sname,SalPrice,RateVAT,Stores", "itm_id=N'" & txtprc.Text & "' and QuickSearch=0")
                    If dr.HasRows = True Then
                        cmbcats.Text = dr("group_name").ToString
                        cmbname.Text = dr("sname").ToString
                        txtprice.Text = dr("SalPrice").ToString
                        ItemsRateVAT = Val(dr("RateVAT").ToString)
                        cmbStores.Text = dr("Stores").ToString
                    End If
                End If
                'Catch ex As Exception
                '    ErrorHandling(ex, Me.Text)
                'End Try

                GetItemsUnity(cmbUnityItems, txtprc.Text)

                Dim ItemsUnity As String = Cls.Get_Code_Value("ItemsUnity", "Unity_Name", "itm_id_Unity", ParcodeMore)
                If ItemsUnity <> "0" Then
                    cmbUnityItems.Text = ItemsUnity
                End If

                WholeasalePrice()

                SetItemsUnity()

                GetItemsUnityTotalCarton()

                If cmbvendores.Text <> "نقداً" Then
                    Dim LastSalPriceCustomer As String = mykey.GetValue("LastSalPriceCustomer", "NO")
                    If LastSalPriceCustomer = "YES" Then
                        MaxClientPrice()
                    End If
                End If

                txtquntUnity.Focus()
                txtquntUnity.Text = 1
                txtqunt.Text = 1
                txtquntUnity.SelectAll()

                Bol = False
            End If
            Dim X As Double = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)
            txtStoreTotal.Text = X

            If AddBillAuto = "YES" Then
                BtnAdd.PerformClick()
            End If
            ParcodeMore = 0
        End If
    End Sub

    Private Sub cmbname_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbname.DropDown
        cmbUnityItems.Text = ""
        cmbname.Text = ""
        txtqunt.Text = ""
        txtprice.Text = ""
        txtprc.Text = ""
        txtTotalTotal.Text = ""
        txtStoreTotal.Text = ""
        txtquntUnity.Text = ""
        txtRateDiscPriceAfter.Text = ""
    End Sub

    Private Sub cmbname_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbname.KeyUp
        If e.KeyCode = 13 Then
            txtprice.Focus()
        End If
        'If e.KeyCode = 8 Then
        '    txtqunt.Text = ""
        '    txtprc.Text = ""
        '    txtprice.Text = ""
        '    txtStoreTotal.Text = ""
        '    txtTotalTotal.Text = ""
        'End If
    End Sub

    Private Sub GetDataBsal()
        Bol = True
        ItemsUnityNumber = 0
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id,group_name,SalPrice,RateVAT,RateDiscSalPriceAfter from items where sname=N'" & cmbname.Text & "' and Stores =N'" & cmbStores.Text & "' and QuickSearch=0"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                txtprc.Text = dr(0).ToString
                cmbcats.Text = dr(1).ToString
                txtprice.Text = dr(2).ToString
                ItemsRateVAT = Val(dr(3).ToString)
                txtRateDiscPriceAfter.Text = Val(dr(4).ToString)
            Else
                Cls.Select_More_Data_Stores("items", "itm_id,group_name,SalPrice,RateVAT,Stores,RateDiscSalPriceAfter", "sname=N'" & cmbname.Text & "' and QuickSearch=0")
                If dr.HasRows = True Then
                    txtprc.Text = dr("itm_id").ToString
                    cmbcats.Text = dr("group_name").ToString
                    txtprice.Text = dr("SalPrice").ToString
                    ItemsRateVAT = Val(dr("RateVAT").ToString)
                    cmbStores.Text = dr("Stores").ToString
                    txtRateDiscPriceAfter.Text = Val(dr("RateDiscSalPriceAfter").ToString)
                End If
            End If

            If cmbvendores.Text <> "نقداً" Then
                Dim LastSalPriceCustomer As String = mykey.GetValue("LastSalPriceCustomer", "NO")
                If LastSalPriceCustomer = "YES" Then
                    MaxClientPrice()
                End If
            End If

            txtStoreTotal.Text = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)

            WholeasalePrice()


            txtquntUnity.Focus()
            txtquntUnity.SelectAll()
            txtquntUnity.Text = 1
            txtqunt.Text = 1
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        GetItemsUnity(cmbUnityItems, txtprc.Text)

        SetItemsUnity()

        GetItemsUnityTotalCarton()

        Bol = False

        If AddBillAuto = "YES" Then
            BtnAdd.PerformClick()
        End If

    End Sub

    Private Sub MaxClientPrice()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select TOP (100) PERCENT dbo.BillsalData.id, dbo.BillsalData.bill_no, dbo.Sales_Bill.Vendorname, dbo.BillsalData.itm_id, dbo.BillsalData.itm_cat, dbo.BillsalData.itm_name, dbo.BillsalData.price,  dbo.BillsalData.bill_date, dbo.Sales_Bill.billtime From dbo.Sales_Bill INNER Join dbo.BillsalData On dbo.Sales_Bill.bill_No = dbo.BillsalData.bill_no Group By dbo.BillsalData.price, dbo.BillsalData.itm_name, dbo.BillsalData.id, dbo.BillsalData.itm_cat, dbo.BillsalData.itm_id, dbo.Sales_Bill.Vendorname, dbo.BillsalData.bill_date, dbo.Sales_Bill.billtime, dbo.BillsalData.bill_no  HAVING      (dbo.Sales_Bill.Vendorname =N'" & cmbvendores.Text & "') AND (dbo.BillsalData.itm_id =N'" & txtprc.Text & "') Order By dbo.Sales_Bill.billtime DESC, dbo.BillsalData.bill_date DESC"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then Exit Sub
            If dr(0) Is DBNull.Value Then
            Else
                txtprice.Text = dr("price")
            End If

            If txtprice.Text = "" Or txtprice.Text = "0" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select SalPrice from items where itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = False Then Exit Sub
                If dr(0) Is DBNull.Value Then
                Else
                    txtprice.Text = dr(0)
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbcats_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Try
            If cmbcats.Text = "" Then GoTo 1
            If Bol = True Then GoTo 1
            Bol = True

            Cls.fill_combo_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbname)
            cmbname.Text = ""
1:
            Bol = False
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Dim RNXD As Integer
    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        For i As Integer = 0 To Dgv_Add.SelectedRows.Count - 1
            RNXD = Dgv_Add.CurrentRow.Index
            Dgv_Add.Rows.RemoveAt(RNXD)
        Next
        SumAllPrice() : sumdisc()
        txtCountItems.Text = Dgv_Add.Rows.Count
    End Sub

    Private Sub BtnAddVendor_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        frmvendors.Show()
    End Sub

    Function Return_D_Week(ByVal DT As Date) As String
        Dim XMN
        Dim XDAY
        XMN = DT.DayOfWeek

        If XMN = 1 Then XDAY = "الأثنين"
        If XMN = 2 Then XDAY = "الثلاثاء"
        If XMN = 3 Then XDAY = "الأربعاء"
        If XMN = 4 Then XDAY = "الخميس"
        If XMN = 5 Then XDAY = "الجمعة"
        If XMN = 6 Then XDAY = "السبت"
        If XMN = 0 Then XDAY = "الأحد"
        Return XDAY
    End Function
    Private Sub BtnClear_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnClear.Click

        cmbvendores.Enabled = True
        txtbillno.Enabled = True

        Dt_AddBill.Rows.Clear() : ClearSave()
        MAXRECORD()
        Exit Sub
        'For RNXD = 0 To Dgv_Add.Rows.Count
        '    RNXD = Dgv_Add.CurrentRow.Index
        '    Dgv_Add.Rows.RemoveAt(RNXD)
        'Next
        'Exit Sub


        ' MsgBox(Dgv_Add.Rows.Count)
        'For i As Integer = 0 To Dgv_Add.Rows.Count
        Dgv_Add.DataSource = ""
        Dt_AddBill.Columns.Add("الباركود", GetType(String))
        Dt_AddBill.Columns.Add("المجموعة", GetType(String))
        Dt_AddBill.Columns.Add("الصنف", GetType(String))
        Dt_AddBill.Columns.Add("السعر", GetType(Double))
        Dt_AddBill.Columns.Add("الكمية", GetType(Integer))
        Dt_AddBill.Columns.Add("إجمالي", GetType(Double))
        Dt_AddBill.Columns.Add("المخزن", GetType(String))
        '  Next

    End Sub

    Private Sub Timer1_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Timer1.Tick
        TxtHour.Text = Cls.get_time(True)
        TxtDay.Text = Return_D_Week(DTPDate.Text)
    End Sub

    Private Sub txtdisc_KeyUp1(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtdisc.KeyUp
        If e.KeyCode = 13 Then
            BtnSave.PerformClick()
        End If
    End Sub

    Private Sub txtdisc_TextChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtdisc.TextChanged
        MyVars.CheckNumber(txtdisc)

        sumdisc()
    End Sub

    Private Sub txtpaying_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            BtnSave.PerformClick()
        End If
    End Sub

    Private Sub txtpaying_TextChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs)
        sumdisc()
    End Sub

    Private Sub ChkVal_CheckedChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkVal.CheckedChanged
        sumdisc()
    End Sub

    Private Sub ChkCent_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkCent.CheckedChanged
        sumdisc()
    End Sub

    Private Sub txttotalpeforedisc_TextChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txttotalpeforedisc.TextChanged
        sumdisc()
    End Sub

    Private Sub cmbvendoresx_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbvendores.KeyUp
        If e.KeyCode = 13 Then
            txtbillno.Focus()
        End If
    End Sub

    Private Sub cmbvendoresx_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbvendores.SelectedIndexChanged
        If cmbvendores.Text <> "نقداً" Then
            Dim LastSalPriceCustomer As String = mykey.GetValue("LastSalPriceCustomer", "NO")
            If LastSalPriceCustomer = "YES" Then
                MaxClientPrice()
            End If
        Else
            Try
                txtprice.Text = Cls.Get_Code_Value_Stores("items", "SalPrice", "sname", cmbname.Text)
            Catch ex As Exception
                'ErrorHandling(ex, Me.Text)
            End Try
        End If

        '============================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select dbo.Customers.Vendorname, dbo.Employees.NameEmployee  From dbo.Customers INNER Join  dbo.Employees ON dbo.Customers.Emp_Code = dbo.Employees.EMPID  Where (dbo.Customers.Vendorname = N'" & cmbvendores.Text & "')"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                If dr(0) Is DBNull.Value Then
                Else
                    cmbEmployees.Text = dr("NameEmployee")
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        '============================================================

        If cmbvendores.Text <> "نقداً" Then
            Dim PriceType_ID As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PriceType_ID from Customers where Vendorname =N'" & cmbvendores.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                PriceType_ID = dr("PriceType_ID").ToString
                If PriceType_ID = 1 Then
                    rdoSectors.Checked = True
                End If
                If PriceType_ID = 2 Then
                    rdoWhole.Checked = True
                End If
                If PriceType_ID = 3 Then
                    rdoWholeWhole.Checked = True
                End If
            Else
                rdoSectors.Checked = True
            End If
        Else
            rdoSectors.Checked = True
        End If
    End Sub

    Private Sub txtbillno_TextChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtbillno.TextChanged
        MyVars.CheckNumber(txtbillno)
    End Sub

    Private Sub MAXRECORD()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "select * from IM_Bsal"
            dr = cmd.ExecuteReader

            Dim dt As New DataTable
            dt.Load(dr)

            If dt.Rows.Count = 0 Then
                Me.txtbillno.Text = 1
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(CAST(bill_No As float)) as mb FROM IM_Bsal"
                dr = cmd.ExecuteReader
                dr.Read()

                Dim sh As Long
                sh = dr("mb")
                Me.txtbillno.Text = sh + 1
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbname_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbname.SelectedIndexChanged
        If txtprc.Text = "" Then
            If ActionDataAdapter = False Then
                GetDataBsal()
            End If
        End If
    End Sub

    Private Sub txttotalafterdisc_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txttotalafterdisc.TextChanged
        sumdisc()
    End Sub

    Private Sub txtpaying_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtpaying.TextChanged
        sumdisc()
    End Sub

    Private Sub txtstaying_TextChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtstaying.TextChanged
        sumdisc()
    End Sub

    Private Sub cmbStores_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbStores.KeyUp
        If e.KeyCode = 13 Then
            GetDataBsal()
        End If
    End Sub

    Private Sub ChkState2_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkState.CheckedChanged
        If ChkCash.Checked = True Or chkVisa.Checked = True Then
            'pay = txtpaying.Text
            txtpaying.Text = txttotalafterdisc.Text : txtpaying.Enabled = False : txtpaying.ReadOnly = True
        Else
            'txtpaying.Text = pay
            txtpaying.Text = "0"
            txtpaying.Enabled = True : txtpaying.ReadOnly = False
            sumdisc()
        End If
    End Sub

    Private Sub cmbcats_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbcats.DropDown
        cmbname.Text = ""
        cmbcats.Text = ""
        txtqunt.Text = ""
        txtprc.Text = ""
        txtprice.Text = ""
    End Sub

    Private Sub cmbcats_KeyUp2(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbcats.KeyUp
        If e.KeyCode = 13 Then
            cmbname.Focus()
        End If
        'If e.KeyCode = 8 Then
        '    txtqunt.Text = ""
        '    txtprc.Text = ""
        '    txtprice.Text = ""
        '    cmbname.Text = ""
        '    txtStoreTotal.Text = ""
        '    txtTotalTotal.Text = ""
        'End If
    End Sub

    Private Sub cmbcats_SelectedIndexChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbcats.SelectedIndexChanged
        If cmbname.Text = "" Then
            Try
                If ActionDataAdapter = False Then


                    If txtprc.Text = "" Then
                        If cmbcats.Text.Trim = "" Then Exit Sub
                        'Cls.fill_combo_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbname)
                        Cls.fill_combo_DataAdapter_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbname)

                        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        'If Company_Branch_ID = "0" Then
                        '    cmd.CommandText = "select distinct sname from Items where group_name =N'" & cmbcats.Text & "' and QuickSearch=0 order by 1"
                        'Else
                        '    cmd.CommandText = "select distinct sname from Items where group_name =N'" & cmbcats.Text & "' and QuickSearch=0 and Company_Branch_ID = N'" & Company_Branch_ID & "'  or Company_Branch_ID =N'0' order by 1"
                        'End If
                        'dr = cmd.ExecuteReader
                        'Do While dr.Read = True
                        '    cmbname.Items.Add(Trim(dr(0)))
                        'Loop
                        cmbname.Text = ""
                    End If
                End If
            Catch ex As Exception
                ErrorHandling(ex, Me.Text)
            End Try
        End If
    End Sub

    Private Sub btnEmp_Click(sender As Object, e As EventArgs) Handles btnEmp.Click
        PanelEmployees.Top = 4
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        PanelEmployees.Top = 10000
    End Sub

    Private Sub ChkCash_CheckedChanged(sender As Object, e As EventArgs) Handles ChkCash.CheckedChanged
        If ChkCash.Checked = True Or chkVisa.Checked = True Then
            'pay = txtpaying.Text
            txtpaying.Text = txttotalafterdisc.Text : txtpaying.Enabled = False : txtpaying.ReadOnly = True
        Else
            'txtpaying.Text = pay
            txtpaying.Text = "0"
            txtpaying.Enabled = True : txtpaying.ReadOnly = False
            sumdisc()
        End If
    End Sub

    Private Sub chkVisa_CheckedChanged(sender As Object, e As EventArgs) Handles chkVisa.CheckedChanged
        If ChkCash.Checked = True Or chkVisa.Checked = True Then
            'pay = txtpaying.Text
            txtpaying.Text = txttotalafterdisc.Text : txtpaying.Enabled = False : txtpaying.ReadOnly = True
        Else
            'txtpaying.Text = pay
            txtpaying.Text = "0"
            txtpaying.Enabled = True : txtpaying.ReadOnly = False
            sumdisc()
        End If
    End Sub

    Private Sub btnAddImageBill_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddImageBill.Click
        Try
            OpenFileDialog1.Filter = "Image Files (*.png *.jpg *.bmp *.JPE *.JPEG) |*.png; *.jpg; *.bmp; *.JPE; *.JPEG|All Files(*.*) |*.*"
            With Me.OpenFileDialog1
                .FilterIndex = 1
                .Title = "أختر صورة الفاتورة"
                .ShowDialog()
                If Len(.FileName) > 0 Then
                    PicBill.Image = Image.FromFile(OpenFileDialog1.FileName)
                End If
            End With
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub ImageUpdateBill()
        Try
            If OpenFileDialog1.FileName <> "" Then
                connectionStringOpen()
                Dim cmd2 As SqlClient.SqlCommand = New SqlClient.SqlCommand
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = " Update IM_Bsal SET  Image_Bill = @Image_Bill WHERE bill_No =N'" & txtbillno.Text & "'"
                cmd2.CommandType = CommandType.Text
                cmd2.Connection = Cn
                Dim fs As FileStream = New FileStream(OpenFileDialog1.FileName, FileMode.Open, FileAccess.Read)
                Dim r As BinaryReader = New BinaryReader(fs)
                Dim FileByteArray(fs.Length - 1) As Byte
                r.Read(FileByteArray, 0, CInt(fs.Length))
                With cmd2
                    .CommandType = CommandType.Text
                    .Connection = Cn
                    .Parameters.Add("@Image_Bill", SqlDbType.Image).Value = FileByteArray
                    .CommandText = S
                End With
                cmd2.ExecuteNonQuery()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub


    Private Declare Function WriteProfileString Lib "kernel32" Alias "WriteProfileStringA" _
    (ByVal lpszSection As String, ByVal lpszKeyName As String,
    ByVal lpszString As String) As Long
    Private Declare Function SendMessage Lib "user32" Alias "SendMessageA" _
         (ByVal hwnd As Long, ByVal wMsg As Long,
         ByVal wParam As Long, ByVal lparam As String) As Long
    Private Const HWND_BROADCAST As Long = &HFFFF&
    Private Const WM_WININICHANGE As Long = &H1A

    Private Function SetDefaultSystemPrinter3(ByVal strPrinterName As String) As Boolean
        'this method does not valid if the change is correct and does not revert to previous printer if wrong
        Dim DeviceLine As String

        'rebuild a valid device line string 
        DeviceLine = strPrinterName & ",,"

        'Store the new printer information in the 
        '[WINDOWS] section of the WIN.INI file for 
        'the DEVICE= item 
        Call WriteProfileString("windows", "Device", DeviceLine)

        'Cause all applications to reload the INI file 
        Call SendMessage(HWND_BROADCAST, WM_WININICHANGE, 0, "windows")

        Return True
    End Function

    Private Sub txtquntUnity_TextChanged(sender As Object, e As EventArgs) Handles txtquntUnity.TextChanged
        cmbUnityItems_SelectedIndexChanged(sender, e)

        MyVars.CheckNumber(txtquntUnity)
        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If
        TotalBeforeVAT = Val(txtprice.Text) * Val(txtquntUnity.Text)
        If ShowValueVAT = "YES" Then
            TotalValueVAT = Format(Val(TotalBeforeVAT) * Val(ItemsRateVAT) / 100, "Fixed")
        End If
        txtTotalTotal.Text = Math.Round(TotalValueVAT, 2) + Math.Round(TotalBeforeVAT, 2)
        If ShowTax = "0" Then
            If StateRateTax = False Then
                txtSalestax.Text = ItemsRateVAT
            End If
            StateRateTax = True
        End If
        Try
            txtTotalTotal.Text = Math.Round(Convert.ToDouble(txtTotalTotal.Text), 2)
        Catch ex As Exception
        End Try
        sumdisc1()
    End Sub

    Private Sub txtquntUnity_KeyUp(sender As Object, e As KeyEventArgs) Handles txtquntUnity.KeyUp
        If e.KeyCode = 13 Then
            If NotUnityItemsProgram = "YES" Then
                cmbUnityItems.Focus()
                cmbUnityItems.SelectAll()
            Else
                BtnAdd.PerformClick()
            End If
        End If
    End Sub

    Private Sub cmbUnityItems_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbUnityItems.KeyUp
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub cmbUnityItems_SelectedIndexChanged_1(sender As Object, e As EventArgs) Handles cmbUnityItems.SelectedIndexChanged
        Try
            If Bol = False Then
                If NotUnityItemsProgram = "YES" Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 0 : End If
                    txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)

                    If ItemsUnityNumber <> 1 Then
                        If ItemsUnityNumber <> 0 Then
                            Dim PriceNumber As String = Cls.Get_Code_Value_Branch_More("ItemsUnity", "SalPriceUnit", "itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'")
                            If PriceNumber <> "0" Then
                                txtprice.Text = PriceNumber
                            End If
                        End If
                    End If
                Else
                    txtqunt.Text = Val(txtquntUnity.Text)
                End If


                GetItemsUnityTotalCarton()

                sumdisc1()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbUnityItems_SelectedIndexChanged(sender As Object, e As EventArgs)
        Try
            If NotUnityItemsProgram = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 0 : End If
                txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
            Else
                txtqunt.Text = Val(txtquntUnity.Text)
            End If

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub btnPricees_Click(sender As Object, e As EventArgs) Handles btnPricees.Click
        Panel_Prices.Top = 4
    End Sub

    Public Sub AverageTinPrice(ByVal itm_id As String, ByVal Stores As String, ByVal Price As Double, ByVal Qunt As Double, ByVal Unity As String, ByVal DiscountRate As Double, ByVal PriceDiscount As Double)
        Try
            Dim PriceAverage As String
            TinPriceAverage = 0
            Dim Xqunt As Double = Qunt

            If NotUnityItemsProgram = "YES" Then
                PriceAverage = Cls.Get_Code_Value_Branch_More("ItemsUnity", "TinPriceUnit", "itm_id=N'" & itm_id & "' and Unity_Name=N'" & Unity & "'")
                TinPrice = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)

                Price = Cls.Get_Code_Value_Branch_More("ItemsUnity", "SalPriceUnit", "itm_id=N'" & itm_id & "' and Unity_Name=N'" & Unity & "'")
                Price = Math.Round(Convert.ToDouble(Val(Price)), 2)
            Else
                PriceAverage = Cls.Get_Code_Value_Stores_More("items", "TinPriceAverage", "itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'")
                TinPriceAverage = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)

                PriceAverage = Cls.Get_Code_Value_Stores_More("items", "TinPrice", "itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'")
                TinPrice = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)
            End If

            'If NotUnityItemsProgram = "YES" Then
            '    NumberPieces = Cls.Get_Code_Value_Stores_More("ItemsUnity", "NumberPieces", "itm_id =N'" & itm_id & "' and Unity_Name=N'" & Unity & "'")
            '    If NumberPieces <> 1 Then
            '        Price = Cls.Get_Code_Value_Stores_More("Items", "SalPrice", "itm_id =N'" & itm_id & "' and Stores=N'" & Stores & "'")
            '        If NumberPieces <> 0 Then
            '            Xqunt = Val(NumberPieces) * Val(Qunt)
            '        End If
            '    End If
            'End If

            Price_Unity = Price
            If DiscountRate <> 0 Then
                Price = PriceDiscount
                Price_Unity = PriceDiscount
            End If

            If PriceAverage = "" Then : PriceAverage = 0 : End If

            If LastTinPriceItems = "NO" Then : PriceAverage = TinPriceAverage : Else PriceAverage = TinPrice : End If
            If TinPriceAverage = 0 Then
                PriceAverage = TinPrice
                TinPriceAverage = TinPrice
            End If

            TotalPrice = Price - PriceAverage
            Profits = TotalPrice * Xqunt
            Profits = Math.Round(Profits, 2)

            If DiscountRate <> 0 Then
                If DiscountsTin <> 0 Then
                    Dim TotalDiscountRate As Double = DiscountsTin - DiscountRate
                    Dim TotalRate As Double = Format(Val(PriceAverage) * Val(TotalDiscountRate) / 100, "Fixed")
                    Profits = Format(Val(Xqunt) * Val(TotalRate))
                    Profits = Math.Round(Profits, 2)
                End If
            End If

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub btnClosePrices_Click(sender As Object, e As EventArgs) Handles btnClosePrices.Click
        Panel_Prices.Top = 10000
    End Sub

    Private Sub txtTotalValueVAT_TextChanged(sender As Object, e As EventArgs) Handles txtTotalValueVAT.TextChanged
        sumdisc()
    End Sub

    Private Sub SearchForProduct()
        Dim SearchForProduct As String = mykey.GetValue("SearchForProduct", "NO")
        If SearchForProduct = "YES" Then
            cmbcats.Visible = False
            Labelcats.Visible = False
            cmbname.Size = New System.Drawing.Size(320, 21)
        Else
            cmbcats.Visible = True
            Labelcats.Visible = True
        End If
    End Sub

    Private Sub Daily_Restrictions()
        Dim Account As String = "" : Dim AccountCode As String = "" : Dim AccountPaying As String = "" : Dim PayingCode As String = ""
        Dim Discounts As String = "" : Dim DiscountsCode As String = "" : Dim AccountTreasury As String = "" : Dim AccountCodeTreasury As String = ""

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مرتجعات مبيعات'" : dr = cmd.ExecuteReader
            If dr.Read Then
                Account = dr("Link_AccountsTree") : AccountCode = dr("ACCNumber")
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مرتجعات مقبوضات عملاء'" : dr = cmd.ExecuteReader
            If dr.Read Then
                AccountPaying = dr("Link_AccountsTree") : PayingCode = dr("ACCNumber")
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مرتجعات خصومات عملاء'" : dr = cmd.ExecuteReader
            If dr.Read Then
                Discounts = dr("Link_AccountsTree") : DiscountsCode = dr("ACCNumber")
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select ACCName,ACCNumber from AccountsTree where ACCName=N'الخزينة'" : dr = cmd.ExecuteReader
            If dr.Read Then
                AccountTreasury = dr("ACCName") : AccountCodeTreasury = dr("ACCNumber")
            End If

            '========================================================================================

            Dim MOVID As String = Cls.MAXRECORD("MOVES", "MOVID")
            Dim MOVRegNumber As String = Cls.MAXRECORD("MOVES", "MOVRegNumber")

            If ChkCash.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Account & "',N'" & txttotalpeforedisc.Text & "',N'" & txttotalpeforedisc.Text & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If

            If chkVisa.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Account & "',N'" & txttotalpeforedisc.Text & "',N'" & txttotalpeforedisc.Text & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If

            If ChkState.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Account & "',N'" & txttotalpeforedisc.Text & "',N'" & txttotalpeforedisc.Text & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If

            If Val(txtpaying.Text) > 0 Then
                'Dim bill_no As String = Cls.MAXRECORD("IM_Vst", "id") - 1

                ' من حساب / مرتجعات مقبوضات عملاء
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & PayingCode & "',N'" & AccountPaying & "',N'" & txtpaying.Text & "',N'0',N'" & AccountPaying & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()


                ' الى حساب / الخزينة
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'0',N'" & txtpaying.Text & "',N'" & AccountTreasury & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

            End If

            If Val(txtdisc.Text) > 0 Then
                'Dim bill_no As String = Cls.MAXRECORD("IM_Vst_disc", "id") - 1

                ' من حساب / الخزينة
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'" & DiscTotal & "',N'0',N'" & AccountTreasury & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' الى حساب / مرتجعات خصومات عملاء
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & DiscountsCode & "',N'" & Discounts & "',N'0',N'" & DiscTotal & "',N'" & Discounts & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

    End Sub

    Private Sub GetDebtorlCreditorPrevious()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select vnamntcredit , vnamntdebit from Customers where Vendorname=N'" & cmbvendores.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then Exit Sub
            If dr(0) Is DBNull.Value Or dr(1) Is DBNull.Value Then
            Else
                AmntcreditPrevious = dr("vnamntcredit")
                AmntdebitPrevious = dr("vnamntdebit")
            End If

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub SetItemsUnity()
        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 1 : End If
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(txtquntUnity.Text)
        End If

    End Sub

    Private Sub txtdiscBill_TextChanged(sender As Object, e As EventArgs)
        MyVars.CheckNumber(txtdiscBill)
        sumdisc1()
    End Sub

    Private Sub sumdisc1()
        Dim DiscVal As Double
        Dim TotalPriseQunt As Double
        If ShowDiscountRateItemSales = "NO" Then
            TotalPriseQunt = Val(txtprice.Text) * Val(txtquntUnity.Text)
        Else
            TotalPriseQunt = Val(txtRateDiscPriceAfter.Text) * Val(txtquntUnity.Text)
        End If

        If ChkCent2.Checked = True Then
            If ShowDiscountRateItemSales = "NO" Then
                DiscVal = Val((Val(TotalPriseQunt) * (100 - Val(txtdiscBill.Text))) / 100)
            Else
                DiscVal = Val(TotalPriseQunt)
            End If
            DiscVal = Math.Round(DiscVal, 2)
        ElseIf ChkVal2.Checked = True Then
            DiscVal = Val(TotalPriseQunt) - Val(txtdiscBill.Text)
        End If
        txtTotalTotal.Text = Math.Round(TotalValueVAT, 2) + Math.Round(DiscVal, 2)

        If ChkCent2.Checked = True Then
            StateDisc = "نسبة"
            If ShowDiscountRateItemSales = "NO" Then
                lblDiscount_Price_After.Text = Val((Val(Val(txtprice.Text) * Val(1)) * (100 - Val(txtdiscBill.Text))) / 100)
                Dim XVal As String = Format(Val(txtprice.Text) * Val(txtdiscBill.Text) / 100, "Fixed")
                DiscountsValue = Val(XVal) * Val(txtquntUnity.Text)
            Else
                lblDiscount_Price_After.Text = Val((Val(Val(txtprice.Text) * Val(1)) * (100 + Val(txtdiscBill.Text))) / 100)
                Dim XVal As String = Format(Val(txtprice.Text) * Val(txtdiscBill.Text) / 100, "Fixed")
                DiscountsValue = Val(XVal) * Val(txtquntUnity.Text)

                'txtRateDiscPriceAfter.Text = Val((Val(txtprice.Text) * (100 - Val(txtdiscBill.Text))) / 100)
            End If
        Else
            StateDisc = "قيمة"
            DiscountsValue = Val(txtdiscBill.Text)
        End If

    End Sub

    Private Sub txtdiscBill_MouseDoubleClick(sender As Object, e As MouseEventArgs)

    End Sub

    Private Sub ChkCent2_CheckedChanged(sender As Object, e As EventArgs) Handles ChkCent2.CheckedChanged
        sumdisc1()
    End Sub

    Private Sub ChkVal2_CheckedChanged(sender As Object, e As EventArgs) Handles ChkVal2.CheckedChanged
        sumdisc1()
    End Sub

    Private Sub txtdiscBill_TextChanged_1(sender As Object, e As EventArgs) Handles txtdiscBill.TextChanged
        MyVars.CheckNumber(txtdiscBill)
        sumdisc1()
    End Sub

    Private Sub txtdiscBill_KeyUp(sender As Object, e As KeyEventArgs) Handles txtdiscBill.KeyUp
        If ((e.KeyCode = Keys.Enter)) Then
            txtprice.Focus()
        End If
    End Sub

    Private Sub txtdiscBill_MouseDoubleClick_1(sender As Object, e As MouseEventArgs) Handles txtdiscBill.MouseDoubleClick
        PaneldiscBill.Top = 200
    End Sub

    Private Sub btnClosediscBill_Click(sender As Object, e As EventArgs) Handles btnClosediscBill.Click
        PaneldiscBill.Top = 10000
    End Sub

    Private Sub Label20_MouseDoubleClick(sender As Object, e As MouseEventArgs) Handles Label20.MouseDoubleClick
        Dim MyString As String
        MyString = InputBox("أدخل خصم الصنف", "طلب معلومات", Nothing)

        If MyString <> "" Then
            mykey.SetValue("DiscountItems", MyString)
            txtdiscBill.Text = MyString
            For i As Integer = 0 To Dgv_Add.RowCount - 1
                Dgv_Add.Rows(i).Cells(12).Value = MyString
            Next
            sumdisc1()
        End If
    End Sub

    Private Sub WholeasalePrice()
        Try
10:
            If rdoSectors.Checked = True Then
                txtprice.Text = Cls.Get_Code_Value_Stores_More("items", "SalPrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
                txtdiscBill.Text = Cls.Get_Code_Value_Stores_More("items", "RateDiscSalPrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
            End If

            If rdoWhole.Checked = True Then
                txtprice.Text = Cls.Get_Code_Value_Stores_More("items", "WholePrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
                txtdiscBill.Text = Cls.Get_Code_Value_Stores_More("items", "RateDiscWholePrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
            End If

            If rdoWholeWhole.Checked = True Then
                txtprice.Text = Cls.Get_Code_Value_Stores_More("items", "WholeWholePrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
                txtdiscBill.Text = Cls.Get_Code_Value_Stores_More("items", "RateDiscWholeWholePrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
            If NetworkName = "Yes" Then
                If UseExternalServer = "Yes" Then
                    connect()
                    GoTo 10
                End If
            End If
        End Try
    End Sub

    Sub ShowSalesTax()
        If ShowValueVAT = "YES" Then
            lblTaxSal.Visible = True
            lblPercent.Visible = False
            txtSalestax.Visible = True
            txtTotalValueVAT.Visible = True
            lblPercent.Visible = True
            lblTotalValueVAT.Visible = True
        Else
            If ShowTax = "0" Then
                lblTaxSal.Visible = False
                lblPercent.Visible = False
                txtSalestax.Visible = False
                txtTotalValueVAT.Visible = False
                lblPercent.Visible = False
                lblTotalValueVAT.Visible = False
            Else
                lblTaxSal.Visible = True
                lblPercent.Visible = True
                txtSalestax.Visible = True
                txtTotalValueVAT.Visible = True
                lblPercent.Visible = True
                lblTotalValueVAT.Visible = True
            End If
        End If
    End Sub

    Private Sub GetItemsUnityTotalCarton()
        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from View_ItemsUnity where itm_id=N'" & txtprc.Text & "' and Unity_Name =N'" & cmbUnityItems.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                NumberPieces = dr(0).ToString
            End If

            Dim Itm_Store As Double = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)
            If NumberPieces = 0 Or NumberPieces = 1 Then
                txtStoreTotal.Text = Itm_Store
            Else
                If Itm_Store = 0 And NumberPieces = 0 Then
                    txtStoreTotal.Text = 0
                Else
                    txtStoreTotal.Text = Val(Itm_Store) / Val(NumberPieces)
                    txtStoreTotal.Text = Math.Round(Val(txtStoreTotal.Text), 2)
                End If
            End If
        Else
            txtqunt.Text = Val(txtquntUnity.Text)
        End If


    End Sub

    Private Sub Frm_IM_BSal_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        If txttotalpeforedisc.Text <> "0" Then
            Dim x As String = MsgBox("يوجد فاتورة لم يتم حفظها هل تريد حفظ الفاتورة قبل الاغلاق", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
            If x = vbCancel Then
                Exit Sub
            Else
                BtnSave.PerformClick()
            End If
        End If
    End Sub

    Private Sub Dgv_Add_CellValueChanged(sender As Object, e As DataGridViewCellEventArgs) Handles Dgv_Add.CellValueChanged

        If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Add.Rows.Count) = 0 Then Beep() : Exit Sub
        Dim ITMID As String = Dgv_Add.SelectedRows(0).Cells(0).Value
        Dim Stores As String = Dgv_Add.SelectedRows(0).Cells(8).Value
        Dim Unity As String = Dgv_Add.SelectedRows(0).Cells(6).Value

        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & ITMID & "' and Unity_Name=N'" & Unity & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 1 : End If
            Dgv_Add.SelectedRows(0).Cells(4).Value = Val(NumberPieces) * Val(Dgv_Add.SelectedRows(0).Cells(5).Value)
        Else
            Dgv_Add.SelectedRows(0).Cells(4).Value = Val(Dgv_Add.SelectedRows(0).Cells(5).Value)
        End If

        SumAllPrice()
    End Sub

    Private Sub txtprc_KeyDown(sender As Object, e As KeyEventArgs) Handles txtprc.KeyDown
        If ((e.KeyCode = 8)) Then
            cmbUnityItems.Text = ""
            cmbname.Text = ""
            txtqunt.Text = ""
            txtprice.Text = ""
            txtTotalTotal.Text = ""
            txtStoreTotal.Text = ""
            txtquntUnity.Text = ""
            txtRateDiscPriceAfter.Text = ""
        End If
    End Sub

    Private Sub txtbillno_KeyUp_1(sender As Object, e As KeyEventArgs) Handles txtbillno.KeyUp
        FillDataItems_Text(txtbillno.Text)
    End Sub
    Private Sub FillDataItems_Text(ByVal Number As Double)
1:
        ''Try
        'FillDataActive = True
        'txtbillno.Enabled = False
        'Dt_AddBill.Rows.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'S = "select itm_id,itm_cat,itm_name, Price, qu,qu_unity,itm_Unity,totalprice,Stores,Discounts,ValueVAT,BeforeVAT,RateVAT,DiscountsValue,Discount_Price_After,bill_EndDate from BillsalData where bill_no =N'" & Number & "'" : cmd.CommandText = S : dr = cmd.ExecuteReader
        'Do While dr.Read
        '    Dgv_Add.DataSource = Fn_AddBill(dr(0).ToString, dr(1).ToString, dr(2).ToString, Val(dr(3).ToString), Val(dr(4).ToString), Val(dr(5).ToString), dr(6).ToString, Val(dr(7).ToString), dr(8).ToString, Val(dr(9).ToString), "", Val(0), Val(0), Val(0), Val(0), Val(0), Val(dr(10).ToString), Val(dr(11).ToString), Val(dr(12).ToString), Val(dr(13).ToString), Val(dr(14).ToString), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), dr(15).ToString, 0)
        'Loop

        'Dim XStat As String = ""
        'Dim Xbill_date As String = ""
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select * from Sales_Bill where bill_no =N'" & Number & "'" : dr = cmd.ExecuteReader : dr.Read()
        'If dr.HasRows = True Then
        '    txtbillno.Text = Number
        '    cmbvendores.Text = dr("Vendorname").ToString
        '    Xbill_date = dr("bill_date")
        '    DateTimePicker1.Text = Cls.R_date(Xbill_date)
        '    txttotalpeforedisc.Text = dr("totalpricebeforedisc").ToString
        '    txtdisc.Text = dr("disc").ToString
        '    txttotalafterdisc.Text = dr("totalpriceafterdisc").ToString
        '    txtstaying.Text = dr("STAYING").ToString
        '    txtpaying.Text = dr("bey").ToString
        '    txtSalestax.Text = dr("SalesTax").ToString
        '    txtNotes.Text = dr("Notes").ToString
        '    cmbEmployees.Text = dr("EmpName").ToString
        '    txtExpenses.Text = dr("ExpensesBill").ToString
        '    txtSheft_Number.Text = dr("Sheft_Number").ToString
        '    XStat = dr("stat").ToString
        '    If XStat = "نقداً" Then
        '        ChkCash.Checked = True
        '    ElseIf XStat = "آجل" Then
        '        ChkState.Checked = True
        '    ElseIf XStat = "فيزا" Then
        '        chkVisa.Checked = True
        '    End If
        'End If

        'SumAllPrice()
        'sumdisc()

        'Try
        '    Dgv_Add.Columns(2).Width = 220
        '    Dgv_Add.Columns(0).ReadOnly = True
        '    Dgv_Add.Columns(1).ReadOnly = True
        '    Dgv_Add.Columns(2).ReadOnly = True
        '    Dgv_Add.Columns(4).ReadOnly = True
        '    Dgv_Add.Columns(6).ReadOnly = True
        '    Dgv_Add.Columns(7).ReadOnly = True
        '    Dgv_Add.Columns(1).Visible = False
        '    Dgv_Add.Columns(4).Visible = False
        '    Dgv_Add.Columns(8).Visible = False
        '    Dgv_Add.Columns(11).Visible = False
        '    Dgv_Add.Columns(12).Visible = False
        '    Dgv_Add.Columns(13).Visible = False
        '    Dgv_Add.Columns(14).Visible = False
        '    Dgv_Add.Columns(15).Visible = False
        '    If ShowValueVAT = "NO" Then
        '        Dgv_Add.Columns(16).Visible = False
        '        Dgv_Add.Columns(17).Visible = False
        '        Dgv_Add.Columns(18).Visible = False
        '    End If
        '    Dgv_Add.Columns(19).Visible = False
        '    Dgv_Add.Columns(20).Visible = False
        '    Dgv_Add.Columns(21).Visible = False
        '    Dgv_Add.Columns(22).Visible = False
        '    Dgv_Add.Columns(23).Visible = False
        '    Dgv_Add.Columns(24).Visible = False
        '    Dgv_Add.Columns(25).Visible = False
        '    Dgv_Add.Columns(26).Visible = False
        '    Dgv_Add.Columns(27).Visible = False
        '    Dgv_Add.Columns(28).Visible = False
        '    Dgv_Add.Columns(29).Visible = False

        '    If TextNotActivateEditSalesPrice = "YES" Then
        '        Dgv_Add.Columns(3).ReadOnly = True
        '    Else
        '        Dgv_Add.Columns(3).ReadOnly = False
        '    End If
        '    If ColorWithItems = "NO" Then
        '        Dgv_Add.Columns(10).ReadOnly = True
        '    Else
        '        Dgv_Add.Columns(10).Visible = False
        '    End If
        '    If ShowDiscountRateItemSales = "NO" Then
        '        Dgv_Add.Columns(3).Visible = True
        '        Dgv_Add.Columns(20).Visible = False
        '    Else
        '        Dgv_Add.Columns(3).Visible = False
        '        Dgv_Add.Columns(20).Visible = True
        '    End If
        'Catch ex As Exception
        'End Try

        'chckval()
        'StoreSales = False

        ''Catch ex As Exception
        ''    GoTo 1
        ''    'ErrorHandling(ex, Me.Text)
        ''End Try
    End Sub

    Private Sub btnSearchItems_Click(sender As Object, e As EventArgs) Handles btnSearchItems.Click
        PanelSearch.Location = New System.Drawing.Point(5, 200)
        txtsearsh.Focus()
        PanelSearch.Size = New System.Drawing.Size(800, 240)
        txtsearsh_TextChanged(sender, e)
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        PanelSearch.Top = 5000
        PanelSearch.Dock = DockStyle.None
        MaximizeButtons = True
    End Sub

    Private Sub btnMaximizeButtons_Click(sender As Object, e As EventArgs) Handles btnMaximizeButtons.Click
        If MaximizeButtons = True Then
            PanelSearch.Dock = DockStyle.Fill
            MaximizeButtons = False
        Else
            PanelSearch.Dock = DockStyle.None
            MaximizeButtons = True
        End If
        PanelSearch.Location = New System.Drawing.Point(5, 200)
        txtsearsh.Focus()
        PanelSearch.Size = New System.Drawing.Size(800, 240)

    End Sub

    Private Sub txtsearsh_TextChanged(sender As Object, e As EventArgs) Handles txtsearsh.TextChanged
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
                S = Cls.Get_Select_Grid_S("itm_id As [الباركود],sname as [الاسم],SalPrice As [سعر التجزئة],WholePrice As [سعر الجملة],TinPrice As [سعر الشراء],store as [المخزون]", "items", "Stores=N'" & cmbStores.Text & "'  and QuickSearch=0 and sname <> ''")
            Else
                S = Cls.Get_Select_Grid_S("itm_id as [الباركود],sname as [الاسم],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],TinPrice as [سعر الشراء],store as [المخزون]", "items", "Stores=N'" & cmbStores.Text & "'  and QuickSearch=0 and sname Like N'%" & txtsearsh.Text & "%'")
            End If
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)
            DTGV.Columns(0).Width = 130
            DTGV.Columns(1).Width = 400
            DTGV.Columns(2).Width = 100
            DTGV.Columns(3).Width = 100
            DTGV.Columns(4).Width = 100
            DTGV.Columns(5).Width = 100
            DTGV.Columns(3).Visible = False
            DTGV.Columns(4).Visible = False

            Dim HideQuntItems As String = mykey.GetValue("HideQuntItems", "NO")
            If HideQuntItems = "YES" Then
                DTGV.Columns(5).Visible = False
            Else
                DTGV.Columns(5).Visible = True
            End If

            If PermtionName = "مدير" Then
                DTGV.Columns(3).Visible = True
                DTGV.Columns(4).Visible = True
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbFindCats_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbFindCats.SelectedIndexChanged
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If cmbFindCats.Text = "" Then
                S = Cls.Get_Select_Grid_S("itm_id as [الباركود],sname as [الاسم],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],TinPrice as [سعر الشراء],store as [المخزون]", "items", "Stores=N'" & cmbStores.Text & "' and sname <> ''")
            Else
                S = Cls.Get_Select_Grid_S("itm_id as [الباركود],sname as [الاسم],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],TinPrice as [سعر الشراء],store as [المخزون]", "items", "Stores=N'" & cmbStores.Text & "' and group_name =N'" & cmbFindCats.Text & "'")
            End If
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)
            DTGV.Columns(0).Width = 130
            DTGV.Columns(1).Width = 400
            DTGV.Columns(2).Width = 100
            DTGV.Columns(3).Width = 100
            DTGV.Columns(4).Width = 100
            DTGV.Columns(5).Width = 100
            DTGV.Columns(3).Visible = False
            DTGV.Columns(4).Visible = False

            Dim HideQuntItems As String = mykey.GetValue("HideQuntItems", "NO")
            If HideQuntItems = "YES" Then
                DTGV.Columns(5).Visible = False
            Else
                DTGV.Columns(5).Visible = True
            End If

            If PermtionName = "مدير" Then
                DTGV.Columns(3).Visible = True
                DTGV.Columns(4).Visible = True
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub btnAddNewItems_Click(sender As Object, e As EventArgs) Handles btnAddNewItems.Click
        ActionAddNewItems = True
        FrmItemsNew.ShowDialog()
    End Sub

    Private Sub DTGV_DoubleClick(sender As Object, e As EventArgs) Handles DTGV.DoubleClick
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        txtprc.Text = DTGV.SelectedRows(0).Cells(0).Value
        cmbname.Text = DTGV.SelectedRows(0).Cells(1).Value
        GetDataBsal()
        txtsearsh.Text = ""
    End Sub

    Private Sub cmbStores_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbStores.SelectedIndexChanged

    End Sub

    Private Sub GetCustomerAddress()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select addr,tel1 from Customers where Vendorname=N'" & cmbvendores.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                CustomerAddress = dr(0).ToString
                CustomerTel = dr(1).ToString
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetDebtorlCreditor()
        Try
            'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            'cmd.CommandText = "select vnamntcredit , vnamntdebit from Customers where Vendorname=N'" & cmbvendores.Text & "'"
            'dr = cmd.ExecuteReader : dr.Read()
            'If dr.HasRows = True Then
            '    Amntcredit = dr("vnamntcredit")
            '    Amntdebit = dr("vnamntdebit")
            'End If
            If AmntcreditPrevious <> 0 Then
                Dim XCredit As Double = AmntcreditPrevious
                AmntcreditAfter = Val(XCredit) - Val(txtstaying.Text)
            End If

            If AmntdebitPrevious <> 0 Then
                Dim XDebit As Double = AmntdebitPrevious
                AmntdebitAfter = Val(XDebit) + Val(txtstaying.Text)
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetShowDiscountRateItemSales()
        If ShowDiscountRateItemSales = "NO" Then
            txtRateDiscPriceAfter.Visible = False
            txtprice.Visible = True
        Else
            txtRateDiscPriceAfter.Visible = True
            txtprice.Visible = False
        End If
    End Sub
End Class
