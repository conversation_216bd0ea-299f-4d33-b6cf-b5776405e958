﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmBackupRestore
    Inherits System.Windows.Forms.Form

    'Form reemplaza a Dispose para limpiar la lista de componentes.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Requerido por el Diseñador de Windows Forms
    Private components As System.ComponentModel.IContainer

    'NOTA: el Diseñador de Windows Forms necesita el siguiente procedimiento
    'Se puede modificar usando el Diseñador de Windows Forms.  
    'No lo modifique con el editor de código.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.Button1 = New System.Windows.Forms.Button()
        Me.txtPathBackUp = New System.Windows.Forms.TextBox()
        Me.Button2 = New System.Windows.Forms.Button()
        Me.Button3 = New System.Windows.Forms.Button()
        Me.txtPathRestore = New System.Windows.Forms.TextBox()
        Me.Button4 = New System.Windows.Forms.Button()
        Me.ofdRestore = New System.Windows.Forms.OpenFileDialog()
        Me.sfdBackUp = New System.Windows.Forms.SaveFileDialog()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.Button5 = New System.Windows.Forms.Button()
        Me.TabControl1 = New System.Windows.Forms.TabControl()
        Me.TabPage1 = New System.Windows.Forms.TabPage()
        Me.TabPage2 = New System.Windows.Forms.TabPage()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.cmbBackUpTimeFlashMemoryHour = New System.Windows.Forms.ComboBox()
        Me.cmbBackUpTimeFlashMemory = New System.Windows.Forms.ComboBox()
        Me.txtPathBackUpFlashMemory = New System.Windows.Forms.TextBox()
        Me.Button7 = New System.Windows.Forms.Button()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.sfdBackUpFlashMemory = New System.Windows.Forms.SaveFileDialog()
        Me.Button6 = New System.Windows.Forms.Button()
        Me.Button8 = New System.Windows.Forms.Button()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabControl1.SuspendLayout()
        Me.TabPage1.SuspendLayout()
        Me.TabPage2.SuspendLayout()
        Me.SuspendLayout()
        '
        'Button1
        '
        Me.Button1.Location = New System.Drawing.Point(412, 107)
        Me.Button1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(184, 28)
        Me.Button1.TabIndex = 0
        Me.Button1.Text = "نسخة إحتياطية الجهاز bak"
        Me.Button1.UseVisualStyleBackColor = True
        '
        'txtPathBackUp
        '
        Me.txtPathBackUp.Location = New System.Drawing.Point(167, 54)
        Me.txtPathBackUp.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtPathBackUp.Name = "txtPathBackUp"
        Me.txtPathBackUp.Size = New System.Drawing.Size(427, 24)
        Me.txtPathBackUp.TabIndex = 1
        '
        'Button2
        '
        Me.Button2.Location = New System.Drawing.Point(34, 52)
        Me.Button2.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button2.Name = "Button2"
        Me.Button2.Size = New System.Drawing.Size(126, 28)
        Me.Button2.TabIndex = 2
        Me.Button2.Text = "مسار الملف"
        Me.Button2.UseVisualStyleBackColor = True
        '
        'Button3
        '
        Me.Button3.Location = New System.Drawing.Point(471, 294)
        Me.Button3.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button3.Name = "Button3"
        Me.Button3.Size = New System.Drawing.Size(126, 28)
        Me.Button3.TabIndex = 5
        Me.Button3.Text = "مسار الملف"
        Me.Button3.UseVisualStyleBackColor = True
        '
        'txtPathRestore
        '
        Me.txtPathRestore.Location = New System.Drawing.Point(604, 296)
        Me.txtPathRestore.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtPathRestore.Name = "txtPathRestore"
        Me.txtPathRestore.Size = New System.Drawing.Size(427, 24)
        Me.txtPathRestore.TabIndex = 4
        '
        'Button4
        '
        Me.Button4.Location = New System.Drawing.Point(916, 342)
        Me.Button4.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button4.Name = "Button4"
        Me.Button4.Size = New System.Drawing.Size(117, 28)
        Me.Button4.TabIndex = 3
        Me.Button4.Text = "أستعادة البيانات"
        Me.Button4.UseVisualStyleBackColor = True
        '
        'ofdRestore
        '
        Me.ofdRestore.FileName = "OpenFileDialog1"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.ForeColor = System.Drawing.SystemColors.AppWorkspace
        Me.Label1.Location = New System.Drawing.Point(410, 28)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(178, 17)
        Me.Label1.TabIndex = 6
        Me.Label1.Text = "حفظ نسخة من قاعدة البيانات"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.ForeColor = System.Drawing.SystemColors.AppWorkspace
        Me.Label3.Location = New System.Drawing.Point(846, 272)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(199, 17)
        Me.Label3.TabIndex = 8
        Me.Label3.Text = "إستعادة نسخة من قاعدة البيانات"
        '
        'PictureBox1
        '
        Me.PictureBox1.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.BackUpDatat
        Me.PictureBox1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.PictureBox1.Location = New System.Drawing.Point(44, 74)
        Me.PictureBox1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(342, 294)
        Me.PictureBox1.TabIndex = 9
        Me.PictureBox1.TabStop = False
        '
        'Panel1
        '
        Me.Panel1.BackColor = System.Drawing.Color.Gainsboro
        Me.Panel1.Location = New System.Drawing.Point(409, 232)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(658, 2)
        Me.Panel1.TabIndex = 10
        '
        'Button5
        '
        Me.Button5.Location = New System.Drawing.Point(365, 140)
        Me.Button5.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button5.Name = "Button5"
        Me.Button5.Size = New System.Drawing.Size(234, 28)
        Me.Button5.TabIndex = 11
        Me.Button5.Text = "نسخة إحتياطية على الفلاشة bak"
        Me.Button5.UseVisualStyleBackColor = True
        '
        'TabControl1
        '
        Me.TabControl1.Controls.Add(Me.TabPage1)
        Me.TabControl1.Controls.Add(Me.TabPage2)
        Me.TabControl1.Location = New System.Drawing.Point(409, 10)
        Me.TabControl1.Name = "TabControl1"
        Me.TabControl1.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.TabControl1.RightToLeftLayout = True
        Me.TabControl1.SelectedIndex = 0
        Me.TabControl1.Size = New System.Drawing.Size(648, 215)
        Me.TabControl1.TabIndex = 12
        '
        'TabPage1
        '
        Me.TabPage1.Controls.Add(Me.Button6)
        Me.TabPage1.Controls.Add(Me.txtPathBackUp)
        Me.TabPage1.Controls.Add(Me.Button1)
        Me.TabPage1.Controls.Add(Me.Button2)
        Me.TabPage1.Controls.Add(Me.Label1)
        Me.TabPage1.Location = New System.Drawing.Point(4, 25)
        Me.TabPage1.Name = "TabPage1"
        Me.TabPage1.Padding = New System.Windows.Forms.Padding(3)
        Me.TabPage1.Size = New System.Drawing.Size(640, 186)
        Me.TabPage1.TabIndex = 0
        Me.TabPage1.Text = "نسخة أحتياطية على الجهاز"
        Me.TabPage1.UseVisualStyleBackColor = True
        '
        'TabPage2
        '
        Me.TabPage2.Controls.Add(Me.Button8)
        Me.TabPage2.Controls.Add(Me.Label4)
        Me.TabPage2.Controls.Add(Me.Label5)
        Me.TabPage2.Controls.Add(Me.cmbBackUpTimeFlashMemoryHour)
        Me.TabPage2.Controls.Add(Me.cmbBackUpTimeFlashMemory)
        Me.TabPage2.Controls.Add(Me.txtPathBackUpFlashMemory)
        Me.TabPage2.Controls.Add(Me.Button5)
        Me.TabPage2.Controls.Add(Me.Button7)
        Me.TabPage2.Controls.Add(Me.Label2)
        Me.TabPage2.Location = New System.Drawing.Point(4, 25)
        Me.TabPage2.Name = "TabPage2"
        Me.TabPage2.Padding = New System.Windows.Forms.Padding(3)
        Me.TabPage2.Size = New System.Drawing.Size(640, 186)
        Me.TabPage2.TabIndex = 1
        Me.TabPage2.Text = "نسخة أحتياطية على الفلاشة"
        Me.TabPage2.UseVisualStyleBackColor = True
        '
        'Label4
        '
        Me.Label4.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.Label4.Location = New System.Drawing.Point(87, 106)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(41, 17)
        Me.Label4.TabIndex = 547
        Me.Label4.Text = "دقيقة"
        '
        'Label5
        '
        Me.Label5.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Tahoma", 8.0!)
        Me.Label5.Location = New System.Drawing.Point(223, 106)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(161, 17)
        Me.Label5.TabIndex = 550
        Me.Label5.Text = "انشاء نسخة احتياطيه   كل"
        '
        'cmbBackUpTimeFlashMemoryHour
        '
        Me.cmbBackUpTimeFlashMemoryHour.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbBackUpTimeFlashMemoryHour.FormattingEnabled = True
        Me.cmbBackUpTimeFlashMemoryHour.Items.AddRange(New Object() {"1", "2", "5", "10", "15", "60", "90", "120"})
        Me.cmbBackUpTimeFlashMemoryHour.Location = New System.Drawing.Point(131, 102)
        Me.cmbBackUpTimeFlashMemoryHour.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbBackUpTimeFlashMemoryHour.Name = "cmbBackUpTimeFlashMemoryHour"
        Me.cmbBackUpTimeFlashMemoryHour.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.cmbBackUpTimeFlashMemoryHour.Size = New System.Drawing.Size(83, 24)
        Me.cmbBackUpTimeFlashMemoryHour.TabIndex = 549
        '
        'cmbBackUpTimeFlashMemory
        '
        Me.cmbBackUpTimeFlashMemory.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbBackUpTimeFlashMemory.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.cmbBackUpTimeFlashMemory.Font = New System.Drawing.Font("JF Flat", 7.2!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbBackUpTimeFlashMemory.FormattingEnabled = True
        Me.cmbBackUpTimeFlashMemory.Items.AddRange(New Object() {"YES", "NO"})
        Me.cmbBackUpTimeFlashMemory.Location = New System.Drawing.Point(15, 100)
        Me.cmbBackUpTimeFlashMemory.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbBackUpTimeFlashMemory.Name = "cmbBackUpTimeFlashMemory"
        Me.cmbBackUpTimeFlashMemory.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.cmbBackUpTimeFlashMemory.Size = New System.Drawing.Size(66, 29)
        Me.cmbBackUpTimeFlashMemory.TabIndex = 548
        '
        'txtPathBackUpFlashMemory
        '
        Me.txtPathBackUpFlashMemory.Location = New System.Drawing.Point(172, 53)
        Me.txtPathBackUpFlashMemory.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtPathBackUpFlashMemory.Name = "txtPathBackUpFlashMemory"
        Me.txtPathBackUpFlashMemory.Size = New System.Drawing.Size(427, 24)
        Me.txtPathBackUpFlashMemory.TabIndex = 8
        '
        'Button7
        '
        Me.Button7.Location = New System.Drawing.Point(15, 51)
        Me.Button7.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button7.Name = "Button7"
        Me.Button7.Size = New System.Drawing.Size(126, 28)
        Me.Button7.TabIndex = 9
        Me.Button7.Text = "مسار الملف"
        Me.Button7.UseVisualStyleBackColor = True
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.ForeColor = System.Drawing.SystemColors.AppWorkspace
        Me.Label2.Location = New System.Drawing.Point(415, 27)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(178, 17)
        Me.Label2.TabIndex = 10
        Me.Label2.Text = "حفظ نسخة من قاعدة البيانات"
        '
        'Button6
        '
        Me.Button6.Location = New System.Drawing.Point(222, 107)
        Me.Button6.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button6.Name = "Button6"
        Me.Button6.Size = New System.Drawing.Size(184, 28)
        Me.Button6.TabIndex = 7
        Me.Button6.Text = "نسخة إحتياطية الجهاز rar"
        Me.Button6.UseVisualStyleBackColor = True
        Me.Button6.Visible = False
        '
        'Button8
        '
        Me.Button8.Location = New System.Drawing.Point(125, 140)
        Me.Button8.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button8.Name = "Button8"
        Me.Button8.Size = New System.Drawing.Size(234, 28)
        Me.Button8.TabIndex = 551
        Me.Button8.Text = "نسخة إحتياطية على الفلاشة rar"
        Me.Button8.UseVisualStyleBackColor = True
        '
        'frmBackupRestore
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.White
        Me.ClientSize = New System.Drawing.Size(1071, 469)
        Me.Controls.Add(Me.TabControl1)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.PictureBox1)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.Button3)
        Me.Controls.Add(Me.txtPathRestore)
        Me.Controls.Add(Me.Button4)
        Me.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.MaximizeBox = False
        Me.Name = "frmBackupRestore"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "حفظ واسترجاع نسخة من قاعدة البيانات"
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabControl1.ResumeLayout(False)
        Me.TabPage1.ResumeLayout(False)
        Me.TabPage1.PerformLayout()
        Me.TabPage2.ResumeLayout(False)
        Me.TabPage2.PerformLayout()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents Button1 As System.Windows.Forms.Button
    Friend WithEvents txtPathBackUp As System.Windows.Forms.TextBox
    Friend WithEvents Button2 As System.Windows.Forms.Button
    Friend WithEvents Button3 As System.Windows.Forms.Button
    Friend WithEvents txtPathRestore As System.Windows.Forms.TextBox
    Friend WithEvents Button4 As System.Windows.Forms.Button
    Friend WithEvents ofdRestore As System.Windows.Forms.OpenFileDialog
    Friend WithEvents sfdBackUp As System.Windows.Forms.SaveFileDialog
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents Button5 As Button
    Friend WithEvents TabControl1 As TabControl
    Friend WithEvents TabPage1 As TabPage
    Friend WithEvents TabPage2 As TabPage
    Friend WithEvents txtPathBackUpFlashMemory As TextBox
    Friend WithEvents Button7 As Button
    Friend WithEvents Label2 As Label
    Friend WithEvents sfdBackUpFlashMemory As SaveFileDialog
    Friend WithEvents Label4 As Label
    Friend WithEvents Label5 As Label
    Friend WithEvents cmbBackUpTimeFlashMemoryHour As ComboBox
    Friend WithEvents cmbBackUpTimeFlashMemory As ComboBox
    Friend WithEvents Button6 As Button
    Friend WithEvents Button8 As Button
End Class
