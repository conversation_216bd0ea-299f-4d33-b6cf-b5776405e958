﻿Imports System.Data.SqlClient
Imports System.IO
Imports CrystalDecisions.CrystalReports.Engine

Public Class FrmSalesTouch
    Dim Dt_AddBill As New DataTable
    Dim ItemsButtonAction As Boolean = False
    Dim aray_itm_id As New ArrayList
    Dim aray_sname As New ArrayList
    Dim aray_Stores As New ArrayList
    Dim aray_itm_Unity As New ArrayList
    Dim aray_qu As New ArrayList
    Dim aray_qu_unity As New ArrayList
    Dim aray_TinPrice As New ArrayList
    Dim aray_ColorButton As New ArrayList
    Dim aray_SalPrice As New ArrayList
    Dim prc, items, cats, price, WholeWholePrice, RateDiscPriceAfter, Stores, Unity As String
    Dim BalanceBarcode As String
    Dim BarcodeQuantityStar As String = ""
    Dim UseOnlySales As String = mykey.GetValue("UseOnlySales", "NO")
    Dim TextNotActivateEditSalesPrice As String = mykey.GetValue("TextNotActivateEditSalesPrice", "NO")
    Dim ColorWithItems As String = mykey.GetValue("UseColorWithItems", "NO")
    Dim FromArgb1 As String = ""
    Dim FromArgb2 As String = ""
    Dim FromArgb3 As String = ""
    Dim DataGridViewWidth As Boolean = False
    Dim Key1 As Integer
    Dim sign_Indicator As Integer = 0
    Dim QuntText As String = ""
    Dim RNXD As Integer
    Dim Treasury_Code_ID As Integer = 0
    Dim TimeAmPm As String
    Dim TimeAmPm2 As String
    Dim ValidateTextSaveTrue As Boolean = False
    Dim Sales_Bill As String = ""
    Dim BillsalData As String = ""
    Dim vst As String = ""
    Dim Vst_disc As String = ""
    Dim RateValues As String
    Dim DiscTotal As Double
    Dim StatusOrder As String = ""
    Dim DiscountsTin As Double = 0
    Dim Vendorname As String = ""
    Dim paying As Double = 0
    Dim staying As Double = 0
    Dim Amntcredit, Amntdebit, AmntcreditPrevious, AmntdebitPrevious, AmountDebitCreditPrevious, AmountDebitCreditAfter As Double
    Dim StateDisc As String = ""
    Dim Dates As String
    Dim BillSerialNumber As Double
    Dim ButtonAction As Boolean = False
    Dim ComboboxAction As Boolean = False
    Dim first7Chars, str, str3, XPrc As String
    Dim StoreSales As Boolean = False
    Dim ParcodeUnity As String = ""
    Dim UserPermtionsAction As Boolean = False
    Dim ActivatePaidAndRest As Boolean = False
    Dim ActivatePrintChackEnd As Boolean = False
    Dim Cls_Altfiqith As New Class_Altfiqith
    Dim OrderDelivery As Boolean = False
    Dim CustomerAddress As String = ""
    Dim CustomerTel As String = ""
    Dim CustomerMobile As String = ""
    Dim PhoneEmployee As String = ""
    Dim SalesBillNotDiscountBill As String = mykey.GetValue("SalesBillNotDiscountBill", "NO")
    Dim SalesBillNotDiscount As String = mykey.GetValue("SalesBillNotDiscount", "NO")
    Dim ShowCustomerBalanceSalesScreen As String = mykey.GetValue("ShowCustomerBalanceSalesScreen", "NO")
    Dim SalesPricePublic As String = mykey.GetValue("SalesPricePublic", "NO")


    Private Sub FrmSalesTouch_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployees)

        FillGroup()

        MAXRECORD()
        FillGroup()

        SelectUserPermtions()

        GridViewOpenForm()

        Cls.fill_combo_Branch("Treasury", "Treasury_Name", cmbTreasuryName)

        Treasury_Code = Cls.Get_Code_Value("Users", "Treasury_Code", "UserName", UserName)
        cmbTreasuryName.Text = Cls.Get_Code_Value("Treasury", "Treasury_Name", "Treasury_Code", Treasury_Code)
        txtTreasuryName.Text = cmbTreasuryName.Text

        If TreasuryControl = "YES" Then
            lblTreasuryName.Visible = True
            cmbTreasuryName.Visible = True
        Else
            lblTreasuryName.Visible = False
            cmbTreasuryName.Visible = False
        End If


        Cls.fill_combo("Items", "sname", cmbname)

        cmbTreasuryName.Text = Cls.Get_Code_Value_Branch_More("Treasury", "Treasury_Name", "Treasury_Code=N'" & Treasury_Code & "'")
        Treasury_Code_ID = Cls.Get_Code_Value_Branch_More("Treasury", "Treasury_Code", "Treasury_Name=N'" & cmbTreasuryName.Text & "'")

        Cls.fill_combo_Branch("stores", "store", cmbStores)
        cmbStores.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        'Store_Name()
        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", cmbvendores)

        End If
        cmbvendores.Items.Add("نقداً")
        cmbvendores.Text = "نقداً"

        PanelSelectPrinter.Top = 5000
        PanelPayingStaying.Top = 5000

        GetFocusText()

        Check_Sheft_Status()


        ActivatePaidAndRestAll = mykey.GetValue("ActivatePaidAndRestAll", "NO")
        If ActivatePaidAndRestAll = "YES" Then
            chkActivatePaidAndRest.Checked = True
        Else
            chkActivatePaidAndRest.Checked = False
        End If

        lblUserName.Text = UserName
    End Sub

    Private Sub FillGroup()
        If NetworkName = "Yes" Then : If UseExternalServer = "Yes" Then : connect() : End If : End If
        connect()
        Dim FromArgb As String = ""
        FlowLay_Group.Controls.Clear()

        Dim aray_g_name As New ArrayList
        Dim For_group_name As String = ""
        aray_g_name.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select distinct g_name From groups where QuickSearch =0 order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_g_name.Add(dr(0))
        Loop



        For i As Integer = 0 To aray_g_name.Count - 1
            Dim btn As New Button
            AddHandler btn.Click, AddressOf ClickMe
            Dim Panel2 As New Panel
            Dim Panel3 As New Label

            For_group_name = aray_g_name(i).ToString


            btn.Width = 220
            btn.Height = 62
            btn.FlatStyle = FlatStyle.Flat
            btn.Text = Trim(For_group_name)
            btn.Visible = True
            btn.Tag = Trim(For_group_name)
            'SplitFromArgb(FromArgb)
            'btn.BackColor = Color.FromArgb(FromArgb1, FromArgb2, FromArgb3)
            btn.BackColor = Color.FromArgb(246, 244, 239)
            btn.ForeColor = Color.Black
            btn.TextAlign = System.Drawing.ContentAlignment.MiddleRight
            btn.Font = New System.Drawing.Font("JF Flat", 10.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            'set_color_button(btn, FlowLay_Items)

            '========= Image Items ================================
            Try
                connect()
                connectionStringOpen()
                Dim sql As String
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                sql = "SELECT Groups_Image FROM Groups WHERE G_name =N'" & For_group_name & "'"
                Dim cmd As SqlClient.SqlCommand = New SqlClient.SqlCommand(sql, Cn)
                Dim by() As Byte
                by = cmd.ExecuteScalar()
                If (by.Length > 0) Then
                    Dim stream As New MemoryStream(by, True)
                    stream.Write(by, 0, by.Length)
                    btn.BackgroundImage = New Bitmap(stream)
                    stream.Close()
                Else
                    btn.BackgroundImage = Nothing
                End If
            Catch ex As Exception
            End Try
            '========= Image Items ================================
            btn.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
            FlowLay_Group.Controls.Add(btn)

        Next
        GetFocusText()

    End Sub

    Private Sub ClickMe(ByVal sender As Object, ByVal e As EventArgs)
        Dim btn As Button
        btn = CType(sender, Button)
        Dim str As String = btn.Tag
        mykey.SetValue("ButtonGroupTag", str)
        FlowLay_Items.Controls.Clear()

        aray_itm_id.Clear() : aray_sname.Clear() : aray_ColorButton.Clear() : aray_SalPrice.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id,sname,SalPrice from Items where group_name =N'" & str & "' and QuickSearch=0  order by id"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_itm_id.Add(dr(0))
            aray_sname.Add(dr(1))
            aray_SalPrice.Add(dr(2))
        Loop

        Dim ForItem_id As String : Dim Forsname As String : Dim ForSalPrice As String
        For i As Integer = 0 To aray_itm_id.Count - 1
            ForItem_id = aray_itm_id(i).ToString
            Forsname = aray_sname(i).ToString
            ForSalPrice = aray_SalPrice(i).ToString



            Dim Panel2 As New Panel
            Dim lblStore2 As New Label
            Dim lblPrice2 As New Label
            Dim lblItems2 As New Label
            Dim btn2 As New Button
            AddHandler btn2.Click, AddressOf ClickMeButton

            Panel2.SuspendLayout()
            'Panel2
            Panel2.BackColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
            Panel2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
            Panel2.Controls.Add(lblItems2)
            Panel2.Controls.Add(lblPrice2)
            Panel2.Controls.Add(btn2)
            Panel2.Location = New System.Drawing.Point(3, 3)
            Panel2.Name = "PanelHome"
            Panel2.Size = New System.Drawing.Size(165, 151)
            Panel2.TabIndex = 428
            'btnItems
            btn2.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
            btn2.Dock = System.Windows.Forms.DockStyle.Top
            btn2.FlatStyle = System.Windows.Forms.FlatStyle.Flat
            btn2.Font = New System.Drawing.Font("JF Flat", 10.2!, System.Drawing.FontStyle.Bold)
            btn2.ForeColor = Color.White
            btn2.Size = New System.Drawing.Size(167, 97)
            btn2.UseVisualStyleBackColor = False
            'btn2.Text = Trim(Forsname)
            btn2.Visible = True
            btn2.Tag = Trim(ForItem_id)
            'FromArgb = Trim(ForColorButton)
            'SplitFromArgb(FromArgb)
            'If FromArgb1 = "" Then
            btn2.BackColor = Color.FromArgb(255, 255, 255)
            'Else
            '    btn2.BackColor = Color.FromArgb(FromArgb1, FromArgb2, FromArgb3)
            'End If
            'lblPrice
            lblPrice2.AutoSize = True
            lblPrice2.Font = New System.Drawing.Font("JF Flat", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            lblPrice2.ForeColor = System.Drawing.Color.White
            lblPrice2.Location = New System.Drawing.Point(1, 0)
            lblPrice2.Size = New System.Drawing.Size(33, 30)
            lblPrice2.TabIndex = 235
            lblPrice2.Text = ForSalPrice + " $"
            'lblItems
            lblItems2.Font = New System.Drawing.Font("JF Flat", 10.2!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            lblItems2.ForeColor = System.Drawing.Color.Black
            lblItems2.Location = New System.Drawing.Point(0, 99)
            lblItems2.Name = Trim(Forsname)
            lblItems2.Size = New System.Drawing.Size(168, 51)
            lblItems2.TabIndex = 236
            lblItems2.Text = Trim(Forsname)
            lblItems2.Anchor = System.Windows.Forms.AnchorStyles.Bottom
            lblItems2.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
            lblItems2.BackColor = Color.FromArgb(246, 244, 239)

            '========= Image Items ================================
            Try
                connect()
                Dim sql As String
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                sql = "SELECT Items_Images FROM Items WHERE itm_id =N'" & ForItem_id & "'"
                Dim cmd As SqlClient.SqlCommand = New SqlClient.SqlCommand(sql, Cn)
                Dim by() As Byte
                by = cmd.ExecuteScalar()
                If (by.Length > 0) Then
                    Dim stream As New MemoryStream(by, True)
                    stream.Write(by, 0, by.Length)
                    btn2.BackgroundImage = New Bitmap(stream)
                    stream.Close()
                Else
                    btn2.BackgroundImage = Nothing
                End If
            Catch ex As Exception
            End Try
            '========= Image Items ================================
            btn2.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
            FlowLay_Items.Controls.Add(Panel2)
        Next
        GetFocusText()
    End Sub

    Private Sub ClickMeButton(ByVal sender As Object, ByVal e As EventArgs)
        ButtonAction = True
        Dim btn As Button
        btn = CType(sender, Button)

        Dim parcode As String = btn.Tag
        txtprc.Text = parcode

        items = Cls.Get_Code_Value_Branch_More("Items", "sname", "itm_id=N'" & parcode & "'")

        If QuntText = "" Or QuntText = "0" Then
            QuntText = 1
        End If

        GetDataSalesButton("Items")

        GetAddItemsGrid()

        ItemsButtonAction = False
        QuntText = 0
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub

    Private Sub btnMinimized_Click(sender As Object, e As EventArgs) Handles btnMinimized.Click
        Me.WindowState = FormWindowState.Minimized

    End Sub

    Private Sub GetDataParcode()
        Try
            'BackItems:
            If NetworkName = "Yes" Then : If UseExternalServer = "Yes" Then : connect() : End If : End If

            first7Chars = txtprc.Text

            BalanceBarcode = "0"
            ItemsUnityNumber = 0
            Try
                first7Chars = txtprc.Text.Substring(0, 7)
            Catch ex As Exception
            End Try

            ItemsUnityNumber = 0
            Bol = True

            Try
                BalanceBarcode = Cls.Get_Code_Value_More("items", "BalanceBarcode", "itm_id =N'" & first7Chars & "' and Stores =N'" & cmbStores.Text & "'")

                If BalanceBarcode <> "1" Then
                    If BeforeScanningBarcodeQuantityStar = "YES" Then
                        Dim PRC As String = txtprc.Text
                        Dim split As String() = New String() {"*"}
                        Dim itemsSplit As String() = PRC.Split(split, StringSplitOptions.None)
                        BarcodeQuantityStar = itemsSplit(0).ToString()
                        Dim XPrc As String = itemsSplit(1).ToString()
                        first7Chars = XPrc
                        txtprc.Text = XPrc
                        QuntText = BarcodeQuantityStar
                    End If
                End If

            Catch ex As Exception
                BarcodeQuantityStar = ""
            End Try
            'Try
            ParcodeMore = ""
            If BalanceBarcode = "0" Then
                GetBarcodeMore(txtprc.Text) : If ParcodeMore <> "" Then : If ParcodeMore <> 0 Then : txtprc.Text = ParcodeMore : first7Chars = txtprc.Text : End If : End If
                If ParcodeMore = "0" Then
                    Dim PrcUnity As String = Cls.Get_Code_Value("ItemsUnity", "itm_id", "itm_id_Unity", ParcodeUnity)
                    If PrcUnity <> "0" Then
                        txtprc.Text = PrcUnity
                        first7Chars = txtprc.Text
                    End If
                End If
            End If

            Stores = mykey.GetValue("StoresName", "المخزن الرئيسى")
            'Stores = cmbStores.Text

            '==================== Balance Barcode =================================
            If BalanceBarcode = "1" Then
                    Dim PRC As String = txtprc.Text
                    Dim lastName As String = PRC.Substring(PRC.IndexOf("") + 7)
                    str3 = lastName.Trim().Remove(lastName.Length - 1)
                    Dim first7Chars As String = PRC.Trim().Remove(PRC.Length - 6)
                    txtprc.Text = first7Chars
                    ParcodeMore = txtprc.Text
                    Cls.Select_More_Data_Stores("items", "itm_id,sname,group_name,SalPrice,WholeWholePrice,RateVAT,RateDiscSalPriceAfter,Unity", "itm_id=N'" & first7Chars & "' and Stores =N'" & Stores & "' and QuickSearch=0")

                    GoTo Barcode1
                End If

                Dim HasRowsActive As Boolean = False

                '==================== Balance Barcode =================================
                Cls.Select_More_Data_Stores("items", "itm_id,sname,group_name,SalPrice,WholeWholePrice,RateVAT,Stores,RateDiscSalPriceAfter,Unity", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "' and QuickSearch=0")

Barcode1:
                If dr.HasRows = True Then
                    prc = dr("itm_id").ToString
                    items = dr("sname").ToString
                    cats = dr("group_name").ToString
                    price = dr("SalPrice").ToString
                    WholeWholePrice = dr("WholeWholePrice").ToString
                    ItemsRateVAT = Val(dr("RateVAT").ToString)
                    RateDiscPriceAfter = Val(dr("RateDiscSalPriceAfter").ToString)
                    Unity = Val(dr("Unity").ToString)
                    HasRowsActive = True
                Else
                Cls.Select_More_Data_Stores("items", "itm_id,sname,group_name,SalPrice,WholeWholePrice,RateVAT,Stores,RateDiscSalPriceAfter,Unity", "itm_id=N'" & first7Chars & "' and QuickSearch=0")
                If dr.HasRows = True Then
                    prc = dr("itm_id").ToString
                    items = dr("sname").ToString
                    cats = dr("group_name").ToString
                    price = dr("SalPrice").ToString
                    WholeWholePrice = dr("WholeWholePrice").ToString
                    ItemsRateVAT = Val(dr("RateVAT").ToString)
                    Stores = dr("Stores").ToString
                    RateDiscPriceAfter = Val(dr("RateDiscSalPriceAfter").ToString)
                    Unity = Val(dr("Unity").ToString)
                    HasRowsActive = True
                End If
            End If
            If HasRowsActive = False Then
                'MsgBox("هذا الباركود غير متوافق مع المخزن المحدد او لم يتم تسجيلة من قبل", MsgBoxStyle.Exclamation)
                Exit Sub
            End If

            Dim QuX As Double = 0
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                If Dgv_Add.Rows(i).Cells(0).Value = prc Then
                    QuX += Val(Dgv_Add.Rows(i).Cells(4).Value.ToString())
                End If
            Next

            'If UseBalanceBarcode = "YES" Then
            If BalanceBarcode = "1" Then
                    If str3.Length = 2 Then
                        QuntText = "0.0" & str3
                        QuntText = QuntText
                    End If
                    If str3.Length = 3 Then
                        QuntText = "0." & str3
                    End If
                    If str3.Length = 4 Then
                        Dim newNumber As String
                        Dim firstChar As Char
                        firstChar = str3.Chars(0)
                        newNumber = str3.Remove(0, 1)
                        QuntText = firstChar & "." & newNumber
                    End If
                If str3.Length = 5 Then
                    Dim newNumber As String
                    Dim firstChar As String
                    firstChar = str3.Substring(0, 2)
                    newNumber = str3.Remove(0, 2)
                    QuntText = firstChar & "." & newNumber
                End If
                If QuX <> 0 Then
                    Dim QuTotal As Double = QuntText + QuX
                    QuntText = QuTotal
                    For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                        If Dgv_Add.Rows(i).Cells(0).Value = prc Then
                            Dgv_Add.Rows(i).Cells(4).Value = QuntText
                            Dgv_Add.Rows(i).Cells(5).Value = QuntText
                        End If
                    Next
                End If
                txtprc.Text = first7Chars

                    TotalPrice = Val(price) * Val(QuntText)
                    'txtprice.Text = TotalPrice
                    QuntText = QuntText
                Else
                If BarcodeQuantityStar = "" Then
                    Try
                        If QuntText.ToString() = "" Or QuntText = 0 Then
                            QuntText = 1
                        End If
                    Catch ex As Exception
                        QuntText = 1
                    End Try
                End If
            End If
                'End If
                '==================== Balance Barcode =================================


                'Catch ex As Exception
                '    'If price = "" Then
                '    '    GoTo BackItems
                '    'End If
                'End Try

                SumAllPrice()

            GetFocusText()
            If NetworkName = "Yes" Then
                If UseExternalServer = "Yes" Then
                    connect()
                End If
            End If

            Bol = False
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

    End Sub
    Private Sub GetDataSales(ByVal StatusPrcName As String)
        Try
BackItems:
            If NetworkName = "Yes" Then : If UseExternalServer = "Yes" Then : connect() : End If : End If
            ItemsUnityNumber = 0
                Bol = True
            Try
                ParcodeMore = ""
                GetBarcodeMore(txtprc.Text) : If ParcodeMore <> "" Then : If ParcodeMore <> 0 Then : txtprc.Text = ParcodeMore : first7Chars = txtprc.Text : End If : End If
                If ParcodeMore = "0" Then
                    Dim PrcUnity As String = Cls.Get_Code_Value("ItemsUnity", "itm_id", "itm_id_Unity", ParcodeUnity)
                    If PrcUnity <> "0" Then
                        txtprc.Text = PrcUnity
                        first7Chars = txtprc.Text
                    End If
                End If

                Stores = mykey.GetValue("StoresName", "المخزن الرئيسى")

                If StatusPrcName = "Parcode" Then
                    Cls.Select_More_Data_Stores("items", "itm_id,sname,group_name,SalPrice,WholeWholePrice,RateVAT,RateDiscSalPriceAfter,Unity", "itm_id=N'" & txtprc.Text & "' and Stores =N'" & Stores & "' and QuickSearch=0")
                End If
                If StatusPrcName = "Items" Then
                    Cls.Select_More_Data_Stores("items", "itm_id,sname,group_name,SalPrice,WholeWholePrice,RateVAT,RateDiscSalPriceAfter,Unity", "sname=N'" & items & "' and Stores =N'" & Stores & "' and QuickSearch=0")
                End If
                If dr.HasRows = True Then
                    prc = dr("itm_id").ToString
                    items = dr("sname").ToString
                    cats = dr("group_name").ToString
                    price = dr("SalPrice").ToString
                    WholeWholePrice = dr("WholeWholePrice").ToString
                    ItemsRateVAT = Val(dr("RateVAT").ToString)
                    RateDiscPriceAfter = Val(dr("RateDiscSalPriceAfter").ToString)
                    Unity = Val(dr("Unity").ToString)
                Else
                    If StatusPrcName = "Parcode" Then
                        Cls.Select_More_Data_Stores("items", "itm_id,sname,group_name,SalPrice,WholeWholePrice,RateVAT,Stores,RateDiscSalPriceAfter,Unity", "itm_id=N'" & prc & "' and QuickSearch=0")
                    End If
                    If StatusPrcName = "Items" Then
                        Cls.Select_More_Data_Stores("items", "itm_id,sname,sname,group_name,SalPrice,WholeWholePrice,RateVAT,RateDiscSalPriceAfter,Unity", "sname=N'" & items & "' and QuickSearch=0")
                    End If
                    If dr.HasRows = True Then
                        prc = dr("itm_id").ToString
                        items = dr("sname").ToString
                        cats = dr("group_name").ToString
                        price = dr("SalPrice").ToString
                        WholeWholePrice = dr("WholeWholePrice").ToString
                        ItemsRateVAT = Val(dr("RateVAT").ToString)
                        Stores = dr("Stores").ToString
                        RateDiscPriceAfter = Val(dr("RateDiscSalPriceAfter").ToString)
                        Unity = Val(dr("Unity").ToString)
                    End If
                End If

            Catch ex As Exception
                If price = "" Then
                        GoTo BackItems
                    End If
                End Try

            'Dim QuX As Double = 0
            'For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            '    If Dgv_Add.Rows(i).Cells(0).Value = prc Then
            '        QuX += Val(Dgv_Add.Rows(i).Cells(4).Value.ToString())
            '    End If
            'Next

            'If QuX <> 0 Then
            '    Dim QuTotal As Double = QuntText + QuX
            '    QuntText = QuTotal
            '    For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            '        If Dgv_Add.Rows(i).Cells(0).Value = prc Then
            '            Dgv_Add.Rows(i).Cells(4).Value = QuntText
            '            Dgv_Add.Rows(i).Cells(5).Value = QuntText
            '        End If
            '    Next
            'End If



            'If UseBalanceBarcode = "YES" Then
            'If BalanceBarcode = "1" Then
            '    Dim QuX As Double = 0
            '    For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            '        If Dgv_Add.Rows(i).Cells(0).Value = prc Then
            '            QuX += Val(Dgv_Add.Rows(i).Cells(4).Value.ToString())
            '        End If
            '    Next

            '    If str3.Length = 2 Then
            '        QuntText = "0.0" & str3
            '        QuntText = QuntText
            '    End If
            '    If str3.Length = 3 Then
            '        QuntText = "0." & str3
            '    End If
            '    If str3.Length = 4 Then
            '        Dim newNumber As String
            '        Dim firstChar As Char
            '        firstChar = str3.Chars(0)
            '        newNumber = str3.Remove(0, 1)
            '        QuntText = firstChar & "." & newNumber
            '    End If
            '    If str3.Length = 5 Then
            '        Dim newNumber As String
            '        Dim firstChar As String
            '        firstChar = str3.Substring(0, 2)
            '        newNumber = str3.Remove(0, 2)
            '        QuntText = firstChar & "." & newNumber
            '    End If
            '    If QuX <> 0 Then
            '        Dim QuTotal As Double = QuntText + QuX
            '        QuntText = QuTotal
            '        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            '            If Dgv_Add.Rows(i).Cells(0).Value = prc Then
            '                Dgv_Add.Rows(i).Cells(4).Value = QuntText
            '                Dgv_Add.Rows(i).Cells(5).Value = QuntText
            '            End If
            '        Next
            '    End If
            '    txtprc.Text = first7Chars

            '    TotalPrice = Val(price) * Val(QuntText)
            '    'txtprice.Text = TotalPrice
            '    QuntText = QuntText
            'Else
            '    If BarcodeQuantityStar = "" Then
            '        QuntText = 1
            '    End If
            'End If
            'End If
            '==================== Balance Barcode =================================


            SumAllPrice()

                GetFocusText()
                If NetworkName = "Yes" Then
                    If UseExternalServer = "Yes" Then
                        connect()
                    End If
                End If


        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetDataSalesButton(ByVal StatusPrcName As String)
        Try
BackItems:
            If NetworkName = "Yes" Then : If UseExternalServer = "Yes" Then : connect() : End If : End If
            ItemsUnityNumber = 0
            Bol = True
            Try
                ParcodeMore = ""
                GetBarcodeMore(txtprc.Text) : If ParcodeMore <> "" Then : If ParcodeMore <> 0 Then : txtprc.Text = ParcodeMore : first7Chars = txtprc.Text : End If : End If
                If ParcodeMore = "0" Then
                    Dim PrcUnity As String = Cls.Get_Code_Value("ItemsUnity", "itm_id", "itm_id_Unity", ParcodeUnity)
                    If PrcUnity <> "0" Then
                        txtprc.Text = PrcUnity
                        first7Chars = txtprc.Text
                    End If
                End If

                Stores = mykey.GetValue("StoresName", "المخزن الرئيسى")

                If StatusPrcName = "Parcode" Then
                    Cls.Select_More_Data_Stores("items", "itm_id,sname,group_name,SalPrice,WholeWholePrice,RateVAT,RateDiscSalPriceAfter,Unity", "itm_id=N'" & txtprc.Text & "' and Stores =N'" & Stores & "' and QuickSearch=0")
                End If
                If StatusPrcName = "Items" Then
                    Cls.Select_More_Data_Stores("items", "itm_id,sname,group_name,SalPrice,WholeWholePrice,RateVAT,RateDiscSalPriceAfter,Unity", "sname=N'" & items & "' and Stores =N'" & Stores & "' and QuickSearch=0")
                End If
                If dr.HasRows = True Then
                    prc = dr("itm_id").ToString
                    items = dr("sname").ToString
                    cats = dr("group_name").ToString
                    price = dr("SalPrice").ToString
                    WholeWholePrice = dr("WholeWholePrice").ToString
                    ItemsRateVAT = Val(dr("RateVAT").ToString)
                    RateDiscPriceAfter = Val(dr("RateDiscSalPriceAfter").ToString)
                    Unity = Val(dr("Unity").ToString)
                Else
                    If StatusPrcName = "Parcode" Then
                        Cls.Select_More_Data_Stores("items", "itm_id,sname,group_name,SalPrice,WholeWholePrice,RateVAT,Stores,RateDiscSalPriceAfter,Unity", "itm_id=N'" & prc & "' and QuickSearch=0")
                    End If
                    If StatusPrcName = "Items" Then
                        Cls.Select_More_Data_Stores("items", "itm_id,sname,sname,group_name,SalPrice,WholeWholePrice,RateVAT,RateDiscSalPriceAfter,Unity", "sname=N'" & items & "' and QuickSearch=0")
                    End If
                    If dr.HasRows = True Then
                        prc = dr("itm_id").ToString
                        items = dr("sname").ToString
                        cats = dr("group_name").ToString
                        price = dr("SalPrice").ToString
                        WholeWholePrice = dr("WholeWholePrice").ToString
                        ItemsRateVAT = Val(dr("RateVAT").ToString)
                        Stores = dr("Stores").ToString
                        RateDiscPriceAfter = Val(dr("RateDiscSalPriceAfter").ToString)
                        Unity = Val(dr("Unity").ToString)
                    End If
                End If

            Catch ex As Exception
                If price = "" Then
                    GoTo BackItems
                End If
            End Try

            'Dim QuX As Double = 0
            'For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            '    If Dgv_Add.Rows(i).Cells(0).Value = prc Then
            '        QuX += Val(Dgv_Add.Rows(i).Cells(4).Value.ToString())
            '    End If
            'Next

            'If QuX <> 0 Then
            '    Dim QuTotal As Double = QuntText + QuX
            '    QuntText = QuTotal
            '    For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            '        If Dgv_Add.Rows(i).Cells(0).Value = prc Then
            '            Dgv_Add.Rows(i).Cells(4).Value = QuntText
            '            Dgv_Add.Rows(i).Cells(5).Value = QuntText
            '        End If
            '    Next
            'End If


            SumAllPrice()

            GetFocusText()
            If NetworkName = "Yes" Then
                If UseExternalServer = "Yes" Then
                    connect()
                End If
            End If


        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetAddItemsGrid()

        If NetworkName = "Yes" Then : If UseExternalServer = "Yes" Then : connect() : End If : End If

        'Try
        If BalanceBarcode = "0" Then
            TotalPrice = 0
            'QuntText = 0
            Try
                If QuntText.ToString() = "" Or QuntText = 0 Then
                    QuntText = 1
                End If
            Catch ex As Exception
                QuntText = 1
            End Try
        End If


        Dim Total As Double
        If ValidateTextSaveAdd(prc, Stores, 0) = False Then Exit Sub

        Dim ItmID As String
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
            ItmID = Dgv_Add.Rows(i).Cells(0).Value
            If txtprc.Text = "" Then
                txtprc.Text = prc
            End If
            If txtprc.Text = ItmID Then
                Dim XTotalqunt As String

                QuntText = Dgv_Add.Rows(i).Cells(4).Value

                If BalanceBarcode <> 1 Then
                    XTotalqunt = Val(1) + Val(QuntText)
                    TotalPrice = Val(XTotalqunt) * Val(price)
                Else
                    If ButtonAction = True Then
                        XTotalqunt = Val(1) + Val(QuntText)
                    Else
                        If ComboboxAction = True Then
                            XTotalqunt = Val(1) + Val(QuntText)
                        Else
                            XTotalqunt = QuntText
                        End If
                    End If
                    TotalPrice = Val(XTotalqunt) * Val(price)
                End If


                RNXD = Dgv_Add.Rows(i).Cells(0).RowIndex
                Dgv_Add.Rows.RemoveAt(RNXD)

                Dim XTotalTotalqunt As String = Val(XTotalqunt)
                Dgv_Add.DataSource = Fn_AddBill(txtprc.Text, cats, items, Val(price), Val(XTotalTotalqunt), Val(XTotalTotalqunt), Unity, TotalPrice, Stores, 0, Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), Val(TotalValueVAT), Val(TotalBeforeVAT), Val(ItemsRateVAT), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), 0, 0, 0, FormatNumberWithSeparators(Val(TotalPrice)))
                GoTo 2
            End If
        Next

        TotalPrice = Val(price) * Val(QuntText)

        Dgv_Add.DataSource = Fn_AddBill(prc, cats, items, Val(price), Val(QuntText), Val(QuntText), Unity, Val(TotalPrice), Stores, 0, Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), Val(TotalValueVAT), Val(TotalBeforeVAT), Val(ItemsRateVAT), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), 0, 0, 0, FormatNumberWithSeparators(Val(TotalPrice)))

2:
        ClearAdd() : SumAllPrice() : txtprc.Focus()
        txtCountItems.Text = Dgv_Add.Rows.Count
        GetFocusText()

        GetWidthVisibleReadOnly()

        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try


    End Sub

    Private Sub GetAddItemsGridButton()

        If NetworkName = "Yes" Then : If UseExternalServer = "Yes" Then : connect() : End If : End If

        'Try
        If BalanceBarcode = "0" Then
            TotalPrice = 0
            QuntText = 0
            Try
                If QuntText.ToString() = "" Or QuntText = 0 Then
                    QuntText = 1
                End If
            Catch ex As Exception
                QuntText = 1
            End Try
        End If


        Dim Total As Double
        If ValidateTextSaveAdd(prc, Stores, 0) = False Then Exit Sub

        Dim ItmID As String
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
            ItmID = Dgv_Add.Rows(i).Cells(0).Value
            If txtprc.Text = "" Then
                txtprc.Text = prc
            End If
            If txtprc.Text = ItmID Then
                Dim XTotalqunt As String

                QuntText = Dgv_Add.Rows(i).Cells(4).Value

                If BalanceBarcode <> 1 Then
                    XTotalqunt = Val(1) + Val(QuntText)
                    TotalPrice = Val(XTotalqunt) * Val(price)
                Else
                    XTotalqunt = QuntText
                    TotalPrice = Val(XTotalqunt) * Val(price)
                End If


                RNXD = Dgv_Add.Rows(i).Cells(0).RowIndex
                Dgv_Add.Rows.RemoveAt(RNXD)

                Dim XTotalTotalqunt As String = Val(XTotalqunt)
                Dgv_Add.DataSource = Fn_AddBill(txtprc.Text, cats, items, Val(price), Val(XTotalTotalqunt), Val(XTotalTotalqunt), Unity, TotalPrice, Stores, 0, Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), Val(TotalValueVAT), Val(TotalBeforeVAT), Val(ItemsRateVAT), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), 0, 0, 0, FormatNumberWithSeparators(Val(TotalPrice)))
                GoTo 2
            End If
        Next

        TotalPrice = Val(price) * Val(QuntText)

        Dgv_Add.DataSource = Fn_AddBill(prc, cats, items, Val(price), Val(QuntText), Val(QuntText), Unity, Val(TotalPrice), Stores, 0, Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), Val(TotalValueVAT), Val(TotalBeforeVAT), Val(ItemsRateVAT), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), Val(0), 0, 0, 0, FormatNumberWithSeparators(Val(TotalPrice)))

2:
        ClearAdd() : SumAllPrice() : txtprc.Focus()
        txtCountItems.Text = Dgv_Add.Rows.Count
        GetFocusText()

        GetWidthVisibleReadOnly()

        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try


    End Sub


    Private Sub btnClearTextbox_Click(sender As Object, e As EventArgs) Handles btnClearTextbox.Click
        ClearSave()
        KeyClear()
        MAXRECORD()
        GetFocusText()

    End Sub

    Private Sub MAXRECORD()
        'On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Sales_Bill"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Me.lblOrderNO.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(bill_No As int)) as mb FROM Sales_Bill where bill_No <> N'جرد'"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            If Not dr.IsDBNull(dr.GetOrdinal("mb")) Then
                sh = dr("mb")
                Me.lblOrderNO.Text = sh + 1
            Else
                Me.lblOrderNO.Text = 1
            End If
        End If

    End Sub

    Private Sub KeyClear()
        Key1 = 0
    End Sub

    Private Sub NumberTach(ByVal Number As String)
        If sign_Indicator = 0 Then
            QuntText = QuntText & CStr(Number)
        ElseIf sign_Indicator = 1 Then
            QuntText = Number
            sign_Indicator = 0
        End If
        Key1 = 1
        GetFocusText()
    End Sub

    Private Sub ClearSave()
        cmbvendores.SelectedIndex = -1
        lblOrderNO.Text = ""
        txttotalafterdisc.Text = "0"
        txttotalpeforedisc.Text = "0"
        txtdisc.Text = "0"
        cmbvendores.Text = "نقدا"
        QuntText = "0"
        txtprc.Text = ""
        cmbname.Text = ""
        txtpayingWindow.Text = ""
        txtstayingWindow.Text = ""
        Dt_AddBill.Rows.Clear()
    End Sub

    Private Sub btn7_Click(sender As Object, e As EventArgs) Handles btn7.Click
        NumberTach(7)
    End Sub

    Private Sub btn8_Click(sender As Object, e As EventArgs) Handles btn8.Click
        NumberTach(8)
    End Sub

    Private Sub btn9_Click(sender As Object, e As EventArgs) Handles btn9.Click
        NumberTach(9)
    End Sub

    Private Sub btn6_Click(sender As Object, e As EventArgs) Handles btn6.Click
        NumberTach(6)
    End Sub

    Private Sub btn5_Click(sender As Object, e As EventArgs) Handles btn5.Click
        NumberTach(5)
    End Sub

    Private Sub btn4_Click(sender As Object, e As EventArgs) Handles btn4.Click
        NumberTach(4)
    End Sub

    Private Sub btn3_Click(sender As Object, e As EventArgs) Handles btn3.Click
        NumberTach(3)
    End Sub

    Private Sub btn2_Click(sender As Object, e As EventArgs) Handles btn2.Click
        NumberTach(2)
    End Sub

    Private Sub btn1_Click(sender As Object, e As EventArgs) Handles btn1.Click
        NumberTach(1)
    End Sub

    Private Sub btn0_Click(sender As Object, e As EventArgs) Handles btn0.Click
        NumberTach(0)
    End Sub

    Private Sub btnDecimal_Click(sender As Object, e As EventArgs) Handles btnDecimal.Click
        NumberTach(".")
    End Sub

    Private Sub sumdisc()
        Try
            Dim DiscVal As Double

            DiscVal = Val(txttotalpeforedisc.Text) - Val(txtdisc.Text)
            txttotalafterdisc.Text = DiscVal

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub SumAllPrice()
        Dim SM1, SM2, SM3 As Double
        For i As Integer = 0 To Dgv_Add.RowCount - 1
            SM1 = Val(Dgv_Add.Rows(i).Cells(3).Value)
            SM2 = Val(Dgv_Add.Rows(i).Cells(4).Value)
            SM3 = SM1 * SM2 : SM3 = Math.Round(SM3, 2)

            Dgv_Add.Rows(i).Cells(7).Value = SM3
        Next

        Bol = True
        Dim SM As Double
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            SM = SM + Dgv_Add.Rows(i).Cells(7).Value
        Next

        txttotalpeforedisc.Text = Math.Round(SM, 2)
        txttotalafterdisc.Text = Math.Round(SM, 2)
    End Sub

    Private Sub txtdisc_TextChanged(sender As Object, e As EventArgs) Handles txtdisc.TextChanged
        MyVars.CheckNumber(txtdisc)
        sumdisc()
    End Sub

    Private Sub btnPrintChackEnd_Click(sender As Object, e As EventArgs) Handles btnPrintChackEnd.Click
        If ActivatePaidAndRest = False Then
            If chkActivatePaidAndRest.Checked = True Then
                txtpayingWindow.Text = "0"
                ActivatePrintChackEnd = True
                PanelPayingStaying.Location = New System.Drawing.Point(20, 76)
                PanelPayingStaying.Size = New System.Drawing.Size(325, 195)
                txtpayingWindow.Focus()
                txtpayingWindow.SelectAll()
                Exit Sub
            End If
        End If


        If NetworkName = "Yes" Then : If UseExternalServer = "Yes" Then : connect() : End If : End If

        If ValidateTextSave() = False Then
            ValidateTextSaveTrue = True
            Exit Sub
        End If

        MAXRECORD()

        Save_Bill()

        IM.CustomerAccountTotal(cmbvendores.Text)

        '===============================================================================
        Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
        If UseAccounts = "YES" Then
            Daily_Restrictions()
        End If
        '===============================================================================

        If ValidateTextSaveTrue = True Then
            ValidateTextSaveTrue = False
            Exit Sub
        End If

        If chkprint.Checked = True Then
            PrintReport()
        End If

        ViewScreenNCR(txttotalafterdisc.Text)

        Get_Movement_In_Out_Money(DateTimePicker1.Text, Treasury_Code_ID)

        Dt_AddBill.Rows.Clear()
        ClearSave()
        MAXRECORD()
        OrdersOnlineStore = False
        ActivatePaidAndRest = False
        GetFocusText()

    End Sub

    Private Sub Save_Bill()
        ValidateTextSaveTrue = False
        If NetworkName = "Yes" Then : If UseExternalServer = "Yes" Then : connect() : End If : End If

        SumAllPrice()

        paying = Val(txttotalafterdisc.Text)
        staying = Val(0)

        If ItemAddedInvoiceSavedAutomatically = "NO" Then
            If ValidateTextSave() = False Then
                ValidateTextSaveTrue = True
                Exit Sub
            End If
        End If

        Dim STAT As String = ""
        STAT = "نقداً"


        DiscTotal = Val(txtdisc.Text)
        StateDisc = "قيمة"

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select StatusOrder from Sales_Bill where bill_no=N'" & lblOrderNO.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            StatusOrder = dr("StatusOrder").ToString
        Else
            StatusOrder = ""
        End If

        GetDebtorlCreditorPrevious()

        Sales_Bill = "Sales_Bill" : BillsalData = "BillsalData" : vst = "vst" : Vst_disc = "Vst_disc"

        'Try

        If ItemAddedInvoiceSavedAutomatically = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete From  " & BillsalData & " where bill_no =N'" & lblOrderNO.Text & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  " & Vst_disc & " where TIN_NO =N'" & lblOrderNO.Text & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  " & vst & " where BillNo =N'" & lblOrderNO.Text & "'" : cmd.ExecuteNonQuery()
        End If

        If ItemAddedInvoiceSavedAutomatically = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_No from " & Sales_Bill & " where bill_No=N'" & lblOrderNO.Text.Trim & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update " & Sales_Bill & " set Company_Branch_ID =N'" & Company_Branch_ID & "',Vendorname =N'" & cmbvendores.Text & "',bill_date =N'" & Cls.C_date(DateTimePicker1.Text) & "',billtime =N'" & Cls.get_time(True) & "',totalpricebeforedisc =N'" & Val(txttotalpeforedisc.Text.Trim) & "',disc =N'" & Val(DiscTotal) & "',totalpriceafterdisc =N'" & txttotalafterdisc.Text & "',stat =N'" & STAT & "',bey =N'" & paying & "',STAYING =N'" & staying & "',SalesTax =N'" & 0 & "',Notes =N'',UserName =N'" & UserName & "',Sheft_Number =N'" & txtSheft_Number.Text & "',EmpName =N'" & cmbEmployees.Text & "',DiscountTax =N'" & Val(0) & "',bill_NoTax =N'" & 0 & "',DeliveryService =N'" & Val(0) & "',ExpensesBill =N'" & Val(0) & "',RateValues =N'" & Val(RateValues) & "',CreditPrevious =N'" & Val(AmntcreditPrevious) & "',DebitPrevious =N'" & Val(AmntdebitPrevious) & "',Treasury_Code =N'" & Treasury_Code_ID & "',ValueVAT =N'" & Val(0) & "',DiscountsValue =N'" & Val(0) & "',RateDriverDelivery =N'" & Val(0) & "',StatusOrder =N'" & StatusOrder & "',BillTimeAmBm =N'" & txtTimeAMPM.Text.Trim & "',CommercialIndustrialProfitsTax =N'" & 0 & "',AutoSeriesVATActive =N'" & 0 & "' where bill_No =N'" & lblOrderNO.Text & "'" : cmd.ExecuteNonQuery()
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into " & Sales_Bill & "(Company_Branch_ID,bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,SalesTax,Notes,UserName,Sheft_Number,EmpName,DiscountTax,bill_NoTax,DeliveryService,ExpensesBill,RateValues,CreditPrevious,DebitPrevious,Treasury_Code,ValueVAT,DiscountsValue,RateDriverDelivery,CloseSheft,BillTimeAmBm,CommercialIndustrialProfitsTax,AutoSeriesVATActive) values ("
                S = S & "N'" & Company_Branch_ID & "',N'" & lblOrderNO.Text.Trim & "' ,N'" & cmbvendores.Text.Trim & "' ,N'" & Cls.C_date(DateTimePicker1.Text) & "' ,N'" & Cls.get_time(True) & "' ," & Val(txttotalpeforedisc.Text.Trim) & "," & Val(DiscTotal) & " ," & Val(txttotalafterdisc.Text.Trim) & " ,N'" & STAT & "',N'" & Val(paying) & "',N'" & Val(staying) & "',N'" & Val(0) & "',N'',N'" & UserName & "',N'" & txtSheft_Number.Text & "',N'',N'" & Val(0) & "',N'" & 0 & "',N'" & Val(0) & "',N'" & Val(0) & "',N'" & Val(RateValues) & "',N'" & Val(AmntcreditPrevious) & "',N'" & Val(AmntdebitPrevious) & "',N'" & Treasury_Code_ID & "',N'" & Val(0) & "',N'" & txtdisc.Text & "',N'" & Val(0) & "',0,N'" & txtTimeAMPM.Text & "',N'" & 0 & "',N'" & 0 & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into " & Sales_Bill & "(Company_Branch_ID,bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,SalesTax,Notes,UserName,Sheft_Number,EmpName,DiscountTax,bill_NoTax,DeliveryService,ExpensesBill,RateValues,CreditPrevious,DebitPrevious,ValueVAT,Treasury_Code,DiscountsValue,RateDriverDelivery,CloseSheft,BillTimeAmBm,CommercialIndustrialProfitsTax,AutoSeriesVATActive) values ("
            S = S & "N'" & Company_Branch_ID & "',N'" & lblOrderNO.Text.Trim & "' ,N'" & cmbvendores.Text.Trim & "' ,N'" & Cls.C_date(DateTimePicker1.Text) & "' ,N'" & Cls.get_time(True) & "' ," & Val(txttotalpeforedisc.Text.Trim) & "," & Val(DiscTotal) & " ," & Val(txttotalafterdisc.Text.Trim) & " ,N'" & STAT & "',N'" & Val(paying) & "',N'" & Val(staying) & "',N'" & Val(0) & "',N'',N'" & UserName & "',N'" & txtSheft_Number.Text & "',N'',N'" & Val(0) & "',N'" & 0 & "',N'" & Val(0) & "',N'" & Val(0) & "',N'" & Val(RateValues) & "',N'" & Val(AmntcreditPrevious) & "',N'" & Val(AmntdebitPrevious) & "',N'" & Val(0) & "',N'" & Treasury_Code_ID & "',N'" & txtdisc.Text & "',N'" & Val(0) & "',0,N'" & txtTimeAMPM.Text & "',N'" & 0 & "',N'" & 0 & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If

        Dim bill_EndDate As String = "" : Dim bill_no_Expired As String = ""
        If NetworkName = "Yes" Then : If UseExternalServer = "Yes" Then : connect() : End If : End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1

            DiscountsTin = Val(Cls.Get_Code_Value_More_ALL_Select("Select Top(100) PERCENT Discounts From dbo.BilltINData Where (itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "') Order By id DESC"))

            AverageTinPrice(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value, Dgv_Add.Rows(i).Cells(3).Value, Dgv_Add.Rows(i).Cells(5).Value, Dgv_Add.Rows(i).Cells(6).Value.ToString, Dgv_Add.Rows(i).Cells(9).Value, Dgv_Add.Rows(i).Cells(20).Value)

            Vendorname = Cls.Get_Code_Value_More("" & BillsalData & "", "Vendorname", "itm_id =N'" & Dgv_Add.Rows(i).Cells(0).Value & "'")

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Top(100) PERCENT bill_no_Expired, bill_EndDate As Ex_bill_EndDate, qu_expired, itm_id, Expired, Stores, id From dbo.BilltINData  Group By itm_id, Expired, qu_expired, Stores, bill_EndDate, id, bill_no_Expired  HAVING(itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "') AND (Stores = N'" & Dgv_Add.Rows(i).Cells(8).Value & "') AND (qu_expired <> 0)    ORDER BY Ex_bill_EndDate"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                bill_no_Expired = dr(0).ToString
                bill_EndDate = dr(1).ToString
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into " & BillsalData & "(Company_Branch_ID, bill_no, itm_id, itm_cat, itm_name, price, qu, qu_unity, itm_Unity, TotalPrice, Stores, Discounts, itm_Notes, bill_date,billtime, UserName, TinPriceAverage, Profits, EmpName, Sheft_Number, ValueVAT, BeforeVAT, RateVAT, TinPrice, Vendorname, STAT, ResourceName, Treasury_Code, bill_EndDate, bill_no_Expired, Price_Unity,DiscountsValue,Discount_Price_After,StateDisc,DiscountsTin,CloseSheft)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & lblOrderNO.Text.Trim & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Dgv_Add.Rows(i).Cells(3).Value & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "',N'" & Dgv_Add.Rows(i).Cells(9).Value & "',N'" & Dgv_Add.Rows(i).Cells(10).Value & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Cls.get_time(True) & "',N'" & UserName & "',N'" & TinPriceAverage & "',N'" & Profits & "',N'" & cmbEmployees.Text & "',N'" & txtSheft_Number.Text & "',N'" & Dgv_Add.Rows(i).Cells(16).Value & "',N'" & Dgv_Add.Rows(i).Cells(17).Value & "',N'" & Dgv_Add.Rows(i).Cells(18).Value & "',N'" & TinPrice & "',N'" & cmbvendores.Text.Trim & "',N'" & STAT & "',N'" & Vendorname & "',N'" & Treasury_Code_ID & "',N'" & bill_EndDate & "',N'" & bill_no_Expired & "',N'" & Price_Unity & "',N'" & Dgv_Add.Rows(i).Cells(19).Value & "',N'" & Dgv_Add.Rows(i).Cells(20).Value & "',N'" & StateDisc & "',N'" & DiscountsTin & "',0)"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
            'End If

            IM.StoreExpired(Dgv_Add.Rows(i).Cells(0).Value.ToString(), Dgv_Add.Rows(i).Cells(8).Value.ToString(), bill_EndDate.ToString(), bill_no_Expired)

        Next

        If NetworkName = "Yes" Then : If UseExternalServer = "Yes" Then : connect() : End If : End If

        If Val(txttotalafterdisc.Text) > 0 Then
            If ItemAddedInvoiceSavedAutomatically = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select billno from " & vst & " where billno=N'" & lblOrderNO.Text.Trim & "'" : dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update " & vst & " set Company_Branch_ID =N'" & Company_Branch_ID & "',Vendorname =N'" & cmbvendores.Text & "',VND_XTM =N'" & Cls.get_time(True) & "',VND_dt =N'" & Cls.C_date(DateTimePicker1.Text) & "',VND_amx =N'" & paying & "',VND_ho =N'بفاتورة',VND_rcv =N'بفاتورة',VND_dec =N'بفاتورة',billno =N'" & lblOrderNO.Text & "',VND_no =N'دفعة نقدية',UserName =N'" & UserName & "',EmpName =N'" & cmbEmployees.Text & "',Treasury_Code =N'" & Treasury_Code_ID & "' where billno =N'" & lblOrderNO.Text & "'" : cmd.ExecuteNonQuery()
                Else
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    S = "insert into " & vst & "(Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,billno,VND_no,UserName,EmpName,Treasury_Code) values"
                    S = S & " (N'" & Company_Branch_ID & "',N'" & cmbvendores.Text & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(DateTimePicker1.Text) & "'," & paying & ",N'بفاتورة',N'بفاتورة',N'بفاتورة',N'" & lblOrderNO.Text & "',N'دفعة نقدية',N'" & UserName & "',N'" & cmbEmployees.Text & "',N'" & Treasury_Code_ID & "')"
                    cmd.CommandText = S : cmd.ExecuteNonQuery()
                End If
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into " & vst & "(Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,billno,VND_no,UserName,EmpName,Treasury_Code) values"
                S = S & " (N'" & Company_Branch_ID & "',N'" & cmbvendores.Text & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(DateTimePicker1.Text) & "'," & paying & ",N'بفاتورة',N'بفاتورة',N'بفاتورة',N'" & lblOrderNO.Text & "',N'دفعة نقدية',N'" & UserName & "',N'" & cmbEmployees.Text & "',N'" & Treasury_Code_ID & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        End If

        If Val(txtdisc.Text) > 0 Then
            If ItemAddedInvoiceSavedAutomatically = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select TIN_NO from " & Vst_disc & " where TIN_NO=N'" & lblOrderNO.Text.Trim & "'" : dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update " & Vst_disc & " set Company_Branch_ID =N'" & Company_Branch_ID & "',Vendorname =N'" & cmbvendores.Text & "',amnt =N'" & txtdisc.Text & "',pdate =N'" & Cls.C_date(DateTimePicker1.Text) & "',VND_XTM =N'" & Cls.get_time(True) & "',det =N'خصم على فاتورة مباشرة',TIN_NO =N'" & lblOrderNO.Text & "',UserName =N'" & UserName & "',Treasury_Code =N'" & Treasury_Code_ID & "' where TIN_NO =N'" & lblOrderNO.Text & "'" : cmd.ExecuteNonQuery()
                Else
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    S = "insert into " & Vst_disc & " (Company_Branch_ID,Vendorname,amnt,pdate,VND_XTM,det,TIN_NO,UserName,Treasury_Code) values (N'" & Company_Branch_ID & "',N'" & cmbvendores.Text.Trim & "'," & Val(DiscTotal) & ",N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Cls.get_time(True) & "',N'خصم على فاتورة مباشرة',N'" & lblOrderNO.Text.Trim & "',N'" & UserName & "',N'" & Treasury_Code_ID & "')"
                    cmd.CommandText = S : cmd.ExecuteNonQuery()
                End If
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into " & Vst_disc & " (Company_Branch_ID,Vendorname,amnt,pdate,VND_XTM,det,TIN_NO,UserName,Treasury_Code) values (N'" & Company_Branch_ID & "',N'" & cmbvendores.Text.Trim & "'," & Val(DiscTotal) & ",N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Cls.get_time(True) & "',N'خصم على فاتورة مباشرة',N'" & lblOrderNO.Text.Trim & "',N'" & UserName & "',N'" & Treasury_Code_ID & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        End If

        '================================================================================
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            IM.Store(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value)

            If ConnectOnlineStore = "YES" Then
                StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
                Cos.UpdateProductStock(StockOnline, Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(2).Value)
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update " & BillsalData & " set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & lblOrderNO.Text & "' and itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "' and Stores =N'" & Dgv_Add.Rows(i).Cells(8).Value & "'" : cmd.ExecuteNonQuery()
        Next

        '===============================================================================
        Dim UpdateSalPrice As String = mykey.GetValue("UpdateSalPrice", "NO")
        If UpdateSalPrice = "YES" Then
            UpdatePriceItems()
        End If
        '===============================================================================


        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub

    Private Sub UpdatePriceItems()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                If UpdateSalPriceAllStores = "NO" Then
                    cmd.CommandText = "update items set SalPrice = " & Val(Dgv_Add.Rows(i).Cells(3).Value) & " where itm_id =N'" & Dgv_Add.Rows(i).Cells(0).Value & "' and Stores =N'" & Dgv_Add.Rows(i).Cells(8).Value & "'" : cmd.ExecuteNonQuery()
                Else
                    cmd.CommandText = "update items set SalPrice = " & Val(Dgv_Add.Rows(i).Cells(3).Value) & " where itm_id =N'" & Dgv_Add.Rows(i).Cells(0).Value & "'" : cmd.ExecuteNonQuery()
                End If
            Next
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetDebtorlCreditorPrevious()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select vnamntcredit , vnamntdebit from Customers where Vendorname=N'" & cmbvendores.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                AmntcreditPrevious = dr("vnamntcredit")
                AmntdebitPrevious = dr("vnamntdebit")
                AmountDebitCreditPrevious = AmntcreditPrevious - AmntdebitPrevious

                'Dim XX As String = "-" & AmntdebitPrevious
                'AmntdebitPrevious = Val(XX)
                'If AmntcreditPrevious <> 0 Then
                '    AmountDebitCreditPrevious = AmntcreditPrevious
                'End If
                'If AmntdebitPrevious <> 0 Then
                '    AmountDebitCreditPrevious = AmntdebitPrevious
                'End If

            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub SplitDate()

        Dim PRC As String = DateTimePicker1.Text
        Try
            Dim split As String() = New String() {"/"}
            Dim itemsSplit As String() = PRC.Split(split, StringSplitOptions.None)
            Dim XDay As String = itemsSplit(2).ToString()
            Dim XMonth As String = itemsSplit(1).ToString()
            Dim XYears As String = itemsSplit(0).ToString()

            Dates = XDay & "/" & XMonth & "/" & XYears
        Catch ex As Exception
            Try
                Dim split As String() = New String() {"-"}
                Dim itemsSplit As String() = PRC.Split(split, StringSplitOptions.None)
                Dim XDay As String = itemsSplit(2).ToString()
                Dim XMonth As String = itemsSplit(1).ToString()
                Dim XYears As String = itemsSplit(0).ToString()

                Dates = XDay & "/" & XMonth & "/" & XYears
            Catch exc As Exception
            End Try
        End Try

    End Sub

    Private Sub PrintReport()
        If NetworkName = "Yes" Then : If UseExternalServer = "Yes" Then : connect() : End If : End If
        mykey.SetValue("ErrorLoadReportFailed", "NO")

        Dim txt, txtNameEn, txtCmpAddress, txtCmpEmail, txtCmpTel, txtCmpMobile, txtCmpUnderBILL, txtCmpAddressBill, txtCustomerAddressSales, txtCustomerTel, txtCommercialRecord, txtTaxCard, txtSaleTax, txtDiscountTax, txtCMPNameDown, txtAltfiqith, txtCmpFax, txtEndorsement, txtDelegateName, txtPhoneEmployee, txtProgramNameBill, txtObjectUserName, txtObjectDeliveryService, txtObjectCommercialAndIndustrialProfitsTax As TextObject

        Dim XTotalBill As String
        If paying = 0 Then
            XTotalBill = staying
        Else
            XTotalBill = txttotalafterdisc.Text
        End If

        SplitDate()

        Dim STAT As String = ""
        STAT = "نقداً"

        Dim TotalQunt As Double
        For i As Integer = 0 To Dgv_Add.RowCount - 1
            TotalQunt += Val(Dgv_Add.Rows(i).Cells(5).Value.ToString)
        Next

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Dim RateDiscount As String
        'If ChkCent2.Checked = True Then
        '    RateDiscount = "%"
        'Else
        RateDiscount = "$"
        'End If

        Dim TotalQuntGrid As Double = 0
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            Val(Dgv_Add.Rows(i).Cells(3).Value)
        Next

        Dim SalPrice As String = ""

        Try
            Cls.delete_Branch_All("PrintSalesPurchases")
        Catch ex As Exception
            ErrorHandling(ex, "Private Sub PrintReport()")
        End Try

        Dim taxn As String = Cls.Get_Code_Value_Branch_More("Customers", "taxn", "Vendorname =N'" & cmbvendores.Text & "'")

        Dim CurrentBalanceCustVnd As String = GetCurrentBalanceCustVnd(cmbvendores.Text)

        Dim Xqunt As String = ""
        Try
            BillSerialNumber = 0
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                BillSerialNumber += 1

                If ShowDiscountRateItemSales = "NO" Then
                    SalPrice = Dgv_Add.Rows(i).Cells(3).Value.ToString
                Else
                    SalPrice = Dgv_Add.Rows(i).Cells(3).Value.ToString

                    If SalPrice = 0 Then
                        SalPrice = Dgv_Add.Rows(i).Cells(3).Value.ToString
                    End If
                End If

                S = "insert into PrintSalesPurchases(Company_Branch_ID,itm_id,itm_cat,itm_name,price,qu,Unity,totalprice,store,det,VnPay,priceSal,BILL_NO,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,BEY,STAYING,vnamntcredit,vnamntdebit,TotalCreditor,TotalDebtor,UserName,Stat,TotalDisc,vndiscount,vintinval,TotalafterDisc,TotalBay,Totalstaying,CustomerName,valuereturns,Totalreturns,Driv_Name,Driv_CarNumber,Recipient,Delivery_Date,KiloMeter,Supervisor_Reform,VnReceipts,Received_Date,Name1,Name2,Name3,Name4,Name5,Name6,Name7,Number1,Name8,Name9,NumberInt1,TotalBeforeDisc,Name10)  values("
                S = S & "N'" & Company_Branch_ID & "',N'" & Dgv_Add.Rows(i).Cells(0).Value.ToString & "',N'" & Dgv_Add.Rows(i).Cells(1).Value.ToString & "',N'" & Dgv_Add.Rows(i).Cells(2).Value.ToString & "',N'" & SalPrice & "',N'" & Dgv_Add.Rows(i).Cells(5).Value.ToString & "',N'" & Dgv_Add.Rows(i).Cells(6).Value.ToString & "',N'" & Dgv_Add.Rows(i).Cells(7).Value.ToString & "',N'" & Dgv_Add.Rows(i).Cells(8).Value.ToString & "',N'" & Dgv_Add.Rows(i).Cells(9).Value.ToString & "',N'" & Dgv_Add.Rows(i).Cells(10).Value.ToString & "',N'" & Dgv_Add.Rows(i).Cells(11).Value.ToString & "',N'" & lblOrderNO.Text & "',N'" & cmbvendores.Text & "',N'" & Dates & "',N'" & Cls.Get_Time_AM_PM(TxtHour.Text.ToString) & "',N'" & 0 & "',N'" & txtdisc.Text & "',N'" & txttotalafterdisc.Text & "',N'" & txttotalafterdisc.Text & "',N'" & 0 & "',N'" & Amntcredit & "',N'" & Amntdebit & "',N'" & AmntcreditPrevious & "',N'" & AmntdebitPrevious & "',N'" & XTotalBill & "',N'',N'" & Dgv_Add.Rows.Count & "',N'',N'" & RateValues & "',N'" & Dgv_Add.Rows(i).Cells(12).Value.ToString & "',N'" & Dgv_Add.Rows(i).Cells(13).Value.ToString & "',N'" & Dgv_Add.Rows(i).Cells(14).Value.ToString & "',N'" & Dgv_Add.Rows(i).Cells(15).Value.ToString & "',N'" & Val(DiscTotal) & "',N'" & txttotalafterdisc.Text & "',N'" & ColorWithItems & "',N'" & TotalQunt & "',N'" & UserName & "',N'" & DefaultCurrencyProgram & "',N'" & 0 & "',N'" & 0 & "',N'" & Dgv_Add.Rows(i).Cells(20).Value.ToString & "',N'" & RateDiscount & "',N'',N'',N'',N'" & 0 & "',N'" & 0 & "',N'" & STAT & "',N'" & taxn & "',N'" & 0 & "',N'" & AmountDebitCreditPrevious & "',N'" & AmountDebitCreditAfter & "',N'" & BillSerialNumber & "',N'" & BillSerialNumber & "',N'" & 0 & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
            BillSerialNumber = 0

        Catch ex As Exception
            ErrorHandling(ex, "Private Sub PrintReport()")
        End Try

        AddReportView()

        Try
            Cls.Select_More_Data_Branch_Print_Orderby("PrintSalesPurchases", "*", "NumberInt1")
            Dim dt As New DataTable
            dt.Load(dr)

            Cls.GetDefaultPrinterA4()

            Dim rpt

            Cls.GetDefaultPrinterBill()

            If PrintSmall = "YES" Then
                Cls.GetDefaultPrinterBill()
                If OrderDelivery = True Then
                    'GetDataCustomerDelivery()
                    'rpt = New Rpt_SoldSmall_OrderDelivery
                    'txtObjectMobile = rpt.Section1.ReportObjects("Mobile")
                    'txtObjectMobile.Text = txtCustMobile
                    'txtObjectTel = rpt.Section1.ReportObjects("tel")
                    'txtObjectTel.Text = txtCusttel1
                    'txtObjectAddress = rpt.Section1.ReportObjects("address")
                    'txtObjectAddress.Text = txtCustAddress
                    'txtObjectApartment = rpt.Section1.ReportObjects("Apartment")
                    'txtObjectApartment.Text = txtCustApartment
                    'txtObjectRole = rpt.Section1.ReportObjects("Role")
                    'txtObjectRole.Text = txtCustRole
                    'txtObjectRegion = rpt.Section1.ReportObjects("Region")
                    'txtObjectRegion.Text = txtCustRegion
                    'txtObjectMark = rpt.Section1.ReportObjects("Mark")
                    'txtObjectMark.Text = txtCustMark
                    'txtObjectUserName = rpt.Section1.ReportObjects("UserName")
                    'txtObjectUserName.Text = UserName
                    'txtObjectDeliveryService = rpt.Section1.ReportObjects("DeliveryService")
                    'txtObjectDeliveryService.Text = txtDeliveryService.Text
                Else
                    If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                        rpt = New Rpt_SoldSmall
                        txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
                        txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(txttotalafterdisc.Text)
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                        rpt = New Rpt_SoldSmall_2
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                        rpt = New Rpt_SoldSmall_3
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                        rpt = New Rpt_SoldSmall_4
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                        rpt = New Rpt_SoldSmall_5
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                        rpt = New Rpt_SoldSmall_6
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                        rpt = New Rpt_SoldSmall_7
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                        rpt = New Rpt_SoldSmall_8
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                        rpt = New Rpt_SoldSmall_9
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                        rpt = New Rpt_SoldSmall_10
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                        rpt = New Rpt_SoldSmall_11
                        If ShowCustomerAddressSales = "YES" Then
                            If CustomerAddress <> "" Then
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                            End If
                        End If
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                        rpt = New Rpt_SoldSmall_12
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                        rpt = New Rpt_SoldSmall_13
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                        rpt = New Rpt_SoldSmall_14
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                        rpt = New Rpt_SoldSmall_15
                        txtObjectUserName = rpt.Section1.ReportObjects("UserName")
                        txtObjectUserName.Text = UserName
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                        rpt = New Rpt_SoldSmall_16
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                        rpt = New Rpt_SoldSmall_17
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                        rpt = New Rpt_SoldSmall_18
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                        rpt = New Rpt_SoldSmall_19
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                        rpt = New Rpt_SoldSmall_20
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                        rpt = New Rpt_SoldSmall_21
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                        rpt = New Rpt_SoldSmall_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                        rpt = New Rpt_SoldSmall_23
                        If ShowCustomerAddressSales = "YES" Then
                            If CustomerAddress <> "" Then
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                            End If
                        End If
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                        'txtObjectDeliveryService = rpt.Section1.ReportObjects("DeliveryService")
                        'txtObjectDeliveryService.Text = txtDeliveryService.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                        rpt = New Rpt_SoldSmall_24
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                        rpt = New Rpt_SoldSmall_10
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                        rpt = New Rpt_SoldSmall_24
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                        rpt = New Rpt_SoldSmall_24
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                        rpt = New Rpt_SoldSmall_24
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                        rpt = New Rpt_SoldSmall_24
                    End If
                End If
                If SalesInvoicePrintingLanguage = "English" Then
                    rpt = New Rpt_SoldSmall_EN
                End If
            End If

            If PrintSmall = "NO" Then
                If OrderDelivery = True Then
                    'GetDataCustomerDelivery()
                    'rpt = New Rpt_SalesBill_OrderDelivery
                    'txtObjectMobile = rpt.Section1.ReportObjects("Mobile")
                    'txtObjectMobile.Text = txtCustMobile
                    'txtObjectTel = rpt.Section1.ReportObjects("tel")
                    'txtObjectTel.Text = txtCusttel1
                    'txtObjectAddress = rpt.Section1.ReportObjects("address")
                    'txtObjectAddress.Text = txtCustAddress
                    'txtObjectApartment = rpt.Section1.ReportObjects("Apartment")
                    'txtObjectApartment.Text = txtCustApartment
                    'txtObjectRole = rpt.Section1.ReportObjects("Role")
                    'txtObjectRole.Text = txtCustRole
                    'txtObjectRegion = rpt.Section1.ReportObjects("Region")
                    'txtObjectRegion.Text = txtCustRegion
                    'txtObjectMark = rpt.Section1.ReportObjects("Mark")
                    'txtObjectMark.Text = txtCustMark
                    'txtObjectUserName = rpt.Section1.ReportObjects("UserName")
                    'txtObjectUserName.Text = UserName
                Else
                    If ColorWithItems <> "" Then
                        If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                            rpt = New Rpt_SalesBill
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                            rpt = New Rpt_SalesBill_2
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                            rpt = New Rpt_SalesBill_3
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                            rpt = New Rpt_SalesBill_4
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                            rpt = New Rpt_SalesBill_5
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                            rpt = New Rpt_SalesBill_6
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                            rpt = New Rpt_SalesBill_7
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                            rpt = New Rpt_SalesBill_8
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                            rpt = New Rpt_SalesBill_9
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                            rpt = New Rpt_SalesBill_10
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                            rpt = New Rpt_SalesBill_11
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                            rpt = New Rpt_SalesBill_Delegate
                            txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                            txtEndorsement.Text = CMPEndorsement
                            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                            txtDelegateName.Text = cmbEmployees.Text
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                            rpt = New Rpt_SalesBill_Delegate_2
                            txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                            txtEndorsement.Text = CMPEndorsement
                            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                            txtDelegateName.Text = cmbEmployees.Text
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                            txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                            txtPhoneEmployee.Text = PhoneEmployee
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                            rpt = New Rpt_SalesBill_14
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                            rpt = New Rpt_SalesBill_15
                            txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                            txtEndorsement.Text = CMPEndorsement
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                            rpt = New Rpt_SalesBill_16
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                            rpt = New Rpt_SalesBill_17
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                            rpt = New Rpt_SalesBill_18
                            If ShowCustomerAddressSales = "YES" Then
                                If CustomerAddress <> "" Then
                                    txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                    txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                                    txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                    txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                                End If
                            End If
                            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                            txtDelegateName.Text = cmbEmployees.Text
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                            rpt = New Rpt_SalesBill_19
                            If ShowCustomerAddressSales = "YES" Then
                                If CustomerAddress <> "" Then
                                    txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                    txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                                    txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                    txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                                End If
                            End If
                            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                            txtDelegateName.Text = cmbEmployees.Text
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                            rpt = New Rpt_SalesBill_20
                            If CustomerAddress <> "" Then
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = CustomerTel
                            End If
                            txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                            txtDelegateName.Text = cmbEmployees.Text
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                            rpt = New Rpt_SalesBill_21
                            If CommercialAndIndustrialProfitsTax = 0 Then
                                txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                                txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                            End If
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                            rpt = New Rpt_SalesBill_22
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                            rpt = New Rpt_SalesBill_23
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                            rpt = New Rpt_SalesBill_24
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                            rpt = New Rpt_SalesBill_25
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                            rpt = New Rpt_SalesBill_26
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                            rpt = New Rpt_SalesBill_27
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                            rpt = New Rpt_SalesBill_28
                        End If
                        If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                            rpt = New Rpt_SalesBill_29
                        End If
                    Else
                        If SalesBillNotDiscountBill = "YES" Then
                            If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                                rpt = New Rpt_SalesBill_NotDiscountBill
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                                rpt = New Rpt_SalesBill_2
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                                rpt = New Rpt_SalesBill_3
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                                rpt = New Rpt_SalesBill_4
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                                rpt = New Rpt_SalesBill_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                                rpt = New Rpt_SalesBill_6
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                                rpt = New Rpt_SalesBill_7
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                                rpt = New Rpt_SalesBill_8
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                                rpt = New Rpt_SalesBill_9
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                                rpt = New Rpt_SalesBill_10
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                                rpt = New Rpt_SalesBill_11
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                                rpt = New Rpt_SalesBill_Delegate
                                txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                txtEndorsement.Text = CMPEndorsement
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                                rpt = New Rpt_SalesBill_Delegate_2
                                txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                txtEndorsement.Text = CMPEndorsement
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = CustomerTel
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                                txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                                txtPhoneEmployee.Text = PhoneEmployee
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                                rpt = New Rpt_SalesBill_14
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                                rpt = New Rpt_SalesBill_15
                                txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                txtEndorsement.Text = CMPEndorsement
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                                rpt = New Rpt_SalesBill_16
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                                rpt = New Rpt_SalesBill_17
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                                rpt = New Rpt_SalesBill_18
                                If ShowCustomerAddressSales = "YES" Then
                                    If CustomerAddress <> "" Then
                                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                        txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                        txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                                    End If
                                End If
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                                rpt = New Rpt_SalesBill_19
                                If ShowCustomerAddressSales = "YES" Then
                                    If CustomerAddress <> "" Then
                                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                        txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                        txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                                    End If
                                End If
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                                rpt = New Rpt_SalesBill_20
                                If ShowCustomerAddressSales = "YES" Then
                                    If CustomerAddress <> "" Then
                                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                        txtCustomerAddressSales.Text = CustomerAddress
                                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                        txtCustomerTel.Text = CustomerTel
                                    End If
                                End If
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                                rpt = New Rpt_SalesBill_21
                                If CommercialAndIndustrialProfitsTax = 0 Then
                                    txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                                    txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                                End If
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                                rpt = New Rpt_SalesBill_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                                rpt = New Rpt_SalesBill_23
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                                rpt = New Rpt_SalesBill_24
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                                rpt = New Rpt_SalesBill_25
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                                rpt = New Rpt_SalesBill_26
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                                rpt = New Rpt_SalesBill_27
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                                rpt = New Rpt_SalesBill_28
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                                rpt = New Rpt_SalesBill_29
                            End If
                            GoTo 10
                        End If
                        If SalesBillNotDiscount = "YES" Then
                            If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                                rpt = New Rpt_SalesBill_NotDiscount
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                                rpt = New Rpt_SalesBill_2
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                                rpt = New Rpt_SalesBill_3
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                                rpt = New Rpt_SalesBill_4
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                                rpt = New Rpt_SalesBill_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                                rpt = New Rpt_SalesBill_6
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                                rpt = New Rpt_SalesBill_7
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                                rpt = New Rpt_SalesBill_8
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                                rpt = New Rpt_SalesBill_9
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                                rpt = New Rpt_SalesBill_10
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                                rpt = New Rpt_SalesBill_11
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                                rpt = New Rpt_SalesBill_Delegate
                                txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                txtEndorsement.Text = CMPEndorsement
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                                rpt = New Rpt_SalesBill_Delegate_2
                                txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                txtEndorsement.Text = CMPEndorsement
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = CustomerTel
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                                txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                                txtPhoneEmployee.Text = PhoneEmployee
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                                rpt = New Rpt_SalesBill_14
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                                rpt = New Rpt_SalesBill_15
                                txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                txtEndorsement.Text = CMPEndorsement
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                                rpt = New Rpt_SalesBill_16
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                                rpt = New Rpt_SalesBill_17
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                                rpt = New Rpt_SalesBill_18
                                If ShowCustomerAddressSales = "YES" Then
                                    If CustomerAddress <> "" Then
                                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                        txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                        txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                                    End If
                                End If
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                                rpt = New Rpt_SalesBill_19
                                If ShowCustomerAddressSales = "YES" Then
                                    If CustomerAddress <> "" Then
                                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                        txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                        txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                                    End If
                                End If
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                                rpt = New Rpt_SalesBill_20
                                If ShowCustomerAddressSales = "YES" Then
                                    If CustomerAddress <> "" Then
                                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                        txtCustomerAddressSales.Text = CustomerAddress
                                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                        txtCustomerTel.Text = CustomerTel
                                    End If
                                End If
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                                rpt = New Rpt_SalesBill_21
                                If CommercialAndIndustrialProfitsTax = 0 Then
                                    txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                                    txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                                End If
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                                rpt = New Rpt_SalesBill_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                                rpt = New Rpt_SalesBill_23
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                                rpt = New Rpt_SalesBill_24
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                                rpt = New Rpt_SalesBill_25
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                                rpt = New Rpt_SalesBill_26
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                                rpt = New Rpt_SalesBill_27
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                                rpt = New Rpt_SalesBill_28
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                                rpt = New Rpt_SalesBill_29
                            End If
                        Else
                            If ShowCustomerBalanceSalesScreen = "YES" Then
                                If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                                    rpt = New Rpt_SalesBill
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                                    rpt = New Rpt_SalesBill_2
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                                    rpt = New Rpt_SalesBill_3
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                                    rpt = New Rpt_SalesBill_4
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                                    rpt = New Rpt_SalesBill_5
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                                    rpt = New Rpt_SalesBill_6
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                                    rpt = New Rpt_SalesBill_7
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                                    rpt = New Rpt_SalesBill_8
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                                    rpt = New Rpt_SalesBill_9
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                                    rpt = New Rpt_SalesBill_10
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                                    rpt = New Rpt_SalesBill_11
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                                    rpt = New Rpt_SalesBill_Delegate
                                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                    txtEndorsement.Text = CMPEndorsement
                                    txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                    txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                                    txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                    txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                    txtDelegateName.Text = cmbEmployees.Text
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                                    rpt = New Rpt_SalesBill_Delegate_2
                                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                    txtEndorsement.Text = CMPEndorsement
                                    txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                    txtCustomerAddressSales.Text = CustomerAddress
                                    txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                    txtCustomerTel.Text = CustomerTel
                                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                    txtDelegateName.Text = cmbEmployees.Text
                                    txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                                    txtPhoneEmployee.Text = PhoneEmployee
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                                    rpt = New Rpt_SalesBill_14
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                                    rpt = New Rpt_SalesBill_15
                                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                    txtEndorsement.Text = CMPEndorsement
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                                    rpt = New Rpt_SalesBill_16
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                                    rpt = New Rpt_SalesBill_17
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                                    rpt = New Rpt_SalesBill_18
                                    If ShowCustomerAddressSales = "YES" Then
                                        If CustomerAddress <> "" Then
                                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                            txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                            txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                                        End If
                                    End If
                                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                    txtDelegateName.Text = cmbEmployees.Text
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                                    rpt = New Rpt_SalesBill_19
                                    If ShowCustomerAddressSales = "YES" Then
                                        If CustomerAddress <> "" Then
                                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                            txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                            txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                                        End If
                                    End If
                                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                    txtDelegateName.Text = cmbEmployees.Text
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                                    rpt = New Rpt_SalesBill_20
                                    If ShowCustomerAddressSales = "YES" Then
                                        If CustomerAddress <> "" Then
                                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                            txtCustomerAddressSales.Text = CustomerAddress
                                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                            txtCustomerTel.Text = CustomerTel
                                        End If
                                    End If
                                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                    txtDelegateName.Text = cmbEmployees.Text
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                                    rpt = New Rpt_SalesBill_21
                                    If CommercialAndIndustrialProfitsTax = 0 Then
                                        txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                                        txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                                    End If
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                                    rpt = New Rpt_SalesBill_22
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                                    rpt = New Rpt_SalesBill_23
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                                    rpt = New Rpt_SalesBill_24
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                                    rpt = New Rpt_SalesBill_25
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                                    rpt = New Rpt_SalesBill_26
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                                    rpt = New Rpt_SalesBill_27
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                                    rpt = New Rpt_SalesBill_28
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                                    rpt = New Rpt_SalesBill_29
                                End If
                            Else
                                If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                                    rpt = New Rpt_SalesBill_Cash
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                                    rpt = New Rpt_SalesBill_2
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                                    rpt = New Rpt_SalesBill_3
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                                    rpt = New Rpt_SalesBill_4
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                                    rpt = New Rpt_SalesBill_5
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                                    rpt = New Rpt_SalesBill_6
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                                    rpt = New Rpt_SalesBill_7
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                                    rpt = New Rpt_SalesBill_8
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                                    rpt = New Rpt_SalesBill_9
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                                    rpt = New Rpt_SalesBill_10
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                                    rpt = New Rpt_SalesBill_11
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                                    rpt = New Rpt_SalesBill_Delegate
                                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                    txtEndorsement.Text = CMPEndorsement
                                    txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                    txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                                    txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                    txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                    txtDelegateName.Text = cmbEmployees.Text
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                                    rpt = New Rpt_SalesBill_Delegate_2
                                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                    txtEndorsement.Text = CMPEndorsement
                                    txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                    txtCustomerAddressSales.Text = CustomerAddress
                                    txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                    txtCustomerTel.Text = CustomerTel
                                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                    txtDelegateName.Text = cmbEmployees.Text
                                    txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                                    txtPhoneEmployee.Text = PhoneEmployee
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                                    rpt = New Rpt_SalesBill_14
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                                    rpt = New Rpt_SalesBill_15
                                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                    txtEndorsement.Text = CMPEndorsement
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                                    rpt = New Rpt_SalesBill_16
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                                    rpt = New Rpt_SalesBill_17
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                                    rpt = New Rpt_SalesBill_18
                                    If ShowCustomerAddressSales = "YES" Then
                                        If CustomerAddress <> "" Then
                                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                            txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                            txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                                        End If
                                    End If
                                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                    txtDelegateName.Text = cmbEmployees.Text
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                                    rpt = New Rpt_SalesBill_19
                                    If ShowCustomerAddressSales = "YES" Then
                                        If CustomerAddress <> "" Then
                                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                            txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                            txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                                        End If
                                    End If
                                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                    txtDelegateName.Text = cmbEmployees.Text
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                                    rpt = New Rpt_SalesBill_20
                                    If ShowCustomerAddressSales = "YES" Then
                                        If CustomerAddress <> "" Then
                                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                            txtCustomerAddressSales.Text = CustomerAddress
                                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                            txtCustomerTel.Text = CustomerTel
                                        End If
                                    End If
                                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                    txtDelegateName.Text = cmbEmployees.Text
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                                    rpt = New Rpt_SalesBill_21
                                    If CommercialAndIndustrialProfitsTax = 0 Then
                                        txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                                        txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                                    End If
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                                    rpt = New Rpt_SalesBill_22
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                                    rpt = New Rpt_SalesBill_23
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                                    rpt = New Rpt_SalesBill_24
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                                    rpt = New Rpt_SalesBill_25
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                                    rpt = New Rpt_SalesBill_26
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                                    rpt = New Rpt_SalesBill_27
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                                    rpt = New Rpt_SalesBill_28
                                End If
                                If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                                    rpt = New Rpt_SalesBill_29
                                End If
                            End If
                        End If
10:
                    End If
                End If
                txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
                txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(txttotalafterdisc.Text)
                txtCMPNameDown = rpt.Section1.ReportObjects("txtCMPNameDown")
                txtCMPNameDown.Text = CMPNameDown
                txtCmpFax = rpt.Section1.ReportObjects("txtFax")
                txtCmpFax.Text = CmpFax
                'If ShowCustomerAddressSales = "YES" Then
                '    If CustomerAddress <> "" Then
                '        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                '        txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                '        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                '        txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                '        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                '        txtDelegateName.Text = cmbEmployees.Text
                '    End If
                'End If
            End If

            If PrintSmall = "A5" Then
                If ColorWithItems <> "" Then
                    If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                        rpt = New Rpt_SalesBill_A5_1
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                        rpt = New Rpt_SalesBill_BalanceCust_A5_2
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                        rpt = New Rpt_SalesBill_A5_3
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                        rpt = New Rpt_SalesBill_A5_4
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                        rpt = New Rpt_SalesBill_A5_6
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                        rpt = New Rpt_SalesBill_A5_7
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                        rpt = New Rpt_SalesBill_A5_9
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                        rpt = New Rpt_SalesBill_A5_6_Delegate_2
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                        txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                        txtPhoneEmployee.Text = PhoneEmployee
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                        rpt = New Rpt_SalesBill_A5_6_Delegate_2
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                        txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                        txtPhoneEmployee.Text = PhoneEmployee
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                        rpt = New Rpt_SalesBill_BalanceCust_A5_14
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                        rpt = New Rpt_SalesBill_A5_15
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                        rpt = New Rpt_SalesBill_A5_16
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                        rpt = New Rpt_SalesBill_A5_17
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                Else
                    If ShowCustomerBalanceSalesScreen = "YES" Then
                        If SalesPricePublic = "YES" Then
                            rpt = New Rpt_SalesBill_A5_PricePublic
                        Else
                            If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                                rpt = New Rpt_SalesBill_A5_1
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                                rpt = New Rpt_SalesBill_BalanceCust_A5_2
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                                rpt = New Rpt_SalesBill_A5_3
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                                rpt = New Rpt_SalesBill_A5_4
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                                rpt = New Rpt_SalesBill_A5_6
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                                rpt = New Rpt_SalesBill_A5_7
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                                rpt = New Rpt_SalesBill_A5_9
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = ""
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                                rpt = New Rpt_SalesBill_A5_6_Delegate_2
                                txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                txtEndorsement.Text = CMPEndorsement
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = CustomerTel
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                                txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                                txtPhoneEmployee.Text = PhoneEmployee
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                                rpt = New Rpt_SalesBill_A5_6_Delegate_2
                                txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                txtEndorsement.Text = CMPEndorsement
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = CustomerTel
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                                txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                                txtPhoneEmployee.Text = PhoneEmployee
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                                rpt = New Rpt_SalesBill_BalanceCust_A5_14
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                                rpt = New Rpt_SalesBill_A5_15
                                txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                txtEndorsement.Text = CMPEndorsement
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                                rpt = New Rpt_SalesBill_A5_16
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                                rpt = New Rpt_SalesBill_A5_17
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                        End If
                    Else
                        If SalesPricePublic = "YES" Then
                            If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                                rpt = New Rpt_SalesBill_BalanceCust_A5_PricePublic
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                                rpt = New Rpt_SalesBill_BalanceCust_A5_2
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                                rpt = New Rpt_SalesBill_A5_3
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                                rpt = New Rpt_SalesBill_A5_4
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                                rpt = New Rpt_SalesBill_A5_6
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                                rpt = New Rpt_SalesBill_A5_7
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                                rpt = New Rpt_SalesBill_A5_9
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                                rpt = New Rpt_SalesBill_A5_6_Delegate_2
                                txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                txtEndorsement.Text = CMPEndorsement
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = CustomerTel
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                                txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                                txtPhoneEmployee.Text = PhoneEmployee
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                                rpt = New Rpt_SalesBill_A5_6_Delegate_2
                                txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                txtEndorsement.Text = CMPEndorsement
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = CustomerTel
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                                txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                                txtPhoneEmployee.Text = PhoneEmployee
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                                rpt = New Rpt_SalesBill_BalanceCust_A5_14
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                                rpt = New Rpt_SalesBill_A5_15
                                txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                txtEndorsement.Text = CMPEndorsement
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                                rpt = New Rpt_SalesBill_A5_16
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                                rpt = New Rpt_SalesBill_A5_17
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                        Else
                            If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                                rpt = New Rpt_SalesBill_BalanceCust_A5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                                rpt = New Rpt_SalesBill_BalanceCust_A5_2
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                                rpt = New Rpt_SalesBill_A5_3
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                                rpt = New Rpt_SalesBill_A5_4
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                                rpt = New Rpt_SalesBill_A5_6
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                                rpt = New Rpt_SalesBill_A5_7
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                                rpt = New Rpt_SalesBill_A5_9
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                                rpt = New Rpt_SalesBill_A5_6_Delegate_2
                                txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                txtEndorsement.Text = CMPEndorsement
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = CustomerTel
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                                txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                                txtPhoneEmployee.Text = PhoneEmployee
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                                rpt = New Rpt_SalesBill_A5_6_Delegate_2
                                txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                txtEndorsement.Text = CMPEndorsement
                                txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                                txtCustomerAddressSales.Text = CustomerAddress
                                txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                                txtCustomerTel.Text = CustomerTel
                                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                                txtDelegateName.Text = cmbEmployees.Text
                                txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                                txtPhoneEmployee.Text = PhoneEmployee
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                                rpt = New Rpt_SalesBill_BalanceCust_A5_14
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                                rpt = New Rpt_SalesBill_A5_15
                                txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                                txtEndorsement.Text = CMPEndorsement
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                                rpt = New Rpt_SalesBill_A5_16
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                                rpt = New Rpt_SalesBill_A5_17
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                                rpt = New Rpt_SalesBill_A5_5
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                            If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                                rpt = New Rpt_SalesBill_A5_22
                            End If
                        End If
                    End If
                End If
                txtCMPNameDown = rpt.Section1.ReportObjects("txtCMPNameDown")
                txtCMPNameDown.Text = CMPNameDown
                txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
                txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(txttotalafterdisc.Text)
                If ShowTax = "YES" Then
                    'txtSaleTax = rpt.Section1.ReportObjects("txtSaleTax")
                    'txtSaleTax.Text = txtSalestax.Text & " : ضريبة المبيعات"
                    'txtDiscountTax = rpt.Section1.ReportObjects("txtDiscountTax")
                    'txtDiscountTax.Text = ValueDiscountTax & " : ضريبة الخصم"
                End If
            End If

            If Height_Width_Altitude_Density = "YES" Then
                If printNotInvoiceWidthHeight = False Then
                    rpt = New Rpt_SalesBill_Width_Height
                End If

            End If

            If ShowValueVAT = "YES" Then
                If PrintSmall = "YES" Then
                    rpt = New Rpt_SoldSmall_VAT
                End If
                If PrintSmall = "NO" Then
                    If CommercialAndIndustrialProfitsTaxYESNO = "YES" Then
                        'If txtCommercialAndIndustrialProfitsTaxRate.Text = "0" And CommercialAndIndustrialProfitsTaxYESNO = "NO" Then
                        '    rpt = New Rpt_SalesBill_Cash_VAT
                        'Else
                        '    rpt = New Rpt_SalesBill_Cash_VAT_1
                        '    GoTo CommercialAndIndustrialProfitsVAT
                        'End If
                        'If txtCommercialAndIndustrialProfitsTaxRate.Text = "0" And CommercialAndIndustrialProfitsTaxYESNO = "YES" Then
                        '    rpt = New Rpt_SalesBill_Cash_VAT_1
                        '    GoTo CommercialAndIndustrialProfitsVAT
                        'End If
                    End If
                    txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
                    txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(txttotalafterdisc.Text)
                End If
                If PrintSmall = "A5" Then
                    rpt = New Rpt_SalesBill_BalanceCust_A5_VAT
                End If
            End If

            If ActivateElectronicBill = "YES" Then
                If PrintSmall = "YES" Then
                    rpt = New Rpt_SoldSmall_VAT_QRCode
                End If
                If PrintSmall = "NO" Then
                    rpt = New Rpt_SalesBill_Cash_VAT_QRCode2
                    'txtObjectVATRate = rpt.Section1.ReportObjects("txtVATRate")
                    'txtObjectVATRate.Text = "(% " & txtSalestax.Text & " )"
                    txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
                    txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(txttotalafterdisc.Text)
                End If
                If PrintSmall = "A5" Then
                    rpt = New Rpt_SalesBill_BalanceCust_A5_VAT_QRCode
                End If
            End If

            If DefineCustomInvoiceHandling = "YES" Then
                rpt = New Rpt_SalesBill_A5_Custom_Invoice_1
            End If

CommercialAndIndustrialProfitsVAT:


            Try
                rpt.SetDataSource(dt)
            Catch ex As Exception
                mykey.SetValue("ErrorLoadReportFailed", "YES")
                System.Diagnostics.Process.Start(ChangeFileNameProgramFITSOFT)
                End
            End Try

            txtCmpAddressBill = rpt.Section1.ReportObjects("txtTitelAddress")
            txtCmpAddressBill.Text = CMPAddressBill
            txt = rpt.Section1.ReportObjects("txtTitelAr")
            txt.Text = NameArCompay
            txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
            txtNameEn.Text = NameEnCompany
            txtCmpAddress = rpt.Section1.ReportObjects("txtAddress")
            txtCmpAddress.Text = CmpAddress
            txtCmpEmail = rpt.Section1.ReportObjects("txtEmail")
            txtCmpEmail.Text = CmpEmail
            txtCmpTel = rpt.Section1.ReportObjects("txtTel")
            txtCmpTel.Text = CmpTel
            txtCmpMobile = rpt.Section1.ReportObjects("txtMobile")
            txtCmpMobile.Text = CmpMobile
            txtCommercialRecord = rpt.Section1.ReportObjects("txtCommercialRecord")
            txtCommercialRecord.Text = CMPCommercialRecord
            txtTaxCard = rpt.Section1.ReportObjects("txtTaxCard")
            txtTaxCard.Text = CMPTaxCard
            txtCmpFax = rpt.Section1.ReportObjects("txtFax")
            txtCmpFax.Text = CmpFax
            If cmbEmployees.Text = "" Then
                txtCmpUnderBILL = rpt.Section1.ReportObjects("txtUnderBILL")
                txtCmpUnderBILL.Text = CMPUnderBILL
            End If

            If SelectLogoPathOther = "YES" Then
                rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
            Else
                rpt.SetParameterValue("ImageURL", CMPLogoPath)
            End If

            If HideProgramNameBill = "YES" Then
                txtProgramNameBill = rpt.Section1.ReportObjects("txtProgramNameBill")
                txtProgramNameBill.Text = ""
            Else
                txtProgramNameBill = rpt.Section1.ReportObjects("txtProgramNameBill")
                txtProgramNameBill.Text = ProgramNameBill
            End If

            If rdoPrintDirect.Checked = True Then
                Dim InvoicePrintedTwoCopies As String = mykey.GetValue("InvoicePrintedTwoCopies", "NO")
                If InvoicePrintedTwoCopies = "YES" Then
                    rpt.PrintToPrinter(1, False, 0, 0)
                    rpt.PrintToPrinter(1, False, 0, 0)
                    GoTo 1
                End If

                rpt.PrintToPrinter(1, False, 0, 0)
1:
            End If
            If rdoPrintPreview.Checked = True Then
                Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
                Frm_PrintReports.Text = "فاتـــــورة مبــيــعات"
                Frm_PrintReports.Show()
            End If

            If RunDatabaseInternet = "YES" Then : connect() : End If
        Catch ex As Exception
            ErrorHandling(ex, "Private Sub PrintReport()")
        End Try
    End Sub

    Function ValidateTextSave() As Boolean
        Try

            If cmbvendores.Text = "" Then MsgBox("فضلا أختر العميل", MsgBoxStyle.Exclamation) : cmbvendores.Focus() : Return False
            If lblOrderNO.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الفاتورة", MsgBoxStyle.Exclamation) : lblOrderNO.Focus() : Return False
            If txtdisc.Text = "" Then
                txtdisc.Text = "0"
            End If

            If Dgv_Add.Rows.Count < 1 Then MsgBox("فضلا أكمل بيانات الفاتورة", MsgBoxStyle.Exclamation) : Return False

            If ItemAddedInvoiceSavedAutomatically = "NO" Then
                Dim BillnoAutonumber As String = mykey.GetValue("BillnoAutonumber", "YES")
                If BillnoAutonumber = "NO" Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select count(*) from Sales_Bill where bill_no =N'" & lblOrderNO.Text.Trim & "'" : H = cmd.ExecuteScalar
                    If H > 0 Then
                        MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : lblOrderNO.Focus() : Return False
                    End If
                End If
            End If


            For i As Integer = 0 To Dt_AddBill.Rows.Count - 1
                If Dgv_Add.Rows(i).Cells(0).Value = txtprc.Text.Trim Then MsgBox("صنف مكرر بنفس الفاتورة", MsgBoxStyle.Exclamation) : txtprc.Focus() : txtprc.SelectAll() : Return False
            Next

            If ItemAddedInvoiceSavedAutomatically = "NO" Then
                If UseOnlySales <> "YES" Then
                    Dim Xstore As Double
                    Dim MM As Long
                    For i As Integer = 0 To Dt_AddBill.Rows.Count - 1
                        Xstore = IM.Get_Itm_Store(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value)
                        If UseOnlySales <> "YES" Then
                            If Val(Dgv_Add.Rows(i).Cells(5).Value) > Xstore Then
                                MM += 1
                                Dgv_Add.Rows(i).DefaultCellStyle.BackColor = Color.Red
                                Dgv_Add.Rows(i).DefaultCellStyle.ForeColor = Color.White
                            Else
                                Dgv_Add.Rows(i).DefaultCellStyle.BackColor = Color.White
                                Dgv_Add.Rows(i).DefaultCellStyle.ForeColor = Color.Black
                            End If
                        End If
                    Next
                    If MM > 0 Then
                        MsgBox("الكمية بالمخزن لا تكفي الكمية المباعة", MsgBoxStyle.Critical)
                        Return False
                    End If
                End If
            End If

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        Return True
    End Function

    Private Sub GetFocusText()
        If FocusText = "YES" Then
            txtprc.Focus()
        End If
        If FocusText = "NO" Then
            cmbname.Focus()
        End If
        If FocusText = "SearchFree" Then
            cmbname.Focus()
        End If
    End Sub

    Private Sub btnDelete_Items_Click(sender As Object, e As EventArgs) Handles btnDelete_Items.Click
        Try
            If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
            If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            For i As Integer = 0 To Dgv_Add.SelectedRows.Count - 1
                RNXD = Dgv_Add.CurrentRow.Index
                Dgv_Add.Rows.RemoveAt(RNXD)
            Next
            SumAllPrice()
            sumdisc()
            txtCountItems.Text = Dgv_Add.Rows.Count
            If Dgv_Add.Rows.Count = 0 Then
                ClearSave()
                MAXRECORD()
                PriceOfferBill = False
            End If

            GetFocusText()

        Catch ex As Exception
            'ErrorHandling(ex, Me.Text)
        End Try

    End Sub

    Friend Function Fn_AddBill(ByVal Col_Prc As String, ByVal Col_Cats As String, ByVal Col_Name As String _
, ByVal Col_Price As Double, ByVal Col_Quant As Double, ByVal Col_qu_unity As Double, ByVal Col_Unity As String, ByVal Col_Total As Double, ByVal Col_Store As String, ByVal Col_Dsic As Double, ByVal Col_Notes As String, ByVal Col_WholeWholePrice As String, ByVal Col_Height As Double, ByVal Col_Width As Double, ByVal Col_Altitude As Double, ByVal Col_Density As Double, ByVal Col_ValueVAT As Double, ByVal Col_TotalBeforeVAT As Double, ByVal Col_ItemsRateVAT As Double, ByVal Col_DiscountsValue As Double, ByVal Col_UnitPriceAfterDisc As Double, ByVal Col_Offersbill_No As Double, ByVal Col_Offers_Qunt As Double, ByVal Col_PriceOffersBefore As Double, ByVal Col_PriceOffersAfter As Double, ByVal Col_NumberOffersbill_No As Double, ByVal Col_TotalOffersQunt As Double, ByVal Col_Offers_Qunt_Loop As Double, ByVal Col_bill_EndDate As String, ByVal Col_bill_no_Expired As Double, ByVal Col_StateDisc As String, ByVal Col_Total2 As String) As DataTable
        'Try
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("الباركود", GetType(String))
            Dt_AddBill.Columns.Add("المجموعة", GetType(String))
            Dt_AddBill.Columns.Add("أسم الصنف", GetType(String))
            Dt_AddBill.Columns.Add("السعر", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية1", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية", GetType(Double))
            Dt_AddBill.Columns.Add("الوحدة", GetType(String))
            Dt_AddBill.Columns.Add("إجمالي", GetType(Double))
            Dt_AddBill.Columns.Add("المخزن", GetType(String))
            Dt_AddBill.Columns.Add("الخصم", GetType(Double))
            Dt_AddBill.Columns.Add("ملاحظات", GetType(String))
            Dt_AddBill.Columns.Add("سعر جملة الجملة", GetType(String))
            Dt_AddBill.Columns.Add("Height", GetType(String))
            Dt_AddBill.Columns.Add("Width", GetType(String))
            Dt_AddBill.Columns.Add("Altitude", GetType(String))
            Dt_AddBill.Columns.Add("Density", GetType(String))
            Dt_AddBill.Columns.Add("قيمة الضريبة", GetType(Double))
            Dt_AddBill.Columns.Add("قبل الضريبة", GetType(Double))
            Dt_AddBill.Columns.Add("نسبة الضريبة", GetType(Double))
            Dt_AddBill.Columns.Add("قيمة الخصم", GetType(Double))
            Dt_AddBill.Columns.Add("سعر البيع", GetType(Double))
            Dt_AddBill.Columns.Add("Offersbill_No", GetType(Double))
            Dt_AddBill.Columns.Add("Offers_Qunt", GetType(Double))
            Dt_AddBill.Columns.Add("PriceOffersBefore", GetType(Double))
            Dt_AddBill.Columns.Add("PriceOffersAfter", GetType(Double))
            Dt_AddBill.Columns.Add("NumberOffersbill_No", GetType(Double))
            Dt_AddBill.Columns.Add("TotalOffersQunt", GetType(Double))
            Dt_AddBill.Columns.Add("Offers_Qunt_Loop", GetType(Double))
            Dt_AddBill.Columns.Add("تاريخ الصلاحية", GetType(String))
            Dt_AddBill.Columns.Add("bill_no_Expired", GetType(Double))
            Dt_AddBill.Columns.Add("نسبة او قيمة خصم الصنف", GetType(String)) '30
            Dt_AddBill.Columns.Add("اجمالي", GetType(String)) '31
        End If

        DTV_Width()

        Dt_AddBill.Rows.Add(Col_Prc, Col_Cats, Col_Name, Col_Price, Col_Quant, Col_qu_unity, Col_Unity, Col_Total, Col_Store, Col_Dsic, Col_Notes, Col_WholeWholePrice, Col_Height, Col_Width, Col_Altitude, Col_Density, Col_ValueVAT, Col_TotalBeforeVAT, Col_ItemsRateVAT, Col_DiscountsValue, Col_UnitPriceAfterDisc, Col_Offersbill_No, Col_Offers_Qunt, Col_PriceOffersBefore, Col_PriceOffersAfter, Col_NumberOffersbill_No, Col_TotalOffersQunt, Col_Offers_Qunt_Loop, Col_bill_EndDate, Col_bill_no_Expired, Col_StateDisc, Col_Total2)
        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
        Return Dt_AddBill
    End Function

    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        On Error Resume Next
        SlpitTime()
        txtTimeAMPM.Text = TimeAmBm
        TxtHour.Text = Cls.get_time(True)
        TxtDay.Text = Return_D_Week(DateTimePicker1.Text)
    End Sub

    Private Sub Dgv_Add_CellValueChanged(sender As Object, e As DataGridViewCellEventArgs) Handles Dgv_Add.CellValueChanged
        Try
            'If Bol <> True Then

            If OrdersOnlineStoreAdd = False Then

                    If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
                    If (Dgv_Add.Rows.Count) = 0 Then Beep() : Exit Sub
                    Dim ITMID As String = Dgv_Add.SelectedRows(0).Cells(0).Value
                    Dim Stores As String = Dgv_Add.SelectedRows(0).Cells(8).Value
                    Dim Unity As String = Dgv_Add.SelectedRows(0).Cells(6).Value
                    Dim SalPrice As String = Dgv_Add.SelectedRows(0).Cells(3).Value
                    'SumOffersDiscounts()


                    Dim MinimumSalPrice As Double
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select MinimumSalPrice from items where itm_id=N'" & ITMID & "' and Stores=N'" & Stores & "'"
                    dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then
                        MinimumSalPrice = dr(0)
                        If Val(SalPrice) < MinimumSalPrice Then
                            MsgBox("سعر التجزئة وصل للحد الادنى", MsgBoxStyle.Information)
                            'WholeasalePrice(ITMID, Stores)
                            'Dgv_Add.SelectedRows(0).Cells(3).Value = txtprice.Text
                            'txtprice.Text = ""
                            Exit Sub
                        End If
                    End If

                If BalanceBarcode <> "1" Then
                    If NotUnityItemsProgram = "YES" Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & ITMID & "' and Unity_Name=N'" & Unity & "'" : dr = cmd.ExecuteReader : dr.Read()
                        If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 1 : End If
                        Dgv_Add.SelectedRows(0).Cells(4).Value = Val(NumberPieces) * Val(Dgv_Add.SelectedRows(0).Cells(5).Value)
                    Else
                        Dgv_Add.SelectedRows(0).Cells(4).Value = Val(Dgv_Add.SelectedRows(0).Cells(5).Value)
                    End If
                Else
                    Dgv_Add.SelectedRows(0).Cells(4).Value = Val(Dgv_Add.SelectedRows(0).Cells(5).Value)
                End If


                    Dim qunt As String = Dgv_Add.SelectedRows(0).Cells(4).Value

                    SumAllPrice()

                    Dim SM As Double
                    For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                        SM = SM + Dgv_Add.Rows(i).Cells(7).Value
                    Next
                    txttotalpeforedisc.Text = SM : ToolTip1.SetToolTip(txttotalpeforedisc, FormatNumberWithSeparators(SM))

                    Dim Xstore As Double = IM.Get_Itm_Store(ITMID, Stores)
                    If UseOnlySales <> "YES" Then
                        If StoreSales = False Then
                            StoreSales = True
                            If Val(qunt) > Xstore Then
                                MsgBox("الكمية بالمخزن لا تكفي الكمية المباعة", MsgBoxStyle.Critical)
                            End If
                        End If
                    End If

                    Try
                        For i As Integer = 0 To Dgv_Add.SelectedRows.Count - 1
                            If ItemAddedInvoiceSavedAutomatically = "YES" Then
                                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                                cmd.CommandText = "select bill_No from " & BillsalData & " where bill_No=N'" & lblOrderNO.Text.Trim & "' and itm_id=N'" & Dgv_Add.SelectedRows(i).Cells(0).Value & "'" : dr = cmd.ExecuteReader : dr.Read()
                                If dr.HasRows = True Then
                                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                                    cmd.CommandText = "update " & BillsalData & " set qu =N'" & Dgv_Add.SelectedRows(i).Cells(4).Value & "',qu_unity =N'" & Dgv_Add.SelectedRows(i).Cells(5).Value & "' where bill_No =N'" & lblOrderNO.Text & "'" : cmd.ExecuteNonQuery()
                                End If
                                IM.Store(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value)
                            End If
                        Next
                    Catch ex As Exception
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    End Try

                End If
            'End If
            GetFocusText()
        Catch ex As Exception
            'ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub txtprc_KeyDown(sender As Object, e As KeyEventArgs) Handles txtprc.KeyDown
        If ((e.KeyCode = 8)) Then
            txtprc.SelectAll()
        End If
        GetKeyDown(sender, e)
    End Sub

    Private Sub GetKeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbname.KeyDown

        If ((e.KeyCode = Keys.F4)) Then
            GetSelectUserPermtions("تسجيل مرتجعات مبيعات")
            If UserPermtionsAction = True Then
                Frm_IM_BSal.Show()
            End If
        End If

        If ((e.KeyCode = Keys.F5)) Then
            btnPrintChackEnd.PerformClick()
        End If

        If ((e.KeyCode = Keys.F6)) Then
            btnChackEnd.PerformClick()
        End If

        If ((e.KeyCode = Keys.F7)) Then
            Dim BillnoAutonumber As String = mykey.GetValue("BillnoAutonumber", "YES")
            If BillnoAutonumber = "YES" Then
                Dim newForm As New List(Of FrmSalesTouch)
                newForm.Add(New FrmSalesTouch)
                newForm(0).Show()
            End If
        End If

        If ((e.KeyCode = Keys.F8)) Then
            Dim BillnoAutonumber As String = mykey.GetValue("BillnoAutonumber", "YES")
            If BillnoAutonumber = "YES" Then
                Dim newForm As New List(Of FrmSales)
                newForm.Add(New FrmSales)
                newForm(0).Show()
            Else
                FrmSales.Show()
            End If
        End If

        If ((e.KeyCode = Keys.F10)) Then
            ' الشيفت
            If DealingSheftStatusSales = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select count(*) from Sheft_Status" : H = cmd.ExecuteScalar
                If H = 0 Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Users set Sheft_Stat = 0 where UserName=N'" & UserName & "'" : cmd.ExecuteNonQuery()
                    MAXRECORD_SheftNumber(True)
                    Cls.insert("Sheft_Status", "Sheft_Number,Sheft_Date,UserName,Sheft_Stat", "N'" & Sheft_Number & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & UserName & "',0")
                    Cash_Out_IN_Button = "Close"
                    txtSheft_Number.Text = Sheft_Number
                End If


                lblSheft_Number.Text = txtSheft_Number.Text
                ButtonAction = True
                TimeAmBm = txtTimeAMPM.Text
                Cash_Out_IN = "أنهاء شيفت"
                ButtonAction = False
                FrmSales_Cash_Out.ShowDialog()
            End If
        End If

        'If ((e.KeyCode = Keys.F) AndAlso (e.Modifiers = (Keys.Control))) Then
        '    btnSearchItems_Click(sender, e)
        'End If

        If ((e.KeyCode = Keys.S) AndAlso (e.Modifiers = (Keys.Control))) Then
            GetSelectUserPermtions("عرض وطباعة المبيعات")
            If UserPermtionsAction = True Then
                FrmShowSales.Show()
            End If
        End If

        If ((e.KeyCode = Keys.R) AndAlso (e.Modifiers = (Keys.Control))) Then
            GetSelectUserPermtions("تسجيل مرتجعات مبيعات")
            If UserPermtionsAction = True Then
                Frm_IM_BSal.MdiParent = Me
                Frm_IM_BSal.Show()
            End If
        End If

        If ((e.KeyCode = Keys.D) AndAlso (e.Modifiers = (Keys.Control))) Then
            GetSelectUserPermtions("حذف وتعديل المبيعات")
            If UserPermtionsAction = True Then
                FrmEditSales.MdiParent = Me
                FrmEditSales.Show()
            End If
        End If

        If ((e.KeyCode = Keys.I) AndAlso (e.Modifiers = (Keys.Control))) Then
            GetSelectUserPermtions("تسجيل بيانات العملاء")
            If UserPermtionsAction = True Then
                Customer.MdiParent = Me
                Customer.Show()
            End If
        End If

        If ((e.KeyCode = Keys.T) AndAlso (e.Modifiers = (Keys.Control))) Then
            GetSelectUserPermtions("تقرير بالمبيعات اليومية")
            If UserPermtionsAction = True Then
                Frm_daySales.Show()
            End If
        End If

        If ((e.KeyCode = Keys.M) AndAlso (e.Modifiers = (Keys.Control))) Then
            GetSelectUserPermtions("تسجيل  أصناف")
            If UserPermtionsAction = True Then
                FrmItemsNew.Show()
            End If
        End If

        If ((e.KeyCode = Keys.U) AndAlso (e.Modifiers = (Keys.Control))) Then
            GetSelectUserPermtions("تعديل أصناف")
            If UserPermtionsAction = True Then
                Frm_Edit_Item.Show()
            End If
        End If

        If ((e.KeyCode = Keys.W) AndAlso (e.Modifiers = (Keys.Control))) Then
            GetSelectUserPermtions("بحث عن الاصناف")
            If UserPermtionsAction = True Then
                FrmItemSearch.Show()
            End If
        End If

        If ((e.KeyCode = Keys.P) AndAlso (e.Modifiers = (Keys.Control))) Then
            GetSelectUserPermtions("إجراء عملية مشتريات")
            If UserPermtionsAction = True Then
                Frmimport.Show()
            End If
        End If

        If ((e.KeyCode = Keys.O) AndAlso (e.Modifiers = (Keys.Control))) Then
            GetSelectUserPermtions("تسجيل بيانات موردين")
            If UserPermtionsAction = True Then
                frmvendors.Show()
            End If
        End If

        If ((e.KeyCode = Keys.L) AndAlso (e.Modifiers = (Keys.Control))) Then
            GetSelectUserPermtions("مدفوعات الموردين")
            If UserPermtionsAction = True Then
                Vendor_pay.Show()
            End If
        End If

        If ((e.KeyCode = Keys.Y) AndAlso (e.Modifiers = (Keys.Control))) Then
            GetSelectUserPermtions("تسجيل مرتجعات مشتريات")
            If UserPermtionsAction = True Then
                Frm_IM_BTin.Show()
            End If
        End If

        If ((e.KeyCode = Keys.K) AndAlso (e.Modifiers = (Keys.Control))) Then
            GetSelectUserPermtions("تسجيل مصروفات")
            If UserPermtionsAction = True Then
                outcome.Show()
            End If
        End If

    End Sub

    Private Sub btnChackEnd_Click(sender As Object, e As EventArgs) Handles btnChackEnd.Click
        If ActivatePaidAndRest = False Then
            If chkActivatePaidAndRest.Checked = True Then
                ActivatePrintChackEnd = False
                PanelPayingStaying.Location = New System.Drawing.Point(20, 76)
                PanelPayingStaying.Size = New System.Drawing.Size(325, 195)
                txtpayingWindow.Focus()
                txtpayingWindow.SelectAll()
                Exit Sub
            End If
        End If

        If NetworkName = "Yes" Then : If UseExternalServer = "Yes" Then : connect() : End If : End If

        If ValidateTextSave() = False Then
            ValidateTextSaveTrue = True
            Exit Sub
        End If

        MAXRECORD()

        Save_Bill()

        IM.CustomerAccountTotal(cmbvendores.Text)

        '===============================================================================
        Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
        If UseAccounts = "YES" Then
            Daily_Restrictions()
        End If
        '===============================================================================

        If ValidateTextSaveTrue = True Then
            ValidateTextSaveTrue = False
            Exit Sub
        End If

        ViewScreenNCR(txttotalafterdisc.Text)

        Get_Movement_In_Out_Money(DateTimePicker1.Text, Treasury_Code_ID)

        Dt_AddBill.Rows.Clear()
        ClearSave()
        MAXRECORD()
        OrdersOnlineStore = False
        GetFocusText()

    End Sub

    Private Sub cmbname_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbname.SelectedIndexChanged
        If cmbname.Text.Trim <> "" Then
            Bol = False
            ButtonAction = False
            ComboboxAction = True

            items = cmbname.Text
            Try
                If QuntText.ToString() = "" Or QuntText = 0 Then
                    QuntText = 1
                End If
            Catch ex As Exception
                QuntText = 1
            End Try

            GetDataSales("Items")

            GetAddItemsGrid()

            QuntText = 0
            cmbname.Text = ""
            Bol = False
            ComboboxAction = False

        End If

    End Sub

    Private Sub txtpayingWindow_TextChanged(sender As Object, e As EventArgs) Handles txtpayingWindow.TextChanged
        txtstayingWindow.Text = Val(txtpayingWindow.Text) - Val(txttotalafterdisc.Text)
    End Sub

    Private Sub btnClosePanelPayingStaying_Click(sender As Object, e As EventArgs) Handles btnClosePanelPayingStaying.Click
        PanelPayingStaying.Top = 5000
        txtpayingWindow.Text = "0"
    End Sub

    Private Sub btnOKPayingStaying_Click(sender As Object, e As EventArgs) Handles btnOKPayingStaying.Click
        ActivatePaidAndRest = True
        If ActivatePrintChackEnd = True Then
            btnPrintChackEnd_Click(sender, e)
        End If
        If ActivatePrintChackEnd = False Then
            btnChackEnd_Click(sender, e)
        End If
        PanelPayingStaying.Top = 5000
    End Sub

    Private Sub txtpayingWindow_KeyUp(sender As Object, e As KeyEventArgs) Handles txtpayingWindow.KeyUp
        If e.KeyCode = 13 Then
            btnOKPayingStaying.PerformClick()
        End If
    End Sub

    Private Sub chkActivatePaidAndRest_CheckedChanged(sender As Object, e As EventArgs) Handles chkActivatePaidAndRest.CheckedChanged
        Dim XActivatePaidAndRestAll As String = ""
        If chkActivatePaidAndRest.Checked = True Then
            XActivatePaidAndRestAll = "YES"
        Else
            XActivatePaidAndRestAll = "NO"
        End If
        mykey.SetValue("ActivatePaidAndRestAll", XActivatePaidAndRestAll)
        ActivatePaidAndRestAll = XActivatePaidAndRestAll
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        FrmItemsNew.ShowDialog()
    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        Frm_Edit_Item.ShowDialog()

    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs) Handles Button3.Click
        Frmimport.ShowDialog()
    End Sub

    Private Sub Button4_Click(sender As Object, e As EventArgs) Handles Button4.Click
        Vendor_pay.ShowDialog()
    End Sub

    Private Sub Button7_Click(sender As Object, e As EventArgs) Handles Button7.Click
        Frm_daySales.ShowDialog()
    End Sub

    Private Sub Button6_Click(sender As Object, e As EventArgs) Handles Button6.Click
        Frm_IM_in_out_Money.ShowDialog()
    End Sub

    Private Sub Button5_Click(sender As Object, e As EventArgs) Handles Button5.Click
        ' الشيفت
        If DealingSheftStatusSales = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Sheft_Status" : H = cmd.ExecuteScalar
            If H = 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Users set Sheft_Stat = 0 where UserName=N'" & UserName & "'" : cmd.ExecuteNonQuery()
                MAXRECORD_SheftNumber(True)
                Cls.insert("Sheft_Status", "Sheft_Number,Sheft_Date,UserName,Sheft_Stat", "N'" & Sheft_Number & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & UserName & "',0")
                Cash_Out_IN_Button = "Close"
                txtSheft_Number.Text = Sheft_Number
            End If


            lblSheft_Number.Text = txtSheft_Number.Text
            ButtonAction = True
            TimeAmBm = txtTimeAMPM.Text
            Cash_Out_IN = "أنهاء شيفت"
            ButtonAction = False
            FrmSales_Cash_Out.ShowDialog()
        End If
    End Sub

    Private Sub Button8_Click(sender As Object, e As EventArgs) Handles Button8.Click
        GetSelectUserPermtions("تسجيل مرتجعات مبيعات")
        If UserPermtionsAction = True Then
            Frm_IM_BSal.Show()
        End If
    End Sub

    Function Return_D_Week(ByVal DT As Date) As String
        Dim XMN
        Dim XDAY
        XMN = DT.DayOfWeek

        If XMN = 1 Then XDAY = "الأثنين"
        If XMN = 2 Then XDAY = "الثلاثاء"
        If XMN = 3 Then XDAY = "الأربعاء"
        If XMN = 4 Then XDAY = "الخميس"
        If XMN = 5 Then XDAY = "الجمعة"
        If XMN = 6 Then XDAY = "السبت"
        If XMN = 0 Then XDAY = "الأحد"
        Return XDAY
    End Function

    Private Sub txtprc_KeyUp(sender As Object, e As KeyEventArgs) Handles txtprc.KeyUp
        If e.KeyCode = 13 Then
            If txtprc.Text.Trim <> "" Then
                ButtonAction = False
                Try
                    If QuntText.ToString() = "" Or QuntText = 0 Then
                        QuntText = 1
                    End If
                Catch ex As Exception
                    QuntText = 1
                End Try

                prc = txtprc.Text
                items = Cls.Select_More_Data_Stores("items", "sname", "itm_id=N'" & prc & "' and QuickSearch=0")

                GetDataParcode()

                GetAddItemsGrid()

                QuntText = 0
            End If
        End If
    End Sub

    Private Sub btnSelectPrinter_Click(sender As Object, e As EventArgs) Handles btnSelectPrinter.Click
        PanelSelectPrinter.Location = New System.Drawing.Point(20, 76)
        PanelSelectPrinter.Size = New System.Drawing.Size(300, 205)
    End Sub

    Friend Sub DTV_Width()
        Try
            If Dgv_Add.Rows.Count > 10000 Then
                'Dgv_Add.Columns(0).Width = 70
                'Dgv_Add.Columns(1).Width = 90
                'Dgv_Add.Columns(2).Width = 130
                'Dgv_Add.Columns(3).Width = 60
                'Dgv_Add.Columns(4).Width = 60
                'Dgv_Add.Columns(5).Width = 75
                'Dgv_Add.Columns(6).Width = 70
                'Dgv_Add.Columns(7).Width = 70
                'Dgv_Add.Columns(8).Width = 70
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub btnCloseSelectPrinter_Click(sender As Object, e As EventArgs) Handles btnCloseSelectPrinter.Click
        PanelSelectPrinter.Top = 5000
        GetFocusText()

    End Sub

    Function ValidateTextSaveAdd(ByVal prc As String, ByVal Store As String, ByVal qu As String) As Boolean
        'Try
        If ButtonAction = False Then

                If txtprc.Text <> "" Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select * from items where itm_id=N'" & txtprc.Text & "'"
                    dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = False Then
                        'MsgBox("الباركود غير مسجل مسبقا من فضلك راجع الباركود ", MsgBoxStyle.Exclamation)
                        Return False
                    End If
                    GoTo 1
                End If


                If cmbname.Text <> "" Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select * from items where sname=N'" & cmbname.Text & "'"
                    dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = False Then
                        'MsgBox("الصنف غير مسجل مسبقا من فضلك راجع الصنف ", MsgBoxStyle.Exclamation)
                        Return False
                    End If
                End If
            End If
1:

            If UseOnlySales = "YES" Then
                Dim AllowItemStock As String = ""
                Dim Xstore As Double
                Dim MM As Long

            'AllowItemStock = Cls.Get_Code_Value_More("items", "AllowItemStock", "itm_id =N'" & prc & "'")
            'If AllowItemStock = True Then
            '        Xstore = IM.Get_Itm_Store(prc, Store)
            '        If Val(qu) > Xstore Then
            '            MM += 1
            '        End If
            '    End If


            'For i As Integer = 0 To Dt_AddBill.Rows.Count - 1
            '    AllowItemStock = Cls.Get_Code_Value_More("items", "AllowItemStock", "itm_id =N'" & prc & "'")
            '    If AllowItemStock = True Then
            '        Xstore = IM.Get_Itm_Store(prc, Store)
            '        If Val(qu) > Xstore Then
            '            MM += 1
            '        End If
            '    End If
            'Next
            If MM > 0 Then
                    MsgBox("الكمية بالمخزن لا تكفي الكمية المباعة", MsgBoxStyle.Critical)
                    Return False
                End If
            End If

        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try

        Return True
    End Function

    Private Sub SplitFromArgb(ByVal FromArgb As String)
        On Error Resume Next
        Dim PRC As String = FromArgb
        Dim split As String() = New String() {","}
        Dim itemsSplit As String() = PRC.Split(split, StringSplitOptions.None)
        FromArgb1 = itemsSplit(0).ToString()
        FromArgb2 = itemsSplit(1).ToString()
        FromArgb3 = itemsSplit(2).ToString()
    End Sub

    Private Sub GridViewOpenForm()
        Dgv_Add.DataSource = Fn_AddBill(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)
        If DataGridViewWidth = False Then
            Dgv_Add.Columns(2).Width = 30
            Dgv_Add.Columns(3).Width = 15
            Dgv_Add.Columns(5).Width = 15
            Dgv_Add.Columns(7).Width = 15

            GetWidthVisibleReadOnly()
            DataGridViewWidth = True
        End If

        Dt_AddBill.Rows.Clear()
    End Sub

    Private Sub GetWidthVisibleReadOnly()
        'Dgv_Add.Columns(2).Width = 120
        'Dgv_Add.Columns(3).Width = 33
        'Dgv_Add.Columns(4).Width = 33
        'Dgv_Add.Columns(5).Width = 33
        Dgv_Add.Columns(0).ReadOnly = True
        Dgv_Add.Columns(1).ReadOnly = True
        Dgv_Add.Columns(2).ReadOnly = True
        Dgv_Add.Columns(4).ReadOnly = True
        Dgv_Add.Columns(6).ReadOnly = True
        Dgv_Add.Columns(7).ReadOnly = True

        Dgv_Add.Columns(0).Visible = False
        Dgv_Add.Columns(1).Visible = False
        Dgv_Add.Columns(4).Visible = False
        Dgv_Add.Columns(6).Visible = False
        Dgv_Add.Columns(8).Visible = False
        Dgv_Add.Columns(9).Visible = False
        Dgv_Add.Columns(10).Visible = False
        Dgv_Add.Columns(11).Visible = False
        Dgv_Add.Columns(12).Visible = False
        Dgv_Add.Columns(13).Visible = False
        Dgv_Add.Columns(14).Visible = False
        Dgv_Add.Columns(15).Visible = False
        Dgv_Add.Columns(16).Visible = False
        Dgv_Add.Columns(17).Visible = False
        Dgv_Add.Columns(18).Visible = False
        Dgv_Add.Columns(19).Visible = False
        Dgv_Add.Columns(20).Visible = False
        Dgv_Add.Columns(21).Visible = False
        Dgv_Add.Columns(22).Visible = False
        Dgv_Add.Columns(23).Visible = False
        Dgv_Add.Columns(24).Visible = False
        Dgv_Add.Columns(25).Visible = False
        Dgv_Add.Columns(26).Visible = False
        Dgv_Add.Columns(27).Visible = False
        Dgv_Add.Columns(28).Visible = False
        Dgv_Add.Columns(29).Visible = False
        Dgv_Add.Columns(30).Visible = False

        If TextNotActivateEditSalesPrice = "YES" Then
            Dgv_Add.Columns(3).ReadOnly = True
        Else
            Dgv_Add.Columns(3).ReadOnly = False
        End If
        If ColorWithItems = "NO" Then
            Dgv_Add.Columns(10).ReadOnly = True
        Else
            Dgv_Add.Columns(10).Visible = False
        End If
        If NotUnityItemsProgram = "YES" Then
            'Dgv_Add.Columns(5).ReadOnly = True
        End If

        'If ShowDiscountRateItemSales = "NO" Then
        '    Dgv_Add.Columns(3).Visible = True
        '    Dgv_Add.Columns(20).Visible = False
        'Else
        '    Dgv_Add.Columns(3).Visible = False
        '    Dgv_Add.Columns(20).Visible = True
        'End If
        If NoSalesAllowedTheMinimumQuantity = "YES" Then
            Dgv_Add.Columns(5).ReadOnly = True
        End If
        If ActivateFormatNumberWithSeparators = "YES" Then
            Dgv_Add.Columns(31).Visible = True
            Dgv_Add.Columns(7).Visible = False
        Else
            Dgv_Add.Columns(31).Visible = False
            Dgv_Add.Columns(7).Visible = True
        End If
    End Sub

    Private Sub ClearAdd()
        cmbname.Text = ""
        txtprc.Text = ""
        QuntText = "1"
    End Sub

    Private Sub Daily_Restrictions()
        Dim Account As String = "" : Dim AccountCode As String = "" : Dim AccountPaying As String = "" : Dim PayingCode As String = ""
        Dim Discounts As String = "" : Dim DiscountsCode As String = "" : Dim AccountTreasury As String = "" : Dim AccountCodeTreasury As String = ""

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مبيعات'" : dr = cmd.ExecuteReader
            If dr.Read Then
                Account = dr("Link_AccountsTree") : AccountCode = dr("ACCNumber")
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مقبوضات عملاء'" : dr = cmd.ExecuteReader
            If dr.Read Then
                AccountPaying = dr("Link_AccountsTree") : PayingCode = dr("ACCNumber")
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'خصومات عملاء'" : dr = cmd.ExecuteReader
            If dr.Read Then
                Discounts = dr("Link_AccountsTree") : DiscountsCode = dr("ACCNumber")
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select ACCName,ACCNumber from AccountsTree where ACCName=N'الخزينة'" : dr = cmd.ExecuteReader
            If dr.Read Then
                AccountTreasury = dr("ACCName") : AccountCodeTreasury = dr("ACCNumber")
            End If

            '========================================================================================

            Dim MOVID As String = Cls.MAXRECORD("MOVES", "MOVID")
            Dim MOVRegNumber As String = Cls.MAXRECORD("MOVES", "MOVRegNumber")

            'If ChkCash.Checked = True Or chkVisa.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
            S = S & "N'" & lblOrderNO.Text & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Account & "',N'" & txttotalpeforedisc.Text & "',N'" & txttotalpeforedisc.Text & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
            'End If

            'If ChkState.Checked = True Then
            '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '    S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
            '    S = S & "N'" & txtbillno.Text & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Account & "',N'" & txttotalpeforedisc.Text & "',N'" & txttotalpeforedisc.Text & "',N'" & UserName & "')"
            '    cmd.CommandText = S : cmd.ExecuteNonQuery()
            'End If

            If Val(txttotalafterdisc.Text) > 0 Then

                ' من حساب / الخزينة
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & lblOrderNO.Text & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'" & txttotalafterdisc.Text & "',N'0',N'" & AccountTreasury & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' الى حساب / مقبوضات عملاء
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & lblOrderNO.Text & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & PayingCode & "',N'" & AccountPaying & "',N'0',N'" & txttotalafterdisc.Text & "',N'" & AccountPaying & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If

            If Val(txtdisc.Text) > 0 Then
                'Dim bill_no As String = Cls.MAXRECORD("Vst_disc", "id") - 1

                ' من حساب / خصومات المبيعات
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & lblOrderNO.Text & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & DiscountsCode & "',N'" & Discounts & "',N'" & txtdisc.Text & "',N'0',N'" & Discounts & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' الى حساب / الخزينة
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & lblOrderNO.Text & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'0',N'" & txtdisc.Text & "',N'" & AccountTreasury & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Public Sub AverageTinPrice(ByVal itm_id As String, ByVal Stores As String, ByVal Price As Double, ByVal Qunt As Double, ByVal Unity As String, ByVal DiscountRate As Double, ByVal PriceDiscount As Double)
        Try
            Dim PriceAverage As String = 0
            TinPriceAverage = 0
            Dim Xqunt As Double = Qunt

            If NotUnityItemsProgram = "YES" Then
                PriceAverage = Cls.Get_Code_Value_Branch_More("ItemsUnity", "TinPriceUnit", "itm_id=N'" & itm_id & "' and Unity_Name=N'" & Unity & "'")
                TinPrice = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)

                Price = Cls.Get_Code_Value_Branch_More("ItemsUnity", "SalPriceUnit", "itm_id=N'" & itm_id & "' and Unity_Name=N'" & Unity & "'")
                Price = Math.Round(Convert.ToDouble(Val(Price)), 2)
            Else
                PriceAverage = Cls.Get_Code_Value_Stores_More("items", "TinPriceAverage", "itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'")
                TinPriceAverage = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)

                PriceAverage = Cls.Get_Code_Value_Stores_More("items", "TinPrice", "itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'")
                TinPrice = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)
            End If

            'If NotUnityItemsProgram = "YES" Then
            '    NumberPieces = Cls.Get_Code_Value_Stores_More("ItemsUnity", "NumberPieces", "itm_id =N'" & itm_id & "' and Unity_Name=N'" & Unity & "'")
            '    If NumberPieces <> 1 Then
            '        Price = Cls.Get_Code_Value_Branch_More("ItemsUnity", "SalPriceUnit", "itm_id=N'" & itm_id & "' and Unity_Name=N'" & Unity & "'")
            '        If NumberPieces <> 0 Then
            '            Xqunt = Val(NumberPieces) * Val(Qunt)
            '        End If
            '    End If
            'End If


            Price_Unity = Price

            If DiscountRate <> 0 Then
                Price = PriceDiscount
                Price_Unity = PriceDiscount
            End If

            If PriceAverage = "" Then : PriceAverage = 0 : End If

            If LastTinPriceItems = "NO" Then : PriceAverage = TinPriceAverage : Else PriceAverage = TinPrice : End If
            If TinPriceAverage = 0 Then
                PriceAverage = TinPrice
                TinPriceAverage = TinPrice
            End If

            TotalPrice = Price - PriceAverage
            Profits = TotalPrice * Xqunt
            Profits = Math.Round(Profits, 2)


            If DiscountRate <> 0 Then
                If DiscountsTin <> 0 Then
                    Dim TotalDiscountRate As Double = DiscountsTin - DiscountRate
                    Dim TotalRate As Double = Format(Val(PriceAverage) * Val(TotalDiscountRate) / 100, "Fixed")
                    Profits = Format(Val(Xqunt) * Val(TotalRate))
                    Profits = Math.Round(Profits, 2)
                End If
            End If

            'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            'cmd.CommandText = "update Items set TinPriceAverage = " & Val(PriceAverage) & " where itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'" : cmd.ExecuteNonQuery()

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub Store_Name()
        cmbStores.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select distinct store from stores order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            cmbStores.Items.Add(Trim(dr(0)))
        Loop
    End Sub

    Private Sub GetSelectUserPermtions(ByVal text As String)
        Try
            Dim aray_1 As New ArrayList : aray_1.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select PermtionName from  Permtions where UserID =N'" & UserID & "'"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_1.Add(dr("PermtionName"))
            Loop
            UserPermtionsAction = False
            Dim PermtionName As String = ""
            For i As Integer = 0 To aray_1.Count - 1
                PermtionName = aray_1(i).ToString()
                If PermtionName = "مدير" Then
                    UserPermtionsAction = True
                Else
                    If PermtionName = text Then
                        UserPermtionsAction = True
                    End If
                End If
            Next
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub


    Private Sub Check_Sheft_Status()
        Dim Sheft_Stat As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select  Sheft_Stat from Users where  UserName =N'" & UserName & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Sheft_Stat = dr(0).ToString
        End If


        If Sheft_Stat = "0" Then
            'btnCash_IN.Enabled = False
            'btnCash_Out.Enabled = True

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select  Sheft_Number from Sales_Bill where  CloseSheft =N'0' and UserName =N'" & UserName & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Sheft_Number = dr(0).ToString
                txtSheft_Number.Text = Sheft_Number
            End If
        Else
            'btnCash_IN.Enabled = True
            'btnCash_Out.Enabled = False

            MAXRECORD_SheftNumber(True)
            txtSheft_Number.Text = Sheft_Number
        End If

        lblSheft_Number.Text = Sheft_Number
    End Sub

    Private Sub SelectUserPermtions()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select UserID from  Users where UserName =N'" & UserName & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            UserID = dr("UserID")
        End If

        Dim aray_1 As New ArrayList
        aray_1.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select PermtionName from  Permtions where UserID =N'" & UserID & "'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr("PermtionName"))
        Loop

        Dim PermtionName As String = ""
        For i As Integer = 0 To aray_1.Count - 1
            PermtionName = aray_1(i).ToString()
            If PermtionName = "مدير" Then
                Button1.Enabled = True
                Button2.Enabled = True
                Button3.Enabled = True
                Button4.Enabled = True
                Button5.Enabled = True
                Button6.Enabled = True
                Button7.Enabled = True
                Button8.Enabled = True
            Else
                If PermtionName = "تسجيل  أصناف" Then
                    Button1.Enabled = True
                End If
                If PermtionName = "تعديل أصناف" Then
                    Button2.Enabled = True
                End If
                If PermtionName = "إجراء عملية مشتريات" Then
                    Button3.Enabled = True
                End If
                If PermtionName = "مدفوعات الموردين" Then
                    Button4.Enabled = True
                End If
                'If PermtionName = "إنهاء شيفت" Then
                '    Button5.Enabled = True
                'End If
                If PermtionName = "حركة الخزينة" Then
                    Button6.Enabled = True
                End If
                If PermtionName = "تقرير بالمبيعات اليومية" Then
                    Button7.Enabled = True
                End If
                If PermtionName = "تسجيل مرتجعات مبيعات" Then
                    Button8.Enabled = True
                End If
            End If
        Next
    End Sub

End Class