﻿Imports System.Data.SqlClient
Imports System.IO
Imports CrystalDecisions.CrystalReports.Engine
Imports vb = Microsoft.VisualBasic
Public Class FrmItemsNew
    Dim ListBoxSelectedIndex As Integer
    Dim Dt_AddBill As New DataTable
    Dim Dt_AddBill_BarcodeMore As New DataTable
    Dim Dt_AddBill_UnityItems As New DataTable
    Dim Dt_AddBillAlternative As New DataTable
    Dim RNXD As Integer
    Dim BarcodeMore As String = mykey.GetValue("BarcodeMore", "NO")
    Dim DefaultBarcodePaper As String = ""
    Dim UnityName As String = ""
    Dim GroupsRate As Boolean = False
    Private autoCompleteData As New AutoCompleteStringCollection()

    Private Sub Headerx()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        'Try
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If LanguageMainProgram = "العربية" Then

            If ActivateFormatNumberWithSeparators = "YES" Then
                S = Cls.Get_Select_Grid_S("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],CAST(TinPriceAverage AS NVARCHAR(50)) as [متوسط سعر الشراء],CAST(SalPrice AS NVARCHAR(50)) as [سعر التجزئة],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة],CAST(MinimumSalPrice AS NVARCHAR(50)) as [حد ادنى للسعر],rng as [حد الطلب] ,CAST(store AS NVARCHAR(50)) as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id<>''")
            Else
                S = Cls.Get_Select_Grid_S("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],WholeWholePrice as [سعر جملة الجملة],MinimumSalPrice as [حد ادنى للسعر],rng as [حد الطلب] ,store as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id<>''")
            End If
            If FilterSelect = "Number" Then
                S = S & " order by [الباركود]"
            End If
            If FilterSelect = "Date" Then
                S = S & " order by [المجموعة]"
            End If
            If FilterSelect = "Name" Then
                S = S & " order by [الصنف]"
            End If

        ElseIf LanguageMainProgram = "English" Then

            If ActivateFormatNumberWithSeparators = "YES" Then
                S = Cls.Get_Select_Grid_S("id as [ID], itm_id as [Barcode], group_name as [Category], group_branch as [Subcategory], sname as [ItemName], CAST(TinPrice AS NVARCHAR(50)) as [PurchasePrice], CAST(TinPriceAverage AS NVARCHAR(50)) as [AvgPurchasePrice], CAST(SalPrice AS NVARCHAR(50)) as [RetailPrice], CAST(WholePrice AS NVARCHAR(50)) as [WholesalePrice], CAST(WholeWholePrice AS NVARCHAR(50)) as [BulkPrice], CAST(MinimumSalPrice AS NVARCHAR(50)) as [MinPriceLimit], rng as [ReorderLimit], CAST(store AS NVARCHAR(50)) as [StockQty], Stores as [Store], RateVAT as [VATRate]", "items", "id<>''")
            Else
                S = Cls.Get_Select_Grid_S("id as [ID], itm_id as [Barcode], group_name as [Category], group_branch as [Subcategory], sname as [ItemName], TinPrice as [PurchasePrice], TinPriceAverage as [AvgPurchasePrice], SalPrice as [RetailPrice], WholePrice as [WholesalePrice], WholeWholePrice as [BulkPrice], MinimumSalPrice as [MinPriceLimit], rng as [ReorderLimit], store as [StockQty], Stores as [Store], RateVAT as [VATRate]", "items", "id<>''")
            End If

            If FilterSelect = "Number" Then
                S = S & " order by [Barcode]"
            End If
            If FilterSelect = "Date" Then
                S = S & " order by [Category]"
            End If
            If FilterSelect = "Name" Then
                S = S & " order by [ItemName]"
            End If
        End If


        cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)
            'Catch ex As Exception
            '    ErrorHandling(ex, Me.Text)
            'End Try

            Try
            Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
            If ShowGroupBranch = "NO" Then
                DTGV.Columns(3).Visible = False
            Else
                DTGV.Columns(3).Visible = True
            End If

            Dim HideWholeWholeSalePrice As String = mykey.GetValue("HideWholeWholeSalePrice", "NO")
            If HideWholeWholeSalePrice = "NO" Then
                DTGV.Columns(8).Visible = True
                DTGV.Columns(9).Visible = True
            Else
                DTGV.Columns(8).Visible = False
                DTGV.Columns(9).Visible = False
            End If

            If ShowValueVAT = "NO" Then
                DTGV.Columns(14).Visible = False
            End If

            Dim SM As Double
            For i As Integer = 0 To DTGV.Rows.Count - 1
                If LastTinPriceItems = "YES" Then
                    SM = SM + Val(DTGV.Rows(i).Cells(5).Value.ToString) * Val(DTGV.Rows(i).Cells(12).Value)
                Else
                    SM = SM + Val(DTGV.Rows(i).Cells(6).Value.ToString) * Val(DTGV.Rows(i).Cells(12).Value)
                End If
            Next
            txt_Total.Text = FormatNumberWithSeparators(SM)

            Dim SM1 As Double
            For i As Integer = 0 To DTGV.Rows.Count - 1
                SM1 = SM1 + DTGV.Rows(i).Cells(12).Value
            Next
            txtTotalQunt.Text = SM1

            txtNumberItems.Text = DTGV.RowCount
            DTGV.Columns(4).Width = 250
            DTGV.Columns(0).Visible = False

            GetFormatNumberWithSeparators()
            txtsearsh.Text = ""
        Catch ex As Exception
            ErrorHandlingNotMessage(ex, Me.Text)
        End Try
    End Sub

    Private Sub FrmItemsNew_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        If Height_Width_Altitude_Density = "YES" Then
            btnAddHeightWidth.Visible = True
        End If

        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Bra.Fil("groups", "g_name", cmbcats)
        Bra.Fil("groups", "g_name", cmbFindCats)
        Bra.Fil("Companies", "Name", cmbCompanies)
        Cls.fill_combo_Branch("stores", "store", cmbStores)
        Bra.Fil("Type_Unity", "Unity_Name", cmbUnity)
        'If LanguageMainProgram = "العربية" Then
        Cls.fill_combo_orderby("SELECT DISTINCT TOP (100) PERCENT UnitySize_Name, UnitySize_ID FROM     dbo.ItemsUnitySize ORDER BY UnitySize_ID", cmbUnitySize)
            'ElseIf LanguageMainProgram = "English" Then
            '    cmbUnitySize.Text = Cls.Get_Code_Value_More("Companies", "Id", "Name =N'" & cmbCompanies.Text & "'")
            '    Cls.fill_combo_orderby("SELECT DISTINCT TOP (100) PERCENT UnitySize_Name_en, UnitySize_ID FROM     dbo.ItemsUnitySize ORDER BY UnitySize_ID", cmbUnitySize)
            'End If

            Cls.fill_combo("vendors", "Vendorname", cmbvendores)
        cmbStores.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        txtrng.Text = mykey.GetValue("RngDefaultStock", "2")
        GroupBranch()
        'HideWholeWholeSalePrice()
        txttinprice.Text = 0
        txtsearsh.Text = " بحث ...." : txtsearsh.Font = New Font(txtsearsh.Font, FontStyle.Italic)
        Headerx()
        cmbcats.Focus()
        If chkAutomaticBarcodeNumber.Checked = True Then
            TxtPrc.Text = MaxRecordTables("Items", "itm_id")
        End If
        dtpDateItem.Top = 5000
        PanelViewImage.Top = 5000
        PanelBarcodeMore.Top = 5000
        PanelUnityItems.Top = 5000
        PanelPaperType.Top = 5000
        PanelItemDiscountRate.Top = 5000
        PanelAlternative.Top = 5000
        PanelOnlineStore.Top = 5000
        PanelHeightWidth.Top = 5000

        DTGVColumnsWidth()
        If UseBalanceBarcode = "YES" Then
            cmbBalanceBarcode.Visible = True
            Label36.Visible = True
        Else
            cmbBalanceBarcode.Visible = False
            Label36.Visible = False
        End If

        GetValueDefaultBarcodePaper()
        If DealingPharmacySystem = "YES" Then
            btnItemsAlternative.Visible = True
            cmb_Expired.Text = "بصلاحية"
            Cls.FillComboDataAdapterQuickSearch("Items", "sname", cmbTradeName)

            Cls.fill_combo("ItemsAlternative", "ScientificName", cmbScientificName)
        Else
            btnItemsAlternative.Visible = False
        End If
        If ConnectOnlineStore = "YES" Then
            btnDiscountedAfterPrice.Visible = True
        End If

        'AutoCompleteCustomSource()
        If LanguageMainProgram = "العربية" Then
            'SetArabic()
        ElseIf LanguageMainProgram = "English" Then
            SetEnglish()
        End If

    End Sub

    Private Sub DTGVColumnsWidth()
        Try
            If DTGV.Columns.Count = 0 Then
                DTGV.Columns(1).Width = 90
                DTGV.Columns(2).Width = 90
                DTGV.Columns(3).Width = 100
                DTGV.Columns(4).Width = 250
                DTGV.Columns(5).Width = 50
                DTGV.Columns(6).Width = 65
                DTGV.Columns(7).Width = 65
                DTGV.Columns(8).Width = 65
                DTGV.Columns(9).Width = 65
                DTGV.Columns(10).Width = 65
                DTGV.Columns(11).Width = 65
                DTGV.Columns(12).Width = 65
            End If
        Catch ex As Exception
            ErrorHandling(ex, "yasser_class")
        End Try
    End Sub

    Private Sub GroupBranch()
        Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
        If ShowGroupBranch = "NO" Then
            cmbGroup_Branch.Visible = False
            btnGroup_Branch.Visible = False
            Label16.Visible = False
            cmbitmnm.Size = New System.Drawing.Size(420, 24)
            'Label1.Location = New System.Drawing.Point(400, 24)
        Else
            cmbGroup_Branch.Visible = True
            btnGroup_Branch.Visible = True
            Label16.Visible = True
            cmbitmnm.Size = New System.Drawing.Size(260, 24)
            'Label1.Location = New System.Drawing.Point(765, 106)
        End If
    End Sub

    Private Sub HideWholeWholeSalePrice()

        Dim HideWholeWholeSalePrice As String = mykey.GetValue("HideWholeWholeSalePrice", "NO")
        If HideWholeWholeSalePrice = "NO" Then
            txtWholeWholePrice.Visible = True
            lblWholeWholePrice.Visible = True
            txtWholePrice.Visible = True
            lblWholePrice.Visible = True
            txtRateWholeWholePrice.Visible = True
            Label39.Visible = True
            txtRateMinimumSalPrice.Visible = True
            Label40.Visible = True
        Else
            txtWholeWholePrice.Visible = False
            lblWholeWholePrice.Visible = False
            txtWholePrice.Visible = False
            lblWholePrice.Visible = False
            txtRateWholeWholePrice.Visible = False
            Label39.Visible = False
            txtRateMinimumSalPrice.Visible = False
            Label40.Visible = False
            txtMinimumSalPrice.Location = New System.Drawing.Point(620, 166)
            lblMinimumSalPrice.Location = New System.Drawing.Point(620, 145)
            txtrng.Location = New System.Drawing.Point(540, 166)
            lblrng.Location = New System.Drawing.Point(550, 145)
            TxtPrc.Location = New System.Drawing.Point(180, 166)
            lblPrc.Location = New System.Drawing.Point(320, 145)
            TxtPrc.Size = New System.Drawing.Point(350, 34)

        End If

    End Sub

    Private Sub cmbcats_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbcats.DropDown
        cmbGroup_Branch.Text = ""
    End Sub

    Private Sub cmbcats_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbcats.KeyUp
        Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
        If e.KeyCode = 13 Then
            If ShowGroupBranch = "NO" Then
                cmbitmnm.Focus()
            Else
                cmbGroup_Branch.Focus()
            End If
        End If

    End Sub

    Function ValidateSave() As Boolean

        If cmbcats.Text = "" Then MsgBox("فضلا أدخل مجموعة الصنف", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbitmnm.Text = "" Then MsgBox("فضلا أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbitmnm.Focus() : Return False
        'If cmbUnity.Text = "" Then MsgBox("فضلا أدخل وحد القياس", MsgBoxStyle.Exclamation) : cmbUnity.Focus() : Return False
        If cmbStores.Text = "" Then MsgBox("من فضلك اختر المخزن المورد اليه", MsgBoxStyle.Exclamation) : cmbStores.Focus() : Return False
        If txtqunt.Text = "" Then MsgBox("فضلا أدخل الكمية ", MsgBoxStyle.Exclamation) : txtqunt.Focus() : Return False
        'If Val(txttinprice.Text.Trim) = 0 Then MsgBox("فضلا أدخل سعر الشراء", MsgBoxStyle.Exclamation) : txttinprice.Focus() : Return False
        'If Val(txt_priseSal.Text.Trim) = 0 Then MsgBox("فضلا أدخل سعر التجزئة", MsgBoxStyle.Exclamation) : txt_priseSal.Focus() : Return False
        If txtrng.Text = "" Then
            txtrng.Text = 0
        End If
        If TxtPrc.Text = "" Then MsgBox("فضلا أدخل باركود الصنف", MsgBoxStyle.Exclamation) : Return False

        'cmd.CommandText = "select count(*) from Items where group_name =N'" & cmbcats.Text.Trim & "' and sname =N'" & cmbitmnm.Text.Trim & "'" : H = cmd.ExecuteScalar
        'If H > 0 Then
        '    MsgBox("الصنف مسجل مسبقاً", MsgBoxStyle.Exclamation) : cmbitmnm.Focus() : Return False
        'End If

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where sname =N'" & cmbitmnm.Text.Trim & "' and Stores =N'" & cmbStores.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                MsgBox(" الصنف مسجل مسبقاً بنفس أسم المخزن", MsgBoxStyle.Exclamation) : cmbitmnm.Focus() : Return False
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where itm_id =N'" & TxtPrc.Text.Trim & "' and Stores =N'" & cmbStores.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                MsgBox(" الباركود مسجل مسبقاً بنفس أسم المخزن", MsgBoxStyle.Exclamation) : cmbitmnm.Focus() : Return False
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try


        Dim ParcodeItems As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If Company_Branch_ID = "0" Then
            cmd.CommandText = "select distinct itm_id from Items where sname =N'" & cmbitmnm.Text.Trim & "'"
        Else
            cmd.CommandText = "select distinct itm_id from Items where Company_Branch_ID =N'" & Company_Branch_ID & "' and sname =N'" & cmbitmnm.Text.Trim & "'"
        End If
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            ParcodeItems = dr(0).ToString
        Else
            ParcodeItems = 0
        End If
        If ParcodeItems <> "0" Then
            If Val(ParcodeItems) <> Val(TxtPrc.Text) Then
                MsgBox("الصنف مسجل مسبقا ويوجد له مخزن من فضلك اضغط دبل كليك على الصنف وأضف له المخزن الجديد أو ممكن تغيير الباركود لنفس الصنف الموجود مسبقا ", MsgBoxStyle.Exclamation) : cmbitmnm.Focus() : Return False
            End If
        End If

        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where itm_id =N'" & TxtPrc.Text.Trim & "' and Stores =N'" & cmbStores.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                MsgBox("الباركود مسجل مسبقاً", MsgBoxStyle.Exclamation) : TxtPrc.Focus() : Return False
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try


        Try
            If cmbvendores.Text <> "" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select count(*) from vendors where Vendorname =N'" & cmbvendores.Text.Trim & "'" : H = cmd.ExecuteScalar
                If H = 0 Then
                    MsgBox("أسم المورد غير مسجل", MsgBoxStyle.Exclamation) : cmbvendores.Focus() : Return False
                End If
            End If
        Catch ex As Exception
        End Try

        'If BarcodeMore = "YES" Then
        '    Try
        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        cmd.CommandText = "select count(*) from BarcodeMore where itm_id_More =N'" & TxtPrc.Text.Trim & "'" : H = cmd.ExecuteScalar
        '        If H > 0 Then
        '            MsgBox("الباركود مسجل مسبقاً", MsgBoxStyle.Exclamation) : TxtPrc.Focus() : Return False
        '        End If
        '    Catch ex As Exception
        '        ErrorHandling(ex, Me.Text)
        '    End Try
        'End If

        Return True
    End Function



    Private Sub Savex()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        If ValidateSave() = False Then Exit Sub

        AddGroubs()
        AddStores()

        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Dim expired As String
        If cmb_Expired.Text = "بدون صلاحية" Then
            expired = ""
        Else
            expired = Cls.C_date(dtpExpiration.Text)
        End If

        Dim QuickSearch As String
        If chkQuickSearch.Checked = True Then
            QuickSearch = "0"
        Else
            QuickSearch = "1"
        End If

        Dim BalanceBarcode As String
        If cmbBalanceBarcode.Text = "بدون ميزان الباركود" Then
            BalanceBarcode = "0"
        Else
            BalanceBarcode = "1"
        End If

        Dim PriceIncludesVAT As Integer
        If chkPriceIncludesVAT.Checked = True Then
            PriceIncludesVAT = 1
        Else
            PriceIncludesVAT = 0
        End If

        Dim TypePlusDiscRateSalPrice, TypePlusDiscRateWholePrice, TypePlusDiscRateWholeWholePrice As Double
        If rdoDiscRateSalPrice.Checked = True Then
            TypePlusDiscRateSalPrice = 0
        Else
            TypePlusDiscRateSalPrice = 1
        End If
        If rdoDiscRateWholePrice.Checked = True Then
            TypePlusDiscRateWholePrice = 0
        Else
            TypePlusDiscRateWholePrice = 1
        End If
        If rdoDiscRateWholeWholePrice.Checked = True Then
            TypePlusDiscRateWholeWholePrice = 0
        Else
            TypePlusDiscRateWholeWholePrice = 1
        End If

        Dim DeferredCurrentDiscount As String
        If rdoCurrentDiscTinPrice.Checked = True Then
            DeferredCurrentDiscount = "0"
        Else
            DeferredCurrentDiscount = "1"
        End If

        GetItemsUnity()

        Dim bill_no_Expired As String = ""

        'connectionStringClose()
        'connectionStringTransaction()

        Dim CompaniesID As String = Cls.Get_Code_Value_More("Companies", "Id", "Name =N'" & cmbCompanies.Text & "'")

        Dim X As String = "جرد"

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()


            S = "insert into Items (Company_Branch_ID,itm_id,group_name,group_branch,sname,Unity,rng,tinprice,salprice,TinPriceAverage,WholePrice,WholeWholePrice,MinimumSalPrice,RatePriceOffers,RateWholePrice,RateWholeWholePrice,RateMinimumSalPrice,RateDiscTinPrice,RateDiscSalPrice,RateDiscWholePrice,RateDiscWholeWholePrice,RateDiscTinPriceAfter,RateDiscSalPriceAfter,RateDiscWholePriceAfter,RateDiscWholeWholePriceAfter,TypePlusDiscRateSalPrice,TypePlusDiscRateWholePrice,TypePlusDiscRateWholeWholePrice,DiscountedPrice,DiscountedPrice2,DiscountedPrice3,tin,sal,btin,bsal,decayed,tinpricetotal,salpricetotal,btinpricetotal,bsalpricetotal,decayedpricetotal,store,ValStore,profits,UserName,Stores,QuickSearch,Height,Width,Altitude,Density,BalanceBarcode,RateVAT,Vendorname,PriceIncludesVAT,DeferredCurrentDiscount,Tag,Description,LimitQuantity,CompaniesID,CompaniesName) values ("
            S = S & "N'" & Company_Branch_ID & "',N'" & TxtPrc.Text.Trim & "',N'" & cmbcats.Text.Trim & "',N'" & cmbGroup_Branch.Text.Trim & "',N'" & cmbitmnm.Text.Trim & "',N'" & UnityName.Trim & "',"
            S = S & "" & Val(txtrng.Text) & ","
            S = S & "" & Val(txttinprice.Text) & ","
            S = S & "" & Val(txt_priseSal.Text) & ","
            S = S & "" & Val(txttinprice.Text) & ","
            S = S & "" & Val(txtWholePrice.Text) & ","
            S = S & "" & Val(txtWholeWholePrice.Text) & ","
            S = S & "" & Val(txtMinimumSalPrice.Text) & ","
            S = S & "" & Val(txtRatePriceSale.Text) & ","
            S = S & "" & Val(txtRateWholePrice.Text) & ","
            S = S & "" & Val(txtRateWholeWholePrice.Text) & ","
            S = S & "" & Val(txtRateMinimumSalPrice.Text) & ","
            S = S & "" & Val(txtRateDiscTinPrice.Text) & ","
            S = S & "" & Val(txtRateDiscSalPrice.Text) & ","
            S = S & "" & Val(txtRateDiscWholePrice.Text) & ","
            S = S & "" & Val(txtRateDiscWholeWholePrice.Text) & ","
            S = S & "" & Val(txtRateDiscTinPriceAfter.Text) & ","
            S = S & "" & Val(txtRateDiscSalPriceAfter.Text) & ","
            S = S & "" & Val(txtRateDiscWholePriceAfter.Text) & ","
            S = S & "" & Val(txtRateDiscWholeWholePriceAfter.Text) & ","
            S = S & "" & Val(TypePlusDiscRateSalPrice) & ","
            S = S & "" & Val(TypePlusDiscRateWholePrice) & ","
            S = S & "" & Val(TypePlusDiscRateWholeWholePrice) & ","
            S = S & "" & Val(txtDiscountedPrice.Text) & ","
            S = S & "" & Val(txtDiscountedPrice2.Text) & ","
            S = S & "" & Val(txtDiscountedPrice3.Text) & ","
            S = S & "0,0,0,0,0,0,0,0,0,0,0,0,0,N'" & UserName & "',N'" & cmbStores.Text.Trim & "',N'" & QuickSearch & "',N'" & Val(txtHeight.Text) & "',N'" & Val(txtWidth.Text) & "',N'" & Val(txtAltitude.Text) & "',N'" & Val(txtDensity.Text) & "',N'" & Val(BalanceBarcode) & "',N'" & Val(txtRateVAT.Text) & "',N'" & cmbvendores.Text.Trim & "'," & PriceIncludesVAT & "," & DeferredCurrentDiscount & ",N'" & txtTag.Text & "',N'" & txtDescription.Text & "',N'" & txtLimitQuantity.Text & "',N'" & CompaniesID & "',N'" & cmbCompanies.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            bill_no_Expired = MaxRecordTables("BilltINData", "bill_no_Expired")


            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into BilltINData (Company_Branch_ID,bill_no,itm_id,itm_cat,group_branch,itm_name,itm_Unity,price,TinPriceAverage,qu,qu_unity,totalprice,UserName,Stores,bill_date,bill_EndDate,Expired,bill_ProductionDate,Vendorname,qu_expired,Treasury_Code,bill_no_Expired,CurrentStock,CurrentStockTotal,Discounts)"
            S = S & " values (N'" & Company_Branch_ID & "',N'" & X & "',N'" & TxtPrc.Text & "',N'" & cmbcats.Text.Trim & "',N'" & cmbGroup_Branch.Text.Trim & "',N'" & cmbitmnm.Text.Trim & "',N'" & UnityName & "'," & Val(txttinprice.Text) & "," & Val(txttinprice.Text) & "," & Val(txtqunt.Text) & "," & Val(txtqunt.Text) & "," & Val(txttinprice.Text) * Val(txtqunt.Text) & ",N'" & UserName & "',N'" & cmbStores.Text & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & expired & "',N'" & cmb_Expired.Text & "',N'" & Cls.C_date(dtpProductionDate.Text) & "',N'" & cmbvendores.Text.Trim & "'," & Val(txtqunt.Text) & ",N'" & Treasury_Code & "',N'" & bill_no_Expired & "'," & Val(txtqunt.Text) & "," & Val(txttinprice.Text) * Val(txtqunt.Text) & "," & Val(txtRateDiscTinPrice.Text) & ")"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            'trans.Commit()
            'connectionStringOpen()
        Catch ex As Exception
            'trans.Rollback()
            'ErrorHandling(ex, Me.Text)
            'Cn.Close()
            'connectionStringOpen()
            'Exit Sub
        End Try

        GetBarcodeMore()

        GetAddItemsAlternative()

        IM.Store(TxtPrc.Text.Trim, cmbStores.Text)

        If ConnectOnlineStore = "YES" Then
            Cos.AddProduct(cmbitmnm.Text.Trim, TxtPrc.Text.Trim, TxtPrc.Text.Trim, txtDescription.Text.Trim, cmbcats.Text.Trim, cmbCompanies.Text.Trim, txt_priseSal.Text, txtDiscountedPrice.Text, txtWholePrice.Text, txtDiscountedPrice2.Text, txtWholeWholePrice.Text, txtDiscountedPrice3.Text, txtStockOnline.Text, txtLimitQuantity.Text)
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & X & "' and itm_id = N'" & TxtPrc.Text.Trim & "' and Stores =N'" & cmbStores.Text & "'" : cmd.ExecuteNonQuery()

        IM.StoreExpired(TxtPrc.Text.Trim, cmbStores.Text, Cls.C_date(dtpExpiration.Text.ToString()), bill_no_Expired)

        ImageUpdate()

        If chkPrintParcode.Checked = True Then
            PrintParcode()
        End If

        'Bra.Fil("groups", "g_name", cmbcats)
        'Cls.fill_combo_Branch("stores", "store", cmbStores)
        'Cls.FillComboDataAdapterQuickSearch("Items", "sname", cmbitmnm)
        Headerx()
        Clear_All()
        If chkAutomaticBarcodeNumber.Checked = True Then
            TxtPrc.Text = MaxRecordTables("Items", "itm_id")
        End If
        cmbitmnm.Focus()
        cmbUnity.Text = mykey.GetValue("DefaultUnityName", "قطعة")
        txtrng.Text = mykey.GetValue("RngDefaultStock", "2")
        txtNumberPieces.Text = 1
        trueItems()

        dgvBarcodeMore.DataSource = ""
        txtBarcodeMore.Text = ""
    End Sub

    Sub Clear_All()
        If chkAutomaticBarcodeNumber.Checked = False Then
            TxtPrc.Text = ""
        End If
        cmbitmnm.Text = ""
        txtqunt.Text = "0"
        txttinprice.Text = "0"
        txt_priseSal.Text = "0"
        txtWholeWholePrice.Text = "0"
        txtWholePrice.Text = "0"
        txtMinimumSalPrice.Text = "0"
        txtRatePriceSale.Text = "0"
        txtHeight.Text = "0"
        txtWidth.Text = "0"
        txtAltitude.Text = "0"
        txtDensity.Text = "0"
        txtRateVAT.Text = "0"
        PicLogo.Image = Nothing
        OpenFileDialog1.FileName = ""
        Dt_AddBill_UnityItems.Rows.Clear()
        Dt_AddBillAlternative.Rows.Clear()
        TxtPrc.Enabled = True
        If GroupsRate = False Then
            txtRateDiscTinPrice.Text = "0"
            txtRateDiscSalPrice.Text = "0"
            txtRateDiscWholePrice.Text = "0"
            txtRateDiscWholeWholePrice.Text = "0"
        End If
        txtRateDiscTinPriceAfter.Text = "0"
        txtRateDiscSalPriceAfter.Text = "0"
        txtRateDiscWholePriceAfter.Text = "0"
        txtRateDiscWholeWholePriceAfter.Text = "0"
        txtDiscountedPrice.Text = "0"
        txtDiscountedPrice2.Text = "0"
        txtDiscountedPrice3.Text = "0"
        txtStockOnline.Text = "0"
    End Sub

    Private Sub btnsave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnsave.Click
        Try
            Dim ParcodeEdit13Number As String = mykey.GetValue("ParcodeEdit13Number", "NO")
            If ParcodeEdit13Number = "YES" Then
                If TxtPrc.Text.Length = 13 Or TxtPrc.Text.Length = 12 Then
                    mykey.SetValue("CodeEAN13Prc", TxtPrc.Text)
                    Dim ReturnCodeEAN13Reg As String = mykey.GetValue("ReturnCodeEAN13Reg", "ReturnCodeEAN13Reg")
                    TxtPrc.Text = ReturnCodeEAN13Reg
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        Savex()

        ActionAddNewItems = True
    End Sub

    Function SumListCombo(ByVal X As ListBox)
        Dim SM As Double
        For i As Integer = 0 To X.Items.Count - 1
            SM = SM + Val(X.Items(i))
        Next
        Return SM
    End Function

    Function ValidatAdd() As Boolean
        Try
            If cmbcats.Text.Trim = "" Then MsgBox("أختر مجموعة الصنف", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
            If cmbitmnm.Text.Trim = "" Then MsgBox("أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbitmnm.Focus() : Return False
            If Val(txtqunt.Text) > 0 Then
                If Val(txttinprice.Text) = 0 Then
                    MsgBox("فضلا أدخل سعر شراء الوحدة", MsgBoxStyle.Exclamation) : txttinprice.Focus() : Return False
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        Return True
    End Function

    Private Sub txtsearsh_Leave(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtsearsh.Leave
        '    txtsearsh.Text = " بحث ...." : txtsearsh.Font = New Font(txtsearsh.Font, FontStyle.Italic)
    End Sub

    Private Sub txtsearsh_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles txtsearsh.MouseClick
        If txtsearsh.Text = " بحث ...." Then txtsearsh.SelectAll() : txtsearsh.Font = New Font(txtsearsh.Font, FontStyle.Regular)
    End Sub

    Private Sub txtqunt_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtqunt.KeyUp
        If e.KeyCode = 13 Then
            txttinprice.Focus()
            txttinprice.SelectAll()
        End If
    End Sub

    Private Sub txtqunt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtqunt.TextChanged
        If Not IsNumeric(txtqunt.Text) Then
            System.Windows.Forms.SendKeys.Send("{backspace}")
        End If
    End Sub

    Private Sub txttinprice_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txttinprice.KeyUp
        If e.KeyCode = 13 Then
            txtRatePriceSale.Focus()
            txtRatePriceSale.Select()
        End If
    End Sub

    Private Sub txttinprice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txttinprice.TextChanged
        MyVars.CheckNumber(txttinprice)
        GetRateDiscPriceAfter()
        If txttinprice.Text <> "" Then
            For i As Integer = 0 To dgvUnityItems.Rows.Count - 1
                If dgvUnityItems.Rows(i).Cells(6).Value = "الاصغر" Then
                    Dim split As String() = New String() {"."}
                    Dim SplitNull As String = ""
                    Dim itemsSplit As String() = txttinprice.Text.Split(split, StringSplitOptions.None)
                    If itemsSplit.Length <> 1 Then
                        SplitNull = itemsSplit(1).ToString()
                    Else
                        dgvUnityItems.Rows(i).Cells(2).Value = txttinprice.Text
                    End If
                    If SplitNull <> "" Then
                        dgvUnityItems.Rows(i).Cells(2).Value = txttinprice.Text
                    End If
                End If
            Next
        End If
    End Sub

    Private Sub txtrng_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtrng.KeyUp
        Try
            If e.KeyCode = 13 Then
                Dim HideWholeWholeSalePrice As String = mykey.GetValue("HideWholeWholeSalePrice", "NO")
                If HideWholeWholeSalePrice = "NO" Then
                    TxtPrc.Focus()
                    'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    'cmd.CommandText = "select count(*) from items where group_name =N'" & cmbcats.Text.Trim & "'" : H = cmd.ExecuteScalar
                    'Dim X As String
                    'Dim XH As String
                    'XH = H
                    'If Len(XH) = 1 Then
                    '    X = "0" & H
                    'ElseIf Len(XH) = 2 Then
                    '    X = H
                    'End If

                    'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    'cmd.CommandText = "select id from Groups where G_name =N'" & cmbcats.Text.Trim & "'"
                    'dr = cmd.ExecuteReader : dr.Read()

                    'If chkAutomaticBarcodeNumber.Checked = True Then
                    '    If Len(txttinprice.Text) = 1 Then
                    '        TxtPrc.Text = dr(0) & "0" & Int(txttinprice.Text.Trim) & X
                    '    ElseIf Len(txttinprice.Text) = 2 Then
                    '        TxtPrc.Text = dr(0) & Int(txttinprice.Text.Trim) & X
                    '    ElseIf Len(txttinprice.Text) = 3 Then
                    '        TxtPrc.Text = dr(0) & "00" & X
                    '    ElseIf Len(txttinprice.Text) = 4 Then
                    '        TxtPrc.Text = dr(0) & "0" & Int(txttinprice.Text.Trim) & X
                    '    End If
                    'End If


                    TxtPrc.Focus()
                    TxtPrc.SelectAll()
                Else
                    btnsave.PerformClick()
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        'TxtPrc.Text = vb.Right(Clss.GenerateItmId_Or_Parcode(), 6)

    End Sub

    Private Sub txtrng_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtrng.TextChanged
        MyVars.CheckNumber(txtrng)
    End Sub

    Private Sub cmbcats_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbcats.SelectedIndexChanged
        If cmbitmnm.Text = "" Then
            Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
            Try
                'If ShowGroupBranch = "NO" Then
                If cmbcats.Text.Trim = "" Then Exit Sub
                Cls.fill_combo_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbitmnm)
                cmbitmnm.Text = ""
                'End If

                If ShowGroupBranch = "YES" Then
                    cmbGroup_Branch.Items.Clear()
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "SELECT dbo.Groups.G_name, dbo.Group_Branch.branch_Name FROM dbo.Groups INNER JOIN  dbo.Group_Branch ON dbo.Groups.id = dbo.Group_Branch.Group_Name_ID WHERE     (dbo.Groups.G_name =N'" & cmbcats.Text & "')  order by 1"
                    dr = cmd.ExecuteReader
                    Do While dr.Read
                        cmbGroup_Branch.Items.Add(Trim(dr(1)))
                    Loop
                    cmbGroup_Branch.Text = ""
                End If


                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select RateDiscTinPrice,RateDiscSalPrice,RateDiscWholePrice,RateDiscWholeWholePrice,TypePlusDiscRateSalPrice,TypePlusDiscRateWholePrice,TypePlusDiscRateWholeWholePrice from Groups where G_name=N'" & cmbcats.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    txtRateDiscTinPrice.Text = dr(0).ToString
                    txtRateDiscSalPrice.Text = dr(1).ToString
                    txtRateDiscWholePrice.Text = dr(2).ToString
                    txtRateDiscWholeWholePrice.Text = dr(3).ToString
                    If dr(4).ToString = "0" Then
                        rdoDiscRateSalPrice.Checked = True
                    Else
                        rdoPlusRateSalPrice.Checked = True
                    End If
                    If dr(5).ToString = "0" Then
                        rdoDiscRateWholePrice.Checked = True
                    Else
                        rdoPlusRateWholePrice.Checked = True
                    End If
                    If dr(6).ToString = "0" Then
                        rdoDiscRateWholeWholePrice.Checked = True
                    Else
                        rdoPlusRateWholeWholePrice.Checked = True
                    End If
                    GroupsRate = True
                Else
                    txtRateDiscTinPrice.Text = "0"
                    txtRateDiscSalPrice.Text = "0"
                    txtRateDiscWholePrice.Text = "0"
                    txtRateDiscWholeWholePrice.Text = "0"
                End If


            Catch ex As Exception
                ErrorHandling(ex, Me.Text)
                If NetworkName = "Yes" Then
                    If UseExternalServer = "Yes" Then
                        connect()
                    End If
                End If
            End Try
        End If
    End Sub

    Private Sub BtnAddCat_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAddCat.Click
        FrmCats.Show()
    End Sub

    Private Sub Button2_Click_1(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Shell("calc.exe", vbNormalFocus)
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        TxtPrc.Text = vb.Right(Clss.GenerateItmId_Or_Parcode(), 6)
    End Sub

    Private Sub cmbitmnm_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbitmnm.KeyUp
        If e.KeyCode = 13 Then
            txtqunt.Focus()
        End If
    End Sub

    Private Sub TxtPrc_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles TxtPrc.KeyUp
        If e.KeyCode = 13 Then
            If TxtPrc.Text.Trim = "" Then
                Button1.PerformClick()
            Else
                btnsave.PerformClick()
            End If
        End If
    End Sub

    Private Sub txt_priseSal_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txt_priseSal.KeyUp
        If e.KeyCode = 13 Then
            Dim HideWholeWholeSalePrice As String = mykey.GetValue("HideWholeWholeSalePrice", "NO")
            If HideWholeWholeSalePrice = "NO" Then
                txtWholePrice.Focus()
                txtWholePrice.SelectAll()
            Else
                txtMinimumSalPrice.Focus()
                txtMinimumSalPrice.SelectAll()
            End If
        End If
    End Sub

    Private Sub txt_priseSal_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txt_priseSal.TextChanged
        MyVars.CheckNumber(txt_priseSal)
        GetRateDiscPriceAfter()
        If txt_priseSal.Text <> "" Then
            For i As Integer = 0 To dgvUnityItems.Rows.Count - 1
                If dgvUnityItems.Rows(i).Cells(6).Value = "الاصغر" Then
                    Dim split As String() = New String() {"."}
                    Dim SplitNull As String = ""
                    Dim itemsSplit As String() = txt_priseSal.Text.Split(split, StringSplitOptions.None)
                    If itemsSplit.Length <> 1 Then
                        SplitNull = itemsSplit(1).ToString()
                    Else
                        dgvUnityItems.Rows(i).Cells(3).Value = txt_priseSal.Text
                    End If
                    If SplitNull <> "" Then
                        dgvUnityItems.Rows(i).Cells(3).Value = txt_priseSal.Text
                    End If
                End If
            Next
        End If
    End Sub
    Sub AddStores()
        Try
            REM للتاكد من عدم التكرار
            Dim TableName, FieldName, StringFind As String
            Dim S As String
            TableName = "stores"
            FieldName = "store"
            StringFind = cmbStores.Text
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "Select * From " & TableName & " Where " & FieldName & "=N'" & StringFind & "'"
            cmd.CommandType = CommandType.Text
            cmd.CommandText = S
            dr = cmd.ExecuteReader
            'dr.Read()
            If dr.HasRows = False Then
                Cls.Get_Value_Count_More("Stores", "StausMainStore =N'0'")
                Dim StausMainStore As Integer
                If H = 0 Then
                    StausMainStore = 0
                Else
                    StausMainStore = 1
                End If

                REM لحفظ المخزن
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Stores(Company_Branch_ID,store,UserName,StausMainStore) values (N'" & Company_Branch_ID & "',N'" & StringFind & "',N'" & UserName & "',N'" & StausMainStore & "')"
                cmd.CommandText = S
                cmd.ExecuteNonQuery()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
    Sub AddGroubs()
        Try
            REM للتاكد من عدم التكرار
            Dim TableName, FieldName, StringFind As String
            Dim S As String
            TableName = "groups"
            FieldName = "g_name"
            StringFind = cmbcats.Text
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "Select * From " & TableName & " Where " & FieldName & "=N'" & StringFind & "'"
            cmd.CommandType = CommandType.Text
            cmd.CommandText = S
            dr = cmd.ExecuteReader
            'dr.Read()
            If dr.HasRows = False Then
                REM لحفظ المجموعه
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into groups(Company_Branch_ID,g_name,UserName) values (N'" & Company_Branch_ID & "',N'" & StringFind & "',N'" & UserName & "')"
                cmd.CommandText = S
                cmd.ExecuteNonQuery()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbStores_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbStores.KeyUp
        If e.KeyCode = 13 Then
            txtqunt.Focus()
        End If
    End Sub

    Private Sub MAXRECORD()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from Items"
            dr = cmd.ExecuteReader

            Dim dt As New DataTable
            dt.Load(dr)

            If dt.Rows.Count = 0 Then
                Me.TxtPrc.Text = 1
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(itm_id) as mb FROM Items where id <> N'" & TxtPrc.Text & "'"
                dr = cmd.ExecuteReader
                dr.Read()

                Dim sh As Long
                sh = dr("mb")
                Me.TxtPrc.Text = sh + 1
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub txtWholePrice_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtWholePrice.KeyUp
        If e.KeyCode = 13 Then
            txtWholeWholePrice.Focus()
            txtWholeWholePrice.SelectAll()
        End If
    End Sub

    Private Sub txtWholePrice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtWholePrice.TextChanged
        MyVars.CheckNumber(txtWholePrice)
        GetRateDiscPriceAfter()
    End Sub

    Private Sub txtMinimumSalPrice_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtMinimumSalPrice.KeyUp
        If e.KeyCode = 13 Then
            txtrng.Focus()
            txtrng.SelectAll()
        End If
    End Sub

    Private Sub txtMinimumSalPrice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtMinimumSalPrice.TextChanged
        MyVars.CheckNumber(txtMinimumSalPrice)
    End Sub

    Private Sub txtWholeWholePrice_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtWholeWholePrice.KeyUp
        If e.KeyCode = 13 Then
            txtMinimumSalPrice.Focus()
            txtMinimumSalPrice.SelectAll()
        End If
    End Sub

    Private Sub txtWholeWholePrice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtWholeWholePrice.TextChanged
        MyVars.CheckNumber(txtWholeWholePrice)
        GetRateDiscPriceAfter()
    End Sub

    Private Sub btnHelp_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnHelp.Click
        cmbUnity.Text = mykey.GetValue("DefaultUnityName", "قطعة")
        txtNumberPieces.Text = 1
        PanelUnityItems.Top = 350
        PanelUnityItems.Size = New System.Drawing.Size(681, 336)
    End Sub

    Private Sub btnGroup_Branch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnGroup_Branch.Click
        Frm_Group_Branch.ShowDialog()
    End Sub

    Private Sub cmbGroup_Branch_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbGroup_Branch.SelectedIndexChanged
        If cmbitmnm.Text = "" Then
            Try
                Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
                If ShowGroupBranch = "NO" Then
                    If cmbcats.Text.Trim = "" Then Exit Sub
                    Cls.fill_combo_Stores_Where("Items", "sname", "group_branch", cmbGroup_Branch.Text, cmbitmnm)
                    cmbitmnm.Text = ""
                End If
            Catch ex As Exception
                ErrorHandling(ex, Me.Text)
            End Try
        End If

    End Sub

    Private Sub cmbGroup_Branch_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbGroup_Branch.KeyUp
        If e.KeyCode = 13 Then
            cmbitmnm.Focus()
        End If

    End Sub

    Private Sub ALTER_TABLE()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Customers ADD NewField nvarchar(50),NewField2 int,NewField3 float,NewField4 datetime,NewField5 nvarchar(MAX),NewField6 bigint"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetFocusText()
        If FocusText = "YES" Then
            TxtPrc.Focus()
        Else
            cmbitmnm.Focus()
        End If
    End Sub

    Private Sub LOGOBUTTON_Click(sender As Object, e As EventArgs) Handles LOGOBUTTON.Click
        Try
            OpenFileDialog1.Filter = "Image Files (*.png *.jpg *.bmp *.JPE *.JPEG) |*.png; *.jpg; *.bmp; *.JPE; *.JPEG|All Files(*.*) |*.*"
            With Me.OpenFileDialog1
                .FilterIndex = 1
                .Title = "حدد صورة الصنف"
                .ShowDialog()
                If Len(.FileName) > 0 Then
                    PicLogo.Image = Image.FromFile(OpenFileDialog1.FileName)
                    picWorkShowImage.Image = Image.FromFile(OpenFileDialog1.FileName)
                End If
            End With
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub ImageUpdate()
        Try
            If OpenFileDialog1.FileName = "" Then
            Else
                connectionStringOpen()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = " Update Items SET  Items_Images = @Items_Images WHERE itm_id =N'" & TxtPrc.Text & "'"
                Dim cmd As SqlClient.SqlCommand = New SqlClient.SqlCommand
                cmd.CommandType = CommandType.Text
                cmd.Connection = Cn
                Dim fs As FileStream = New FileStream(OpenFileDialog1.FileName, FileMode.Open, FileAccess.Read)
                Dim r As BinaryReader = New BinaryReader(fs)
                Dim FileByteArray(fs.Length - 1) As Byte
                r.Read(FileByteArray, 0, CInt(fs.Length))
                With cmd
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    .CommandType = CommandType.Text
                    .Connection = Cn
                    .Parameters.Add("@Items_Images", SqlDbType.Image).Value = FileByteArray
                    .CommandText = S
                End With
                cmd.ExecuteNonQuery()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub


    Private Sub btnCloseViewImage_Click(sender As Object, e As EventArgs) Handles btnCloseViewImage.Click
        PanelViewImage.Dock = DockStyle.None
        PanelViewImage.Top = 5000
    End Sub

    Private Sub PicLogo_Click(sender As Object, e As EventArgs) Handles PicLogo.Click
        PanelViewImage.Top = 20
        PanelViewImage.Dock = DockStyle.Fill
    End Sub

    Private Sub txtqunt_KeyDown(sender As Object, e As KeyEventArgs) Handles txtqunt.KeyDown
        If ((e.KeyCode = Keys.F5)) Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub txt_priseSal_KeyDown(sender As Object, e As KeyEventArgs) Handles txt_priseSal.KeyDown
        If ((e.KeyCode = Keys.F5)) Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub txttinprice_KeyDown(sender As Object, e As KeyEventArgs) Handles txttinprice.KeyDown
        If ((e.KeyCode = Keys.F5)) Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub txtWholePrice_KeyDown(sender As Object, e As KeyEventArgs) Handles txtWholePrice.KeyDown
        If ((e.KeyCode = Keys.F5)) Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub txtWholeWholePrice_KeyDown(sender As Object, e As KeyEventArgs) Handles txtWholeWholePrice.KeyDown
        If ((e.KeyCode = Keys.F5)) Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub txtMinimumSalPrice_KeyDown(sender As Object, e As KeyEventArgs) Handles txtMinimumSalPrice.KeyDown
        If ((e.KeyCode = Keys.F5)) Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub txtrng_KeyDown(sender As Object, e As KeyEventArgs) Handles txtrng.KeyDown
        If ((e.KeyCode = Keys.F5)) Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub TxtPrc_KeyDown(sender As Object, e As KeyEventArgs) Handles TxtPrc.KeyDown
        If ((e.KeyCode = Keys.F5)) Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub txtRatePriceSale_TextChanged(sender As Object, e As EventArgs) Handles txtRatePriceSale.TextChanged
        MyVars.CheckNumber(txtRatePriceSale)
        If txtRatePriceSale.Text <> "0" Then
            Try
                Dim DiscVal As Double
                DiscVal = Val((Val(txttinprice.Text) * (100 + Val(txtRatePriceSale.Text))) / 100)
                txt_priseSal.Text = Math.Round(DiscVal, 4)
            Catch ex As Exception
            End Try
        End If
    End Sub

    Private Sub txtRatePriceSale_KeyUp(sender As Object, e As KeyEventArgs) Handles txtRatePriceSale.KeyUp
        If e.KeyCode = 13 Then
            txt_priseSal.Focus()
            txt_priseSal.Select()
        End If
    End Sub


#Region "BarcodeMore"
    Private Sub btnCloseBarcodeMore_Click(sender As Object, e As EventArgs) Handles btnCloseBarcodeMore.Click
        PanelBarcodeMore.Top = 5000
    End Sub

    Private Sub btnBarcodeMore_Click(sender As Object, e As EventArgs) Handles btnBarcodeMore.Click
        PanelBarcodeMore.Top = 200
        PanelBarcodeMore.Size = New System.Drawing.Size(347, 287)
    End Sub

    Private Sub btnAddBarcodeMore_Click(sender As Object, e As EventArgs) Handles btnAddBarcodeMore.Click
        Try
            If txtBarcodeMore.Text = "" Then MsgBox("فضلا ادخل الباركود", MsgBoxStyle.Exclamation) : txtBarcodeMore.Focus() : Exit Sub
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where itm_id =N'" & txtBarcodeMore.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                MsgBox("الباركود مسجل مسبقاً", MsgBoxStyle.Exclamation) : TxtPrc.Focus() : Exit Sub
            End If

            'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            'cmd.CommandText = "select count(*) from BarcodeMore where itm_id_More =N'" & txtBarcodeMore.Text.Trim & "'" : H = cmd.ExecuteScalar
            'If H > 0 Then
            '    MsgBox("الباركود مسجل مسبقاً", MsgBoxStyle.Exclamation) : TxtPrc.Focus() : Exit Sub
            'End If

            dgvBarcodeMore.DataSource = Fn_AddBillBarcodeMore(txtBarcodeMore.Text)

            txtNumberBarcodeMore.Text = dgvBarcodeMore.RowCount

            txtBarcodeMore.Text = ""
            txtBarcodeMore.Focus()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

    End Sub

    Friend Function Fn_AddBillBarcodeMore(ByVal Col_BarcodeMore As String) As DataTable
        If Dt_AddBill_BarcodeMore.Columns.Count = 0 Then
            Dt_AddBill_BarcodeMore.Columns.Add("الباركود", GetType(String))
        End If

        Dt_AddBill_BarcodeMore.Rows.Add(Col_BarcodeMore)
        Return Dt_AddBill_BarcodeMore
    End Function

    Private Sub btnBarcodeMoreGenerate_Click(sender As Object, e As EventArgs) Handles btnBarcodeMoreGenerate.Click
        txtBarcodeMore.Text = vb.Right(Clss.GenerateItmId_Or_Parcode(), 6)
    End Sub

    Private Sub txtBarcodeMore_KeyUp(sender As Object, e As KeyEventArgs) Handles txtBarcodeMore.KeyUp
        If e.KeyCode = 13 Then
            btnAddBarcodeMore.PerformClick()
        End If
    End Sub

    Private Sub GetBarcodeMore()
        Try
            If BarcodeMore = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete from BarcodeMore where itm_id =N'" & itmprc & "'" : cmd.ExecuteNonQuery()

                Dim Active As Boolean = False
                For i As Integer = 0 To dgvBarcodeMore.Rows.Count - 1

                    If dgvBarcodeMore.Rows(i).Cells(0).Value <> TxtPrc.Text Then
                        If Active = False Then
                            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                            S = "insert into BarcodeMore(itm_id,itm_id_More)  values("
                            S = S & "N'" & TxtPrc.Text.Trim & "',N'" & TxtPrc.Text.Trim & "')"
                            cmd.CommandText = S : cmd.ExecuteNonQuery()
                            Active = True
                        End If
                    Else
                        Active = True
                    End If

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    S = "insert into BarcodeMore(itm_id,itm_id_More)  values("
                    S = S & "N'" & TxtPrc.Text.Trim & "',N'" & dgvBarcodeMore.Rows(i).Cells(0).Value & "')"
                    cmd.CommandText = S : cmd.ExecuteNonQuery()
                Next
                Active = False
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

#End Region

#Region "UnityItems"
    Private Sub btnCloseUnityItems_Click(sender As Object, e As EventArgs) Handles btnCloseUnityItems.Click
        PanelUnityItems.Top = 5000
    End Sub

    Private Sub btnAddUnityItems_Click(sender As Object, e As EventArgs) Handles btnAddUnityItems.Click
        Try
            If cmbUnity.Text = "" Then MsgBox("فضلا ادخل نوع الوحدة", MsgBoxStyle.Exclamation) : cmbUnity.Focus() : Exit Sub
            If txtNumberPieces.Text = "" Or txtNumberPieces.Text = "0" Then MsgBox("فضلا ادخل عدد القطع", MsgBoxStyle.Exclamation) : txtNumberPieces.Focus() : Exit Sub
            If cmbUnitySize.Text = "" Then MsgBox("فضلا ادخل حجم الوحدة", MsgBoxStyle.Exclamation) : cmbUnitySize.Focus() : Exit Sub

            If dgvUnityItems.Rows.Count = 0 Then
                If txtitm_id_Unity.Text <> TxtPrc.Text Then
                    MsgBox("فضلا يجب ان يكون باركود اول وحدة مطابق لباركود الصنف الاساسى", MsgBoxStyle.Exclamation) : cmbUnitySize.Focus() : Exit Sub
                Else
                    TxtPrc.Enabled = False
                End If
            End If

            'If txtSalPriceUnit.Text = "" Then MsgBox("فضلا ادخل سعر البيع", MsgBoxStyle.Exclamation) : txtSalPriceUnit.Focus() : Exit Sub
            'If txtTinPriceUnit.Text = "" Then MsgBox("فضلا ادخل سعر الشراء", MsgBoxStyle.Exclamation) : txtTinPriceUnit.Focus() : Exit Sub

            For i As Integer = 0 To dgvUnityItems.Rows.Count - 1
                If dgvUnityItems.Rows(i).Cells(0).Value = cmbUnity.Text Then
                    MsgBox("أسم الوحدة مسجلة مسبقا على نفس الصنف", MsgBoxStyle.Exclamation) : cmbUnity.Focus() : Exit Sub
                End If

                If dgvUnityItems.Rows(i).Cells(7).Value = txtitm_id_Unity.Text Then
                    MsgBox("باركود الوحدة مسجل مسبقا على نفس الصنف", MsgBoxStyle.Exclamation) : txtitm_id_Unity.Focus() : Exit Sub
                End If

                If dgvUnityItems.Rows(i).Cells(6).Value = "الاصغر" Then
                    If cmbUnitySize.Text = "الاصغر" Then
                        MsgBox("الوحدة الاصغر مسجلة مسبقا", MsgBoxStyle.Exclamation) : cmbUnitySize.Focus() : Exit Sub
                    End If
                End If
            Next

            If cmbUnitySize.Text = "الاصغر" Then
                If txtTinPriceUnit.Text <> 0 Then
                    txttinprice.Text = txtTinPriceUnit.Text
                End If
                If txtSalPriceUnit.Text <> 0 Then
                    txt_priseSal.Text = txtSalPriceUnit.Text
                End If
            End If
            If cmbUnitySize.Text = "الاكبر" Then
                If txtTinPriceUnit.Text <> 0 Then
                    'Dim X1 As Double = Math.Round(253.666667, 2)
                    'Dim X3 As Decimal = Val(12) * Val(9.58333333333333)
                    Dim TotalUnitySize As Double = Val(txtTinPriceUnit.Text) / Val(txtNumberPieces.Text)
                    txttinprice.Text = TotalUnitySize
                End If
            End If

            Dim DefaultPurchase As String = ""
            If chkDefaultTin.Checked = True Then : DefaultPurchase = "1" : Else DefaultPurchase = "0" : End If
            Dim DefaultSale As String = ""
            If chkDefaultSale.Checked = True Then : DefaultSale = "1" : Else DefaultSale = "0" : End If


            dgvUnityItems.DataSource = Fn_AddBillUnityItems(cmbUnity.Text, txtNumberPieces.Text, txtTinPriceUnit.Text, txtSalPriceUnit.Text, DefaultPurchase, DefaultSale, cmbUnitySize.Text, txtitm_id_Unity.Text)

            For i As Integer = 0 To dgvUnityItems.Rows.Count - 1
                If dgvUnityItems.Rows(i).Cells(6).Value = "الاصغر" Then
                    dgvUnityItems.Rows(i).Cells(2).Value = txttinprice.Text
                    dgvUnityItems.Rows(i).Cells(3).Value = txt_priseSal.Text
                End If
            Next

            cmbUnitySize.Text = ""
            txtTinPriceUnit.Text = "0"
            txtSalPriceUnit.Text = "0"
            txtNumberPieces.Text = ""
            txtitm_id_Unity.Text = ""
            'dgvUnityItems.Columns(2).Visible = False
            'dgvUnityItems.Columns(3).Visible = False
            dgvUnityItems.Columns(4).Visible = False
            dgvUnityItems.Columns(5).Visible = False

            dgvUnityItems.Columns(0).ReadOnly = True
            dgvUnityItems.Columns(1).ReadOnly = False
            dgvUnityItems.Columns(2).ReadOnly = False
            dgvUnityItems.Columns(3).ReadOnly = False
            dgvUnityItems.Columns(4).ReadOnly = True
            dgvUnityItems.Columns(5).ReadOnly = True
            dgvUnityItems.Columns(6).ReadOnly = True
            dgvUnityItems.Columns(7).ReadOnly = False
            cmbUnity.Focus()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Friend Function Fn_AddBillUnityItems(ByVal Col_Unity_Name As String, ByVal Col_NumberPieces As Double, ByVal Col_TinPriceUnit As Double, ByVal Col_SalPriceUnit As Double, ByVal Col_DefaultTin As Integer, ByVal Col_DefaultSale As Integer, ByVal Col_UnitySize As String, ByVal Col_itm_id_Unity As String) As DataTable
        Try
            If Dt_AddBill_UnityItems.Columns.Count = 0 Then
                Dt_AddBill_UnityItems.Columns.Add("أسم الوحدة", GetType(String))
                Dt_AddBill_UnityItems.Columns.Add("عدد القطع", GetType(Double))
                Dt_AddBill_UnityItems.Columns.Add("سعر الشراء", GetType(Double))
                Dt_AddBill_UnityItems.Columns.Add("سعر البيع", GetType(Double))
                Dt_AddBill_UnityItems.Columns.Add("أفتراضى للشراء", GetType(Double))
                Dt_AddBill_UnityItems.Columns.Add("أفتراضى للبيع", GetType(Double))
                Dt_AddBill_UnityItems.Columns.Add("حجم الوحدة", GetType(String))
                Dt_AddBill_UnityItems.Columns.Add("باركود الوحدة", GetType(String))
            End If

            Dt_AddBill_UnityItems.Rows.Add(Col_Unity_Name, Col_NumberPieces, Col_TinPriceUnit, Col_SalPriceUnit, Col_DefaultTin, Col_DefaultSale, Col_UnitySize, Col_itm_id_Unity)
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        Return Dt_AddBill_UnityItems
    End Function

    Private Sub btnDelBarcodeMore_Click(sender As Object, e As EventArgs) Handles btnDelBarcodeMore.Click
        Try
            If dgvBarcodeMore.RowCount = 0 Then Beep() : Exit Sub
            If (dgvBarcodeMore.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            For i As Integer = 0 To dgvBarcodeMore.SelectedRows.Count - 1
                RNXD = dgvBarcodeMore.CurrentRow.Index
                dgvBarcodeMore.Rows.RemoveAt(RNXD)
            Next
            txtNumberBarcodeMore.Text = dgvBarcodeMore.RowCount
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub brnDelUnityItems_Click(sender As Object, e As EventArgs) Handles brnDelUnityItems.Click
        Try
            If dgvUnityItems.RowCount = 0 Then Beep() : Exit Sub
            If (dgvUnityItems.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            For i As Integer = 0 To dgvUnityItems.SelectedRows.Count - 1
                RNXD = dgvUnityItems.CurrentRow.Index
                dgvUnityItems.Rows.RemoveAt(RNXD)
            Next
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbUnity_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbUnity.KeyUp
        If e.KeyCode = 13 Then
            txtNumberPieces.Focus()
        End If
    End Sub
    Private Sub txtUnity_Name_KeyUp(sender As Object, e As KeyEventArgs)
        If e.KeyCode = 13 Then
            txtNumberPieces.Focus()
        End If
    End Sub

    Private Sub GetItemsUnity()
        'Try
        If dgvUnityItems.Rows.Count = 0 Then
            Dim DefaultUnityName As String = mykey.GetValue("DefaultUnityName", "قطعة")
            If DefaultUnityName = "" Then
                DefaultUnityName = "قطعة"
            End If
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into ItemsUnity(itm_id,Unity_Name,NumberPieces,TinPriceUnit,SalPriceUnit,DefaultTin,DefaultSale,UnitySize_ID,itm_id_Unity,Company_Branch_ID)  values("
            S = S & "N'" & TxtPrc.Text.Trim & "',N'" & DefaultUnityName & "',N'1'," & Val(txttinprice.Text) & "," & Val(txt_priseSal.Text) & ",N'1',N'1',N'1',N'" & TxtPrc.Text & "',N'" & Company_Branch_ID & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
            UnityName = DefaultUnityName
        Else
            Dim UnitySize_ID As Double
            For i As Integer = 0 To dgvUnityItems.Rows.Count - 1
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select UnitySize_ID from ItemsUnitySize where UnitySize_Name=N'" & dgvUnityItems.Rows(i).Cells(6).Value & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    UnitySize_ID = dr("UnitySize_ID").ToString
                End If
                If UnitySize_ID = 1 Then
                    UnityName = dgvUnityItems.Rows(i).Cells(0).Value
                End If
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into ItemsUnity(itm_id,Unity_Name,NumberPieces,TinPriceUnit,SalPriceUnit,DefaultTin,DefaultSale,UnitySize_ID,itm_id_Unity,Company_Branch_ID)  values("
                S = S & "N'" & TxtPrc.Text.Trim & "',N'" & dgvUnityItems.Rows(i).Cells(0).Value & "',N'" & dgvUnityItems.Rows(i).Cells(1).Value & "',N'" & dgvUnityItems.Rows(i).Cells(2).Value & "',N'" & dgvUnityItems.Rows(i).Cells(3).Value & "',N'" & dgvUnityItems.Rows(i).Cells(4).Value & "',N'" & dgvUnityItems.Rows(i).Cells(5).Value & "',N'" & UnitySize_ID & "',N'" & dgvUnityItems.Rows(i).Cells(7).Value & "',N'" & Company_Branch_ID & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
        End If
        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub

    Private Sub txtNumberPieces_KeyUp(sender As Object, e As KeyEventArgs) Handles txtNumberPieces.KeyUp
        If e.KeyCode = 13 Then
            txtTinPriceUnit.Focus()
        End If
    End Sub

    Private Sub txtSalPriceUnit_KeyUp(sender As Object, e As KeyEventArgs) Handles txtSalPriceUnit.KeyUp
        If e.KeyCode = 13 Then
            cmbUnitySize.Focus()
        End If
    End Sub

    Private Sub txtTinPriceUnit_KeyUp(sender As Object, e As KeyEventArgs) Handles txtTinPriceUnit.KeyUp
        If e.KeyCode = 13 Then
            txtSalPriceUnit.Focus()
        End If
    End Sub

    Private Sub DTGV_DoubleClick(sender As Object, e As EventArgs) Handles DTGV.DoubleClick
        SubGetData()
    End Sub

    Private Sub SubGetData()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Try
            If DTGV.RowCount = 0 Then Beep() : Exit Sub
            If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub

            falesItems()

            Dim PriceIncludesVAT As String = ""


            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select  group_name,group_branch,sname,Unity,rng,store,itm_id,TinPrice,SalPrice,WholePrice,WholeWholePrice,MinimumSalPrice,Stores,QuickSearch,BalanceBarcode,RateWholePrice,RateWholeWholePrice,RateMinimumSalPrice,RateVAT,RateDiscTinPrice,RateDiscSalPrice,RateDiscWholePrice,RateDiscWholeWholePrice,RateDiscTinPriceAfter,RateDiscSalPriceAfter,RateDiscWholePriceAfter,RateDiscWholeWholePriceAfter,PriceIncludesVAT,TypePlusDiscRateSalPrice,TypePlusDiscRateWholePrice,TypePlusDiscRateWholeWholePrice,DeferredCurrentDiscount,Vendorname from items where itm_id =N'" & DTGV.SelectedRows(0).Cells(1).Value & "' and Stores=N'" & DTGV.SelectedRows(0).Cells(13).Value & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then Exit Sub
            Dim group_name, group_branch, sname, Unity, rng, store, Prc, prcSal, tin, WholePrice, WholeWholePrice, MinimumSalPrice, Stores, QuickSearch, BalanceBarcode, RateWholePrice, RateWholeWholePrice, RateMinimumSalPrice, RateVAT, DeferredCurrentDiscount As String
            If dr(0) Is DBNull.Value Then
            Else
                group_name = dr(0)
            End If
            If dr(1) Is DBNull.Value Then
            Else
                group_branch = dr(1)
            End If
            If dr(2) Is DBNull.Value Then
            Else
                sname = dr(2)
            End If
            If dr(3) Is DBNull.Value Then
            Else
                Unity = dr(3)
            End If
            If dr(4) Is DBNull.Value Then
            Else
                rng = dr(4)
            End If
            If dr(5) Is DBNull.Value Then
            Else
                store = dr(5)
            End If
            If dr(6) Is DBNull.Value Then
            Else
                Prc = dr(6)
            End If
            If dr(7) Is DBNull.Value Then
            Else
                tin = dr(7)
            End If
            If dr(8) Is DBNull.Value Then
            Else
                prcSal = dr(8)
            End If
            If dr(9) Is DBNull.Value Then
            Else
                WholePrice = dr(9)
            End If
            If dr(10) Is DBNull.Value Then
            Else
                WholeWholePrice = dr(10)
            End If
            If dr(11) Is DBNull.Value Then
            Else
                MinimumSalPrice = dr(11)
            End If
            If dr(12) Is DBNull.Value Then
            Else
                Stores = dr(12)
            End If
            If dr(13) Is DBNull.Value Then
            Else
                QuickSearch = dr(13)
            End If
            If dr(14) Is DBNull.Value Then
            Else
                BalanceBarcode = dr(14)
            End If
            If dr(15) Is DBNull.Value Then
            Else
                RateWholePrice = dr(15)
            End If
            If dr(16) Is DBNull.Value Then
            Else
                RateWholeWholePrice = dr(16)
            End If
            If dr(17) Is DBNull.Value Then
            Else
                RateMinimumSalPrice = dr(17)
            End If
            If dr(18) Is DBNull.Value Then
            Else
                RateVAT = dr(18)
            End If
            If dr(19) Is DBNull.Value Then
            Else
                txtRateDiscTinPrice.Text = dr(19)
            End If
            If dr(20) Is DBNull.Value Then
            Else
                txtRateDiscSalPrice.Text = dr(20)
            End If
            If dr(21) Is DBNull.Value Then
            Else
                txtRateDiscWholePrice.Text = dr(21)
            End If
            If dr(22) Is DBNull.Value Then
            Else
                txtRateDiscWholeWholePrice.Text = dr(22)
            End If
            If dr(23) Is DBNull.Value Then
            Else
                txtRateDiscTinPriceAfter.Text = dr(23)
            End If
            If dr(24) Is DBNull.Value Then
            Else
                txtRateDiscSalPriceAfter.Text = dr(24)
            End If
            If dr(25) Is DBNull.Value Then
            Else
                txtRateDiscWholePriceAfter.Text = dr(25)
            End If
            If dr(26) Is DBNull.Value Then
            Else
                txtRateDiscWholeWholePriceAfter.Text = dr(26)
            End If
            If dr(27) Is DBNull.Value Then
            Else
                PriceIncludesVAT = dr(27)
            End If
            If dr(28) Is DBNull.Value Then
            Else
                If dr(28).ToString = "0" Then
                    rdoDiscRateSalPrice.Checked = True
                Else
                    rdoPlusRateSalPrice.Checked = True
                End If
            End If
            If dr(29) Is DBNull.Value Then
            Else
                If dr(29).ToString = "0" Then
                    rdoDiscRateWholePrice.Checked = True
                Else
                    rdoPlusRateWholePrice.Checked = True
                End If
            End If
            If dr(30) Is DBNull.Value Then
            Else
                If dr(30).ToString = "0" Then
                    rdoDiscRateWholeWholePrice.Checked = True
                Else
                    rdoPlusRateWholeWholePrice.Checked = True
                End If
            End If
            If dr(31) Is DBNull.Value Then
            Else
                If dr(31).ToString = "0" Then
                    rdoCurrentDiscTinPrice.Checked = True
                Else
                    rdoDeferredDiscTinPrice.Checked = True
                End If
            End If
            If dr(32) Is DBNull.Value Then
            Else
                cmbvendores.Text = dr(32)
            End If

            If PriceIncludesVAT = "1" Then
                chkPriceIncludesVAT.Checked = True
            Else
                chkPriceIncludesVAT.Checked = False
            End If

            Dim bill_EndDate, Expired, EndDate As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select  bill_EndDate,Expired,bill_ProductionDate,Vendorname from BilltINData where itm_id =N'" & DTGV.SelectedRows(0).Cells(1).Value & "' and Stores=N'" & DTGV.SelectedRows(0).Cells(13).Value & "' and bill_no = N'جرد'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                If dr(0) Is DBNull.Value Then
                Else
                    bill_EndDate = dr(0).ToString
                End If
                If dr(1) Is DBNull.Value Then
                Else
                    Expired = dr(1).ToString
                End If
                If dr(2) Is DBNull.Value Then
                Else
                    dtpDateItem.Text = Cls.R_date(dr(2).ToString)
                End If
                If dr(3) Is DBNull.Value Then
                Else
                    cmbvendores.Text = dr(3).ToString
                End If
                If bill_EndDate <> "" Then
                    EndDate = Cls.R_date(bill_EndDate)
                Else
                    EndDate = ""
                End If
            Else
                Expired = "بدون صلاحية"
            End If

            If QuickSearch = "0" Then
                chkQuickSearch.Checked = True
            Else
                chkQuickSearch.Checked = False
            End If

            If BalanceBarcode = "0" Then
                cmbBalanceBarcode.Text = "بدون ميزان الباركود"
            Else
                cmbBalanceBarcode.Text = "ميزان الباركود"
            End If

            cmbcats.Text = group_name : cmbGroup_Branch.Text = group_branch : cmbitmnm.Text = sname : cmbUnity.Text = Unity : txtrng.Text = Val(rng)
            txtqunt.Text = store : TxtPrc.Text = Prc : itmprc = Prc : txttinprice.Text = tin : txt_priseSal.Text = prcSal : txtWholePrice.Text = WholePrice : txtWholeWholePrice.Text = WholeWholePrice : txtMinimumSalPrice.Text = MinimumSalPrice : cmbStores.Text = Stores : dtpExpiration.Text = EndDate : cmb_Expired.Text = Expired : txtRateVAT.Text = RateVAT

            If Expired <> "بدون صلاحية" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select bill_date from BilltINData where bill_date <> N'جرد' and itm_id =N'" & TxtPrc.Text & "' and Stores =N'" & cmbStores.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    Dim Xbill_date As String
                    If dr("bill_date") Is DBNull.Value Then
                    Else
                        Xbill_date = dr("bill_date")
                    End If
                    dtpDateItem.Text = Cls.R_date(Xbill_date)
                End If
            End If

            SHOWPHOTO()

            If BarcodeMore = "YES" Then
                Dt_AddBill_BarcodeMore.Rows.Clear()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "select itm_id_More from BarcodeMore where itm_id =N'" & TxtPrc.Text & "'" : cmd.CommandText = S : dr = cmd.ExecuteReader
                Do While dr.Read
                    dgvBarcodeMore.DataSource = Fn_AddBillBarcodeMore(dr(0).ToString)
                Loop
            End If


            GetDataItemsAlternative()

            GetAddItemsUnity()

            txtqunt.Text = 0
            cmbStores.Text = ""

            'If chkEditFristStore.Checked = True Then
            '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '    cmd.CommandText = "select  qu from BilltINData where itm_id =N'" & TxtPrc.Text & "' and Stores=N'" & cmbStores.Text & "' and bill_no = N'جرد'" : dr = cmd.ExecuteReader : dr.Read()
            '    If dr.HasRows = True Then
            '        txtqunt.Text = dr(0).ToString
            '        txtqunt.Enabled = True
            '    End If
            'End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

    End Sub

    Private Sub falesItems()
        cmbcats.Enabled = False
        TxtPrc.Enabled = False
        Button1.Enabled = False
        cmbGroup_Branch.Enabled = False
        cmbitmnm.Enabled = False
        TxtPrc.Enabled = False
        txttinprice.Enabled = False
        txttinprice.Enabled = False
        txtRatePriceSale.Enabled = False
        txt_priseSal.Enabled = False
        txtWholePrice.Enabled = False
        txtWholeWholePrice.Enabled = False
        txtWholeWholePrice.Enabled = False
        txtWholeWholePrice.Enabled = False
        txtMinimumSalPrice.Enabled = False
        txtrng.Enabled = False
        txtqunt.Enabled = False
        txtRateVAT.Enabled = False
        txtRatePriceSale.Enabled = False
        txtRateWholePrice.Enabled = False
        txtRateWholeWholePrice.Enabled = False
        txtRateMinimumSalPrice.Enabled = False
        txtRateDiscTinPrice.Enabled = False
        txtRateDiscSalPrice.Enabled = False
        txtRateDiscWholePrice.Enabled = False
        txtRateDiscWholeWholePrice.Enabled = False
        cmbvendores.Enabled = False
        Label41.Enabled = False
        cmb_Expired.Enabled = False
        dtpExpiration.Enabled = False
        dtpProductionDate.Enabled = False
        cmbBalanceBarcode.Enabled = False
    End Sub

    Private Sub trueItems()
        cmbcats.Enabled = True
        TxtPrc.Enabled = True
        Button1.Enabled = True
        cmbGroup_Branch.Enabled = True
        cmbitmnm.Enabled = True
        TxtPrc.Enabled = True
        txttinprice.Enabled = True
        txttinprice.Enabled = True
        txtRatePriceSale.Enabled = True
        txt_priseSal.Enabled = True
        txtWholePrice.Enabled = True
        txtWholeWholePrice.Enabled = True
        txtWholeWholePrice.Enabled = True
        txtWholeWholePrice.Enabled = True
        txtMinimumSalPrice.Enabled = True
        txtrng.Enabled = True
        txtqunt.Enabled = True
        txtRateVAT.Enabled = True
        txtRatePriceSale.Enabled = True
        txtRateWholePrice.Enabled = True
        txtRateWholeWholePrice.Enabled = True
        txtRateMinimumSalPrice.Enabled = True
        txtRateDiscTinPrice.Enabled = True
        txtRateDiscSalPrice.Enabled = True
        txtRateDiscWholePrice.Enabled = True
        txtRateDiscWholeWholePrice.Enabled = True
        cmbvendores.Enabled = True
        Label41.Enabled = True
        cmb_Expired.Enabled = True
        dtpExpiration.Enabled = True
        dtpProductionDate.Enabled = True
        cmbBalanceBarcode.Enabled = True
    End Sub

    Private Sub GetAddItemsUnity()
        Try
            Dt_AddBill_UnityItems.Rows.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "select Unity_Name,NumberPieces,TinPriceUnit,SalPriceUnit,DefaultTin,DefaultSale,UnitySize_Name,itm_id_Unity from View_ItemsUnitySize where itm_id =N'" & TxtPrc.Text & "'" : cmd.CommandText = S : dr = cmd.ExecuteReader
            Do While dr.Read
                dgvUnityItems.DataSource = Fn_AddBillUnityItems(dr(0).ToString, dr(1).ToString, dr(2).ToString, dr(3).ToString, dr(4).ToString, dr(5).ToString, dr(6).ToString, dr(7).ToString)
            Loop
            'dgvUnityItems.Columns(2).Visible = False
            'dgvUnityItems.Columns(3).Visible = False
            dgvUnityItems.Columns(4).Visible = False
            dgvUnityItems.Columns(5).Visible = False

            dgvUnityItems.Columns(0).ReadOnly = True
            dgvUnityItems.Columns(1).ReadOnly = False
            dgvUnityItems.Columns(2).ReadOnly = False
            dgvUnityItems.Columns(3).ReadOnly = False
            dgvUnityItems.Columns(4).ReadOnly = True
            dgvUnityItems.Columns(5).ReadOnly = True
            dgvUnityItems.Columns(6).ReadOnly = True
            dgvUnityItems.Columns(7).ReadOnly = False
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

    End Sub

    Private Sub SHOWPHOTO()
        Try
            If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim XiIDTM As String
            XiIDTM = DTGV.SelectedRows(0).Cells(0).Value
            If NetworkName = "Yes" Then
                If UseExternalServer = "Yes" Then
                    connect()
                End If
            End If
            connectionStringOpen()
            Dim sql As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            sql = "SELECT Items_Images FROM Items WHERE itm_id =N'" & XiIDTM & "'"
            Dim cmd As SqlClient.SqlCommand = New SqlClient.SqlCommand(sql, Cn)
            Dim by() As Byte
            by = cmd.ExecuteScalar()
            If (by.Length > 0) Then
                Dim stream As New MemoryStream(by, True)
                stream.Write(by, 0, by.Length)
                PicLogo.Image = New Bitmap(stream)
                picWorkShowImage.Image = New Bitmap(stream)
                stream.Close()
            Else
                Me.PicLogo.Image = Nothing
                Me.picWorkShowImage.Image = Nothing
            End If
        Catch ex As Exception
            'ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub btnClear_Click(sender As Object, e As EventArgs) Handles btnClear.Click
        Clear_All()
        If chkAutomaticBarcodeNumber.Checked = True Then
            TxtPrc.Text = MaxRecordTables("Items", "itm_id")
        End If
        cmbitmnm.Focus()
        cmbUnity.Text = mykey.GetValue("DefaultUnityName", "قطعة")
        cmbStores.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        txtrng.Text = mykey.GetValue("RngDefaultStock", "2")
        txtNumberPieces.Text = 1
        trueItems()
    End Sub

    Private Sub AutoCompleteCustomSource()
        Try
            Dim Conn As SqlConnection
            Dim Dr As SqlDataAdapter
            Dim Dt As New DataTable
            Conn = New SqlConnection(constring)

            Dr = New SqlDataAdapter("Select sname From Items", Conn)
            Dr.Fill(Dt)
            Dim DataSource As New AutoCompleteStringCollection
            For i As Integer = 0 To Dt.Rows.Count - 1
                DataSource.Add(Dt.Rows(i)(0).ToString)
            Next

            TextBox1.AutoCompleteCustomSource = DataSource
            TextBox1.AutoCompleteSource = AutoCompleteSource.CustomSource
            TextBox1.AutoCompleteMode = AutoCompleteMode.SuggestAppend
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub TxtPrc_TextChanged(sender As Object, e As EventArgs) Handles TxtPrc.TextChanged

    End Sub

    Private Sub cmbUnitySize_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbUnitySize.KeyUp
        If e.KeyCode = 13 Then
            txtitm_id_Unity.Focus()
        End If
    End Sub

    Private Sub txtitm_id_Unity_KeyUp(sender As Object, e As KeyEventArgs) Handles txtitm_id_Unity.KeyUp
        If e.KeyCode = 13 Then
            btnAddUnityItems.PerformClick()
        End If
    End Sub

    Private Sub txtitm_id_Unity_TextChanged(sender As Object, e As EventArgs) Handles txtitm_id_Unity.TextChanged
        If dgvUnityItems.Rows.Count = 0 Then
            TxtPrc.Text = txtitm_id_Unity.Text
        End If
    End Sub

    Private Sub PrintParcode()

        If SettingPrinterAuto = "YES" Then
            Dim DefaultPrinterBarcode As String = mykey.GetValue("DefaultPrinterBarcode", "DefaultPrinterBarcode")
            If Not Cls.SetDefaultSystemPrinter4(DefaultPrinterBarcode) Then
                MessageBox.Show("Error occured will trying to set the default printer!")
            End If
        End If


        GetValueDefaultBarcodePaper()
        Dim X As String = "!" & TxtPrc.Text & "!"
        DataGridViewParcode.DataSource = Fn_AddBill_Parcode(X, cmbcats.Text, cmbitmnm.Text, txt_priseSal.Text, txtqunt.Text)


        If DataGridViewParcode.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If
        AddReportView()
        Try
            If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

            Cls.delete_Branch_All("PrintAllItems")

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()

            For m As Integer = 0 To txtqunt.Text - 1

                S = "insert into PrintAllItems (Company_Branch_ID,itm_id,group_name,sname,TinPrice,qunt,Notes)  values ("
                S = S & "N'" & Company_Branch_ID & "',N'" & TxtPrc.Text & "',N'" & cmbcats.Text & "',N'" & cmbitmnm.Text & "',N'" & txt_priseSal.Text & "',"
                S = S & "N'" & txtqunt.Text & "',N'" & txtqunt.Text & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next

            Dim rpt
            If rdoPaperHalves1.Checked = True Then
                rpt = New rpt_Parcode10
            End If
            If rdoPaperHalves2.Checked = True Then
                rpt = New rpt_Parcode5
            End If
            If rdoOneRow.Checked = True Then
                If chkShowPrice.Checked = True Then
                    If rdoFonts7.Checked = True Then
                        rpt = New rpt_Parcode7
                    End If
                    If rdoFonts9.Checked = True Then
                        rpt = New rpt_Parcode9
                    End If
                Else
                    If rdoFonts7.Checked = True Then
                        rpt = New rpt_Parcode6
                    End If
                    If rdoFonts9.Checked = True Then
                        rpt = New rpt_Parcode8
                    End If
                End If
            End If
            If rdoMediumBarCode2.Checked = True Then
                If chkShowPrice.Checked = True Then
                    rpt = New rpt_ParcodeMediumBarCode_Price2
                Else
                    rpt = New rpt_ParcodeMediumBarCode2
                End If
            End If
            If rdoThreeRows.Checked = True Then
                rpt = New rpt_Parcode2
            End If
            If rdoOneRow13Number.Checked = True Then
                If chkShowPrice.Checked = True Then
                    rpt = New rpt_Parcode13NumberPrice
                Else
                    rpt = New rpt_Parcode13Number
                End If
            End If
            If rdoParcodeExpirationDate.Checked = True Then
                rpt = New rpt_ParcodeExpirationDate
            End If

            Cls.Select_More_Data_Branch_Print("PrintAllItems", "*")
            Dim dt As New DataTable
            dt.Load(dr)
            rpt.SetDataSource(dt)

            Dim txtNameAr, txtType_Currency As TextObject
            If rdoMediumBarCode2.Checked = True Then
                txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
                txtNameAr.Text = NameArCompay
            End If
            Dim ExpirationDate As String = mykey.GetValue("ExpirationDate", "YES")
            If ExpirationDate = "YES" Then
                txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
                txtNameAr.Text = NameArCompay
            End If
            If chkShowPrice.Checked = True Then
                If chkShowPrice.Enabled = True Then
                    Dim Type_Currency As String = mykey.GetValue("TypeCurrency", "جنية مصرى")
                    If Type_Currency = "" Then
                        Type_Currency = "LE: "
                    Else
                        txtType_Currency = rpt.Section1.ReportObjects("txtTypeCurrency")
                        txtType_Currency.Text = Type_Currency
                    End If
                End If
            End If

            If chkViewPrint.Checked = True Then
                Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
                Frm_PrintReports.Show()
            Else
                rpt.PrintToPrinter(1, False, 0, 0)
            End If

            Dt_AddBill.Rows.Clear()

            If RunDatabaseInternet = "YES" Then : connect() : End If

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Dim Dt_AddBillParcode As New DataTable
    Friend Function Fn_AddBill_Parcode(ByVal Col_Prc As String, ByVal Col_Cats As String, ByVal Col_Name As String _
, ByVal Col_Price As Double, ByVal Col_Quant As Double) As DataTable
        Try
            If Dt_AddBillParcode.Columns.Count = 0 Then
                Dt_AddBillParcode.Columns.Add("الباركود", GetType(String))
                Dt_AddBillParcode.Columns.Add("المجموعة", GetType(String))
                Dt_AddBillParcode.Columns.Add("الاسم", GetType(String))
                Dt_AddBillParcode.Columns.Add("السعر", GetType(Double))
                Dt_AddBillParcode.Columns.Add("الكمية", GetType(Double))
            End If

            Dt_AddBillParcode.Rows.Add(Col_Prc, Col_Cats, Col_Name, Col_Price, Col_Quant)
            Return Dt_AddBillParcode
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Function

    Private Sub GetValueDefaultBarcodePaper()
        Dim DefaultBarcodePaper As String = mykey.GetValue("DefaultBarcodePaper", "PaperHalves1")
        If DefaultBarcodePaper = "PaperHalves1" Then
            rdoPaperHalves1.Checked = True
        End If
        If DefaultBarcodePaper = "PaperHalves2" Then
            rdoPaperHalves2.Checked = True
        End If
        If DefaultBarcodePaper = "ThreeRows" Then
            rdoThreeRows.Checked = True
        End If
        If DefaultBarcodePaper = "OneRow" Then
            rdoOneRow.Checked = True
        End If
        If DefaultBarcodePaper = "MediumBarCode2" Then
            rdoMediumBarCode2.Checked = True
        End If
        If DefaultBarcodePaper = "OneRow13Number" Then
            rdoOneRow13Number.Checked = True
        End If
        If DefaultBarcodePaper = "ParcodeExpirationDate" Then
            rdoParcodeExpirationDate.Checked = True
        End If
    End Sub

    Private Sub btnClosePaperType_Click(sender As Object, e As EventArgs) Handles btnClosePaperType.Click
        PanelPaperType.Top = 5000
    End Sub

    Private Sub btnPaperType_Click(sender As Object, e As EventArgs) Handles btnPaperType.Click
        PanelPaperType.Top = 284
        PanelPaperType.Left = 278
        PanelPaperType.Size = New System.Drawing.Size(660, 200)
    End Sub

    Private Sub txtRateWholePrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateWholePrice.TextChanged
        MyVars.CheckNumber(txtRateWholePrice)
        If txttinprice.Text = "." Then
            txttinprice.Text = "0."
        End If

        If txtRateWholePrice.Text <> "0" Then
            Try
                Dim DiscVal As Double
                DiscVal = Val((Val(txttinprice.Text) * (100 + Val(txtRateWholePrice.Text))) / 100)
                txtWholePrice.Text = Math.Round(DiscVal, 4)
            Catch ex As Exception
            End Try
        End If
    End Sub

    Private Sub txtRateWholeWholePrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateWholeWholePrice.TextChanged
        MyVars.CheckNumber(txtRateWholeWholePrice)
        If txttinprice.Text = "." Then
            txttinprice.Text = "0."
        End If

        If txtRateWholeWholePrice.Text <> "0" Then
            Try
                Dim DiscVal As Double
                DiscVal = Val((Val(txttinprice.Text) * (100 + Val(txtRateWholeWholePrice.Text))) / 100)
                txtWholeWholePrice.Text = Math.Round(DiscVal, 4)
            Catch ex As Exception
            End Try
        End If
    End Sub

    Private Sub txtRateMinimumSalPrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateMinimumSalPrice.TextChanged
        MyVars.CheckNumber(txtRateMinimumSalPrice)
        If txttinprice.Text = "." Then
            txttinprice.Text = "0."
        End If

        If txtRateMinimumSalPrice.Text <> "0" Then
            Try
                Dim DiscVal As Double
                DiscVal = Val((Val(txttinprice.Text) * (100 + Val(txtRateMinimumSalPrice.Text))) / 100)
                txtMinimumSalPrice.Text = Math.Round(DiscVal, 4)
            Catch ex As Exception
            End Try
        End If
    End Sub

    Private Sub txtRateVAT_TextChanged(sender As Object, e As EventArgs) Handles txtRateVAT.TextChanged
        MyVars.CheckNumber(txtRateVAT)
    End Sub

    Private Sub dgvUnityItems_CellValueChanged(sender As Object, e As DataGridViewCellEventArgs) Handles dgvUnityItems.CellValueChanged
        If dgvUnityItems.RowCount = 0 Then Beep() : Exit Sub
        If (dgvUnityItems.Rows.Count) = 0 Then Beep() : Exit Sub
        Dim NumberPieces As String = dgvUnityItems.SelectedRows(0).Cells(1).Value
        Dim TinPrice As String = dgvUnityItems.SelectedRows(0).Cells(2).Value
        Dim SalPrice As String = dgvUnityItems.SelectedRows(0).Cells(3).Value
        Dim Parcode As String = dgvUnityItems.SelectedRows(0).Cells(7).Value
        If NumberPieces = 0 Or NumberPieces = 1 Then
            TxtPrc.Text = Parcode
            If TinPrice <> 0 Then
                txttinprice.Text = TinPrice
            End If
            If SalPrice <> 0 Then
                txt_priseSal.Text = SalPrice
            End If
        End If
    End Sub

#End Region


    Function MaxRecordTablesItems(ByVal Tables As String, ByVal Code As String)
        Try
            Dim MaxRecoedCode As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from " & Tables & ""
            dr = cmd.ExecuteReader
            Dim dt As New DataTable
            dt.Load(dr)

            If dt.Rows.Count = 0 Then
                MaxRecoedCode = 1
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(CAST(" & Code & " As float)) as mb FROM " & Tables & " where " & Code & " <> N''"
                dr = cmd.ExecuteReader
                dr.Read()

                Dim sh As Long
                sh = dr("mb")
                MaxRecoedCode = sh + 1
            End If
            Return MaxRecoedCode
        Catch ex As Exception
        End Try
    End Function

    Private Sub btnCloseItemDiscountRate_Click(sender As Object, e As EventArgs) Handles btnCloseItemDiscountRate.Click
        PanelItemDiscountRate.Top = 5000
    End Sub

    Private Sub btnItemDiscountRate_Click(sender As Object, e As EventArgs) Handles btnItemDiscountRate.Click
        txtRateDiscTinPrice.Focus()
        txtRateDiscTinPrice.Select()
        PanelItemDiscountRate.Top = 205
        PanelItemDiscountRate.Size = New System.Drawing.Size(580, 230)
    End Sub

    Private Sub txtItemDiscRateTinPrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateDiscTinPrice.TextChanged
        MyVars.CheckNumber(txtRateDiscTinPrice)
        GetRateDiscPriceAfter()
    End Sub

    Private Sub txtItemDiscRateSalPrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateDiscSalPrice.TextChanged
        MyVars.CheckNumber(txtRateDiscSalPrice)
        GetRateDiscPriceAfter()
    End Sub

    Private Sub txtItemDiscRateWholePrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateDiscWholePrice.TextChanged
        MyVars.CheckNumber(txtRateDiscWholePrice)
        GetRateDiscPriceAfter()
    End Sub

    Private Sub txtItemDiscRateWholeWholePrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateDiscWholeWholePrice.TextChanged
        MyVars.CheckNumber(txtRateDiscWholeWholePrice)
        GetRateDiscPriceAfter()
    End Sub

    Private Sub txtItemDiscRateTinPrice_KeyUp(sender As Object, e As KeyEventArgs) Handles txtRateDiscTinPrice.KeyUp
        If e.KeyCode = 13 Then
            txtRateDiscSalPrice.Focus()
            txtRateDiscSalPrice.Select()
        End If
    End Sub

    Private Sub txtItemDiscRateSalPrice_KeyUp(sender As Object, e As KeyEventArgs) Handles txtRateDiscSalPrice.KeyUp
        If e.KeyCode = 13 Then
            txtRateDiscWholePrice.Focus()
            txtRateDiscWholePrice.Select()
        End If
    End Sub

    Private Sub txtItemDiscRateWholePrice_KeyUp(sender As Object, e As KeyEventArgs) Handles txtRateDiscWholePrice.KeyUp
        If e.KeyCode = 13 Then
            txtRateDiscWholeWholePrice.Focus()
            txtRateDiscWholeWholePrice.Select()
        End If
    End Sub

    Private Sub txtRateDiscWholeWholePrice_KeyUp(sender As Object, e As KeyEventArgs) Handles txtRateDiscWholeWholePrice.KeyUp
        If e.KeyCode = 13 Then
            PanelItemDiscountRate.Top = 5000
        End If
    End Sub

    Private Sub PictureBox2_Click(sender As Object, e As EventArgs) Handles PictureBox2.Click
        Headerx()
    End Sub

    Private Sub GetRateDiscPriceAfter()

        txtRateDiscTinPriceAfter.Text = Val((Val(txttinprice.Text) * (100 - Val(txtRateDiscTinPrice.Text))) / 100)
        txtRateDiscTinPriceAfter.Text = Math.Round(Val(txtRateDiscTinPriceAfter.Text), 2)

        If rdoDiscRateSalPrice.Checked = True Then
            txtRateDiscSalPriceAfter.Text = Val((Val(txt_priseSal.Text) * (100 - Val(txtRateDiscSalPrice.Text))) / 100)
            txtRateDiscSalPriceAfter.Text = Math.Round(Val(txtRateDiscSalPriceAfter.Text), 2)
        Else
            txtRateDiscSalPriceAfter.Text = Val((Val(txt_priseSal.Text) * (100 + Val(txtRateDiscSalPrice.Text))) / 100)
            txtRateDiscSalPriceAfter.Text = Math.Round(Val(txtRateDiscSalPriceAfter.Text), 2)
        End If
        If rdoDiscRateWholePrice.Checked = True Then
            txtRateDiscWholePriceAfter.Text = Val((Val(txtWholePrice.Text) * (100 - Val(txtRateDiscWholePrice.Text))) / 100)
            txtRateDiscWholePriceAfter.Text = Math.Round(Val(txtRateDiscWholePriceAfter.Text), 2)
        Else
            txtRateDiscWholePriceAfter.Text = Val((Val(txtWholePrice.Text) * (100 + Val(txtRateDiscWholePrice.Text))) / 100)
            txtRateDiscWholePriceAfter.Text = Math.Round(Val(txtRateDiscWholePriceAfter.Text), 2)
        End If
        If rdoDiscRateWholeWholePrice.Checked = True Then
            txtRateDiscWholeWholePriceAfter.Text = Val((Val(txtWholeWholePrice.Text) * (100 - Val(txtRateDiscWholeWholePrice.Text))) / 100)
            txtRateDiscWholeWholePriceAfter.Text = Math.Round(Val(txtRateDiscWholeWholePriceAfter.Text), 2)
        Else
            txtRateDiscWholeWholePriceAfter.Text = Val((Val(txtWholeWholePrice.Text) * (100 + Val(txtRateDiscWholeWholePrice.Text))) / 100)
            txtRateDiscWholeWholePriceAfter.Text = Math.Round(Val(txtRateDiscWholeWholePriceAfter.Text), 2)
        End If
    End Sub

    Private Sub btnItemsAlternative_Click(sender As Object, e As EventArgs) Handles btnItemsAlternative.Click
        PanelAlternative.Top = 159
        PanelAlternative.Left = 278
        PanelAlternative.Size = New System.Drawing.Size(670, 341)
        cmbTradeName.Focus()
    End Sub

    Private Sub btnCloseAlternative_Click(sender As Object, e As EventArgs) Handles btnCloseAlternative.Click
        PanelAlternative.Top = 5000
    End Sub

    Private Sub btnAddAlternative_Click(sender As Object, e As EventArgs) Handles btnAddAlternative.Click
        Dim Xitm_id As String
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id from items where sname= '" & cmbTradeName.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            Xitm_id = dr(0)
        End If

        For i As Integer = 0 To Dt_AddBill.Rows.Count - 1
            If DGVAlternative.Rows(i).Cells(0).Value = cmbTradeName.Text.Trim Then MsgBox("صنف مكرر بنفس الفاتورة", MsgBoxStyle.Exclamation) : TxtPrc.Focus() : TxtPrc.SelectAll() : Exit Sub
        Next

        If cmbTradeName.Text = "" Then MsgBox("فضلا حدد اإسم الصنف التجارى", MsgBoxStyle.Exclamation) : cmbTradeName.Focus() : Exit Sub

        DGVAlternative.DataSource = Fn_AddBillAlternative(TxtPrc.Text, Xitm_id, cmbTradeName.Text, cmbScientificName.Text, txtDrugConcentration.Text)

        cmbTradeName.Text = ""
        cmbScientificName.Text = ""
        txtDrugConcentration.Text = ""
        cmbTradeName.Focus()
    End Sub


    Friend Function Fn_AddBillAlternative(ByVal Col_itm_id As String, ByVal Col_itm_idTradeName As Double, ByVal Col_TradeName As String, ByVal Col_ScientificName As String, ByVal Col_DrugConcentration As String) As DataTable
        If Dt_AddBillAlternative.Columns.Count = 0 Then
            Dt_AddBillAlternative.Columns.Add("الباركود", GetType(String))
            Dt_AddBillAlternative.Columns.Add("الباركود2", GetType(Double))
            Dt_AddBillAlternative.Columns.Add("الاسم التجارى", GetType(String))
            Dt_AddBillAlternative.Columns.Add("الاسم العلمى", GetType(String))
            Dt_AddBillAlternative.Columns.Add("التركيز", GetType(String))
        End If

        Dt_AddBillAlternative.Rows.Add(Col_itm_id, Col_itm_idTradeName, Col_TradeName, Col_ScientificName, Col_DrugConcentration)

        Return Dt_AddBillAlternative

    End Function

    Private Sub GetAddItemsAlternative()
        If DealingPharmacySystem = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To DGVAlternative.Rows.Count - 1
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into ItemsAlternative(itm_id,itm_idTradeName,TradeName,ScientificName,DrugConcentration)  values("
                S = S & "'" & DGVAlternative.Rows(i).Cells(0).Value & "','" & DGVAlternative.Rows(i).Cells(1).Value & "','" & DGVAlternative.Rows(i).Cells(2).Value & "','" & DGVAlternative.Rows(i).Cells(3).Value & "','" & DGVAlternative.Rows(i).Cells(4).Value & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
        End If
    End Sub

    Private Sub GetDataItemsAlternative()
        If DealingPharmacySystem = "YES" Then
            Dt_AddBillAlternative.Rows.Clear()
            Dim itmprc As String = ""
            itmprc = DTGV.SelectedRows(0).Cells(1).Value

            Dt_AddBill.Rows.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "select itm_id,itm_idTradeName, TradeName, ScientificName, DrugConcentration from ItemsAlternative where itm_id = '" & itmprc & "'" : cmd.CommandText = S : dr = cmd.ExecuteReader
            Do While dr.Read
                DGVAlternative.DataSource = Fn_AddBillAlternative(dr(0).ToString(), dr(1).ToString(), dr(2), dr(3).ToString(), dr(4).ToString())
            Loop

            PanelAlternative.Top = 159
            PanelAlternative.Left = 278
            PanelAlternative.Size = New System.Drawing.Size(773, 341)
            cmbTradeName.Focus()
        End If

    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs) Handles btnDeleteAlternative.Click
        If DGVAlternative.RowCount = 0 Then Beep() : Exit Sub
        If (DGVAlternative.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        RNXD = DGVAlternative.CurrentRow.Index
        DGVAlternative.Rows.RemoveAt(RNXD)
    End Sub

    Private Sub cmbTradeName_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbTradeName.KeyUp
        If e.KeyCode = 13 Then
            cmbScientificName.Focus()
        End If
    End Sub

    Private Sub cmbScientificName_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbScientificName.KeyUp
        If e.KeyCode = 13 Then
            txtDrugConcentration.Focus()
        End If
    End Sub

    Private Sub txtDrugConcentration_KeyUp(sender As Object, e As KeyEventArgs) Handles txtDrugConcentration.KeyUp
        If e.KeyCode = 13 Then
            btnAddAlternative.PerformClick()
        End If
    End Sub

    Private Sub rdoDiscRateSalPrice_CheckedChanged(sender As Object, e As EventArgs) Handles rdoDiscRateSalPrice.CheckedChanged
        GetRateDiscPriceAfter()
    End Sub

    Private Sub rdoPlusRateSalPrice_CheckedChanged(sender As Object, e As EventArgs) Handles rdoPlusRateSalPrice.CheckedChanged
        GetRateDiscPriceAfter()
    End Sub

    Private Sub rdoDiscRateWholePrice_CheckedChanged(sender As Object, e As EventArgs) Handles rdoDiscRateWholePrice.CheckedChanged
        GetRateDiscPriceAfter()
    End Sub

    Private Sub rdoPlusRateWholePrice_CheckedChanged(sender As Object, e As EventArgs) Handles rdoPlusRateWholePrice.CheckedChanged
        GetRateDiscPriceAfter()
    End Sub

    Private Sub rdoDiscRateWholeWholePrice_CheckedChanged(sender As Object, e As EventArgs) Handles rdoDiscRateWholeWholePrice.CheckedChanged
        GetRateDiscPriceAfter()
    End Sub

    Private Sub rdoPlusRateWholeWholePrice_CheckedChanged(sender As Object, e As EventArgs) Handles rdoPlusRateWholeWholePrice.CheckedChanged
        GetRateDiscPriceAfter()
    End Sub

    Private Sub PictureBox1_Click(sender As Object, e As EventArgs) Handles PictureBox1.Click
        FrmSales_SerialNumber.Close()
        FrmSales_SerialNumber.Show()
    End Sub

    Private Sub btnDiscountedAfterPrice_Click(sender As Object, e As EventArgs) Handles btnDiscountedAfterPrice.Click
        PanelOnlineStore.Top = 300
        PanelOnlineStore.Left = 900
        PanelOnlineStore.Size = New System.Drawing.Size(480, 330)
    End Sub

    Private Sub btnCloseDiscountedAfterPrice_Click(sender As Object, e As EventArgs) Handles btnCloseDiscountedAfterPrice.Click
        PanelOnlineStore.Top = 5000
    End Sub

    Private Sub txtDiscountedPrice_TextChanged(sender As Object, e As EventArgs) Handles txtDiscountedPrice.TextChanged
        MyVars.CheckNumber(txtDiscountedPrice)
    End Sub

    Private Sub txtDiscountedPrice2_TextChanged(sender As Object, e As EventArgs) Handles txtDiscountedPrice2.TextChanged
        MyVars.CheckNumber(txtDiscountedPrice2)
    End Sub

    Private Sub txtDiscountedPrice3_TextChanged(sender As Object, e As EventArgs) Handles txtDiscountedPrice3.TextChanged
        MyVars.CheckNumber(txtDiscountedPrice3)
    End Sub

    Private Sub txtLimitQuantity_TextChanged(sender As Object, e As EventArgs) Handles txtLimitQuantity.TextChanged
        MyVars.CheckNumber(txtLimitQuantity)
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub

    Private Sub txtsearsh_TextChanged(sender As Object, e As EventArgs) Handles txtsearsh.TextChanged
        'Try
        If txtsearsh.Text = "'" Then : txtsearsh.Text = "" : End If
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If LanguageMainProgram = "العربية" Then
            If ActivateFormatNumberWithSeparators = "YES" Then
                If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
                    S = Cls.Get_Select_Grid_S("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],CAST(TinPriceAverage AS NVARCHAR(50)) as [متوسط سعر الشراء],CAST(SalPrice AS NVARCHAR(50)) as [سعر التجزئة],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة],CAST(MinimumSalPrice AS NVARCHAR(50)) as [حد ادنى للسعر],rng as [حد الطلب] ,CAST(store AS NVARCHAR(50)) as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id<>''")
                Else
                    S = Cls.Get_Select_Grid_S("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],CAST(TinPriceAverage AS NVARCHAR(50)) as [متوسط سعر الشراء],CAST(SalPrice AS NVARCHAR(50)) as [سعر التجزئة],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة],CAST(MinimumSalPrice AS NVARCHAR(50)) as [حد ادنى للسعر],rng as [حد الطلب] ,CAST(store AS NVARCHAR(50)) as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "sname Like N'%" & txtsearsh.Text & "%'")
                End If
            Else
                If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
                    S = Cls.Get_Select_Grid_S("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],WholeWholePrice as [سعر جملة الجملة],MinimumSalPrice as [حد ادنى للسعر],rng as [حد الطلب] ,store as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id<>''")
                Else
                    S = Cls.Get_Select_Grid_S("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],WholeWholePrice as [سعر جملة الجملة],MinimumSalPrice as [حد ادنى للسعر],rng as [حد الطلب] ,store as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "sname Like N'%" & txtsearsh.Text & "%'")
                End If
            End If
            If FilterSelect = "Number" Then
                S = S & " order by [الباركود]"
            End If
            If FilterSelect = "Date" Then
                S = S & " order by [المجموعة]"
            End If
            If FilterSelect = "Name" Then
                S = S & " order by [الصنف]"
            End If

        ElseIf LanguageMainProgram = "English" Then

            If ActivateFormatNumberWithSeparators = "YES" Then
                If txtsearsh.Text = "Search...." Or txtsearsh.Text = "" Then
                    S = Cls.Get_Select_Grid_S("id as [ID], itm_id as [Barcode], group_name as [Category], group_branch as [Subcategory], sname as [ItemName], CAST(TinPrice AS NVARCHAR(50)) as [PurchasePrice], CAST(TinPriceAverage AS NVARCHAR(50)) as [AvgPurchasePrice], CAST(SalPrice AS NVARCHAR(50)) as [RetailPrice], CAST(WholePrice AS NVARCHAR(50)) as [WholesalePrice], CAST(WholeWholePrice AS NVARCHAR(50)) as [BulkPrice], CAST(MinimumSalPrice AS NVARCHAR(50)) as [MinPriceLimit], rng as [ReorderLimit], CAST(store AS NVARCHAR(50)) as [StockQty], Stores as [Store], RateVAT as [VATRate]", "items", "id<>''")
                Else
                    S = Cls.Get_Select_Grid_S("id as [ID], itm_id as [Barcode], group_name as [Category], group_branch as [Subcategory], sname as [ItemName], CAST(TinPrice AS NVARCHAR(50)) as [PurchasePrice], CAST(TinPriceAverage AS NVARCHAR(50)) as [AvgPurchasePrice], CAST(SalPrice AS NVARCHAR(50)) as [RetailPrice], CAST(WholePrice AS NVARCHAR(50)) as [WholesalePrice], CAST(WholeWholePrice AS NVARCHAR(50)) as [BulkPrice], CAST(MinimumSalPrice AS NVARCHAR(50)) as [MinPriceLimit], rng as [ReorderLimit], CAST(store AS NVARCHAR(50)) as [StockQty], Stores as [Store], RateVAT as [VATRate]", "items", "sname Like N'%" & txtsearsh.Text & "%'")
                End If
            Else
                If txtsearsh.Text = "Search...." Or txtsearsh.Text = "" Then
                    S = Cls.Get_Select_Grid_S("id as [ID], itm_id as [Barcode], group_name as [Category], group_branch as [Subcategory], sname as [ItemName], TinPrice as [PurchasePrice], TinPriceAverage as [AvgPurchasePrice], SalPrice as [RetailPrice], WholePrice as [WholesalePrice], WholeWholePrice as [BulkPrice], MinimumSalPrice as [MinPriceLimit], rng as [ReorderLimit], store as [StockQty], Stores as [Store], RateVAT as [VATRate]", "items", "id<>''")
                Else
                    S = Cls.Get_Select_Grid_S("id as [ID], itm_id as [Barcode], group_name as [Category], group_branch as [Subcategory], sname as [ItemName], TinPrice as [PurchasePrice], TinPriceAverage as [AvgPurchasePrice], SalPrice as [RetailPrice], WholePrice as [WholesalePrice], WholeWholePrice as [BulkPrice], MinimumSalPrice as [MinPriceLimit], rng as [ReorderLimit], store as [StockQty], Stores as [Store], RateVAT as [VATRate]", "items", "sname Like N'%" & txtsearsh.Text & "%'")
                End If
            End If

            If FilterSelect = "Number" Then
                S = S & " order by [Barcode]"
            End If
            If FilterSelect = "Date" Then
                S = S & " order by [Category]"
            End If
            If FilterSelect = "Name" Then
                S = S & " order by [ItemName]"
            End If
        End If


        cmd.CommandText = S : dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)


        Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
        If ShowGroupBranch = "NO" Then
            DTGV.Columns(1).Visible = False
        Else
            DTGV.Columns(1).Visible = True
        End If

        Dim HideWholeWholeSalePrice As String = mykey.GetValue("HideWholeWholeSalePrice", "NO")
        If HideWholeWholeSalePrice = "NO" Then
            DTGV.Columns(8).Visible = True
            DTGV.Columns(9).Visible = True
        Else
            DTGV.Columns(8).Visible = False
            DTGV.Columns(9).Visible = False
        End If
        If ShowValueVAT = "NO" Then
            DTGV.Columns(14).Visible = False
        End If

        Dim SM As Double
        For i As Integer = 0 To DTGV.Rows.Count - 1
            If LastTinPriceItems = "YES" Then
                SM = SM + Val(DTGV.Rows(i).Cells(5).Value.ToString) * Val(DTGV.Rows(i).Cells(12).Value)
            Else
                SM = SM + Val(DTGV.Rows(i).Cells(6).Value.ToString) * Val(DTGV.Rows(i).Cells(12).Value)
            End If
        Next
        txt_Total.Text = FormatNumberWithSeparators(SM)

        Dim SM1 As Double
        For i As Integer = 0 To DTGV.Rows.Count - 1
            SM1 = SM1 + Val(DTGV.Rows(i).Cells(12).Value.ToString)
        Next
        txtTotalQunt.Text = SM1

        txtNumberItems.Text = DTGV.RowCount

        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub

    Private Sub GetFormatNumberWithSeparators()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            DTGV.Rows(i).Cells(5).Value = FormatNumberWithSeparators(Double.Parse(DTGV.Rows(i).Cells(5).Value))
            DTGV.Rows(i).Cells(6).Value = FormatNumberWithSeparators(Double.Parse(DTGV.Rows(i).Cells(6).Value))
            DTGV.Rows(i).Cells(7).Value = FormatNumberWithSeparators(Double.Parse(DTGV.Rows(i).Cells(7).Value))
            DTGV.Rows(i).Cells(8).Value = FormatNumberWithSeparators(Double.Parse(DTGV.Rows(i).Cells(8).Value))
            DTGV.Rows(i).Cells(9).Value = FormatNumberWithSeparators(Double.Parse(DTGV.Rows(i).Cells(9).Value))
            DTGV.Rows(i).Cells(10).Value = FormatNumberWithSeparators(Double.Parse(DTGV.Rows(i).Cells(11).Value))
            DTGV.Rows(i).Cells(12).Value = FormatNumberWithSeparators(Double.Parse(DTGV.Rows(i).Cells(12).Value))
        Next
    End Sub

    Private Sub TextBox1_TextChanged(sender As Object, e As EventArgs) Handles TextBox1.TextChanged
    End Sub

    Private Sub btnCloseHeightWidth_Click(sender As Object, e As EventArgs) Handles btnCloseHeightWidth.Click
        PanelHeightWidth.Top = 5000
    End Sub

    Private Sub txtHeight_KeyUp(sender As Object, e As KeyEventArgs) Handles txtHeight.KeyUp
        If e.KeyCode = 13 Then
            txtWidth.Focus()
            txtWidth.SelectAll()
        End If
    End Sub

    Private Sub txtWidth_KeyUp(sender As Object, e As KeyEventArgs) Handles txtWidth.KeyUp
        If e.KeyCode = 13 Then
            txtAltitude.Focus()
            txtAltitude.SelectAll()
        End If
    End Sub

    Private Sub txtAltitude_KeyUp(sender As Object, e As KeyEventArgs) Handles txtAltitude.KeyUp
        If e.KeyCode = 13 Then
            txtDensity.Focus()
            txtDensity.SelectAll()
        End If
    End Sub

    Private Sub btnNetWeightView_Click(sender As Object, e As EventArgs) Handles btnNetWeightView.Click
        PanelHeightWidth.Top = 200
    End Sub

    Private Sub SetEnglish()
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.RightToLeftLayout = True
        Panel1.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.Text = ENT_Language.PerformingPurchasingOperationsInEnglish
        Label26.Text = ENT_Language.PerformingPurchasingOperationsInEnglish
        Label4.Text = ENT_Language.ItemCategoryInEnglish
        Label16.Text = ENT_Language.ItemSubcategoryInEnglish
        Label10.Text = ENT_Language.ProductionDateInEnglish
        Label8.Text = ENT_Language.ExpiryTypeInEnglish
        chkPriceIncludesVAT.Text = ENT_Language.PriceIncludesVATInEnglish
        chkQuickSearch.Text = ENT_Language.ShowInSearchInEnglish
        chkAutomaticBarcodeNumber.Text = ENT_Language.AutoBarcodeInEnglish
        chkAddItemAllStores.Text = ENT_Language.AddItemToAllStoresInEnglish
        Label1.Text = ENT_Language.ItemNameInEnglish
        Label21.Text = ENT_Language.WarehouseInEnglish
        Label43.Text = ENT_Language.VendorInEnglish
        Label6.Text = ENT_Language.QuantityInEnglish
        Label42.Text = ENT_Language.VATInEnglish
        Label7.Text = ENT_Language.PurchasePriceInEnglish
        Label11.Text = ENT_Language.SellingPriceInEnglish
        lblWholePrice.Text = ENT_Language.WholesalePriceInEnglish
        lblWholeWholePrice.Text = ENT_Language.BulkPriceInEnglish
        lblrng.Text = ENT_Language.ReorderLimitInEnglish
        lblMinimumSalPrice.Text = ENT_Language.MinPriceLimitInEnglish
        lblPrc.Text = ENT_Language.BarcodeInEnglish
        Label15.Text = ENT_Language.BarcodeInEnglish
        chkViewPrint.Text = ENT_Language.PrintPreviewInEnglish
        chkPrintParcode.Text = ENT_Language.PrintBarcodeInEnglish
        btnsave.Text = ENT_Language.Added
        Label22.Text = ENT_Language.UnityInEnglish
        Label27.Text = ENT_Language.NumberInEnglish
        Label29.Text = ENT_Language.PurchasePriceInEnglish
        Label28.Text = ENT_Language.SellingPriceInEnglish
        Label34.Text = ENT_Language.UnitSizeInEnglish
        Label35.Text = ENT_Language.UnitBarcodeInEnglish
        LOGOBUTTON.Text = ENT_Language.ItemImageInEnglish
        Label36.Text = ENT_Language.BarcodeScaleInEnglish
        Label5.Text = ENT_Language.QuantityInEnglish
        LblTotal.Text = ENT_Language.InventoryInEnglish
        btnPaperType.Text = ENT_Language.PaperTypeInEnglish
    End Sub

    Private Sub SetArabic()
        Me.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.RightToLeftLayout = True

    End Sub

    Private Sub txtFindParcode_TextChanged(sender As Object, e As EventArgs) Handles txtFindParcode.TextChanged
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        'GetUP_ParcodeItemsUnity()

        GetBarcodeMore()

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If ActivateFormatNumberWithSeparators = "YES" Then
                If txtFindParcode.Text = "" Then
                    S = Cls.Get_Select_Grid_S("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],CAST(TinPriceAverage AS NVARCHAR(50)) as [متوسط سعر الشراء],CAST(SalPrice AS NVARCHAR(50)) as [سعر التجزئة],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة],CAST(MinimumSalPrice AS NVARCHAR(50)) as [حد ادنى للسعر],rng as [حد الطلب] ,CAST(store AS NVARCHAR(50)) as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id<>''")
                Else
                    S = Cls.Get_Select_Grid_S("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],CAST(TinPriceAverage AS NVARCHAR(50)) as [متوسط سعر الشراء],CAST(SalPrice AS NVARCHAR(50)) as [سعر التجزئة],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة],CAST(MinimumSalPrice AS NVARCHAR(50)) as [حد ادنى للسعر],rng as [حد الطلب] ,CAST(store AS NVARCHAR(50)) as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "itm_id =N'" & txtFindParcode.Text & "'")
                End If
            Else
                If txtFindParcode.Text = "" Then
                    S = Cls.Get_Select_Grid_S("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],WholeWholePrice as [سعر جملة الجملة],MinimumSalPrice as [حد ادنى للسعر],rng as [حد الطلب] ,store as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id<>''")
                Else
                    S = Cls.Get_Select_Grid_S("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],WholeWholePrice as [سعر جملة الجملة],MinimumSalPrice as [حد ادنى للسعر],rng as [حد الطلب] ,store as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "itm_id =N'" & txtFindParcode.Text & "'")
                End If
            End If

            If FilterSelect = "Number" Then
                S = S & " order by [الباركود]"
            End If
            If FilterSelect = "Date" Then
                S = S & " order by [المجموعة]"
            End If
            If FilterSelect = "Name" Then
                S = S & " order by [الصنف]"
            End If
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)

            'GetGridViewFormatNumberWithSeparators()

            Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
            If ShowGroupBranch = "NO" Then
                DTGV.Columns(2).Visible = False
            Else
                DTGV.Columns(2).Visible = True
            End If

            Dim HideWholeWholeSalePrice As String = mykey.GetValue("HideWholeWholeSalePrice", "NO")
            If HideWholeWholeSalePrice = "NO" Then
                DTGV.Columns(7).Visible = True
                DTGV.Columns(8).Visible = True
            Else
                DTGV.Columns(7).Visible = False
                DTGV.Columns(8).Visible = False
            End If
            If ShowValueVAT = "NO" Then
                DTGV.Columns(13).Visible = False
            End If

            Dim SM As Double
            For i As Integer = 0 To DTGV.Rows.Count - 1
                If LastTinPriceItems = "YES" Then
                    SM = SM + Val(DTGV.Rows(i).Cells(4).Value.ToString) * Val(DTGV.Rows(i).Cells(11).Value)
                Else
                    SM = SM + Val(DTGV.Rows(i).Cells(5).Value.ToString) * Val(DTGV.Rows(i).Cells(11).Value)
                End If
            Next
            txt_Total.Text = FormatNumberWithSeparators(SM)

            Dim SM1 As Double
            For i As Integer = 0 To DTGV.Rows.Count - 1
                SM1 = SM1 + Val(DTGV.Rows(i).Cells(11).Value.ToString)
            Next
            txtTotalQunt.Text = SM1

            txtNumberItems.Text = DTGV.RowCount
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbFindCats_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbFindCats.SelectedIndexChanged
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        GetBarcodeMore()

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If ActivateFormatNumberWithSeparators = "YES" Then
                If cmbFindCats.Text = "" Then
                    S = Cls.Get_Select_Grid_S("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],CAST(TinPriceAverage AS NVARCHAR(50)) as [متوسط سعر الشراء],CAST(SalPrice AS NVARCHAR(50)) as [سعر التجزئة],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة],CAST(MinimumSalPrice AS NVARCHAR(50)) as [حد ادنى للسعر],rng as [حد الطلب] ,CAST(store AS NVARCHAR(50)) as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id<>''")
                Else
                    S = Cls.Get_Select_Grid_S("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],CAST(TinPriceAverage AS NVARCHAR(50)) as [متوسط سعر الشراء],CAST(SalPrice AS NVARCHAR(50)) as [سعر التجزئة],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة],CAST(MinimumSalPrice AS NVARCHAR(50)) as [حد ادنى للسعر],rng as [حد الطلب] ,CAST(store AS NVARCHAR(50)) as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "group_name =N'" & cmbFindCats.Text & "'")
                End If
            Else
                If cmbFindCats.Text = "" Then
                    S = Cls.Get_Select_Grid_S("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],WholeWholePrice as [سعر جملة الجملة],MinimumSalPrice as [حد ادنى للسعر],rng as [حد الطلب] ,store as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id<>''")
                Else
                    S = Cls.Get_Select_Grid_S("id as [رقم],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],WholeWholePrice as [سعر جملة الجملة],MinimumSalPrice as [حد ادنى للسعر],rng as [حد الطلب] ,store as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "group_name =N'" & cmbFindCats.Text & "'")
                End If
            End If
            If FilterSelect = "Number" Then
                S = S & " order by [الباركود]"
            End If
            If FilterSelect = "Date" Then
                S = S & " order by [المجموعة]"
            End If
            If FilterSelect = "Name" Then
                S = S & " order by [الصنف]"
            End If
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)

            Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
            If ShowGroupBranch = "NO" Then
                DTGV.Columns(2).Visible = False
            Else
                DTGV.Columns(2).Visible = True
            End If

            Dim HideWholeWholeSalePrice As String = mykey.GetValue("HideWholeWholeSalePrice", "NO")
            If HideWholeWholeSalePrice = "NO" Then
                DTGV.Columns(7).Visible = True
                DTGV.Columns(8).Visible = True
            Else
                DTGV.Columns(7).Visible = False
                DTGV.Columns(8).Visible = False
            End If
            If ShowValueVAT = "NO" Then
                DTGV.Columns(13).Visible = False
            End If

            Dim SM As Double
            For i As Integer = 0 To DTGV.Rows.Count - 1
                If LastTinPriceItems = "YES" Then
                    SM = SM + Val(DTGV.Rows(i).Cells(4).Value.ToString) * Val(DTGV.Rows(i).Cells(11).Value)
                Else
                    SM = SM + Val(DTGV.Rows(i).Cells(5).Value.ToString) * Val(DTGV.Rows(i).Cells(11).Value)
                End If
            Next
            txt_Total.Text = FormatNumberWithSeparators(SM)

            Dim SM1 As Double
            For i As Integer = 0 To DTGV.Rows.Count - 1
                SM1 = SM1 + Val(DTGV.Rows(i).Cells(11).Value.ToString)
            Next
            txtTotalQunt.Text = SM1

            txtNumberItems.Text = DTGV.RowCount

            'GetGridViewFormatNumberWithSeparators()

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
End Class