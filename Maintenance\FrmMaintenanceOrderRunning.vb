﻿Imports CrystalDecisions.CrystalReports.Engine

Public Class FrmMaintenanceOrderRunning

    Dim AddItemsID As String
    Dim Dt_AddBill As New DataTable
    Dim Dt_AddBill_ReceivingCar As New DataTable
    Dim RNXD As Integer
    Dim WithEvents BS As New BindingSource
    Dim ActiveX As Boolean
    Dim billno As String = ""
    Dim AutobillnoDay As String = ""
    Dim AddressCust, TelCust, MobileCust, RegionCust, MarkCust As String

    Private Sub FrmMaintenanceOrderRunning_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        SelectTypeDeviceCar()
        Cls.fill_combo("Customers", "Vendorname", cmbCustomer)
        Bra.Fil("MaintenanceTypeProduct", "TypeProduct_Name", cmbTypeProduct)
        Bra.Fil("MaintenanceType", "Maintenance_Name", cmbMaintenanceType)
        Cls.fill_combo_Branch("Maintenance_Drivers", "Driv_Name", cmbDrivers)
        Cls.fill_combo_Branch("Maintenance_Supervisor", "Supervisor", cmbSupervisor)
        Cls.fill_combo_Branch("Maintenance_Recipient", "Recipient", cmbRecipient)
        Cls.fill_combo_Branch("Maintenance_Car_Data", "Car_Number", cmbCarNumber)
        Bra.Fil("MaintenanceDeviceBrand", "DeviceBrand_Name", cmbDeviceBrand)
        Bra.Fil("MaintenanceDeviceModel", "DeviceModel_Name", cmbDeviceModel)
        Cls.fill_combo("Maintenance_Manu_Year", "Manu_Year", cmbManu_Year)
        Cls.fill_combo("Maintenance_Car_Data", "CC", cmbCC)
        Cls.fill_combo("Maintenance_Car_Data", "Cylinders_No", cmbCylinders_No)
        Cls.fill_combo("Maintenance_Car_Data", "Valves_No", cmbValves_No)
        Cls.fill_combo("Maintenance_ReceivingCar", "ReceivingCar", cmbReceivingCar)

        MAXRECORD("MaintenanceOrderRunning", "OrderID")
        txtOrderID.Text = AddItemsID
        txtKiloMeter.Focus()
        dtpTime.Top = 5000
    End Sub

    Private Sub SelectTypeDeviceCar()

        If SelectTypeMaintenanceDeviceCar = "NULL" Then
            MsgBox("يجب الذهاب لشاشة الاعدادات لضبط تحديد بيئة العمل داخل الصيانة", MsgBoxStyle.Exclamation)
            Me.Close()
        End If
        If SelectTypeMaintenanceDeviceCar = "CarMaintenance" Then
            lblTypeProduct.Text = "نوع المركبة"
            lblOrderID.Text = "رقم الاذن"
            lblCarNumber.Text = "رقم السيارة"
            'lblVisit_Date.Visible = False
            'dtpVisit_Date.Visible = False
            'PanelCarMaintenance.Visible = True
            'PanelDevicesMaintenance.Visible = False
            'PanelCarMaintenance.Location = New System.Drawing.Point(505, 129)
            cmbCustomer.Location = New System.Drawing.Point(552, 94)
            cmbCustomer.Size = New System.Drawing.Size(364, 29)
            lblTypeProduct.Location = New System.Drawing.Point(419, 68)
            cmbTypeProduct.Location = New System.Drawing.Point(381, 94)
        End If
        If SelectTypeMaintenanceDeviceCar = "DevicesMaintenance" Then
            lblTypeProduct.Text = "نوع الجهاز"
            lblOrderID.Text = "رقم البلاغ"
            lblCarNumber.Text = "رقم الجهاز"
            cmbManu_Year.Visible = False
            cmbManu_Year.Visible = False
            'lblVisit_Date.Visible = True
            'dtpVisit_Date.Visible = True
            'PanelDevicesMaintenance.Visible = True
            'PanelCarMaintenance.Visible = False
            'PanelDevicesMaintenance.Location = New System.Drawing.Point(505, 129)
        End If
    End Sub

    Private Sub MAXRECORD(ByVal Table As String, ByVal Felds As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " + Table + ""
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            AddItemsID = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" + Felds + " As float)) as mb FROM " + Table + " where " + Felds + " <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            AddItemsID = sh + 1
        End If

    End Sub

    Private Sub txtOrderID_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtOrderID.TextChanged
        MyVars.CheckNumber(txtOrderID)
    End Sub

    Private Sub txtOrderID_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtOrderID.KeyUp
        If e.KeyCode = 13 Then
            cmbCustomer.Focus()
        End If
    End Sub

    Private Sub cmbCustomer_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCustomer.KeyUp
        If e.KeyCode = 13 Then
            cmbTypeProduct.Focus()
        End If
    End Sub

    Private Sub txtKiloMeter_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtKiloMeter.KeyUp
        If e.KeyCode = 13 Then
            cmbSupervisor.Focus()
        End If
    End Sub

    Private Sub cmbTypeProduct_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbTypeProduct.KeyUp
        If e.KeyCode = 13 Then
            dtpDate.Focus()
        End If
    End Sub

    Private Sub cmbMaintenanceType_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbMaintenanceType.DropDown
        cmbRequiredRepair.Text = ""
    End Sub

    Private Sub cmbMaintenanceType_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbMaintenanceType.KeyUp
        If e.KeyCode = 13 Then
            cmbRequiredRepair.Focus()
        End If
    End Sub

    Private Sub btnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAdd.Click
        If ValidateTextAdd() = False Then Exit Sub

        Dim MaintenanceType_ID As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select MaintenanceType_ID from MaintenanceType where Maintenance_Name=N'" & cmbMaintenanceType.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then

            MAXRECORD("MaintenanceType", "MaintenanceType_ID")

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MaintenanceType(MaintenanceType_ID,Maintenance_Name) values ("
            S = S & "N'" & AddItemsID & "',N'" & cmbMaintenanceType.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select MaintenanceType_ID from MaintenanceType where Maintenance_Name=N'" & cmbMaintenanceType.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
            Else
                If dr(0) Is DBNull.Value Then
                Else
                    MaintenanceType_ID = dr("MaintenanceType_ID")
                End If
            End If
        Else
            If dr(0) Is DBNull.Value Then
            Else
                MaintenanceType_ID = dr("MaintenanceType_ID")
            End If
        End If


        Dim SlNo As Long
        If Me.Dgv_Add.Rows.Count = 0 Then
            SlNo = 1
        Else
            SlNo = Me.Dgv_Add.Rows.Count + 1
        End If


        Dgv_Add.DataSource = Fn_AddBill(SlNo, MaintenanceType_ID, cmbMaintenanceType.Text, cmbRequiredRepair.Text)

        Dgv_Add.Columns(0).Width = 65
        Dgv_Add.Columns(2).Width = 160
        Dgv_Add.Columns(3).Width = 690

        Dgv_Add.Columns(1).Visible = False

        cmbRequiredRepair.Text = ""
        cmbMaintenanceType.Text = ""
        cmbMaintenanceType.Focus()
    End Sub

    Friend Function Fn_AddBill(ByVal Col_Siral As String, ByVal Col_MaintenanceType_ID As String, ByVal Col_MaintenanceType As String, ByVal Col_RequiredRepair As String) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("مسلسل", GetType(String))
            Dt_AddBill.Columns.Add("كود", GetType(String))
            Dt_AddBill.Columns.Add("نوع الصيانة", GetType(String))
            Dt_AddBill.Columns.Add("المطلوب اصلاحة", GetType(String))
        End If

        Dt_AddBill.Rows.Add(Col_Siral, Col_MaintenanceType_ID, Col_MaintenanceType, Col_RequiredRepair)

        Return Dt_AddBill

    End Function

    Friend Function Fn_AddBill_ReceivingCar(ByVal Col_Siral As String, ByVal Col_ReceivingCar_ID As String, ByVal Col_ReceivingCar As String) As DataTable
        If Dt_AddBill_ReceivingCar.Columns.Count = 0 Then
            Dt_AddBill_ReceivingCar.Columns.Add("مسلسل", GetType(String))
            Dt_AddBill_ReceivingCar.Columns.Add("كود", GetType(String))
            Dt_AddBill_ReceivingCar.Columns.Add("نوع الصيانة", GetType(String))
        End If

        Dt_AddBill_ReceivingCar.Rows.Add(Col_Siral, Col_ReceivingCar_ID, Col_ReceivingCar)

        Return Dt_AddBill_ReceivingCar

    End Function

    Function ValidateTextAdd() As Boolean
        Dim NumberDevices As String = ""
        'If SelectTypeMaintenanceDeviceCar = "DevicesMaintenance" Then
        '    NumberDevices = "فضلا ادخل رقم البلاغ"
        'Else
        NumberDevices = "فضلا ادخل رقم الاذن"
        'End If
        If txtOrderID.Text = "" Then MsgBox("'" & NumberDevices & "'", MsgBoxStyle.Exclamation) : txtOrderID.Focus() : Return False

        If SelectTypeMaintenanceDeviceCar = "CarMaintenance" Then
            If cmbCarNumber.Text = "" Then MsgBox("من فضلك أدخل رقم السيارة", MsgBoxStyle.Exclamation) : cmbCarNumber.Focus() : Return False
            If cmbTypeProduct.Text = "" Then MsgBox("فضلا أدخل نوع المركبة", MsgBoxStyle.Exclamation) : cmbTypeProduct.Focus() : Return False
        End If
        If cmbCustomer.Text = "" Then MsgBox("فضلا أدخل  أسم العميل", MsgBoxStyle.Exclamation) : cmbCustomer.Focus() : Return False
        If cmbMaintenanceType.Text = "" Then MsgBox("فضلا أختر نوع الصيانة", MsgBoxStyle.Exclamation) : cmbMaintenanceType.Focus() : Return False
        If cmbRequiredRepair.Text = "" Then MsgBox("فضلا أدخل المطلوب اصلاحة", MsgBoxStyle.Exclamation) : cmbRequiredRepair.Focus() : Return False

        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select count(*) from MaintenanceOrderRunning where OrderID =N'" & txtOrderID.Text.Trim & "'" : H = cmd.ExecuteScalar
        'If H > 0 Then
        '    MsgBox("رقم الاذن مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtOrderID.Focus() : Return False
        'End If


        Return True
    End Function

    Function ValidateTextAdd_ReceivingCar() As Boolean

        If cmbReceivingCar.Text = "" Then MsgBox("فضلا أدخل أستلام السيارة", MsgBoxStyle.Exclamation) : cmbReceivingCar.Focus() : Return False

        Return True
    End Function


    Private Sub btnSettingPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSettingPrint.Click
        FrmSettingPrinterDefault.ShowDialog()
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        btnSave.Enabled = False
        MAXRECORD("MaintenanceOrderRunning", "OrderID")
        txtOrderID.Text = AddItemsID

        If ValidateTextSave() = False Then Exit Sub

        Dim HTime As String = TxtHour.Text
        Dim split As String() = New String() {":"}
        Dim itemsSplit As String() = HTime.Split(split, StringSplitOptions.None)
        Dim Hours As String = itemsSplit(0).ToString()
        Dim Menits As String = itemsSplit(1).ToString()
        Dim XTime As String = Hours.Trim() & ":" & Menits.Trim()

        Dim Customers_ID As String = Cls.Get_Code_Value("Customers", "Cust_Code", "Vendorname", cmbCustomer.Text)
        Dim TypeProduct_ID As String = Cls.Get_Code_Value("MaintenanceTypeProduct", "TypeProduct_ID", "TypeProduct_Name", cmbTypeProduct.Text)
        Dim DeviceBrand_ID As String = Cls.Get_Code_Value("MaintenanceDeviceBrand", "DeviceBrand_ID", "DeviceBrand_Name", cmbDeviceBrand.Text)
        Dim DeviceModel_ID As String = Cls.Get_Code_Value("MaintenanceDeviceModel", "DeviceModel_ID", "DeviceModel_Name", cmbDeviceModel.Text)
        Dim Driv_ID As String = Cls.Get_Code_Value("Maintenance_Drivers", "Driv_ID", "Driv_Name", cmbDrivers.Text)
        Dim Car_Data_ID As String = Cls.Get_Code_Value("Maintenance_Car_Data", "Car_Data_ID", "Car_Number", cmbCarNumber.Text)
        Dim Supervisor_ID As String = Cls.Get_Code_Value("Maintenance_Supervisor", "Supervisor_ID", "Supervisor", cmbSupervisor.Text)
        Dim Recipient_ID As String = Cls.Get_Code_Value("Maintenance_Recipient", "Recipient_ID", "Recipient", cmbRecipient.Text)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into MaintenanceOrderRunning(OrderID,Car_Data_ID,CarNumber, Customer_ID,Driv_ID,OrderDate,OrderTime,TimeAMPM, KiloMeter, TypeProduct_ID,Supervisor_ID,Recipient_ID,MaintenanceStatus_ID,UserName,DeviceModel_ID,DeviceBrand_ID) values ("
        S = S & "N'" & txtOrderID.Text.Trim & "',N'" & Car_Data_ID & "',N'" & cmbCarNumber.Text & "' ,N'" & Customers_ID & "',N'" & Driv_ID & "',N'" & Cls.C_date(dtpDate.Text) & "',N'" & XTime & "',N'" & dtpTime.Text & "',N'" & txtKiloMeter.Text & "',N'" & TypeProduct_ID & "',N'" & Supervisor_ID & "',N'" & Recipient_ID & "',1,N'" & UserName & "',N'" & DeviceModel_ID & "',N'" & DeviceBrand_ID & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            AddItemsID = MaxRecordTables("MaintenanceOrderRunningAdd", "OrderAdd_ID")

            Dim MaintenanceType_ID As String = Cls.Get_Code_Value("MaintenanceType", "MaintenanceType_ID", "Maintenance_Name", Dgv_Add.Rows(i).Cells(2).Value)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MaintenanceOrderRunningAdd (OrderAdd_ID,OrderID,MaintenanceType_ID,RequiredRepair)  values("
            S = S & "N'" & AddItemsID & "',N'" & txtOrderID.Text & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(3).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        For i As Integer = 0 To DGVReceivingCar.Rows.Count - 1
            AddItemsID = MaxRecordTables("Maintenance_ReceivingCarAdd", "ReceivingCarAddID")

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Maintenance_ReceivingCarAdd (ReceivingCarAddID,OrderID,ReceivingCar_ID,UserName,Company_Branch_ID)  values("
            S = S & "N'" & AddItemsID & "',N'" & txtOrderID.Text & "',N'" & DGVReceivingCar.Rows(i).Cells(1).Value & "',N'" & UserName & "',N'" & Company_Branch_ID & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        If chkprint.Checked = True Then
            PrintReport()
        End If

        ClearText()
        CarNumberTrue()

        Dt_AddBill.Rows.Clear()
        Dt_AddBill_ReceivingCar.Rows.Clear()
        Cls.fill_combo("Customers", "Vendorname", cmbCustomer)
        Bra.Fil("MaintenanceTypeProduct", "TypeProduct_Name", cmbTypeProduct)
        Bra.Fil("MaintenanceType", "Maintenance_Name", cmbMaintenanceType)
        Cls.fill_combo_Branch("Maintenance_Drivers", "Driv_Name", cmbDrivers)

        MAXRECORD("MaintenanceOrderRunning", "OrderID")
        txtOrderID.Text = AddItemsID
        btnSave.Enabled = True
    End Sub

    Function ValidateTextSave() As Boolean
        If txtOrderID.Text = "" Then MsgBox("فضلا ادخل رقم الاذن", MsgBoxStyle.Exclamation) : txtOrderID.Focus() : Return False
        If cmbCarNumber.Text = "" Then MsgBox("من فضلك أدخل رقم السيارة", MsgBoxStyle.Exclamation) : cmbCarNumber.Focus() : Return False
        If cmbTypeProduct.Text = "" Then MsgBox("فضلا أدخل نوع المركبة", MsgBoxStyle.Exclamation) : cmbTypeProduct.Focus() : Return False
        If cmbCustomer.Text = "" Then MsgBox("فضلا أدخل  أسم العميل", MsgBoxStyle.Exclamation) : cmbCustomer.Focus() : Return False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from MaintenanceOrderRunning where OrderID =N'" & txtOrderID.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H > 0 Then
            MsgBox("رقم الاذن مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtOrderID.Focus() : Return False
            btnSave.Enabled = True
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from Maintenance_Car_Data where Car_Number =N'" & cmbCarNumber.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H < 0 Then
            MsgBox("رقم السيارة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtOrderID.Focus() : Return False
            btnSave.Enabled = True
        End If

        Return True
    End Function

    Private Sub PrintReport()

        Dim txtNameAr, txtNameEN, txtNameArDown, txtVisit_Date, txtDeviceModel, txtDeviceBrand, txtTypeProduct, txtWebsite, txtMobile, txtTel, txtTelCust, txtMobileCust, txtRegion, txtMark, txtaddressCust, txtpercent As TextObject

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        For i As Integer = 0 To Dgv_Add.Rows.Count - 1

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases(Company_Branch_ID,BILL_NO, Vendorname, bill_date, billtime, KiloMeter,Driv_CarNumber, Supervisor_Reform, Recipient,Received_Date,Details) values ("
            S = S & "N'" & Company_Branch_ID & "',N'" & txtOrderID.Text.Trim & "',N'" & cmbCustomer.Text & "',N'" & Cls.C_date(dtpDate.Text) & "',N'" & dtpTime.Text.Trim & "' ,N'" & txtKiloMeter.Text & "' ,N'" & cmbCarNumber.Text & "',N'" & cmbSupervisor.Text & "' ,N'" & cmbRecipient.Text & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Dgv_Add.Rows(i).Cells(3).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

        Next
        AddReportView()
        GetDataCustomer()

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)

        Dim rpt
        'Dim rpt2
        If rdoLarge.Checked = True Then
            'If SelectTypeMaintenanceDeviceCar = "DevicesMaintenance" Then
            '    'rpt = New Rpt_MaintenanceOrderRunningDevices
            '    'rpt2 = New Rpt_MaintenanceOrderRunningDevicesReceipt
            'Else
            rpt = New Rpt_MaintenanceOrderRunning
            'End If
        End If
        If rdoSmall.Checked = True Then
            rpt = New Rpt_MaintenanceOrderRunningSmall
        End If

        rpt.SetDataSource(dt)
        'rpt2.SetDataSource(dt)

        txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
        txtNameAr.Text = NameArCompay
        'txtNameArDown = rpt.Section1.ReportObjects("txtTitelArDown")
        'txtNameArDown.Text = CMPUnderBILL
        txtNameEN = rpt.Section1.ReportObjects("txtTitelEn")
        txtNameEN.Text = NameEnCompany

        If SelectTypeMaintenanceDeviceCar = "DevicesMaintenance" Then
            txtRegion = rpt.Section1.ReportObjects("txtRegion")
            txtRegion.Text = RegionCust
            txtTelCust = rpt.Section1.ReportObjects("txtTelCust")
            txtTelCust.Text = TelCust
            txtMobileCust = rpt.Section1.ReportObjects("txtMobileCust")
            txtMobileCust.Text = MobileCust
            txtaddressCust = rpt.Section1.ReportObjects("txtaddressCust")
            txtaddressCust.Text = AddressCust
            txtMark = rpt.Section1.ReportObjects("txtMark")
            txtMark.Text = MarkCust
            txtDeviceModel = rpt.Section1.ReportObjects("txtDeviceModel")
            txtDeviceModel.Text = cmbDeviceModel.Text
            txtDeviceBrand = rpt.Section1.ReportObjects("txtDeviceBrand")
            txtDeviceBrand.Text = cmbDeviceBrand.Text
            txtTypeProduct = rpt.Section1.ReportObjects("txtTypeProduct")
            txtTypeProduct.Text = cmbTypeProduct.Text
            txtWebsite = rpt.Section1.ReportObjects("txtWebsite")
            txtWebsite.Text = CMPWebsite
            'txtMobile = rpt.Section1.ReportObjects("txtMobile")
            'txtMobile.Text = CmpMobile
            txtTel = rpt.Section1.ReportObjects("txtTel")
            txtTel.Text = CmpTel
            txtpercent = rpt.Section1.ReportObjects("txtpercent")
            txtpercent.Text = ""
            '====================================================================
            'txtRegion = rpt2.Section1.ReportObjects("txtRegion")
            'txtRegion.Text = RegionCust
            'txtTelCust = rpt2.Section1.ReportObjects("txtTelCust")
            'txtTelCust.Text = TelCust
            'txtMobileCust = rpt2.Section1.ReportObjects("txtMobileCust")
            'txtMobileCust.Text = MobileCust
            'txtaddressCust = rpt2.Section1.ReportObjects("txtaddressCust")
            'txtaddressCust.Text = AddressCust
            'txtMark = rpt2.Section1.ReportObjects("txtMark")
            'txtMark.Text = MarkCust
            'txtVisit_Date = rpt2.Section1.ReportObjects("txtVisit_Date")
            'txtVisit_Date.Text = Cls.C_date(dtpVisit_Date.Text)
            'txtDeviceModel = rpt2.Section1.ReportObjects("txtDeviceModel")
            'txtDeviceModel.Text = cmbDeviceModel.Text
            'txtDeviceBrand = rpt2.Section1.ReportObjects("txtDeviceBrand")
            'txtDeviceBrand.Text = cmbDeviceBrand.Text
            'txtTypeProduct = rpt2.Section1.ReportObjects("txtTypeProduct")
            'txtTypeProduct.Text = cmbTypeProduct.Text
            'txtWebsite = rpt2.Section1.ReportObjects("txtWebsite")
            'txtWebsite.Text = CMPWebsite
            ''txtMobile = rpt2.Section1.ReportObjects("txtMobile")
            ''txtMobile.Text = CmpMobile
            'txtTel = rpt2.Section1.ReportObjects("txtTel")
            'txtTel.Text = CmpTel
            'txtpercent = rpt2.Section1.ReportObjects("txtpercent")
            'txtpercent.Text = ""
        End If
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        If rdoPrintDirect.Checked = True Then
            rpt.PrintToPrinter(1, False, 0, 0)
            'rpt2.PrintToPrinter(1, False, 0, 0)
        End If
        If rdoPrintPreview.Checked = True Then
            Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
            Frm_PrintReports.Text = "أوامر تشغيل الصيانة"
            Frm_PrintReports.Show()
        End If

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub GetDataCustomer()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Customers where Vendorname=N'" & cmbCustomer.Text & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            AddressCust = dr("addr").ToString
            TelCust = dr("tel1").ToString
            MobileCust = dr("Mobile").ToString
            RegionCust = dr("Region").ToString
            MarkCust = dr("Mark").ToString
        End If
    End Sub

    Private Sub cmbSupervisor_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbSupervisor.KeyUp
        If e.KeyCode = 13 Then
            cmbRecipient.Focus()
        End If
    End Sub

    Private Sub cmbRecipient_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbRecipient.KeyUp
        If e.KeyCode = 13 Then
            cmbMaintenanceType.Focus()
        End If
    End Sub

    Private Sub cmbCarNumber_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbCarNumber.KeyUp
        If e.KeyCode = 13 Then
            GetCarNumberData()
            txtKiloMeter.Focus()
        End If
    End Sub

    Private Sub cmbDeviceBrand_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbDeviceBrand.KeyUp
        If e.KeyCode = 13 Then
            cmbDeviceModel.Focus()
        End If
    End Sub

    Private Sub cmbDeviceModel_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbDeviceModel.KeyUp
        If e.KeyCode = 13 Then
            cmbSupervisor.Focus()
        End If
    End Sub

    Private Sub BtnClear_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnClear.Click
        cmbCarNumber.Text = ""
        txtKiloMeter.Text = ""
        txtOrderID.Text = ""
        cmbRecipient.Text = ""
        cmbSupervisor.Text = ""
        cmbRequiredRepair.Text = ""
        cmbMaintenanceType.Text = ""
        cmbCustomer.Text = ""
        cmbTypeProduct.Text = ""
        txtChassis_No.Text = ""
        txtDescription.Text = ""
        cmbCylinders_No.Text = ""
        cmbManu_Year.Text = ""
        cmbValves_No.Text = ""
        cmbCC.Text = ""
        cmbReceivingCar.Text = ""
        Dt_AddBill.Rows.Clear()
        Dt_AddBill_ReceivingCar.Rows.Clear()

        MAXRECORD("MaintenanceOrderRunning", "OrderID")
        txtOrderID.Text = AddItemsID
    End Sub

    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        RNXD = Dgv_Add.CurrentRow.Index
        Dgv_Add.Rows.RemoveAt(RNXD)
    End Sub

    Private Sub Timer1_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Timer1.Tick
        On Error Resume Next
        TxtHour.Text = Cls.get_time(True)
    End Sub

    Private Sub cmbDrivers_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbDrivers.KeyUp
        If e.KeyCode = 13 Then
            cmbSupervisor.Focus()
        End If
    End Sub

    Private Sub cmbCustomer_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbCustomer.SelectedIndexChanged
        Try
            If ActiveX = False Then
                Cls.fill_combo_Stores_Where("View_MaintenanceOrderRunning", "CarNumber", "Vendorname", cmbCustomer.Text, cmbCarNumber)
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbCarNumber_TextChanged(sender As Object, e As EventArgs) Handles cmbCarNumber.TextChanged
        If cmbCarNumber.Text = "" Then
            Cls.fill_combo_Branch("Maintenance_Car_Data", "Car_Number", cmbCarNumber)
        End If
    End Sub

    Private Sub dtpDelivery_Date_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            'If SelectTypeMaintenanceDeviceCar = "CarMaintenance" Then
            txtKiloMeter.Focus()
            'End If
            'If SelectTypeMaintenanceDeviceCar = "DevicesMaintenance" Then
            '    cmbDeviceBrand.Focus()
            'End If
        End If
    End Sub

    Private Sub cmbCarNumber_DropDown(sender As Object, e As EventArgs) Handles cmbCarNumber.DropDown
        txtKiloMeter.Text = ""
        cmbRecipient.Text = ""
        cmbSupervisor.Text = ""
        cmbRequiredRepair.Text = ""
        cmbMaintenanceType.Text = ""
        cmbCustomer.Text = ""
        cmbTypeProduct.Text = ""
        cmbDrivers.Text = ""
        cmbDeviceModel.Text = ""
        cmbDeviceBrand.Text = ""
        txtDescription.Text = ""
        txtChassis_No.Text = ""
    End Sub

    Private Sub cmbMaintenanceType_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbMaintenanceType.SelectedIndexChanged
        If cmbMaintenanceType.Text.Trim = "" Then Exit Sub
        cmbRequiredRepair.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT distinct   dbo.MaintenanceType.Maintenance_Name, dbo.MaintenanceOrderRunningAdd.RequiredRepair FROM         dbo.MaintenanceOrderRunningAdd INNER JOIN   dbo.MaintenanceType ON dbo.MaintenanceOrderRunningAdd.MaintenanceType_ID = dbo.MaintenanceType.MaintenanceType_ID WHERE     (dbo.MaintenanceType.Maintenance_Name = N'" & cmbMaintenanceType.Text.Trim & "')"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbRequiredRepair.Items.Add(Trim(dr("RequiredRepair")))
        Loop
    End Sub

    Private Sub btnAddReceivingCar_Click(sender As Object, e As EventArgs) Handles btnAddReceivingCar.Click
        If ValidateTextAdd_ReceivingCar() = False Then Exit Sub

        Dim ReceivingCar_ID As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select ReceivingCar_ID from Maintenance_ReceivingCar where ReceivingCar=N'" & cmbReceivingCar.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            MAXRECORD("Maintenance_ReceivingCar", "ReceivingCar_ID")

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MaintenanceType(ReceivingCar_ID,ReceivingCar) values ("
            S = S & "N'" & AddItemsID & "',N'" & cmbReceivingCar.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ReceivingCar_ID from Maintenance_ReceivingCar where ReceivingCar=N'" & cmbReceivingCar.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                ReceivingCar_ID = dr("ReceivingCar_ID")
            End If
        Else
            If dr(0) Is DBNull.Value Then
            Else
                ReceivingCar_ID = dr("ReceivingCar_ID")
            End If
        End If

        Dim SlNo As Long
        If Me.DGVReceivingCar.Rows.Count = 0 Then
            SlNo = 1
        Else
            SlNo = Me.DGVReceivingCar.Rows.Count + 1
        End If


        DGVReceivingCar.DataSource = Fn_AddBill_ReceivingCar(SlNo, ReceivingCar_ID, cmbReceivingCar.Text)

        DGVReceivingCar.Columns(0).Width = 60
        'DGVReceivingCar.Columns(2).Width = 60

        DGVReceivingCar.Columns(1).Visible = False

        cmbReceivingCar.Text = ""
        cmbReceivingCar.Focus()
    End Sub

    Private Sub btnDeleteReceivingCar_Click(sender As Object, e As EventArgs) Handles btnDeleteReceivingCar.Click
        If DGVReceivingCar.RowCount = 0 Then Beep() : Exit Sub
        If (DGVReceivingCar.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        RNXD = DGVReceivingCar.CurrentRow.Index
        DGVReceivingCar.Rows.RemoveAt(RNXD)
    End Sub

    Private Sub GetCarNumberData()
        ActiveX = True
        Cls.Select_More_Data_Stores("View_Maintenance_Car_Data", "Vendorname,Driv_Name,TypeProduct_Name,DeviceBrand_Name,DeviceModel_Name,Manu_Year,CC,Cylinders_No,Valves_No,Chassis_No,Description,KiloMeter", "Car_Number=N'" & cmbCarNumber.Text & "'")
        If dr.HasRows = True Then
            cmbCustomer.Text = dr("Vendorname").ToString
            cmbDrivers.Text = dr("Driv_Name").ToString
            cmbTypeProduct.Text = dr("TypeProduct_Name").ToString
            cmbDeviceBrand.Text = dr("DeviceBrand_Name").ToString
            cmbDeviceModel.Text = dr("DeviceModel_Name").ToString
            cmbManu_Year.Text = dr("Manu_Year").ToString
            cmbCC.Text = dr("CC").ToString
            cmbCylinders_No.Text = dr("Cylinders_No").ToString
            cmbValves_No.Text = dr("Valves_No").ToString
            txtChassis_No.Text = dr("Chassis_No").ToString
            txtDescription.Text = dr("Description").ToString
            txtKiloMeter.Text = dr("KiloMeter").ToString
        End If
        ActiveX = False
    End Sub

    Private Sub GetDataImport()
        'Try
        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "select itm_id,TinPrice,SalPrice,WholePrice,MinimumSalPrice,group_name,RatePriceOffers,rng from items where sname=N'" & cmbname.Text.Trim & "' and Stores =N'" & cmbStores.Text.Trim & "'"
        '    dr = cmd.ExecuteReader : dr.Read()
        '    If dr.HasRows = True Then
        '        txtprc.Text = dr(0).ToString
        '        txtprice.Text = Val(dr(1).ToString)
        '        txt_priseSal.Text = Val(dr(2).ToString)
        '        txtWholePrice.Text = Val(dr(3).ToString)
        '        txtMinimumSalPrice.Text = Val(dr(4).ToString)
        '        cmbcats.Text = dr(5).ToString
        '        txtRatePriceSale.Text = Val(dr(6).ToString)
        '        txtrng.Text = Val(dr(7).ToString)
        '    End If
        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub

    Private Sub CarNumberTrue()
        cmbTypeProduct.Enabled = True
        cmbDeviceBrand.Enabled = True
        cmbDeviceModel.Enabled = True
        cmbManu_Year.Enabled = True
        cmbCC.Enabled = True
        cmbCylinders_No.Enabled = True
        cmbValves_No.Enabled = True
        txtChassis_No.Enabled = True
    End Sub

    Private Sub ClearText()
        cmbCarNumber.Text = ""
        txtKiloMeter.Text = ""
        txtOrderID.Text = ""
        cmbRecipient.Text = ""
        cmbSupervisor.Text = ""
        cmbRequiredRepair.Text = ""
        cmbMaintenanceType.Text = ""
        cmbCustomer.Text = ""
        cmbTypeProduct.Text = ""
        cmbDrivers.Text = ""
        cmbDeviceModel.Text = ""
        cmbDeviceBrand.Text = ""
    End Sub
End Class