﻿Imports System.Text
Imports System.Security.Cryptography
Imports VB = Microsoft.VisualBasic
Imports System.IO
Imports System.Data.SqlClient

Public Class Online_Store
    Dim aray_1, aray_2 As New ArrayList
    Dim UserId As String = ""
    Dim Quantity As String = ""
    Dim SlidesPriceId As String = ""
    Dim slidesPriceNo As String = ""
    Dim ProductId As String = ""
    Dim TotalPrice As Double
    Dim TotalPriceOrder As Double
    Dim TotalValueDiscountOrder As Double
    Dim TotalDiscountedPrice As Double

    Friend Function UpdateProductPrice(ByVal Price As String, ByVal Price2 As String, ByVal Price3 As String, ByVal DiscountedPrice As String, ByVal DiscountedPrice2 As String, ByVal DiscountedPrice3 As String, ByVal Code As String, ByVal Name As String)
        If Not ConnectingOnlineStore() Is Nothing Then
            If ConnectOnlineStore = "YES" Then

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "select count(*) from Product where  Code =N'" & Code & "'"
                cmd.CommandText = S : H = cmd.ExecuteScalar
                If H <> 0 Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Product set Price =N'" & Val(Price) & "',Price2 =N'" & Val(Price2) & "',Price3 =N'" & Val(Price3) & "',DiscountedPrice =N'" & Val(DiscountedPrice) & "',DiscountedPrice2 =N'" & Val(DiscountedPrice2) & "',DiscountedPrice3 =N'" & Val(DiscountedPrice3) & "' where Code =N'" & Code & "'" : cmd.ExecuteNonQuery()
                End If

                '======================== Start Update Cart Set Price ================================

                ProductId = Cls.Get_Code_Value_More("Product", "Id", "Name =N'" & Name & "'")

                aray_1.Clear() : aray_2.Clear()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select UserId,Quantity from Cart where ProductId=N'" & ProductId & "'"
                dr = cmd.ExecuteReader
                Do While dr.Read = True
                    aray_1.Add(dr(0))
                    aray_2.Add(dr(1))
                Loop

                For i As Integer = 0 To aray_1.Count - 1
                    UserId = aray_1(i).ToString
                    Quantity = aray_2(i).ToString
                    SlidesPriceId = Cls.Get_Code_Value_More("[User]", "SlidesPriceId", "Id =N'" & UserId & "' and IsDelete = 0")
                    slidesPriceNo = Cls.Get_Code_Value_More("[SlidesPrice]", "Id", "SlidesPriceNo =N'" & SlidesPriceId & "' and IsSlidesPrice = 1 and IsDelete = 0")

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select * from Product where Id =N'" & ProductId & "' and IsDelete = 0" : dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then
                        If slidesPriceNo = 0 Then
                            Price = dr("Price")
                            DiscountedPrice = dr("DiscountedPrice")
                        End If
                        If slidesPriceNo = 1 Then
                            Price = dr("Price")
                            DiscountedPrice = dr("DiscountedPrice")
                        End If
                        If slidesPriceNo = 2 Then
                            Price = dr("Price2")
                            DiscountedPrice = dr("DiscountedPrice2")
                        End If
                        If slidesPriceNo = 3 Then
                            Price = dr("Price3")
                            DiscountedPrice = dr("DiscountedPrice3")
                        End If
                        TotalPrice = Val(Quantity) * Val(Price)
                        TotalPriceOrder = Val(Quantity) * Val(Price)
                        TotalDiscountedPrice = Val(Price) - Val(DiscountedPrice)
                        If TotalDiscountedPrice <> 0 Then
                            TotalPriceOrder = Val(Quantity) * Val(DiscountedPrice)
                            TotalValueDiscountOrder = TotalDiscountedPrice * Quantity
                        Else
                            TotalPrice = Quantity * Price
                            TotalValueDiscountOrder = 0
                        End If
                    End If

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Cart set Price =N'" & Val(Price) & "',DiscountedPrice =N'" & Val(DiscountedPrice) & "',TotalPrice =N'" & Val(TotalPrice) & "',TotalPriceOrder =N'" & Val(TotalPriceOrder) & "',TotalValueDiscountOrder =N'" & Val(TotalValueDiscountOrder) & "' where ProductId =N'" & ProductId & "' and  UserId =N'" & UserId & "'" : cmd.ExecuteNonQuery()
                Next

                '======================== End Update Cart Set Price ================================

            End If
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Function

    Friend Function UpdateProductStock(ByVal Stock As String, ByVal Code As String, ByVal Name As String)
        If Not ConnectingOnlineStore() Is Nothing Then
            If ConnectOnlineStore = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "select count(*) from Product where  Code =N'" & Code & "'"
                cmd.CommandText = S : H = cmd.ExecuteScalar
                If H <> 0 Then
                    'Dim XStock As Integer = Stock

                    Dim split As String() = New String() {"."}
                    Dim itemsSplit As String() = Stock.Split(split, StringSplitOptions.None)
                    Dim Number1 As String = itemsSplit(0).ToString()
                    Dim Number2 As String = ""
                    If Number2 <> Nothing Then
                        Number2 = itemsSplit(1).ToString()
                    End If
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Product set Stock =N'" & Val(Number1) & "' where Code =N'" & Code & "'" : cmd.ExecuteNonQuery()
                End If
                Cn.Close()
                connect()
            Else
                Cn.Close()
                connect()
                MsgBox(Cls_Constant.ErrMsg)
            End If
        End If
    End Function


    Friend Function UpdateProductPrice2(ByVal Price As String, ByVal Price2 As String, ByVal Price3 As String, ByVal DiscountedPrice As String, ByVal DiscountedPrice2 As String, ByVal DiscountedPrice3 As String, ByVal Code As String, ByVal Name As String)
        If ConnectOnlineStore = "YES" Then

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "select count(*) from Product where  Code =N'" & Code & "'"
            cmd.CommandText = S : H = cmd.ExecuteScalar
            If H <> 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Product set Price =N'" & Val(Price) & "',Price2 =N'" & Val(Price2) & "',Price3 =N'" & Val(Price3) & "',DiscountedPrice =N'" & Val(DiscountedPrice) & "',DiscountedPrice2 =N'" & Val(DiscountedPrice2) & "',DiscountedPrice3 =N'" & Val(DiscountedPrice3) & "' where Code =N'" & Code & "'" : cmd.ExecuteNonQuery()

                '======================== Start Update Cart Set Price ================================

                ProductId = Cls.Get_Code_Value_More("Product", "Id", "Name =N'" & Name & "'")

                aray_1.Clear() : aray_2.Clear()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select UserId,Quantity from Cart where ProductId=N'" & ProductId & "'"
                dr = cmd.ExecuteReader
                Do While dr.Read = True
                    aray_1.Add(dr(0))
                    aray_2.Add(dr(1))
                Loop

                For i As Integer = 0 To aray_1.Count - 1
                    UserId = aray_1(i).ToString
                    Quantity = aray_2(i).ToString
                    SlidesPriceId = Cls.Get_Code_Value_More("[User]", "SlidesPriceId", "Id =N'" & UserId & "' and IsDelete = 0")
                    slidesPriceNo = Cls.Get_Code_Value_More("[SlidesPrice]", "Id", "SlidesPriceNo =N'" & SlidesPriceId & "' and IsSlidesPrice = 1 and IsDelete = 0")

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select * from Product where Id =N'" & ProductId & "' and IsDelete = 0" : dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then
                        If slidesPriceNo = 0 Then
                            Price = dr("Price")
                            DiscountedPrice = dr("DiscountedPrice")
                        End If
                        If slidesPriceNo = 1 Then
                            Price = dr("Price")
                            DiscountedPrice = dr("DiscountedPrice")
                        End If
                        If slidesPriceNo = 2 Then
                            Price = dr("Price2")
                            DiscountedPrice = dr("DiscountedPrice2")
                        End If
                        If slidesPriceNo = 3 Then
                            Price = dr("Price3")
                            DiscountedPrice = dr("DiscountedPrice3")
                        End If
                        TotalPrice = Val(Quantity) * Val(Price)
                        TotalPriceOrder = Val(Quantity) * Val(Price)
                        TotalDiscountedPrice = Val(Price) - Val(DiscountedPrice)
                        If TotalDiscountedPrice <> 0 Then
                            TotalPriceOrder = Val(Quantity) * Val(DiscountedPrice)
                            TotalValueDiscountOrder = TotalDiscountedPrice * Quantity
                        Else
                            TotalPrice = Quantity * Price
                            TotalValueDiscountOrder = 0
                        End If
                    End If

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Cart set Price =N'" & Val(Price) & "',DiscountedPrice =N'" & Val(DiscountedPrice) & "',TotalPrice =N'" & Val(TotalPrice) & "',TotalPriceOrder =N'" & Val(TotalPriceOrder) & "',TotalValueDiscountOrder =N'" & Val(TotalValueDiscountOrder) & "' where ProductId =N'" & ProductId & "' and  UserId =N'" & UserId & "'" : cmd.ExecuteNonQuery()
                Next

                '======================== End Update Cart Set Price ================================

            End If
        Else
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Function

    Friend Function UpdateProductStock2(ByVal Stock As String, ByVal Code As String, ByVal Name As String)
        If ConnectOnlineStore = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "select count(*) from Product where  Code =N'" & Code & "'"
            cmd.CommandText = S : H = cmd.ExecuteScalar
            If H <> 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Product set Stock =N'" & Val(Stock) & "' where Code =N'" & Code & "'" : cmd.ExecuteNonQuery()
            Else
                MsgBox(Cls_Constant.ErrMsg)
            End If
        End If
    End Function

    Friend Function UpdateProductPriceStock(ByVal Stock As String, ByVal Price As String, ByVal Price2 As String, ByVal Price3 As String, ByVal DiscountedPrice As String, ByVal DiscountedPrice2 As String, ByVal DiscountedPrice3 As String, ByVal Code As String, ByVal Name As String)
        If Not ConnectingOnlineStore() Is Nothing Then
            If ConnectOnlineStore = "YES" Then

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "select count(*) from Product where  Code =N'" & Code & "'"
                cmd.CommandText = S : H = cmd.ExecuteScalar
                If H <> 0 Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Product set Stock =N'" & Val(Stock) & "',Price =N'" & Val(Price) & "',Price2 =N'" & Val(Price2) & "',Price3 =N'" & Val(Price3) & "',DiscountedPrice =N'" & Val(DiscountedPrice) & "',DiscountedPrice2 =N'" & Val(DiscountedPrice2) & "',DiscountedPrice3 =N'" & Val(DiscountedPrice3) & "' where Code =N'" & Code & "'" : cmd.ExecuteNonQuery()

                    '======================== Start Update Cart Set Price ================================

                    ProductId = Cls.Get_Code_Value_More("Product", "Id", "Name =N'" & Name & "'")

                    aray_1.Clear() : aray_2.Clear()
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "Select UserId,Quantity from Cart where ProductId=N'" & ProductId & "'"
                    dr = cmd.ExecuteReader
                    Do While dr.Read = True
                        aray_1.Add(dr(0))
                        aray_2.Add(dr(1))
                    Loop

                    For i As Integer = 0 To aray_1.Count - 1
                        UserId = aray_1(i).ToString
                        Quantity = aray_2(i).ToString
                        SlidesPriceId = Cls.Get_Code_Value_More("[User]", "SlidesPriceId", "Id =N'" & UserId & "' and IsDelete = 0")
                        slidesPriceNo = Cls.Get_Code_Value_More("[SlidesPrice]", "Id", "SlidesPriceNo =N'" & SlidesPriceId & "' and IsSlidesPrice = 1 and IsDelete = 0")

                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "select * from Product where Id =N'" & ProductId & "' and IsDelete = 0" : dr = cmd.ExecuteReader : dr.Read()
                        If dr.HasRows = True Then
                            If slidesPriceNo = 0 Then
                                Price = dr("Price")
                                DiscountedPrice = dr("DiscountedPrice")
                            End If
                            If slidesPriceNo = 1 Then
                                Price = dr("Price")
                                DiscountedPrice = dr("DiscountedPrice")
                            End If
                            If slidesPriceNo = 2 Then
                                Price = dr("Price2")
                                DiscountedPrice = dr("DiscountedPrice2")
                            End If
                            If slidesPriceNo = 3 Then
                                Price = dr("Price3")
                                DiscountedPrice = dr("DiscountedPrice3")
                            End If
                            TotalPrice = Val(Quantity) * Val(Price)
                            TotalPriceOrder = Val(Quantity) * Val(Price)
                            TotalDiscountedPrice = Val(Price) - Val(DiscountedPrice)
                            If TotalDiscountedPrice <> 0 Then
                                TotalPriceOrder = Val(Quantity) * Val(DiscountedPrice)
                                TotalValueDiscountOrder = TotalDiscountedPrice * Quantity
                            Else
                                TotalPrice = Quantity * Price
                                TotalValueDiscountOrder = 0
                            End If
                        End If

                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update Cart set Price =N'" & Val(Price) & "',DiscountedPrice =N'" & Val(DiscountedPrice) & "',TotalPrice =N'" & Val(TotalPrice) & "',TotalPriceOrder =N'" & Val(TotalPriceOrder) & "',TotalValueDiscountOrder =N'" & Val(TotalValueDiscountOrder) & "' where ProductId =N'" & ProductId & "' and  UserId =N'" & UserId & "'" : cmd.ExecuteNonQuery()
                    Next

                    '======================== End Update Cart Set Price ================================

                End If
            Else
                MsgBox(Cls_Constant.ErrMsg)
            End If
        End If
    End Function

    Friend Function UpdateProductPriceStock2(ByVal Stock As Integer, ByVal Price As String, ByVal Price2 As String, ByVal Price3 As String, ByVal DiscountedPrice As String, ByVal DiscountedPrice2 As String, ByVal DiscountedPrice3 As String, ByVal Code As String, ByVal Name As String)
        If ConnectOnlineStore = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "select count(*) from Product where  Code =N'" & Code & "'"
            cmd.CommandText = S : H = cmd.ExecuteScalar
            If H <> 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Product set Stock =N'" & Val(Stock) & "',Price =N'" & Val(Price) & "',Price2 =N'" & Val(Price2) & "',Price3 =N'" & Val(Price3) & "',DiscountedPrice =N'" & Val(DiscountedPrice) & "',DiscountedPrice2 =N'" & Val(DiscountedPrice2) & "',DiscountedPrice3 =N'" & Val(DiscountedPrice3) & "' where Code =N'" & Code & "'" : cmd.ExecuteNonQuery()

                '======================== Start Update Cart Set Price ================================

                ProductId = Cls.Get_Code_Value_More("Product", "Id", "Name =N'" & Name & "'")

                aray_1.Clear() : aray_2.Clear()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select UserId,Quantity from Cart where ProductId=N'" & ProductId & "'"
                dr = cmd.ExecuteReader
                Do While dr.Read = True
                    aray_1.Add(dr(0))
                    aray_2.Add(dr(1))
                Loop

                For i As Integer = 0 To aray_1.Count - 1
                    UserId = aray_1(i).ToString
                    Quantity = aray_2(i).ToString
                    SlidesPriceId = Cls.Get_Code_Value_More("[User]", "SlidesPriceId", "Id =N'" & UserId & "' and IsDelete = 0")
                    slidesPriceNo = Cls.Get_Code_Value_More("[SlidesPrice]", "Id", "SlidesPriceNo =N'" & SlidesPriceId & "' and IsSlidesPrice = 1 and IsDelete = 0")

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select * from Product where Id =N'" & ProductId & "' and IsDelete = 0" : dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then
                        If slidesPriceNo = 0 Then
                            Price = dr("Price")
                            DiscountedPrice = dr("DiscountedPrice")
                        End If
                        If slidesPriceNo = 1 Then
                            Price = dr("Price")
                            DiscountedPrice = dr("DiscountedPrice")
                        End If
                        If slidesPriceNo = 2 Then
                            Price = dr("Price2")
                            DiscountedPrice = dr("DiscountedPrice2")
                        End If
                        If slidesPriceNo = 3 Then
                            Price = dr("Price3")
                            DiscountedPrice = dr("DiscountedPrice3")
                        End If
                        TotalPrice = Val(Quantity) * Val(Price)
                        TotalPriceOrder = Val(Quantity) * Val(Price)
                        TotalDiscountedPrice = Val(Price) - Val(DiscountedPrice)
                        If TotalDiscountedPrice <> 0 Then
                            TotalPriceOrder = Val(Quantity) * Val(DiscountedPrice)
                            TotalValueDiscountOrder = TotalDiscountedPrice * Quantity
                        Else
                            TotalPrice = Quantity * Price
                            TotalValueDiscountOrder = 0
                        End If
                    End If

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Cart set Price =N'" & Val(Price) & "',DiscountedPrice =N'" & Val(DiscountedPrice) & "',TotalPrice =N'" & Val(TotalPrice) & "',TotalPriceOrder =N'" & Val(TotalPriceOrder) & "',TotalValueDiscountOrder =N'" & Val(TotalValueDiscountOrder) & "' where ProductId =N'" & ProductId & "' and  UserId =N'" & UserId & "'" : cmd.ExecuteNonQuery()
                Next

                '======================== End Update Cart Set Price ================================

            End If
        Else
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Function

    Friend Function AddProduct(ByVal Name As String, ByVal Code As String, ByVal Tag As String, ByVal Description As String, ByVal Category As String, ByVal Company As String, ByVal Price As String, ByVal DiscountedPrice As String, ByVal Price2 As String, ByVal DiscountedPrice2 As String, ByVal Price3 As String, ByVal DiscountedPrice3 As String, ByVal Stock As String, ByVal LimitQuantity As String)
        If Not ConnectingOnlineStore() Is Nothing Then
            If ConnectOnlineStore = "YES" Then

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "select count(*) from Product where  Code =N'" & Code & "'"
                cmd.CommandText = S : H = cmd.ExecuteScalar
                If H = 0 Then

                    Dim CategoryId As String = Cls.Get_Code_Value_More("Category", "Id", "Name =N'" & Category & "'")
                    Dim CompanyId As String = Cls.Get_Code_Value_More("Company", "Id", "Name =N'" & Company & "'")
                    If CategoryId = 0 Then : CategoryId = 1 : End If
                    If CompanyId = 0 Then : CompanyId = 1 : End If

                    ' استعلام آمن لإضافة المنتج
                    Try
                        Dim insertQuery As String = "INSERT INTO Product (Name, Code, Tag, Description, CategoryId, CompanyId, Price, DiscountedPrice, Price2, DiscountedPrice2, Price3, DiscountedPrice3, Stock, LimitQuantity) VALUES (@name, @code, @tag, @description, @categoryId, @companyId, @price, @discountedPrice, @price2, @discountedPrice2, @price3, @discountedPrice3, @stock, @limitQuantity)"

                        Dim insertParams As New Dictionary(Of String, Object) From {
                            {"@name", Name},
                            {"@code", Code},
                            {"@tag", Tag},
                            {"@description", Description},
                            {"@categoryId", CategoryId},
                            {"@companyId", CompanyId},
                            {"@price", Val(Price)},
                            {"@discountedPrice", Val(DiscountedPrice)},
                            {"@price2", Val(Price2)},
                            {"@discountedPrice2", Val(DiscountedPrice2)},
                            {"@price3", Val(Price3)},
                            {"@discountedPrice3", Val(DiscountedPrice3)},
                            {"@stock", Val(Stock)},
                            {"@limitQuantity", Val(LimitQuantity)}
                        }

                        SecureDatabaseManager.Instance.ExecuteNonQuery(insertQuery, insertParams)

                    Catch ex As Exception
                        ErrorHandler.LogError("خطأ في إضافة منتج للمتجر الإلكتروني", ex, $"ProductName: {Name}")
                        Throw
                    End Try
                End If

                Cn.Close()
                connect()
            Else
                MsgBox(Cls_Constant.ErrMsg)
            End If
        End If

    End Function

    Friend Function UpdateProductAll(ByVal Tag As String, ByVal Description As String, ByVal Category As String, ByVal Company As String, ByVal LimitQuantity As String, ByVal Price As String, ByVal Price2 As String, ByVal Price3 As String, ByVal DiscountedPrice As String, ByVal DiscountedPrice2 As String, ByVal DiscountedPrice3 As String, ByVal Code As String, ByVal Name As String, ByVal NameBefor As String)
        If Not ConnectingOnlineStore() Is Nothing Then
            If ConnectOnlineStore = "YES" Then

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "select count(*) from Product where  Code =N'" & Code & "'"
                cmd.CommandText = S : H = cmd.ExecuteScalar
                If H <> 0 Then
                    Dim CategoryId As String = Cls.Get_Code_Value_More("Category", "Id", "Name =N'" & Category & "'")
                    Dim CompanyId As String = Cls.Get_Code_Value_More("Company", "Id", "Name =N'" & Company & "'")
                    Dim CartProductId As String = Cls.Get_Code_Value_More("Cart", "ProductId", "ProductName =N'" & NameBefor & "'")
                    ProductId = Cls.Get_Code_Value_More("OrderItem", "ProductId", "ProductName =N'" & NameBefor & "'")

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Product set Name =N'" & Name & "',Tag =N'" & Tag & "',Description =N'" & Description & "',LimitQuantity =N'" & LimitQuantity & "',Price =N'" & Val(Price) & "',Price2 =N'" & Val(Price2) & "',Price3 =N'" & Val(Price3) & "',DiscountedPrice =N'" & Val(DiscountedPrice) & "',DiscountedPrice2 =N'" & Val(DiscountedPrice2) & "',DiscountedPrice3 =N'" & Val(DiscountedPrice3) & "' where Code =N'" & Code & "'" : cmd.ExecuteNonQuery()

                    If CategoryId <> 0 Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update Product set CategoryId =N'" & CategoryId & "' where Code =N'" & Code & "'" : cmd.ExecuteNonQuery()
                    End If

                    If CompanyId <> 0 Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update Product set CompanyId =N'" & CompanyId & "' where Code =N'" & Code & "'" : cmd.ExecuteNonQuery()
                    End If

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update OrderItem set ProductName =N'" & Name & "' where ProductId =N'" & ProductId & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Cart set ProductName =N'" & Name & "' where ProductId =N'" & ProductId & "'" : cmd.ExecuteNonQuery()

                    '======================== Start Update Cart Set Price ================================

                    ProductId = Cls.Get_Code_Value_More("Product", "Id", "Name =N'" & Name & "'")

                    aray_1.Clear() : aray_2.Clear()
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "Select UserId,Quantity from Cart where ProductId=N'" & ProductId & "'"
                    dr = cmd.ExecuteReader
                    Do While dr.Read = True
                        aray_1.Add(dr(0))
                        aray_2.Add(dr(1))
                    Loop

                    For i As Integer = 0 To aray_1.Count - 1
                        UserId = aray_1(i).ToString
                        Quantity = aray_2(i).ToString
                        SlidesPriceId = Cls.Get_Code_Value_More("[User]", "SlidesPriceId", "Id =N'" & UserId & "' and IsDelete = 0")
                        slidesPriceNo = Cls.Get_Code_Value_More("[SlidesPrice]", "Id", "SlidesPriceNo =N'" & SlidesPriceId & "' and IsSlidesPrice = 1 and IsDelete = 0")

                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "select * from Product where Id =N'" & ProductId & "' and IsDelete = 0" : dr = cmd.ExecuteReader : dr.Read()
                        If dr.HasRows = True Then
                            If slidesPriceNo = 0 Then
                                Price = dr("Price")
                                DiscountedPrice = dr("DiscountedPrice")
                            End If
                            If slidesPriceNo = 1 Then
                                Price = dr("Price")
                                DiscountedPrice = dr("DiscountedPrice")
                            End If
                            If slidesPriceNo = 2 Then
                                Price = dr("Price2")
                                DiscountedPrice = dr("DiscountedPrice2")
                            End If
                            If slidesPriceNo = 3 Then
                                Price = dr("Price3")
                                DiscountedPrice = dr("DiscountedPrice3")
                            End If
                            TotalPrice = Val(Quantity) * Val(Price)
                            TotalPriceOrder = Val(Quantity) * Val(Price)
                            If DiscountedPrice = 0 Or DiscountedPrice = 0.00 Then
                                TotalDiscountedPrice = 0
                            Else
                                TotalDiscountedPrice = Val(Price) - Val(DiscountedPrice)
                            End If
                            If TotalDiscountedPrice <> 0 Then
                                TotalPriceOrder = Val(Quantity) * Val(DiscountedPrice)
                                TotalValueDiscountOrder = TotalDiscountedPrice * Quantity
                            Else
                                TotalPrice = Quantity * Price
                                TotalValueDiscountOrder = 0
                            End If
                        End If

                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update Cart set Price =N'" & Val(Price) & "',DiscountedPrice =N'" & Val(DiscountedPrice) & "',TotalPrice =N'" & Val(TotalPrice) & "',TotalPriceOrder =N'" & Val(TotalPriceOrder) & "',TotalValueDiscountOrder =N'" & Val(TotalValueDiscountOrder) & "',TotalBeforeDiscountOrder =N'" & Val(TotalPrice) & "' where ProductId =N'" & ProductId & "' and  UserId =N'" & UserId & "'" : cmd.ExecuteNonQuery()
                    Next

                    '======================== End Update Cart Set Price ================================

                End If
            End If
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Function

    Friend Function GetProductValue(ByVal Table_name As String, ByVal Field_Code As String, ByVal Field_Name As String, ByVal Value_toChek As String)
        Try
            If Not ConnectingOnlineStore() Is Nothing Then
                If ConnectOnlineStore = "YES" Then
                    Dim Code As String = "0"
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where " & Field_Name & " =N'" & Value_toChek & "'"
                    dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then
                        Code = dr(0).ToString
                    End If
                    Cn.Close()
                    connect()
                    Return Code
                Else
                    Cn.Close()
                    connect()
                    MsgBox(Cls_Constant.ErrMsg)
                End If
            End If
        Catch ex As Exception
        End Try
    End Function

    Friend Function GetProduct(ByVal Stock As String, ByVal Code As String, ByVal Name As String)
        If Not ConnectingOnlineStore() Is Nothing Then
            If ConnectOnlineStore = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "select count(*) from Product where  Code =N'" & Code & "'"
                cmd.CommandText = S : H = cmd.ExecuteScalar
                If H <> 0 Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Product set Stock =N'" & Val(Stock) & "' where Code =N'" & Code & "'" : cmd.ExecuteNonQuery()
                End If
                Cn.Close()
                connect()
            Else
                Cn.Close()
                connect()
                MsgBox(Cls_Constant.ErrMsg)
            End If
        End If
    End Function

    Friend Function DeleteProduct(ByVal Code As String)
        If Not ConnectingOnlineStore() Is Nothing Then
            If ConnectOnlineStore = "YES" Then

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete  from Product where Code =N'" & Code & "'" : cmd.ExecuteNonQuery()
            End If
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Function

    Friend Function AddCategory(ByVal Name As String, ByVal IsHide As String)
        If Not ConnectingOnlineStore() Is Nothing Then
            If ConnectOnlineStore = "YES" Then

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "select count(*) from Category where  Name =N'" & Name & "'"
                cmd.CommandText = S : H = cmd.ExecuteScalar
                If H = 0 Then

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    S = "insert into Category (Name,IsDelete,IsHide)"
                    S = S & " values (N'" & Name & "',N'False',N'" & IsHide & "')"
                    cmd.CommandText = S : cmd.ExecuteNonQuery()
                End If

                Cn.Close()
                connect()
            Else
                MsgBox(Cls_Constant.ErrMsg)
            End If
        End If

    End Function

    Friend Function UpdateCategory(ByVal Name As String, ByVal IsHide As String)
        If Not ConnectingOnlineStore() Is Nothing Then
            If ConnectOnlineStore = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Category set Name =N'" & Name & "',IsHide =N'" & IsHide & "' where Name =N'" & Name & "'" : cmd.ExecuteNonQuery()

            End If
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Function

    Friend Function DeleteCategory(ByVal Name As String)
        If Not ConnectingOnlineStore() Is Nothing Then
            If ConnectOnlineStore = "YES" Then

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete  from Groups where Name =N'" & Name & "'" : cmd.ExecuteNonQuery()
            End If
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Function

    Friend Function UpdateTabelOnlineStore(ByVal Tabel As String, ByVal Field As String, ByVal Text As String, ByVal ID As String)
        If Not ConnectingOnlineStore() Is Nothing Then
            If ConnectOnlineStore = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "select count(*) from " & Tabel & " where  Id =N'" & ID & "'"
                cmd.CommandText = S : H = cmd.ExecuteScalar
                If H <> 0 Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update " & Tabel & " set " & Field & " =N'" & Text & "' where Id =N'" & ID & "'" : cmd.ExecuteNonQuery()
                End If
                Cn.Close()
                connect()
            Else
                Cn.Close()
                connect()
                MsgBox(Cls_Constant.ErrMsg)
            End If
        End If
    End Function

    Friend Function DeleteTabelOnlineStore(ByVal Tabel As String, ByVal Field As String, ByVal Text As String)
        If Not ConnectingOnlineStore() Is Nothing Then
            If ConnectOnlineStore = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete  from " & Tabel & " where " & Field & " =N'" & Text & "'" : cmd.ExecuteNonQuery()
            End If
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Function

    Function GetCodeValueOnlineStore(ByVal Table_name As String, ByVal Field_Code As String, ByVal Field_Name As String, ByVal Value_toChek As String)
        Try
            If Not ConnectingOnlineStore() Is Nothing Then
                If ConnectOnlineStore = "YES" Then
                    Dim Code As String = "0"
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where " & Field_Name & " =N'" & Value_toChek & "'"
                    dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then
                        Code = dr(0).ToString
                    End If
                    Return Code
                End If
                Cn.Close()
                connect()
            Else
                Cn.Close()
                connect()
                MsgBox(Cls_Constant.ErrMsg)
            End If
        Catch ex As Exception
        End Try
    End Function

    Sub Fill_ComboBox_OnlineStore(ByVal Table_Name As String, ByVal Field_Name As String, ByVal combo As ComboBox)
        Try
            If Not ConnectingOnlineStore() Is Nothing Then
                If ConnectOnlineStore = "YES" Then
                    combo.Items.Clear()
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " order by 1"
                    dr = cmd.ExecuteReader
                    Do While dr.Read = True
                        combo.Items.Add(Trim(dr(0)))
                    Loop
                End If
                Cn.Close()
                connect()
            Else
                Cn.Close()
                connect()
                MsgBox(Cls_Constant.ErrMsg)
            End If
        Catch ex As Exception
            Cn.Close()
            connect()
        End Try
    End Sub

    Public Sub GetStockOnlineStore_To_StockOffline()
        Dim aray_Code As New ArrayList
        Dim aray_ProductName As New ArrayList
        Dim aray_Quantity As New ArrayList
        Dim aray_Stock As New ArrayList

        Dim StockOnline As String = ""
        Dim StockOffline As String = ""
        Dim CodeOnline As String = ""
        Dim Parcode As String = ""
        Dim group_name As String = ""
        Dim sname As String = ""
        Dim Quantity As String = ""
        Dim Store As String = ""

        If Not ConnectingOnlineStore() Is Nothing Then
            If ConnectOnlineStore = "YES" Then

                aray_Code.Clear() : aray_ProductName.Clear() : aray_Quantity.Clear() : aray_Stock.Clear()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select dbo.Product.Code, dbo.OrderItem.ProductName, dbo.OrderItem.Quantity, dbo.Product.Stock, dbo.OrderItem.OrderStatusId  From dbo.OrderItem INNER Join dbo.Product On dbo.OrderItem.ProductId = dbo.Product.Id  Where (dbo.OrderItem.OrderStatusId = 1)"
                dr = cmd.ExecuteReader
                Do While dr.Read = True
                    aray_Code.Add(dr(0))
                    aray_ProductName.Add(dr(1))
                    aray_Quantity.Add(dr(2))
                    aray_Stock.Add(dr(3))
                Loop

                Cn.Close()
                connect()
            Else
                Cn.Close()
                connect()
                MsgBox(Cls_Constant.ErrMsg)
            End If
        End If


        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If


        For i As Integer = 0 To aray_Code.Count - 1
            Parcode = aray_Code(i).ToString
            sname = aray_ProductName(i).ToString
            Quantity = aray_Quantity(i).ToString
            StockOnline = aray_Stock(i).ToString


            Store = Cls.Get_Code_Value_More("Stores", "store", "StausMainStore =N'0'")
            StockOffline = IM.Get_Itm_Store(Parcode, Store)

            If StockOnline <> StockOffline Then

                group_name = Cls.Get_Code_Value_More("Items", "group_name", "itm_id =N'" & Parcode & "' and Stores =N'" & Store & "'")
                sname = Cls.Get_Code_Value_More("Items", "sname", "itm_id =N'" & Parcode & "' and Stores =N'" & Store & "'")

                Dim xid As String = ""
                Dim TotalEqual As String
                If Convert.ToDouble(Quantity) >= Convert.ToDouble(StockOffline) Then
                    TotalEqual = StockOnline - StockOffline

                    Try
                        ' إدراج آمن لبيانات التسوية
                        Dim insertQuery As String = "INSERT INTO BilltINData(Company_Branch_ID, bill_no, itm_id, itm_cat, itm_name, qu, qu_unity, Stores, username, bill_date) VALUES (@companyBranchId, @billNo, @itemId, @itemCat, @itemName, @quantity, @quantityUnity, @stores, @username, @billDate)"

                        Dim insertParams As New Dictionary(Of String, Object) From {
                            {"@companyBranchId", Company_Branch_ID},
                            {"@billNo", "تسوية"},
                            {"@itemId", Parcode},
                            {"@itemCat", group_name},
                            {"@itemName", sname},
                            {"@quantity", TotalEqual},
                            {"@quantityUnity", TotalEqual},
                            {"@stores", Store},
                            {"@username", UserName},
                            {"@billDate", Cls.C_date(DateTime.Today.ToString("yyyy-MM-dd"))}
                        }

                        SecureDatabaseManager.Instance.ExecuteNonQuery(insertQuery, insertParams)

                        IM.Store(Parcode, Store)

                        ' الحصول على ID بشكل آمن
                        Dim getIdQuery As String = "SELECT TOP 1 id FROM BilltINData WHERE itm_id = @itemId AND bill_no = @billNo AND Stores = @stores ORDER BY id DESC"
                        Dim getIdParams As New Dictionary(Of String, Object) From {
                            {"@itemId", Parcode},
                            {"@billNo", "تسوية"},
                            {"@stores", Store}
                        }

                        Dim idResult As Object = SecureDatabaseManager.Instance.ExecuteScalar(getIdQuery, getIdParams)
                        If idResult IsNot Nothing Then
                            xid = idResult.ToString()

                            ' تحديث آمن للمخزون
                            Dim updateQuery As String = "UPDATE BilltINData SET CurrentStock = @currentStock, CurrentStockTotal = @currentStockTotal WHERE id = @id AND itm_id = @itemId AND Stores = @stores"
                            Dim updateParams As New Dictionary(Of String, Object) From {
                                {"@currentStock", Val(CurrentStock)},
                                {"@currentStockTotal", Val(CurrentStockTotal)},
                                {"@id", xid},
                                {"@itemId", Parcode},
                                {"@stores", Store}
                            }

                            SecureDatabaseManager.Instance.ExecuteNonQuery(updateQuery, updateParams)
                        End If

                    Catch ex As Exception
                        ErrorHandler.LogError("خطأ في تسوية المخزون", ex, $"ItemCode: {Parcode}")
                        Throw
                    End Try
                End If
                If Convert.ToDouble(Quantity) < Convert.ToDouble(StockOffline) Then
                    TotalEqual = StockOffline - StockOnline

                    Try
                        ' إدراج آمن لبيانات المبيعات
                        Dim insertSalesQuery As String = "INSERT INTO BillsalData(Company_Branch_ID, bill_no, itm_id, itm_cat, itm_name, qu, qu_unity, Stores, username, bill_date, Vendorname, Stat) VALUES (@companyBranchId, @billNo, @itemId, @itemCat, @itemName, @quantity, @quantityUnity, @stores, @username, @billDate, @vendorName, @stat)"

                        Dim insertSalesParams As New Dictionary(Of String, Object) From {
                            {"@companyBranchId", Company_Branch_ID},
                            {"@billNo", "تسوية"},
                            {"@itemId", Parcode},
                            {"@itemCat", group_name},
                            {"@itemName", sname},
                            {"@quantity", TotalEqual},
                            {"@quantityUnity", TotalEqual},
                            {"@stores", Store},
                            {"@username", UserName},
                            {"@billDate", Cls.C_date(DateTime.Today.ToString("yyyy-MM-dd"))},
                            {"@vendorName", "نقداً"},
                            {"@stat", "نقدا"}
                        }

                        SecureDatabaseManager.Instance.ExecuteNonQuery(insertSalesQuery, insertSalesParams)

                        IM.Store(Parcode, Store)

                        ' الحصول على ID بشكل آمن
                        Dim getSalesIdQuery As String = "SELECT TOP 1 id FROM BillsalData WHERE itm_id = @itemId AND bill_no = @billNo AND Stores = @stores ORDER BY id DESC"
                        Dim getSalesIdParams As New Dictionary(Of String, Object) From {
                            {"@itemId", Parcode},
                            {"@billNo", "تسوية"},
                            {"@stores", Store}
                        }

                        Dim salesIdResult As Object = SecureDatabaseManager.Instance.ExecuteScalar(getSalesIdQuery, getSalesIdParams)
                        If salesIdResult IsNot Nothing Then
                            xid = salesIdResult.ToString()

                            ' تحديث آمن للمخزون
                            Dim updateSalesQuery As String = "UPDATE BillsalData SET CurrentStock = @currentStock, CurrentStockTotal = @currentStockTotal WHERE id = @id AND itm_id = @itemId AND Stores = @stores"
                            Dim updateSalesParams As New Dictionary(Of String, Object) From {
                                {"@currentStock", Val(CurrentStock)},
                                {"@currentStockTotal", Val(CurrentStockTotal)},
                                {"@id", xid},
                                {"@itemId", Parcode},
                                {"@stores", Store}
                            }

                            SecureDatabaseManager.Instance.ExecuteNonQuery(updateSalesQuery, updateSalesParams)
                        End If

                    Catch ex As Exception
                        ErrorHandler.LogError("خطأ في تسوية مبيعات المخزون", ex, $"ItemCode: {Parcode}")
                        Throw
                    End Try
                End If

                ' تحديث آمن لحالة الجرد
                Try
                    Dim updateInventoryQuery As String = "UPDATE Items SET InventoryDone = @inventoryDone WHERE itm_id = @itemId AND Stores = @stores"
                    Dim updateInventoryParams As New Dictionary(Of String, Object) From {
                        {"@inventoryDone", 1},
                        {"@itemId", Parcode},
                        {"@stores", Store}
                    }

                    SecureDatabaseManager.Instance.ExecuteNonQuery(updateInventoryQuery, updateInventoryParams)

                Catch ex As Exception
                    ErrorHandler.LogError("خطأ في تحديث حالة الجرد", ex, $"ItemCode: {Parcode}")
                    Throw
                End Try

            End If

        Next

    End Sub

    Public Sub GetStockOnlineStore_To_StockOffline_SingleItem(ByVal itemBarcode As String)
        Dim StockOnline As String = ""
        Dim StockOffline As String = ""
        Dim ProductName As String = ""
        Dim Quantity As String = ""
        Dim Store As String = ""
        Dim group_name As String = ""

        If Not ConnectingOnlineStore() Is Nothing Then
            If ConnectOnlineStore = "YES" Then
                ' Get online stock information for specific item
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select dbo.Product.Code, dbo.OrderItem.ProductName, dbo.OrderItem.Quantity, dbo.Product.Stock, dbo.OrderItem.OrderStatusId " &
                                "From dbo.OrderItem INNER Join dbo.Product On dbo.OrderItem.ProductId = dbo.Product.Id " &
                                "Where (dbo.OrderItem.OrderStatusId = 1) AND dbo.Product.Code = N'" & itemBarcode & "'"
                dr = cmd.ExecuteReader

                If dr.Read() Then
                    ProductName = dr(1).ToString()
                    Quantity = dr(2).ToString()
                    StockOnline = dr(3).ToString()
                End If

                Cn.Close()
                connect()
            Else
                Cn.Close()
                connect()
                MsgBox(Cls_Constant.ErrMsg)
                Exit Sub
            End If
        End If

        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        ' Get offline store information
        Store = Cls.Get_Code_Value_More("Stores", "store", "StausMainStore =N'0'")
        StockOffline = IM.Get_Itm_Store(itemBarcode, Store)

        If StockOnline <> StockOffline Then
            group_name = Cls.Get_Code_Value_More("Items", "group_name", "itm_id =N'" & itemBarcode & "' and Stores =N'" & Store & "'")
            ProductName = Cls.Get_Code_Value_More("Items", "sname", "itm_id =N'" & itemBarcode & "' and Stores =N'" & Store & "'")

            Dim xid As String = ""
            Dim TotalEqual As String

            If Convert.ToDouble(Quantity) >= Convert.ToDouble(StockOffline) Then
                ' Handle case where online quantity is greater
                TotalEqual = StockOnline - StockOffline
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()

                S = "insert into BilltINData(Company_Branch_ID,bill_no,itm_id,itm_cat,itm_name,qu,qu_unity,Stores,username,bill_date) values(" &
                    "N'" & Company_Branch_ID & "',N'تسوية',N'" & itemBarcode & "',N'" & group_name & "',N'" & ProductName & "',N'" & TotalEqual &
                    "',N'" & TotalEqual & "',N'" & Store & "',N'" & UserName & "',N'" & Cls.C_date(DateTime.Today.ToString("yyyy-MM-dd")) & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                IM.Store(itemBarcode, Store)

                xid = Cls.Get_Code_Value_More_ALL_Select("Select Top(100) PERCENT id, bill_no, itm_id, Stores From dbo.BilltINData " &
                                                        "Where (itm_id = N'" & itemBarcode & "') AND (bill_no = N'تسوية') AND " &
                                                        "(Stores = N'" & Store & "') Order By id DESC")

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " &
                                Val(CurrentStockTotal) & " where id =N'" & xid & "' and itm_id = N'" & itemBarcode &
                                "' and Stores =N'" & Store & "'"
                cmd.ExecuteNonQuery()

            ElseIf Convert.ToDouble(Quantity) < Convert.ToDouble(StockOffline) Then
                ' Handle case where offline quantity is greater
                TotalEqual = StockOffline - StockOnline
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()

                S = "insert into BillsalData(Company_Branch_ID,bill_no,itm_id,itm_cat,itm_name,qu,qu_unity,Stores,username,bill_date,Vendorname,Stat) values(" &
                    "N'" & Company_Branch_ID & "',N'تسوية',N'" & itemBarcode & "',N'" & group_name & "',N'" & ProductName & "',N'" & TotalEqual &
                    "',N'" & TotalEqual & "',N'" & Store & "',N'" & UserName & "',N'" & Cls.C_date(DateTime.Today.ToString("yyyy-MM-dd")) & "',N'نقداً',N'نقدا')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                IM.Store(itemBarcode, Store)

                xid = Cls.Get_Code_Value_More_ALL_Select("Select Top(100) PERCENT id, bill_no, itm_id, Stores From dbo.BillsalData " &
                                                        "Where (itm_id = N'" & itemBarcode & "') AND (bill_no = N'تسوية') AND " &
                                                        "(Stores = N'" & Store & "') Order By id DESC")

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BillsalData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " &
                                Val(CurrentStockTotal) & " where id =N'" & xid & "' and itm_id = N'" & itemBarcode &
                                "' and Stores =N'" & Store & "'"
                cmd.ExecuteNonQuery()
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Items set InventoryDone = " & Val(1) & " where itm_id = N'" & itemBarcode &
                             "' and Stores =N'" & Store & "'"
            cmd.ExecuteNonQuery()
        End If
    End Sub

End Class
