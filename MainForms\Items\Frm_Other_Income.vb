﻿Public Class Frm_Other_Income
    Dim Result_Code As String
    Dim Yers As String = ""
    Dim Month As String = ""

    Private Sub FrmOther_Income_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Cls.fill_combo("Other_Income_Category", "Income_Category_Name", cmbRevenueCategory)
        Cls.fill_combo("Other_Income_Category", "Income_Category_Name", cmbRevenueCategoryFind)
        MAXRECORD("Other_Income", "Income_ID")
        Panel_Search.Top = 5000
        GetDateNotBeenActivatedPrograms(dtpDate)
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If Validate_Text() = False Then Exit Sub

        Chack_Code("Other_Income_Category", "Income_Category_ID", "Income_Category_Name", cmbRevenueCategory.Text)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Other_Income(Income_ID,Income_Category_ID,Income_Name,Income_Date,Amount,Note,UserName,Treasury_Code,Company_Branch_ID) values ("
        S = S & "N'" & txtCode.Text & "',N'" & Result_Code & "',N'" & txtRevenueName.Text & "',N'" & Cls.C_date(dtpDate.Text) & "',N'" & txtAmount.Text & "',N'" & txtNotes.Text & "',N'" & UserName & "',N'" & Treasury_Code & "',N'" & Company_Branch_ID & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        Get_Movement_In_Out_Money(dtpDate.Text, Treasury_Code)

        CLEAR_ALL()

        MAXRECORD("Other_Income", "Income_ID")
        Header()
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)
        cmbRevenueCategory.Focus()
    End Sub

    Sub CLEAR_ALL()
        txtAmount.Text = ""
        cmbRevenueCategory.Text = ""
        txtNotes.Text = ""
        txtRevenueName.Text = ""
        txtCode.Text = ""
    End Sub

    Private Sub Chack_Code(ByVal Table As String, ByVal Code As String, ByVal Name As String, ByVal TextBox As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select " & Code & " from " & Table & " where " & Name & "=N'" & TextBox & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            Result_Code = dr(0)
        End If
    End Sub

    Function Validate_Text() As Boolean
        If Trim(cmbRevenueCategory.Text) = "" Then
            MsgBox("فضلاً أختر اسم الفئة", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            cmbRevenueCategory.Focus() : Return False : Exit Function
        End If
        If Trim(txtRevenueName.Text) = "" Then
            MsgBox("فضلاً أختر اسم الدخل", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtRevenueName.Focus() : Return False : Exit Function
        End If
        If Trim(txtAmount.Text) = "" Then
            MsgBox("فضلاً أدخل المبلغ", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtAmount.Focus() : Return False : Exit Function
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * From Other_Income where Income_ID =N'" & txtCode.Text & "'"
        dr = cmd.ExecuteReader()
        If dr.HasRows = True Then
            MsgBox("عفواً يوجد رقم حركة مسجله مسبقا بنفس الرقم", MsgBoxStyle.Exclamation)
            Return False : Exit Function
        End If

        Return True
    End Function

    Private Sub MAXRECORD(ByVal Table As String, ByVal Felds As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " + Table + ""
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            txtCode.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" + Felds + " As int)) as mb FROM " + Table + " where " + Felds + " <> ''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            txtCode.Text = sh + 1
        End If

    End Sub

    Private Sub txtAmount_TextChanged(sender As Object, e As EventArgs) Handles txtAmount.TextChanged
        MyVars.CheckNumber(txtAmount)
    End Sub

    Private Sub cmbIncome_Category_Name_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbRevenueCategory.KeyUp
        If e.KeyCode = 13 Then
            txtRevenueName.Focus()
        End If
    End Sub

    Private Sub dtpIncome_Date_KeyUp(sender As Object, e As KeyEventArgs) Handles dtpDate.KeyUp
        If e.KeyCode = 13 Then
            txtAmount.Focus()
        End If
    End Sub

    Private Sub txtAmount_KeyUp(sender As Object, e As KeyEventArgs) Handles txtAmount.KeyUp
        If e.KeyCode = 13 Then
            txtNotes.Focus()
        End If
    End Sub

    Private Sub Header()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select dbo.Other_Income.Income_ID As مسلسل, dbo.Other_Income_Category.Income_Category_Name As الفئة, dbo.Other_Income.Income_Name As [أسم الدخل], dbo.Other_Income.Income_Date As التاريخ, dbo.Other_Income.Amount As المبلغ,  dbo.Other_Income.Note AS ملاحظات From dbo.Other_Income INNER Join  dbo.Other_Income_Category ON dbo.Other_Income.Income_Category_ID = dbo.Other_Income_Category.Income_Category_ID Where (dbo.Other_Income.Income_ID <> '')"
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
        DataGridView1.Columns(0).Visible = False

        DataGridView1.Columns(1).Width = 90
        DataGridView1.Columns(2).Width = 200

        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(3).Value)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(3).Value = SM
        Next

        Dim SM1 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM1 = SM1 + DataGridView1.Rows(i).Cells(4).Value
        Next
        txtTotalAmount.Text = SM1
    End Sub

    Private Sub HeaderSearch()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select dbo.Other_Income.Income_ID As [مسلسل], dbo.Other_Income_Category.Income_Category_Name As [الفئة], dbo.Other_Income.Income_Name As [أسم الدخل], dbo.Other_Income.Income_Date As [التاريخ], dbo.Other_Income.Amount As المبلغ,  dbo.Other_Income.Note AS ملاحظات From dbo.Other_Income LEFT OUTER Join  dbo.Other_Income_Category ON dbo.Other_Income.Income_Category_ID = dbo.Other_Income_Category.Income_Category_ID Where (dbo.Other_Income.Income_ID <> '')"
        If chkAll.Checked = False Then
            If cmbRevenueCategoryFind.Text <> "" Then
                S = S & " And (dbo.Other_Income_Category.Income_Category_Name = N'" & cmbRevenueCategoryFind.Text.Trim & "')"
            End If
        End If
        If chkDate.Checked = False Then
            S = S & " AND dbo.Other_Income.Income_Date >=N'" & Cls.C_date(dtpFrom.Text) & "' AND dbo.Other_Income.Income_Date <=N'" & Cls.C_date(dtpTo.Text) & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [مسلسل]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [التاريخ]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [الفئة]"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
        DataGridView1.Columns(0).Visible = False
        DataGridView1.Columns(1).Width = 90
        DataGridView1.Columns(2).Width = 200

        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(3).Value)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(3).Value = SM
        Next

        Dim SM1 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM1 = SM1 + DataGridView1.Rows(i).Cells(4).Value
        Next
        txtTotalAmount.Text = SM1

    End Sub

    Private Sub BtnFind_Click(sender As Object, e As EventArgs) Handles BtnFind.Click
        Panel_Search.Top = 20
        Panel_Search.Dock = DockStyle.Fill
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        HeaderSearch()
    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbRevenueCategoryFind.Enabled = False
            cmbRevenueCategoryFind.SelectedIndex = -1
        ElseIf chkAll.Checked = False Then
            cmbRevenueCategoryFind.Enabled = True
            cmbRevenueCategoryFind.SelectedIndex = -1
        End If
    End Sub

    Private Sub chkDate_CheckedChanged(sender As Object, e As EventArgs) Handles chkDate.CheckedChanged
        If chkDate.Checked = True Then
            dtpFrom.Enabled = False
            dtpTo.Enabled = False
        Else
            dtpFrom.Enabled = True
            dtpTo.Enabled = True
        End If
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Panel_Search.Dock = DockStyle.None
        Panel_Search.Top = 5000

    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim Income_ID As String = DataGridView1.SelectedRows(i).Cells(0).Value

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete  from Other_Income where Income_ID =N'" & Income_ID & "'" : cmd.ExecuteNonQuery()

        Next

        HeaderSearch()
        CLEAR_ALL()
        MAXRECORD("Other_Income", "Income_ID")
        cmbRevenueCategory.Focus()
    End Sub

    Private Sub btnRevenueCategory_Click(sender As Object, e As EventArgs) Handles btnRevenueCategory.Click
        Frm_Other_Income_Category.ShowDialog()
    End Sub

    Private Sub txtRevenueName_KeyUp(sender As Object, e As KeyEventArgs) Handles txtRevenueName.KeyUp
        If e.KeyCode = 13 Then
            dtpDate.Focus()
        End If
    End Sub

    Private Sub txtNotes_KeyUp(sender As Object, e As KeyEventArgs) Handles txtNotes.KeyUp
        If e.KeyCode = 13 Then
            btnSave.PerformClick()
        End If
    End Sub

    Private Sub cmbRevenueCategory_DropDown(sender As Object, e As EventArgs) Handles cmbRevenueCategory.DropDown
        Cls.fill_combo("Other_Income_Category", "Income_Category_Name", cmbRevenueCategory)
        Cls.fill_combo("Other_Income_Category", "Income_Category_Name", cmbRevenueCategoryFind)
    End Sub

    Private Sub txtCode_TextChanged(sender As Object, e As EventArgs) Handles txtCode.TextChanged
        MyVars.CheckNumber(txtCode)
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class