﻿Public Class frmAssets

    Private Sub frmAssets_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        On Error Resume Next
        If Action = "Edit" Then
            FillData()
            btnSave.Text = "تعديل"
        Else
            MAXRECORD()
            txtAssets.Focus()
        End If
    End Sub

    Private Sub MAXRECORD()
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select * from Assets"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            txtSeries.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(AssID As float)) as mb FROM Assets"
            dr = cmd.ExecuteReader
            dr.Read()
            Dim sh As Long
            sh = dr("mb")
            txtSeries.Text = sh + 1
        End If
    End Sub

    Private Sub txtOriginalValue_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtOriginalValue.KeyUp
        If e.KeyCode = 13 Then
            txtRateDepreciation.Focus()
        End If
    End Sub

    Private Sub txtOriginalValue_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtOriginalValue.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtOriginalValue)
        txtNetValue.Text = Format(Val(txtOriginalValue.Text) * ((100 - Val(txtRateDepreciation.Text)) / 100), "Fixed")
    End Sub

    Private Sub txtRateDepreciation_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtRateDepreciation.KeyUp
        If e.KeyCode = 13 Then
            txtNotes.Focus()
        End If
    End Sub

    Private Sub txtRateDepreciation_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtRateDepreciation.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtRateDepreciation)
        txtNetValue.Text = Format(Val(txtOriginalValue.Text) * ((100 - Val(txtRateDepreciation.Text)) / 100), "Fixed")
    End Sub

    Private Sub txtAssets_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtAssets.KeyUp
        If e.KeyCode = 13 Then
            cmbTypeAssets.Focus()
        End If
    End Sub

    Private Sub cmbTypeAssets_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbTypeAssets.KeyUp
        If e.KeyCode = 13 Then
            dtpDatePurchase.Focus()
        End If
    End Sub

    Private Sub dtpDatePurchase_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDatePurchase.KeyUp
        If e.KeyCode = 13 Then
            txtOriginalValue.Focus()
        End If
    End Sub

    Private Sub txtNotes_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtNotes.KeyUp
        If e.KeyCode = 13 Then
            btnSave.PerformClick()
        End If
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        If ValidateTextAdd() = False Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If Action = "Edit" Then
            cmd.CommandText = "delete From  Assets where AssID =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Assets(AssID,Original,TypeAssets,DatePurchase,OriginalValue,RateDepreciation,NetValue,Notes,UserName,Treasury_Code,Company_Branch_ID) values ("
        S = S & "N'" & txtSeries.Text.Trim & "' ,N'" & txtAssets.Text.Trim & "' ,N'" & cmbTypeAssets.Text.Trim & "',N'" & Cls.C_date(dtpDatePurchase.Text) & "',"
        S = S & "N'" & txtOriginalValue.Text.Trim & "',N'" & txtRateDepreciation.Text.Trim & "',N'" & txtNetValue.Text.Trim & "',N'" & txtNotes.Text & "',N'" & UserName & "',N'" & Treasury_Code & "',N'" & Company_Branch_ID & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        '===============================================================================
        Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
        If UseAccounts = "YES" Then
            Daily_Restrictions()
        End If
        '===============================================================================

        If Action = "Edit" Then
            Action = "Add"
            Me.Close()
            frmShowAssets.btnShow.PerformClick()
        Else
            MsgBox("تمت عملية الحفظ بنجاح", MsgBoxStyle.Information)
            ClearSave()
            MAXRECORD()
        End If

    End Sub

    Private Sub ClearSave()
        Cls.clear(Me)
        txtAssets.Focus()
    End Sub

    Function ValidateTextAdd() As Boolean
        If txtAssets.Text = "" Then MsgBox("فضلا أدخل الاصل", MsgBoxStyle.Exclamation) : txtAssets.Focus() : Return False
        If cmbTypeAssets.Text = "" Then MsgBox("فضلا ادخل نوع الاصل", MsgBoxStyle.Exclamation) : cmbTypeAssets.Focus() : Return False
        If txtOriginalValue.Text = "" Then MsgBox("فضلا ادخل القيمة الاصلية", MsgBoxStyle.Exclamation) : txtOriginalValue.Focus() : Return False
        If txtRateDepreciation.Text = "" Then MsgBox("فضلا ادخل نسبة الاهلاك", MsgBoxStyle.Exclamation) : txtRateDepreciation.Focus() : Return False
        Return True
    End Function

    Private Sub FillData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select AssID,Original,TypeAssets,DatePurchase,OriginalValue,RateDepreciation,NetValue,Notes FROM Assets where AssID =N'" & EditItmId & "'"
        dr = cmd.ExecuteReader : dr.Read()
        Dim XAssID, XVOriginal, XTypeAssets, XDatePurchase, XOriginalValue, XRateDepreciation, XNetValue, XNotes As String
        XAssID = dr("AssID")
        XVOriginal = dr("Original")
        XTypeAssets = dr("TypeAssets")
        XDatePurchase = dr("DatePurchase")
        XOriginalValue = dr("OriginalValue")
        XRateDepreciation = dr("RateDepreciation")
        XNetValue = dr("NetValue")
        XNotes = dr("Notes")


        txtSeries.Text = XAssID
        txtAssets.Text = XVOriginal
        cmbTypeAssets.Text = XTypeAssets
        dtpDatePurchase.Text = XDatePurchase
        txtOriginalValue.Text = XOriginalValue
        txtRateDepreciation.Text = XRateDepreciation
        txtNetValue.Text = XNetValue
        txtNotes.Text = XNotes
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        If Action = "Edit" Then
            Action = "Add"
        End If
        Me.Close()
    End Sub

    Private Sub txtSeries_TextChanged(sender As Object, e As EventArgs) Handles txtSeries.TextChanged
        MyVars.CheckNumber(txtSeries)
    End Sub

    Private Sub Daily_Restrictions()
        Dim Account As String = ""
        Dim AccountCode As String = ""
        Dim AccountTreasury As String = "" : Dim AccountCodeTreasury As String = ""


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'اصول'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            Account = dr("Link_AccountsTree")
            AccountCode = dr("ACCNumber")
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select ACCName,ACCNumber from AccountsTree where ACCName=N'الخزينة'" : dr = cmd.ExecuteReader
        If dr.Read Then
            AccountTreasury = dr("ACCName") : AccountCodeTreasury = dr("ACCNumber")
        End If

        '========================================================================================

        Dim MOVRegNumber As String = Cls.MAXRECORD("MOVES", "MOVRegNumber")
        Dim MOVID As String = Cls.MAXRECORD("MOVES", "MOVID")
        Dim bill_no As String = Cls.MAXRECORD("Assets", "AssID") - 1

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
        S = S & "N'" & bill_no & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpDatePurchase.Text) & "',N'" & Account & "',N'" & txtNetValue.Text & "',N'" & txtNetValue.Text & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        ' من حساب / الاصول
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
        S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpDatePurchase.Text) & "',N'" & AccountCode & "',N'" & Account & "',N'" & txtNetValue.Text & "',N'0',N'" & Account & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        ' الى حساب / الخزينة
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
        S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpDatePurchase.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'0',N'" & txtNetValue.Text & "',N'" & AccountTreasury & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

    End Sub

End Class