﻿Imports System.Data.SqlClient
Imports System.IO
Imports vb = Microsoft.VisualBasic

Public Class Frm_Edit_Item
    Dim archiveManager As New Cls_ArchiveManager

    Dim ListBoxSelectedIndex As Integer
    'Dim ItmID, IDStores, IDSname As String
    Dim BarcodeMore As String = mykey.GetValue("BarcodeMore", "NO")
    Dim Dt_AddBill_BarcodeMore As New DataTable
    Dim Dt_AddBill As New DataTable
    Dim Dt_AddBill_UnityItems As New DataTable
    Dim Dt_AddBillAlternative As New DataTable

    Dim RNXD As Integer
    Dim EditActive As Boolean = False
    Dim UnityName As String = ""
    Dim GroupsRate As Boolean = False
    Dim branch_ID As String

    Private Sub Headerx()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Try

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If LanguageMainProgram = "العربية" Then
                If ActivateFormatNumberWithSeparators = "YES" Then
                    S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],CAST(TinPriceAverage AS NVARCHAR(50)) as [متوسط سعر الشراء],CAST(SalPrice AS NVARCHAR(50)) as [سعر التجزئة],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة],CAST(MinimumSalPrice AS NVARCHAR(50)) as [حد ادنى للسعر],rng as [حد الطلب] ,CAST(store AS NVARCHAR(50)) as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id <>''")
                Else
                    S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],WholeWholePrice as [سعر جملة الجملة],MinimumSalPrice as [حد ادنى للسعر],rng as [حد الطلب] ,store as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id <>''")
                End If
                If FilterSelect = "Number" Then
                    S = S & " order by [الباركود]"
                End If
                If FilterSelect = "Date" Then
                    S = S & " order by [المجموعة]"
                End If
                If FilterSelect = "Name" Then
                    S = S & " order by [الصنف]"
                End If
            ElseIf LanguageMainProgram = "English" Then
                If ActivateFormatNumberWithSeparators = "YES" Then
                    S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Group], group_branch as [Subgroup], sname as [Item], CAST(TinPrice AS NVARCHAR(50)) as [Purchase Price], CAST(TinPriceAverage AS NVARCHAR(50)) as [Average Purchase Price], CAST(SalPrice AS NVARCHAR(50)) as [Retail Price], CAST(WholePrice AS NVARCHAR(50)) as [Wholesale Price], CAST(WholeWholePrice AS NVARCHAR(50)) as [Bulk Wholesale Price], CAST(MinimumSalPrice AS NVARCHAR(50)) as [Minimum Price], rng as [Reorder Level], CAST(store AS NVARCHAR(50)) as [Stock], Stores as [Store], RateVAT as [VAT Rate]", "items", "id <>''")
                Else
                    S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Group], group_branch as [Subgroup], sname as [Item], TinPrice as [Purchase Price], TinPriceAverage as [Average Purchase Price], SalPrice as [Retail Price], WholePrice as [Wholesale Price], WholeWholePrice as [Bulk Wholesale Price], MinimumSalPrice as [Minimum Price], rng as [Reorder Level], store as [Stock], Stores as [Store], RateVAT as [VAT Rate]", "items", "id <>''")
                End If

                If FilterSelect = "Number" Then
                    S = S & " order by [Barcode]"
                End If
                If FilterSelect = "Date" Then
                    S = S & " order by [Group]"
                End If
                If FilterSelect = "Name" Then
                    S = S & " order by [Item]"
                End If
            End If

            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)

            Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
            If ShowGroupBranch = "NO" Then
                DTGV.Columns(2).Visible = False
            Else
                DTGV.Columns(2).Visible = True
            End If

            Dim HideWholeWholeSalePrice As String = mykey.GetValue("HideWholeWholeSalePrice", "NO")
            If HideWholeWholeSalePrice = "NO" Then
                DTGV.Columns(7).Visible = True
                DTGV.Columns(8).Visible = True
            Else
                DTGV.Columns(7).Visible = False
                DTGV.Columns(8).Visible = False
            End If
            If ShowValueVAT = "NO" Then
                DTGV.Columns(13).Visible = False
            End If

            Dim SM As Double
            For i As Integer = 0 To DTGV.Rows.Count - 1
                If LastTinPriceItems = "YES" Then
                    SM = SM + Val(DTGV.Rows(i).Cells(4).Value.ToString) * Val(DTGV.Rows(i).Cells(11).Value)
                Else
                    SM = SM + Val(DTGV.Rows(i).Cells(5).Value.ToString) * Val(DTGV.Rows(i).Cells(11).Value)
                End If
            Next
            txt_Total.Text = FormatNumberWithSeparators(SM)

            Dim SM1 As Double
            For i As Integer = 0 To DTGV.Rows.Count - 1
                SM1 = SM1 + Val(DTGV.Rows(i).Cells(11).Value)
            Next
            txtTotalQunt.Text = SM1
            txtNumberItems.Text = DTGV.RowCount

            GetGridViewFormatNumberWithSeparators()

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub FrmItemsNew_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If Height_Width_Altitude_Density = "YES" Then
            btnAddHeightWidth.Visible = True
        End If
        Bra.Fil("groups", "g_name", cmbcats)
        Bra.Fil("groups", "g_name", cmbFindCats)
        Bra.Fil("Companies", "Name", cmbCompanies)

        'Cls.FillComboDataSet("stores", "id", "store", cmbStores)
        Cls.fill_combo_Branch("stores", "store", cmbStores)
        Bra.Fil("Type_Unity", "Unity_Name", cmbUnity)

        Cls.fill_combo_orderby("SELECT DISTINCT TOP (100) PERCENT UnitySize_Name, UnitySize_ID FROM     dbo.ItemsUnitySize ORDER BY UnitySize_ID", cmbUnitySize)

        Cls.fill_combo("vendors", "Vendorname", cmbvendores)
        Cls.fill_combo("vendors", "Vendorname", cmbFindVendores)

        txtsearsh.Text = " بحث ...." : txtsearsh.Font = New Font(txtsearsh.Font, FontStyle.Italic)
        'showData()
        Headerx()
        DTGVColumnsWidth()
        cmbcats.Focus()
        PanelViewImage.Top = 5000
        PanelBarcodeMore.Top = 5000
        PanelUnityItems.Top = 5000
        PanelViewItemDeleted.Top = 5000
        PanelItemDiscountRate.Top = 5000
        PanelAlternative.Top = 5000
        PanelOnlineStore.Top = 5000
        PanelHeightWidth.Top = 5000

        cmbStores.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        txtrng.Text = mykey.GetValue("RngDefaultStock", "2")
        GroupBranch()
        'HideWholeWholeSalePrice()
        If UseBalanceBarcode = "YES" Then
            cmbBalanceBarcode.Visible = True
            Label36.Visible = True
        Else
            cmbBalanceBarcode.Visible = False
            Label36.Visible = False
        End If
        If DealingPharmacySystem = "YES" Then
            btnItemsAlternative.Visible = True
            cmb_Expired.Text = "بصلاحية"
            'Cls.fill_combo("Items", "sname", cmbTradeName)
            Cls.FillComboDataAdapterQuickSearch("Items", "sname", cmbTradeName)
            Cls.fill_combo("ItemsAlternative", "ScientificName", cmbScientificName)
        Else
            btnItemsAlternative.Visible = False
        End If
        If ConnectOnlineStore = "YES" Then
            btnDiscountedAfterPrice.Visible = True
        End If
        If ActionNumericSeparators = False Then
            txt_Total.Text = FormatNumberWithThousandSeparator(txt_Total.Text)
            txtTotalQunt.Text = FormatNumberWithThousandSeparator(txtTotalQunt.Text)
            txtNumberItems.Text = FormatNumberWithThousandSeparator(txtNumberItems.Text)
        End If

        If Show_Height_Width_Altitude_Density = "YES" Then
            btnNetWeightView.Visible = True
        Else
            btnNetWeightView.Visible = False
        End If

        If ShowBeginningBalanceAdjustmentItemScreen = "YES" Then
            chkEditFristStore.Enabled = True
        Else
            chkEditFristStore.Enabled = False
        End If

        If LanguageMainProgram = "العربية" Then
            'SetArabic()
        ElseIf LanguageMainProgram = "English" Then
            SetEnglish()
        End If
    End Sub

    Private Sub cmbcats_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbcats.DropDown
        cmbGroup_Branch.Text = ""
    End Sub

    Private Sub cmbcats_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbcats.KeyUp
        Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
        If e.KeyCode = 13 Then
            If ShowGroupBranch = "NO" Then
                cmbitmnm.Focus()
            Else
                cmbGroup_Branch.Focus()
            End If
        End If
    End Sub

    Function ValidateSave() As Boolean
        Try
            If cmbcats.Text = "" Then MsgBox("فضلا أدخل مجموعة الصنف", MsgBoxStyle.Exclamation) : Return False
            If cmbitmnm.Text = "" Then MsgBox("فضلا أدخل اسم الصنف", MsgBoxStyle.Exclamation) : Return False
            'If cmbUnity.Text = "" Then MsgBox("فضلا أدخل وحد القياس", MsgBoxStyle.Exclamation) : cmbUnity.Focus() : Return False
            If cmbStores.Text = "" Then MsgBox("من فضلك اختر المخزن المورد اليه", MsgBoxStyle.Exclamation) : cmbStores.Focus() : Return False
            If txtqunt.Text = "" Then MsgBox("فضلا أدخل الكمية ", MsgBoxStyle.Exclamation) : txtqunt.Focus() : Return False
            'If Val(txttinprice.Text.Trim) = 0 Then MsgBox("فضلا أدخل سعر الشراء", MsgBoxStyle.Exclamation) : txttinprice.Focus() : Return False
            'If Val(txt_priseSal.Text.Trim) = 0 Then MsgBox("فضلا أدخل سعر التجزئة", MsgBoxStyle.Exclamation) : txt_priseSal.Focus() : Return False
            If TxtPrc.Text = "" Then MsgBox("فضلا أدخل باركود الصنف", MsgBoxStyle.Exclamation) : Return False

            'cmd.CommandText = "select count(*) from Items where group_name =N'" & cmbcats.Text.Trim & "' and sname =N'" & cmbitmnm.Text.Trim & "'" : H = cmd.ExecuteScalar
            'If H > 0 Then
            '    MsgBox("الصنف مسجل مسبقاً", MsgBoxStyle.Exclamation) : TxtPrc.Focus() : Return False
            'End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where itm_id =N'" & TxtPrc.Text.Trim & "' and Stores =N'" & cmbStores.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                If itmprc <> TxtPrc.Text.Trim Then
                    MsgBox("الباركود مسجل مسبقاً", MsgBoxStyle.Exclamation) : TxtPrc.Focus() : Return False
                End If
            End If

            Try
                If cmbvendores.Text <> "" Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select count(*) from vendors where Vendorname =N'" & cmbvendores.Text.Trim & "'" : H = cmd.ExecuteScalar
                    If H = 0 Then
                        MsgBox("أسم المورد غير مسجل", MsgBoxStyle.Exclamation) : cmbvendores.Focus() : Return False
                    End If
                End If
            Catch ex As Exception
            End Try

            'Try
            '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '    cmd.CommandText = "select count(*) from Items where sname =N'" & cmbitmnm.Text.Trim & "' and Stores =N'" & cmbStores.Text.Trim & "'" : H = cmd.ExecuteScalar
            '    If H > 0 Then
            '        MsgBox(" الصنف مسجل مسبقاً بنفس أسم المخزن", MsgBoxStyle.Exclamation) : cmbitmnm.Focus() : Return False
            '    End If
            'Catch ex As Exception
            '    ErrorHandling(ex, Me.Text)
            'End Try

            'Try
            '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '    cmd.CommandText = "select count(*) from Items where itm_id =N'" & TxtPrc.Text.Trim & "' and Stores =N'" & cmbStores.Text.Trim & "'" : H = cmd.ExecuteScalar
            '    If H > 0 Then
            '        MsgBox(" الباركود مسجل مسبقاً بنفس أسم المخزن", MsgBoxStyle.Exclamation) : cmbitmnm.Focus() : Return False
            '    End If
            'Catch ex As Exception
            '    ErrorHandling(ex, Me.Text)
            'End Try

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        Return True
    End Function

    Private Sub Savex()
        Try
            Dim expired As String
            If cmb_Expired.Text = "بدون صلاحية" Then
                expired = ""
            Else
                expired = Cls.C_date(dtpExpiration.Text)
            End If

            Dim QuickSearch As String
            If chkQuickSearch.Checked = True Then
                QuickSearch = "0"
            Else
                QuickSearch = "1"
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Items (Company_Branch_ID,itm_id,group_name,group_branch,sname,Unity,rng,tinprice,salprice,TinPriceAverage,WholePrice,WholeWholePrice,MinimumSalPrice,tin,sal,btin,bsal,decayed,tinpricetotal,salpricetotal,btinpricetotal,bsalpricetotal,decayedpricetotal,store,ValStore,profits,UserName,Stores,QuickSearch,Vendorname) values ("
            S = S & "N'" & Company_Branch_ID & "',N'" & TxtPrc.Text & "',N'" & cmbcats.Text.Trim & "',N'" & cmbGroup_Branch.Text.Trim & "',N'" & cmbitmnm.Text & "',N'" & cmbUnity.Text & "',"
            S = S & "" & Val(txtrng.Text) & ","
            S = S & "" & Val(txttinprice.Text) & ","
            S = S & "" & Val(txt_priseSal.Text) & ","
            S = S & "" & Val(txttinprice.Text) & ","
            S = S & "" & Val(txtWholePrice.Text) & ","
            S = S & "" & Val(txtWholeWholePrice.Text) & ","
            S = S & "" & Val(txtMinimumSalPrice.Text) & ","
            S = S & "N'" & txtqunt.Text & "',0,0,0,0,0,0,0,0,0,0,0,0,N'" & UserName & "',N'" & cmbStores.Text & "',N'" & QuickSearch & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            Dim X As String = "جرد"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into BilltINData (Company_Branch_ID,bill_no,itm_id,itm_cat,group_branch,itm_name,itm_Unity,price,TinPriceAverage,qu,totalprice,UserName,Stores,bill_date,bill_EndDate,Expired,Vendorname,qu_expired)"
            S = S & " values (N'" & Company_Branch_ID & "',N'" & X & "',N'" & TxtPrc.Text & "',N'" & cmbcats.Text.Trim & "',N'" & cmbGroup_Branch.Text.Trim & "',N'" & cmbitmnm.Text.Trim & "',N'" & cmbUnity.Text.Trim & "',N'" & txttinprice.Text & "',N'" & txttinprice.Text & "'," & Val(txtqunt.Text) & "," & Val(txttinprice.Text) * Val(txtqunt.Text) & ",N'" & UserName & "',N'" & cmbStores.Text & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & expired & "',N'" & cmb_Expired.Text & "',N'" & cmbvendores.Text.Trim & "'," & Val(txtqunt.Text) & ",N'" & cmbvendores.Text.Trim & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            ImageUpdate()

            IM.Store(TxtPrc.Text.Trim, cmbStores.Text)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & X & "' and itm_id = N'" & TxtPrc.Text.Trim & "' and Stores =N'" & cmbStores.Text & "'" : cmd.ExecuteNonQuery()


            Bra.Fil("groups", "g_name", cmbcats)
            Cls.fill_combo_Branch("stores", "store", cmbStores)
            Clear_All()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
    Sub Clear_All()
        Try
            cmbitmnm.Text = ""
            cmbUnity.Text = ""
            cmbStores.Text = ""
            TxtPrc.Text = ""
            txtqunt.Text = ""
            txttinprice.Text = ""
            txt_priseSal.Text = ""
            txtWholePrice.Text = ""
            txtWholeWholePrice.Text = ""
            txtrng.Text = ""
            txtMinimumSalPrice.Text = ""
            txtHeight.Text = ""
            txtWidth.Text = ""
            txtAltitude.Text = ""
            txtDensity.Text = ""
            cmbGroup_Branch.Text = ""
            cmbitmnm.Focus()
            Dt_AddBill_UnityItems.Rows.Clear()
            Dt_AddBillAlternative.Rows.Clear()
            dgvBarcodeMore.DataSource = ""
            txtBarcodeMore.Text = ""

            txtIDStores.Text = ""
            txtIDSname.Text = ""
            txtIDItm.Text = ""
            cmbvendores.Text = ""
            If GroupsRate = False Then
                txtRateDiscTinPrice.Text = "0"
                txtRateDiscSalPrice.Text = "0"
                txtRateDiscWholePrice.Text = "0"
                txtRateDiscWholeWholePrice.Text = "0"
            End If
            txtRateDiscTinPriceAfter.Text = "0"
            txtRateDiscSalPriceAfter.Text = "0"
            txtRateDiscWholePriceAfter.Text = "0"
            txtRateDiscWholeWholePriceAfter.Text = "0"
            cmbCompanies.Text = ""
            txtDiscountedPrice.Text = "0"
            txtDiscountedPrice2.Text = "0"
            txtDiscountedPrice3.Text = "0"
            txtTag.Text = ""
            txtLimitQuantity.Text = ""
            txtDescription.Text = ""
            txtStockOnline.Text = "0"
        Catch ex As Exception
        End Try
    End Sub

    Private Sub btnsave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnsave.Click

        GetNumericValueSeparators()

        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If


        GetRateDiscPriceAfter()

        If txtqunt.Enabled = False Then
            If ValidateSave() = False Then Exit Sub
            AddGroubs()
            AddGroup_Branch()
            AddStores()
            UpdateItems()
        Else
            If ValidateSave() = False Then Exit Sub
            'Deletexxx()
            AddGroubs()
            AddGroup_Branch()
            AddStores()
            'Savex()
            UpdateItemsALL()
        End If

        If ConnectOnlineStore = "YES" Then
            Cos.UpdateProductAll(txtTag.Text, txtDescription.Text, cmbcats.Text, cmbCompanies.Text, txtLimitQuantity.Text, txt_priseSal.Text, txtWholePrice.Text, txtWholeWholePrice.Text, txtDiscountedPrice.Text, txtDiscountedPrice2.Text, txtDiscountedPrice3.Text, TxtPrc.Text, cmbitmnm.Text, txtIDSname.Text)
            If chkEditFristStore.Checked = False Then
                Cos.UpdateProductStock(txtStockOnline.Text, TxtPrc.Text, cmbitmnm.Text)
            End If
        End If

        GetBarcodeMoreAdded()

        GetItemsUnity()

        GetUpdateItemsAlternative()

        Update_Grid()

        Clear_All()
        Clear()
        PanelBarcodeMore.Top = 5000
        itmprc = ""
        cmbUnity.Text = mykey.GetValue("DefaultUnityName", "قطعة")
        txtNumberPieces.Text = 1
    End Sub

    Private Sub Update_Grid()

        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.Rows.Count) = 0 Then Beep() : Exit Sub
        DTGV.SelectedRows(0).Cells(0).Value = TxtPrc.Text
        DTGV.SelectedRows(0).Cells(1).Value = cmbcats.Text
        DTGV.SelectedRows(0).Cells(2).Value = cmbGroup_Branch.Text
        DTGV.SelectedRows(0).Cells(3).Value = cmbitmnm.Text
        DTGV.SelectedRows(0).Cells(4).Value = txttinprice.Text
        DTGV.SelectedRows(0).Cells(5).Value = txtTinPriceAverage.Text
        DTGV.SelectedRows(0).Cells(6).Value = txt_priseSal.Text
        DTGV.SelectedRows(0).Cells(7).Value = txtWholePrice.Text
        DTGV.SelectedRows(0).Cells(8).Value = txtWholeWholePrice.Text
        DTGV.SelectedRows(0).Cells(9).Value = txtMinimumSalPrice.Text
        DTGV.SelectedRows(0).Cells(10).Value = txtrng.Text
        DTGV.SelectedRows(0).Cells(11).Value = txtRateVAT.Text
        'For i As Integer = 0 To dgvUnityItems.Rows.Count - 1
        '    If dgvUnityItems.Rows(i).Cells(6).Value = "الاصغر" Then
        '        DTGV.SelectedRows(0).Cells(6).Value = dgvUnityItems.Rows(i).Cells(0).Value
        '    End If
        'Next

    End Sub

    Sub Clear()
        cmbcats.Text = ""
        cmbitmnm.Text = ""
        cmbStores.Text = ""
        TxtPrc.Text = ""
        txtqunt.Text = ""
        txttinprice.Text = ""
        txtTinPriceAverage.Text = ""
        txt_priseSal.Text = ""
        txtWholePrice.Text = ""
        txtMinimumSalPrice.Text = ""
        cmbitmnm.Focus()
    End Sub

    Private Sub UpdateItems()
        Dim QuickSearch As String
        If chkQuickSearch.Checked = True Then
            QuickSearch = "0"
        Else
            QuickSearch = "1"
        End If

        Dim BalanceBarcode As String
        If cmbBalanceBarcode.Text = "بدون ميزان الباركود" Then
            BalanceBarcode = "0"
        Else
            BalanceBarcode = "1"
        End If

        Dim PriceIncludesVAT As Integer
        If chkPriceIncludesVAT.Checked = True Then
            PriceIncludesVAT = 1
        Else
            PriceIncludesVAT = 0
        End If

        Dim TypePlusDiscRateSalPrice, TypePlusDiscRateWholePrice, TypePlusDiscRateWholeWholePrice As Double
        If rdoDiscRateSalPrice.Checked = True Then
            TypePlusDiscRateSalPrice = 0
        Else
            TypePlusDiscRateSalPrice = 1
        End If
        If rdoDiscRateWholePrice.Checked = True Then
            TypePlusDiscRateWholePrice = 0
        Else
            TypePlusDiscRateWholePrice = 1
        End If
        If rdoDiscRateWholeWholePrice.Checked = True Then
            TypePlusDiscRateWholeWholePrice = 0
        Else
            TypePlusDiscRateWholeWholePrice = 1
        End If

        Dim DeferredCurrentDiscount As Double
        If rdoCurrentDiscTinPrice.Checked = True Then
            DeferredCurrentDiscount = 0
        Else
            DeferredCurrentDiscount = 1
        End If

        'connectionStringClose()
        'connectionStringTransaction()
        'Try


        If chkEditAllStores.Checked = False Then

            If chkParcodeChange.Checked = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update items set Company_Branch_ID =N'" & Company_Branch_ID & "',TinPrice = " & Val(txttinprice.Text) & ",SalPrice = " & Val(txt_priseSal.Text) & ",rng = " & Val(txtrng.Text) & ",Height =N'" & txtHeight.Text & "',Width =N'" & txtWidth.Text & "',Altitude =N'" & txtAltitude.Text & "',WholePrice = " & Val(txtWholePrice.Text) & ",WholeWholePrice = " & Val(txtWholeWholePrice.Text) & ",MinimumSalPrice = " & Val(txtMinimumSalPrice.Text) & ",QuickSearch = " & QuickSearch & ",BalanceBarcode =N'" & BalanceBarcode & "',RateWholePrice =N'" & txtRateWholePrice.Text & "',RateWholeWholePrice =N'" & txtRateWholeWholePrice.Text & "',RateMinimumSalPrice =N'" & txtRateMinimumSalPrice.Text & "',RateVAT =N'" & txtRateVAT.Text & "',Unity =N'" & UnityName & "',Vendorname =N'" & cmbvendores.Text.Trim & "',TinPriceAverage =N'" & txtTinPriceAverage.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & ",DiscountedPrice = " & Val(txtDiscountedPrice.Text) & ",DiscountedPrice2 = " & Val(txtDiscountedPrice2.Text) & ",DiscountedPrice3 = " & Val(txtDiscountedPrice3.Text) & ",LimitQuantity = " & Val(txtLimitQuantity.Text) & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update items set RateDiscTinPrice = " & Val(txtRateDiscTinPrice.Text) & ",RateDiscSalPrice = " & Val(txtRateDiscSalPrice.Text) & ",RateDiscWholePrice = " & Val(txtRateDiscWholePrice.Text) & ",RateDiscWholeWholePrice = " & Val(txtRateDiscWholeWholePrice.Text) & ",RateDiscTinPriceAfter = " & Val(txtRateDiscTinPriceAfter.Text) & ",RateDiscSalPriceAfter = " & Val(txtRateDiscSalPriceAfter.Text) & ",RateDiscWholePriceAfter = " & Val(txtRateDiscWholePriceAfter.Text) & ",RateDiscWholeWholePriceAfter = " & Val(txtRateDiscWholeWholePriceAfter.Text) & ",TypePlusDiscRateSalPrice = " & Val(TypePlusDiscRateSalPrice) & ",TypePlusDiscRateWholePrice = " & Val(TypePlusDiscRateWholePrice) & ",TypePlusDiscRateWholeWholePrice = " & Val(TypePlusDiscRateWholeWholePrice) & ",DeferredCurrentDiscount = " & Val(DeferredCurrentDiscount) & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If ConnectOnlineStore = "YES" Then
                    connect()
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "UPDATE items SET CompaniesID = @CompaniesID, CompaniesName = @CompaniesName, Tag = @Tag, Description = @Description, LimitQuantity = @LimitQuantity, DiscountedPrice = @DiscountedPrice, DiscountedPrice2 = @DiscountedPrice2, DiscountedPrice3 = @DiscountedPrice3 WHERE itm_id = @ItemID AND Stores = N'" & txtIDStores.Text & "'"
                    cmd.Parameters.Clear()
                    cmd.Parameters.AddWithValue("@CompaniesID", lblCompanyId.Text)
                    cmd.Parameters.AddWithValue("@CompaniesName", cmbCompanies.Text)
                    cmd.Parameters.AddWithValue("@Tag", txtTag.Text)
                    cmd.Parameters.AddWithValue("@Description", txtDescription.Text)
                    cmd.Parameters.AddWithValue("@LimitQuantity", Val(txtLimitQuantity.Text))
                    cmd.Parameters.AddWithValue("@DiscountedPrice", Val(txtDiscountedPrice.Text))
                    cmd.Parameters.AddWithValue("@DiscountedPrice2", Val(txtDiscountedPrice2.Text))
                    cmd.Parameters.AddWithValue("@DiscountedPrice3", Val(txtDiscountedPrice3.Text))
                    cmd.Parameters.AddWithValue("@ItemID", txtIDItm.Text)

                    cmd.ExecuteNonQuery()
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set Company_Branch_ID =N'" & Company_Branch_ID & "',bill_EndDate = " & Cls.C_date(dtpExpiration.Text) & ",Expired =N'" & cmb_Expired.Text & "',bill_ProductionDate =N'" & Cls.C_date(dtpProductionDate.Text) & "',Vendorname =N'" & cmbvendores.Text.Trim & "',itm_Unity =N'" & UnityName & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set qu_expired =N'" & txtqunt.Text.Trim & "',Treasury_Code =N'" & Treasury_Code & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'  and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set Discounts = " & Val(txtRateDiscTinPrice.Text) & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update items set sname = N'" & cmbitmnm.Text.Trim & "',group_name = N'" & cmbcats.Text.Trim & "',group_branch =N'" & cmbGroup_Branch.Text.Trim & "',Unity =N'" & cmbUnity.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BillsalData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update data_decayed set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update IM_Bsal_Data set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update IM_Btin_Data set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsOffersDiscounts set sname = N'" & cmbitmnm.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsTransfer set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsTransferData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and StoresFrom =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Receive_BillsalData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Receive_BilltINData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsTransfer_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsTransfer_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Manufacturing_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Manufacturing_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ManufacturingProductAdd set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

            End If

            '==============================================================================

            If chkParcodeChange.Checked = True Then
                ' تم حذف اسم المخزن من تعديل  items - BilltINData
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update items set Company_Branch_ID =N'" & Company_Branch_ID & "',itm_id =N'" & TxtPrc.Text & "',group_name =N'" & cmbcats.Text & "',group_branch =N'" & cmbGroup_Branch.Text & "',sname =N'" & cmbitmnm.Text & "',Unity =N'" & UnityName & "',TinPrice = " & Val(txttinprice.Text) & ",SalPrice = " & Val(txt_priseSal.Text) & ",rng = " & Val(txtrng.Text) & ",WholePrice = " & Val(txtWholePrice.Text) & ",WholeWholePrice = " & Val(txtWholeWholePrice.Text) & ",MinimumSalPrice = " & Val(txtMinimumSalPrice.Text) & ",QuickSearch = " & QuickSearch & ",Height =N'" & txtHeight.Text & "',Width =N'" & txtWidth.Text & "',Altitude =N'" & txtAltitude.Text & "',RateWholePrice =N'" & txtRateWholePrice.Text & "',RateWholeWholePrice =N'" & txtRateWholeWholePrice.Text & "',RateMinimumSalPrice =N'" & txtRateMinimumSalPrice.Text & "',RateVAT =N'" & txtRateVAT.Text & "',BalanceBarcode =N'" & BalanceBarcode & "',Vendorname =N'" & cmbvendores.Text.Trim & "',TinPriceAverage =N'" & txtTinPriceAverage.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & ",DiscountedPrice = " & Val(txtDiscountedPrice.Text) & ",DiscountedPrice2 = " & Val(txtDiscountedPrice2.Text) & ",DiscountedPrice3 = " & Val(txtDiscountedPrice3.Text) & ",LimitQuantity = " & Val(txtLimitQuantity.Text) & " where sname =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update items set RateDiscTinPrice = " & Val(txtRateDiscTinPrice.Text) & ",RateDiscSalPrice = " & Val(txtRateDiscSalPrice.Text) & ",RateDiscWholePrice = " & Val(txtRateDiscWholePrice.Text) & ",RateDiscWholeWholePrice = " & Val(txtRateDiscWholeWholePrice.Text) & ",RateDiscTinPriceAfter = " & Val(txtRateDiscTinPriceAfter.Text) & ",RateDiscSalPriceAfter = " & Val(txtRateDiscSalPriceAfter.Text) & ",RateDiscWholePriceAfter = " & Val(txtRateDiscWholePriceAfter.Text) & ",RateDiscWholeWholePriceAfter = " & Val(txtRateDiscWholeWholePriceAfter.Text) & ",TypePlusDiscRateSalPrice = " & Val(TypePlusDiscRateSalPrice) & ",TypePlusDiscRateWholePrice = " & Val(TypePlusDiscRateWholePrice) & ",TypePlusDiscRateWholeWholePrice = " & Val(TypePlusDiscRateWholeWholePrice) & ",DeferredCurrentDiscount = " & Val(DeferredCurrentDiscount) & " where sname =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If ConnectOnlineStore = "YES" Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update items set CompaniesID = " & lblCompanyId.Text & ",CompaniesName = N'" & cmbCompanies.Text & "',Tag = " & txtTag.Text & ",Description = N'" & txtDescription.Text & "',LimitQuantity = " & txtLimitQuantity.Text & ",DiscountedPrice = " & Val(txtDiscountedPrice.Text) & ",DiscountedPrice2 = " & Val(txtDiscountedPrice2.Text) & ",DiscountedPrice3 = " & Val(txtDiscountedPrice3.Text) & " where sname =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set Company_Branch_ID =N'" & Company_Branch_ID & "',bill_EndDate = " & Cls.C_date(dtpExpiration.Text) & ",Expired =N'" & cmb_Expired.Text & "',Vendorname =N'" & cmbvendores.Text.Trim & "',itm_Unity =N'" & UnityName & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set qu_expired =N'" & txtqunt.Text.Trim & "',Treasury_Code =N'" & Treasury_Code & "' where itm_name =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set Discounts = " & Val(txtRateDiscTinPrice.Text) & " where itm_name =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update items set itm_id = N'" & TxtPrc.Text & "',group_name = N'" & cmbcats.Text & "',group_branch =N'" & cmbGroup_Branch.Text & "',Unity =N'" & UnityName & "' where sname =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set itm_id = N'" & TxtPrc.Text & "' where itm_name =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BillsalData set itm_id = N'" & TxtPrc.Text & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update data_decayed set itm_id = N'" & TxtPrc.Text & "' where itm_name =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update IM_Bsal_Data set itm_id = N'" & TxtPrc.Text & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update IM_Btin_Data set itm_id = N'" & TxtPrc.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsOffersDiscounts set itm_id = N'" & TxtPrc.Text.Trim & "' where sname =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsTransfer set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_name =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsTransferData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_name =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Receive_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Receive_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsTransfer_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsTransfer_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Manufacturing_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Manufacturing_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ManufacturingProductAdd set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

            End If

        Else



            If chkParcodeChange.Checked = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update items set Company_Branch_ID =N'" & Company_Branch_ID & "',TinPrice = " & Val(txttinprice.Text) & ",SalPrice = " & Val(txt_priseSal.Text) & ",rng = " & Val(txtrng.Text) & ",WholePrice = " & Val(txtWholePrice.Text) & ",WholeWholePrice = " & Val(txtWholeWholePrice.Text) & ",MinimumSalPrice = " & Val(txtMinimumSalPrice.Text) & ",QuickSearch = " & QuickSearch & ",BalanceBarcode =N'" & BalanceBarcode & "',RateWholePrice =N'" & txtRateWholePrice.Text & "',RateWholeWholePrice =N'" & txtRateWholeWholePrice.Text & "',RateMinimumSalPrice =N'" & txtRateMinimumSalPrice.Text & "',RateVAT =N'" & txtRateVAT.Text & "',Unity =N'" & UnityName & "',Vendorname =N'" & cmbvendores.Text.Trim & "',TinPriceAverage =N'" & txtTinPriceAverage.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & ",DiscountedPrice = " & Val(txtDiscountedPrice.Text) & ",DiscountedPrice2 = " & Val(txtDiscountedPrice2.Text) & ",DiscountedPrice3 = " & Val(txtDiscountedPrice3.Text) & ",LimitQuantity = " & Val(txtLimitQuantity.Text) & " where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update items set RateDiscTinPrice = " & Val(txtRateDiscTinPrice.Text) & ",RateDiscSalPrice = " & Val(txtRateDiscSalPrice.Text) & ",RateDiscWholePrice = " & Val(txtRateDiscWholePrice.Text) & ",RateDiscWholeWholePrice = " & Val(txtRateDiscWholeWholePrice.Text) & ",RateDiscTinPriceAfter = " & Val(txtRateDiscTinPriceAfter.Text) & ",RateDiscSalPriceAfter = " & Val(txtRateDiscSalPriceAfter.Text) & ",RateDiscWholePriceAfter = " & Val(txtRateDiscWholePriceAfter.Text) & ",RateDiscWholeWholePriceAfter = " & Val(txtRateDiscWholeWholePriceAfter.Text) & ",TypePlusDiscRateSalPrice = " & Val(TypePlusDiscRateSalPrice) & ",TypePlusDiscRateWholePrice = " & Val(TypePlusDiscRateWholePrice) & ",TypePlusDiscRateWholeWholePrice = " & Val(TypePlusDiscRateWholeWholePrice) & ",DeferredCurrentDiscount = " & Val(DeferredCurrentDiscount) & " where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If ConnectOnlineStore = "YES" Then
                    connect()
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "UPDATE items SET CompaniesID = @CompaniesID, CompaniesName = @CompaniesName, Tag = @Tag, Description = @Description, LimitQuantity = @LimitQuantity, DiscountedPrice = @DiscountedPrice, DiscountedPrice2 = @DiscountedPrice2, DiscountedPrice3 = @DiscountedPrice3 WHERE itm_id = @ItemID"
                    cmd.Parameters.Clear()
                    cmd.Parameters.AddWithValue("@CompaniesID", lblCompanyId.Text)
                    cmd.Parameters.AddWithValue("@CompaniesName", cmbCompanies.Text)
                    cmd.Parameters.AddWithValue("@Tag", txtTag.Text)
                    cmd.Parameters.AddWithValue("@Description", txtDescription.Text)
                    cmd.Parameters.AddWithValue("@LimitQuantity", Val(txtLimitQuantity.Text))
                    cmd.Parameters.AddWithValue("@DiscountedPrice", Val(txtDiscountedPrice.Text))
                    cmd.Parameters.AddWithValue("@DiscountedPrice2", Val(txtDiscountedPrice2.Text))
                    cmd.Parameters.AddWithValue("@DiscountedPrice3", Val(txtDiscountedPrice3.Text))
                    cmd.Parameters.AddWithValue("@ItemID", txtIDItm.Text)

                    cmd.ExecuteNonQuery()
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set Company_Branch_ID =N'" & Company_Branch_ID & "',bill_EndDate = " & Cls.C_date(dtpExpiration.Text) & ",Expired =N'" & cmb_Expired.Text & "',bill_ProductionDate =N'" & Cls.C_date(dtpProductionDate.Text) & "',Vendorname =N'" & cmbvendores.Text.Trim & "',itm_Unity =N'" & UnityName & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set qu_expired =N'" & txtqunt.Text.Trim & "',Treasury_Code =N'" & Treasury_Code & "' where itm_id =N'" & txtIDItm.Text & "'  and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set Discounts = " & Val(txtRateDiscTinPrice.Text) & " where itm_id =N'" & txtIDItm.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update items set sname = N'" & cmbitmnm.Text.Trim & "',group_name = N'" & cmbcats.Text.Trim & "',group_branch =N'" & cmbGroup_Branch.Text.Trim & "',Unity =N'" & cmbUnity.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BillsalData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update data_decayed set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update IM_Bsal_Data set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update IM_Btin_Data set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsOffersDiscounts set sname = N'" & cmbitmnm.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsTransfer set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsTransferData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Receive_BillsalData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Receive_BilltINData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsTransfer_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsTransfer_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Manufacturing_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Manufacturing_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ManufacturingProductAdd set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

            End If

            '==============================================================================

            If chkParcodeChange.Checked = True Then
                ' تم حذف اسم المخزن من تعديل  items - BilltINData
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update items set Company_Branch_ID =N'" & Company_Branch_ID & "',itm_id =N'" & TxtPrc.Text & "',group_name =N'" & cmbcats.Text & "',group_branch =N'" & cmbGroup_Branch.Text & "',sname =N'" & cmbitmnm.Text & "',Unity =N'" & UnityName & "',TinPrice = " & Val(txttinprice.Text) & ",SalPrice = " & Val(txt_priseSal.Text) & ",rng = " & Val(txtrng.Text) & ",WholePrice = " & Val(txtWholePrice.Text) & ",WholeWholePrice = " & Val(txtWholeWholePrice.Text) & ",MinimumSalPrice = " & Val(txtMinimumSalPrice.Text) & ",QuickSearch = " & QuickSearch & ",RateWholePrice =N'" & txtRateWholePrice.Text & "',RateWholeWholePrice =N'" & txtRateWholeWholePrice.Text & "',RateMinimumSalPrice =N'" & txtRateMinimumSalPrice.Text & "',RateVAT =N'" & txtRateVAT.Text & "',BalanceBarcode =N'" & BalanceBarcode & "',Vendorname =N'" & cmbvendores.Text.Trim & "',TinPriceAverage =N'" & txtTinPriceAverage.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & ",DiscountedPrice = " & Val(txtDiscountedPrice.Text) & ",DiscountedPrice2 = " & Val(txtDiscountedPrice2.Text) & ",DiscountedPrice3 = " & Val(txtDiscountedPrice3.Text) & ",LimitQuantity = " & Val(txtLimitQuantity.Text) & " where sname =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update items set RateDiscTinPrice = " & Val(txtRateDiscTinPrice.Text) & ",RateDiscSalPrice = " & Val(txtRateDiscSalPrice.Text) & ",RateDiscWholePrice = " & Val(txtRateDiscWholePrice.Text) & ",RateDiscWholeWholePrice = " & Val(txtRateDiscWholeWholePrice.Text) & ",RateDiscTinPriceAfter = " & Val(txtRateDiscTinPriceAfter.Text) & ",RateDiscSalPriceAfter = " & Val(txtRateDiscSalPriceAfter.Text) & ",RateDiscWholePriceAfter = " & Val(txtRateDiscWholePriceAfter.Text) & ",RateDiscWholeWholePriceAfter = " & Val(txtRateDiscWholeWholePriceAfter.Text) & ",TypePlusDiscRateSalPrice = " & Val(TypePlusDiscRateSalPrice) & ",TypePlusDiscRateWholePrice = " & Val(TypePlusDiscRateWholePrice) & ",TypePlusDiscRateWholeWholePrice = " & Val(TypePlusDiscRateWholeWholePrice) & ",DeferredCurrentDiscount = " & Val(DeferredCurrentDiscount) & " where sname =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()

                If ConnectOnlineStore = "YES" Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update items set CompaniesID = " & lblCompanyId.Text & ",CompaniesName = N'" & cmbCompanies.Text & "',Tag = " & txtTag.Text & ",Description = N'" & txtDescription.Text & "',LimitQuantity = " & txtLimitQuantity.Text & ",DiscountedPrice = " & Val(txtDiscountedPrice.Text) & ",DiscountedPrice2 = " & Val(txtDiscountedPrice2.Text) & ",DiscountedPrice3 = " & Val(txtDiscountedPrice3.Text) & " where sname =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set Company_Branch_ID =N'" & Company_Branch_ID & "',bill_EndDate = " & Cls.C_date(dtpExpiration.Text) & ",Expired =N'" & cmb_Expired.Text & "',Vendorname =N'" & cmbvendores.Text.Trim & "',itm_Unity =N'" & UnityName & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set qu_expired =N'" & txtqunt.Text.Trim & "',Treasury_Code =N'" & Treasury_Code & "' where itm_name =N'" & txtIDSname.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set Discounts = " & Val(txtRateDiscTinPrice.Text) & " where itm_name =N'" & txtIDSname.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update items set itm_id = N'" & TxtPrc.Text & "',group_name = N'" & cmbcats.Text & "',group_branch =N'" & cmbGroup_Branch.Text & "',Unity =N'" & UnityName & "' where sname =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set itm_id = N'" & TxtPrc.Text & "' where itm_name =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BillsalData set itm_id = N'" & TxtPrc.Text & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update data_decayed set itm_id = N'" & TxtPrc.Text & "' where itm_name =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update IM_Bsal_Data set itm_id = N'" & TxtPrc.Text & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update IM_Btin_Data set itm_id = N'" & TxtPrc.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsOffersDiscounts set itm_id = N'" & TxtPrc.Text.Trim & "' where sname =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsTransfer set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_name =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsTransferData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_name =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Receive_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Receive_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsTransfer_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ItemsTransfer_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Manufacturing_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Manufacturing_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update ManufacturingProductAdd set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

            End If
        End If


        'trans.Commit()
        'connectionStringOpen()
        'Catch ex As Exception
        '    trans.Rollback()
        '    ErrorHandling(ex, Me.Text)
        '    Cn.Close()
        '    connectionStringOpen()
        '    Exit Sub
        'End Try

        TrueItems()

        ImageUpdate()

        IM.Store(TxtPrc.Text.Trim, cmbStores.Text)

        If chkEditAllStores.Checked = False Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'جرد' and itm_id = N'" & TxtPrc.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'جرد' and itm_id = N'" & TxtPrc.Text.Trim & "'" : cmd.ExecuteNonQuery()

        End If

        Bra.Fil("groups", "g_name", cmbcats)
        Cls.fill_combo_Branch("stores", "store", cmbStores)
    End Sub

    Private Sub UpdateItemsALL()
        'connectionStringClose()
        'connectionStringTransaction()
        'Try

        If chkEditFristStore.Checked = True Then
            If chkEditAllStores.Checked = False Then

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set qu =N'" & Val(txtqunt.Text) & "',qu_unity =N'" & Val(txtqunt.Text) & "',CurrentStock =N'" & Val(txtqunt.Text) & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                IM.Store(TxtPrc.Text.Trim, cmbStores.Text)

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'جرد' and itm_id = N'" & TxtPrc.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

            Else

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set qu =N'" & Val(txtqunt.Text) & "',qu_unity =N'" & Val(txtqunt.Text) & "',CurrentStock =N'" & Val(txtqunt.Text) & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                IM.Store(TxtPrc.Text.Trim, cmbStores.Text)

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'جرد' and itm_id = N'" & TxtPrc.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()


            End If

            chkEditFristStore.Checked = False
        Else
            Dim expired As String
            If cmb_Expired.Text = "بدون صلاحية" Then
                expired = ""
            Else
                expired = Cls.C_date(dtpExpiration.Text)
            End If

            Dim QuickSearch As String
            If chkQuickSearch.Checked = True Then
                QuickSearch = "0"
            Else
                QuickSearch = "1"
            End If

            Dim BalanceBarcode As String
            If cmbBalanceBarcode.Text = "بدون ميزان الباركود" Then
                BalanceBarcode = "0"
            Else
                BalanceBarcode = "1"
            End If

            Dim PriceIncludesVAT As Integer
            If chkPriceIncludesVAT.Checked = True Then
                PriceIncludesVAT = 1
            Else
                PriceIncludesVAT = 0
            End If

            Dim TypePlusDiscRateSalPrice, TypePlusDiscRateWholePrice, TypePlusDiscRateWholeWholePrice As Double
            If rdoDiscRateSalPrice.Checked = True Then
                TypePlusDiscRateSalPrice = 0
            Else
                TypePlusDiscRateSalPrice = 1
            End If
            If rdoDiscRateWholePrice.Checked = True Then
                TypePlusDiscRateWholePrice = 0
            Else
                TypePlusDiscRateWholePrice = 1
            End If
            If rdoDiscRateWholeWholePrice.Checked = True Then
                TypePlusDiscRateWholeWholePrice = 0
            Else
                TypePlusDiscRateWholeWholePrice = 1
            End If

            Dim DeferredCurrentDiscount As Double
            If rdoCurrentDiscTinPrice.Checked = True Then
                DeferredCurrentDiscount = 0
            Else
                DeferredCurrentDiscount = 1
            End If

            If txtIDStores.Text = "" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Items set Stores = N'" & cmbStores.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set Stores = N'" & cmbStores.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                txtIDStores.Text = cmbStores.Text.Trim
            End If

            If chkEditAllStores.Checked = False Then


                If chkParcodeChange.Checked = False Then

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Items set itm_id = N'" & TxtPrc.Text.Trim & "',group_name = N'" & cmbcats.Text.Trim & "',group_branch = N'" & cmbGroup_Branch.Text.Trim & "',sname = N'" & cmbitmnm.Text.Trim & "',Unity = N'" & UnityName & "',rng = " & Val(txtrng.Text) & ",tinprice = " & Val(txttinprice.Text) & ",salprice = " & Val(txt_priseSal.Text) & ",WholePrice = " & Val(txtWholePrice.Text) & ",WholeWholePrice = " & Val(txtWholeWholePrice.Text) & ",MinimumSalPrice = " & Val(txtMinimumSalPrice.Text) & ",tin =0,sal =0,btin =0,bsal =0,decayed =0,tinpricetotal =0,salpricetotal =0,btinpricetotal =0,bsalpricetotal =0,decayedpricetotal =0,ValStore =0,store =0,profits =0,UserName=N'" & UserName & "',QuickSearch=N'" & QuickSearch & "',Height =N'" & txtHeight.Text & "',Width =N'" & txtWidth.Text & "',Altitude =N'" & txtAltitude.Text & "',BalanceBarcode = N'" & BalanceBarcode & "',RateWholePrice =N'" & txtRateWholePrice.Text & "',RateWholeWholePrice =N'" & txtRateWholeWholePrice.Text & "',RateMinimumSalPrice =N'" & txtRateMinimumSalPrice.Text & "',RateVAT =N'" & txtRateVAT.Text & "',Vendorname =N'" & cmbvendores.Text.Trim & "',TinPriceAverage =N'" & txtTinPriceAverage.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & ",DiscountedPrice = " & Val(txtDiscountedPrice.Text) & ",DiscountedPrice2 = " & Val(txtDiscountedPrice2.Text) & ",DiscountedPrice3 = " & Val(txtDiscountedPrice3.Text) & ",LimitQuantity = " & Val(txtLimitQuantity.Text) & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update items set RateDiscTinPrice = " & Val(txtRateDiscTinPrice.Text) & ",RateDiscSalPrice = " & Val(txtRateDiscSalPrice.Text) & ",RateDiscWholePrice = " & Val(txtRateDiscWholePrice.Text) & ",RateDiscWholeWholePrice = " & Val(txtRateDiscWholeWholePrice.Text) & ",RateDiscTinPriceAfter = " & Val(txtRateDiscTinPriceAfter.Text) & ",RateDiscSalPriceAfter = " & Val(txtRateDiscSalPriceAfter.Text) & ",RateDiscWholePriceAfter = " & Val(txtRateDiscWholePriceAfter.Text) & ",RateDiscWholeWholePriceAfter = " & Val(txtRateDiscWholeWholePriceAfter.Text) & ",TypePlusDiscRateSalPrice = " & Val(TypePlusDiscRateSalPrice) & ",TypePlusDiscRateWholePrice = " & Val(TypePlusDiscRateWholePrice) & ",TypePlusDiscRateWholeWholePrice = " & Val(TypePlusDiscRateWholeWholePrice) & ",DeferredCurrentDiscount = " & Val(DeferredCurrentDiscount) & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If ConnectOnlineStore = "YES" Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update items set CompaniesID = " & lblCompanyId.Text & ",CompaniesName = N'" & cmbCompanies.Text & "',Tag = " & txtTag.Text & ",Description = N'" & txtDescription.Text & "',LimitQuantity = " & txtLimitQuantity.Text & ",DiscountedPrice = " & Val(txtDiscountedPrice.Text) & ",DiscountedPrice2 = " & Val(txtDiscountedPrice2.Text) & ",DiscountedPrice3 = " & Val(txtDiscountedPrice3.Text) & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()
                    End If

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "',itm_cat =N'" & cmbcats.Text.Trim & "',group_branch =N'" & cmbGroup_Branch.Text.Trim & "',itm_name =N'" & cmbitmnm.Text.Trim & "',itm_Unity =N'" & UnityName & "',price =N'" & txttinprice.Text & "',TinPriceAverage =N'" & txttinprice.Text & "',qu =N'" & Val(txtqunt.Text) & "',totalprice=" & Val(txttinprice.Text) * Val(txtqunt.Text) & ",UserName=N'" & UserName & "',Stores=N'" & cmbStores.Text & "',bill_date=N'" & Cls.C_date(dtpDateItem.Text) & "',bill_EndDate=N'" & expired & "',Expired=N'" & cmb_Expired.Text & "',bill_ProductionDate =N'" & Cls.C_date(dtpProductionDate.Text) & "',Vendorname =N'" & cmbvendores.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set qu_expired =N'" & txtqunt.Text.Trim & "',Treasury_Code =N'" & Treasury_Code & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set Discounts = " & Val(txtRateDiscTinPrice.Text) & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update data_decayed set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update IM_Bsal_Data set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update IM_Btin_Data set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsOffersDiscounts set itm_id = N'" & TxtPrc.Text.Trim & "',sname = N'" & cmbitmnm.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsTransfer set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsTransferData set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and StoresFrom =N'" & cmbStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Receive_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Receive_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsTransfer_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsTransfer_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Manufacturing_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Manufacturing_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ManufacturingProductAdd set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                End If
                If chkParcodeChange.Checked = True Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Items set group_name = N'" & cmbcats.Text.Trim & "',group_branch = N'" & cmbGroup_Branch.Text.Trim & "',itm_id =N'" & TxtPrc.Text.Trim & "',Unity = N'" & UnityName & "',rng = " & Val(txtrng.Text) & ",tinprice = " & Val(txttinprice.Text) & ",salprice = " & Val(txt_priseSal.Text) & ",WholePrice = " & Val(txtWholePrice.Text) & ",WholeWholePrice = " & Val(txtWholeWholePrice.Text) & ",MinimumSalPrice = " & Val(txtMinimumSalPrice.Text) & ",tin =0,sal =0,btin =0,bsal =0,decayed =0,tinpricetotal =0,salpricetotal =0,btinpricetotal =0,bsalpricetotal =0,decayedpricetotal =0,ValStore =0,store =0,profits =0,UserName=N'" & UserName & "',QuickSearch=N'" & QuickSearch & "',Height =N'" & txtHeight.Text & "',Width =N'" & txtWidth.Text & "',Altitude =N'" & txtAltitude.Text & "',RateVAT =N'" & txtRateVAT.Text & "',BalanceBarcode = N'" & BalanceBarcode & "',Vendorname =N'" & cmbvendores.Text.Trim & "',TinPriceAverage =N'" & txtTinPriceAverage.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & ",DiscountedPrice = " & Val(txtDiscountedPrice.Text) & ",DiscountedPrice2 = " & Val(txtDiscountedPrice2.Text) & ",DiscountedPrice3 = " & Val(txtDiscountedPrice3.Text) & ",LimitQuantity = " & Val(txtLimitQuantity.Text) & " where sname= N'" & txtIDSname.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update items set RateDiscTinPrice = " & Val(txtRateDiscTinPrice.Text) & ",RateDiscSalPrice = " & Val(txtRateDiscSalPrice.Text) & ",RateDiscWholePrice = " & Val(txtRateDiscWholePrice.Text) & ",RateDiscWholeWholePrice = " & Val(txtRateDiscWholeWholePrice.Text) & ",RateDiscTinPriceAfter = " & Val(txtRateDiscTinPriceAfter.Text) & ",RateDiscSalPriceAfter = " & Val(txtRateDiscSalPriceAfter.Text) & ",RateDiscWholePriceAfter = " & Val(txtRateDiscWholePriceAfter.Text) & ",RateDiscWholeWholePriceAfter = " & Val(txtRateDiscWholeWholePriceAfter.Text) & ",TypePlusDiscRateSalPrice = " & Val(TypePlusDiscRateSalPrice) & ",TypePlusDiscRateWholePrice = " & Val(TypePlusDiscRateWholePrice) & ",TypePlusDiscRateWholeWholePrice = " & Val(TypePlusDiscRateWholeWholePrice) & ",DeferredCurrentDiscount = " & Val(DeferredCurrentDiscount) & " where sname =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If ConnectOnlineStore = "YES" Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update items set CompaniesID = " & lblCompanyId.Text & ",CompaniesName = N'" & cmbCompanies.Text & "',Tag = " & txtTag.Text & ",Description = N'" & txtDescription.Text & "',LimitQuantity = " & txtLimitQuantity.Text & ",DiscountedPrice = " & Val(txtDiscountedPrice.Text) & ",DiscountedPrice2 = " & Val(txtDiscountedPrice2.Text) & ",DiscountedPrice3 = " & Val(txtDiscountedPrice3.Text) & " where sname =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()
                    End If

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set itm_cat =N'" & cmbcats.Text.Trim & "',group_branch =N'" & cmbGroup_Branch.Text.Trim & "',itm_id =N'" & TxtPrc.Text.Trim & "',itm_Unity =N'" & UnityName & "',price =N'" & txttinprice.Text & "',TinPriceAverage =N'" & txttinprice.Text & "',qu =N'" & Val(txtqunt.Text) & "',totalprice=" & Val(txttinprice.Text) * Val(txtqunt.Text) & ",UserName=N'" & UserName & "',Stores=N'" & cmbStores.Text & "',bill_date=N'" & Cls.C_date(dtpDateItem.Text) & "',bill_EndDate=N'" & expired & "',Expired=N'" & cmb_Expired.Text & "',bill_ProductionDate =N'" & Cls.C_date(dtpProductionDate.Text) & "',Vendorname =N'" & cmbvendores.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name= N'" & txtIDSname.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "' where itm_name= N'" & txtIDSname.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set qu_expired =N'" & txtqunt.Text.Trim & "',Treasury_Code =N'" & Treasury_Code & "' where itm_name =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set Discounts = " & Val(txtRateDiscTinPrice.Text) & " where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BillsalData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name= N'" & txtIDSname.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update data_decayed set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "' where itm_name= N'" & txtIDSname.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update IM_Bsal_Data set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name= N'" & txtIDSname.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update IM_Btin_Data set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name= N'" & txtIDSname.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsOffersDiscounts set sname = N'" & cmbitmnm.Text.Trim & "' where sname= N'" & txtIDSname.Text.Trim & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsTransfer set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "' where itm_name= N'" & txtIDSname.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsTransferData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "' where itm_name= N'" & txtIDSname.Text.Trim & "' and StoresFrom =N'" & cmbStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Receive_BillsalData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name= N'" & txtIDSname.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Receive_BilltINData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name= N'" & txtIDSname.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsTransfer_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id= N'" & txtIDItm.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsTransfer_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id= N'" & txtIDItm.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Manufacturing_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id= N'" & txtIDItm.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Manufacturing_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id= N'" & txtIDItm.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ManufacturingProductAdd set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id= N'" & txtIDItm.Text.Trim & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()
                End If





            Else





                If chkParcodeChange.Checked = False Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Items set itm_id = N'" & TxtPrc.Text.Trim & "',group_name = N'" & cmbcats.Text.Trim & "',group_branch = N'" & cmbGroup_Branch.Text.Trim & "',sname = N'" & cmbitmnm.Text.Trim & "',Unity = N'" & UnityName & "',rng = " & Val(txtrng.Text) & ",tinprice = " & Val(txttinprice.Text) & ",salprice = " & Val(txt_priseSal.Text) & ",WholePrice = " & Val(txtWholePrice.Text) & ",WholeWholePrice = " & Val(txtWholeWholePrice.Text) & ",MinimumSalPrice = " & Val(txtMinimumSalPrice.Text) & ",tin =0,sal =0,btin =0,bsal =0,decayed =0,tinpricetotal =0,salpricetotal =0,btinpricetotal =0,bsalpricetotal =0,decayedpricetotal =0,ValStore =0,store =0,profits =0,UserName=N'" & UserName & "',Stores=N'" & cmbStores.Text & "',QuickSearch=N'" & QuickSearch & "',BalanceBarcode = N'" & BalanceBarcode & "',RateWholePrice =N'" & txtRateWholePrice.Text & "',RateWholeWholePrice =N'" & txtRateWholeWholePrice.Text & "',RateMinimumSalPrice =N'" & txtRateMinimumSalPrice.Text & "',RateVAT =N'" & txtRateVAT.Text & "',Vendorname =N'" & cmbvendores.Text.Trim & "',TinPriceAverage =N'" & txtTinPriceAverage.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & ",DiscountedPrice = " & Val(txtDiscountedPrice.Text) & ",DiscountedPrice2 = " & Val(txtDiscountedPrice2.Text) & ",DiscountedPrice3 = " & Val(txtDiscountedPrice3.Text) & ",LimitQuantity = " & Val(txtLimitQuantity.Text) & " where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update items set RateDiscTinPrice = " & Val(txtRateDiscTinPrice.Text) & ",RateDiscSalPrice = " & Val(txtRateDiscSalPrice.Text) & ",RateDiscWholePrice = " & Val(txtRateDiscWholePrice.Text) & ",RateDiscWholeWholePrice = " & Val(txtRateDiscWholeWholePrice.Text) & ",RateDiscTinPriceAfter = " & Val(txtRateDiscTinPriceAfter.Text) & ",RateDiscSalPriceAfter = " & Val(txtRateDiscSalPriceAfter.Text) & ",RateDiscWholePriceAfter = " & Val(txtRateDiscWholePriceAfter.Text) & ",RateDiscWholeWholePriceAfter = " & Val(txtRateDiscWholeWholePriceAfter.Text) & ",TypePlusDiscRateSalPrice = " & Val(TypePlusDiscRateSalPrice) & ",TypePlusDiscRateWholePrice = " & Val(TypePlusDiscRateWholePrice) & ",TypePlusDiscRateWholeWholePrice = " & Val(TypePlusDiscRateWholeWholePrice) & ",DeferredCurrentDiscount = " & Val(DeferredCurrentDiscount) & " where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                    If ConnectOnlineStore = "YES" Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update items set CompaniesID = " & lblCompanyId.Text & ",CompaniesName = N'" & cmbCompanies.Text & "',Tag = " & txtTag.Text & ",Description = N'" & txtDescription.Text & "',LimitQuantity = " & txtLimitQuantity.Text & ",DiscountedPrice = " & Val(txtDiscountedPrice.Text) & ",DiscountedPrice2 = " & Val(txtDiscountedPrice2.Text) & ",DiscountedPrice3 = " & Val(txtDiscountedPrice3.Text) & " where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()
                    End If

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "',itm_cat =N'" & cmbcats.Text.Trim & "',group_branch =N'" & cmbGroup_Branch.Text.Trim & "',itm_name =N'" & cmbitmnm.Text.Trim & "',itm_Unity =N'" & UnityName & "',price =N'" & txttinprice.Text & "',TinPriceAverage =N'" & txttinprice.Text & "',qu =N'" & Val(txtqunt.Text) & "',totalprice=" & Val(txttinprice.Text) * Val(txtqunt.Text) & ",UserName=N'" & UserName & "',Stores=N'" & cmbStores.Text & "',bill_date=N'" & Cls.C_date(dtpDateItem.Text) & "',bill_EndDate=N'" & expired & "',Expired=N'" & cmb_Expired.Text & "',bill_ProductionDate =N'" & Cls.C_date(dtpProductionDate.Text) & "',Vendorname =N'" & cmbvendores.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set qu_expired =N'" & txtqunt.Text.Trim & "',Treasury_Code =N'" & Treasury_Code & "' where itm_id =N'" & txtIDItm.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set Discounts = " & Val(txtRateDiscTinPrice.Text) & " where itm_id =N'" & txtIDItm.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update data_decayed set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update IM_Bsal_Data set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update IM_Btin_Data set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsOffersDiscounts set itm_id = N'" & TxtPrc.Text.Trim & "',sname = N'" & cmbitmnm.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsTransfer set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsTransferData set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Receive_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Receive_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "',itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsTransfer_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsTransfer_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Manufacturing_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Manufacturing_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ManufacturingProductAdd set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()

                End If
                If chkParcodeChange.Checked = True Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Items set group_name = N'" & cmbcats.Text.Trim & "',group_branch = N'" & cmbGroup_Branch.Text.Trim & "',itm_id =N'" & TxtPrc.Text.Trim & "',Unity = N'" & UnityName & "',rng = " & Val(txtrng.Text) & ",tinprice = " & Val(txttinprice.Text) & ",salprice = " & Val(txt_priseSal.Text) & ",WholePrice = " & Val(txtWholePrice.Text) & ",WholeWholePrice = " & Val(txtWholeWholePrice.Text) & ",MinimumSalPrice = " & Val(txtMinimumSalPrice.Text) & ",tin =0,sal =0,btin =0,bsal =0,decayed =0,tinpricetotal =0,salpricetotal =0,btinpricetotal =0,bsalpricetotal =0,decayedpricetotal =0,ValStore =0,store =0,profits =0,UserName=N'" & UserName & "',Stores=N'" & cmbStores.Text & "',QuickSearch=N'" & QuickSearch & "',RateVAT =N'" & txtRateVAT.Text & "',BalanceBarcode = N'" & BalanceBarcode & "',Vendorname =N'" & cmbvendores.Text.Trim & "',TinPriceAverage =N'" & txtTinPriceAverage.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & ",DiscountedPrice = " & Val(txtDiscountedPrice.Text) & ",DiscountedPrice2 = " & Val(txtDiscountedPrice2.Text) & ",DiscountedPrice3 = " & Val(txtDiscountedPrice3.Text) & ",LimitQuantity = " & Val(txtLimitQuantity.Text) & " where sname= N'" & txtIDSname.Text.Trim & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update items set RateDiscTinPrice = " & Val(txtRateDiscTinPrice.Text) & ",RateDiscSalPrice = " & Val(txtRateDiscSalPrice.Text) & ",RateDiscWholePrice = " & Val(txtRateDiscWholePrice.Text) & ",RateDiscWholeWholePrice = " & Val(txtRateDiscWholeWholePrice.Text) & ",RateDiscTinPriceAfter = " & Val(txtRateDiscTinPriceAfter.Text) & ",RateDiscSalPriceAfter = " & Val(txtRateDiscSalPriceAfter.Text) & ",RateDiscWholePriceAfter = " & Val(txtRateDiscWholePriceAfter.Text) & ",RateDiscWholeWholePriceAfter = " & Val(txtRateDiscWholeWholePriceAfter.Text) & ",TypePlusDiscRateSalPrice = " & Val(TypePlusDiscRateSalPrice) & ",TypePlusDiscRateWholePrice = " & Val(TypePlusDiscRateWholePrice) & ",TypePlusDiscRateWholeWholePrice = " & Val(TypePlusDiscRateWholeWholePrice) & ",DeferredCurrentDiscount = " & Val(DeferredCurrentDiscount) & " where sname =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()

                    If ConnectOnlineStore = "YES" Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update items set CompaniesID = " & lblCompanyId.Text & ",CompaniesName = N'" & cmbCompanies.Text & "',Tag = " & txtTag.Text & ",Description = N'" & txtDescription.Text & "',LimitQuantity = " & txtLimitQuantity.Text & ",DiscountedPrice = " & Val(txtDiscountedPrice.Text) & ",DiscountedPrice2 = " & Val(txtDiscountedPrice2.Text) & ",DiscountedPrice3 = " & Val(txtDiscountedPrice3.Text) & " where sname =N'" & txtIDSname.Text & "'" : cmd.ExecuteNonQuery()
                    End If

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set itm_cat =N'" & cmbcats.Text.Trim & "',group_branch =N'" & cmbGroup_Branch.Text.Trim & "',itm_id =N'" & TxtPrc.Text.Trim & "',itm_Unity =N'" & UnityName & "',price =N'" & txttinprice.Text & "',TinPriceAverage =N'" & txttinprice.Text & "',qu =N'" & Val(txtqunt.Text) & "',totalprice=" & Val(txttinprice.Text) * Val(txtqunt.Text) & ",UserName=N'" & UserName & "',Stores=N'" & cmbStores.Text & "',bill_date=N'" & Cls.C_date(dtpDateItem.Text) & "',bill_EndDate=N'" & expired & "',Expired=N'" & cmb_Expired.Text & "',bill_ProductionDate =N'" & Cls.C_date(dtpProductionDate.Text) & "',Vendorname =N'" & cmbvendores.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name= N'" & txtIDSname.Text.Trim & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "' where itm_name= N'" & txtIDSname.Text.Trim & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set qu_expired =N'" & txtqunt.Text.Trim & "',Treasury_Code =N'" & Treasury_Code & "' where itm_name =N'" & txtIDSname.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set Discounts = " & Val(txtRateDiscTinPrice.Text) & " where itm_id =N'" & txtIDItm.Text & "' and bill_no=N'جرد'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BillsalData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name= N'" & txtIDSname.Text.Trim & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update data_decayed set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text & "' where itm_name= N'" & txtIDSname.Text.Trim & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update IM_Bsal_Data set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name= N'" & txtIDSname.Text.Trim & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update IM_Btin_Data set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name= N'" & txtIDSname.Text.Trim & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsOffersDiscounts set sname = N'" & cmbitmnm.Text.Trim & "' where sname= N'" & txtIDSname.Text.Trim & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsTransfer set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "' where itm_name= N'" & txtIDSname.Text.Trim & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsTransferData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "' where itm_name= N'" & txtIDSname.Text.Trim & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Receive_BillsalData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name= N'" & txtIDSname.Text.Trim & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Receive_BilltINData set itm_name = N'" & cmbitmnm.Text.Trim & "',itm_cat = N'" & cmbcats.Text.Trim & "',PriceIncludesVAT = " & PriceIncludesVAT & " where itm_name= N'" & txtIDSname.Text.Trim & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsTransfer_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id= N'" & txtIDItm.Text.Trim & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsTransfer_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id= N'" & txtIDItm.Text.Trim & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Manufacturing_BillsalData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id= N'" & txtIDItm.Text.Trim & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Manufacturing_BilltINData set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id= N'" & txtIDItm.Text.Trim & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ManufacturingProductAdd set itm_id = N'" & TxtPrc.Text.Trim & "' where itm_id= N'" & txtIDItm.Text.Trim & "'" : cmd.ExecuteNonQuery()
                End If

            End If


        End If

        '    trans.Commit()
        '    connectionStringOpen()
        'Catch ex As Exception
        '    Try : trans.Rollback() : Catch exRollback As Exception : End Try
        '    ErrorHandling(ex, Me.Text)
        '    Cn.Close()
        '    connectionStringOpen()
        '    Exit Sub
        'End Try


        ImageUpdate()

        IM.Store(TxtPrc.Text.Trim, cmbStores.Text)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'جرد' and itm_id = N'" & TxtPrc.Text.Trim & "' and Stores =N'" & cmbStores.Text & "'" : cmd.ExecuteNonQuery()

        Bra.Fil("groups", "g_name", cmbcats)
        Cls.fill_combo_Branch("stores", "store", cmbStores)
    End Sub

    Sub AddStores()
        Try
            REM للتاكد من عدم التكرار
            Dim TableName, FieldName, StringFind As String
            Dim S As String
            TableName = "stores"
            FieldName = "store"
            StringFind = cmbStores.Text
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "Select * From " & TableName & " Where " & FieldName & "=N'" & StringFind & "'"

            cmd.CommandType = CommandType.Text
            cmd.CommandText = S
            dr = cmd.ExecuteReader
            'dr.Read()
            If dr.HasRows = False Then
                Cls.Get_Value_Count_More("Stores", "StausMainStore =N'0'")
                Dim StausMainStore As Integer
                If H = 0 Then
                    StausMainStore = 0
                Else
                    StausMainStore = 1
                End If

                REM لحفظ المخزن
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Stores(Company_Branch_ID,store,UserName,StausMainStore) values (N'" & Company_Branch_ID & "',N'" & StringFind & "',N'" & UserName & "',N'" & StausMainStore & "')"
                cmd.CommandText = S
                cmd.ExecuteNonQuery()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
    Sub AddGroubs()
        Try
            REM للتاكد من عدم التكرار
            Dim TableName, FieldName, StringFind As String
            Dim S As String
            TableName = "groups"
            FieldName = "g_name"
            StringFind = cmbcats.Text
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "Select * From " & TableName & " Where " & FieldName & "=N'" & StringFind & "'"
            cmd.CommandType = CommandType.Text
            cmd.CommandText = S
            dr = cmd.ExecuteReader
            'dr.Read()
            If dr.HasRows = False Then
                REM لحفظ المجموعه
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into groups(Company_Branch_ID,g_name,UserName) values (N'" & Company_Branch_ID & "',N'" & StringFind & "',N'" & UserName & "')"
                cmd.CommandText = S
                cmd.ExecuteNonQuery()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Sub AddGroup_Branch()
        Try
            REM للتاكد من عدم التكرار
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "Select * From Group_Branch Where branch_Name=N'" & cmbGroup_Branch.Text & "'"
            cmd.CommandType = CommandType.Text
            cmd.CommandText = S
            dr = cmd.ExecuteReader
            'dr.Read()
            If dr.HasRows = False Then
                Dim Groups_ID As String = Cls.Get_Code_Value_Branch("Groups", "id", "G_name", cmbcats.Text)

                MAXRECORD_Group_Branch()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Group_Branch (Company_Branch_ID,branch_ID,Group_Name_ID,branch_Name,UserName)"
                S = S & " values (N'" & Company_Branch_ID & "',N'" & branch_ID & "',N'" & Groups_ID & "',N'" & cmbGroup_Branch.Text.Trim & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub MAXRECORD_Group_Branch()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Group_Branch"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            branch_ID = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(branch_ID As float)) as mb FROM Group_Branch where branch_ID <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            branch_ID = sh + 1
        End If

    End Sub


    Function SumListCombo(ByVal X As ListBox)
        Dim SM As Double
        For i As Integer = 0 To X.Items.Count - 1
            SM = SM + Val(X.Items(i))
        Next
        Return SM
    End Function


    Function ValidatAdd() As Boolean
        If cmbcats.Text.Trim = "" Then MsgBox("أختر مجموعة الصنف", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbitmnm.Text.Trim = "" Then MsgBox("أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbitmnm.Focus() : Return False

        Return True
    End Function

    Private Sub txtsearsh_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles txtsearsh.MouseClick
        If txtsearsh.Text = " بحث ...." Then txtsearsh.SelectAll() : txtsearsh.Font = New Font(txtsearsh.Font, FontStyle.Regular)
    End Sub

    Private Sub TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtsearsh.TextChanged
        'Try
        If txtsearsh.Text = "'" Then : txtsearsh.Text = "" : End If
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If LanguageMainProgram = "العربية" Then
            If ActivateFormatNumberWithSeparators = "YES" Then
                If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
                    S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],CAST(TinPriceAverage AS NVARCHAR(50)) as [متوسط سعر الشراء],CAST(SalPrice AS NVARCHAR(50)) as [سعر التجزئة],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة],CAST(MinimumSalPrice AS NVARCHAR(50)) as [حد ادنى للسعر],rng as [حد الطلب] ,CAST(store AS NVARCHAR(50)) as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id<>''")
                Else
                    S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],CAST(TinPriceAverage AS NVARCHAR(50)) as [متوسط سعر الشراء],CAST(SalPrice AS NVARCHAR(50)) as [سعر التجزئة],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة],CAST(MinimumSalPrice AS NVARCHAR(50)) as [حد ادنى للسعر],rng as [حد الطلب] ,CAST(store AS NVARCHAR(50)) as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "sname Like N'%" & txtsearsh.Text & "%'")
                End If
            Else
                If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
                    S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],WholeWholePrice as [سعر جملة الجملة],MinimumSalPrice as [حد ادنى للسعر],rng as [حد الطلب] ,store as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id<>''")
                Else
                    S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],WholeWholePrice as [سعر جملة الجملة],MinimumSalPrice as [حد ادنى للسعر],rng as [حد الطلب] ,store as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "sname Like N'%" & txtsearsh.Text & "%'")
                End If
            End If
            If FilterSelect = "Number" Then
                S = S & " order by [الباركود]"
            End If
            If FilterSelect = "Date" Then
                S = S & " order by [المجموعة]"
            End If
            If FilterSelect = "Name" Then
                S = S & " order by [الصنف]"
            End If
        ElseIf LanguageMainProgram = "English" Then
            If ActivateFormatNumberWithSeparators = "YES" Then
                If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
                    S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Group], group_branch as [Subgroup], sname as [Item], CAST(TinPrice AS NVARCHAR(50)) as [Purchase Price], CAST(TinPriceAverage AS NVARCHAR(50)) as [Avg Purchase Price], CAST(SalPrice AS NVARCHAR(50)) as [Retail Price], CAST(WholePrice AS NVARCHAR(50)) as [Wholesale Price], CAST(WholeWholePrice AS NVARCHAR(50)) as [Bulk Price], CAST(MinimumSalPrice AS NVARCHAR(50)) as [Min Price], rng as [Reorder Level], CAST(store AS NVARCHAR(50)) as [Stock], Stores as [Warehouse], RateVAT as [VAT Rate]", "items", "id<>''")
                Else
                    S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Group], group_branch as [Subgroup], sname as [Item], CAST(TinPrice AS NVARCHAR(50)) as [Purchase Price], CAST(TinPriceAverage AS NVARCHAR(50)) as [Avg Purchase Price], CAST(SalPrice AS NVARCHAR(50)) as [Retail Price], CAST(WholePrice AS NVARCHAR(50)) as [Wholesale Price], CAST(WholeWholePrice AS NVARCHAR(50)) as [Bulk Price], CAST(MinimumSalPrice AS NVARCHAR(50)) as [Min Price], rng as [Reorder Level], CAST(store AS NVARCHAR(50)) as [Stock], Stores as [Warehouse], RateVAT as [VAT Rate]", "items", "sname Like N'%" & txtsearsh.Text & "%'")
                End If
            Else
                If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
                    S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Group], group_branch as [Subgroup], sname as [Item], TinPrice as [Purchase Price], TinPriceAverage as [Avg Purchase Price], SalPrice as [Retail Price], WholePrice as [Wholesale Price], WholeWholePrice as [Bulk Price], MinimumSalPrice as [Min Price], rng as [Reorder Level], store as [Stock], Stores as [Warehouse], RateVAT as [VAT Rate]", "items", "id<>''")
                Else
                    S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Group], group_branch as [Subgroup], sname as [Item], TinPrice as [Purchase Price], TinPriceAverage as [Avg Purchase Price], SalPrice as [Retail Price], WholePrice as [Wholesale Price], WholeWholePrice as [Bulk Price], MinimumSalPrice as [Min Price], rng as [Reorder Level], store as [Stock], Stores as [Warehouse], RateVAT as [VAT Rate]", "items", "sname Like N'%" & txtsearsh.Text & "%'")
                End If
            End If

            If FilterSelect = "Number" Then
                S = S & " order by [Barcode]"
            End If
            If FilterSelect = "Date" Then
                S = S & " order by [Group]"
            End If
            If FilterSelect = "Name" Then
                S = S & " order by [Item]"
            End If
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)


            Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
            If ShowGroupBranch = "NO" Then
                DTGV.Columns(2).Visible = False
            Else
                DTGV.Columns(2).Visible = True
            End If

            Dim HideWholeWholeSalePrice As String = mykey.GetValue("HideWholeWholeSalePrice", "NO")
            If HideWholeWholeSalePrice = "NO" Then
                DTGV.Columns(7).Visible = True
                DTGV.Columns(8).Visible = True
            Else
                DTGV.Columns(7).Visible = False
                DTGV.Columns(8).Visible = False
            End If
            If ShowValueVAT = "NO" Then
                DTGV.Columns(13).Visible = False
            End If

            Dim SM As Double
            For i As Integer = 0 To DTGV.Rows.Count - 1
                If LastTinPriceItems = "YES" Then
                    SM = SM + Val(DTGV.Rows(i).Cells(4).Value.ToString) * Val(DTGV.Rows(i).Cells(11).Value)
                Else
                    SM = SM + Val(DTGV.Rows(i).Cells(5).Value.ToString) * Val(DTGV.Rows(i).Cells(11).Value)
                End If
            Next
            txt_Total.Text = FormatNumberWithSeparators(SM)

            Dim SM1 As Double
            For i As Integer = 0 To DTGV.Rows.Count - 1
                SM1 = SM1 + Val(DTGV.Rows(i).Cells(11).Value.ToString)
            Next
            txtTotalQunt.Text = SM1

            txtNumberItems.Text = DTGV.RowCount

            GetGridViewFormatNumberWithSeparators()

        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub

    Private Sub txtqunt_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtqunt.KeyUp
        If e.KeyCode = 13 Then
            txttinprice.Focus()
            txttinprice.SelectAll()
        End If
    End Sub

    Private Sub txtqunt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtqunt.TextChanged
        If ActionNumericSeparators = False Then
            If ActivateFormatNumberWithSeparators = "YES" Then
                txtqunt.Text = FormatNumberWithSeparators(Val(txtqunt.Text))
            End If
        End If
        If Not IsNumeric(txtqunt.Text) Then
            System.Windows.Forms.SendKeys.Send("{backspace}")
        End If
    End Sub

    Private Sub txttinprice_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txttinprice.KeyUp

        If e.KeyCode = 13 Then
            If ActionNumericSeparators = False Then
                txttinprice.Text = FormatNumberWithSeparators(txttinprice.Text)
            End If
            txt_priseSal.Focus()
            txt_priseSal.SelectAll()
        End If
    End Sub

    Private Sub txttinprice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txttinprice.TextChanged

        MyVars.CheckNumber(txttinprice)
        'GetRateDiscPriceAfter()
        If NotUnityItemsProgram = "YES" Then
            If txttinprice.Text <> "" Then
                For i As Integer = 0 To dgvUnityItems.Rows.Count - 1
                    If dgvUnityItems.Rows(i).Cells(6).Value = "الاصغر" Then
                        Dim split As String() = New String() {"."}
                        Dim SplitNull As String = ""
                        Dim itemsSplit As String() = txttinprice.Text.Split(split, StringSplitOptions.None)
                        If itemsSplit.Length <> 1 Then
                            SplitNull = itemsSplit(1).ToString()
                        Else
                            dgvUnityItems.Rows(i).Cells(2).Value = txttinprice.Text
                        End If
                        If SplitNull <> "" Then
                            dgvUnityItems.Rows(i).Cells(2).Value = txttinprice.Text
                        End If
                    End If
                Next
            End If
        End If

    End Sub

    Private Sub txtrng_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtrng.KeyUp
        Try
            If e.KeyCode = 13 Then
                Dim HideWholeWholeSalePrice As String = mykey.GetValue("HideWholeWholeSalePrice", "NO")
                If HideWholeWholeSalePrice = "NO" Then
                    TxtPrc.Focus()
                    'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    'cmd.CommandText = "select count(*) from items where group_name =N'" & cmbcats.Text.Trim & "'" : H = cmd.ExecuteScalar
                    'Dim X As String
                    'Dim XH As String
                    'XH = H
                    'If Len(XH) = 1 Then
                    '    X = "0" & H
                    'ElseIf Len(XH) = 2 Then
                    '    X = H
                    'End If

                    'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    'cmd.CommandText = "select id from Groups where G_name =N'" & cmbcats.Text.Trim & "'"
                    'dr = cmd.ExecuteReader : dr.Read()

                    'If Len(txttinprice.Text) = 1 Then
                    '    TxtPrc.Text = dr(0) & "0" & Int(txttinprice.Text.Trim) & X
                    'ElseIf Len(txttinprice.Text) = 2 Then
                    '    TxtPrc.Text = dr(0) & Int(txttinprice.Text.Trim) & X
                    'ElseIf Len(txttinprice.Text) = 3 Then
                    '    TxtPrc.Text = dr(0) & "00" & X
                    'ElseIf Len(txttinprice.Text) = 4 Then
                    '    TxtPrc.Text = dr(0) & "0" & Int(txttinprice.Text.Trim) & X
                    'End If

                    TxtPrc.SelectAll()

                Else
                    btnsave.PerformClick()
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub Deletex()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DTGV.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If

        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.SelectedRows.Count - 1
            If DTGV.RowCount = 0 Then Beep() : Exit Sub
            If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub

            txtIDItm.Text = DTGV.SelectedRows(i).Cells(0).Value
            txtIDStores.Text = DTGV.SelectedRows(i).Cells(12).Value

            '=========================================== Archive Manager ============================================================
            archiveManager.ArchiveAndDeleteItems("Delete", txtIDItm.Text, txtIDStores.Text, UserName, "تم حذف الصنف لعدم النشاط")
            archiveManager.ArchiveAndDeleteBilltINData("Delete", "جرد", txtIDItm.Text, txtIDStores.Text, UserName, "تم حذف الصنف لعدم النشاط")
            'archiveManager.ArchiveAndDeleteItemsUnity("Delete", txtIDItm.Text, txtIDStores.Text, UserName, "تم حذف الوحدة لعدم النشاط")
            '=========================================== Archive Manager ============================================================

            If ConnectOnlineStore = "YES" Then
                Cos.DeleteProduct(txtIDItm.Text)
            End If

            AddNewItemsDeleted(txtIDItm.Text, txtIDStores.Text)

            Try
                Dim XDate As String = ""
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select bill_date from BilltINData where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no =N'جرد'" : dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    XDate = dr("bill_date")
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete from Items where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete from BilltINData where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no =N'جرد'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete from ItemsUnity where itm_id =N'" & txtIDItm.Text & "'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete from ItemsTransferData where itm_id =N'" & txtIDItm.Text & "' and StoresTO =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete from ItemsTransfer where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete from ItemsTransfer_BillsalData where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete from ItemsTransfer_BilltINData where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete from Manufacturing_BillsalData where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete from Manufacturing_BilltINData where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'" : cmd.ExecuteNonQuery()

                Get_Movement_In_Out_Money(XDate, Treasury_Code)
            Catch ex As Exception
                ErrorHandling(ex, Me.Text)
            End Try
        Next
        Headerx()
        'S = "update BilltINData set qu = " & Totalqu & " where Stores =N'" & cmbStores.Text & "' and itm_name=N'" & cmbitmnm.Text & "'"
        'cmd.CommandText = S : cmd.ExecuteNonQuery()
        'IM.Store(DTGV.SelectedRows(0).Cells(0).Value)

    End Sub

    Private Sub txtrng_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtrng.TextChanged
        MyVars.CheckNumber(txtrng)
    End Sub

    Private Sub cmbcats_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbcats.SelectedIndexChanged
        If cmbitmnm.Text = "" Then
            Try
                If EditActive = False Then
                    Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
                    If ShowGroupBranch = "NO" Then
                        If cmbcats.Text.Trim = "" Then Exit Sub
                        Cls.fill_combo_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbitmnm)
                        cmbitmnm.Text = ""
                    End If

                    If ShowGroupBranch = "YES" Then
                        cmbGroup_Branch.Items.Clear()
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()

                        cmd.CommandText = "SELECT dbo.Groups.G_name, dbo.Group_Branch.branch_Name FROM dbo.Groups INNER JOIN  dbo.Group_Branch ON dbo.Groups.id = dbo.Group_Branch.Group_Name_ID WHERE     (dbo.Groups.G_name =N'" & cmbcats.Text & "')  order by 1"
                        dr = cmd.ExecuteReader
                        Do While dr.Read
                            cmbGroup_Branch.Items.Add(Trim(dr(1)))
                        Loop
                        cmbGroup_Branch.Text = ""
                    End If

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select RateDiscTinPrice,RateDiscSalPrice,RateDiscWholePrice,RateDiscWholeWholePrice,TypePlusDiscRateSalPrice,TypePlusDiscRateWholePrice,TypePlusDiscRateWholeWholePrice from Groups where G_name=N'" & cmbcats.Text & "'"
                    dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then
                        txtRateDiscTinPrice.Text = dr(0).ToString
                        txtRateDiscSalPrice.Text = dr(1).ToString
                        txtRateDiscWholePrice.Text = dr(2).ToString
                        txtRateDiscWholeWholePrice.Text = dr(3).ToString
                        If dr(4).ToString = "0" Then
                            rdoDiscRateSalPrice.Checked = True
                        Else
                            rdoPlusRateSalPrice.Checked = True
                        End If
                        If dr(5).ToString = "0" Then
                            rdoDiscRateWholePrice.Checked = True
                        Else
                            rdoPlusRateWholePrice.Checked = True
                        End If
                        If dr(6).ToString = "0" Then
                            rdoDiscRateWholeWholePrice.Checked = True
                        Else
                            rdoPlusRateWholeWholePrice.Checked = True
                        End If
                        GroupsRate = True
                    Else
                        txtRateDiscTinPrice.Text = "0"
                        txtRateDiscSalPrice.Text = "0"
                        txtRateDiscWholePrice.Text = "0"
                        txtRateDiscWholeWholePrice.Text = "0"
                    End If
                End If

            Catch ex As Exception
                ErrorHandling(ex, Me.Text)
            End Try
        End If
    End Sub

    Private Sub BtnAddCat_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAddCat.Click
        FrmCats.Show()
    End Sub

    Private Sub DTGV_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles DTGV.DoubleClick
        SubGetData()
    End Sub

    Private Sub SubGetData()
        EditActive = True
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        txtIDItm.Text = DTGV.SelectedRows(0).Cells(0).Value
        TxtPrc.Text = DTGV.SelectedRows(0).Cells(0).Value
        txtIDStores.Text = DTGV.SelectedRows(0).Cells(12).Value
        txtIDSname.Text = DTGV.SelectedRows(0).Cells(3).Value

        TrueItems()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If txtIDItm.Text = "" Then
                cmd.CommandText = "select * from BilltINData where itm_name =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no <> N'جرد'"
            Else
                cmd.CommandText = "select * from BilltINData where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no <> N'جرد'"
            End If
            H = cmd.ExecuteScalar
            If H > 0 Then
                'MsgBox("لا يمكنك التعديل في مخزون هذا الصنف حيث انك قد أجريت عملية شراء عليه", MsgBoxStyle.Exclamation)
                falesItems()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If txtIDItm.Text = "" Then
                cmd.CommandText = "select * from BillsalData where itm_name =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no <> N'جرد'"
            Else
                cmd.CommandText = "select * from BillsalData where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no <> N'جرد'"
            End If
            H = cmd.ExecuteScalar
            If H > 0 Then
                'MsgBox("لا يمكنك التعديل في مخزون هذا الصنف حيث انك قد أجريت عملية بيع عليه", MsgBoxStyle.Exclamation)
                falesItems()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        'Try
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If txtIDItm.Text = "" Then
            cmd.CommandText = "select * from IM_Btin_Data where itm_name =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no <> N'جرد'"
        Else
            cmd.CommandText = "select * from IM_Btin_Data where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no <> N'جرد'"
        End If
        H = cmd.ExecuteScalar
        If H > 0 Then
            'MsgBox("لا يمكنك التعديل في مخزون هذا الصنف حيث انك قد أجريت عملية مرتجع شراء عليه", MsgBoxStyle.Exclamation)
            falesItems()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If txtIDItm.Text = "" Then
            cmd.CommandText = "select * from IM_Bsal_Data where itm_name = N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no <> N'جرد'"
        Else
            cmd.CommandText = "select * from IM_Bsal_Data where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no <> N'جرد'"
        End If
        H = cmd.ExecuteScalar
        If H > 0 Then
            'MsgBox("لا يمكنك التعديل في مخزون هذا الصنف حيث انك قد أجريت عملية  مرتجع بيع عليه", MsgBoxStyle.Exclamation)
            falesItems()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from ManufacturingProductAdd where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'"
        H = cmd.ExecuteScalar
        If H > 0 Then
            'MsgBox("لا يمكنك التعديل في مخزون هذا الصنف حيث انك قد أجريت عملية  مرتجع بيع عليه", MsgBoxStyle.Exclamation)
            falesItems()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If txtIDItm.Text = "" Then
            txtIDItm.Text = Cls.Get_Code_Value_More("Items", "itm_id", "sname =N'" & txtIDSname.Text & "'")
            cmd.CommandText = "select * from ItemsTransfer_BillsalData where itm_id = N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'"
        Else
            cmd.CommandText = "select * from ItemsTransfer_BillsalData where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'"
        End If
        H = cmd.ExecuteScalar
        If H > 0 Then
            falesItems()
        End If


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If txtIDItm.Text = "" Then
            txtIDItm.Text = Cls.Get_Code_Value_More("Items", "itm_id", "sname =N'" & txtIDSname.Text & "'")
            cmd.CommandText = "select * from ItemsTransfer_BilltINData where itm_id = N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'"
        Else
            cmd.CommandText = "select * from ItemsTransfer_BilltINData where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'"
        End If
        H = cmd.ExecuteScalar
        If H > 0 Then
            falesItems()
        End If



        Dim BalanceBarcode As String = ""
        Dim QuickSearch As String = ""
        Dim PriceIncludesVAT As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If txtIDItm.Text = "" Then
            TxtPrc.Text = ""
            cmd.CommandText = "select  group_name,group_branch,sname,Unity,rng,store,itm_id,TinPrice,SalPrice,WholePrice,WholeWholePrice,MinimumSalPrice,Stores,QuickSearch,Height,Width,Altitude,Density,BalanceBarcode,RateWholePrice,RateWholeWholePrice,RateMinimumSalPrice,RateVAT,Vendorname,TinPriceAverage,RateDiscTinPrice,RateDiscSalPrice,RateDiscWholePrice,RateDiscWholeWholePrice,PriceIncludesVAT,RateDiscTinPriceAfter,RateDiscSalPriceAfter,RateDiscWholePriceAfter,RateDiscWholeWholePriceAfter,TypePlusDiscRateSalPrice,TypePlusDiscRateWholePrice,TypePlusDiscRateWholeWholePrice,DeferredCurrentDiscount from items where sname =N'" & txtIDSname.Text & "' and Stores=N'" & txtIDStores.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        Else
            cmd.CommandText = "select  group_name,group_branch,sname,Unity,rng,store,itm_id,TinPrice,SalPrice,WholePrice,WholeWholePrice,MinimumSalPrice,Stores,QuickSearch,Height,Width,Altitude,Density,BalanceBarcode,RateWholePrice,RateWholeWholePrice,RateMinimumSalPrice,RateVAT,Vendorname,TinPriceAverage,RateDiscTinPrice,RateDiscSalPrice,RateDiscWholePrice,RateDiscWholeWholePrice,PriceIncludesVAT,RateDiscTinPriceAfter,RateDiscSalPriceAfter,RateDiscWholePriceAfter,RateDiscWholeWholePriceAfter,TypePlusDiscRateSalPrice,TypePlusDiscRateWholePrice,TypePlusDiscRateWholeWholePrice,DeferredCurrentDiscount from items where itm_id =N'" & txtIDItm.Text & "' and Stores=N'" & txtIDStores.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        End If
        If dr.HasRows = True Then
            cmbcats.Text = dr("group_name").ToString
            cmbGroup_Branch.Text = dr("group_branch").ToString
            cmbitmnm.Text = dr("sname").ToString
            cmbUnity.Text = dr("Unity").ToString
            txtrng.Text = dr("rng").ToString
            txtqunt.Text = dr("store").ToString
            TxtPrc.Text = dr("itm_id").ToString
            itmprc = dr("itm_id").ToString
            txttinprice.Text = dr("TinPrice").ToString
            txt_priseSal.Text = dr("SalPrice").ToString
            txtWholePrice.Text = dr("WholePrice").ToString
            txtWholeWholePrice.Text = dr("WholeWholePrice").ToString
            txtMinimumSalPrice.Text = dr("MinimumSalPrice").ToString
            cmbStores.Text = dr("Stores").ToString
            QuickSearch = dr("QuickSearch").ToString
            txtHeight.Text = dr("Height").ToString
            txtWidth.Text = dr("Width").ToString
            txtAltitude.Text = dr("Altitude").ToString
            txtDensity.Text = dr("Density").ToString
            BalanceBarcode = dr("BalanceBarcode").ToString
            txtRateWholePrice.Text = dr("RateWholePrice").ToString
            txtRateWholeWholePrice.Text = dr("RateWholeWholePrice").ToString
            txtRateMinimumSalPrice.Text = dr("RateMinimumSalPrice").ToString
            txtRateVAT.Text = dr("RateVAT").ToString
            cmbvendores.Text = dr("Vendorname").ToString
            txtTinPriceAverage.Text = dr("TinPriceAverage").ToString
            txtRateDiscTinPrice.Text = dr("RateDiscTinPrice").ToString
            txtRateDiscSalPrice.Text = dr("RateDiscSalPrice").ToString
            txtRateDiscWholePrice.Text = dr("RateDiscWholePrice").ToString
            txtRateDiscWholeWholePrice.Text = dr("RateDiscWholeWholePrice").ToString
            PriceIncludesVAT = dr("PriceIncludesVAT").ToString
            txtRateDiscTinPriceAfter.Text = dr("RateDiscTinPriceAfter").ToString
            txtRateDiscSalPriceAfter.Text = dr("RateDiscSalPriceAfter").ToString
            txtRateDiscWholePriceAfter.Text = dr("RateDiscWholePriceAfter").ToString
            txtRateDiscWholeWholePriceAfter.Text = dr("RateDiscWholeWholePriceAfter").ToString
            Dim TypePlusDiscRateSalPrice As String = dr("TypePlusDiscRateSalPrice").ToString
            Dim TypePlusDiscRateWholePrice As String = dr("TypePlusDiscRateWholePrice").ToString
            Dim TypePlusDiscRateWholeWholePrice As String = dr("TypePlusDiscRateWholeWholePrice").ToString
            Dim DeferredCurrentDiscount As String = dr("DeferredCurrentDiscount").ToString

            If TypePlusDiscRateSalPrice = "0" Then
                rdoDiscRateSalPrice.Checked = True
            Else
                rdoPlusRateSalPrice.Checked = True
            End If

            If TypePlusDiscRateWholePrice = "0" Then
                rdoDiscRateWholePrice.Checked = True
            Else
                rdoPlusRateWholePrice.Checked = True
            End If

            If TypePlusDiscRateWholeWholePrice = "0" Then
                rdoDiscRateWholeWholePrice.Checked = True
            Else
                rdoPlusRateWholeWholePrice.Checked = True
            End If

            If DeferredCurrentDiscount = "0" Then
                rdoCurrentDiscTinPrice.Checked = True
            Else
                rdoDeferredDiscTinPrice.Checked = True
            End If
        End If


        If ConnectOnlineStore = "YES" Then
            If Not ConnectingOnlineStore() Is Nothing Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select CompanyId,Tag,Description,LimitQuantity,DiscountedPrice,DiscountedPrice2,DiscountedPrice3 from Product where Code =N'" & TxtPrc.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    lblCompanyId.Text = dr(0).ToString
                    txtTag.Text = dr(1).ToString
                    txtDescription.Text = dr(2).ToString
                    txtLimitQuantity.Text = dr(3).ToString
                    txtDiscountedPrice.Text = dr(4).ToString
                    txtDiscountedPrice2.Text = dr(5).ToString
                    txtDiscountedPrice3.Text = dr(6).ToString
                End If
                cmbCompanies.Text = Cls.Get_Code_Value_More("Company", "Name", "Id =N'" & lblCompanyId.Text & "'")
            End If
            Cn.Close()
            connect()
        End If

        If ConnectOnlineStore = "YES" Then
            txtStockOnline.Text = Cos.GetProductValue("Product", "Stock", "Code", TxtPrc.Text)
        End If

        If BalanceBarcode = "0" Then
            cmbBalanceBarcode.Text = "بدون ميزان الباركود"
        Else
            cmbBalanceBarcode.Text = "ميزان الباركود"
        End If

        Dim bill_EndDate As String = "" : Dim Expired As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If txtIDItm.Text = "" Then
            cmd.CommandText = "select  bill_EndDate,Expired,bill_ProductionDate,Vendorname from BilltINData where itm_name =N'" & txtIDSname.Text & "' and Stores=N'" & txtIDStores.Text & "' and bill_no = N'جرد'" : dr = cmd.ExecuteReader : dr.Read()
        Else
            cmd.CommandText = "select  bill_EndDate,Expired,bill_ProductionDate,Vendorname from BilltINData where itm_id =N'" & txtIDItm.Text & "' and Stores=N'" & txtIDStores.Text & "' and bill_no = N'جرد'" : dr = cmd.ExecuteReader : dr.Read()
        End If
        If dr.HasRows = True Then
            If dr(0) Is DBNull.Value Then
            Else
                bill_EndDate = dr(0)
            End If
            If dr(1) Is DBNull.Value Then
            Else
                cmb_Expired.Text = dr(1)
            End If
            If dr(2) Is DBNull.Value Then
            Else
                dtpDateItem.Text = Cls.R_date(dr(2).ToString)
            End If
            If dr(3) Is DBNull.Value Then
            Else
                cmbvendores.Text = dr(3).ToString
            End If
            If bill_EndDate <> "" Then
                dtpExpiration.Text = Cls.R_date(bill_EndDate)
            Else
                dtpExpiration.Text = ""
            End If
        Else
            cmb_Expired.Text = "بدون صلاحية"
        End If

        If QuickSearch = "0" Then
            chkQuickSearch.Checked = True
        Else
            chkQuickSearch.Checked = False
        End If

        If PriceIncludesVAT = "1" Then
            chkPriceIncludesVAT.Checked = True
        Else
            chkPriceIncludesVAT.Checked = False
        End If

        If Expired <> "بدون صلاحية" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()

            If txtIDItm.Text = "" Then
                cmd.CommandText = "select bill_date from BilltINData where bill_date <> N'جرد' and itm_name =N'" & txtIDSname.Text & "' and Stores =N'" & txtIDStores.Text & "'"
            Else
                cmd.CommandText = "select bill_date from BilltINData where bill_date <> N'جرد' and itm_id =N'" & TxtPrc.Text & "' and Stores =N'" & txtIDStores.Text & "'"
            End If
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Dim Xbill_date As String = ""
                If dr("bill_date") Is DBNull.Value Then
                Else
                    Xbill_date = dr("bill_date")
                End If
                dtpDateItem.Text = Cls.R_date(Xbill_date)
            End If
        End If

        SHOWPHOTO()

        If BarcodeMore = "YES" Then
            Dt_AddBill_BarcodeMore.Rows.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "select itm_id_More from BarcodeMore where itm_id =N'" & TxtPrc.Text & "'" : cmd.CommandText = S : dr = cmd.ExecuteReader
            Do While dr.Read
                dgvBarcodeMore.DataSource = Fn_AddBillBarcodeMore(dr(0).ToString)
            Loop
        End If

        GetAddItemsUnity()

        GetDataItemsAlternative()

        If chkEditFristStore.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If txtIDItm.Text = "" Then
                cmd.CommandText = "select  qu from BilltINData where itm_name =N'" & txtIDSname.Text & "' and Stores=N'" & txtIDStores.Text & "' and bill_no = N'جرد'" : dr = cmd.ExecuteReader : dr.Read()
            Else
                cmd.CommandText = "select  qu from BilltINData where itm_id =N'" & txtIDItm.Text & "' and Stores=N'" & txtIDStores.Text & "' and bill_no = N'جرد'" : dr = cmd.ExecuteReader : dr.Read()
            End If
            If dr.HasRows = True Then
                txtqunt.Text = dr(0).ToString
                txtqunt.Enabled = True
            End If
        End If

        SetFormatNumberValueSeparators()

        EditActive = False

        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub

    Private Sub Button2_Click_1(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Shell("calc.exe", vbNormalFocus)
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        TxtPrc.Text = vb.Right(Clss.GenerateItmId_Or_Parcode(), 6)
    End Sub

    Private Sub cmbitmnm_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbitmnm.KeyUp
        If e.KeyCode = 13 Then
            txtqunt.Focus()
        End If
    End Sub

    Private Sub TxtPrc_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles TxtPrc.KeyUp
        If e.KeyCode = 13 Then
            If TxtPrc.Text.Trim = "" Then
                Button1.PerformClick()
            Else
                btnsave.PerformClick()
            End If
        End If
    End Sub

    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        Deletex()
    End Sub

    Private Sub txt_priseSal_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txt_priseSal.KeyUp
        If e.KeyCode = 13 Then
            If ActionNumericSeparators = False Then
                txt_priseSal.Text = FormatNumberWithSeparators(txt_priseSal.Text)
            End If

            Dim HideWholeWholeSalePrice As String = mykey.GetValue("HideWholeWholeSalePrice", "NO")
            If HideWholeWholeSalePrice = "NO" Then
                txtWholePrice.Focus()
                txtWholePrice.SelectAll()
            Else
                txtMinimumSalPrice.Focus()
                txtMinimumSalPrice.SelectAll()
            End If
        End If
    End Sub

    Private Sub txt_priseSal_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txt_priseSal.TextChanged

        MyVars.CheckNumber(txt_priseSal)
        GetRateDiscPriceAfter()

        If NotUnityItemsProgram = "YES" Then
            If txt_priseSal.Text <> "" Then
                For i As Integer = 0 To dgvUnityItems.Rows.Count - 1
                    If dgvUnityItems.Rows(i).Cells(6).Value = "الاصغر" Then
                        Dim split As String() = New String() {"."}
                        Dim SplitNull As String = ""
                        Dim itemsSplit As String() = txt_priseSal.Text.Split(split, StringSplitOptions.None)
                        If itemsSplit.Length <> 1 Then
                            SplitNull = itemsSplit(1).ToString()
                        Else
                            dgvUnityItems.Rows(i).Cells(3).Value = txt_priseSal.Text
                        End If
                        If SplitNull <> "" Then
                            dgvUnityItems.Rows(i).Cells(3).Value = txt_priseSal.Text
                        End If
                    End If
                Next
            End If
        End If

    End Sub

    Private Sub cmbStores_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbStores.KeyUp
        If e.KeyCode = 13 Then
            txtqunt.Focus()
            txtqunt.Focus()
        End If
    End Sub

    Private Sub txtWholePrice_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtWholePrice.KeyUp
        If e.KeyCode = 13 Then
            If ActionNumericSeparators = False Then
                txtWholePrice.Text = FormatNumberWithSeparators(txtWholePrice.Text)
            End If
            txtWholeWholePrice.Focus()
            txtWholeWholePrice.SelectAll()
        End If
    End Sub

    Private Sub txtWholePrice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtWholePrice.TextChanged

        MyVars.CheckNumber(txtWholePrice)
        'GetRateDiscPriceAfter()
    End Sub

    Private Sub txtMinimumSalPrice_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtMinimumSalPrice.KeyUp
        If e.KeyCode = 13 Then
            If ActionNumericSeparators = False Then
                txtMinimumSalPrice.Text = FormatNumberWithSeparators(txtMinimumSalPrice.Text)
            End If
            txtrng.Focus()
            txtrng.SelectAll()
        End If
    End Sub

    Private Sub txtMinimumSalPrice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtMinimumSalPrice.TextChanged

        MyVars.CheckNumber(txtMinimumSalPrice)
    End Sub

    Private Sub falesItems()
        cmbStores.Enabled = True
        txtqunt.Enabled = False
        TxtPrc.Enabled = False
        Button1.Enabled = False
        'cmbGroup_Branch.Enabled = False
        If chkParcodeChange.Checked = False Then
            cmbitmnm.Enabled = True
            TxtPrc.Enabled = False
        Else
            TxtPrc.Enabled = True
            cmbitmnm.Enabled = False
        End If
    End Sub
    Private Sub TrueItems()
        cmbStores.Enabled = True
        txtqunt.Enabled = True
        TxtPrc.Enabled = True
        Button1.Enabled = True
        'cmbGroup_Branch.Enabled = True
        If chkParcodeChange.Checked = False Then
            cmbitmnm.Enabled = True
            TxtPrc.Enabled = False
        Else
            TxtPrc.Enabled = True
            cmbitmnm.Enabled = True
        End If
    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.Close()
    End Sub

    Private Sub txtWholeWholePrice_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtWholeWholePrice.KeyUp
        If e.KeyCode = 13 Then
            If ActionNumericSeparators = False Then
                txtWholeWholePrice.Text = FormatNumberWithSeparators(txtWholeWholePrice.Text)
            End If
            txtMinimumSalPrice.Focus()
            txtMinimumSalPrice.SelectAll()
        End If
    End Sub

    Private Sub txtWholeWholePrice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtWholeWholePrice.TextChanged

        MyVars.CheckNumber(txtWholeWholePrice)
        'GetRateDiscPriceAfter()
    End Sub

    Private Sub txtFindParcode_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtFindParcode.TextChanged
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        GetUP_ParcodeItemsUnity()

        GetBarcodeMore()

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If LanguageMainProgram = "العربية" Then
                If ActivateFormatNumberWithSeparators = "YES" Then
                    If txtFindParcode.Text = "" Then
                        S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],CAST(TinPriceAverage AS NVARCHAR(50)) as [متوسط سعر الشراء],CAST(SalPrice AS NVARCHAR(50)) as [سعر التجزئة],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة],CAST(MinimumSalPrice AS NVARCHAR(50)) as [حد ادنى للسعر],rng as [حد الطلب] ,CAST(store AS NVARCHAR(50)) as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id<>''")
                    Else
                        S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],CAST(TinPriceAverage AS NVARCHAR(50)) as [متوسط سعر الشراء],CAST(SalPrice AS NVARCHAR(50)) as [سعر التجزئة],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة],CAST(MinimumSalPrice AS NVARCHAR(50)) as [حد ادنى للسعر],rng as [حد الطلب] ,CAST(store AS NVARCHAR(50)) as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "itm_id =N'" & txtFindParcode.Text & "'")
                    End If
                Else
                    If txtFindParcode.Text = "" Then
                        S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],WholeWholePrice as [سعر جملة الجملة],MinimumSalPrice as [حد ادنى للسعر],rng as [حد الطلب] ,store as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id<>''")
                    Else
                        S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],WholeWholePrice as [سعر جملة الجملة],MinimumSalPrice as [حد ادنى للسعر],rng as [حد الطلب] ,store as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "itm_id =N'" & txtFindParcode.Text & "'")
                    End If
                End If

                If FilterSelect = "Number" Then
                    S = S & " order by [الباركود]"
                End If
                If FilterSelect = "Date" Then
                    S = S & " order by [المجموعة]"
                End If
                If FilterSelect = "Name" Then
                    S = S & " order by [الصنف]"
                End If
            ElseIf LanguageMainProgram = "English" Then
                If ActivateFormatNumberWithSeparators = "YES" Then
                    If txtFindParcode.Text = "" Then
                        S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Group], group_branch as [Subgroup], sname as [Item], CAST(TinPrice AS NVARCHAR(50)) as [Purchase Price], CAST(TinPriceAverage AS NVARCHAR(50)) as [Avg Purchase Price], CAST(SalPrice AS NVARCHAR(50)) as [Retail Price], CAST(WholePrice AS NVARCHAR(50)) as [Wholesale Price], CAST(WholeWholePrice AS NVARCHAR(50)) as [Bulk Price], CAST(MinimumSalPrice AS NVARCHAR(50)) as [Min Price], rng as [Reorder Level], CAST(store AS NVARCHAR(50)) as [Stock], Stores as [Warehouse], RateVAT as [VAT Rate]", "items", "id<>''")
                    Else
                        S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Group], group_branch as [Subgroup], sname as [Item], CAST(TinPrice AS NVARCHAR(50)) as [Purchase Price], CAST(TinPriceAverage AS NVARCHAR(50)) as [Avg Purchase Price], CAST(SalPrice AS NVARCHAR(50)) as [Retail Price], CAST(WholePrice AS NVARCHAR(50)) as [Wholesale Price], CAST(WholeWholePrice AS NVARCHAR(50)) as [Bulk Price], CAST(MinimumSalPrice AS NVARCHAR(50)) as [Min Price], rng as [Reorder Level], CAST(store AS NVARCHAR(50)) as [Stock], Stores as [Warehouse], RateVAT as [VAT Rate]", "items", "itm_id =N'" & txtFindParcode.Text & "'")
                    End If
                Else
                    If txtFindParcode.Text = "" Then
                        S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Group], group_branch as [Subgroup], sname as [Item], TinPrice as [Purchase Price], TinPriceAverage as [Avg Purchase Price], SalPrice as [Retail Price], WholePrice as [Wholesale Price], WholeWholePrice as [Bulk Price], MinimumSalPrice as [Min Price], rng as [Reorder Level], store as [Stock], Stores as [Warehouse], RateVAT as [VAT Rate]", "items", "id<>''")
                    Else
                        S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Group], group_branch as [Subgroup], sname as [Item], TinPrice as [Purchase Price], TinPriceAverage as [Avg Purchase Price], SalPrice as [Retail Price], WholePrice as [Wholesale Price], WholeWholePrice as [Bulk Price], MinimumSalPrice as [Min Price], rng as [Reorder Level], store as [Stock], Stores as [Warehouse], RateVAT as [VAT Rate]", "items", "itm_id =N'" & txtFindParcode.Text & "'")
                    End If
                End If

                If FilterSelect = "Number" Then
                    S = S & " order by [Barcode]"
                End If
                If FilterSelect = "Date" Then
                    S = S & " order by [Group]"
                End If
                If FilterSelect = "Name" Then
                    S = S & " order by [Item]"
                End If
            End If

            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)

            GetGridViewFormatNumberWithSeparators()

            Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
            If ShowGroupBranch = "NO" Then
                DTGV.Columns(2).Visible = False
            Else
                DTGV.Columns(2).Visible = True
            End If

            Dim HideWholeWholeSalePrice As String = mykey.GetValue("HideWholeWholeSalePrice", "NO")
            If HideWholeWholeSalePrice = "NO" Then
                DTGV.Columns(7).Visible = True
                DTGV.Columns(8).Visible = True
            Else
                DTGV.Columns(7).Visible = False
                DTGV.Columns(8).Visible = False
            End If
            If ShowValueVAT = "NO" Then
                DTGV.Columns(13).Visible = False
            End If

            Dim SM As Double
            For i As Integer = 0 To DTGV.Rows.Count - 1
                If LastTinPriceItems = "YES" Then
                    SM = SM + Val(DTGV.Rows(i).Cells(4).Value.ToString) * Val(DTGV.Rows(i).Cells(11).Value)
                Else
                    SM = SM + Val(DTGV.Rows(i).Cells(5).Value.ToString) * Val(DTGV.Rows(i).Cells(11).Value)
                End If
            Next
            txt_Total.Text = FormatNumberWithSeparators(SM)

            Dim SM1 As Double
            For i As Integer = 0 To DTGV.Rows.Count - 1
                SM1 = SM1 + Val(DTGV.Rows(i).Cells(11).Value.ToString)
            Next
            txtTotalQunt.Text = SM1

            txtNumberItems.Text = DTGV.RowCount
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetBarcodeMore()
        Try
            If BarcodeMore = "YES" Then
                Dim Parcode As String = ""
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select itm_id from BarcodeMore where itm_id_More=N'" & txtFindParcode.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    txtFindParcode.Text = dr("itm_id").ToString
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbGroup_Branch_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbGroup_Branch.SelectedIndexChanged
        If cmbitmnm.Text = "" Then
            Try
                If EditActive = False Then
                    Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
                    If ShowGroupBranch = "NO" Then
                        If cmbcats.Text.Trim = "" Then Exit Sub
                        Cls.fill_combo_Stores_Where("Items", "sname", "group_branch", cmbGroup_Branch.Text, cmbitmnm)
                        cmbitmnm.Text = ""
                    End If
                End If
            Catch ex As Exception
                ErrorHandling(ex, Me.Text)
            End Try
        End If
    End Sub

    Private Sub GroupBranch()
        Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
        If ShowGroupBranch = "NO" Then
            cmbGroup_Branch.Visible = False
            btnGroup_Branch.Visible = False
            lblGroub.Visible = False
            cmbitmnm.Size = New System.Drawing.Size(430, 24)
            'lblItems.Location = New System.Drawing.Point(758, 96)
        Else
            cmbGroup_Branch.Visible = True
            btnGroup_Branch.Visible = True
            lblGroub.Visible = True
            cmbitmnm.Size = New System.Drawing.Size(260, 24)
            'lblItems.Location = New System.Drawing.Point(758, 96)
        End If
    End Sub

    Private Sub cmbGroup_Branch_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbGroup_Branch.KeyUp
        If e.KeyCode = 13 Then
            cmbitmnm.Focus()
        End If
    End Sub

    Private Sub HideWholeWholeSalePrice()
        Dim HideWholeWholeSalePrice As String = mykey.GetValue("HideWholeWholeSalePrice", "NO")
        If HideWholeWholeSalePrice = "NO" Then
            txtWholeWholePrice.Visible = True
            lblWholeWholePrice.Visible = True
            txtWholePrice.Visible = True
            lblWholePrice.Visible = True
            txtRateWholeWholePrice.Visible = True
            Label39.Visible = True
            txtRateMinimumSalPrice.Visible = True
            Label40.Visible = True
        Else
            txtWholeWholePrice.Visible = False
            lblWholeWholePrice.Visible = False
            txtWholePrice.Visible = False
            lblWholePrice.Visible = False
            txtRateWholeWholePrice.Visible = False
            Label39.Visible = False
            txtRateMinimumSalPrice.Visible = False
            Label40.Visible = False
            txtMinimumSalPrice.Location = New System.Drawing.Point(620, 149)
            lblMinimumSalPrice.Location = New System.Drawing.Point(620, 128)
            txtrng.Location = New System.Drawing.Point(540, 149)
            lblrng.Location = New System.Drawing.Point(550, 128)
            TxtPrc.Location = New System.Drawing.Point(180, 149)
            lblPrc.Location = New System.Drawing.Point(320, 128)
            TxtPrc.Size = New System.Drawing.Point(350, 34)

        End If
    End Sub

    Private Sub LOGOBUTTON_Click(sender As Object, e As EventArgs) Handles LOGOBUTTON.Click
        On Error Resume Next
        OpenFileDialog1.Filter = "Image Files (*.png *.jpg *.bmp *.JPE *.JPEG) |*.png; *.jpg; *.bmp; *.JPE; *.JPEG|All Files(*.*) |*.*"
        With Me.OpenFileDialog1
            .FilterIndex = 1
            .Title = "حدد صورة الصنف"
            .ShowDialog()
            If Len(.FileName) > 0 Then
                PicLogo.Image = Image.FromFile(OpenFileDialog1.FileName)
                picWorkShowImage.Image = Image.FromFile(OpenFileDialog1.FileName)
            End If
        End With
    End Sub

    Private Sub PicLogo_Click(sender As Object, e As EventArgs) Handles PicLogo.Click
        PanelViewImage.Top = 20
        PanelViewImage.Dock = DockStyle.Fill
    End Sub

    Private Sub btnCloseViewImage_Click(sender As Object, e As EventArgs) Handles btnCloseViewImage.Click
        PanelViewImage.Dock = DockStyle.None
        PanelViewImage.Top = 5000
    End Sub

    Private Sub SHOWPHOTO()
        Try
            If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim XiIDTM As String
            XiIDTM = DTGV.SelectedRows(0).Cells(0).Value
            If NetworkName = "Yes" Then
                If UseExternalServer = "Yes" Then
                    connect()
                End If
            End If
            connectionStringOpen()
            Dim sql As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            sql = "SELECT Items_Images FROM Items WHERE itm_id =N'" & XiIDTM & "'"
            Dim cmd As SqlClient.SqlCommand = New SqlClient.SqlCommand(sql, Cn)
            Dim by() As Byte
            by = cmd.ExecuteScalar()
            If (by.Length > 0) Then
                Dim stream As New MemoryStream(by, True)
                stream.Write(by, 0, by.Length)
                PicLogo.Image = New Bitmap(stream)
                picWorkShowImage.Image = New Bitmap(stream)
                stream.Close()
            Else
                Me.PicLogo.Image = Nothing
                Me.picWorkShowImage.Image = Nothing
            End If
        Catch ex As Exception
            'ErrorHandling(ex, Me.Text)
        End Try
    End Sub


    Private Sub ImageUpdate()
        Try
            If OpenFileDialog1.FileName = "" Then
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = " Update Items SET  Items_Images = @Items_Images WHERE itm_id =N'" & TxtPrc.Text & "'"
                Dim cmd As SqlClient.SqlCommand = New SqlClient.SqlCommand
                cmd.CommandType = CommandType.Text
                cmd.Connection = Cn
                Dim fs As FileStream = New FileStream(OpenFileDialog1.FileName, FileMode.Open, FileAccess.Read)
                Dim r As BinaryReader = New BinaryReader(fs)
                Dim FileByteArray(fs.Length - 1) As Byte
                r.Read(FileByteArray, 0, CInt(fs.Length))
                With cmd
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    .CommandType = CommandType.Text
                    .Connection = Cn
                    .Parameters.Add("@Items_Images", SqlDbType.Image).Value = FileByteArray
                    .CommandText = S
                End With
                cmd.ExecuteNonQuery()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub DTGVColumnsWidth()
        Try
            'DTGV.Columns(0).Width = 90
            'DTGV.Columns(1).Width = 100
            'DTGV.Columns(2).Width = 100
            DTGV.Columns(3).Width = 250
            'DTGV.Columns(4).Width = 60
            'DTGV.Columns(5).Width = 60
            'DTGV.Columns(6).Width = 60
            'DTGV.Columns(7).Width = 55
            'DTGV.Columns(8).Width = 55
        Catch ex As Exception

        End Try

    End Sub

#Region "BarcodeMore"

    Private Sub btnCloseBarcodeMore_Click(sender As Object, e As EventArgs) Handles btnCloseBarcodeMore.Click
        PanelBarcodeMore.Top = 5000
    End Sub

    Private Sub btnAddBarcodeMore_Click(sender As Object, e As EventArgs) Handles btnAddBarcodeMore.Click
        If txtBarcodeMore.Text = "" Then MsgBox("فضلا ادخل الباركود", MsgBoxStyle.Exclamation) : txtBarcodeMore.Focus() : Exit Sub

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where itm_id =N'" & txtBarcodeMore.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                MsgBox("الباركود مسجل مسبقاً", MsgBoxStyle.Exclamation) : TxtPrc.Focus() : Exit Sub
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from BarcodeMore where itm_id_More =N'" & txtBarcodeMore.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                MsgBox("الباركود مسجل مسبقاً", MsgBoxStyle.Exclamation) : TxtPrc.Focus() : Exit Sub
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        dgvBarcodeMore.DataSource = Fn_AddBillBarcodeMore(txtBarcodeMore.Text)

        txtNumberBarcodeMore.Text = dgvBarcodeMore.RowCount

        txtBarcodeMore.Text = ""
        txtBarcodeMore.Focus()
    End Sub

    Friend Function Fn_AddBillBarcodeMore(ByVal Col_BarcodeMore As String) As DataTable
        If Dt_AddBill_BarcodeMore.Columns.Count = 0 Then
            Dt_AddBill_BarcodeMore.Columns.Add("الباركود", GetType(String))
        End If

        Dt_AddBill_BarcodeMore.Rows.Add(Col_BarcodeMore)
        Return Dt_AddBill_BarcodeMore
    End Function

    Private Sub txtBarcodeMore_KeyUp(sender As Object, e As KeyEventArgs) Handles txtBarcodeMore.KeyUp
        If e.KeyCode = 13 Then
            btnAddBarcodeMore.PerformClick()
        End If
    End Sub

    Private Sub btnBarcodeMore_Click(sender As Object, e As EventArgs) Handles btnBarcodeMore.Click
        PanelBarcodeMore.Top = 200
        PanelBarcodeMore.Size = New System.Drawing.Size(347, 287)
    End Sub

    Private Sub btnBarcodeMoreGenerate_Click(sender As Object, e As EventArgs) Handles btnBarcodeMoreGenerate.Click
        txtBarcodeMore.Text = vb.Right(Clss.GenerateItmId_Or_Parcode(), 6)
    End Sub

    Private Sub btnDelBarcodeMore_Click(sender As Object, e As EventArgs) Handles btnDelBarcodeMore.Click
        If dgvBarcodeMore.RowCount = 0 Then Beep() : Exit Sub
        If (dgvBarcodeMore.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        For i As Integer = 0 To dgvBarcodeMore.SelectedRows.Count - 1
            RNXD = dgvBarcodeMore.CurrentRow.Index
            dgvBarcodeMore.Rows.RemoveAt(RNXD)
        Next
        txtNumberBarcodeMore.Text = dgvBarcodeMore.RowCount
    End Sub

#End Region

#Region "UnityItems"
    Private Sub btnCloseUnityItems_Click(sender As Object, e As EventArgs) Handles btnCloseUnityItems.Click
        PanelUnityItems.Top = 5000
    End Sub

    Private Sub btnAddUnityItems_Click(sender As Object, e As EventArgs) Handles btnAddUnityItems.Click
        If cmbUnity.Text = "" Then MsgBox("فضلا ادخل نوع الوحدة", MsgBoxStyle.Exclamation) : cmbUnity.Focus() : Exit Sub
        If txtNumberPieces.Text = "" Or txtNumberPieces.Text = "0" Then MsgBox("فضلا ادخل عدد القطع", MsgBoxStyle.Exclamation) : txtNumberPieces.Focus() : Exit Sub
        If cmbUnitySize.Text = "" Then MsgBox("فضلا ادخل حجم الوحدة", MsgBoxStyle.Exclamation) : cmbUnitySize.Focus() : Exit Sub

        If dgvUnityItems.Rows.Count = 0 Then
            If txtitm_id_Unity.Text <> TxtPrc.Text Then
                MsgBox("فضلا يجب ان يكون باركود اول وحدة مطابق لباركود الصنف الاساسى", MsgBoxStyle.Exclamation) : cmbUnitySize.Focus() : Exit Sub
            Else
                TxtPrc.Enabled = False
            End If
        End If

        'If txtSalPriceUnit.Text = "" Then MsgBox("فضلا ادخل سعر البيع", MsgBoxStyle.Exclamation) : txtSalPriceUnit.Focus() : Exit Sub
        'If txtTinPriceUnit.Text = "" Then MsgBox("فضلا ادخل سعر الشراء", MsgBoxStyle.Exclamation) : txtTinPriceUnit.Focus() : Exit Sub
        Try
            For i As Integer = 0 To dgvUnityItems.Rows.Count - 1
                If dgvUnityItems.Rows(i).Cells(0).Value = cmbUnity.Text Then
                    MsgBox("أسم الوحدة مسجلة مسبقا على نفس الصنف", MsgBoxStyle.Exclamation) : cmbUnity.Focus() : Exit Sub
                End If
                If dgvUnityItems.Rows(i).Cells(7).Value = txtitm_id_Unity.Text Then
                    MsgBox("باركود الوحدة مسجل مسبقا على نفس الصنف", MsgBoxStyle.Exclamation) : txtitm_id_Unity.Focus() : Exit Sub
                End If
                If dgvUnityItems.Rows(i).Cells(6).Value = "الاصغر" Then
                    If cmbUnitySize.Text = "الاصغر" Then
                        MsgBox("الوحدة الاصغر مسجلة مسبقا", MsgBoxStyle.Exclamation) : cmbUnitySize.Focus() : Exit Sub
                    End If
                End If
            Next

            If cmbUnitySize.Text = "الاصغر" Then
                If txtTinPriceUnit.Text <> 0 Then
                    txttinprice.Text = txtTinPriceUnit.Text
                End If
                If txtSalPriceUnit.Text <> 0 Then
                    txt_priseSal.Text = txtSalPriceUnit.Text
                End If
            End If
            If cmbUnitySize.Text = "الاكبر" Then
                If txtTinPriceUnit.Text <> 0 Then
                    'Dim X1 As Double = Math.Round(253.666667, 2)
                    'Dim X3 As Decimal = Val(12) * Val(9.58333333333333)
                    Dim TotalUnitySize As Double = Val(txtTinPriceUnit.Text) / Val(txtNumberPieces.Text)
                    txttinprice.Text = TotalUnitySize
                End If
            End If

            Dim DefaultPurchase As String = ""
            If chkDefaultTin.Checked = True Then : DefaultPurchase = "1" : Else DefaultPurchase = "0" : End If
            Dim DefaultSale As String = ""
            If chkDefaultSale.Checked = True Then : DefaultSale = "1" : Else DefaultSale = "0" : End If


            dgvUnityItems.DataSource = Fn_AddBillUnityItems(cmbUnity.Text, txtNumberPieces.Text, txtTinPriceUnit.Text, txtSalPriceUnit.Text, DefaultPurchase, DefaultSale, cmbUnitySize.Text, txtitm_id_Unity.Text)

            For i As Integer = 0 To dgvUnityItems.Rows.Count - 1
                If dgvUnityItems.Rows(i).Cells(6).Value = "الاصغر" Then
                    dgvUnityItems.Rows(i).Cells(2).Value = txttinprice.Text
                End If
            Next

            cmbUnitySize.Text = ""
            txtTinPriceUnit.Text = "0"
            txtSalPriceUnit.Text = "0"
            txtNumberPieces.Text = ""
            'dgvUnityItems.Columns(2).Visible = False
            'dgvUnityItems.Columns(3).Visible = False
            dgvUnityItems.Columns(4).Visible = False
            dgvUnityItems.Columns(5).Visible = False

            dgvUnityItems.Columns(0).ReadOnly = True
            dgvUnityItems.Columns(1).ReadOnly = False
            dgvUnityItems.Columns(2).ReadOnly = False
            dgvUnityItems.Columns(3).ReadOnly = False
            dgvUnityItems.Columns(4).ReadOnly = True
            dgvUnityItems.Columns(5).ReadOnly = True
            dgvUnityItems.Columns(6).ReadOnly = True
            dgvUnityItems.Columns(7).ReadOnly = False

            cmbUnity.Focus()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetAddItemsUnity()
        Try
            Dt_AddBill_UnityItems.Rows.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "select Unity_Name,NumberPieces,TinPriceUnit,SalPriceUnit,DefaultTin,DefaultSale,UnitySize_Name,itm_id_Unity from View_ItemsUnitySize where itm_id =N'" & TxtPrc.Text & "'" : cmd.CommandText = S : dr = cmd.ExecuteReader
            Do While dr.Read
                dgvUnityItems.DataSource = Fn_AddBillUnityItems(dr(0).ToString, dr(1).ToString, dr(2).ToString, dr(3).ToString, dr(4).ToString, dr(5).ToString, dr(6).ToString, dr(7).ToString)
            Loop
            'dgvUnityItems.Columns(2).Visible = False
            'dgvUnityItems.Columns(3).Visible = False

            dgvUnityItems.Columns(4).Visible = False
            dgvUnityItems.Columns(5).Visible = False

            dgvUnityItems.Columns(0).ReadOnly = True
            dgvUnityItems.Columns(1).ReadOnly = False
            dgvUnityItems.Columns(2).ReadOnly = False
            dgvUnityItems.Columns(3).ReadOnly = False
            dgvUnityItems.Columns(4).ReadOnly = True
            dgvUnityItems.Columns(5).ReadOnly = True
            dgvUnityItems.Columns(6).ReadOnly = True
            dgvUnityItems.Columns(7).ReadOnly = False
        Catch ex As Exception
            'ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Friend Function Fn_AddBillUnityItems(ByVal Col_Unity_Name As String, ByVal Col_NumberPieces As Double, ByVal Col_TinPriceUnit As Double, ByVal Col_SalPriceUnit As Double, ByVal Col_DefaultTin As Integer, ByVal Col_DefaultSale As Integer, Col_UnitySize As String, ByVal Col_itm_id_Unity As String) As DataTable
        Try
            If Dt_AddBill_UnityItems.Columns.Count = 0 Then
                Dt_AddBill_UnityItems.Columns.Add("أسم الوحدة", GetType(String))
                Dt_AddBill_UnityItems.Columns.Add("عدد القطع", GetType(Double))
                Dt_AddBill_UnityItems.Columns.Add("سعر الشراء", GetType(Double))
                Dt_AddBill_UnityItems.Columns.Add("سعر البيع", GetType(Double))
                Dt_AddBill_UnityItems.Columns.Add("أفتراضى للشراء", GetType(Double))
                Dt_AddBill_UnityItems.Columns.Add("أفتراضى للبيع", GetType(Double))
                Dt_AddBill_UnityItems.Columns.Add("حجم الوحدة", GetType(String))
                Dt_AddBill_UnityItems.Columns.Add("باركود الوحدة", GetType(String))
            End If
            Dt_AddBill_UnityItems.Rows.Add(Col_Unity_Name, Col_NumberPieces, Col_TinPriceUnit, Col_SalPriceUnit, Col_DefaultTin, Col_DefaultSale, Col_UnitySize, Col_itm_id_Unity)
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        Return Dt_AddBill_UnityItems
    End Function

    Private Sub GetItemsUnity()
        connectionStringClose()
        connectionStringTransaction()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete from ItemsUnity where itm_id =N'" & itmprc & "'" : cmd.ExecuteNonQuery()

            Dim UnitySize_ID As Double
            Dim itm_id As String = ""
            For i As Integer = 0 To dgvUnityItems.Rows.Count - 1
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select UnitySize_ID from ItemsUnitySize where UnitySize_Name=N'" & dgvUnityItems.Rows(i).Cells(6).Value & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    UnitySize_ID = dr("UnitySize_ID").ToString
                End If
                If UnitySize_ID = 1 Then
                    UnityName = dgvUnityItems.Rows(i).Cells(0).Value
                End If
                If dgvUnityItems.Rows.Count = 1 Then
                    itm_id = TxtPrc.Text.Trim
                Else
                    itm_id = dgvUnityItems.Rows(i).Cells(7).Value
                End If
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into ItemsUnity(itm_id,Unity_Name,NumberPieces,TinPriceUnit,SalPriceUnit,DefaultTin,DefaultSale,UnitySize_ID,itm_id_Unity,Company_Branch_ID)  values("
                S = S & "N'" & TxtPrc.Text.Trim & "',N'" & dgvUnityItems.Rows(i).Cells(0).Value & "',N'" & dgvUnityItems.Rows(i).Cells(1).Value & "',N'" & dgvUnityItems.Rows(i).Cells(2).Value & "',N'" & dgvUnityItems.Rows(i).Cells(3).Value & "',N'" & dgvUnityItems.Rows(i).Cells(4).Value & "',N'" & dgvUnityItems.Rows(i).Cells(5).Value & "',N'" & UnitySize_ID & "',N'" & itm_id & "',N'" & Company_Branch_ID & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
            trans.Commit()
            connectionStringOpen()
        Catch ex As Exception
            trans.Rollback()
            ErrorHandling(ex, Me.Text)
            Cn.Close()
            connectionStringOpen()
            Exit Sub
        End Try
    End Sub

    Private Sub GetBarcodeMoreAdded()
        connectionStringClose()
        connectionStringTransaction()
        Try
            If BarcodeMore = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete from BarcodeMore where itm_id =N'" & itmprc & "'" : cmd.ExecuteNonQuery()

                Dim Active As Boolean = False
                For i As Integer = 0 To dgvBarcodeMore.Rows.Count - 1

                    If dgvBarcodeMore.Rows(i).Cells(0).Value <> TxtPrc.Text Then
                        If Active = False Then
                            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                            S = "insert into BarcodeMore(itm_id,itm_id_More)  values("
                            S = S & "N'" & TxtPrc.Text.Trim & "',N'" & TxtPrc.Text.Trim & "')"
                            cmd.CommandText = S : cmd.ExecuteNonQuery()
                            Active = True
                        End If
                    Else
                        Active = True
                    End If

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    S = "insert into BarcodeMore(itm_id,itm_id_More)  values("
                    S = S & "N'" & TxtPrc.Text.Trim & "',N'" & dgvBarcodeMore.Rows(i).Cells(0).Value & "')"
                    cmd.CommandText = S : cmd.ExecuteNonQuery()
                Next
                Active = False
            End If
            trans.Commit()
            connectionStringOpen()
        Catch ex As Exception
            trans.Rollback()
            ErrorHandling(ex, Me.Text)
            Cn.Close()
            connectionStringOpen()
            Exit Sub
        End Try

    End Sub
    Private Sub txtNumberPieces_KeyUp(sender As Object, e As KeyEventArgs) Handles txtNumberPieces.KeyUp
        If e.KeyCode = 13 Then
            txtTinPriceUnit.Focus()
        End If
    End Sub

    Private Sub txtTinPriceUnit_KeyUp(sender As Object, e As KeyEventArgs) Handles txtTinPriceUnit.KeyUp
        If e.KeyCode = 13 Then
            txtSalPriceUnit.Focus()
        End If
    End Sub

    Private Sub txtSalPriceUnit_KeyUp(sender As Object, e As KeyEventArgs) Handles txtSalPriceUnit.KeyUp
        If e.KeyCode = 13 Then
            cmbUnitySize.Focus()
        End If
    End Sub

    Private Sub btnHelp_Click(sender As Object, e As EventArgs) Handles btnHelp.Click
        cmbUnity.Text = mykey.GetValue("DefaultUnityName", "قطعة")
        txtNumberPieces.Text = 1
        PanelUnityItems.Top = 300
        PanelUnityItems.Size = New System.Drawing.Size(681, 336)
    End Sub

    Private Sub brnDelUnityItems_Click(sender As Object, e As EventArgs) Handles brnDelUnityItems.Click
        If dgvUnityItems.RowCount = 0 Then Beep() : Exit Sub
        If (dgvUnityItems.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        For i As Integer = 0 To dgvUnityItems.SelectedRows.Count - 1
            RNXD = dgvUnityItems.CurrentRow.Index
            dgvUnityItems.Rows.RemoveAt(RNXD)
        Next
    End Sub

    Private Sub cmbUnity_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbUnity.KeyUp
        If e.KeyCode = 13 Then
            txtNumberPieces.Focus()
        End If
    End Sub

    Private Sub chkParcodeChange_CheckedChanged(sender As Object, e As EventArgs) Handles chkParcodeChange.CheckedChanged
        TrueItems()
        falesItems()
    End Sub

    Private Sub txtRatePriceSale_TextChanged(sender As Object, e As EventArgs) Handles txtRatePriceSale.TextChanged
        If txtRatePriceSale.Text <> "0" Then
            Try
                Dim DiscVal As Double
                DiscVal = Val((Val(txttinprice.Text) * (100 + Val(txtRatePriceSale.Text))) / 100)
                txt_priseSal.Text = Math.Round(DiscVal, 4)
            Catch ex As Exception
            End Try
        End If
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(sender As Object, e As EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            dtpItemDeletedFrom.Enabled = False
            dtpItemDeletedTo.Enabled = False
        Else
            dtpItemDeletedFrom.Enabled = True
            dtpItemDeletedTo.Enabled = True
        End If
    End Sub

    Private Sub btnShowItemDeleted_Click(sender As Object, e As EventArgs) Handles btnShowItemDeleted.Click
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If ActivateFormatNumberWithSeparators = "YES" Then
                S = "select id as [رقم],delete_date  as [تاريخ الحذف],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الاسم],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],CAST(SalPrice AS NVARCHAR(50)) as [سعر التجزئة],store as [المخزون],Stores as [المخزن],UserName as [المستخدم] from ItemsDeleted where id <> N''"
            Else
                S = "select id as [رقم],delete_date  as [تاريخ الحذف],itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الاسم],TinPrice as [سعر الشراء],SalPrice as [سعر التجزئة],store as [المخزون],Stores as [المخزن],UserName as [المستخدم] from ItemsDeleted where id <> N''"
            End If
            If ChkWithoutDate.Checked = False Then
                S = S & " and delete_date >=N'" & Cls.C_date(dtpItemDeletedFrom.Text) & "' and delete_date <=N'" & Cls.C_date(dtpItemDeletedTo.Text) & "'"
            End If
            S = S & " order by id"

            cmd.CommandText = S : dr = cmd.ExecuteReader
            dgvViewItemDeleted.DataSource = Cls.PopulateDataView(dr)
            dgvViewItemDeleted.Columns(0).Visible = False


            Dim SM As String
            For i As Integer = 0 To dgvViewItemDeleted.RowCount - 1
                SM = Val(dgvViewItemDeleted.Rows(i).Cells(1).Value)
                SM = Cls.R_date(SM)
                dgvViewItemDeleted.Rows(i).Cells(1).Value = SM
            Next

            txtNumberBarcodeMore.Text = dgvViewItemDeleted.RowCount
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub btnCloseViewItemDeleted_Click(sender As Object, e As EventArgs) Handles btnCloseViewItemDeleted.Click
        PanelViewItemDeleted.Dock = DockStyle.None
        PanelViewItemDeleted.Top = 5000
    End Sub

    Private Sub btnViewItemDeleted_Click(sender As Object, e As EventArgs) Handles btnViewItemDeleted.Click
        PanelViewItemDeleted.Top = 80
        PanelViewItemDeleted.Dock = DockStyle.Fill
    End Sub

    Private Sub AddNewItemsDeleted(ByVal View_itm_id As String, ByVal View_Stores As String)
        Try
            Dim itm_id As String = "" : Dim group_name As String = "" : Dim group_branch As String = ""
            Dim sname As String = "" : Dim Unity As String = "" : Dim rng As String = ""
            Dim tinprice As String = "" : Dim salprice As String = "" : Dim TinPriceAverage As String = ""
            Dim WholePrice As String = "" : Dim WholeWholePrice As String = "" : Dim MinimumSalPrice As String = ""
            Dim RatePriceOffers As String = "" : Dim store As String = "" : Dim Stores As String = "" : Dim QuickSearch As String = ""

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from Items where itm_id=N'" & View_itm_id & "' and Stores=N'" & View_Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                itm_id = dr("itm_id").ToString
                group_name = dr("group_name").ToString
                group_branch = dr("group_branch").ToString
                sname = dr("sname").ToString
                Unity = dr("Unity").ToString
                rng = dr("rng").ToString
                tinprice = dr("tinprice").ToString
                salprice = dr("salprice").ToString
                TinPriceAverage = dr("TinPriceAverage").ToString
                WholePrice = dr("WholePrice").ToString
                WholeWholePrice = dr("WholeWholePrice").ToString
                MinimumSalPrice = dr("MinimumSalPrice").ToString
                RatePriceOffers = dr("RatePriceOffers").ToString
                store = dr("store").ToString
                Stores = dr("Stores").ToString
                QuickSearch = dr("QuickSearch").ToString
            End If

            Dim DateTimeNow As String = DateTime.Now.ToString("yyyyMMdd")

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into ItemsDeleted (Company_Branch_ID,delete_date,itm_id,group_name,group_branch,sname,Unity,rng,tinprice,salprice,TinPriceAverage,WholePrice,WholeWholePrice,MinimumSalPrice,RatePriceOffers,store,UserName,Stores,QuickSearch) values ("
            S = S & "N'" & Company_Branch_ID & "',N'" & DateTimeNow & "',N'" & itm_id & "',N'" & group_name & "',N'" & group_branch & "',N'" & sname & "',N'" & Unity & "',"
            S = S & "" & Val(rng) & ","
            S = S & "" & Val(tinprice) & ","
            S = S & "" & Val(salprice) & ","
            S = S & "" & Val(TinPriceAverage) & ","
            S = S & "" & Val(WholePrice) & ","
            S = S & "" & Val(WholeWholePrice) & ","
            S = S & "" & Val(MinimumSalPrice) & ","
            S = S & "" & Val(RatePriceOffers) & ","
            S = S & "N'" & store & "',N'" & UserName & "',N'" & Stores & "',N'" & QuickSearch & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbUnitySize_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbUnitySize.KeyUp
        If e.KeyCode = 13 Then
            txtitm_id_Unity.Focus()
        End If
    End Sub

    Private Sub txtitm_id_Unity_TextChanged(sender As Object, e As EventArgs) Handles txtitm_id_Unity.TextChanged
        If dgvUnityItems.Rows.Count = 0 Then
            TxtPrc.Text = txtitm_id_Unity.Text
        End If
    End Sub

    Private Sub txtRateWholePrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateWholePrice.TextChanged
        MyVars.CheckNumber(txtRateWholePrice)
        If txttinprice.Text = "." Then
            txttinprice.Text = "0."
        End If

        If txtRateWholePrice.Text <> "0" Then
            Try
                Dim DiscVal As Double
                DiscVal = Val((Val(txttinprice.Text) * (100 + Val(txtRateWholePrice.Text))) / 100)
                txtWholePrice.Text = Math.Round(DiscVal, 4)
            Catch ex As Exception
            End Try
        End If
    End Sub

    Private Sub txtRateWholeWholePrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateWholeWholePrice.TextChanged
        MyVars.CheckNumber(txtRateWholeWholePrice)
        If txttinprice.Text = "." Then
            txttinprice.Text = "0."
        End If

        If txtRateWholeWholePrice.Text <> "0" Then
            Try
                Dim DiscVal As Double
                DiscVal = Val((Val(txttinprice.Text) * (100 + Val(txtRateWholeWholePrice.Text))) / 100)
                txtWholeWholePrice.Text = Math.Round(DiscVal, 4)
            Catch ex As Exception
            End Try
        End If
    End Sub

    Private Sub txtRateMinimumSalPrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateMinimumSalPrice.TextChanged
        MyVars.CheckNumber(txtRateMinimumSalPrice)
        If txttinprice.Text = "." Then
            txttinprice.Text = "0."
        End If

        If txtRateMinimumSalPrice.Text <> "0" Then
            Try
                Dim DiscVal As Double
                DiscVal = Val((Val(txttinprice.Text) * (100 + Val(txtRateMinimumSalPrice.Text))) / 100)
                txtMinimumSalPrice.Text = Math.Round(DiscVal, 4)
            Catch ex As Exception
            End Try
        End If
    End Sub

    Private Sub txtRateVAT_TextChanged(sender As Object, e As EventArgs) Handles txtRateVAT.TextChanged
        MyVars.CheckNumber(txtRateVAT)
    End Sub

    Private Sub dgvUnityItems_CellValueChanged(sender As Object, e As DataGridViewCellEventArgs) Handles dgvUnityItems.CellValueChanged
        If EditActive = False Then
            If dgvUnityItems.RowCount = 0 Then Beep() : Exit Sub
            If (dgvUnityItems.Rows.Count) = 0 Then Beep() : Exit Sub
            Dim NumberPieces As String = dgvUnityItems.SelectedRows(0).Cells(1).Value
            Dim TinPrice As String = dgvUnityItems.SelectedRows(0).Cells(2).Value
            Dim SalPrice As String = dgvUnityItems.SelectedRows(0).Cells(3).Value
            Dim Parcode As String = dgvUnityItems.SelectedRows(0).Cells(7).Value
            If NumberPieces = 0 Or NumberPieces = 1 Then
                TxtPrc.Text = Parcode
                txttinprice.Text = TinPrice
                txt_priseSal.Text = SalPrice
            End If
        End If
    End Sub

    Private Sub btnCloseItemDiscountRate_Click(sender As Object, e As EventArgs) Handles btnCloseItemDiscountRate.Click
        PanelItemDiscountRate.Top = 5000
    End Sub

    Private Sub txtRateDiscTinPrice_KeyUp(sender As Object, e As KeyEventArgs) Handles txtRateDiscTinPrice.KeyUp
        If e.KeyCode = 13 Then
            txtRateDiscSalPrice.Focus()
            txtRateDiscSalPrice.Select()
        End If
    End Sub

    Private Sub txtRateDiscSalPrice_KeyUp(sender As Object, e As KeyEventArgs) Handles txtRateDiscSalPrice.KeyUp
        If e.KeyCode = 13 Then
            txtRateDiscWholePrice.Focus()
            txtRateDiscWholePrice.Select()
        End If
    End Sub

    Private Sub txtRateDiscWholePrice_KeyUp(sender As Object, e As KeyEventArgs) Handles txtRateDiscWholePrice.KeyUp
        If e.KeyCode = 13 Then
            txtRateDiscWholeWholePrice.Focus()
            txtRateDiscWholeWholePrice.Select()
        End If
    End Sub

    Private Sub txtRateDiscWholeWholePrice_KeyUp(sender As Object, e As KeyEventArgs) Handles txtRateDiscWholeWholePrice.KeyUp
        If e.KeyCode = 13 Then
            PanelItemDiscountRate.Top = 5000
        End If
    End Sub

    Private Sub txtRateDiscTinPrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateDiscTinPrice.TextChanged
        MyVars.CheckNumber(txtRateDiscTinPrice)
        GetRateDiscPriceAfter()
    End Sub

    Private Sub txtRateDiscSalPrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateDiscSalPrice.TextChanged
        MyVars.CheckNumber(txtRateDiscSalPrice)
        GetRateDiscPriceAfter()
    End Sub

    Private Sub txtRateDiscWholePrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateDiscWholePrice.TextChanged
        MyVars.CheckNumber(txtRateDiscWholePrice)
        GetRateDiscPriceAfter()
    End Sub

    Private Sub txtRateDiscWholeWholePrice_TextChanged(sender As Object, e As EventArgs) Handles txtRateDiscWholeWholePrice.TextChanged
        MyVars.CheckNumber(txtRateDiscWholeWholePrice)
        GetRateDiscPriceAfter()
    End Sub

    Private Sub btnItemDiscountRate_Click(sender As Object, e As EventArgs) Handles btnItemDiscountRate.Click
        txtRateDiscTinPrice.Focus()
        txtRateDiscTinPrice.Select()
        PanelItemDiscountRate.Top = 205
        PanelItemDiscountRate.Size = New System.Drawing.Size(550, 230)
    End Sub

    Private Sub PictureBox2_Click(sender As Object, e As EventArgs) Handles PictureBox2.Click
        Headerx()
    End Sub

    Private Sub cmbFindCats_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbFindCats.SelectedIndexChanged
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        GetBarcodeMore()

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If LanguageMainProgram = "العربية" Then
                If ActivateFormatNumberWithSeparators = "YES" Then
                    If cmbFindCats.Text = "" Then
                        S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],CAST(TinPriceAverage AS NVARCHAR(50)) as [متوسط سعر الشراء],CAST(SalPrice AS NVARCHAR(50)) as [سعر التجزئة],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة],CAST(MinimumSalPrice AS NVARCHAR(50)) as [حد ادنى للسعر],rng as [حد الطلب] ,CAST(store AS NVARCHAR(50)) as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id<>''")
                    Else
                        S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],CAST(TinPriceAverage AS NVARCHAR(50)) as [متوسط سعر الشراء],CAST(SalPrice AS NVARCHAR(50)) as [سعر التجزئة],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة],CAST(MinimumSalPrice AS NVARCHAR(50)) as [حد ادنى للسعر],rng as [حد الطلب] ,CAST(store AS NVARCHAR(50)) as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "group_name =N'" & cmbFindCats.Text & "'")
                    End If
                Else
                    If cmbFindCats.Text = "" Then
                        S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],WholeWholePrice as [سعر جملة الجملة],MinimumSalPrice as [حد ادنى للسعر],rng as [حد الطلب] ,store as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id<>''")
                    Else
                        S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],WholeWholePrice as [سعر جملة الجملة],MinimumSalPrice as [حد ادنى للسعر],rng as [حد الطلب] ,store as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "group_name =N'" & cmbFindCats.Text & "'")
                    End If
                End If
                If FilterSelect = "Number" Then
                    S = S & " order by [الباركود]"
                End If
                If FilterSelect = "Date" Then
                    S = S & " order by [المجموعة]"
                End If
                If FilterSelect = "Name" Then
                    S = S & " order by [الصنف]"
                End If
            ElseIf LanguageMainProgram = "English" Then
                If ActivateFormatNumberWithSeparators = "YES" Then
                    If cmbFindCats.Text = "" Then
                        S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Category], group_branch as [Subcategory], sname as [Item], CAST(TinPrice AS NVARCHAR(50)) as [Purchase Price], CAST(TinPriceAverage AS NVARCHAR(50)) as [Avg Purchase Price], CAST(SalPrice AS NVARCHAR(50)) as [Retail Price], CAST(WholePrice AS NVARCHAR(50)) as [Wholesale Price], CAST(WholeWholePrice AS NVARCHAR(50)) as [Bulk Price], CAST(MinimumSalPrice AS NVARCHAR(50)) as [Min Price], rng as [Reorder Level], CAST(store AS NVARCHAR(50)) as [Inventory], Stores as [Warehouse], RateVAT as [VAT Rate]", "items", "id<>''")
                    Else
                        S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Category], group_branch as [Subcategory], sname as [Item], CAST(TinPrice AS NVARCHAR(50)) as [Purchase Price], CAST(TinPriceAverage AS NVARCHAR(50)) as [Avg Purchase Price], CAST(SalPrice AS NVARCHAR(50)) as [Retail Price], CAST(WholePrice AS NVARCHAR(50)) as [Wholesale Price], CAST(WholeWholePrice AS NVARCHAR(50)) as [Bulk Price], CAST(MinimumSalPrice AS NVARCHAR(50)) as [Min Price], rng as [Reorder Level], CAST(store AS NVARCHAR(50)) as [Inventory], Stores as [Warehouse], RateVAT as [VAT Rate]", "items", "group_name =N'" & cmbFindCats.Text & "'")
                    End If
                Else
                    If cmbFindCats.Text = "" Then
                        S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Category], group_branch as [Subcategory], sname as [Item], TinPrice as [Purchase Price], TinPriceAverage as [Avg Purchase Price], SalPrice as [Retail Price], WholePrice as [Wholesale Price], WholeWholePrice as [Bulk Price], MinimumSalPrice as [Min Price], rng as [Reorder Level], store as [Inventory], Stores as [Warehouse], RateVAT as [VAT Rate]", "items", "id<>''")
                    Else
                        S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Category], group_branch as [Subcategory], sname as [Item], TinPrice as [Purchase Price], TinPriceAverage as [Avg Purchase Price], SalPrice as [Retail Price], WholePrice as [Wholesale Price], WholeWholePrice as [Bulk Price], MinimumSalPrice as [Min Price], rng as [Reorder Level], store as [Inventory], Stores as [Warehouse], RateVAT as [VAT Rate]", "items", "group_name =N'" & cmbFindCats.Text & "'")
                    End If
                End If

                If FilterSelect = "Number" Then
                    S = S & " order by [Barcode]"
                End If
                If FilterSelect = "Date" Then
                    S = S & " order by [Category]"
                End If
                If FilterSelect = "Name" Then
                    S = S & " order by [Item]"
                End If
            End If

            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)

            Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
            If ShowGroupBranch = "NO" Then
                DTGV.Columns(2).Visible = False
            Else
                DTGV.Columns(2).Visible = True
            End If

            Dim HideWholeWholeSalePrice As String = mykey.GetValue("HideWholeWholeSalePrice", "NO")
            If HideWholeWholeSalePrice = "NO" Then
                DTGV.Columns(7).Visible = True
                DTGV.Columns(8).Visible = True
            Else
                DTGV.Columns(7).Visible = False
                DTGV.Columns(8).Visible = False
            End If
            If ShowValueVAT = "NO" Then
                DTGV.Columns(13).Visible = False
            End If

            Dim SM As Double
            For i As Integer = 0 To DTGV.Rows.Count - 1
                If LastTinPriceItems = "YES" Then
                    SM = SM + Val(DTGV.Rows(i).Cells(4).Value.ToString) * Val(DTGV.Rows(i).Cells(11).Value)
                Else
                    SM = SM + Val(DTGV.Rows(i).Cells(5).Value.ToString) * Val(DTGV.Rows(i).Cells(11).Value)
                End If
            Next
            txt_Total.Text = FormatNumberWithSeparators(SM)

            Dim SM1 As Double
            For i As Integer = 0 To DTGV.Rows.Count - 1
                SM1 = SM1 + Val(DTGV.Rows(i).Cells(11).Value.ToString)
            Next
            txtTotalQunt.Text = SM1

            txtNumberItems.Text = DTGV.RowCount

            GetGridViewFormatNumberWithSeparators()

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbFindVendores_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbFindVendores.SelectedIndexChanged
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        GetBarcodeMore()

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If LanguageMainProgram = "العربية" Then
                If ActivateFormatNumberWithSeparators = "YES" Then
                    If cmbFindVendores.Text = "" Then
                        S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],CAST(TinPriceAverage AS NVARCHAR(50)) as [متوسط سعر الشراء],CAST(SalPrice AS NVARCHAR(50)) as [سعر التجزئة],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة],CAST(MinimumSalPrice AS NVARCHAR(50)) as [حد ادنى للسعر],rng as [حد الطلب] ,CAST(store AS NVARCHAR(50)) as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id<>''")
                    Else
                        S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],CAST(TinPriceAverage AS NVARCHAR(50)) as [متوسط سعر الشراء],CAST(SalPrice AS NVARCHAR(50)) as [سعر التجزئة],CAST(WholePrice AS NVARCHAR(50)) as [سعر الجملة],CAST(WholeWholePrice AS NVARCHAR(50)) as [سعر جملة الجملة],CAST(MinimumSalPrice AS NVARCHAR(50)) as [حد ادنى للسعر],rng as [حد الطلب] ,CAST(store AS NVARCHAR(50)) as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "Vendorname =N'" & cmbFindVendores.Text & "'")
                    End If
                Else
                    If cmbFindVendores.Text = "" Then
                        S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],WholeWholePrice as [سعر جملة الجملة],MinimumSalPrice as [حد ادنى للسعر],rng as [حد الطلب] ,store as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "id<>''")
                    Else
                        S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],WholeWholePrice as [سعر جملة الجملة],MinimumSalPrice as [حد ادنى للسعر],rng as [حد الطلب] ,store as [المخزون],Stores as [المخزن],RateVAT as [الضريبة]", "items", "Vendorname =N'" & cmbFindVendores.Text & "'")
                    End If
                End If
                If FilterSelect = "Number" Then
                    S = S & " order by [الباركود]"
                End If
                If FilterSelect = "Date" Then
                    S = S & " order by [المجموعة]"
                End If
                If FilterSelect = "Name" Then
                    S = S & " order by [الصنف]"
                End If
            ElseIf LanguageMainProgram = "English" Then
                If ActivateFormatNumberWithSeparators = "YES" Then
                    If cmbFindVendores.Text = "" Then
                        S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Category], group_branch as [Subcategory], sname as [Item], CAST(TinPrice AS NVARCHAR(50)) as [Purchase Price], CAST(TinPriceAverage AS NVARCHAR(50)) as [Average Purchase Price], CAST(SalPrice AS NVARCHAR(50)) as [Retail Price], CAST(WholePrice AS NVARCHAR(50)) as [Wholesale Price], CAST(WholeWholePrice AS NVARCHAR(50)) as [Bulk Wholesale Price], CAST(MinimumSalPrice AS NVARCHAR(50)) as [Minimum Price], rng as [Reorder Point], CAST(store AS NVARCHAR(50)) as [Inventory], Stores as [Warehouse], RateVAT as [VAT Rate]", "items", "id<>''")
                    Else
                        S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Category], group_branch as [Subcategory], sname as [Item], CAST(TinPrice AS NVARCHAR(50)) as [Purchase Price], CAST(TinPriceAverage AS NVARCHAR(50)) as [Average Purchase Price], CAST(SalPrice AS NVARCHAR(50)) as [Retail Price], CAST(WholePrice AS NVARCHAR(50)) as [Wholesale Price], CAST(WholeWholePrice AS NVARCHAR(50)) as [Bulk Wholesale Price], CAST(MinimumSalPrice AS NVARCHAR(50)) as [Minimum Price], rng as [Reorder Point], CAST(store AS NVARCHAR(50)) as [Inventory], Stores as [Warehouse], RateVAT as [VAT Rate]", "items", "Vendorname =N'" & cmbFindVendores.Text & "'")
                    End If
                Else
                    If cmbFindVendores.Text = "" Then
                        S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Category], group_branch as [Subcategory], sname as [Item], TinPrice as [Purchase Price], TinPriceAverage as [Average Purchase Price], SalPrice as [Retail Price], WholePrice as [Wholesale Price], WholeWholePrice as [Bulk Wholesale Price], MinimumSalPrice as [Minimum Price], rng as [Reorder Point], store as [Inventory], Stores as [Warehouse], RateVAT as [VAT Rate]", "items", "id<>''")
                    Else
                        S = Cls.Get_Select_Grid_S("itm_id as [Barcode], group_name as [Category], group_branch as [Subcategory], sname as [Item], TinPrice as [Purchase Price], TinPriceAverage as [Average Purchase Price], SalPrice as [Retail Price], WholePrice as [Wholesale Price], WholeWholePrice as [Bulk Wholesale Price], MinimumSalPrice as [Minimum Price], rng as [Reorder Point], store as [Inventory], Stores as [Warehouse], RateVAT as [VAT Rate]", "items", "Vendorname =N'" & cmbFindVendores.Text & "'")
                    End If
                End If

                If FilterSelect = "Number" Then
                    S = S & " order by [Barcode]"
                End If
                If FilterSelect = "Date" Then
                    S = S & " order by [Category]"
                End If
                If FilterSelect = "Name" Then
                    S = S & " order by [Item]"
                End If
            End If

            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)

            Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
            If ShowGroupBranch = "NO" Then
                DTGV.Columns(2).Visible = False
            Else
                DTGV.Columns(2).Visible = True
            End If

            Dim HideWholeWholeSalePrice As String = mykey.GetValue("HideWholeWholeSalePrice", "NO")
            If HideWholeWholeSalePrice = "NO" Then
                DTGV.Columns(7).Visible = True
                DTGV.Columns(8).Visible = True
            Else
                DTGV.Columns(7).Visible = False
                DTGV.Columns(8).Visible = False
            End If
            If ShowValueVAT = "NO" Then
                DTGV.Columns(13).Visible = False
            End If

            Dim SM As Double
            For i As Integer = 0 To DTGV.Rows.Count - 1
                If LastTinPriceItems = "YES" Then
                    SM = SM + Val(DTGV.Rows(i).Cells(4).Value.ToString) * Val(DTGV.Rows(i).Cells(11).Value)
                Else
                    SM = SM + Val(DTGV.Rows(i).Cells(5).Value.ToString) * Val(DTGV.Rows(i).Cells(11).Value)
                End If
            Next
            txt_Total.Text = FormatNumberWithSeparators(SM)

            Dim SM1 As Double
            For i As Integer = 0 To DTGV.Rows.Count - 1
                SM1 = SM1 + Val(DTGV.Rows(i).Cells(11).Value.ToString)
            Next
            txtTotalQunt.Text = SM1

            txtNumberItems.Text = DTGV.RowCount

            GetGridViewFormatNumberWithSeparators()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetRateDiscPriceAfter()
        txtRateDiscTinPriceAfter.Text = Val((Val(txttinprice.Text) * (100 - Val(txtRateDiscTinPrice.Text))) / 100)
        txtRateDiscTinPriceAfter.Text = Math.Round(Val(txtRateDiscTinPriceAfter.Text), 2)

        If rdoDiscRateSalPrice.Checked = True Then
            txtRateDiscSalPriceAfter.Text = Val((Val(txt_priseSal.Text) * (100 - Val(txtRateDiscSalPrice.Text))) / 100)
            txtRateDiscSalPriceAfter.Text = Math.Round(Val(txtRateDiscSalPriceAfter.Text), 2)
        Else
            txtRateDiscSalPriceAfter.Text = Val((Val(txt_priseSal.Text) * (100 + Val(txtRateDiscSalPrice.Text))) / 100)
            txtRateDiscSalPriceAfter.Text = Math.Round(Val(txtRateDiscSalPriceAfter.Text), 2)
        End If
        If rdoDiscRateWholePrice.Checked = True Then
            txtRateDiscWholePriceAfter.Text = Val((Val(txtWholePrice.Text) * (100 - Val(txtRateDiscWholePrice.Text))) / 100)
            txtRateDiscWholePriceAfter.Text = Math.Round(Val(txtRateDiscWholePriceAfter.Text), 2)
        Else
            txtRateDiscWholePriceAfter.Text = Val((Val(txtWholePrice.Text) * (100 + Val(txtRateDiscWholePrice.Text))) / 100)
            txtRateDiscWholePriceAfter.Text = Math.Round(Val(txtRateDiscWholePriceAfter.Text), 2)
        End If
        If rdoDiscRateWholeWholePrice.Checked = True Then
            txtRateDiscWholeWholePriceAfter.Text = Val((Val(txtWholeWholePrice.Text) * (100 - Val(txtRateDiscWholeWholePrice.Text))) / 100)
            txtRateDiscWholeWholePriceAfter.Text = Math.Round(Val(txtRateDiscWholeWholePriceAfter.Text), 2)
        Else
            txtRateDiscWholeWholePriceAfter.Text = Val((Val(txtWholeWholePrice.Text) * (100 + Val(txtRateDiscWholeWholePrice.Text))) / 100)
            txtRateDiscWholeWholePriceAfter.Text = Math.Round(Val(txtRateDiscWholeWholePriceAfter.Text), 2)
        End If

    End Sub

    Private Sub btnItemsAlternative_Click(sender As Object, e As EventArgs) Handles btnItemsAlternative.Click
        PanelAlternative.Top = 159
        PanelAlternative.Left = 278
        PanelAlternative.Size = New System.Drawing.Size(670, 341)
        cmbTradeName.Focus()
    End Sub

    Private Sub btnCloseAlternative_Click(sender As Object, e As EventArgs) Handles btnCloseAlternative.Click
        PanelAlternative.Top = 5000
    End Sub

    Private Sub btnAddAlternative_Click(sender As Object, e As EventArgs) Handles btnAddAlternative.Click
        Dim Xitm_id As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id from items where sname= '" & cmbTradeName.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            Xitm_id = dr(0)
        End If

        For i As Integer = 0 To Dt_AddBill.Rows.Count - 1
            If DGVAlternative.Rows(i).Cells(0).Value = cmbTradeName.Text.Trim Then MsgBox("صنف مكرر بنفس الفاتورة", MsgBoxStyle.Exclamation) : TxtPrc.Focus() : TxtPrc.SelectAll() : Exit Sub
        Next

        If cmbTradeName.Text = "" Then MsgBox("فضلا حدد اإسم الصنف التجارى", MsgBoxStyle.Exclamation) : cmbTradeName.Focus() : Exit Sub

        DGVAlternative.DataSource = Fn_AddBillAlternative(TxtPrc.Text, Xitm_id, cmbTradeName.Text, cmbScientificName.Text, txtDrugConcentration.Text)

        cmbTradeName.Text = ""
        cmbScientificName.Text = ""
        txtDrugConcentration.Text = ""
        cmbTradeName.Focus()
    End Sub


    Friend Function Fn_AddBillAlternative(ByVal Col_itm_id As String, ByVal Col_itm_idTradeName As Double, ByVal Col_TradeName As String, ByVal Col_ScientificName As String, ByVal Col_DrugConcentration As String) As DataTable
        If Dt_AddBillAlternative.Columns.Count = 0 Then
            Dt_AddBillAlternative.Columns.Add("الباركود", GetType(String))
            Dt_AddBillAlternative.Columns.Add("الباركود2", GetType(Double))
            Dt_AddBillAlternative.Columns.Add("الاسم التجارى", GetType(String))
            Dt_AddBillAlternative.Columns.Add("الاسم العلمى", GetType(String))
            Dt_AddBillAlternative.Columns.Add("التركيز", GetType(String))
        End If

        Dt_AddBillAlternative.Rows.Add(Col_itm_id, Col_itm_idTradeName, Col_TradeName, Col_ScientificName, Col_DrugConcentration)

        Return Dt_AddBillAlternative

    End Function

    Private Sub GetUpdateItemsAlternative()
        If DealingPharmacySystem = "YES" Then
            Dim itmprc As String = ""
            itmprc = DTGV.SelectedRows(0).Cells(0).Value
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete from ItemsAlternative where itm_id =N'" & itmprc & "'" : cmd.ExecuteNonQuery()

            For i As Integer = 0 To DGVAlternative.Rows.Count - 1

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into ItemsAlternative(itm_id,itm_idTradeName,TradeName,ScientificName,DrugConcentration)  values("
                S = S & "'" & DGVAlternative.Rows(i).Cells(0).Value & "','" & DGVAlternative.Rows(i).Cells(1).Value & "','" & DGVAlternative.Rows(i).Cells(2).Value & "','" & DGVAlternative.Rows(i).Cells(3).Value & "','" & DGVAlternative.Rows(i).Cells(4).Value & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
        End If
    End Sub

    Private Sub GetDataItemsAlternative()
        If DealingPharmacySystem = "YES" Then
            Dt_AddBillAlternative.Rows.Clear()
            Dim itmprc As String = ""
            itmprc = DTGV.SelectedRows(0).Cells(0).Value

            Dt_AddBill.Rows.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "select itm_id,itm_idTradeName, TradeName, ScientificName, DrugConcentration from ItemsAlternative where itm_id = '" & itmprc & "'" : cmd.CommandText = S : dr = cmd.ExecuteReader
            Do While dr.Read
                DGVAlternative.DataSource = Fn_AddBillAlternative(dr(0).ToString(), dr(1).ToString(), dr(2), dr(3).ToString(), dr(4).ToString())
            Loop

            PanelAlternative.Top = 159
            PanelAlternative.Left = 278
            PanelAlternative.Size = New System.Drawing.Size(773, 341)
            cmbTradeName.Focus()
        End If


    End Sub

    Private Sub btnDeleteAlternative_Click(sender As Object, e As EventArgs) Handles btnDeleteAlternative.Click
        If DGVAlternative.RowCount = 0 Then Beep() : Exit Sub
        If (DGVAlternative.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        RNXD = DGVAlternative.CurrentRow.Index
        DGVAlternative.Rows.RemoveAt(RNXD)
    End Sub

    Private Sub cmbTradeName_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbTradeName.KeyUp
        If e.KeyCode = 13 Then
            cmbScientificName.Focus()
        End If
    End Sub

    Private Sub cmbScientificName_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbScientificName.KeyUp
        If e.KeyCode = 13 Then
            txtDrugConcentration.Focus()
        End If
    End Sub

    Private Sub txtDrugConcentration_KeyUp(sender As Object, e As KeyEventArgs) Handles txtDrugConcentration.KeyUp
        If e.KeyCode = 13 Then
            btnAddAlternative.PerformClick()
        End If
    End Sub

    Private Sub rdoDiscRateSalPrice_CheckedChanged(sender As Object, e As EventArgs) Handles rdoDiscRateSalPrice.CheckedChanged
        GetRateDiscPriceAfter()
    End Sub

    Private Sub rdoPlusRateSalPrice_CheckedChanged(sender As Object, e As EventArgs) Handles rdoPlusRateSalPrice.CheckedChanged
        GetRateDiscPriceAfter()
    End Sub

    Private Sub rdoDiscRateWholePrice_CheckedChanged(sender As Object, e As EventArgs) Handles rdoDiscRateWholePrice.CheckedChanged
        GetRateDiscPriceAfter()
    End Sub

    Private Sub rdoPlusRateWholePrice_CheckedChanged(sender As Object, e As EventArgs) Handles rdoPlusRateWholePrice.CheckedChanged
        GetRateDiscPriceAfter()
    End Sub

    Private Sub rdoDiscRateWholeWholePrice_CheckedChanged(sender As Object, e As EventArgs) Handles rdoDiscRateWholeWholePrice.CheckedChanged
        GetRateDiscPriceAfter()
    End Sub

    Private Sub rdoPlusRateWholeWholePrice_CheckedChanged(sender As Object, e As EventArgs) Handles rdoPlusRateWholeWholePrice.CheckedChanged
        GetRateDiscPriceAfter()
    End Sub


#End Region

    Private Sub GetUpdateItemsAll()

        For i As Integer = 0 To DTGV.Rows.Count - 1

            txtIDItm.Text = DTGV.Rows(i).Cells(0).Value
            txtIDStores.Text = DTGV.Rows(i).Cells(12).Value
            txtIDSname.Text = DTGV.Rows(i).Cells(3).Value

            SubGetDataUpdateItemsAll()

            btnsave.PerformClick()
        Next

    End Sub

    Private Sub SubGetDataUpdateItemsAll()
        EditActive = True
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        TrueItems()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from BilltINData where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no <> N'جرد'"
            H = cmd.ExecuteScalar
            If H > 0 Then
                'MsgBox("لا يمكنك التعديل في مخزون هذا الصنف حيث انك قد أجريت عملية شراء عليه", MsgBoxStyle.Exclamation)
                falesItems()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from BillsalData where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no <> N'جرد'"
            H = cmd.ExecuteScalar
            If H > 0 Then
                'MsgBox("لا يمكنك التعديل في مخزون هذا الصنف حيث انك قد أجريت عملية بيع عليه", MsgBoxStyle.Exclamation)
                falesItems()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        'Try
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from IM_Btin_Data where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no <> N'جرد'"
        H = cmd.ExecuteScalar
        If H > 0 Then
            'MsgBox("لا يمكنك التعديل في مخزون هذا الصنف حيث انك قد أجريت عملية مرتجع شراء عليه", MsgBoxStyle.Exclamation)
            falesItems()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from IM_Bsal_Data where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "' and bill_no <> N'جرد'"
        H = cmd.ExecuteScalar
        If H > 0 Then
            'MsgBox("لا يمكنك التعديل في مخزون هذا الصنف حيث انك قد أجريت عملية  مرتجع بيع عليه", MsgBoxStyle.Exclamation)
            falesItems()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from ManufacturingProductAdd where itm_id =N'" & txtIDItm.Text & "' and Stores =N'" & txtIDStores.Text & "'"
        H = cmd.ExecuteScalar
        If H > 0 Then
            'MsgBox("لا يمكنك التعديل في مخزون هذا الصنف حيث انك قد أجريت عملية  مرتجع بيع عليه", MsgBoxStyle.Exclamation)
            falesItems()
        End If

        Dim BalanceBarcode As String = ""
        Dim QuickSearch As String = ""
        Dim PriceIncludesVAT As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select  group_name,group_branch,sname,Unity,rng,store,itm_id,TinPrice,SalPrice,WholePrice,WholeWholePrice,MinimumSalPrice,Stores,QuickSearch,Height,Width,Altitude,Density,BalanceBarcode,RateWholePrice,RateWholeWholePrice,RateMinimumSalPrice,RateVAT,Vendorname,TinPriceAverage,RateDiscTinPrice,RateDiscSalPrice,RateDiscWholePrice,RateDiscWholeWholePrice,PriceIncludesVAT,RateDiscTinPriceAfter,RateDiscSalPriceAfter,RateDiscWholePriceAfter,RateDiscWholeWholePriceAfter,TypePlusDiscRateSalPrice,TypePlusDiscRateWholePrice,TypePlusDiscRateWholeWholePrice from items where itm_id =N'" & txtIDItm.Text & "' and Stores=N'" & txtIDStores.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            cmbcats.Text = dr(0).ToString
            cmbGroup_Branch.Text = dr(1).ToString
            cmbitmnm.Text = dr(2).ToString
            cmbUnity.Text = dr(3).ToString
            txtrng.Text = dr(4).ToString
            txtqunt.Text = dr(5).ToString
            TxtPrc.Text = dr(6).ToString
            itmprc = dr(6).ToString
            txttinprice.Text = dr(7).ToString
            txt_priseSal.Text = dr(8).ToString
            txtWholePrice.Text = dr(9).ToString
            txtWholeWholePrice.Text = dr(10).ToString
            txtMinimumSalPrice.Text = dr(11).ToString
            cmbStores.Text = dr(12).ToString
            QuickSearch = dr(13).ToString
            txtHeight.Text = dr(14).ToString
            txtWidth.Text = dr(15).ToString
            txtAltitude.Text = dr(16).ToString
            txtDensity.Text = dr(17).ToString
            BalanceBarcode = dr(18).ToString
            txtRateWholePrice.Text = dr(19).ToString
            txtRateWholeWholePrice.Text = dr(20).ToString
            txtRateMinimumSalPrice.Text = dr(21).ToString
            txtRateVAT.Text = dr(22).ToString
            cmbvendores.Text = dr(23).ToString
            txtTinPriceAverage.Text = dr(24).ToString
            txtRateDiscTinPrice.Text = dr(25).ToString
            txtRateDiscSalPrice.Text = dr(26).ToString
            txtRateDiscWholePrice.Text = dr(27).ToString
            txtRateDiscWholeWholePrice.Text = dr(28).ToString
            PriceIncludesVAT = dr(29).ToString
            txtRateDiscTinPriceAfter.Text = dr(30)
            txtRateDiscSalPriceAfter.Text = dr(31)
            txtRateDiscWholePriceAfter.Text = dr(32)
            txtRateDiscWholeWholePriceAfter.Text = dr(33)
            If dr(34) Is DBNull.Value Then
            Else
                If dr(34).ToString = "0" Then
                    rdoDiscRateSalPrice.Checked = True
                Else
                    rdoPlusRateSalPrice.Checked = True
                End If
            End If
            If dr(35) Is DBNull.Value Then
            Else
                If dr(35).ToString = "0" Then
                    rdoDiscRateWholePrice.Checked = True
                Else
                    rdoPlusRateWholePrice.Checked = True
                End If
            End If
            If dr(36) Is DBNull.Value Then
            Else
                If dr(36).ToString = "0" Then
                    rdoDiscRateWholeWholePrice.Checked = True
                Else
                    rdoPlusRateWholeWholePrice.Checked = True
                End If
            End If
        End If

        If BalanceBarcode = "0" Then
            cmbBalanceBarcode.Text = "بدون ميزان الباركود"
        Else
            cmbBalanceBarcode.Text = "ميزان الباركود"
        End If

        Dim bill_EndDate As String = "" : Dim Expired As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select  bill_EndDate,Expired,bill_ProductionDate,Vendorname from BilltINData where itm_id =N'" & txtIDItm.Text & "' and Stores=N'" & txtIDStores.Text & "' and bill_no = N'جرد'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            If dr(0) Is DBNull.Value Then
            Else
                bill_EndDate = dr(0)
            End If
            If dr(1) Is DBNull.Value Then
            Else
                cmb_Expired.Text = dr(1)
            End If
            If dr(2) Is DBNull.Value Then
            Else
                dtpDateItem.Text = Cls.R_date(dr(2).ToString)
            End If
            If dr(3) Is DBNull.Value Then
            Else
                cmbvendores.Text = dr(3).ToString
            End If
            If bill_EndDate <> "" Then
                dtpExpiration.Text = Cls.R_date(bill_EndDate)
            Else
                dtpExpiration.Text = ""
            End If
        Else
            cmb_Expired.Text = "بدون صلاحية"
        End If

        If QuickSearch = "0" Then
            chkQuickSearch.Checked = True
        Else
            chkQuickSearch.Checked = False
        End If

        If PriceIncludesVAT = "1" Then
            chkPriceIncludesVAT.Checked = True
        Else
            chkPriceIncludesVAT.Checked = False
        End If

        If Expired <> "بدون صلاحية" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_date from BilltINData where bill_date <> N'جرد' and itm_id =N'" & TxtPrc.Text & "' and Stores =N'" & txtIDStores.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Dim Xbill_date As String = ""
                If dr("bill_date") Is DBNull.Value Then
                Else
                    Xbill_date = dr("bill_date")
                End If
                dtpDateItem.Text = Cls.R_date(Xbill_date)
            End If
        End If

        SHOWPHOTO()

        If BarcodeMore = "YES" Then
            Dt_AddBill_BarcodeMore.Rows.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "select itm_id_More from BarcodeMore where itm_id =N'" & TxtPrc.Text & "'" : cmd.CommandText = S : dr = cmd.ExecuteReader
            Do While dr.Read
                dgvBarcodeMore.DataSource = Fn_AddBillBarcodeMore(dr(0).ToString)
            Loop
        End If

        GetAddItemsUnity()

        GetDataItemsAlternative()

        If chkEditFristStore.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select  qu from BilltINData where itm_id =N'" & txtIDItm.Text & "' and Stores=N'" & txtIDStores.Text & "' and bill_no = N'جرد'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                txtqunt.Text = dr(0).ToString
                txtqunt.Enabled = True
            End If
        End If

        EditActive = False

        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub

    Private Sub Button2_Click_2(sender As Object, e As EventArgs) Handles Button2.Click
        GetUpdateItemsAll()
    End Sub

    Private Sub txttinprice_KeyDown(sender As Object, e As KeyEventArgs) Handles txttinprice.KeyDown
        If ((e.KeyCode = Keys.F5)) Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub txtqunt_KeyDown(sender As Object, e As KeyEventArgs) Handles txtqunt.KeyDown
        If ((e.KeyCode = Keys.F5)) Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub txt_priseSal_KeyDown(sender As Object, e As KeyEventArgs) Handles txt_priseSal.KeyDown
        If ((e.KeyCode = Keys.F5)) Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub txtWholePrice_KeyDown(sender As Object, e As KeyEventArgs) Handles txtWholePrice.KeyDown
        If ((e.KeyCode = Keys.F5)) Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub txtWholeWholePrice_KeyDown(sender As Object, e As KeyEventArgs) Handles txtWholeWholePrice.KeyDown
        If ((e.KeyCode = Keys.F5)) Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub txtMinimumSalPrice_KeyDown(sender As Object, e As KeyEventArgs) Handles txtMinimumSalPrice.KeyDown
        If ((e.KeyCode = Keys.F5)) Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub txtrng_KeyDown(sender As Object, e As KeyEventArgs) Handles txtrng.KeyDown
        If ((e.KeyCode = Keys.F5)) Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub TxtPrc_KeyDown(sender As Object, e As KeyEventArgs) Handles TxtPrc.KeyDown
        If ((e.KeyCode = Keys.F5)) Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub GetUP_ParcodeItemsUnity()
        Dim ParcodeUnity As String = ""
        If NotUnityItemsProgram = "YES" Then
            ParcodeUnity = txtFindParcode.Text
            Dim PrcUnity As String = Cls.Get_Code_Value("ItemsUnity", "itm_id", "itm_id_Unity", ParcodeUnity)
            If PrcUnity <> "0" Then
                txtFindParcode.Text = PrcUnity
            End If
        End If
    End Sub

    Private Sub showData()
        Dim columnButton As New DataGridViewButtonColumn
        columnButton.Text = "عرض"
        columnButton.HeaderText = "عرض"
        columnButton.UseColumnTextForButtonValue = True
        columnButton.FlatStyle = FlatStyle.Popup
        columnButton.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter
        columnButton.DefaultCellStyle.ForeColor = Color.Maroon
        columnButton.Width = 40

        connectionStringOpen()
        Using da As New SqlDataAdapter("select itm_id  as [الباركود], group_name as [المجموعة], group_branch as [مجموعة فرعية] ,sname as [الصنف],CAST(TinPrice AS NVARCHAR(50)) as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],WholeWholePrice as [سعر جملة الجملة],MinimumSalPrice as [حد ادنى للسعر],rng as [حد الطلب] ,store as [المخزون],Stores as [المخزن] from items where itm_id <> ''", constring)
            Using ds As New DataSet
                da.Fill(ds, 0)
                DTGV.DataSource = ds.Tables(0)
                DTGV.Columns(0).Width = 90
                DTGV.Columns(1).Width = 100
                DTGV.Columns(2).Width = 100
                DTGV.Columns(3).Width = 250
                DTGV.Columns(4).Width = 60
                DTGV.Columns(5).Width = 60
                DTGV.Columns(6).Width = 60
                DTGV.Columns(7).Width = 55
                DTGV.Columns(8).Width = 55
                DTGV.Columns(9).Width = 55
                DTGV.Columns(10).Width = 55
                DTGV.Columns(11).Width = 55
                DTGV.Columns(12).Width = 55
                DTGV.Columns.Add(columnButton)
            End Using
        End Using
        Cn.Close()
        connectionStringOpen()

    End Sub

    Private Sub DTGV_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles DTGV.CellClick
        On Error Resume Next
        If DTGV.Columns(e.ColumnIndex).HeaderText = "عرض" Then
            SubGetData()
        End If
    End Sub

    Private Sub btnCloseDiscountedAfterPrice_Click(sender As Object, e As EventArgs) Handles btnCloseDiscountedAfterPrice.Click
        PanelOnlineStore.Top = 5000
    End Sub

    Private Sub btnDiscountedAfterPrice_Click(sender As Object, e As EventArgs) Handles btnDiscountedAfterPrice.Click
        PanelOnlineStore.Top = 300
        PanelOnlineStore.Left = 700
        PanelOnlineStore.Size = New System.Drawing.Size(480, 330)
    End Sub

    Private Sub txtDiscountedPrice_TextChanged(sender As Object, e As EventArgs) Handles txtDiscountedPrice.TextChanged
        MyVars.CheckNumber(txtDiscountedPrice)
    End Sub

    Private Sub txtDiscountedPrice2_TextChanged(sender As Object, e As EventArgs) Handles txtDiscountedPrice2.TextChanged
        MyVars.CheckNumber(txtDiscountedPrice2)
    End Sub

    Private Sub txtDiscountedPrice3_TextChanged(sender As Object, e As EventArgs) Handles txtDiscountedPrice3.TextChanged
        MyVars.CheckNumber(txtDiscountedPrice3)
    End Sub

    Private Sub txtLimitQuantity_TextChanged(sender As Object, e As EventArgs) Handles txtLimitQuantity.TextChanged
        MyVars.CheckNumber(txtLimitQuantity)
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub

    Private Sub cmbCompanies_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbCompanies.SelectedIndexChanged
        If EditActive = False Then
            If ConnectOnlineStore = "YES" Then
                If Not ConnectingOnlineStore() Is Nothing Then
                    lblCompanyId.Text = Cls.Get_Code_Value_More("Company", "Id", "Name =N'" & cmbCompanies.Text & "'")
                End If
                Cn.Close()
                connect()
            End If
        End If
    End Sub

    Private Sub GetGridViewFormatNumberWithSeparators()
        If ActivateFormatNumberWithSeparators = "YES" Then
            For i As Integer = 0 To DTGV.Rows.Count - 1

                DTGV.Rows(i).Cells(4).Value = FormatNumberWithSeparators(Double.Parse(Val(DTGV.Rows(i).Cells(4).Value.ToString())))
                DTGV.Rows(i).Cells(5).Value = FormatNumberWithSeparators(Double.Parse(Val(DTGV.Rows(i).Cells(5).Value.ToString())))
                DTGV.Rows(i).Cells(6).Value = FormatNumberWithSeparators(Double.Parse(Val(DTGV.Rows(i).Cells(6).Value.ToString())))
                DTGV.Rows(i).Cells(7).Value = FormatNumberWithSeparators(Double.Parse(Val(DTGV.Rows(i).Cells(7).Value.ToString())))
                DTGV.Rows(i).Cells(8).Value = FormatNumberWithSeparators(Double.Parse(Val(DTGV.Rows(i).Cells(8).Value.ToString())))
                DTGV.Rows(i).Cells(9).Value = FormatNumberWithSeparators(Double.Parse(Val(DTGV.Rows(i).Cells(9).Value.ToString())))
                DTGV.Rows(i).Cells(11).Value = FormatNumberWithSeparators(Double.Parse(Val(DTGV.Rows(i).Cells(11).Value.ToString())))
            Next
        End If
    End Sub

    Private Sub txtTinPriceAverage_TextChanged(sender As Object, e As EventArgs) Handles txtTinPriceAverage.TextChanged
        If ActionNumericSeparators = False Then
            txtTinPriceAverage.Text = FormatNumberWithSeparators(Val(txtTinPriceAverage.Text))
        End If
        MyVars.CheckNumber(txtTinPriceAverage)

    End Sub

    Private Sub GetNumericValueSeparators()
        ActionNumericSeparators = True

        txtqunt.Text = GetRemoveNumericSeparatorsValue(txtqunt.Text)
        txttinprice.Text = GetRemoveNumericSeparatorsValue(txttinprice.Text)
        txt_priseSal.Text = GetRemoveNumericSeparatorsValue(txt_priseSal.Text)
        txtWholePrice.Text = GetRemoveNumericSeparatorsValue(txtWholePrice.Text)
        txtWholeWholePrice.Text = GetRemoveNumericSeparatorsValue(txtWholeWholePrice.Text)
        txtMinimumSalPrice.Text = GetRemoveNumericSeparatorsValue(txtMinimumSalPrice.Text)
        txtTinPriceAverage.Text = GetRemoveNumericSeparatorsValue(txtTinPriceAverage.Text)

        ActionNumericSeparators = False
    End Sub

    Private Sub SetFormatNumberValueSeparators()
        If ActionNumericSeparators = False Then
            txtqunt.Text = FormatNumberWithSeparators(Val(txtqunt.Text))
            txttinprice.Text = FormatNumberWithSeparators(Val(txttinprice.Text))
            txt_priseSal.Text = FormatNumberWithSeparators(Val(txt_priseSal.Text))
            txtWholePrice.Text = FormatNumberWithSeparators(Val(txtWholePrice.Text))
            txtWholeWholePrice.Text = FormatNumberWithSeparators(Val(txtWholeWholePrice.Text))
            txtMinimumSalPrice.Text = FormatNumberWithSeparators(Val(txtMinimumSalPrice.Text))
            txtTinPriceAverage.Text = FormatNumberWithSeparators(Val(txtTinPriceAverage.Text))
        End If
    End Sub

    Private Sub txtHeight_KeyUp(sender As Object, e As KeyEventArgs) Handles txtHeight.KeyUp
        If e.KeyCode = 13 Then
            txtWidth.Focus()
            txtWidth.SelectAll()
        End If
    End Sub

    Private Sub txtWidth_KeyUp(sender As Object, e As KeyEventArgs) Handles txtWidth.KeyUp
        If e.KeyCode = 13 Then
            txtAltitude.Focus()
            txtAltitude.SelectAll()
        End If
    End Sub

    Private Sub txtAltitude_KeyUp(sender As Object, e As KeyEventArgs) Handles txtAltitude.KeyUp
        If e.KeyCode = 13 Then
            txtDensity.Focus()
            txtDensity.SelectAll()
        End If
    End Sub

    Private Sub btnCloseHeightWidth_Click(sender As Object, e As EventArgs) Handles btnCloseHeightWidth.Click
        PanelHeightWidth.Top = 5000
    End Sub

    Private Sub btnNetWeightView_Click(sender As Object, e As EventArgs) Handles btnNetWeightView.Click
        PanelHeightWidth.Top = 200
    End Sub

    Private Sub txtitm_id_Unity_KeyUp(sender As Object, e As KeyEventArgs) Handles txtitm_id_Unity.KeyUp
        If e.KeyCode = 13 Then
            btnAddUnityItems.PerformClick()
        End If
    End Sub

    Private Sub SetEnglish()
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.RightToLeftLayout = True
        Panel1.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.Text = ENT_Language.PerformingPurchasingOperationsInEnglish
        Label26.Text = ENT_Language.PerformingPurchasingOperationsInEnglish
        lblItems.Text = ENT_Language.ItemNameInEnglish
        Label4.Text = ENT_Language.ItemCategoryInEnglish
        Label16.Text = ENT_Language.ItemSubcategoryInEnglish
        Label10.Text = ENT_Language.ProductionDateInEnglish
        Label8.Text = ENT_Language.ExpiryTypeInEnglish
        chkPriceIncludesVAT.Text = ENT_Language.PriceIncludesVATInEnglish
        chkQuickSearch.Text = ENT_Language.ShowInSearchInEnglish
        Label21.Text = ENT_Language.WarehouseInEnglish
        Label43.Text = ENT_Language.VendorInEnglish
        Label6.Text = ENT_Language.QuantityInEnglish
        Label42.Text = ENT_Language.VATInEnglish
        Label7.Text = ENT_Language.PurchasePriceInEnglish
        Label11.Text = ENT_Language.SellingPriceInEnglish
        lblWholePrice.Text = ENT_Language.WholesalePriceInEnglish
        lblWholeWholePrice.Text = ENT_Language.BulkPriceInEnglish
        lblrng.Text = ENT_Language.ReorderLimitInEnglish
        lblMinimumSalPrice.Text = ENT_Language.MinPriceLimitInEnglish
        lblPrc.Text = ENT_Language.BarcodeInEnglish
        Label15.Text = ENT_Language.BarcodeInEnglish
        btnsave.Text = ENT_Language.Added
        Label22.Text = ENT_Language.UnityInEnglish
        Label27.Text = ENT_Language.NumberInEnglish
        Label29.Text = ENT_Language.PurchasePriceInEnglish
        Label37.Text = ENT_Language.PurchaseCostAvgInEnglish
        Label28.Text = ENT_Language.SellingPriceInEnglish
        Label34.Text = ENT_Language.UnitSizeInEnglish
        Label35.Text = ENT_Language.UnitBarcodeInEnglish
        LOGOBUTTON.Text = ENT_Language.ItemImageInEnglish
        Label36.Text = ENT_Language.BarcodeScaleInEnglish
        Label5.Text = ENT_Language.QuantityInEnglish
        LblTotal.Text = ENT_Language.InventoryInEnglish
        Label3.Text = ENT_Language.NumberItemsInEnglish
        BtnDelete.Text = ENT_Language.Delete
        Label20.Text = ENT_Language.BarcodeSearchInEnglish
        Label41.Text = ENT_Language.GroupSearchInEnglish

    End Sub

    Private Sub SetArabic()
        Me.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.RightToLeftLayout = True

    End Sub
End Class