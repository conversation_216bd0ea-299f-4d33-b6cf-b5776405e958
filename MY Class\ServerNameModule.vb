﻿Imports System.IO

Public Module ServerNameModule
    ' مسار الملف الافتراضي
    Private ReadOnly FilePath As String = Path.Combine(Application.StartupPath, "ServerName.txt")

    ''' <summary>
    ''' قراءة أسماء الخوادم من الملف
    ''' </summary>
    ''' <returns>مصفوفة تحتوي على أسماء الخوادم</returns>
    Public Function ReadServerNames() As String()
        Try
            ' التأكد من وجود الملف
            If File.Exists(FilePath) Then
                ' قراءة جميع السطور من الملف
                Return File.ReadAllLines(FilePath)
            Else
                ' إرجاع مصفوفة فارغة إذا لم يكن الملف موجودًا
                Return New String() {}
            End If
        Catch ex As Exception
            ' تسجيل الخطأ وإرجاع مصفوفة فارغة
            MessageBox.Show("خطأ في قراءة الملف: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return New String() {}
        End Try
    End Function

    ''' <summary>
    ''' إضافة اسم خادم جديد
    ''' </summary>
    ''' <param name="serverName">اسم الخادم للإضافة</param>
    ''' <returns>true إذا تمت الإضافة بنجاح, false خلاف ذلك</returns>
    Public Function AddServerName(serverName As String) As Boolean
        Try
            ' التأكد من عدم وجود اسم الخادم مسبقًا
            Dim existingServers = ReadServerNames()

            If Not existingServers.Contains(serverName) Then
                ' إنشاء قائمة جديدة وإضافة الاسم الجديد
                Dim updatedServers As New List(Of String)(existingServers)
                updatedServers.Add(serverName)

                ' حفظ القائمة المحدثة في الملف
                File.WriteAllLines(FilePath, updatedServers)
                Return True
            Else
                MessageBox.Show("اسم الخادم موجود بالفعل", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return False
            End If
        Catch ex As Exception
            MessageBox.Show("خطأ في إضافة اسم الخادم: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ''' <summary>
    ''' حذف اسم خادم
    ''' </summary>
    ''' <param name="serverName">اسم الخادم المراد حذفه</param>
    ''' <returns>true إذا تم الحذف بنجاح, false خلاف ذلك</returns>
    Public Function RemoveServerName(serverName As String) As Boolean
        Try
            ' قراءة الخوادم الحالية
            Dim existingServers = ReadServerNames().ToList()

            ' محاولة حذف اسم الخادم
            If existingServers.Remove(serverName) Then
                ' حفظ القائمة المحدثة في الملف
                File.WriteAllLines(FilePath, existingServers)
                Return True
            Else
                MessageBox.Show("اسم الخادم غير موجود", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return False
            End If
        Catch ex As Exception
            MessageBox.Show("خطأ في حذف اسم الخادم: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function
End Module