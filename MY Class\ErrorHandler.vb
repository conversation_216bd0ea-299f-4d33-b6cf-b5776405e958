Imports System.IO
Imports System.Text

''' <summary>
''' فئة مركزية لمعالجة الأخطاء وتسجيلها
''' تحل محل استخدام "On Error Resume Next" غير الآمن
''' </summary>
Public Class ErrorHandler
    Private Shared _logFilePath As String = Path.Combine(Application.StartupPath, "Logs", "ErrorLog.txt")
    Private Shared _isInitialized As Boolean = False

    ''' <summary>
    ''' تهيئة نظام معالجة الأخطاء
    ''' </summary>
    Public Shared Sub Initialize()
        Try
            ' إنشاء مجلد السجلات إذا لم يكن موجوداً
            Dim logDirectory As String = Path.GetDirectoryName(_logFilePath)
            If Not Directory.Exists(logDirectory) Then
                Directory.CreateDirectory(logDirectory)
            End If

            _isInitialized = True
            LogInfo("تم تهيئة نظام معالجة الأخطاء بنجاح")
        Catch ex As Exception
            ' في حالة فشل التهيئة، نستخدم Console للتسجيل
            Console.WriteLine($"خطأ في تهيئة نظام معالجة الأخطاء: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' تسجيل خطأ مع تفاصيل كاملة
    ''' </summary>
    Public Shared Sub LogError(message As String, ex As Exception, Optional context As String = "")
        Try
            Dim errorMessage As New StringBuilder()
            errorMessage.AppendLine($"[ERROR] {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
            errorMessage.AppendLine($"الرسالة: {message}")
            If Not String.IsNullOrEmpty(context) Then
                errorMessage.AppendLine($"السياق: {context}")
            End If
            errorMessage.AppendLine($"نوع الاستثناء: {ex.GetType().Name}")
            errorMessage.AppendLine($"رسالة الاستثناء: {ex.Message}")
            errorMessage.AppendLine($"مكان الخطأ: {ex.StackTrace}")
            If ex.InnerException IsNot Nothing Then
                errorMessage.AppendLine($"الاستثناء الداخلي: {ex.InnerException.Message}")
            End If
            errorMessage.AppendLine(New String("-"c, 80))

            WriteToLog(errorMessage.ToString())

            ' عرض رسالة للمستخدم (اختياري)
            ShowUserFriendlyError(message)

        Catch logEx As Exception
            ' في حالة فشل التسجيل، نستخدم Console
            Console.WriteLine($"خطأ في تسجيل الخطأ: {logEx.Message}")
            Console.WriteLine($"الخطأ الأصلي: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' تسجيل معلومات عامة
    ''' </summary>
    Public Shared Sub LogInfo(message As String, Optional context As String = "")
        Try
            Dim infoMessage As New StringBuilder()
            infoMessage.AppendLine($"[INFO] {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
            infoMessage.AppendLine($"الرسالة: {message}")
            If Not String.IsNullOrEmpty(context) Then
                infoMessage.AppendLine($"السياق: {context}")
            End If
            infoMessage.AppendLine()

            WriteToLog(infoMessage.ToString())

        Catch ex As Exception
            Console.WriteLine($"خطأ في تسجيل المعلومات: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' تسجيل تحذير
    ''' </summary>
    Public Shared Sub LogWarning(message As String, Optional context As String = "")
        Try
            Dim warningMessage As New StringBuilder()
            warningMessage.AppendLine($"[WARNING] {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
            warningMessage.AppendLine($"الرسالة: {message}")
            If Not String.IsNullOrEmpty(context) Then
                warningMessage.AppendLine($"السياق: {context}")
            End If
            warningMessage.AppendLine()

            WriteToLog(warningMessage.ToString())

        Catch ex As Exception
            Console.WriteLine($"خطأ في تسجيل التحذير: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' كتابة الرسالة إلى ملف السجل
    ''' </summary>
    Private Shared Sub WriteToLog(message As String)
        Try
            If _isInitialized Then
                File.AppendAllText(_logFilePath, message, Encoding.UTF8)
            Else
                Console.WriteLine(message)
            End If
        Catch ex As Exception
            Console.WriteLine($"خطأ في كتابة السجل: {ex.Message}")
            Console.WriteLine($"الرسالة الأصلية: {message}")
        End Try
    End Sub

    ''' <summary>
    ''' عرض رسالة خطأ مفهومة للمستخدم
    ''' </summary>
    Private Shared Sub ShowUserFriendlyError(message As String)
        Try
            ' يمكن تخصيص هذا حسب نوع التطبيق
            MessageBox.Show($"حدث خطأ: {message}" & vbCrLf & "يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.",
                          "خطأ في النظام",
                          MessageBoxButtons.OK,
                          MessageBoxIcon.Error)
        Catch ex As Exception
            ' في حالة فشل عرض MessageBox
            Console.WriteLine($"خطأ في عرض الرسالة: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' تنفيذ عملية مع معالجة آمنة للأخطاء
    ''' </summary>
    Public Shared Function SafeExecute(Of T)(operation As Func(Of T), defaultValue As T, Optional context As String = "") As T
        Try
            Return operation()
        Catch ex As Exception
            LogError($"خطأ في تنفيذ العملية", ex, context)
            Return defaultValue
        End Try
    End Function

    ''' <summary>
    ''' تنفيذ عملية مع معالجة آمنة للأخطاء (بدون قيمة إرجاع)
    ''' </summary>
    Public Shared Sub SafeExecute(operation As Action, Optional context As String = "")
        Try
            operation()
        Catch ex As Exception
            LogError($"خطأ في تنفيذ العملية", ex, context)
        End Try
    End Sub

    ''' <summary>
    ''' تنظيف ملفات السجل القديمة
    ''' </summary>
    Public Shared Sub CleanupOldLogs(Optional daysToKeep As Integer = 30)
        Try
            Dim logDirectory As String = Path.GetDirectoryName(_logFilePath)
            If Directory.Exists(logDirectory) Then
                Dim files As String() = Directory.GetFiles(logDirectory, "*.txt")
                For Each file As String In files
                    Dim fileInfo As New FileInfo(file)
                    If fileInfo.CreationTime < DateTime.Now.AddDays(-daysToKeep) Then
                        File.Delete(file)
                        LogInfo($"تم حذف ملف السجل القديم: {fileInfo.Name}")
                    End If
                Next
            End If
        Catch ex As Exception
            LogError("خطأ في تنظيف ملفات السجل القديمة", ex)
        End Try
    End Sub
End Class
