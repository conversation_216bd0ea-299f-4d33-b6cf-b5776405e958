﻿Imports CrystalDecisions.CrystalReports.Engine
Public Class frmShowAdditional
    Dim WithEvents BS As New BindingSource
    Private Sub frmShowAdditional_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbNameEmployee)
        GetData()
        If PermtionName <> "مدير" Then
            btnDelete.Visible = False
            btnEdit.Visible = False
        End If
    End Sub

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = Cls.Get_Select_Grid_S("AddID as[مسلسل],NameEmployee as[أسم الموظف],Month as[الشهر],SDate as[التاريخ],TimeAttendance as[وقت الحضور],TimeLeave as[وقت الانصراف],HoursBasicLabor as[ساعات العمل الاساسية],AdditionalHours as[ساعات الاضافى]", "Additional", "AddID <> N''")
        If chkAll.Checked = False Then
            If cmbNameEmployee.Text <> Nothing Then
                S = S & " and NameEmployee =N'" & cmbNameEmployee.Text.Trim & "'"
            End If
            If cmbMonth.Text <> Nothing Then
                S = S & " and Month =N'" & cmbMonth.Text.Trim & "'"
            End If
        End If
        If chkDate.Checked = False Then
            S = S & " AND SDate >=N'" & Cls.C_date(dtpFrom.Text) & "' AND SDate <=N'" & Cls.C_date(dtpTo.Text) & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [مسلسل]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [التاريخ]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [أسم الموظف]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
    End Sub

    Private Sub btnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnShow.Click
        GetData()
    End Sub

    Private Sub btnEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnEdit.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        EditItmId = DataGridView1.SelectedRows(0).Cells(0).Value
        Action = "Edit"
        frmAdditional.Show()
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim NumberID As String
            NumberID = DataGridView1.SelectedRows(0).Cells(0).Value


            cmd.CommandText = "delete from Additional where AddID =N'" & NumberID & "'" : cmd.ExecuteNonQuery()
        Next
        GetData()
    End Sub

    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Dim txtFROMDate As String
        Dim txtToDate As String
        Dim f As New Frm_PrintReports
        Dim rpt As New rptAdditional
        Dim txt As TextObject
        'On Error Resume Next
        GETSERVERNAMEANDDATABASENAME(rpt, DataBaseName, PasswordServer, UserNameServer)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If cmbNameEmployee.Text <> Nothing And cmbMonth.Text <> Nothing Then
            rpt.RecordSelectionFormula = "{Additional.NameEmployee} like N'" & cmbNameEmployee.Text & "N'" & "AND {Additional.Month} like N'" & cmbMonth.Text & "'"
        End If
        If cmbNameEmployee.Text <> Nothing Then
            rpt.RecordSelectionFormula = "{Additional.NameEmployee} =N'" & cmbNameEmployee.Text & "'"
        End If
        If cmbMonth.Text <> Nothing Then
            rpt.RecordSelectionFormula = "{Additional.Month} =N'" & cmbMonth.Text & "'"
        End If
        If cmbNameEmployee.Text = Nothing And cmbMonth.Text = Nothing Then
            txt = rpt.Section1.ReportObjects("txtDate")
            txt.Text = "خلال شهر  " & cmbMonth.Text
        End If
        AddReportView()
        Dim txtname, txtNameAr, txtNameEn As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير بالاضافى"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        f.CrystalReportViewer1.ReportSource = rpt
        f.CrystalReportViewer1.Zoom(95%)
        f.CrystalReportViewer1.RefreshReport()
        f.Text = "تقرير بالاضافى"
        f.Show()
    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbNameEmployee.Enabled = False
            cmbMonth.Enabled = False
            cmbNameEmployee.SelectedIndex = -1
            cmbMonth.SelectedIndex = -1
        ElseIf chkAll.Checked = False Then
            cmbNameEmployee.Enabled = True
            cmbMonth.Enabled = True
            cmbNameEmployee.SelectedIndex = -1
            cmbMonth.SelectedIndex = -1
        End If
    End Sub

    Private Sub chkDate_CheckedChanged(sender As Object, e As EventArgs) Handles chkDate.CheckedChanged
        If chkDate.Checked = True Then
            dtpFrom.Enabled = False
            dtpTo.Enabled = False
        Else
            dtpFrom.Enabled = True
            dtpTo.Enabled = True
        End If
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class