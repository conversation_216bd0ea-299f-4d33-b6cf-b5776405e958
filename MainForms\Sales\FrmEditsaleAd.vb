﻿Imports System.Windows.Forms
Imports System.Data
Imports System.Data.SqlClient
Imports vb = Microsoft.VisualBasic
Imports CrystalDecisions.CrystalReports.Engine
Public Class FrmEditsaleAd
    Dim WithEvents BS As New BindingSource
    Dim Amntcredit, Amntdebit, AmntcreditPrevious, AmntdebitPrevious As Double
    Dim R() As String
    Dim Mnm As String
    Dim _totalItem As Double
    Dim aray_itm_id As New ArrayList
    Dim aray_sname As New ArrayList
    Dim aray_Stores As New ArrayList
    Dim aray_qu As New ArrayList
    Dim aray_TinPrice As New ArrayList
    Dim aray_itm_Unity As New ArrayList
    Dim aray_qu_unity As New ArrayList
    Dim TotalPriceBeforeAverage As Double
    Dim billnoID As String
    Dim DiscountTax As String
    Dim bill_NoTax As String
    Dim ShowTax As String = mykey.GetValue("SalesTax", "0")
    Dim TextNotActivateEditSalesPrice As String = mykey.GetValue("TextNotActivateEditSalesPrice", "NO")
    Dim NotLoadItemDataScreensOpen As String = mykey.GetValue("NotLoadItemDataScreensOpen", "NO")
    Dim AddBillAuto As String = mykey.GetValue("AddBillAuto", "NO")
    Dim ValueDiscountTax As String = mykey.GetValue("ValueDiscountTax", "0")
    Dim ExpirationDate As String = mykey.GetValue("ExpirationDate", "NO")
    Dim SalesBillNotDiscount As String = mykey.GetValue("SalesBillNotDiscount", "NO")
    Dim SalesBillNotDiscountBill As String = mykey.GetValue("SalesBillNotDiscountBill", "NO")
    Dim ShowCustomerBalanceSalesScreen As String = mykey.GetValue("ShowCustomerBalanceSalesScreen", "NO")
    Dim SalesPricePublic As String = mykey.GetValue("SalesPricePublic", "NO")
    Dim RateBankExpensesPaidByVisa As String = mykey.GetValue("RateBankExpensesPaidByVisa", "0")
    Dim AcceptedSalePriceZero As String = mykey.GetValue("AcceptedSalePriceZero", "NO")

    Dim OffersStatement, Maxbill_date, Offersbill_No, RatePriceOffers, PriceOffers, PriceOffersBefore, priceItems, Offers_Qunt, Total_Offers_Qunt As String
    Dim Xbill_No As String
    Dim pay As String
    Dim Vendorname As String = ""
    Dim Cls_Altfiqith As New Class_Altfiqith
    Dim CustomerAddress As String = ""
    Dim CustomerTel As String = ""
    Dim DiscountsTin As Double = 0
    Dim DiscountsValue As Double = 0
    Dim TotalDiscountsValue As Double = 0
    Dim StateDisc As String = ""
    Dim PhoneEmployee As String = ""
    Dim TotalAccountAfterInvoice As String = ""
    Dim DiscTotal As Double
    Dim ActionPayCust As Boolean = False
    Dim SearchItemName As String
    Dim ActivFocus As Boolean = False
    Dim Sales_Bill As String = ""
    Dim BillsalData As String = ""
    Dim vst As String = ""
    Dim Vst_disc As String = ""
    Dim Actions As Boolean = False
    Dim Treasury_Code_ID As Integer = 0

    Private Sub FillData()
        'Try
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If rdoReceivingPermission.Checked = True Then
            cmd.CommandText = "select ITM_ID AS [الباركود], itm_cat as [المجموعة] ,itm_name as [اسم الصنف],price as [السعر] ,qu as [الكمية1],qu_unity as [الكمية],itm_Unity as [الوحدة] ,totalprice as [الأجمالي],Stores as [أسم المخزن],UserName as [حجم الوحدة],bill_EndDate as [تاريخ الصلاحية],bill_no_Expired as [رقم الصلاحية],ValueVAT as [قيمة الضريبة],RateVAT as [نسبة الضريبة],Discounts as [الخصم],DiscountsValue as [قيمة الخصم],Discount_Price_After as [سعر البيع],DiscountsTin as [DiscountsTin],BeforeVAT [قبل الضريبة],StateDisc [نسبة او قيمة خصم الصنف] from Receive_BillsalData where bill_no =N'" & EditItmId & "'"
        Else
            cmd.CommandText = "select ITM_ID AS [الباركود], itm_cat as [المجموعة] ,itm_name as [اسم الصنف],price as [السعر] ,qu as [الكمية1],qu_unity as [الكمية],itm_Unity as [الوحدة] ,totalprice as [الأجمالي],Stores as [أسم المخزن],UserName as [حجم الوحدة],bill_EndDate as [تاريخ الصلاحية],bill_no_Expired as [رقم الصلاحية],ValueVAT as [قيمة الضريبة],RateVAT as [نسبة الضريبة],Discounts as [الخصم],DiscountsValue as [قيمة الخصم],Discount_Price_After as [سعر البيع],DiscountsTin as [DiscountsTin],BeforeVAT [قبل الضريبة],StateDisc [نسبة او قيمة خصم الصنف] from BillsalData where bill_no =N'" & EditItmId & "'"
        End If
        dr = cmd.ExecuteReader
        Dgv_Add.DataSource = Cls.PopulateDataView(dr)
        Dgv_Add.Columns(1).Visible = False
        Dgv_Add.Columns(4).Visible = False
        Dgv_Add.Columns(9).Visible = False
        Dgv_Add.Columns(10).Visible = False
        Dgv_Add.Columns(11).Visible = False
        Dgv_Add.Columns(12).Visible = False
        Dgv_Add.Columns(13).Visible = False
        Dgv_Add.Columns(14).Visible = True
        Dgv_Add.Columns(15).Visible = False
        Dgv_Add.Columns(16).Visible = False
        Dgv_Add.Columns(17).Visible = False
        Dgv_Add.Columns(19).Visible = False
        If ShowDiscountRateItemSales = "NO" Then
            Dgv_Add.Columns(3).Visible = True
            Dgv_Add.Columns(16).Visible = False
        Else
            Dgv_Add.Columns(3).Visible = False
            Dgv_Add.Columns(16).Visible = True
        End If
        txtCountItems.Text = Dgv_Add.Rows.Count

        Dim UnitySize As String = ""
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select UnitySize_Name from View_ItemsUnitySize where itm_id=N'" & Dgv_Add.Rows(i).Cells(0).Value & "' and Unity_Name=N'" & Dgv_Add.Rows(i).Cells(6).Value & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                UnitySize = dr(0).ToString
            End If

            Dgv_Add.Rows(i).Cells(9).Value = UnitySize
        Next

        Dim XVendorname, Xbill_date, Xbilltime, Xtotalpricebeforedisc, Xdisc, Xtotalpriceafterdisc, Xstat, Xbey, XSTAYING, XSalesTax, XNotes, XEmpName, XDeliveryService As String
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If rdoReceivingPermission.Checked = True Then
            cmd.CommandText = "select bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,SalesTax,Notes,EmpName,DiscountTax,bill_NoTax,ValueVAT,RateValues,Treasury_Code,DeliveryService from Receive_Sales_Bill where bill_no =N'" & EditItmId & "'"
        Else
            cmd.CommandText = "select bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,SalesTax,Notes,EmpName,DiscountTax,bill_NoTax,ValueVAT,RateValues,Treasury_Code,DeliveryService from Sales_Bill where bill_no =N'" & EditItmId & "'"
        End If
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Xbill_No = dr("bill_No").ToString
            XVendorname = dr("Vendorname").ToString
            Xbill_date = dr("bill_date").ToString
            Xbilltime = dr("billtime").ToString
            Xtotalpricebeforedisc = dr("totalpricebeforedisc").ToString
            Xdisc = dr("disc").ToString
            Xtotalpriceafterdisc = dr("totalpriceafterdisc").ToString
            Xstat = dr("stat").ToString
            Xbey = dr("bey").ToString
            XSTAYING = dr("STAYING").ToString
            XSalesTax = dr("SalesTax").ToString
            If dr(11) Is DBNull.Value Then
            Else
                XNotes = dr("Notes").ToString
            End If
            XEmpName = dr("EmpName").ToString
            DiscountTax = dr("DiscountTax").ToString
            bill_NoTax = dr("bill_NoTax").ToString
            txtTotalValueVAT.Text = dr("ValueVAT").ToString
            txtSalestax.Text = dr("RateValues").ToString
            Dim Treasury_Code_ID As String = dr("Treasury_Code").ToString
            XDeliveryService = dr("DeliveryService").ToString
            cmbTreasuryName.Text = Cls.Get_Code_Value_Branch_More("Treasury", "Treasury_Name", "Treasury_Code=N'" & Treasury_Code_ID & "'")

            If Xstat = "نقداً" Then
                ChkCash.Checked = True
            End If
            If Xstat = "آجل" Then
                ChkState.Checked = True
            End If
            If Xstat = "فيزا" Then
                chkVisa.Checked = True
            End If
            txtbillno.Text = Xbill_No
            cmbvendores.Text = XVendorname
            DateTimePicker1.Text = Cls.R_date(Xbill_date)
            txttotalpeforedisc.Text = Xtotalpricebeforedisc
            txttotalafterdisc.Text = Xtotalpriceafterdisc
            txtdisc.Text = Xdisc
            txtstaying.Text = XSTAYING
            txtSalestax.Text = XSalesTax
            txtNotes.Text = XNotes
            cmbEmployees.Text = XEmpName
            txtpaying.Text = Xbey
            txtDeliveryServiceView.Text = XDeliveryService
        End If


        aray_itm_id.Clear() : aray_Stores.Clear() : aray_qu.Clear() : aray_TinPrice.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select itm_id,Stores,qu from BillsalData where bill_no =N'" & EditItmId & "'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_itm_id.Add(dr(0))
            aray_Stores.Add(dr(1))
            aray_qu.Add(dr(2))
        Loop

        SumAllPrice()
        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub

    Private Sub sales_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        Cls.fill_combo_Branch("Treasury", "Treasury_Name", cmbTreasuryName)

        Treasury_Code = Cls.Get_Code_Value("Users", "Treasury_Code", "UserName", UserName)
        cmbTreasuryName.Text = Cls.Get_Code_Value_Branch_More("Treasury", "Treasury_Name", "Treasury_Code=N'" & Treasury_Code & "'")
        Treasury_Code_ID = Cls.Get_Code_Value_Branch_More("Treasury", "Treasury_Code", "Treasury_Name=N'" & cmbTreasuryName.Text & "'")
        If TreasuryControl = "YES" Then
            lblTreasuryName.Visible = True
            cmbTreasuryName.Visible = True
        Else
            lblTreasuryName.Visible = False
            cmbTreasuryName.Visible = False
        End If

        If ShowDeliveryServiceSales = "YES" Then
            lblDeliveryServiceView.Visible = True
            txtDeliveryServiceView.Visible = True
        End If

        If Dismissal_Notice = True Then
            rdoReceivingPermission.Checked = True
        Else
            rdoReceivingPermission.Checked = False
        End If
        Dismissal_Notice = False

        'Me.MdiParent = MDIParent1
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        FillData()
        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", cmbvendores)
        End If
        Bra.Fil("groups", "g_name", cmbcats)
        If NotLoadItemDataScreensOpen = "NO" Then
            Cls.fill_combo_QuickSearch("Items", "sname", cmbname)
        End If
        Cls.fill_combo_Branch("stores", "store", cmbStores)
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployees)
        cmbvendores.Items.Add("نقداً")
        PanelPrice.Top = 10000
        PanelEmployees.Top = 10000
        cmbStores.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        lblTime.Top = 1000
        TxtHour.Top = 1000
        PaneldiscBill.Top = 10000
        PanelSearch.Top = 5000
        GetTextNotActivateEditSalesPrice()
        GetShowDiscountRateItemSales()
        GetDateNotBeenActivatedPrograms(DateTimePicker1)
        GetDateNotBeenActivatedOutcome()

    End Sub

    Private Sub GetDateNotBeenActivatedOutcome()
        If PermtionName <> "مدير" Then
            If DateNotBeenActivatedOutcome = "NO" Then
                DateTimePicker1.Enabled = True
            Else
                DateTimePicker1.Enabled = False
            End If
        End If
    End Sub

    Private Sub cmbvendores_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbvendores.KeyUp
        If e.KeyCode = 13 Then
            txtbillno.Focus()
        End If
    End Sub

    Private Sub txtbillno_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            DateTimePicker1.Focus()
        End If
    End Sub

    Private Sub DateTimePicker1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            txtprc.Focus()
        End If
    End Sub

    Private Sub cmbcats_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            If cmbcats.Text.Trim = "" Then
                txtdisc.Focus()
            Else
                cmbname.Focus()
            End If
        End If
    End Sub

    Private Sub txtprice_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtprice.KeyUp
        If e.KeyCode = 13 Then
            txtquntUnity.Focus()
        End If
    End Sub

    Private Sub txtprice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtprice.TextChanged
        MyVars.CheckNumber(txtprice)
        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If
        TotalBeforeVAT = Val(txtprice.Text) * Val(txtquntUnity.Text)
        If ShowValueVAT = "YES" Then
            TotalValueVAT = Format(Val(TotalBeforeVAT) * Val(ItemsRateVAT) / 100, "Fixed")
        End If
        txtTotal.Text = Math.Round(TotalValueVAT, 2) + Math.Round(TotalBeforeVAT, 2)
        If ShowTax = "0" Then
            txtSalestax.Text = ItemsRateVAT
        End If
        txtTotal.Text = Math.Round(Convert.ToDouble(txtTotal.Text), 2)
        sumdisc1()
    End Sub

    Private Sub sumdisc1()
        Dim DiscVal As Double
        Dim TotalPriseQunt As Double = Val(txtprice.Text) * Val(txtquntUnity.Text)
        If ChkCent2.Checked = True Then
            DiscVal = Val((Val(TotalPriseQunt) * (100 - Val(txtdiscBill.Text))) / 100)
            DiscVal = Math.Round(DiscVal, 2)
        ElseIf ChkVal.Checked = True Then
            DiscVal = Val(TotalPriseQunt) - Val(txtdiscBill.Text)
        End If

        txtTotal.Text = Math.Round(TotalValueVAT, 2) + Math.Round(DiscVal, 2)

        If ChkCent2.Checked = True Then
            StateDisc = "نسبة"
            lblDiscount_Price_After.Text = Val((Val(Val(txtprice.Text) * Val(1)) * (100 - Val(txtdiscBill.Text))) / 100)
            Dim XVal As String = Format(Val(txtprice.Text) * Val(txtdiscBill.Text) / 100, "Fixed")
            DiscountsValue = Val(XVal) * Val(txtquntUnity.Text)
        Else
            StateDisc = "قيمة"
            DiscountsValue = Val(txtdiscBill.Text)
        End If
    End Sub

    Private Sub txtqunt_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtqunt.KeyUp
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub txtqunt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtqunt.TextChanged
        MyVars.CheckNumber(txtqunt)

        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If
        TotalBeforeVAT = Val(txtprice.Text) * Val(txtquntUnity.Text)
        If ShowValueVAT = "YES" Then
            TotalValueVAT = Format(Val(TotalBeforeVAT) * Val(ItemsRateVAT) / 100, "Fixed")
        End If
        txtTotal.Text = Math.Round(TotalValueVAT, 2) + Math.Round(TotalBeforeVAT, 2)
        If ShowTax = "0" Then
            txtSalestax.Text = ItemsRateVAT
        End If
        txtTotal.Text = Math.Round(Convert.ToDouble(txtTotal.Text), 2)
        sumdisc1()
    End Sub
    Function ValidateTextAdd() As Boolean

        If txtbillno.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الفاتورة", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        If txtprc.Text = "" Then MsgBox("فضلا ادخل الباركود", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
        If cmbcats.Text = "" Then MsgBox("فضلا حدد المجموعة", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbname.Text = "" Then MsgBox("فضلا أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbname.Focus() : Return False
        If cmbStores.Text = "" Then MsgBox("من فضلك اختر المخزن ", MsgBoxStyle.Exclamation) : cmbStores.Focus() : Return False
        If cmbUnityItems.Text = "" Then MsgBox("فضلا أدخل وحدة القياس", MsgBoxStyle.Exclamation) : cmbUnityItems.Focus() : Return False
        If Val(txtquntUnity.Text.Trim) = 0 Then MsgBox("فضلا أدخل الكمية ", MsgBoxStyle.Exclamation) : txtquntUnity.Focus() : Return False
        If AcceptedSalePriceZero = "NO" Then
            If Val(txtprice.Text.Trim) = 0 Then MsgBox("فضلا أدخل السعر", MsgBoxStyle.Exclamation) : txtprice.Focus() : Return False
        End If
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where itm_id =N'" & txtprc.Text.Trim & "'and group_name =N'" & cmbcats.Text.Trim & "'and sname =N'" & cmbname.Text.Trim & "'and Stores =N'" & cmbStores.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H = 0 Then
                MsgBox("الصنف غير مطابق من فضلك راجع الصنف ", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
            End If


            Dim ReturnInvoice As String = mykey.GetValue("ReturnInvoice", "NO")
            If ReturnInvoice = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select count(*) from Sales_Bill where bill_no =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
                If H = 0 Then
                    MsgBox("رقم الفاتورة غير مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
                ElseIf H > 0 Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select count(*) from Sales_Bill where bill_no =N'" & txtbillno.Text.Trim & "' and Vendorname =N'" & cmbvendores.Text.Trim & "'" : H = cmd.ExecuteScalar
                    If H = 0 Then
                        MsgBox("رقم الفاتورة غير مسجل باسم هذا العميل", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
                    End If
                End If
            End If

            'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            'cmd.CommandText = "select count(*) from IM_Bsal where bill_no =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
            'If H > 0 Then
            '    MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
            'End If

            For i As Integer = 0 To Dt_AddBill.Rows.Count - 1
                If Dgv_Add.Rows(i).Cells(0).Value = txtprc.Text.Trim Then MsgBox("صنف مكرر بنفس الفاتورة", MsgBoxStyle.Exclamation) : txtprc.Focus() : txtprc.SelectAll() : Return False
            Next

            If cmbvendores.Text <> "نقداً" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select Vendorname from Customers where Vendorname=N'" & cmbvendores.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = False Then
                    MsgBox("هذا العميل غير مسجل مسبقا", MsgBoxStyle.Exclamation)
                    cmbvendores.Focus() : Return False
                End If
            End If


            Dim MinimumSalPrice As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select MinimumSalPrice from items where sname=N'" & cmbname.Text & "' and Stores=N'" & cmbStores.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
                Return False
            Else
                If dr(0) Is DBNull.Value Then
                Else
                    MinimumSalPrice = dr(0)
                End If

                If txtprice.Text < MinimumSalPrice Then
                    MsgBox("سعر البيع وصل للحد الادنى", MsgBoxStyle.Information)
                    Return False
                End If
            End If

            '==========================================================================================

            Dim ActivationEmail As String = mykey.GetValue("ActivationEmail", "NO")
            Dim XMSG As String
            Dim QuntFrom As Double = 0
            Dim QuntAdd As Double = 0
            Dim XXstore As Double = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)
            Dim Xstore As Double = 0
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                If Dgv_Add.Rows(i).Cells(0).Value = txtprc.Text Then
                    QuntFrom += Dgv_Add.Rows(i).Cells(5).Value
                End If
            Next
            For M As Integer = 0 To aray_itm_id.Count - 1
                If aray_itm_id(M) = txtprc.Text Then
                    QuntAdd += aray_qu(M)
                End If
            Next

            If XXstore < QuntAdd Then
                Xstore = QuntFrom + QuntAdd - XXstore
            Else
                Xstore = XXstore - QuntFrom + QuntAdd
            End If


            Dim UseOnlySales As String = mykey.GetValue("UseOnlySales", "NO")
            If UseOnlySales <> "YES" Then
                If Val(txtqunt.Text) > Xstore Then
                    MsgBox("الكمية بالمخزن لا تكفي الكمية المباعة", MsgBoxStyle.Critical) : Return False
                End If

                If Xstore < 1 Then
                    XMSG = MsgBox("الكمية بالمخزن قد نفذت أو أنك لم تقم بتسجيل آخر عملية مشتريات " & Environment.NewLine & " هل تريد إتمام عملية البيع ؟", MsgBoxStyle.OkCancel + MsgBoxStyle.MsgBoxRight + MsgBoxStyle.Exclamation) : txtprice.Focus()
                    If ActivationEmail = "YES" Then
                        SendEmail("الكمية بالمخزن قد نفذت", txtprc.Text, cmbname.Text, Xstore)
                    End If
                    If XMSG = vbCancel Then Return False
                End If

                Dim XMSG2 As String
                If Xstore - Val(txtqunt.Text) = 0 Then
                    XMSG2 = MsgBox("الكمية بالمخزن ستنفذ هل تريد الأستمرار", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
                    If XMSG2 = vbCancel Then Return False
                End If

                If Xstore - Val(txtqunt.Text) < IM.Get_Itm_Range(txtprc.Text.Trim, cmbStores.Text.Trim) Then
                    If ActivationEmail = "YES" Then
                        SendEmail("الكمية وصلت للحد الادنى", txtprc.Text, cmbname.Text, Xstore)
                    End If
                    XMSG = MsgBox("الكمية بالمخزن قد وصلت للحد الأدنى", MsgBoxStyle.Information) : txtprice.Focus()
                End If
            End If


            '=====================================================================
            If ExpirationDate = "YES" Then
                Try
                    Dim EndDate As String = ""
                    Dim bill_EndDate As String = ""
                    Dim Expired As String = Cls.Get_Code_Value_Branch_More("BilltINData", "Expired", "itm_name=N'" & cmbname.Text.Trim & "' and Stores =N'" & cmbStores.Text.Trim & "'")
                    If Expired <> "بدون صلاحية" Then
                        Dim aray_1 As New ArrayList
                        aray_1.Clear()
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "select bill_EndDate from BilltINData where itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'"
                        dr = cmd.ExecuteReader
                        Do While dr.Read = True
                            aray_1.Add(dr(0))
                        Loop

                        For i As Integer = 0 To aray_1.Count - 1
                            bill_EndDate = aray_1(i).ToString()
                            Dim DateTimeNow As String = DateTime.Now.ToString("yyyyMMdd")

                            If bill_EndDate <> "" Then
                                If bill_EndDate <= DateTimeNow Then
                                    MsgBox("هذا الصنف قد انتهت صلاحيتة", MsgBoxStyle.Critical)
                                    Return False
                                End If
                            End If
                        Next
                    End If

                    Dim qu_expired As String = "" : Dim Total_expired As String = "" : Dim Xqunt As Double = 0

                    qu_expired = Cls.Get_Code_Value_Stores_More("BilltINData", "qu_expired", "bill_no_Expired =N'" & txtbill_no_Expired.Text & "'")

                    Xqunt = Val(txtquntUnity.Text)
                    If NotUnityItemsProgram = "YES" Then
                        NumberPieces = Cls.Get_Code_Value_Stores_More("ItemsUnity", "NumberPieces", "itm_id =N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'")
                        If NumberPieces <> 1 Then
                            Xqunt = Val(NumberPieces) * Val(txtquntUnity.Text)
                        End If
                    End If

                    Total_expired = Val(Xqunt) - Val(qu_expired)
                    If Total_expired > 0 Then
                        If NumberPieces <> 1 Then
                            Total_expired = Val(Total_expired) / Val(NumberPieces)
                            qu_expired = Val(qu_expired) / Val(NumberPieces)
                        End If
                        If qu_expired <> 0 Then
                            MsgBox("من فضلك جزء الصنف على مرتين لانه يوجد كمية من الصنف المباع بتاريخ صلاحية و كمية اخرى من نفس الصنف بتاخ صلاحية اخر", MsgBoxStyle.Critical)
                            MsgBox("الكمية الاولى " & qu_expired & "  |  الكمية الثانية  " & Total_expired & "", MsgBoxStyle.Critical)
                            Return False
                        Else
                            Return True
                        End If
                    End If

                Catch ex As Exception
                    ErrorHandling(ex, Me.Text)
                    If NetworkName = "Yes" Then
                        If UseExternalServer = "Yes" Then
                            connect()
                        End If
                    End If
                End Try
            End If

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        Return True
    End Function

    Function ValidateTextSave() As Boolean
        If cmbvendores.Text = "" Then MsgBox("فضلا أختر العميل", MsgBoxStyle.Exclamation) : cmbvendores.Focus() : Return False
        If txtbillno.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الفاتورة", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        If txtdisc.Text = "" Then
            txtdisc.Text = "0"
        End If
        If txtpaying.Text = "" Then
            txtpaying.Text = "0"
        End If
        If Dgv_Add.Rows.Count < 1 Then MsgBox("فضلا أكمل بيانات الفاتورة", MsgBoxStyle.Exclamation) : cmbvendores.Focus() : Return False

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Sales_Bill where bill_no =N'" & txtbillno.Text & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                If txtbillno.Text.Trim <> EditItmId Then
                    MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
                End If
            End If

            If cmbvendores.Text <> "نقداً" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select Vendorname from Customers where Vendorname=N'" & cmbvendores.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = False Then
                    MsgBox("هذا العميل غير مسجل مسبقا", MsgBoxStyle.Exclamation)
                    cmbvendores.Focus() : Return False
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        Return True
    End Function

    Friend Sub DTV_Width()
        Try
            If Dgv_Add.Rows.Count > 0 Then
                Dgv_Add.Columns(0).Width = 90
                Dgv_Add.Columns(1).Width = 90
                Dgv_Add.Columns(2).Width = 160
                Dgv_Add.Columns(3).Width = 60
                Dgv_Add.Columns(4).Width = 60
                Dgv_Add.Columns(5).Width = 75
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub BtnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAdd.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateTextAdd() = False Then Exit Sub
        'IM.Store(txtprc.Text.Trim, cmbStores.Text)

        If cmbcats.Text = "" Or cmbname.Text = "" Or txtprice.Text = "" Or txtquntUnity.Text = "" Then
            MsgBox("اكمل البيانات")
            Exit Sub
        End If

        Try
            If ChkCent2.Checked = True Then
                StateDisc = ChkCent2.Text
            Else
                StateDisc = ChkVal2.Text
            End If

            Dim MoreItemsInvoice As String = mykey.GetValue("AddMoreInvoiceSeparately", "YES")
            If AddMoreInvoiceSeparately = "YES" Then

            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                If txtbill_no_Expired.Text = "0" Then
                    cmd.CommandText = "delete from TmpBillsalData where ITM_ID =N'" & txtprc.Text.Trim & "'" : cmd.ExecuteNonQuery()
                Else
                    cmd.CommandText = "delete from TmpBillsalData where ITM_ID =N'" & txtprc.Text.Trim & "' and bill_no_Expired =N'" & txtbill_no_Expired.Text.Trim & "' " : cmd.ExecuteNonQuery()
                End If
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into TmpBillsalData (bill_no,itm_id,itm_cat,itm_name,price,qu,qu_unity,itm_Unity,totalprice,Stores,UserName,bill_date,bill_EndDate,bill_no_Expired,RateVAT,ValueVAT,Discounts,DiscountsValue,Discount_Price_After,DiscountsTin,BeforeVAT,StateDisc)"
            S = S & " values (N'" & txtbillno.Text & "',N'" & txtprc.Text & "',N'" & cmbcats.Text.Trim & "',N'" & cmbname.Text.Trim & "',N'" & txtprice.Text & "'," & Val(txtqunt.Text) & "," & Val(txtquntUnity.Text) & ",N'" & cmbUnityItems.Text & "'," & Val(txtTotal.Text) & ",N'" & cmbStores.Text.Trim & "',N'" & UserName & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & txtbill_EndDate.Text & "',N'" & txtbill_no_Expired.Text & "',N'" & ItemsRateVAT & "',N'" & TotalValueVAT & "',N'" & txtdiscBill.Text & "',N'" & DiscountsValue & "',N'" & lblDiscount_Price_After.Text & "',N'" & DiscountsTin & "',N'" & TotalBeforeVAT & "',N'" & StateDisc & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ITM_ID AS [الباركود], itm_cat as [المجموعة] ,itm_name as [اسم الصنف],price as [السعر] ,qu as [الكمية1],qu_unity as [الكمية],itm_Unity as [الوحدة] ,totalprice as [الأجمالي],Stores as [أسم المخزن],UserName as [حجم الوحدة],bill_EndDate as [تاريخ الصلاحية],bill_no_Expired as [رقم الصلاحية],ValueVAT as [قيمة الضريبة],RateVAT as [نسبة الضريبة],Discounts as [الخصم],DiscountsValue as [قيمة الخصم],Discount_Price_After as [سعر البيع],DiscountsTin as [DiscountsTin],BeforeVAT [قبل الضريبة],StateDisc [نسبة او قيمة خصم الصنف] from TmpBillsalData"
            dr = cmd.ExecuteReader
            Dgv_Add.DataSource = Cls.PopulateDataView(dr)

            txtCountItems.Text = Dgv_Add.Rows.Count
            Dgv_Add.Columns(1).Visible = False
            Dgv_Add.Columns(4).Visible = False
            Dgv_Add.Columns(9).Visible = False
            Dgv_Add.Columns(10).Visible = False
            Dgv_Add.Columns(11).Visible = False
            Dgv_Add.Columns(12).Visible = False
            Dgv_Add.Columns(13).Visible = False
            Dgv_Add.Columns(14).Visible = True
            Dgv_Add.Columns(15).Visible = False
            Dgv_Add.Columns(16).Visible = False
            Dgv_Add.Columns(17).Visible = False
            If ShowDiscountRateItemSales = "NO" Then
                Dgv_Add.Columns(3).Visible = True
                Dgv_Add.Columns(16).Visible = False
            Else
                Dgv_Add.Columns(3).Visible = False
                Dgv_Add.Columns(16).Visible = True
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        ClearAdd() : SumAllPrice() : FocusText()
        sumdisc()
    End Sub

    Private Sub FocusText()
        Dim FocusText As String = mykey.GetValue("FocusText", "NO")
        If FocusText = "YES" Then
            txtprc.Focus()
        Else
            cmbname.Focus()
        End If
    End Sub

    Private Sub ClearAdd()
        cmbname.Text = ""
        txtprice.Text = ""
        txtprc.Text = ""
        txtqunt.Text = ""
        txtTotal.Text = ""
        txtquntUnity.Text = ""
        cmbUnityItems.Text = ""
        txtRateDiscPriceAfter.Text = ""
    End Sub

    Dim Dt_AddBill As New DataTable
    Friend Function Fn_AddBill(ByVal Col_Prc As String, ByVal Col_Stores As String) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("الباركود", GetType(String))
            Dt_AddBill.Columns.Add("المخزن", GetType(String))
        End If
        Dt_AddBill.Rows.Add(Col_Prc, Col_Stores)
        Return Dt_AddBill
    End Function

    Private Sub Dgv_Add_CellContentClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles Dgv_Add.CellContentClick
        Try
            If e.ColumnIndex = 0 Then
                txtprc.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Prc").Value.ToString
                cmbcats.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Cats").Value.ToString
                cmbname.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Name").Value.ToString
                txtprice.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Price").Value
                txtqunt.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Quant").Value
                Dgv_Add.Rows.RemoveAt(e.RowIndex)
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        SumAllPrice()
    End Sub


    Private Sub BtnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnSave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        'LimitCustomerBalance()

        SumAllPrice()

        If ValidateTextSave() = False Then Exit Sub

        Dim x As String = MsgBox("هل تريد بالفعل إتمام عملية التعديل", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        GetDebtorlCreditorPrevious()


        Dim STAT As String
        If ChkCash.Checked = True Then : STAT = "نقداً" : End If
        If ChkState.Checked = True Then : STAT = "آجل" : End If
        If chkVisa.Checked = True Then : STAT = "فيزا" : End If

        If rdoDismissalNotice.Checked = True Then
            Sales_Bill = "Sales_Bill" : BillsalData = "BillsalData" : vst = "vst" : Vst_disc = "Vst_disc"
        End If
        If rdoReceivingPermission.Checked = True Then
            Sales_Bill = "Receive_Sales_Bill" : BillsalData = "Receive_BillsalData" : vst = "Receive_vst" : Vst_disc = "Receive_Vst_disc"
        End If

        'Try
        ' XXXXXXXXXXXXXXXXXXXXXXXXXXX
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete From  " & BillsalData & " where bill_no =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
        cmd.CommandText = "delete From  " & Vst_disc & " where TIN_NO =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
        cmd.CommandText = "delete From  " & vst & " where BillNo =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()

        ' XXXXXXXXXXXXXXXXXXXXXXXXXXXXX

        If ChkCent.Checked = True Then
            DiscTotal = Val(txttotalpeforedisc.Text) - Val(txttotalafterdisc.Text)
            DiscTotal = Math.Round(DiscTotal, 1)
        Else
            DiscTotal = txtdisc.Text
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select bill_No from " & Sales_Bill & " where bill_No=N'" & txtbillno.Text.Trim & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update " & Sales_Bill & " set Company_Branch_ID =N'" & Company_Branch_ID & "',Vendorname =N'" & cmbvendores.Text & "',bill_date =N'" & Cls.C_date(DateTimePicker1.Text) & "',billtime =N'" & Cls.get_time(True) & "',totalpricebeforedisc =N'" & Val(txttotalpeforedisc.Text.Trim) & "',disc =N'" & Val(DiscTotal) & "',totalpriceafterdisc =N'" & txttotalafterdisc.Text & "',stat =N'" & STAT & "',bey =N'" & txtpaying.Text & "',STAYING =N'" & txtstaying.Text & "',Notes =N'" & txtNotes.Text & "',username =N'" & UserName & "',EmpName =N'" & cmbEmployees.Text & "',DiscountTax =N'" & DiscountTax & "',bill_NoTax =N'" & bill_NoTax & "',ValueVAT =N'" & Val(txtTotalValueVAT.Text) & "',RateValues =N'" & Val(txtSalestax.Text) & "',DiscountsValue =N'" & Val(TotalDiscountsValue) & "',BillTimeAmBm =N'" & txtTimeAMBM.Text.Trim & "',DeliveryService= N'" & Val(txtDeliveryServiceView.Text.ToString) & "',ExpensesBill= N'" & Val(txtExpenses.Text) & "',CreditPrevious= N'" & Val(AmntcreditPrevious) & "',DebitPrevious= N'" & Val(AmntdebitPrevious) & "',Treasury_Code= N'" & Treasury_Code & "',CommercialIndustrialProfitsTax= N'" & txtCommercialAndIndustrialProfitsTax.Text & "' where bill_No =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into " & Sales_Bill & "(Company_Branch_ID,bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,SalesTax,Notes,UserName,EmpName,DiscountTax,bill_NoTax,DeliveryService,ExpensesBill,CreditPrevious,DebitPrevious,Treasury_Code,ValueVAT,DiscountsValue,CloseSheft,BillTimeAmBm,CommercialIndustrialProfitsTax) values ("
            S = S & "N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "' ,N'" & cmbvendores.Text.Trim & "' ,N'" & Cls.C_date(DateTimePicker1.Text) & "' ,N'" & Cls.get_time(True) & "' ," & Val(txttotalpeforedisc.Text.Trim) & "," & Val(DiscTotal) & " ," & Val(txttotalafterdisc.Text.Trim) & " ,N'" & STAT & "',N'" & txtpaying.Text.Trim & "',N'" & txtstaying.Text.Trim & "',N'" & txtSalestax.Text.Trim & "',N'" & txtNotes.Text & "',N'" & UserName & "',N'" & cmbEmployees.Text & "',N'" & Val(ValueDiscountTax) & "',N'" & bill_NoTax & "',N'" & Val(txtDeliveryServiceView.Text.ToString) & "',N'" & Val(txtExpenses.Text) & "',N'" & Val(AmntcreditPrevious) & "',N'" & Val(AmntdebitPrevious) & "',N'" & Treasury_Code & "',N'" & Val(txtTotalValueVAT.Text) & "',N'" & TotalDiscountsValue & "',0,N'" & txtTimeAMBM.Text & "',N'" & txtCommercialAndIndustrialProfitsTax.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If


        For i As Integer = 0 To Dgv_Add.Rows.Count - 1

            If Val(Dgv_Add.Rows(i).Cells(17).Value.ToString) = 0 Then
                DiscountsTin = Val(Cls.Get_Code_Value_More_ALL_Select("Select Top(100) PERCENT Discounts From dbo." & BillsalData & " Where (itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "') Order By id DESC"))
            Else
                DiscountsTin = Val(Dgv_Add.Rows(i).Cells(17).Value.ToString)
            End If

            AverageTinPrice(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value, Dgv_Add.Rows(i).Cells(3).Value, Dgv_Add.Rows(i).Cells(5).Value, Dgv_Add.Rows(i).Cells(6).Value, Val(Dgv_Add.Rows(i).Cells(14).Value.ToString()), Val(Dgv_Add.Rows(i).Cells(16).Value))

            Vendorname = Cls.Get_Code_Value_More("" & BillsalData & "", "Vendorname", "itm_id =N'" & Dgv_Add.Rows(i).Cells(0).Value & "'")

            'Dim bill_no_Expired As String = Cls.Get_Code_Value_More_ALL_Select("Select Top(100) PERCENT MIN(bill_no_Expired) As Exbill_no_Expired, qu_expired, itm_id, Stores, bill_EndDate From dbo.BilltINData Group By itm_id, Stores, bill_EndDate, qu_expired HAVING(itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "') AND (Stores = N'" & Dgv_Add.Rows(i).Cells(8).Value & "') AND (qu_expired <> 0)  ORDER BY MIN(bill_no_Expired)")

            'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            'cmd.CommandText = "select bill_No from BillsalData where bill_No=N'" & txtbillno.Text.Trim & "' and itm_id=N'" & Dgv_Add.Rows(i).Cells(0).Value & "'" : dr = cmd.ExecuteReader : dr.Read()
            'If dr.HasRows = True Then
            '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '    cmd.CommandText = "update BillsalData set Company_Branch_ID =N'" & Company_Branch_ID & "',bill_no =N'" & txtbillno.Text & "',itm_id =N'" & Dgv_Add.Rows(i).Cells(0).Value & "',itm_cat =N'" & Dgv_Add.Rows(i).Cells(1).Value & "',itm_name =N'" & Dgv_Add.Rows(i).Cells(2).Value & "',price =N'" & Dgv_Add.Rows(i).Cells(3).Value & "',qu =N'" & Dgv_Add.Rows(i).Cells(4).Value & "',qu_unity =N'" & Dgv_Add.Rows(i).Cells(5).Value & "',itm_Unity =N'" & Dgv_Add.Rows(i).Cells(6).Value & "',TotalPrice =N'" & Dgv_Add.Rows(i).Cells(7).Value & "',Stores =N'" & Dgv_Add.Rows(i).Cells(8).Value & "',UserName =N'" & UserName & "',bill_date =N'" & Cls.C_date(DateTimePicker1.Text) & "',billtime =N'" & Cls.get_time(True) & "',TinPriceAverage =N'" & TinPriceAverage & "',Profits =N'" & Profits & "',EmpName =N'" & cmbEmployees.Text & "',TinPrice =N'" & TinPrice & "',Vendorname =N'" & cmbvendores.Text.Trim & "',Stat =N'" & STAT & "',Treasury_Code =N'" & Treasury_Code & "',bill_EndDate =N'" & Dgv_Add.Rows(i).Cells(10).Value & "',bill_no_Expired =N'" & Dgv_Add.Rows(i).Cells(11).Value.ToString() & "',Price_Unity =N'" & Price_Unity & "',ValueVAT =N'" & Dgv_Add.Rows(i).Cells(12).Value.ToString() & "',RateVAT =N'" & Dgv_Add.Rows(i).Cells(13).Value.ToString() & "',Discounts =N'" & Dgv_Add.Rows(i).Cells(14).Value & "',DiscountsValue =N'" & Dgv_Add.Rows(i).Cells(15).Value & "',Discount_Price_After =N'" & Dgv_Add.Rows(i).Cells(16).Value & "',DiscountsTin =N'" & DiscountsTin & "' where bill_No =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
            'Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into " & BillsalData & " (Company_Branch_ID,bill_no,itm_id,itm_cat,itm_name,price,qu,qu_unity,itm_Unity,totalprice,Stores,UserName,bill_date,billtime,TinPriceAverage,Profits,EmpName,TinPrice,Vendorname,Stat,Treasury_Code,bill_EndDate,bill_no_Expired,Price_Unity,ValueVAT,RateVAT,Discounts,DiscountsValue,Discount_Price_After,DiscountsTin)  values (N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Dgv_Add.Rows(i).Cells(3).Value & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "',N'" & UserName & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Cls.get_time(True) & "',N'" & TinPriceAverage & "',N'" & Profits & "',N'" & cmbEmployees.Text & "',N'" & TinPrice & "',N'" & cmbvendores.Text.Trim & "',N'" & STAT & "',N'" & Treasury_Code & "',N'" & Dgv_Add.Rows(i).Cells(10).Value & "',N'" & Dgv_Add.Rows(i).Cells(11).Value.ToString() & "',N'" & Price_Unity & "',N'" & Dgv_Add.Rows(i).Cells(12).Value.ToString() & "',N'" & Dgv_Add.Rows(i).Cells(13).Value.ToString() & "',N'" & Dgv_Add.Rows(i).Cells(14).Value & "',N'" & Dgv_Add.Rows(i).Cells(15).Value & "',N'" & Dgv_Add.Rows(i).Cells(16).Value & "',N'" & DiscountsTin & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
            'End If

            IM.StoreExpired(Dgv_Add.Rows(i).Cells(0).Value.ToString(), Dgv_Add.Rows(i).Cells(8).Value.ToString(), Dgv_Add.Rows(i).Cells(10).Value.ToString(), Dgv_Add.Rows(i).Cells(11).Value.ToString())
        Next

        If Val(txtpaying.Text) > 0 Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select billno from " & vst & " where billno=N'" & txtbillno.Text.Trim & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update " & vst & " set Vendorname =N'" & cmbvendores.Text & "',VND_XTM =N'" & Cls.get_time(True) & "',VND_dt =N'" & Cls.C_date(DateTimePicker1.Text) & "',VND_amx =N'" & txtpaying.Text.Trim & "',VND_ho =N'بفاتورة',VND_rcv =N'بفاتورة',VND_dec =N'بفاتورة',VND_no =N'دفعة نقدية',UserName =N'" & UserName & "',Treasury_Code =N'" & Treasury_Code & "',CashBank =N'0' where billno =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into " & vst & " (Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,billno,VND_no,UserName,Treasury_Code,CashBank) values"
                S = S & " (N'" & Company_Branch_ID & "',N'" & cmbvendores.Text & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(DateTimePicker1.Text) & "'," & txtpaying.Text.Trim & ",N'بفاتورة',N'بفاتورة',N'بفاتورة',N'" & txtbillno.Text & "',N'دفعة نقدية',N'" & UserName & "',N'" & Treasury_Code & "',0)"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        End If

        If Val(txtdisc.Text) > 0 Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TIN_NO from " & Vst_disc & " where TIN_NO=N'" & txtbillno.Text.Trim & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update " & Vst_disc & " set Company_Branch_ID =N'" & Company_Branch_ID & "',Vendorname =N'" & cmbvendores.Text & "',amnt =N'" & txtdisc.Text & "',pdate =N'" & Cls.C_date(DateTimePicker1.Text) & "',VND_XTM =N'" & Cls.get_time(True) & "',det =N'خصم على فاتورة مباشرة',UserName =N'" & UserName & "',Treasury_Code =N'" & Treasury_Code & "' where TIN_NO =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into " & Vst_disc & " (Company_Branch_ID,Vendorname,amnt,pdate,VND_XTM,det,TIN_NO,UserName,Treasury_Code) values (N'" & Company_Branch_ID & "',N'" & cmbvendores.Text.Trim & "'," & Val(DiscTotal) & ",N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Cls.get_time(True) & "',N'خصم على فاتورة مباشرة',N'" & txtbillno.Text.Trim & "',N'" & UserName & "',N'" & Treasury_Code & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        End If

        '===============================================================================
        Dim UpdateSalPrice As String = mykey.GetValue("UpdateSalPrice", "NO")
        If UpdateSalPrice = "YES" Then
            UpdatePriceItems()
        End If
        '===============================================================================
        Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
        If UseAccounts = "YES" Then
            Daily_Restrictions()
        End If
        '===============================================================================

        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            IM.Store(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value)

            If ConnectOnlineStore = "YES" Then
                EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
                StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
                Cos.UpdateProductStock(StockOnline, Dgv_Add.Rows(i).Cells(0).Value, EditItmId)
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update " & BillsalData & " set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbillno.Text & "' and itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "' and Stores =N'" & Dgv_Add.Rows(i).Cells(8).Value & "'" : cmd.ExecuteNonQuery()

        Next
        For i As Integer = 0 To DgvDelete.Rows.Count - 1
            IM.Store(DgvDelete.Rows(i).Cells(0).Value, DgvDelete.Rows(i).Cells(1).Value)

            If ConnectOnlineStore = "YES" Then
                EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", DgvDelete.Rows(i).Cells(0).Value)
                StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", DgvDelete.Rows(i).Cells(0).Value)
                Cos.UpdateProductStock(StockOnline, DgvDelete.Rows(i).Cells(0).Value, EditItmId)
            End If
        Next

        IM.CustomerAccountTotal(cmbvendores.Text)
        IM.EmployeesAccountTotal(cmbEmployees.Text)


        GetDebtorlCreditor()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update " & Sales_Bill & " set CreditPrevious =N'" & Val(AmntcreditPrevious) & "',DebitPrevious =N'" & Val(AmntdebitPrevious) & "',CreditCurrent =N'" & Val(Amntcredit) & "',DebitCurrent =N'" & Val(Amntdebit) & "',PendingBill = " & Val(0) & " where bill_No =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()

        Dim CurrentBalanceCustVnd As String = GetCurrentBalanceCustVnd(cmbvendores.Text)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update " & Sales_Bill & " set CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where bill_No =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()

        Get_Movement_In_Out_Money(DateTimePicker1.Text, Treasury_Code)

        'MsgBox("تمت عملية التعديل بنجاح", MsgBoxStyle.Information)
        If chkprint.Checked = True Then
            PrintReport()
        End If

        If UseManufacturingProduct = "YES" Then
            Product_Manufacturing()
        End If


        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try

        Dt_AddBill.Rows.Clear()
        ClearSave()
        Me.Close()
    End Sub

    'Private Sub Product_Manufacturing()
    '    Try
    '        billnoID = Cls.Get_Code_Value_Branch("purchase_bill", "bill_No", "Sales_Bill_NO", txtbillno.Text)

    '        ' XXXXXXXXXXXXXXXXXXXXXXXXXXX
    '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
    '        cmd.CommandText = "delete From  purchase_bill where bill_no =N'" & billnoID & "'" : cmd.ExecuteNonQuery()
    '        cmd.CommandText = "delete From  BilltINData where bill_no =N'" & billnoID & "'" : cmd.ExecuteNonQuery()
    '        cmd.CommandText = "delete From  Manufacturing_BillsalData where bill_no =N'" & billnoID & "'" : cmd.ExecuteNonQuery()
    '        cmd.CommandText = "delete From  vnd where billno =N'" & billnoID & "'" : cmd.ExecuteNonQuery()
    '        ' XXXXXXXXXXXXXXXXXXXXXXXXXXXXX


    '        Dim VendorName As String = "تصنيع منتج"
    '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
    '        cmd.CommandText = "Select Vendorname from vendors where Vendorname=N'" & VendorName & "'"
    '        dr = cmd.ExecuteReader : dr.Read()
    '        If dr.HasRows = False Then
    '            Cls.insert("vendors", "Company_Branch_ID,Vendorname,valuereturns,vintinval,vndiscount,VnPay,vnamntcredit,vnamntdebit,UserName", "N'" & Company_Branch_ID & "',N'" & VendorName & "',0,0,0,0,0,0,N'" & UserName & "'")
    '        End If
    '        '=======================================================================================================================================================
    '        Dim CostPrice As String = ""
    '        Dim TotalCostPrice As Double

    '        ' مشتريات تصنيع

    '        Dim TypeCurrency As String = mykey.GetValue("TypeCurrency", "جنية مصرى")

    '        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
    '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
    '            cmd.CommandText = "select CostPrice from View_Product_Manufacturing_Show where itm_id_Manufacturing=N'" & Dgv_Add.Rows(i).Cells(0).Value & "'  and Stores_Manufacturing=N'" & Dgv_Add.Rows(i).Cells(6).Value & "'"
    '            dr = cmd.ExecuteReader : dr.Read()
    '            If dr.HasRows = False Then Exit Sub
    '            If dr(0) Is DBNull.Value Then
    '            Else
    '                CostPrice = dr("CostPrice")
    '            End If
    '            TotalCostPrice = Val(CostPrice) * Val(Dgv_Add.Rows(i).Cells(4).Value)
    '        Next

    '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
    '        S = "insert into purchase_bill(Company_Branch_ID,bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,UserName,Status,Currency_Name,Sales_Bill_NO) values ("
    '        S = S & "N'" & Company_Branch_ID & "',N'" & billnoID & "',N'" & VendorName & "' ,N'" & Cls.C_date(DateTimePicker1.Text) & "' ,N'" & Cls.get_time(True) & "' ," & Val(TotalCostPrice) & ",0," & Val(TotalCostPrice) & " ,N'نقداً',N'" & TotalCostPrice & "',0,N'" & UserName & "',N'قيمة',N'" & TypeCurrency & "',N'" & txtbillno.Text.Trim & "')"
    '        cmd.CommandText = S : cmd.ExecuteNonQuery()


    '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
    '        S = "insert into vnd (Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,billno,VND_no,UserName,Sales_Bill_NO) values"
    '        S = S & " (N'" & Company_Branch_ID & "',N'" & VendorName & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(DateTimePicker1.Text) & "'," & txtpaying.Text.Trim & ",N'بفاتورة',N'بفاتورة',N'بفاتورة',N'" & billnoID & "',N'دفعة نقدية',N'" & UserName & "',N'" & txtbillno.Text.Trim & "')"
    '        cmd.CommandText = S : cmd.ExecuteNonQuery()


    '        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
    '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
    '            cmd.CommandText = "select CostPrice from View_Product_Manufacturing_Show where itm_id_Manufacturing=N'" & Dgv_Add.Rows(i).Cells(0).Value & "'  and Stores_Manufacturing=N'" & Dgv_Add.Rows(i).Cells(6).Value & "'"
    '            dr = cmd.ExecuteReader : dr.Read()
    '            If dr.HasRows = False Then Exit Sub
    '            If dr(0) Is DBNull.Value Then
    '            Else
    '                CostPrice = dr("CostPrice")
    '            End If
    '            TotalCostPrice = Val(CostPrice) * Val(Dgv_Add.Rows(i).Cells(4).Value)
    '            PriceTinAverage(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value, Dgv_Add.Rows(i).Cells(5).Value, TotalCostPrice)

    '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
    '            S = "insert into BilltINData (Company_Branch_ID,bill_no,itm_id,itm_cat,itm_name,price,qu,qu_unity,itm_Unity,totalprice,Stores,username,bill_date,Expired,TinPriceAverage,Sales_Bill_NO)  values (N'" & Company_Branch_ID & "',N'" & billnoID & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & CostPrice & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & TotalCostPrice & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "',N'" & UserName & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'بدون صلاحية',N'" & TotalPriceBeforeAverage & "',N'" & txtbillno.Text.Trim & "')"
    '            cmd.CommandText = S : cmd.ExecuteNonQuery()
    '        Next


    '        IM.VendorAccountTotal(VendorName)

    '        '=======================================================================================================================================================

    '        ' مبيعات تصنيع
    '        Dim ActivationEmail As String = mykey.GetValue("ActivationEmail", "NO")
    '        Dim Xstore As Double
    '        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
    '            If ActivationEmail = "YES" Then
    '                Xstore = IM.Get_Itm_Store(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value.Trim)
    '                If Xstore < 1 Then
    '                    SendEmail("الكمية بالمخزن قد نفذت", Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(2).Value, Xstore)
    '                End If
    '                If Xstore - Val(txtqunt.Text) < IM.Get_Itm_Range(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value) Then
    '                    SendEmail("الكمية وصلت للحد الادنى", Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(2).Value, Xstore)
    '                End If
    '            End If

    '            aray_itm_id.Clear() : aray_Stores.Clear() : aray_qu.Clear() : aray_TinPrice.Clear()
    '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
    '            cmd.CommandText = "Select itm_id,Stores,qu,TinPrice from View_Product_Manufacturing where itm_id_Manufacturing=N'" & Dgv_Add.Rows(i).Cells(0).Value & "' and Stores_Manufacturing=N'" & Dgv_Add.Rows(i).Cells(6).Value & "'"
    '            dr = cmd.ExecuteReader
    '            Do While dr.Read = True
    '                aray_itm_id.Add(dr(0))
    '                aray_Stores.Add(dr(1))
    '                aray_qu.Add(dr(2))
    '                aray_TinPrice.Add(dr(3))
    '            Loop

    '            For M As Integer = 0 To aray_itm_id.Count - 1
    '                TotalCostPrice = Val(aray_qu(M)) * Val(Dgv_Add.Rows(i).Cells(4).Value)
    '                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
    '                S = "insert into Manufacturing_BillsalData(Company_Branch_ID,bill_no,itm_id,qu,price,totalprice,Stores,bill_date,UserName)  values("
    '                S = S & "N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "',N'" & aray_itm_id(M) & "',N'" & TotalCostPrice & "',N'" & aray_TinPrice(M) & "',N'" & Val(TotalCostPrice) * Val(aray_TinPrice(M)) & "',N'" & aray_Stores(M) & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & UserName & "')"
    '                cmd.CommandText = S : cmd.ExecuteNonQuery()

    '                IM.Store(aray_itm_id(M), aray_Stores(M))
    '            Next
    '        Next
    '    Catch ex As Exception
    '        ErrorHandling(ex, Me.Text)
    '    End Try
    'End Sub

    Private Sub MAXRECORDAuto(ByVal Tabel As String, ByVal Feild As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from " + Tabel + ""
            dr = cmd.ExecuteReader

            Dim dt As New DataTable
            dt.Load(dr)

            If dt.Rows.Count = 0 Then
                billnoID = 1
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(CAST(" + Feild + " As float)) as mb FROM " + Tabel + " where " + Feild + " <> N'جرد'"
                dr = cmd.ExecuteReader
                dr.Read()

                Dim sh As Long
                sh = dr("mb")
                billnoID = sh + 1
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub PriceTinAverage(ByVal Parcode As String, ByVal Stores As String, ByVal Unity As String, ByVal qunt As Double, ByVal TotalPrice As Double)
        Dim StoreItems, TotalPriceTinAverage, BalanceBeforeBuying, TotalBalanceBeforeBuying As Double
        '================================================ المخزون الحالى =====================================================
        Dim Xqunt As Double = qunt

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select store,TinPriceAverage from Items where itm_id =N'" & Parcode & "' and Stores =N'" & Stores & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            StoreItems = dr("store").ToString()
            Dim xx As String = dr("TinPriceAverage").ToString()
            If xx = "" Then
                TotalPriceTinAverage = 0
            Else
                TotalPriceTinAverage = xx
            End If
        End If
        If NotUnityItemsProgram = "YES" Then
            NumberPieces = Cls.Get_Code_Value_Branch_More("ItemsUnity", "NumberPieces", "itm_id=N'" & Parcode & "' and Unity_Name=N'" & Unity & "'")
            If NumberPieces <> 1 Then
                Xqunt = Val(NumberPieces) * Val(qunt)
            End If
        End If

        '================================================ متوسط سعر الشراء الجديد =====================================================

        Try
            BalanceBeforeBuying = Val(TotalPriceTinAverage) * Val(StoreItems)

            TotalBalanceBeforeBuying = Val(TotalPrice) + Val(BalanceBeforeBuying)
            TotalBalanceBeforeBuying = Math.Round(TotalBalanceBeforeBuying, 2)

            Dim TotalTotal As Double = Val(StoreItems) + Val(Xqunt)
            If TotalBalanceBeforeBuying = 0 And TotalTotal = 0 Then
                TotalPriceBeforeAverage = 0
            Else
                TotalPriceBeforeAverage = Val(TotalBalanceBeforeBuying) / Val(TotalTotal)
                If TinPriceAverageThreeDigits = "NO" Then
                    TotalPriceBeforeAverage = Math.Round(TotalPriceBeforeAverage, 2)
                Else
                    TotalPriceBeforeAverage = Math.Round(TotalPriceBeforeAverage, 3)
                End If
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update items set TinPriceAverage = " & Val(TotalPriceBeforeAverage) & " where itm_id =N'" & Parcode & "' and Stores =N'" & Stores & "'" : cmd.ExecuteNonQuery()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub


    Private Sub UpdatePriceItems()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                cmd.CommandText = "update items set SalPrice = " & Val(Dgv_Add.Rows(i).Cells(3).Value) & " where itm_id =N'" & Dgv_Add.Rows(i).Cells(0).Value & "'" : cmd.ExecuteNonQuery()
            Next
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub PrintReport()
        Dim txt, txtname, txtNameAr, txtCmpUnderBILL, txtNameEn, txtCmpAddress, txtCmpEmail, txtCmpTel, txtCmpMobile, txtCustomerAddressSales, txtCommercialRecord, txtTaxCard, txtSaleTax, txtDiscountTax, txtCMPNameDown, txtAltfiqith, txtCmpFax, txtEndorsement, txtDelegateName, txtCustomerTel, txtPhoneEmployee, txtProgramNameBill, txtObjectUserName, txtObjectCommercialAndIndustrialProfitsTax As TextObject

        Dim XTotalBill As String
        If txtpaying.Text = 0 Then
            XTotalBill = txtstaying.Text
        Else
            XTotalBill = txttotalafterdisc.Text
        End If

        Dim Xbill_No As String = ""
        If ShowTax = "YES" Then
            Xbill_No = bill_NoTax
        Else
            Xbill_No = txtbillno.Text
        End If

        If ShowCustomerAddressSales = "YES" Then
            GetCustomerAddress()
        End If

        GetPrintSerialNumber()

        GetDebtorlCreditor()

        AddReportView()

        Dim RateDiscount As String
        If ChkCent2.Checked = True Then : RateDiscount = "%" : Else RateDiscount = "$" : End If

        Dim TotalAfterDisc As Double
        If TotalDiscountsValue <> 0 Then
            TotalAfterDisc = Val(txttotalpeforedisc.Text) + TotalDiscountsValue
        Else
            TotalAfterDisc = Val(txttotalpeforedisc.Text)
        End If

        'Try
        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        Dim BillSerialNumber As Double
        Dim SalPrice As String = ""

        BillSerialNumber = 0
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            BillSerialNumber += 1

            If ShowDiscountRateItemSales = "NO" Then
                SalPrice = Dgv_Add.Rows(i).Cells(3).Value.ToString
            Else
                SalPrice = Dgv_Add.Rows(i).Cells(16).Value.ToString
            End If

            S = "insert into PrintSalesPurchases(Company_Branch_ID,itm_id,itm_cat,itm_name,price,qu,Unity,totalprice,store,BILL_NO,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,BEY,STAYING,vnamntcredit,vnamntdebit,TotalCreditor,TotalDebtor,UserName,Delivery_Date,KiloMeter,det,Supervisor_Reform,TotalDisc,Recipient,VnReceipts,Received_Date,Totalreturns,Name1,Name2,NumberInt1,TotalBeforeDisc)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & SalPrice & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "',N'" & Xbill_No & "',N'" & cmbvendores.Text & "',N'" & DateTimePicker1.Text & "',N'" & Cls.Get_Time_AM_PM(TxtHour.Text.ToString) & "',N'" & TotalAfterDisc & "',N'" & txtdisc.Text & "',N'" & txttotalafterdisc.Text & "',N'" & txtpaying.Text & "',N'" & txtstaying.Text & "',N'" & Amntcredit & "',N'" & Amntdebit & "',N'" & AmntcreditPrevious & "',N'" & AmntdebitPrevious & "',N'" & XTotalBill & "',N'" & DefaultCurrencyProgram & "',N'" & txtTotalValueVAT.Text & "',N'" & Dgv_Add.Rows(i).Cells(14).Value & "',N'" & TotalDiscountsValue & "',N'" & Dgv_Add.Rows.Count & "',N'" & UserName & "',N'" & Dgv_Add.Rows(i).Cells(16).Value & "',N'" & RateDiscount & "',N'" & TotalAccountAfterInvoice & "',N'" & CustomerTel & "',N'" & CustomerAddress & "',N'" & BillSerialNumber & "',N'" & BillSerialNumber & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        Dim rpt


        Cls.GetDefaultPrinterA4()


        If PrintSmall = "YES" Then
            Cls.GetDefaultPrinterBill()
            If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                rpt = New Rpt_SoldSmall
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                rpt = New Rpt_SoldSmall_2
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                rpt = New Rpt_SoldSmall_3
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                rpt = New Rpt_SoldSmall_4
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                rpt = New Rpt_SoldSmall_5
                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                txtDelegateName.Text = cmbEmployees.Text
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                rpt = New Rpt_SoldSmall_6
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                rpt = New Rpt_SoldSmall_7
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                rpt = New Rpt_SoldSmall_8
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                rpt = New Rpt_SoldSmall_9
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                rpt = New Rpt_SoldSmall_10
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                rpt = New Rpt_SoldSmall_11
                If ShowCustomerAddressSales = "YES" Then
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                    End If
                End If
                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                txtDelegateName.Text = cmbEmployees.Text
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                rpt = New Rpt_SoldSmall_12
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                rpt = New Rpt_SoldSmall_13
                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                txtDelegateName.Text = cmbEmployees.Text
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                rpt = New Rpt_SoldSmall_14
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                rpt = New Rpt_SoldSmall_15
                txtObjectUserName = rpt.Section1.ReportObjects("UserName")
                txtObjectUserName.Text = UserName
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                rpt = New Rpt_SoldSmall_16
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                rpt = New Rpt_SoldSmall_17
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                rpt = New Rpt_SoldSmall_18
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                rpt = New Rpt_SoldSmall_19
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                rpt = New Rpt_SoldSmall_20
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                rpt = New Rpt_SoldSmall_21
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                rpt = New Rpt_SoldSmall_22
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                If ShowCustomerAddressSales = "YES" Then
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                    End If
                End If
                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                txtDelegateName.Text = cmbEmployees.Text
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                rpt = New Rpt_SoldSmall_24
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                rpt = New Rpt_SoldSmall_10
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                rpt = New Rpt_SoldSmall_24
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                rpt = New Rpt_SoldSmall_24
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                rpt = New Rpt_SoldSmall_24
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                rpt = New Rpt_SoldSmall_24
            End If
            If SalesInvoicePrintingLanguage = "English" Then
                rpt = New Rpt_SoldSmall_EN
            End If
        End If

        If PrintSmall = "NO" Then
            'If cmbEmployees.Text = "" Then
            If SalesBillNotDiscountBill = "YES" Then
                If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                    rpt = New Rpt_SalesBill_NotDiscountBill
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                    rpt = New Rpt_SalesBill_2
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                    rpt = New Rpt_SalesBill_3
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                    rpt = New Rpt_SalesBill_4
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                    rpt = New Rpt_SalesBill_5
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                    rpt = New Rpt_SalesBill_6
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                    rpt = New Rpt_SalesBill_7
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                    rpt = New Rpt_SalesBill_8
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                    rpt = New Rpt_SalesBill_9
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                    rpt = New Rpt_SalesBill_10
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                    rpt = New Rpt_SalesBill_11
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                    rpt = New Rpt_SalesBill_Delegate
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                    rpt = New Rpt_SalesBill_Delegate_2
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                    rpt = New Rpt_SalesBill_11
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                    rpt = New Rpt_SalesBill_15
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement

                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                    rpt = New Rpt_SalesBill_16
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                    rpt = New Rpt_SalesBill_17
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                    rpt = New Rpt_SalesBill_18
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                    rpt = New Rpt_SalesBill_19
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                    rpt = New Rpt_SalesBill_20
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                    rpt = New Rpt_SalesBill_21
                    If CommercialAndIndustrialProfitsTax = 0 Then
                        txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                        txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                    End If
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                    rpt = New Rpt_SalesBill_22
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                    rpt = New Rpt_SalesBill_23
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                    rpt = New Rpt_SalesBill_24
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                    rpt = New Rpt_SalesBill_25
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                    rpt = New Rpt_SalesBill_26
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                    rpt = New Rpt_SalesBill_27
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                    rpt = New Rpt_SalesBill_28
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                    rpt = New Rpt_SalesBill_29
                End If
                GoTo 10
            End If
            If SalesBillNotDiscount = "YES" Then
                If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                    rpt = New Rpt_SalesBill_NotDiscount
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                    rpt = New Rpt_SalesBill_2
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                    rpt = New Rpt_SalesBill_3
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                    rpt = New Rpt_SalesBill_4
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                    rpt = New Rpt_SalesBill_5
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                    rpt = New Rpt_SalesBill_6
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                    rpt = New Rpt_SalesBill_7
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                    rpt = New Rpt_SalesBill_8
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                    rpt = New Rpt_SalesBill_9
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                    rpt = New Rpt_SalesBill_10
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                    rpt = New Rpt_SalesBill_11
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                    rpt = New Rpt_SalesBill_Delegate
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                    rpt = New Rpt_SalesBill_Delegate_2
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                    rpt = New Rpt_SalesBill_14
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                    rpt = New Rpt_SalesBill_15
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                    rpt = New Rpt_SalesBill_16
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                    rpt = New Rpt_SalesBill_17
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                    rpt = New Rpt_SalesBill_18
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                    rpt = New Rpt_SalesBill_19
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                    rpt = New Rpt_SalesBill_20
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                    rpt = New Rpt_SalesBill_21
                    If CommercialAndIndustrialProfitsTax = 0 Then
                        txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                        txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                    End If
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                    rpt = New Rpt_SalesBill_22
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                    rpt = New Rpt_SalesBill_23
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                    rpt = New Rpt_SalesBill_24
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                    rpt = New Rpt_SalesBill_25
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                    rpt = New Rpt_SalesBill_26
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                    rpt = New Rpt_SalesBill_27
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                    rpt = New Rpt_SalesBill_28
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                    rpt = New Rpt_SalesBill_29
                End If
            Else
                If ShowCustomerBalanceSalesScreen = "YES" Then
                    If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                        rpt = New Rpt_SalesBill
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                        rpt = New Rpt_SalesBill_2
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                        rpt = New Rpt_SalesBill_3
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                        rpt = New Rpt_SalesBill_4
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                        rpt = New Rpt_SalesBill_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                        rpt = New Rpt_SalesBill_6
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                        rpt = New Rpt_SalesBill_7
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                        rpt = New Rpt_SalesBill_8
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                        rpt = New Rpt_SalesBill_9
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                        rpt = New Rpt_SalesBill_10
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                        rpt = New Rpt_SalesBill_11
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                        rpt = New Rpt_SalesBill_Delegate
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                        rpt = New Rpt_SalesBill_Delegate_2
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                        rpt = New Rpt_SalesBill_14
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                        rpt = New Rpt_SalesBill_15
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                        rpt = New Rpt_SalesBill_16
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                        rpt = New Rpt_SalesBill_17
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                        rpt = New Rpt_SalesBill_18
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                        End If
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                        rpt = New Rpt_SalesBill_19
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                        End If
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                        rpt = New Rpt_SalesBill_20
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                        End If
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                        rpt = New Rpt_SalesBill_21
                        If CommercialAndIndustrialProfitsTax = 0 Then
                            txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                            txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                        End If
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                        rpt = New Rpt_SalesBill_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                        rpt = New Rpt_SalesBill_23
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                        rpt = New Rpt_SalesBill_24
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                        rpt = New Rpt_SalesBill_25
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                        rpt = New Rpt_SalesBill_26
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                        rpt = New Rpt_SalesBill_27
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                        rpt = New Rpt_SalesBill_28
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                        rpt = New Rpt_SalesBill_29
                    End If
                Else
                    If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                        rpt = New Rpt_SalesBill_Cash
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                        rpt = New Rpt_SalesBill_2
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                        rpt = New Rpt_SalesBill_3
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                        rpt = New Rpt_SalesBill_4
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                        rpt = New Rpt_SalesBill_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                        rpt = New Rpt_SalesBill_6
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                        rpt = New Rpt_SalesBill_7
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                        rpt = New Rpt_SalesBill_8
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                        rpt = New Rpt_SalesBill_9
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                        rpt = New Rpt_SalesBill_10
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                        rpt = New Rpt_SalesBill_11
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                        rpt = New Rpt_SalesBill_Delegate
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                        rpt = New Rpt_SalesBill_Delegate_2
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                        txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                        txtPhoneEmployee.Text = PhoneEmployee
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                        rpt = New Rpt_SalesBill_14
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                        rpt = New Rpt_SalesBill_15
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                        rpt = New Rpt_SalesBill_16
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                        rpt = New Rpt_SalesBill_17
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                        rpt = New Rpt_SalesBill_18
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                        End If
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                        rpt = New Rpt_SalesBill_19
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                        End If
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                        rpt = New Rpt_SalesBill_20
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                        End If
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                        rpt = New Rpt_SalesBill_21
                        If CommercialAndIndustrialProfitsTax = 0 Then
                            txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                            txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                        End If
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                        rpt = New Rpt_SalesBill_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                        rpt = New Rpt_SalesBill_23
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                        rpt = New Rpt_SalesBill_24
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                        rpt = New Rpt_SalesBill_25
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                        rpt = New Rpt_SalesBill_26
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                        rpt = New Rpt_SalesBill_27
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                        rpt = New Rpt_SalesBill_28
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                        rpt = New Rpt_SalesBill_29
                    End If
                End If

10:
                'If cmbEmployees.Text <> "" Then
                '    rpt = New Rpt_SalesBill_Delegate
                '    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                '    txtEndorsement.Text = CMPEndorsement
                '    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                '    txtDelegateName.Text = cmbEmployees.Text
                'End If
            End If
            txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
            txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(txttotalafterdisc.Text)
            'End If
            txtCMPNameDown = rpt.Section1.ReportObjects("txtCMPNameDown")
            txtCMPNameDown.Text = CMPNameDown
            txtCmpFax = rpt.Section1.ReportObjects("txtFax")
            txtCmpFax.Text = CmpFax
            'If ShowCustomerAddressSales = "YES" Then
            '    If CustomerAddress <> "" Then
            '        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
            '        txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
            '        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
            '        txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
            '    End If
            '    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
            '    txtDelegateName.Text = cmbEmployees.Text
            'End If
        End If

        If PrintSmall = "A5" Then
            If ShowCustomerBalanceSalesScreen = "YES" Then
                If SalesPricePublic = "YES" Then
                    rpt = New Rpt_SalesBill_A5_PricePublic
                Else
                    If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                        rpt = New Rpt_SalesBill_A5_1
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                        rpt = New Rpt_SalesBill_BalanceCust_A5_2
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                        rpt = New Rpt_SalesBill_A5_3
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                        rpt = New Rpt_SalesBill_A5_4
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                        rpt = New Rpt_SalesBill_A5_6
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                        rpt = New Rpt_SalesBill_A5_7
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                        rpt = New Rpt_SalesBill_A5_9
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                        rpt = New Rpt_SalesBill_A5_6_Delegate_2
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                        txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                        txtPhoneEmployee.Text = PhoneEmployee
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                        rpt = New Rpt_SalesBill_A5_6_Delegate_2
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                        txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                        txtPhoneEmployee.Text = PhoneEmployee
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                        rpt = New Rpt_SalesBill_A5_15
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                        rpt = New Rpt_SalesBill_A5_15
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                        rpt = New Rpt_SalesBill_A5_16
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                        rpt = New Rpt_SalesBill_A5_17
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                        rpt = New Rpt_SalesBill_A5_15
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                        End If
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                        rpt = New Rpt_SalesBill_A5_15
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                        End If
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                        rpt = New Rpt_SalesBill_20
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = CustomerTel
                        End If
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                        rpt = New Rpt_SalesBill_A5_15
                        If CommercialAndIndustrialProfitsTax = 0 Then
                            txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                            txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                        End If
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                End If
            Else
                If SalesPricePublic = "YES" Then
                    rpt = New Rpt_SalesBill_BalanceCust_A5_PricePublic
                Else
                    rpt = New Rpt_SalesBill_BalanceCust_A5
                End If
            End If

            txtCMPNameDown = rpt.Section1.ReportObjects("txtCMPNameDown")
            txtCMPNameDown.Text = CMPNameDown
            txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
            txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(txttotalafterdisc.Text)
            If ShowTax = "YES" Then
                txtSaleTax = rpt.Section1.ReportObjects("txtSaleTax")
                txtSaleTax.Text = txtSalestax.Text & " : ضريبة المبيعات"
                txtDiscountTax = rpt.Section1.ReportObjects("txtDiscountTax")
                txtDiscountTax.Text = ValueDiscountTax & " : ضريبة الخصم"
            End If
        End If

        If ShowValueVAT = "YES" Then
            If PrintSmall = "YES" Then
                rpt = New Rpt_SoldSmall_VAT
            End If
            If PrintSmall = "NO" Then
                rpt = New Rpt_SalesBill_Cash_VAT
            End If
            If PrintSmall = "A5" Then
                rpt = New Rpt_SalesBill_BalanceCust_A5_VAT
            End If
        End If

        Cls.Select_More_Data_Branch_Print_Orderby("PrintSalesPurchases", "*", "NumberInt1")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txt = rpt.Section1.ReportObjects("txtTitelAddress")
        txt.Text = CMPAddressBill
        txt = rpt.Section1.ReportObjects("txtTitelAr")
        txt.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
        txtNameEn.Text = NameEnCompany
        txtCmpAddress = rpt.Section1.ReportObjects("txtAddress")
        txtCmpAddress.Text = CmpAddress
        txtCmpEmail = rpt.Section1.ReportObjects("txtEmail")
        txtCmpEmail.Text = CmpEmail
        txtCmpTel = rpt.Section1.ReportObjects("txtTel")
        txtCmpTel.Text = CmpTel
        txtCmpMobile = rpt.Section1.ReportObjects("txtMobile")
        txtCmpMobile.Text = CmpMobile
        txtCmpUnderBILL = rpt.Section1.ReportObjects("txtUnderBILL")
        txtCmpUnderBILL.Text = CMPUnderBILL
        If PrintSmall <> "YES" Then
            txtCmpFax = rpt.Section1.ReportObjects("txtFax")
            txtCmpFax.Text = CmpFax
        End If
        txtCommercialRecord = rpt.Section1.ReportObjects("txtCommercialRecord")
        txtCommercialRecord.Text = CMPCommercialRecord
        txtTaxCard = rpt.Section1.ReportObjects("txtTaxCard")
        txtTaxCard.Text = CMPTaxCard

        If HideProgramNameBill = "YES" Then
            txtProgramNameBill = rpt.Section1.ReportObjects("txtProgramNameBill")
            txtProgramNameBill.Text = ""
        Else
            txtProgramNameBill = rpt.Section1.ReportObjects("txtProgramNameBill")
            txtProgramNameBill.Text = ProgramNameBill
        End If

        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "فاتـــــورة مبــيــعات"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub

    Sub ForPrintAll(ByVal itm_id As String, ByVal itm_cat As String, ByVal itm_name As String, ByVal price As String, ByVal qu As String, ByVal totalprice As String, ByVal stors As String,
                    ByVal BILL_NO As String, ByVal Vendorname As String, ByVal bill_date As String, ByVal billtime As String,
                    ByVal totalpricebeforedisc As String, ByVal disc As String, ByVal totalpriceafterdisc As String, ByVal BEY As String, ByVal STAYING As String)

        Try
            cmd.CommandType = CommandType.StoredProcedure
            cmd.CommandText = "Sp_PrintSalesPurchases"
            cmd.Parameters.Clear()

            cmd.Parameters.AddWithValue("@itm_id", itm_id)
            cmd.Parameters.AddWithValue("@itm_cat", itm_cat)
            cmd.Parameters.AddWithValue("@itm_name", itm_name)
            cmd.Parameters.AddWithValue("@price", price)
            cmd.Parameters.AddWithValue("@qu", qu)
            cmd.Parameters.AddWithValue("@totalprice", totalprice)
            cmd.Parameters.AddWithValue("@store", stors)
            cmd.Parameters.AddWithValue("@BILL_NO", BILL_NO)
            cmd.Parameters.AddWithValue("@Vendorname", Vendorname)
            cmd.Parameters.AddWithValue("@bill_date", bill_date)
            cmd.Parameters.AddWithValue("@billtime", billtime)
            cmd.Parameters.AddWithValue("@totalpricebeforedisc", totalpricebeforedisc)
            cmd.Parameters.AddWithValue("@disc", disc)
            cmd.Parameters.AddWithValue("@totalpriceafterdisc", totalpriceafterdisc)
            cmd.Parameters.AddWithValue("@BEY", BEY)
            cmd.Parameters.AddWithValue("@STAYING", STAYING)

            cmd.ExecuteNonQuery()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub ClearSave()
        GrpMain.Enabled = True
        cmbvendores.SelectedIndex = -1
        txtbillno.Text = ""
        txtprc.Text = ""
        txttotalafterdisc.Text = ""
        txttotalpeforedisc.Text = ""
        txtdisc.Text = ""
        txtpaying.Text = ""
        txtstaying.Text = ""
        AmntcreditPrevious = "0"
        AmntdebitPrevious = "0"
        Amntdebit = "0"
        Amntcredit = "0"
        mykey.SetValue("DiscountItems", "0")
        ActionPayCust = False
    End Sub

    Private Sub SumAllPrice()
        'Try

        Dim SM, SMVAT As Double
        Dim Price, Qunt, Total, XTotal, DiscVal, TotalRateVAT, TotalRateVATFor, TotalVAT, RateVAT, DiscountsValue, TotalCountItems As Double
        For i As Integer = 0 To Dgv_Add.RowCount - 1
            Price = Dgv_Add.Rows(i).Cells(3).Value
            Qunt = Dgv_Add.Rows(i).Cells(5).Value
            RateVAT = Dgv_Add.Rows(i).Cells(13).Value
            Total = Val(Price) * Val(Qunt)
            StateDisc = Dgv_Add.Rows(i).Cells(19).Value.ToString()
            XTotal = Total

            If ShowValueVAT = "YES" Then
                TotalRateVAT += Format(Val(Total) * Val(RateVAT) / 100, "Fixed")
                TotalRateVATFor = Format(Val(Total) * Val(RateVAT) / 100, "Fixed")
                TotalVAT = Math.Round(TotalRateVATFor, 2) + Math.Round(Total, 2)
            End If
            Total = Math.Round(TotalRateVATFor, 2) + Math.Round(Total, 2)

            DiscVal = XTotal
            If StateDisc = "نسبة" Then
                If ShowDiscountRateItemSales = "NO" Then
                    DiscVal = Val((Val(XTotal) * (100 - Val(Dgv_Add.Rows(i).Cells(14).Value))) / 100)
                Else
                    DiscVal = Val(XTotal)
                End If
                DiscVal = Math.Round(DiscVal, 2)
            ElseIf StateDisc = "قيمة" Then
                DiscVal = Val(XTotal) - Val(Dgv_Add.Rows(i).Cells(14).Value)
            End If
            Dgv_Add.Rows(i).Cells(7).Value = DiscVal

            'SM = SM + Dgv_Add.Rows(i).Cells(7).Value - XTotalRateVAT
            SM = SM + DiscVal
            SMVAT = SMVAT + Dgv_Add.Rows(i).Cells(12).Value
            Dgv_Add.Rows(i).Cells(12).Value = TotalVAT
            Dgv_Add.Rows(i).Cells(18).Value = XTotal
            DiscountsValue = DiscountsValue + Dgv_Add.Rows(i).Cells(15).Value

            If StateDisc = "نسبة" Then
                StateDisc = "نسبة"
                If ShowDiscountRateItemSales = "NO" Then
                    Dgv_Add.Rows(i).Cells(16).Value = Val((Val(Val(Price) * Val(1)) * (100 - Val(Dgv_Add.Rows(i).Cells(14).Value))) / 100)
                    Dim XVal As String = Format(Val(Price) * Val(Dgv_Add.Rows(i).Cells(14).Value) / 100, "Fixed")
                    DiscountsValue = Val(XVal) * Val(Qunt)
                Else
                    Dgv_Add.Rows(i).Cells(16).Value = Val((Val(Val(Price) * Val(1)) * (100 + Val(Dgv_Add.Rows(i).Cells(14).Value))) / 100)
                    Dim XVal As String = Format(Val(Price) * Val(txtdiscBill.Text) / 100, "Fixed")
                    DiscountsValue = Val(XVal) * Val(Qunt)
                End If
            Else
                StateDisc = "قيمة"
                Dgv_Add.Rows(i).Cells(16).Value = Val(Price) - Val(Dgv_Add.Rows(i).Cells(14).Value)
                DiscountsValue = Val(Dgv_Add.Rows(i).Cells(14).Value)
            End If
            Dgv_Add.Rows(i).Cells(15).Value = DiscountsValue
            TotalCountItems += Dgv_Add.Rows(i).Cells(5).Value
        Next

        txttotalpeforedisc.Text = SM
        txttotalafterdisc.Text = SM + Math.Round(TotalRateVAT, 2)
        txtTotalValueVAT.Text = Math.Round(TotalRateVAT, 2)
        txtTotalCountItems.Text = TotalCountItems
        CloseProSalTotalBeforeDisc = txttotalpeforedisc.Text



        'Dim SM, SMVAT As Double
        'Dim Price, Qunt, Total, TotalRateVAT, RateVAT, TotalRateVATFor, TotalVAT, TotalCountItems, DiscountsValue As Double
        'For i As Integer = 0 To Dgv_Add.RowCount - 1
        '    Price = Val(Dgv_Add.Rows(i).Cells(3).Value)
        '    Qunt = Val(Dgv_Add.Rows(i).Cells(5).Value)
        '    RateVAT = Val(Dgv_Add.Rows(i).Cells(13).Value)
        '    Total = Val(Price) * Val(Qunt)

        'If ShowValueVAT = "YES" Then
        '    TotalRateVAT += Format(Val(Total) * Val(RateVAT) / 100, "Fixed")
        '    TotalRateVATFor = Format(Val(Total) * Val(RateVAT) / 100, "Fixed")
        '    TotalVAT = Math.Round(TotalRateVATFor, 2) + Math.Round(Total, 2)
        'End If

        '    Total = Math.Round(TotalRateVATFor, 2) + Math.Round(Total, 2)

        '    SM = SM + Dgv_Add.Rows(i).Cells(7).Value - TotalVAT
        '    SMVAT = SMVAT + Dgv_Add.Rows(i).Cells(12).Value
        '    TotalCountItems += Val(Dgv_Add.Rows(i).Cells(5).Value)
        '    DiscountsValue = DiscountsValue + Val(Dgv_Add.Rows(i).Cells(15).Value.ToString)
        'Next

        'txttotalpeforedisc.Text = SM
        'txtTotalValueVAT.Text = Math.Round(TotalRateVAT, 2)
        'txtTotalCountItems.Text = TotalCountItems
        'TotalDiscountsValue = DiscountsValue


        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub


    Private Sub txttotalpeforedisc_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txttotalpeforedisc.TextChanged
        MyVars.CheckNumber(txttotalpeforedisc)

        sumdisc()
    End Sub

    Private Sub sumdisc()

        Dim DiscVal, TaxVal, TotalVal, TotalDiscountTax, RateBankExpensesVisa, CommercialAndIndustrialProfitsTax As Double

        If ShowTax <> "0" Then
            TaxVal = Format(Val(txttotalpeforedisc.Text) * Val(txtSalestax.Text) / 100, "Fixed")
        End If
        If txtCommercialAndIndustrialProfitsTaxRate.Text <> "0" Then
            CommercialAndIndustrialProfitsTax = Val((Val(txttotalpeforedisc.Text) * (100 - Val(txtCommercialAndIndustrialProfitsTaxRate.Text))) / 100)
            CommercialAndIndustrialProfitsTax = Val(txttotalpeforedisc.Text) - Val(CommercialAndIndustrialProfitsTax)
            txtCommercialAndIndustrialProfitsTax.Text = Math.Round(CommercialAndIndustrialProfitsTax, 2)
        End If

        If chkVisa.Checked = True Then
            RateBankExpensesVisa = Format(Val(txttotalpeforedisc.Text) * Val(RateBankExpensesPaidByVisa) / 100, "Fixed")
        End If

        TotalDiscountTax = Val((Val(txttotalpeforedisc.Text) * (100 - Val(ValueDiscountTax))) / 100)
        TotalDiscountTax = Val(txttotalpeforedisc.Text) - Val(TotalDiscountTax)
        TotalDiscountTax = Math.Round(TotalDiscountTax, 2)

        If ChkCent.Checked = True Then
            DiscVal = Val((Val(txttotalpeforedisc.Text) * (100 - Val(txtdisc.Text))) / 100)
            DiscVal = Math.Round(DiscVal, 2)
        ElseIf ChkVal.Checked = True Then
            DiscVal = Val(txttotalpeforedisc.Text) - Val(txtdisc.Text)
        End If

        TotalVal = TaxVal - Val(txtCommercialAndIndustrialProfitsTax.Text) + DiscVal - TotalDiscountTax + Val(txtDeliveryServiceView.Text)
        txttotalafterdisc.Text = TotalVal + Val(txtExpenses.Text) + RateBankExpensesVisa + Val(txtTotalValueVAT.Text)

        If ChkState.Checked = True Then
            txtstaying.Text = Val(txttotalafterdisc.Text) - Val(txtpaying.Text)
        Else
            If AllowingSalesPaidAndRestToCustomerCash = "NO" Then
                txtstaying.Text = "0"
            End If
        End If

        If AllowingSalesPaidAndRestToCustomerCash = "YES" Then
            If ChkState.Checked = False Then
                If txtpaying.Text = "0" Then
                    If txttotalafterdisc.Text <> 0 Then
                        txtpaying.Text = Val(txttotalafterdisc.Text) - Val(txtstaying.Text)
                    End If
                Else
                    If txttotalafterdisc.Text <> 0 Then
                        If ActionPayCust = True Then
                            txtstaying.Text = Val(txtpaying.Text) - Val(txttotalafterdisc.Text)
                        Else
                            txtpaying.Text = Val(txttotalafterdisc.Text)
                        End If
                    End If
                End If
            End If
        Else
            If txtstaying.Text = "0" Then
                txtpaying.Text = Val(txttotalafterdisc.Text) - Val(txtstaying.Text)
            End If
        End If


        'Dim DiscVal, TaxVal, TotalVal, TotalDiscountTax As Double
        'TaxVal = Format(Val(txttotalpeforedisc.Text) * Val(txtSalestax.Text) / 100, "Fixed")

        'TotalDiscountTax = Val((Val(txttotalpeforedisc.Text) * (100 - Val(DiscountTax))) / 100)
        'TotalDiscountTax = Val(txttotalpeforedisc.Text) - Val(TotalDiscountTax)
        'TotalDiscountTax = Math.Round(TotalDiscountTax, 2)

        'If ChkCent.Checked = True Then
        '    DiscVal = Val((Val(txttotalpeforedisc.Text) * (100 - Val(txtdisc.Text))) / 100)
        '    DiscVal = Math.Round(DiscVal, 2)
        'ElseIf ChkVal.Checked = True Then
        '    DiscVal = Val(txttotalpeforedisc.Text) - Val(txtdisc.Text)
        'End If
        'TotalVal = TaxVal + DiscVal - TotalDiscountTax
        'txttotalafterdisc.Text = TotalVal

        'If ChkState.Checked = True Then
        '    txtstaying.Text = Val(txttotalafterdisc.Text) - Val(txtpaying.Text)
        'Else
        '    txtstaying.Text = "0"
        'End If
        'If txtstaying.Text = "0" Then
        '    txtpaying.Text = Val(txttotalafterdisc.Text) - Val(txtstaying.Text)
        'End If
    End Sub

    Private Sub txtdisc_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtdisc.KeyUp
        If e.KeyCode = 13 Then
            txtpaying.Focus()
        End If
    End Sub

    Private Sub txtdisc_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtdisc.TextChanged
        MyVars.CheckNumber(txtdisc)
        sumdisc()
    End Sub

    Private Sub txttotalafterdisc_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txttotalafterdisc.TextChanged
        MyVars.CheckNumber(txttotalafterdisc)
        sumdisc()
    End Sub

    Private Sub txtpaying_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtpaying.KeyUp
        If e.KeyCode = 13 Then
            BtnSave.PerformClick()
        End If
    End Sub

    Private Sub txtpaying_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtpaying.TextChanged
        MyVars.CheckNumber(txtpaying)

        sumdisc()
    End Sub

    Private Sub txtstaying_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtstaying.TextChanged
        MyVars.CheckNumber(txtstaying)

        sumdisc()
    End Sub

    Private Sub txtbillno_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        MyVars.CheckNumber(txtbillno)

    End Sub

    Private Sub txtprc_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtprc.KeyUp
        txtStore.ForeColor = Color.Black
        If e.KeyCode = 13 Then
            If txtprc.Text.Trim = "" Then
            Else
                'Try
                Bol = True
                Actions = True
                ItemsUnityNumber = 0
                If txtprc.Text.Trim = "" Then Exit Sub

                ParcodeMore = txtprc.Text
                GetBarcodeMore(txtprc.Text) : If ParcodeMore <> "" Then : If ParcodeMore <> 0 Then : txtprc.Text = ParcodeMore : End If : End If
                If ParcodeMore = "0" Then
                    Dim PrcUnity As String = Cls.Get_Code_Value("ItemsUnity", "itm_id", "itm_id_Unity", ParcodeMore)
                    If PrcUnity <> "0" Then
                        txtprc.Text = PrcUnity
                    End If
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select group_name , sname,SalPrice,RateVAT from items where itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "' and QuickSearch=0"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    cmbcats.Text = dr(0).ToString
                    cmbname.Text = dr(1).ToString
                    txtprice.Text = dr(2).ToString
                    ItemsRateVAT = Val(dr(3).ToString)
                Else
                    Cls.Select_More_Data_Stores("items", "group_name,sname,SalPrice,RateVAT,Stores", "itm_id=N'" & txtprc.Text & "' and QuickSearch=0")
                    If dr.HasRows = True Then
                        cmbcats.Text = dr("group_name").ToString
                        cmbname.Text = dr("sname").ToString
                        txtprice.Text = dr("SalPrice").ToString
                        ItemsRateVAT = Val(dr("RateVAT").ToString)
                        cmbStores.Text = dr("Stores").ToString
                    End If
                End If

                If DiscountsTin = 0 Then
                    DiscountsTin = Cls.Get_Code_Value_More_ALL_Select("Select Top(100) PERCENT Discounts From dbo.BilltINData Where (itm_id = N'" & txtprc.Text & "') Order By id DESC")
                End If

                ItemsOffersDiscounts()

                WholeasalePrice()

                GetItemsUnity(cmbUnityItems, txtprc.Text)

                Dim ItemsUnity As String = Cls.Get_Code_Value("ItemsUnity", "Unity_Name", "itm_id_Unity", ParcodeMore)
                If ItemsUnity <> "0" Then
                    cmbUnityItems.Text = ItemsUnity
                End If

                SetItemsUnity()

                If cmbvendores.Text <> "نقدا" Then
                    Dim LastSalPriceCustomer As String = mykey.GetValue("LastSalPriceCustomer", "NO")
                    If LastSalPriceCustomer = "YES" Then
                        MaxClientPrice()
                    End If
                End If

                txtqunt.Text = 1
                txtquntUnity.Text = 1
                If NetworkName = "Yes" Then
                    If UseExternalServer = "Yes" Then
                        connect()
                    End If
                End If

                txtStore.Text = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)

                GetItemsUnityTotalCarton()

                Dim AddBillAuto As String = mykey.GetValue("AddBillAuto", "NO")
                If AddBillAuto = "YES" Then
                    BtnAdd.PerformClick()
                Else
                    Dim DiscountQuantity As String = mykey.GetValue("SelectDiscountQuantity", "SelectPrice")
                    If DiscountQuantity = "SelectPrice" Then
                        txtprice.Focus()
                        txtprice.SelectAll()
                    End If
                    If DiscountQuantity = "SelectDiscount" Then
                        txtdiscBill.Focus()
                        txtdiscBill.SelectAll()
                    End If
                    If DiscountQuantity = "SelectQuantity" Then
                        txtquntUnity.Focus()
                        txtquntUnity.SelectAll()
                    End If
                End If

                Bol = False
                Actions = False
                ParcodeMore = 0
                'Catch ex As Exception
                '    ErrorHandling(ex, Me.Text)
                'End Try
            End If

        End If
    End Sub

    Private Sub cmbname_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbname.KeyUp
        If e.KeyCode = 13 Then
            txtprice.Focus()
        End If
        'If e.KeyCode = 8 Then
        '    txtqunt.Text = ""
        '    txtprc.Text = ""
        '    txtprice.Text = ""
        'End If
    End Sub

    Private Sub cmbname_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbname.SelectedIndexChanged
        If Actions = False Then
            If txtprc.Text = "" Then
                GetDataBsal()
            End If
        End If
    End Sub
    Private Sub GetDataBsal()
        Bol = True
        ItemsUnityNumber = 0
        Try
            Cls.Select_More_Data_Stores("items", "itm_id,group_name,SalPrice,RateVAT,RateDiscSalPriceAfter", "sname=N'" & cmbname.Text & "' and Stores =N'" & cmbStores.Text & "' and QuickSearch=0")
            If dr.HasRows = True Then
                txtprc.Text = dr(0).ToString
                cmbcats.Text = dr(1).ToString
                txtprice.Text = dr(2).ToString
                ItemsRateVAT = Val(dr(3).ToString)
                txtRateDiscPriceAfter.Text = Val(dr(4).ToString)
            Else
                Cls.Select_More_Data_Stores("items", "itm_id,group_name,SalPrice,RateVAT,Stores,RateDiscSalPriceAfter", "sname=N'" & cmbname.Text & "' and QuickSearch=0")
                If dr.HasRows = True Then
                    txtprc.Text = dr("itm_id").ToString
                    cmbcats.Text = dr("group_name").ToString
                    txtprice.Text = dr("SalPrice").ToString
                    ItemsRateVAT = Val(dr("RateVAT").ToString)
                    cmbStores.Text = dr("Stores").ToString
                    txtRateDiscPriceAfter.Text = Val(dr("RateDiscSalPriceAfter").ToString)
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        txtStore.Text = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)

        If DiscountsTin = 0 Then
            DiscountsTin = Cls.Get_Code_Value_More_ALL_Select("Select Top(100) PERCENT Discounts From dbo.BilltINData Where (itm_id = N'" & txtprc.Text & "') Order By id DESC")
        End If

        '====================================================
        ItemsOffersDiscounts()

        WholeasalePrice()

        GetItemsUnity(cmbUnityItems, txtprc.Text)

        SetItemsUnity()

        If cmbvendores.Text <> "نقدا" Then
            Dim LastSalPriceCustomer As String = mykey.GetValue("LastSalPriceCustomer", "NO")
            If LastSalPriceCustomer = "YES" Then
                MaxClientPrice()
            End If
        End If

        GetItemsUnityTotalCarton()

        If ItemsUnityNumber <> 1 Then
            If ItemsUnityNumber <> 0 Then
                Dim XCode As String = Cls.Get_Code_Value_Branch_More("ItemsUnity", "TinPriceUnit", "itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'")
                If XCode <> "0" Then
                    If Val(txttinprice.Text) < Val(XCode) Then
                        txttinprice.Text = Math.Round(Val(XCode), 2)
                    End If
                End If
            End If
        End If

        txtqunt.Text = 1
        txtquntUnity.Text = 1

        If AddBillAuto = "YES" Then
            BtnAdd.PerformClick()
        Else
            Dim DiscountQuantity As String = mykey.GetValue("SelectDiscountQuantity", "SelectPrice")
            If DiscountQuantity = "SelectPrice" Then
                txtprice.Focus()
                txtprice.SelectAll()
            End If
            If DiscountQuantity = "SelectDiscount" Then
                txtdiscBill.Focus()
                txtdiscBill.SelectAll()
            End If
            If DiscountQuantity = "SelectQuantity" Then
                txtquntUnity.Focus()
                txtquntUnity.SelectAll()
            End If
        End If
        Bol = False
    End Sub

    Private Sub ItemsOffersDiscounts()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "SELECT bill_No,bill_date_End,qu,RatePriceOffers,PriceOffers FROM ItemsOffersDiscounts where itm_id =N'" + txtprc.Text + "' and ActionOffers =N'0'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Offersbill_No = dr("bill_No")
                Maxbill_date = dr("bill_date_End")
                Offers_Qunt = dr("qu")
                RatePriceOffers = dr("RatePriceOffers")
                PriceOffers = dr("PriceOffers")


                If Maxbill_date = Cls.C_date(DateTimePicker1.Text) Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsOffersDiscountsTotal set ActionOffers = " & Val(1) & " where bill_No =N'" & Offersbill_No & "'" : cmd.ExecuteNonQuery()
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update ItemsOffersDiscounts set ActionOffers = " & Val(1) & " where bill_No =N'" & Offersbill_No & "'" : cmd.ExecuteNonQuery()
                    Exit Sub
                End If

                If Dgv_Add.RowCount = 0 Then
                    Total_Offers_Qunt = Val(txtqunt.Text)
                Else
                    Total_Offers_Qunt = 0
                End If


                ' عرض على صنف واحد بكمية واحدة
                If Offers_Qunt = 1 Then
                    txtprice.Text = PriceOffers
                    Total_Offers_Qunt = Val(txtqunt.Text)
                    Exit Sub
                End If


                ' عرض على الكميات
                For i As Integer = 0 To Dgv_Add.RowCount - 1
                    If Offersbill_No = Dgv_Add.Rows(i).Cells(21).Value Then
                        Total_Offers_Qunt += Dgv_Add.Rows(i).Cells(5).Value + Val(txtqunt.Text)
                    End If
                Next
                For i As Integer = 0 To Dgv_Add.RowCount - 1
                    If Offersbill_No = Dgv_Add.Rows(i).Cells(21).Value Then
                        Dgv_Add.Rows(i).Cells(22).Value = Total_Offers_Qunt
                    End If
                Next
                For i As Integer = 0 To Dgv_Add.RowCount - 1
                    If Offersbill_No = Dgv_Add.Rows(i).Cells(21).Value Then
                        If Total_Offers_Qunt >= Offers_Qunt Then
                            Dgv_Add.Rows(i).Cells(3).Value = Cls.Get_Code_Value_Branch_More("ItemsOffersDiscounts", "PriceOffers", "sname =N'" + Dgv_Add.Rows(i).Cells(2).Value + "' and ActionOffers =N'0'")
                        End If
                    End If
                Next

                If Total_Offers_Qunt = Offers_Qunt Then
                    txtprice.Text = PriceOffers
                End If
            End If


            'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            'cmd.CommandType = CommandType.Text
            'cmd.CommandText = "SELECT MAX(CAST(bill_No As float)) as mb FROM ItemsOffersDiscounts where sname =N'" + cmbname.Text + "'"
            'dr = cmd.ExecuteReader
            'dr.Read()
            'If dr.HasRows = False Then Exit Sub
            'If dr(0) Is DBNull.Value Then
            'Else
            '    Dim sh As Long
            '    sh = dr("mb")
            '    Maxbill_No = sh
            'End If
            ''=============================================================
            'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            'cmd.CommandType = CommandType.Text
            'cmd.CommandText = "SELECT MAX(CAST(bill_date As float)) as mb FROM ItemsOffersDiscounts where sname =N'" + cmbname.Text + "' and bill_No =N'" + Maxbill_No + "'"
            'dr = cmd.ExecuteReader
            'dr.Read()
            'If dr.HasRows = False Then Exit Sub
            'If dr(0) Is DBNull.Value Then
            'Else
            '    Dim sh As Long
            '    sh = dr("mb")
            '    Maxbill_date = sh
            'End If
            ''====================================================================
            'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            'cmd.CommandText = "SELECT OffersStatement,RatePriceOffers,PriceOffers FROM ItemsOffersDiscounts where bill_date =N'" + Maxbill_date + "' and sname =N'" + cmbname.Text + "' and bill_No =N'" + Maxbill_No + "'"
            'dr = cmd.ExecuteReader : dr.Read()
            'If dr.HasRows = True Then
            '    OffersStatement = dr("OffersStatement")
            '    RatePriceOffers = dr("RatePriceOffers")
            '    PriceOffers = dr("PriceOffers")

            '    If OffersStatement = "بداية العرض" Then
            '        txtprice.Text = PriceOffers
            '    Else
            '        txtprice.Text = priceItems
            '    End If
            '    OffersStatement = ""
            'End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

    End Sub


    Private Sub MaxClientPrice()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select TOP (100) PERCENT dbo.BillsalData.id, dbo.BillsalData.bill_no, dbo.Sales_Bill.Vendorname, dbo.BillsalData.itm_id, dbo.BillsalData.itm_cat, dbo.BillsalData.itm_name, dbo.BillsalData.price,  dbo.BillsalData.bill_date, dbo.Sales_Bill.billtime From dbo.Sales_Bill INNER Join dbo.BillsalData On dbo.Sales_Bill.bill_No = dbo.BillsalData.bill_no Group By dbo.BillsalData.price, dbo.BillsalData.itm_name, dbo.BillsalData.id, dbo.BillsalData.itm_cat, dbo.BillsalData.itm_id, dbo.Sales_Bill.Vendorname, dbo.BillsalData.bill_date, dbo.Sales_Bill.billtime, dbo.BillsalData.bill_no  HAVING      (dbo.Sales_Bill.Vendorname =N'" & cmbvendores.Text & "') AND (dbo.BillsalData.itm_id =N'" & txtprc.Text & "') Order By dbo.Sales_Bill.billtime DESC, dbo.BillsalData.bill_date DESC"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then Exit Sub
            If dr(0) Is DBNull.Value Then
            Else
                txtprice.Text = dr("price")
            End If

            If txtprice.Text = "" Or txtprice.Text = "0" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select SalPrice from items where itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = False Then Exit Sub
                If dr(0) Is DBNull.Value Then
                Else
                    txtprice.Text = dr(0)
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub WholeasalePrice()
        Try
            If rdoSectors.Checked = True Then
                txtprice.Text = Cls.Get_Code_Value_Stores_More("items", "SalPrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
                txtdiscBill.Text = Cls.Get_Code_Value_Stores_More("items", "RateDiscSalPrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
            End If

            If rdoWhole.Checked = True Then
                txtprice.Text = Cls.Get_Code_Value_Stores_More("items", "WholePrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
                txtdiscBill.Text = Cls.Get_Code_Value_Stores_More("items", "RateDiscWholePrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
            End If

            If rdoWholeWhole.Checked = True Then
                txtprice.Text = Cls.Get_Code_Value_Stores_More("items", "WholeWholePrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
                txtdiscBill.Text = Cls.Get_Code_Value_Stores_More("items", "RateDiscWholeWholePrice", "itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'")
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub


    Private Sub cmbcats_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If cmbcats.Text = "" Then GoTo 1
        If Bol = True Then GoTo 1
        Bol = True
        Try
            Cls.fill_combo_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbname)
            cmbname.Text = ""
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
1:
        Bol = False
    End Sub

    Private Sub ChkVal_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkVal.CheckedChanged
        sumdisc()
    End Sub

    Private Sub ChkCent_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkCent.CheckedChanged
        sumdisc()
    End Sub

    Dim RNXD As Integer
    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        For i As Integer = 0 To Dgv_Add.SelectedRows.Count - 1
            DgvDelete.DataSource = Fn_AddBill(Dgv_Add.SelectedRows(0).Cells(0).Value, Dgv_Add.SelectedRows(0).Cells(8).Value)
            Try
                Dim ItmID As String
                ItmID = Dgv_Add.SelectedRows(0).Cells(0).Value
                RNXD = Dgv_Add.CurrentRow.Index
                Dgv_Add.Rows.RemoveAt(RNXD) : SumAllPrice() : sumdisc()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete from TmpBillsalData where ITM_ID =N'" & ItmID & "'" : cmd.ExecuteNonQuery()

                txtCountItems.Text = Dgv_Add.Rows.Count
            Catch ex As Exception
                ErrorHandling(ex, Me.Text)
            End Try
        Next
    End Sub

    Private Sub BtnAddCat_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        FrmCats.Show()
    End Sub

    Private Sub BtnAddVendor_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAddVendor.Click
        frmvendors.Show()
    End Sub

    Private Sub BtnClear_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnClear.Click
        Try
            Dt_AddBill.Rows.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete from TmpBillsalData" : cmd.ExecuteNonQuery()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        SumAllPrice() : sumdisc()
    End Sub

    Private Sub cmbStores_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbStores.KeyUp
        If e.KeyCode = 13 Then
            GetDataBsal()
        End If
    End Sub


    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        On Error Resume Next
        SlpitTime()
        txtTimeAMBM.Text = TimeAmBm
        TxtHour.Text = Cls.get_time(True)
    End Sub

    Private Sub cmbvendores_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbvendores.SelectedIndexChanged
        If cmbvendores.Text <> "نقدا" Then
            Dim LastSalPriceCustomer As String = mykey.GetValue("LastSalPriceCustomer", "NO")
            If LastSalPriceCustomer = "YES" Then
                MaxClientPrice()
            End If
        Else
            Try
                txtprice.Text = Cls.Get_Code_Value_Stores("items", "SalPrice", "sname", cmbname.Text)
            Catch ex As Exception
                ErrorHandling(ex, Me.Text)
            End Try
        End If

        '============================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select dbo.Customers.Vendorname, dbo.Employees.NameEmployee  From dbo.Customers INNER Join  dbo.Employees ON dbo.Customers.Emp_Code = dbo.Employees.EMPID  Where (dbo.Customers.Vendorname = N'" & cmbvendores.Text & "')"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then Exit Sub
            If dr(0) Is DBNull.Value Then
            Else
                cmbEmployees.Text = dr("NameEmployee")
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        '==============================================================
        If cmbvendores.Text <> "نقداً" Then
            Dim PriceType_ID As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PriceType_ID from Customers where Vendorname =N'" & cmbvendores.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                PriceType_ID = dr("PriceType_ID").ToString
                If PriceType_ID = 1 Then
                    rdoSectors.Checked = True
                End If
                If PriceType_ID = 2 Then
                    rdoWhole.Checked = True
                End If
                If PriceType_ID = 3 Then
                    rdoWholeWhole.Checked = True
                End If
            Else
                rdoSectors.Checked = True
            End If
        Else
            rdoSectors.Checked = True
        End If
    End Sub

    Private Sub cmbcats_SelectedIndexChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbcats.SelectedIndexChanged
        If Actions = False Then
            Actions = True

            If cmbname.Text = "" Then
                Try
                    If txtprc.Text = "" Then
                        If cmbcats.Text.Trim = "" Then Exit Sub
                        'Cls.fill_combo_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbname)
                        Cls.fill_combo_DataAdapter_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbname)

                        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        'If Company_Branch_ID = "0" Then
                        '    cmd.CommandText = "select distinct sname from Items where group_name =N'" & cmbcats.Text & "' and QuickSearch=0 order by 1"
                        'Else
                        '    cmd.CommandText = "select distinct sname from Items where group_name =N'" & cmbcats.Text & "' and QuickSearch=0 and Company_Branch_ID = N'" & Company_Branch_ID & "'  or Company_Branch_ID =N'0' order by 1"
                        'End If
                        'dr = cmd.ExecuteReader
                        'Do While dr.Read = True
                        '    cmbname.Items.Add(Trim(dr(0)))
                        'Loop
                        cmbname.Text = ""
                    End If
                Catch ex As Exception
                    ErrorHandling(ex, Me.Text)
                End Try
            End If
            Actions = False
        End If
    End Sub

    Private Sub Dgv_Add_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Dgv_Add.DoubleClick
        Try
            If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
            If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            txtprc.Text = Dgv_Add.SelectedRows(0).Cells(0).Value
            cmbcats.Text = Dgv_Add.SelectedRows(0).Cells(1).Value
            cmbname.Text = Dgv_Add.SelectedRows(0).Cells(2).Value
            txtprice.Text = Dgv_Add.SelectedRows(0).Cells(3).Value
            txtqunt.Text = Dgv_Add.SelectedRows(0).Cells(4).Value
            txtquntUnity.Text = Dgv_Add.SelectedRows(0).Cells(5).Value
            cmbUnityItems.Text = Dgv_Add.SelectedRows(0).Cells(6).Value
            cmbStores.Text = Dgv_Add.SelectedRows(0).Cells(8).Value
            txtbill_EndDate.Text = Dgv_Add.SelectedRows(0).Cells(10).Value
            txtbill_no_Expired.Text = Dgv_Add.SelectedRows(0).Cells(11).Value
            ItemsRateVAT = Dgv_Add.SelectedRows(0).Cells(12).Value
            TotalValueVAT = Dgv_Add.SelectedRows(0).Cells(13).Value
            txtdiscBill.Text = Dgv_Add.SelectedRows(0).Cells(14).Value
            DiscountsValue = Dgv_Add.SelectedRows(0).Cells(15).Value
            lblDiscount_Price_After.Text = Dgv_Add.SelectedRows(0).Cells(16).Value
            DiscountsTin = Dgv_Add.SelectedRows(0).Cells(17).Value
            StateDisc = Dgv_Add.SelectedRows(0).Cells(19).Value

            If StateDisc = "قيمة" Then
                ChkVal2.Checked = True
            Else
                ChkCent2.Checked = True
            End If
            If NotUnityItemsProgram = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 0 : End If
                txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
            Else
                txtqunt.Text = Val(txtquntUnity.Text)
            End If

            txtqunt.Text = Dgv_Add.SelectedRows(0).Cells(4).Value

            txtStore.Text = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)
            txtprice.SelectAll()
            txtprice.Focus()


            If DealingWithSerialItems = "YES" Then
                If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
                If (Dgv_Add.Rows.Count) = 0 Then Beep() : Exit Sub
                SN_Barcode = Dgv_Add.SelectedRows(0).Cells(0).Value
                SN_Store = Dgv_Add.SelectedRows(0).Cells(8).Value
                SN_Qunt = Dgv_Add.SelectedRows(0).Cells(5).Value
                SN_billno = txtbillno.Text

                FrmSales_SerialNumber.Close()
                FrmSales_SerialNumber.Show()
            End If

            Bol = False
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub btnClose_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClose.Click
        PanelPrice.Top = 10000
    End Sub

    Private Sub btnHelp_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnHelp.Click
        PanelPrice.Top = 4
    End Sub

    Private Sub ChkCash_CheckedChanged(sender As Object, e As EventArgs) Handles ChkCash.CheckedChanged
        If ChkCash.Checked = True Then
            'pay = txtpaying.Text
            txtpaying.Text = txttotalafterdisc.Text
            txtpaying.ReadOnly = True
            txtpaying.Enabled = False
            sumdisc()
        End If
    End Sub

    Private Sub ChkState_CheckedChanged_1(sender As Object, e As EventArgs) Handles ChkState.CheckedChanged
        If ChkState.Checked = True Then
            'txtpaying.Text = pay
            txtpaying.Text = "0"
            txtpaying.ReadOnly = False
            txtpaying.Enabled = True
            sumdisc()
        End If
    End Sub

    Private Sub chkVisa_CheckedChanged(sender As Object, e As EventArgs) Handles chkVisa.CheckedChanged
        If chkVisa.Checked = True Then
            'pay = txtpaying.Text
            txtpaying.Text = txttotalafterdisc.Text
            txtpaying.ReadOnly = True
            txtpaying.Enabled = False
            sumdisc()
        End If
    End Sub

    Private Sub rdoSectors_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdoSectors.CheckedChanged
        WholeasalePrice()
    End Sub

    Private Sub rdoWhole_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdoWhole.CheckedChanged
        WholeasalePrice()
    End Sub

    Private Sub rdoWholeWhole_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdoWholeWhole.CheckedChanged
        WholeasalePrice()
    End Sub

    Private Sub btnEmp_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnEmp.Click
        PanelEmployees.Top = 4
    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        PanelEmployees.Top = 2000
    End Sub

    Public Sub AverageTinPrice(ByVal itm_id As String, ByVal Stores As String, ByVal Price As Double, ByVal Qunt As Double, ByVal Unity As String, ByVal DiscountRate As Double, ByVal PriceDiscount As Double)
        Try
            Dim PriceAverage As String = 0
            TinPriceAverage = 0
            Dim Xqunt As Double = Qunt
            TinPriceAverage = 0
            If NotUnityItemsProgram = "YES" Then
                PriceAverage = Cls.Get_Code_Value_Branch_More("ItemsUnity", "TinPriceUnit", "itm_id=N'" & itm_id & "' and Unity_Name=N'" & Unity & "'")
                TinPrice = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)

                Price = Cls.Get_Code_Value_Branch_More("ItemsUnity", "SalPriceUnit", "itm_id=N'" & itm_id & "' and Unity_Name=N'" & Unity & "'")
                Price = Math.Round(Convert.ToDouble(Val(Price)), 2)
            Else
                PriceAverage = Cls.Get_Code_Value_Stores_More("items", "TinPriceAverage", "itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'")
                TinPriceAverage = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)

                PriceAverage = Cls.Get_Code_Value_Stores_More("items", "TinPrice", "itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'")
                TinPrice = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)
            End If
            If TinPrice = 0 Or Price = 0 Then
                PriceAverage = Cls.Get_Code_Value_Stores_More("items", "TinPriceAverage", "itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'")
                TinPriceAverage = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)

                PriceAverage = Cls.Get_Code_Value_Stores_More("items", "TinPrice", "itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'")
                TinPrice = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)
            End If

            'If NotUnityItemsProgram = "YES" Then
            '    NumberPieces = Cls.Get_Code_Value_Stores_More("ItemsUnity", "NumberPieces", "itm_id =N'" & itm_id & "' and Unity_Name=N'" & Unity & "'")
            '    If NumberPieces <> 1 Then
            '        Price = Cls.Get_Code_Value_Stores_More("Items", "SalPrice", "itm_id =N'" & itm_id & "' and Stores=N'" & Stores & "'")
            '        If NumberPieces <> 0 Then
            '            Xqunt = Val(NumberPieces) * Val(Qunt)
            '        End If
            '    End If
            'End If

            Price_Unity = Price
            If DiscountRate <> 0 Then
                Price = PriceDiscount
                Price_Unity = PriceDiscount
            End If

            If PriceAverage = "" Then : PriceAverage = 0 : End If

            If LastTinPriceItems = "NO" Then : PriceAverage = TinPriceAverage : Else PriceAverage = TinPrice : End If
            If TinPriceAverage = 0 Then
                PriceAverage = TinPrice
                TinPriceAverage = TinPrice
            End If

            TotalPrice = Price - PriceAverage
            Profits = TotalPrice * Xqunt
            Profits = Math.Round(Profits, 2)

            If DiscountRate <> 0 Then
                If DiscountsTin <> 0 Then
                    Dim TotalDiscountRate As Double = DiscountsTin - DiscountRate
                    Dim TotalRate As Double = Format(Val(PriceAverage) * Val(TotalDiscountRate) / 100, "Fixed")
                    Profits = Format(Val(Xqunt) * Val(TotalRate))
                    Profits = Math.Round(Profits, 2)
                End If
            End If
            'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            'cmd.CommandText = "update Items set TinPriceAverage = " & Val(PriceAverage) & " where itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'" : cmd.ExecuteNonQuery()

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetDebtorlCreditorPrevious()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CreditPrevious , DebitPrevious from Sales_Bill where bill_No=N'" & txtbillno.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                AmntcreditPrevious = dr("CreditPrevious")
                AmntdebitPrevious = dr("DebitPrevious")
            End If

            'If AmntcreditPrevious <> 0 Then
            '    Dim XCredit As Double = AmntcreditPrevious
            '    AmntcreditPrevious = Val(XCredit) - Val(txtstaying.Text)
            'End If
            'If AmntdebitPrevious <> 0 Then
            '    Dim XDebit As Double = AmntdebitPrevious
            '    AmntdebitPrevious = Val(XDebit) - Val(txtstaying.Text)
            'End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbname_DropDown(sender As Object, e As EventArgs) Handles cmbname.DropDown
        cmbname.Text = ""
        txtprice.Text = ""
        txtprc.Text = ""
        txtqunt.Text = ""
        txtTotal.Text = ""
        txtquntUnity.Text = ""
        cmbUnityItems.Text = ""
        txtRateDiscPriceAfter.Text = ""
    End Sub

    Private Sub txtquntUnity_TextChanged(sender As Object, e As EventArgs) Handles txtquntUnity.TextChanged
        cmbUnityItems_SelectedIndexChanged(sender, e)
        MyVars.CheckNumber(txtqunt)
        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If
        TotalBeforeVAT = Val(txtprice.Text) * Val(txtquntUnity.Text)
        If ShowValueVAT = "YES" Then
            TotalValueVAT = Format(Val(TotalBeforeVAT) * Val(ItemsRateVAT) / 100, "Fixed")
        End If
        txtTotal.Text = Math.Round(TotalValueVAT, 2) + Math.Round(TotalBeforeVAT, 2)
        If ShowTax = "0" Then
            txtSalestax.Text = ItemsRateVAT
        End If
        txtTotal.Text = Math.Round(Convert.ToDouble(txtTotal.Text), 2)
        sumdisc1()
    End Sub

    Private Sub txtquntUnity_KeyUp(sender As Object, e As KeyEventArgs) Handles txtquntUnity.KeyUp
        If e.KeyCode = 13 Then
            If NotUnityItemsProgram = "YES" Then
                cmbUnityItems.Focus()
                cmbUnityItems.SelectAll()
            Else
                BtnAdd.PerformClick()
            End If
        End If
    End Sub

    Private Sub cmbUnityItems_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbUnityItems.KeyUp
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub cmbUnityItems_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbUnityItems.SelectedIndexChanged
        Try
            If Bol = False Then
                If NotUnityItemsProgram = "YES" Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 0 : End If
                    txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)

                    If ItemsUnityNumber <> 1 Then
                        If ItemsUnityNumber <> 0 Then
                            Dim PriceNumber As String = Cls.Get_Code_Value_Branch_More("ItemsUnity", "SalPriceUnit", "itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'")
                            If PriceNumber <> "0" Then
                                txtprice.Text = PriceNumber
                            End If
                        End If
                    End If
                Else
                    txtqunt.Text = Val(txtquntUnity.Text)
                End If

                GetItemsUnityTotalCarton()
                sumdisc1()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub txtSalestax_TextChanged(sender As Object, e As EventArgs) Handles txtSalestax.TextChanged
        MyVars.CheckNumber(txtSalestax)

        sumdisc()
    End Sub

    Private Sub Daily_Restrictions()
        Dim Account As String = "" : Dim AccountCode As String = "" : Dim AccountPaying As String = "" : Dim PayingCode As String = ""
        Dim Discounts As String = "" : Dim DiscountsCode As String = "" : Dim AccountTreasury As String = "" : Dim AccountCodeTreasury As String = ""

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مبيعات'" : dr = cmd.ExecuteReader
            If dr.Read Then
                Account = dr("Link_AccountsTree") : AccountCode = dr("ACCNumber")
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مقبوضات عملاء'" : dr = cmd.ExecuteReader
            If dr.Read Then
                AccountPaying = dr("Link_AccountsTree") : PayingCode = dr("ACCNumber")
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'خصومات عملاء'" : dr = cmd.ExecuteReader
            If dr.Read Then
                Discounts = dr("Link_AccountsTree") : DiscountsCode = dr("ACCNumber")
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select ACCName,ACCNumber from AccountsTree where ACCName=N'الخزينة'" : dr = cmd.ExecuteReader
            If dr.Read Then
                AccountTreasury = dr("ACCName") : AccountCodeTreasury = dr("ACCNumber")
            End If

            '========================================================================================
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete From  MOVES where bill_no =N'" & txtbillno.Text & "' and MOVStatement =N'مبيعات'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & txtbillno.Text & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & txtbillno.Text & "' and MOVDNameAccount =N'مقبوضات عملاء'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & txtbillno.Text & "' and MOVDNameAccount =N'خصومات عملاء'" : cmd.ExecuteNonQuery()

            '========================================================================================
            Dim MOVID As String = Cls.MAXRECORD("MOVES", "MOVID")
            Dim MOVRegNumber As String = Cls.MAXRECORD("MOVES", "MOVRegNumber")

            If ChkCash.Checked = True Or chkVisa.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Account & "',N'" & txttotalpeforedisc.Text & "',N'" & txttotalpeforedisc.Text & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If

            If ChkState.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Account & "',N'" & txttotalpeforedisc.Text & "',N'" & txttotalpeforedisc.Text & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If

            If Val(txtpaying.Text) > 0 Then

                ' من حساب / الخزينة
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'" & txtpaying.Text & "',N'0',N'" & AccountTreasury & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' الى حساب / مقبوضات عملاء
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & PayingCode & "',N'" & AccountPaying & "',N'0',N'" & txtpaying.Text & "',N'" & AccountPaying & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If

            If Val(txtdisc.Text) > 0 Then

                ' من حساب / خصومات المبيعات
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & DiscountsCode & "',N'" & Discounts & "',N'" & DiscTotal & "',N'0',N'" & Discounts & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' الى حساب / الخزينة
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'0',N'" & DiscTotal & "',N'" & AccountTreasury & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetDebtorlCreditor()
        Try

            If AmntcreditPrevious <> 0 Then
                Dim XCredit As Double = AmntcreditPrevious
                Amntcredit = Val(XCredit) + Val(txtstaying.Text)
            End If

            If AmntdebitPrevious <> 0 Then
                Dim XDebit As Double = AmntdebitPrevious
                Amntdebit = Val(XDebit) - Val(txtstaying.Text)
            End If

            Dim CustomerBalance As String = Val(Amntcredit) - Val(Amntdebit)
            TotalAccountAfterInvoice = Val(CustomerBalance) + Val(txttotalafterdisc.Text)

            If Amntcredit = 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select vnamntcredit from Customers where Vendorname=N'" & cmbvendores.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    Amntcredit = dr("vnamntcredit")
                End If
            End If
            If Amntdebit = 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select vnamntdebit from Customers where Vendorname=N'" & cmbvendores.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    Amntdebit = dr("vnamntdebit")
                End If
            End If

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Declare Function WriteProfileString Lib "kernel32" Alias "WriteProfileStringA" _
    (ByVal lpszSection As String, ByVal lpszKeyName As String,
    ByVal lpszString As String) As Long

    Private Sub txtbillno_TextChanged_1(sender As Object, e As EventArgs) Handles txtbillno.TextChanged
        MyVars.CheckNumber(txtbillno)
    End Sub

    Private Sub txtdiscBill_TextChanged(sender As Object, e As EventArgs) Handles txtdiscBill.TextChanged
        MyVars.CheckNumber(txtdiscBill)
        sumdisc1()
    End Sub

    Private Sub Label22_MouseDoubleClick(sender As Object, e As MouseEventArgs) Handles Label22.MouseDoubleClick
        Dim MyString As String
        MyString = InputBox("أدخل خصم الصنف", "طلب معلومات", Nothing)

        If MyString <> "" Then
            mykey.SetValue("DiscountItems", MyString)
            txtdiscBill.Text = MyString
            For i As Integer = 0 To Dgv_Add.RowCount - 1
                Dgv_Add.Rows(i).Cells(12).Value = MyString
            Next
            sumdisc1()
        End If
    End Sub

    Private Sub txtdiscBill_MouseDoubleClick(sender As Object, e As MouseEventArgs) Handles txtdiscBill.MouseDoubleClick
        PaneldiscBill.Top = 200
    End Sub

    Private Sub btnClosediscBill_Click(sender As Object, e As EventArgs) Handles btnClosediscBill.Click
        PaneldiscBill.Top = 10000
    End Sub

    Private Sub ChkCent2_CheckedChanged(sender As Object, e As EventArgs) Handles ChkCent2.CheckedChanged
        sumdisc1()
    End Sub

    Private Sub ChkVal2_CheckedChanged(sender As Object, e As EventArgs) Handles ChkVal2.CheckedChanged
        sumdisc1()
    End Sub

    Private Declare Function SendMessage Lib "user32" Alias "SendMessageA" _
         (ByVal hwnd As Long, ByVal wMsg As Long,
         ByVal wParam As Long, ByVal lparam As String) As Long

    Private Sub txtRateDiscPriceAfter_TextChanged(sender As Object, e As EventArgs) Handles txtRateDiscPriceAfter.TextChanged
        txtprice.Text = txtRateDiscPriceAfter.Text
    End Sub

    Private Sub txtpaying_KeyDown(sender As Object, e As KeyEventArgs) Handles txtpaying.KeyDown
        If ((e.KeyCode = 48)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 49)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 50)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 51)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 52)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 53)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 54)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 55)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 56)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 57)) Then : ActionPayCust = True : End If

        If ((e.KeyCode = 96)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 97)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 98)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 99)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 100)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 101)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 102)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 103)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 104)) Then : ActionPayCust = True : End If
        If ((e.KeyCode = 105)) Then : ActionPayCust = True : End If
    End Sub

    Private Sub cmbStores_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbStores.SelectedIndexChanged

    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        PanelSearch.Top = 5000
        PanelSearch.Dock = DockStyle.None
        MaximizeButtons = True
    End Sub

    Private Sub btnMaximizeButtons_Click(sender As Object, e As EventArgs) Handles btnMaximizeButtons.Click
        If MaximizeButtons = True Then
            PanelSearch.Dock = DockStyle.Fill
            MaximizeButtons = False
        Else
            PanelSearch.Dock = DockStyle.None
            MaximizeButtons = True
        End If
        PanelSearch.Location = New System.Drawing.Point(5, 200)
        PanelSearch.Size = New System.Drawing.Size(800, 240)

    End Sub

    Private Sub btnSearchItems_Click(sender As Object, e As EventArgs) Handles btnSearchItems.Click
        PanelSearch.Location = New System.Drawing.Point(5, 240)
        txtsearsh.Focus()
        PanelSearch.Size = New System.Drawing.Size(800, 240)
        txtsearsh_TextChanged(sender, e)
    End Sub

    Private Sub txtsearsh_TextChanged(sender As Object, e As EventArgs) Handles txtsearsh.TextChanged
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
                S = Cls.Get_Select_Grid_S("itm_id As [الباركود],sname as [الاسم],SalPrice As [سعر التجزئة],WholePrice As [سعر الجملة],TinPrice As [سعر الشراء],store as [المخزون]", "items", "Stores=N'" & cmbStores.Text & "'  and QuickSearch=0 and sname <> ''")
            Else
                S = Cls.Get_Select_Grid_S("itm_id as [الباركود],sname as [الاسم],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],TinPrice as [سعر الشراء],store as [المخزون]", "items", "Stores=N'" & cmbStores.Text & "'  and QuickSearch=0 and sname Like N'%" & txtsearsh.Text & "%'")
            End If
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)
            DTGV.Columns(0).Width = 130
            DTGV.Columns(1).Width = 400
            DTGV.Columns(2).Width = 100
            DTGV.Columns(3).Width = 100
            DTGV.Columns(4).Width = 100
            DTGV.Columns(5).Width = 100
            DTGV.Columns(3).Visible = False
            DTGV.Columns(4).Visible = False

            Dim HideQuntItems As String = mykey.GetValue("HideQuntItems", "NO")
            If HideQuntItems = "YES" Then
                DTGV.Columns(5).Visible = False
            Else
                DTGV.Columns(5).Visible = True
            End If

            If PermtionName = "مدير" Then
                DTGV.Columns(3).Visible = True
                DTGV.Columns(4).Visible = True
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbFindCats_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbFindCats.SelectedIndexChanged
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If cmbFindCats.Text = "" Then
                S = Cls.Get_Select_Grid_S("itm_id as [الباركود],sname as [الاسم],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],TinPrice as [سعر الشراء],store as [المخزون]", "items", "Stores=N'" & cmbStores.Text & "' and sname <> ''")
            Else
                S = Cls.Get_Select_Grid_S("itm_id as [الباركود],sname as [الاسم],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],TinPrice as [سعر الشراء],store as [المخزون]", "items", "Stores=N'" & cmbStores.Text & "' and group_name =N'" & cmbFindCats.Text & "'")
            End If
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)
            DTGV.Columns(0).Width = 130
            DTGV.Columns(1).Width = 400
            DTGV.Columns(2).Width = 100
            DTGV.Columns(3).Width = 100
            DTGV.Columns(4).Width = 100
            DTGV.Columns(5).Width = 100
            DTGV.Columns(3).Visible = False
            DTGV.Columns(4).Visible = False

            Dim HideQuntItems As String = mykey.GetValue("HideQuntItems", "NO")
            If HideQuntItems = "YES" Then
                DTGV.Columns(5).Visible = False
            Else
                DTGV.Columns(5).Visible = True
            End If

            If PermtionName = "مدير" Then
                DTGV.Columns(3).Visible = True
                DTGV.Columns(4).Visible = True
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub btnAddNewItems_Click(sender As Object, e As EventArgs) Handles btnAddNewItems.Click
        ActionAddNewItems = True
        FrmItemsNew.ShowDialog()
    End Sub

    Private Sub DTGV_DoubleClick(sender As Object, e As EventArgs) Handles DTGV.DoubleClick
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        txtprc.Text = DTGV.SelectedRows(0).Cells(0).Value
        cmbname.Text = DTGV.SelectedRows(0).Cells(1).Value
        Try
            ActivFocus = True
            If cmbname.Text.Trim = "" Then Exit Sub

            Cls.fill_combo_Stores_Where("Items", "Unity", "sname", cmbname.Text, cmbUnityItems)

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        GetDataBsal()

        txtqunt.Text = 1
        txtquntUnity.Text = 1
        If SelectItemsComboBoxEnter = "NO" Then
            txtquntUnity.Focus()
            txtquntUnity.SelectAll()
        End If
        SearchItemName = cmbname.Text


        'PanelSearch.Top = 5000
        txtsearsh.Text = ""
    End Sub

    Private Sub txtCodeCustomer_KeyUp(sender As Object, e As KeyEventArgs) Handles txtCodeCustomer.KeyUp
        Try
            If e.KeyCode = 13 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select Vendorname from Customers where Cust_Code=N'" & txtCodeCustomer.Text & "' or tel1=N'" & txtCodeCustomer.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    cmbvendores.Text = dr("Vendorname").ToString
                End If
                cmbname.Focus()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Const HWND_BROADCAST As Long = &HFFFF&
    Private Const WM_WININICHANGE As Long = &H1A

    Private Function SetDefaultSystemPrinter3(ByVal strPrinterName As String) As Boolean
        'this method does not valid if the change is correct and does not revert to previous printer if wrong
        Dim DeviceLine As String

        'rebuild a valid device line string 
        DeviceLine = strPrinterName & ",,"

        'Store the new printer information in the 
        '[WINDOWS] section of the WIN.INI file for 
        'the DEVICE= item 
        Call WriteProfileString("windows", "Device", DeviceLine)

        'Cause all applications to reload the INI file 
        Call SendMessage(HWND_BROADCAST, WM_WININICHANGE, 0, "windows")

        Return True
    End Function

    Private Sub GetTextNotActivateEditSalesPrice()
        If TextNotActivateEditSalesPrice = "YES" Then
            txtprice.Enabled = False
        Else
            txtprice.Enabled = True
        End If
    End Sub

    Private Sub SetItemsUnity()
        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 1 : End If
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(txtquntUnity.Text)
        End If
    End Sub

    Private Sub Product_Manufacturing()
        If ItemAddedInvoiceSavedAutomatically = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete From  Manufacturing_BilltINData where bill_no =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  Manufacturing_BillsalData where bill_no =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
        End If

        '============= مشتريات تصنيع ==========================================================================================================================================
        Try
            Dim CostPrice As String = ""
            Dim TotalCostPrice, TotalCost_qu_unity As Double
            MAXRECORDAuto("Manufacturing_BilltINData", "bill_No")
            Dim TypeCurrency As String = mykey.GetValue("TypeCurrency", "جنية مصرى")

            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select CostPrice from View_Product_Manufacturing_Show where itm_id_Manufacturing=N'" & Dgv_Add.Rows(i).Cells(0).Value & "'  and Stores_Manufacturing=N'" & Dgv_Add.Rows(i).Cells(8).Value & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    CostPrice = dr("CostPrice")
                Else
                    GoTo 2
                End If
                TotalCostPrice = Val(CostPrice) * Val(Dgv_Add.Rows(i).Cells(4).Value)
                PriceTinAverage(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value, Dgv_Add.Rows(i).Cells(6).Value, Dgv_Add.Rows(i).Cells(5).Value, TotalCostPrice)

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Manufacturing_BilltINData (Company_Branch_ID,bill_no,itm_id,price,qu,qu_unity,itm_Unity,totalprice,Stores,username,bill_date,TinPriceAverage)  values (N'" & Company_Branch_ID & "',N'" & billnoID & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & CostPrice & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & TotalCostPrice & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "',N'" & UserName & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Val(TotalPriceBeforeAverage.ToString) & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                IM.Store(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value)

                If ConnectOnlineStore = "YES" Then
                    EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
                    StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
                    Cos.UpdateProductStock(StockOnline, Dgv_Add.Rows(i).Cells(0).Value, EditItmId)
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Manufacturing_BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & billnoID & "' and itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "' and Stores =N'" & Dgv_Add.Rows(i).Cells(8).Value & "'" : cmd.ExecuteNonQuery()


2:
            Next
            '============= مبيعات تصنيع ==========================================================================================================================================

            Dim ActivationEmail As String = mykey.GetValue("ActivationEmail", "NO")
            Dim Xstore As Double
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1

                aray_itm_id.Clear() : aray_sname.Clear() : aray_Stores.Clear() : aray_qu.Clear() : aray_TinPrice.Clear()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select itm_id,sname,Stores,itm_Unity,qu,qu_unity,TinPrice from View_Product_Manufacturing where itm_id_Manufacturing=N'" & Dgv_Add.Rows(i).Cells(0).Value & "' and Stores_Manufacturing=N'" & Dgv_Add.Rows(i).Cells(8).Value & "'"
                dr = cmd.ExecuteReader
                Do While dr.Read = True
                    aray_itm_id.Add(dr(0).ToString()) : aray_sname.Add(dr(1).ToString()) : aray_Stores.Add(dr(2).ToString()) : aray_itm_Unity.Add(dr(3).ToString())
                    aray_qu.Add(dr(4).ToString()) : aray_qu_unity.Add(dr(5).ToString()) : aray_TinPrice.Add(dr(6).ToString())
                Loop

                For M As Integer = 0 To aray_itm_id.Count - 1

                    TotalCostPrice = Val(aray_qu(M)) * Val(Dgv_Add.Rows(i).Cells(4).Value)
                    TotalCost_qu_unity = Val(aray_qu_unity(M)) * Val(Dgv_Add.Rows(i).Cells(5).Value)

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    S = "insert into Manufacturing_BillsalData(Company_Branch_ID,bill_no,itm_id,qu,qu_unity,itm_Unity,price,totalprice,Stores,bill_date,UserName)  values("
                    S = S & "N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "',N'" & aray_itm_id(M) & "',N'" & Val(TotalCostPrice) & "',N'" & TotalCost_qu_unity & "',N'" & aray_itm_Unity(M) & "',N'" & aray_TinPrice(M) & "',N'" & Val(TotalCostPrice) * Val(aray_TinPrice(M)) & "',N'" & aray_Stores(M) & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & UserName & "')"
                    cmd.CommandText = S : cmd.ExecuteNonQuery()

                    IM.Store(aray_itm_id(M), aray_Stores(M))

                    If ConnectOnlineStore = "YES" Then
                        EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", aray_itm_id(M))
                        StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", aray_itm_id(M))
                        Cos.UpdateProductStock(StockOnline, aray_itm_id(M), EditItmId)
                    End If

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Manufacturing_BillsalData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbillno.Text.Trim & "' and itm_id = N'" & aray_itm_id(M) & "' and Stores =N'" & aray_Stores(M) & "'" : cmd.ExecuteNonQuery()

                    If ActivationEmail = "YES" Then
                        Xstore = IM.Get_Itm_Store(aray_itm_id(M), aray_Stores(M))
                        If Xstore < 1 Then
                            SendEmail("الكمية بالمخزن قد نفذت", aray_itm_id(M), aray_sname(M), Xstore)
                        End If
                        If Xstore - Val(txtqunt.Text) < IM.Get_Itm_Range(aray_itm_id(M), aray_Stores(M)) Then
                            SendEmail("الكمية وصلت للحد الادنى", aray_itm_id(M), aray_sname(M), Xstore)
                        End If
                    End If
                Next
            Next
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetItemsUnityTotalCarton()
        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from View_ItemsUnity where itm_id=N'" & txtprc.Text & "' and Unity_Name =N'" & cmbUnityItems.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                NumberPieces = dr(0).ToString
            End If

            Dim Itm_Store As Double = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)
            If NumberPieces = 0 Or NumberPieces = 1 Then
                txtStore.Text = Itm_Store
            Else
                If Itm_Store = 0 And NumberPieces = 0 Then
                    txtStore.Text = 0
                Else
                    txtStore.Text = Val(Itm_Store) / Val(NumberPieces)
                    txtStore.Text = Math.Round(Val(txtStore.Text), 2)
                End If
            End If
        Else
            txtqunt.Text = Val(txtquntUnity.Text)
        End If


    End Sub

    Private Sub GetCustomerAddress()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select addr,tel1 from Customers where Vendorname=N'" & cmbvendores.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                CustomerAddress = dr(0).ToString
                CustomerTel = dr(1).ToString
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Phone from Employees where NameEmployee=N'" & cmbEmployees.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                PhoneEmployee = dr(0).ToString
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetShowDiscountRateItemSales()
        If ShowDiscountRateItemSales = "NO" Then
            txtRateDiscPriceAfter.Visible = False
            txtprice.Visible = True
        Else
            txtRateDiscPriceAfter.Visible = True
            txtprice.Visible = False
        End If
    End Sub

    Private Sub LimitCustomerBalance()
        Dim CustomerBalance As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select vnamntcredit,vnamntdebit from Customers where Vendorname =N'" & cmbvendores.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Dim credit As Double = Val(dr(0).ToString)
            Dim debit As Double = Val(dr(1).ToString)
            CustomerBalance = credit - debit
        Else
            CustomerBalance = "0"
        End If

        Dim Limit_DrawDowns_Price As String = 0
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Limit_DrawDowns_Price from  Customers  Where Vendorname = N'" & cmbvendores.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Limit_DrawDowns_Price = dr("Limit_DrawDowns_Price").ToString
        End If

        Dim TotalBalance As Double = Val(CustomerBalance) + Val(txtstaying.Text)
        If Val(Limit_DrawDowns_Price) <> 0 Then
            If Val(TotalBalance) <= Val(Limit_DrawDowns_Price) = 0 Then
                MsgBox("العميل وصل الى الحد الاقصى للسحب من المديونية", MsgBoxStyle.Critical)
                Exit Sub
            End If
        End If
    End Sub

    Private Sub GetPrintSerialNumber()
        If DealingWithSerialItems = "YES" Then

            If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

            Dim aray_1 As New ArrayList : Dim aray_2 As New ArrayList : Dim aray_3 As New ArrayList
            aray_1.Clear() : aray_2.Clear() : aray_3.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select dbo.BillsalData_SerialNumber.Auto_Number As auto, dbo.BillsalData_SerialNumber.Serial_Number_Name As Serial_Number, dbo.Items.sname As Items, dbo.BillsalData_SerialNumber.bill_no  From dbo.BillsalData_SerialNumber LEFT OUTER Join dbo.Items ON dbo.BillsalData_SerialNumber.itm_id = dbo.Items.itm_id  Where (dbo.BillsalData_SerialNumber.bill_no =N'" & txtbillno.Text & "')"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_1.Add(dr(0)) : aray_2.Add(dr(1)) : aray_3.Add(dr(2))
            Loop
            If aray_1.Count = 0 Then
                Exit Sub
            End If
            Cls.delete_Branch_All("PrintSalesPurchases")

            For i As Integer = 0 To aray_1.Count - 1
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into PrintSalesPurchases(disc,itm_id,itm_name,Vendorname,bill_date,billtime,BILL_NO) values"
                S = S & " (N'" & aray_1(i) & "',N'" & aray_2(i) & "',N'" & aray_3(i) & "',N'" & cmbvendores.Text & "',N'" & DateTimePicker1.Text & "',N'" & txtTimeAMBM.Text & "',N'" & txtbillno.Text & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next

            AddReportView()

            Dim rpt
            If PrintSmall = "YES" Then
                rpt = New Rpt_SerialNumberItems_Small
            End If
            If PrintSmall = "NO" Then
                rpt = New Rpt_SerialNumberItems_A4
            End If
            If PrintSmall = "A5" Then
                rpt = New Rpt_SerialNumberItems_A5
            End If
            Dim txt, txtNameAr, txtProgramNameBill As TextObject

            Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
            Dim dt As New DataTable
            dt.Load(dr)
            rpt.SetDataSource(dt)
            txt = rpt.Section1.ReportObjects("txtReportTitel")
            txt.Text = "سيريال للاصناف"
            txtNameAr = rpt.Section1.ReportObjects("txtTitelNameAr")
            txtNameAr.Text = NameArCompay

            If HideProgramNameBill = "YES" Then
                txtProgramNameBill = rpt.Section1.ReportObjects("txtProgramNameBill")
                txtProgramNameBill.Text = ""
            Else
                txtProgramNameBill = rpt.Section1.ReportObjects("txtProgramNameBill")
                txtProgramNameBill.Text = ProgramNameBill
            End If

            If SelectLogoPathOther = "YES" Then
                rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
            Else
                rpt.SetParameterValue("ImageURL", CMPLogoPath)
            End If
            Frm_PrintReports_2.CrystalReportViewer1.ReportSource = rpt
            Frm_PrintReports_2.Text = "سيريال للاصناف"
            Frm_PrintReports_2.Show()

            If RunDatabaseInternet = "YES" Then : connect() : End If

        End If
    End Sub


End Class