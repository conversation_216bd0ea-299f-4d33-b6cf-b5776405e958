﻿Imports CrystalDecisions.CrystalReports.Engine
Imports vb = Microsoft.VisualBasic
Public Class FrmShowOutCoum
    Dim WithEvents BS As New BindingSource
    Dim archiveManager As New Cls_ArchiveManager

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "select id as [رقم],Cats as [البند], Exp_Name as [الاسم],Exp_Value as [القيمة],Exp_Date_ as [التاريخ],Exp_Notes as [ملاحظات],Payment_Status as [حالة الدفعة],Vendorname as [اسم المورد] from Expenses where id > 0"
        If Chek_AllExpenses.Checked = False Then
            If Cmb_ExpName.Text.Trim <> "" Then
                S = S & " and Exp_Name =N'" & Cmb_ExpName.Text.Trim & "'"
            End If
            If ComboBox1.Text.Trim <> "" Then
                S = S & " and cats =N'" & ComboBox1.Text.Trim & "'"
            End If
            If cmbvendores.Text.Trim <> "" Then
                S = S & " and Vendorname =N'" & cmbvendores.Text.Trim & "'"
            End If
        End If

        If chkAllPayment_Status.Checked = False Then
            If ChkCash.Checked = True Then
                S = S & " and Payment_Status =N'" & ChkCash.Text.Trim & "'"
            End If
            If ChkState.Checked = True Then
                S = S & " and Payment_Status =N'" & ChkState.Text.Trim & "'"
            End If
            If chkBank.Checked = True Then
                S = S & " and Payment_Status =N'" & chkBank.Text.Trim & "'"
            End If
            If rdoCashWallet.Checked = True Then
                S = S & " and Payment_Status =N'" & rdoCashWallet.Text.Trim & "'"
            End If
        End If
        If Chek_WithoutDate.Checked = False Then
            S = S & " and Exp_Date >=N'" & Cls.C_date(Dtp_from.Text) & "' and Exp_Date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [التاريخ]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [الاسم]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
        DataGridView1.Columns(0).Width = 0

        Dim SM As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM = SM + DataGridView1.Rows(i).Cells(3).Value
        Next
        TxtTotal.Text = FormatNumberWithSeparators(SM)

    End Sub

    Private Sub Chek_AllExpenses_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Chek_AllExpenses.CheckedChanged
        If Chek_AllExpenses.Checked = True Then
            Cmb_ExpName.Enabled = False
            ComboBox1.Enabled = False
            cmbvendores.Enabled = False
            Cmb_ExpName.SelectedIndex = -1
            ComboBox1.SelectedIndex = -1
        Else
            Cmb_ExpName.Enabled = True
            ComboBox1.Enabled = True
            cmbvendores.Enabled = True
        End If
        GetData()
    End Sub

    Private Sub Chek_WithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Chek_WithoutDate.CheckedChanged
        If Chek_WithoutDate.Checked = True Then
            Dtp_from.Enabled = False
            Dtp_To.Enabled = False

        Else
            Dtp_from.Enabled = True
            Dtp_To.Enabled = True
        End If
        GetData()
    End Sub

    Private Sub FrmShowOutCoum_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Bra.Fil("Cat_Expenses", "g_name", ComboBox1)
    End Sub

    Private Sub Btn_Print_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Btn_Print.Click

        Cls.GetDefaultPrinterA4()

        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintAllItems")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            S = "Insert into PrintAllItems(Company_Branch_ID,group_name,expe,ValExp,bill_date,Notes) values (N'" & Company_Branch_ID & "',N'" & DataGridView1.Rows(i).Cells(1).Value & "',N'" & DataGridView1.Rows(i).Cells(2).Value & "',N'" & DataGridView1.Rows(i).Cells(3).Value & "',N'" & DataGridView1.Rows(i).Cells(4).Value & "',N'" & DataGridView1.Rows(i).Cells(5).Value & "')"
            cmd.CommandText = S
            cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt
        If PrintSmall = "YES" Then
            Cls.GetDefaultPrinterBill()
            rpt = New Rpt_Exp_Small
        End If
        If PrintSmall = "NO" Then
            rpt = New RptExp
        End If
        If PrintSmall = "A5" Then
            rpt = New RptExp
        End If

        Dim txtNameAr, txtNameEn, txtTotalExpe As TextObject

        Cls.Select_More_Data_Branch_Print("PrintAllItems", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
        txtNameEn.Text = NameEnCompany
        txtTotalExpe = rpt.Section1.ReportObjects("txtTotal")
        txtTotalExpe.Text = TxtTotal.Text
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Sub print(ByVal expe As String, ByVal ValExp As String, ByVal bill_date As String, ByVal Notes As String)
        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "SP_Print_Expenses"
        cmd.Parameters.Clear()
        cmd.Parameters.AddWithValue("@expe", expe)
        cmd.Parameters.AddWithValue("@ValExp", ValExp)
        cmd.Parameters.AddWithValue("@bill_date", bill_date)
        cmd.Parameters.AddWithValue("@Notes", Notes)

        cmd.ExecuteNonQuery()
    End Sub

    Private Sub Cmb_ExpName_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Cmb_ExpName.SelectedIndexChanged
        GetData()
    End Sub

    Private Sub Dtp_from_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Dtp_from.ValueChanged
        GetData()
    End Sub

    Private Sub Dtp_To_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Dtp_To.ValueChanged
        GetData()
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim ItmID As String
            ItmID = DataGridView1.SelectedRows(i).Cells(0).Value

            '=========================================== Archive Manager ============================================================
            archiveManager.ArchiveAndDeleteExpensesData("Delete", ItmID, UserName, "تم حذف المصروف لعدم النشاط")
            '=========================================== Archive Manager ============================================================


            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete From  Expenses where id =N'" & ItmID & "'" : cmd.ExecuteNonQuery()

            cmd.CommandText = "delete From  MOVES where bill_no =N'" & ItmID & "' and MOVStatement =N'مصروفات'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'مصروفات'" : cmd.ExecuteNonQuery()

        Next
        GetData()
    End Sub

    Private Sub ComboBox1_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ComboBox1.SelectedIndexChanged
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Exp_Name from  Expenses where cats =N'" & ComboBox1.Text.Trim & "'"
        dr = cmd.ExecuteReader
        Cmb_ExpName.Items.Clear() : Cmb_ExpName.SelectedIndex = -1
        Do While dr.Read
            Cmb_ExpName.Items.Add(dr(0))
        Loop
        GetData()
    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        GetData()
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub

    Private Sub chkAllPayment_Status_CheckedChanged(sender As Object, e As EventArgs) Handles chkAllPayment_Status.CheckedChanged
        If chkAllPayment_Status.Checked = True Then
            ChkCash.Enabled = False
            ChkState.Enabled = False
            chkBank.Enabled = False
            rdoCashWallet.Enabled = False
        Else
            ChkCash.Enabled = True
            ChkState.Enabled = True
            chkBank.Enabled = True
            rdoCashWallet.Enabled = True
        End If
    End Sub
End Class