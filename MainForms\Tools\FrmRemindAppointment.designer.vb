﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class FrmRemindAppointment
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FrmRemindAppointment))
        Me.txtsearsh = New System.Windows.Forms.TextBox()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.bgHeader = New System.Windows.Forms.Panel()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.Label26 = New System.Windows.Forms.Label()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.Panel5 = New System.Windows.Forms.Panel()
        Me.Panel6 = New System.Windows.Forms.Panel()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.Panel_Search = New System.Windows.Forms.Panel()
        Me.btnAdd = New System.Windows.Forms.Button()
        Me.btnDelete = New System.Windows.Forms.Button()
        Me.btnEdit = New System.Windows.Forms.Button()
        Me.DataGridView1 = New System.Windows.Forms.DataGridView()
        Me.Panel8 = New System.Windows.Forms.Panel()
        Me.PanelRemind_Confirm_Active_ALL = New System.Windows.Forms.Panel()
        Me.rdoRemind_Confirm_Inactive_View = New System.Windows.Forms.RadioButton()
        Me.rdoRemind_Confirm_Active_View = New System.Windows.Forms.RadioButton()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.cmbCustomersView = New System.Windows.Forms.ComboBox()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.txtRemind_Number_View = New System.Windows.Forms.TextBox()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.btnShow = New System.Windows.Forms.Button()
        Me.ChkWithoutDate = New System.Windows.Forms.CheckBox()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.DateTimePicker2 = New System.Windows.Forms.DateTimePicker()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.DateTimePicker1 = New System.Windows.Forms.DateTimePicker()
        Me.chkAll = New System.Windows.Forms.CheckBox()
        Me.PanelAddNew = New System.Windows.Forms.Panel()
        Me.Panel13 = New System.Windows.Forms.Panel()
        Me.rdoRemind_Confirm_Hanging = New System.Windows.Forms.RadioButton()
        Me.PicRemind_Confirm = New System.Windows.Forms.PictureBox()
        Me.rdoRemind_Confirm_Inactive = New System.Windows.Forms.RadioButton()
        Me.rdoRemind_Confirm_Active = New System.Windows.Forms.RadioButton()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.DateTimePicker3 = New System.Windows.Forms.DateTimePicker()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Panel12 = New System.Windows.Forms.Panel()
        Me.Panel11 = New System.Windows.Forms.Panel()
        Me.Panel10 = New System.Windows.Forms.Panel()
        Me.Panel9 = New System.Windows.Forms.Panel()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.cmbRemind_Confirmation = New System.Windows.Forms.ComboBox()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Panel7 = New System.Windows.Forms.Panel()
        Me.btnClose = New System.Windows.Forms.Button()
        Me.Panel4 = New System.Windows.Forms.Panel()
        Me.txtRemind_Number = New System.Windows.Forms.TextBox()
        Me.btnSave = New System.Windows.Forms.Button()
        Me.cmbCustomers = New System.Windows.Forms.ComboBox()
        Me.dtpCapital_Date = New System.Windows.Forms.DateTimePicker()
        Me.LblEsal = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.txtNotes = New System.Windows.Forms.TextBox()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.rdoRemind_Confirm_Hanging_View = New System.Windows.Forms.RadioButton()
        Me.chkRemind_Confirm_Active_ALL = New System.Windows.Forms.CheckBox()
        Me.bgHeader.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel_Search.SuspendLayout()
        CType(Me.DataGridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel8.SuspendLayout()
        Me.PanelRemind_Confirm_Active_ALL.SuspendLayout()
        Me.PanelAddNew.SuspendLayout()
        Me.Panel13.SuspendLayout()
        CType(Me.PicRemind_Confirm, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        Me.Panel7.SuspendLayout()
        Me.SuspendLayout()
        '
        'txtsearsh
        '
        Me.txtsearsh.Location = New System.Drawing.Point(605, 750)
        Me.txtsearsh.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtsearsh.Name = "txtsearsh"
        Me.txtsearsh.Size = New System.Drawing.Size(171, 24)
        Me.txtsearsh.TabIndex = 19
        Me.txtsearsh.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        Me.txtsearsh.Visible = False
        '
        'Label12
        '
        Me.Label12.AutoSize = True
        Me.Label12.Location = New System.Drawing.Point(784, 754)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(111, 17)
        Me.Label12.TabIndex = 85
        Me.Label12.Text = "بحث باسم الصنف"
        Me.Label12.Visible = False
        '
        'bgHeader
        '
        Me.bgHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(239, Byte), Integer), CType(CType(53, Byte), Integer), CType(CType(58, Byte), Integer))
        Me.bgHeader.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.bgHeader.Controls.Add(Me.PictureBox1)
        Me.bgHeader.Controls.Add(Me.Label26)
        Me.bgHeader.Cursor = System.Windows.Forms.Cursors.Default
        Me.bgHeader.Dock = System.Windows.Forms.DockStyle.Top
        Me.bgHeader.Font = New System.Drawing.Font("Arial", 8.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bgHeader.ForeColor = System.Drawing.SystemColors.WindowText
        Me.bgHeader.Location = New System.Drawing.Point(0, 0)
        Me.bgHeader.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.bgHeader.Name = "bgHeader"
        Me.bgHeader.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.bgHeader.Size = New System.Drawing.Size(1191, 67)
        Me.bgHeader.TabIndex = 204
        Me.bgHeader.TabStop = True
        '
        'PictureBox1
        '
        Me.PictureBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureBox1.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.RemindAppointment
        Me.PictureBox1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.PictureBox1.Location = New System.Drawing.Point(1054, -6)
        Me.PictureBox1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(116, 76)
        Me.PictureBox1.TabIndex = 11
        Me.PictureBox1.TabStop = False
        '
        'Label26
        '
        Me.Label26.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label26.AutoSize = True
        Me.Label26.BackColor = System.Drawing.Color.Transparent
        Me.Label26.Cursor = System.Windows.Forms.Cursors.Default
        Me.Label26.Font = New System.Drawing.Font("JF Flat", 15.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label26.ForeColor = System.Drawing.Color.White
        Me.Label26.Location = New System.Drawing.Point(479, 11)
        Me.Label26.Name = "Label26"
        Me.Label26.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.Label26.Size = New System.Drawing.Size(214, 47)
        Me.Label26.TabIndex = 10
        Me.Label26.Text = "تذكير بالمواعيد"
        '
        'Panel2
        '
        Me.Panel2.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel2.Location = New System.Drawing.Point(0, 67)
        Me.Panel2.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(1191, 6)
        Me.Panel2.TabIndex = 205
        '
        'Panel5
        '
        Me.Panel5.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel5.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel5.Location = New System.Drawing.Point(1185, 73)
        Me.Panel5.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel5.Name = "Panel5"
        Me.Panel5.Size = New System.Drawing.Size(6, 643)
        Me.Panel5.TabIndex = 206
        '
        'Panel6
        '
        Me.Panel6.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel6.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel6.Location = New System.Drawing.Point(0, 73)
        Me.Panel6.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel6.Name = "Panel6"
        Me.Panel6.Size = New System.Drawing.Size(6, 643)
        Me.Panel6.TabIndex = 207
        '
        'Panel3
        '
        Me.Panel3.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel3.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel3.Location = New System.Drawing.Point(6, 710)
        Me.Panel3.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(1179, 6)
        Me.Panel3.TabIndex = 208
        '
        'Panel_Search
        '
        Me.Panel_Search.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel_Search.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel_Search.Controls.Add(Me.btnAdd)
        Me.Panel_Search.Controls.Add(Me.btnDelete)
        Me.Panel_Search.Controls.Add(Me.btnEdit)
        Me.Panel_Search.Controls.Add(Me.DataGridView1)
        Me.Panel_Search.Controls.Add(Me.Panel8)
        Me.Panel_Search.Location = New System.Drawing.Point(3, 73)
        Me.Panel_Search.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel_Search.Name = "Panel_Search"
        Me.Panel_Search.Size = New System.Drawing.Size(1182, 639)
        Me.Panel_Search.TabIndex = 373
        '
        'btnAdd
        '
        Me.btnAdd.BackColor = System.Drawing.Color.Transparent
        Me.btnAdd.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Add_1
        Me.btnAdd.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnAdd.Font = New System.Drawing.Font("Tahoma", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnAdd.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnAdd.Location = New System.Drawing.Point(261, 564)
        Me.btnAdd.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnAdd.Name = "btnAdd"
        Me.btnAdd.Size = New System.Drawing.Size(118, 66)
        Me.btnAdd.TabIndex = 241
        Me.btnAdd.UseVisualStyleBackColor = False
        '
        'btnDelete
        '
        Me.btnDelete.BackColor = System.Drawing.Color.Transparent
        Me.btnDelete.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Delete
        Me.btnDelete.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnDelete.Font = New System.Drawing.Font("Tahoma", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnDelete.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnDelete.Location = New System.Drawing.Point(13, 563)
        Me.btnDelete.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnDelete.Name = "btnDelete"
        Me.btnDelete.Size = New System.Drawing.Size(118, 66)
        Me.btnDelete.TabIndex = 240
        Me.btnDelete.UseVisualStyleBackColor = False
        '
        'btnEdit
        '
        Me.btnEdit.BackColor = System.Drawing.Color.Transparent
        Me.btnEdit.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.items_edit_1
        Me.btnEdit.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnEdit.Font = New System.Drawing.Font("Tahoma", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnEdit.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnEdit.Location = New System.Drawing.Point(137, 563)
        Me.btnEdit.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnEdit.Name = "btnEdit"
        Me.btnEdit.Size = New System.Drawing.Size(118, 66)
        Me.btnEdit.TabIndex = 242
        Me.btnEdit.UseVisualStyleBackColor = False
        '
        'DataGridView1
        '
        Me.DataGridView1.AllowUserToAddRows = False
        Me.DataGridView1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DataGridView1.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill
        Me.DataGridView1.BackgroundColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle1.Font = New System.Drawing.Font("JF Flat", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle1.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle1.SelectionBackColor = System.Drawing.Color.FromArgb(CType(CType(251, Byte), Integer), CType(CType(238, Byte), Integer), CType(CType(72, Byte), Integer))
        DataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.Desktop
        DataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DataGridView1.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle1
        Me.DataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window
        DataGridViewCellStyle2.Font = New System.Drawing.Font("JF Flat", 7.8!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.FromArgb(CType(CType(251, Byte), Integer), CType(CType(238, Byte), Integer), CType(CType(72, Byte), Integer))
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.Desktop
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.DataGridView1.DefaultCellStyle = DataGridViewCellStyle2
        Me.DataGridView1.Location = New System.Drawing.Point(-1, 143)
        Me.DataGridView1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.DataGridView1.Name = "DataGridView1"
        Me.DataGridView1.ReadOnly = True
        Me.DataGridView1.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        DataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle3.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle3.Font = New System.Drawing.Font("JF Flat", 7.8!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle3.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(CType(CType(251, Byte), Integer), CType(CType(238, Byte), Integer), CType(CType(72, Byte), Integer))
        DataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.Desktop
        DataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DataGridView1.RowHeadersDefaultCellStyle = DataGridViewCellStyle3
        Me.DataGridView1.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.DataGridView1.Size = New System.Drawing.Size(1180, 412)
        Me.DataGridView1.TabIndex = 25
        '
        'Panel8
        '
        Me.Panel8.Controls.Add(Me.chkRemind_Confirm_Active_ALL)
        Me.Panel8.Controls.Add(Me.PanelRemind_Confirm_Active_ALL)
        Me.Panel8.Controls.Add(Me.Label10)
        Me.Panel8.Controls.Add(Me.cmbCustomersView)
        Me.Panel8.Controls.Add(Me.Label13)
        Me.Panel8.Controls.Add(Me.txtRemind_Number_View)
        Me.Panel8.Controls.Add(Me.Label9)
        Me.Panel8.Controls.Add(Me.btnShow)
        Me.Panel8.Controls.Add(Me.ChkWithoutDate)
        Me.Panel8.Controls.Add(Me.Label7)
        Me.Panel8.Controls.Add(Me.DateTimePicker2)
        Me.Panel8.Controls.Add(Me.Label5)
        Me.Panel8.Controls.Add(Me.DateTimePicker1)
        Me.Panel8.Controls.Add(Me.chkAll)
        Me.Panel8.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel8.Location = New System.Drawing.Point(0, 0)
        Me.Panel8.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel8.Name = "Panel8"
        Me.Panel8.Size = New System.Drawing.Size(1180, 139)
        Me.Panel8.TabIndex = 13
        '
        'PanelRemind_Confirm_Active_ALL
        '
        Me.PanelRemind_Confirm_Active_ALL.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelRemind_Confirm_Active_ALL.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.PanelRemind_Confirm_Active_ALL.Controls.Add(Me.rdoRemind_Confirm_Hanging_View)
        Me.PanelRemind_Confirm_Active_ALL.Controls.Add(Me.rdoRemind_Confirm_Inactive_View)
        Me.PanelRemind_Confirm_Active_ALL.Controls.Add(Me.rdoRemind_Confirm_Active_View)
        Me.PanelRemind_Confirm_Active_ALL.Location = New System.Drawing.Point(507, 18)
        Me.PanelRemind_Confirm_Active_ALL.Name = "PanelRemind_Confirm_Active_ALL"
        Me.PanelRemind_Confirm_Active_ALL.Size = New System.Drawing.Size(99, 98)
        Me.PanelRemind_Confirm_Active_ALL.TabIndex = 418
        '
        'rdoRemind_Confirm_Inactive_View
        '
        Me.rdoRemind_Confirm_Inactive_View.AutoSize = True
        Me.rdoRemind_Confirm_Inactive_View.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.rdoRemind_Confirm_Inactive_View.Location = New System.Drawing.Point(2, 36)
        Me.rdoRemind_Confirm_Inactive_View.Name = "rdoRemind_Confirm_Inactive_View"
        Me.rdoRemind_Confirm_Inactive_View.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.rdoRemind_Confirm_Inactive_View.Size = New System.Drawing.Size(87, 28)
        Me.rdoRemind_Confirm_Inactive_View.TabIndex = 1
        Me.rdoRemind_Confirm_Inactive_View.Text = "غير نشط"
        Me.rdoRemind_Confirm_Inactive_View.UseVisualStyleBackColor = True
        '
        'rdoRemind_Confirm_Active_View
        '
        Me.rdoRemind_Confirm_Active_View.AutoSize = True
        Me.rdoRemind_Confirm_Active_View.Checked = True
        Me.rdoRemind_Confirm_Active_View.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.rdoRemind_Confirm_Active_View.Location = New System.Drawing.Point(27, 7)
        Me.rdoRemind_Confirm_Active_View.Name = "rdoRemind_Confirm_Active_View"
        Me.rdoRemind_Confirm_Active_View.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.rdoRemind_Confirm_Active_View.Size = New System.Drawing.Size(62, 28)
        Me.rdoRemind_Confirm_Active_View.TabIndex = 0
        Me.rdoRemind_Confirm_Active_View.TabStop = True
        Me.rdoRemind_Confirm_Active_View.Text = "نشط"
        Me.rdoRemind_Confirm_Active_View.UseVisualStyleBackColor = True
        '
        'Label10
        '
        Me.Label10.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label10.AutoSize = True
        Me.Label10.BackColor = System.Drawing.Color.Transparent
        Me.Label10.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.Label10.ForeColor = System.Drawing.Color.Black
        Me.Label10.Location = New System.Drawing.Point(612, 48)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(88, 24)
        Me.Label10.TabIndex = 417
        Me.Label10.Text = "حالة التذكير"
        '
        'cmbCustomersView
        '
        Me.cmbCustomersView.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbCustomersView.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbCustomersView.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbCustomersView.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.cmbCustomersView.FormattingEnabled = True
        Me.cmbCustomersView.Items.AddRange(New Object() {"نقدا"})
        Me.cmbCustomersView.Location = New System.Drawing.Point(790, 71)
        Me.cmbCustomersView.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbCustomersView.Name = "cmbCustomersView"
        Me.cmbCustomersView.Size = New System.Drawing.Size(273, 36)
        Me.cmbCustomersView.TabIndex = 415
        '
        'Label13
        '
        Me.Label13.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label13.AutoSize = True
        Me.Label13.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label13.ForeColor = System.Drawing.SystemColors.Desktop
        Me.Label13.Location = New System.Drawing.Point(1067, 76)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(99, 26)
        Me.Label13.TabIndex = 416
        Me.Label13.Text = "أسم العميل"
        '
        'txtRemind_Number_View
        '
        Me.txtRemind_Number_View.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtRemind_Number_View.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtRemind_Number_View.Location = New System.Drawing.Point(790, 21)
        Me.txtRemind_Number_View.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtRemind_Number_View.Name = "txtRemind_Number_View"
        Me.txtRemind_Number_View.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtRemind_Number_View.Size = New System.Drawing.Size(273, 34)
        Me.txtRemind_Number_View.TabIndex = 414
        Me.txtRemind_Number_View.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label9
        '
        Me.Label9.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label9.AutoSize = True
        Me.Label9.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label9.Location = New System.Drawing.Point(1071, 26)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(91, 24)
        Me.Label9.TabIndex = 413
        Me.Label9.Text = "رقم العملية"
        '
        'btnShow
        '
        Me.btnShow.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnShow.BackColor = System.Drawing.Color.Transparent
        Me.btnShow.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Search3
        Me.btnShow.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnShow.Font = New System.Drawing.Font("Tahoma", 9.75!)
        Me.btnShow.Location = New System.Drawing.Point(13, 21)
        Me.btnShow.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnShow.Name = "btnShow"
        Me.btnShow.Size = New System.Drawing.Size(90, 80)
        Me.btnShow.TabIndex = 412
        Me.btnShow.UseVisualStyleBackColor = False
        '
        'ChkWithoutDate
        '
        Me.ChkWithoutDate.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ChkWithoutDate.AutoSize = True
        Me.ChkWithoutDate.Checked = True
        Me.ChkWithoutDate.CheckState = System.Windows.Forms.CheckState.Checked
        Me.ChkWithoutDate.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.ChkWithoutDate.Location = New System.Drawing.Point(109, 51)
        Me.ChkWithoutDate.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.ChkWithoutDate.Name = "ChkWithoutDate"
        Me.ChkWithoutDate.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.ChkWithoutDate.Size = New System.Drawing.Size(61, 28)
        Me.ChkWithoutDate.TabIndex = 409
        Me.ChkWithoutDate.Text = "الكل"
        Me.ChkWithoutDate.UseVisualStyleBackColor = True
        '
        'Label7
        '
        Me.Label7.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.Label7.Location = New System.Drawing.Point(340, 77)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(34, 24)
        Me.Label7.TabIndex = 408
        Me.Label7.Text = "الى"
        '
        'DateTimePicker2
        '
        Me.DateTimePicker2.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DateTimePicker2.CustomFormat = "dd/MM/yyyy"
        Me.DateTimePicker2.Enabled = False
        Me.DateTimePicker2.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.DateTimePicker2.Format = System.Windows.Forms.DateTimePickerFormat.[Short]
        Me.DateTimePicker2.Location = New System.Drawing.Point(177, 71)
        Me.DateTimePicker2.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.DateTimePicker2.Name = "DateTimePicker2"
        Me.DateTimePicker2.RightToLeftLayout = True
        Me.DateTimePicker2.Size = New System.Drawing.Size(157, 36)
        Me.DateTimePicker2.TabIndex = 407
        '
        'Label5
        '
        Me.Label5.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.Label5.Location = New System.Drawing.Point(342, 27)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(29, 24)
        Me.Label5.TabIndex = 406
        Me.Label5.Text = "من"
        '
        'DateTimePicker1
        '
        Me.DateTimePicker1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DateTimePicker1.CustomFormat = "dd/MM/yyyy"
        Me.DateTimePicker1.Enabled = False
        Me.DateTimePicker1.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.DateTimePicker1.Format = System.Windows.Forms.DateTimePickerFormat.[Short]
        Me.DateTimePicker1.Location = New System.Drawing.Point(179, 21)
        Me.DateTimePicker1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.DateTimePicker1.Name = "DateTimePicker1"
        Me.DateTimePicker1.RightToLeftLayout = True
        Me.DateTimePicker1.Size = New System.Drawing.Size(157, 36)
        Me.DateTimePicker1.TabIndex = 405
        '
        'chkAll
        '
        Me.chkAll.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.chkAll.AutoSize = True
        Me.chkAll.Checked = True
        Me.chkAll.CheckState = System.Windows.Forms.CheckState.Checked
        Me.chkAll.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.chkAll.Location = New System.Drawing.Point(723, 46)
        Me.chkAll.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.chkAll.Name = "chkAll"
        Me.chkAll.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkAll.Size = New System.Drawing.Size(61, 28)
        Me.chkAll.TabIndex = 404
        Me.chkAll.Text = "الكل"
        Me.chkAll.UseVisualStyleBackColor = True
        '
        'PanelAddNew
        '
        Me.PanelAddNew.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelAddNew.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.PanelAddNew.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.PanelAddNew.Controls.Add(Me.Panel13)
        Me.PanelAddNew.Controls.Add(Me.Label8)
        Me.PanelAddNew.Controls.Add(Me.DateTimePicker3)
        Me.PanelAddNew.Controls.Add(Me.Label3)
        Me.PanelAddNew.Controls.Add(Me.Panel12)
        Me.PanelAddNew.Controls.Add(Me.Panel11)
        Me.PanelAddNew.Controls.Add(Me.Panel10)
        Me.PanelAddNew.Controls.Add(Me.Panel9)
        Me.PanelAddNew.Controls.Add(Me.Label1)
        Me.PanelAddNew.Controls.Add(Me.cmbRemind_Confirmation)
        Me.PanelAddNew.Controls.Add(Me.Label11)
        Me.PanelAddNew.Controls.Add(Me.Panel1)
        Me.PanelAddNew.Controls.Add(Me.Panel7)
        Me.PanelAddNew.Controls.Add(Me.Panel4)
        Me.PanelAddNew.Controls.Add(Me.txtRemind_Number)
        Me.PanelAddNew.Controls.Add(Me.btnSave)
        Me.PanelAddNew.Controls.Add(Me.cmbCustomers)
        Me.PanelAddNew.Controls.Add(Me.dtpCapital_Date)
        Me.PanelAddNew.Controls.Add(Me.LblEsal)
        Me.PanelAddNew.Controls.Add(Me.Label4)
        Me.PanelAddNew.Controls.Add(Me.txtNotes)
        Me.PanelAddNew.Controls.Add(Me.Label6)
        Me.PanelAddNew.Location = New System.Drawing.Point(212, 120)
        Me.PanelAddNew.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.PanelAddNew.Name = "PanelAddNew"
        Me.PanelAddNew.Size = New System.Drawing.Size(712, 544)
        Me.PanelAddNew.TabIndex = 374
        '
        'Panel13
        '
        Me.Panel13.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Panel13.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel13.Controls.Add(Me.rdoRemind_Confirm_Hanging)
        Me.Panel13.Controls.Add(Me.PicRemind_Confirm)
        Me.Panel13.Controls.Add(Me.rdoRemind_Confirm_Inactive)
        Me.Panel13.Controls.Add(Me.rdoRemind_Confirm_Active)
        Me.Panel13.Location = New System.Drawing.Point(49, 438)
        Me.Panel13.Name = "Panel13"
        Me.Panel13.Size = New System.Drawing.Size(467, 37)
        Me.Panel13.TabIndex = 384
        '
        'rdoRemind_Confirm_Hanging
        '
        Me.rdoRemind_Confirm_Hanging.AutoSize = True
        Me.rdoRemind_Confirm_Hanging.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.rdoRemind_Confirm_Hanging.Location = New System.Drawing.Point(154, 3)
        Me.rdoRemind_Confirm_Hanging.Name = "rdoRemind_Confirm_Hanging"
        Me.rdoRemind_Confirm_Hanging.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.rdoRemind_Confirm_Hanging.Size = New System.Drawing.Size(69, 28)
        Me.rdoRemind_Confirm_Hanging.TabIndex = 3
        Me.rdoRemind_Confirm_Hanging.Text = "معلق"
        Me.rdoRemind_Confirm_Hanging.UseVisualStyleBackColor = True
        '
        'PicRemind_Confirm
        '
        Me.PicRemind_Confirm.BackColor = System.Drawing.Color.Transparent
        Me.PicRemind_Confirm.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Circle_Green
        Me.PicRemind_Confirm.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.PicRemind_Confirm.Location = New System.Drawing.Point(7, 3)
        Me.PicRemind_Confirm.Name = "PicRemind_Confirm"
        Me.PicRemind_Confirm.Size = New System.Drawing.Size(33, 29)
        Me.PicRemind_Confirm.TabIndex = 2
        Me.PicRemind_Confirm.TabStop = False
        '
        'rdoRemind_Confirm_Inactive
        '
        Me.rdoRemind_Confirm_Inactive.AutoSize = True
        Me.rdoRemind_Confirm_Inactive.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.rdoRemind_Confirm_Inactive.Location = New System.Drawing.Point(254, 3)
        Me.rdoRemind_Confirm_Inactive.Name = "rdoRemind_Confirm_Inactive"
        Me.rdoRemind_Confirm_Inactive.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.rdoRemind_Confirm_Inactive.Size = New System.Drawing.Size(87, 28)
        Me.rdoRemind_Confirm_Inactive.TabIndex = 1
        Me.rdoRemind_Confirm_Inactive.Text = "غير نشط"
        Me.rdoRemind_Confirm_Inactive.UseVisualStyleBackColor = True
        '
        'rdoRemind_Confirm_Active
        '
        Me.rdoRemind_Confirm_Active.AutoSize = True
        Me.rdoRemind_Confirm_Active.Checked = True
        Me.rdoRemind_Confirm_Active.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.rdoRemind_Confirm_Active.Location = New System.Drawing.Point(377, 3)
        Me.rdoRemind_Confirm_Active.Name = "rdoRemind_Confirm_Active"
        Me.rdoRemind_Confirm_Active.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.rdoRemind_Confirm_Active.Size = New System.Drawing.Size(62, 28)
        Me.rdoRemind_Confirm_Active.TabIndex = 0
        Me.rdoRemind_Confirm_Active.TabStop = True
        Me.rdoRemind_Confirm_Active.Text = "نشط"
        Me.rdoRemind_Confirm_Active.UseVisualStyleBackColor = True
        '
        'Label8
        '
        Me.Label8.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label8.AutoSize = True
        Me.Label8.BackColor = System.Drawing.Color.Transparent
        Me.Label8.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.Label8.ForeColor = System.Drawing.Color.Black
        Me.Label8.Location = New System.Drawing.Point(537, 444)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(88, 24)
        Me.Label8.TabIndex = 382
        Me.Label8.Text = "حالة التذكير"
        '
        'DateTimePicker3
        '
        Me.DateTimePicker3.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.DateTimePicker3.CustomFormat = "dd/MM/yyyy"
        Me.DateTimePicker3.Font = New System.Drawing.Font("JF Flat", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.DateTimePicker3.Format = System.Windows.Forms.DateTimePickerFormat.Time
        Me.DateTimePicker3.Location = New System.Drawing.Point(49, 253)
        Me.DateTimePicker3.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.DateTimePicker3.Name = "DateTimePicker3"
        Me.DateTimePicker3.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.DateTimePicker3.RightToLeftLayout = True
        Me.DateTimePicker3.Size = New System.Drawing.Size(184, 40)
        Me.DateTimePicker3.TabIndex = 381
        '
        'Label3
        '
        Me.Label3.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label3.AutoSize = True
        Me.Label3.BackColor = System.Drawing.Color.Transparent
        Me.Label3.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.ForeColor = System.Drawing.Color.Black
        Me.Label3.Location = New System.Drawing.Point(255, 258)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(63, 30)
        Me.Label3.TabIndex = 380
        Me.Label3.Text = "الوقت"
        '
        'Panel12
        '
        Me.Panel12.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel12.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel12.Location = New System.Drawing.Point(6, 536)
        Me.Panel12.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel12.Name = "Panel12"
        Me.Panel12.Size = New System.Drawing.Size(698, 6)
        Me.Panel12.TabIndex = 379
        '
        'Panel11
        '
        Me.Panel11.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel11.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel11.Location = New System.Drawing.Point(0, 95)
        Me.Panel11.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel11.Name = "Panel11"
        Me.Panel11.Size = New System.Drawing.Size(6, 447)
        Me.Panel11.TabIndex = 378
        '
        'Panel10
        '
        Me.Panel10.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel10.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel10.Location = New System.Drawing.Point(0, 89)
        Me.Panel10.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel10.Name = "Panel10"
        Me.Panel10.Size = New System.Drawing.Size(704, 6)
        Me.Panel10.TabIndex = 377
        '
        'Panel9
        '
        Me.Panel9.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel9.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel9.Location = New System.Drawing.Point(704, 89)
        Me.Panel9.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel9.Name = "Panel9"
        Me.Panel9.Size = New System.Drawing.Size(6, 453)
        Me.Panel9.TabIndex = 376
        '
        'Label1
        '
        Me.Label1.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label1.AutoSize = True
        Me.Label1.BackColor = System.Drawing.Color.Transparent
        Me.Label1.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.Label1.ForeColor = System.Drawing.Color.Black
        Me.Label1.Location = New System.Drawing.Point(537, 212)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(50, 24)
        Me.Label1.TabIndex = 375
        Me.Label1.Text = "التكرار"
        '
        'cmbRemind_Confirmation
        '
        Me.cmbRemind_Confirmation.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.cmbRemind_Confirmation.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.cmbRemind_Confirmation.Font = New System.Drawing.Font("JF Flat", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbRemind_Confirmation.FormattingEnabled = True
        Me.cmbRemind_Confirmation.Items.AddRange(New Object() {"1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20"})
        Me.cmbRemind_Confirmation.Location = New System.Drawing.Point(49, 204)
        Me.cmbRemind_Confirmation.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbRemind_Confirmation.Name = "cmbRemind_Confirmation"
        Me.cmbRemind_Confirmation.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.cmbRemind_Confirmation.Size = New System.Drawing.Size(467, 41)
        Me.cmbRemind_Confirmation.TabIndex = 374
        '
        'Label11
        '
        Me.Label11.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label11.AutoSize = True
        Me.Label11.BackColor = System.Drawing.Color.Transparent
        Me.Label11.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.Label11.ForeColor = System.Drawing.Color.Black
        Me.Label11.Location = New System.Drawing.Point(537, 164)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(88, 24)
        Me.Label11.TabIndex = 206
        Me.Label11.Text = "أسم العميل"
        '
        'Panel1
        '
        Me.Panel1.BackColor = System.Drawing.Color.FromArgb(CType(CType(239, Byte), Integer), CType(CType(53, Byte), Integer), CType(CType(58, Byte), Integer))
        Me.Panel1.Controls.Add(Me.Label2)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 34)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(710, 55)
        Me.Panel1.TabIndex = 204
        '
        'Label2
        '
        Me.Label2.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label2.AutoSize = True
        Me.Label2.BackColor = System.Drawing.Color.Transparent
        Me.Label2.Font = New System.Drawing.Font("JF Flat", 13.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.ForeColor = System.Drawing.Color.White
        Me.Label2.Location = New System.Drawing.Point(285, 6)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(151, 41)
        Me.Label2.TabIndex = 82
        Me.Label2.Text = "إضافة تذكير"
        '
        'Panel7
        '
        Me.Panel7.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel7.Controls.Add(Me.btnClose)
        Me.Panel7.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel7.Location = New System.Drawing.Point(0, 0)
        Me.Panel7.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel7.Name = "Panel7"
        Me.Panel7.Size = New System.Drawing.Size(710, 34)
        Me.Panel7.TabIndex = 203
        '
        'btnClose
        '
        Me.btnClose.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnClose.BackColor = System.Drawing.Color.Transparent
        Me.btnClose.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Delete
        Me.btnClose.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnClose.Location = New System.Drawing.Point(678, 1)
        Me.btnClose.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnClose.Name = "btnClose"
        Me.btnClose.Size = New System.Drawing.Size(29, 31)
        Me.btnClose.TabIndex = 206
        Me.btnClose.UseVisualStyleBackColor = False
        '
        'Panel4
        '
        Me.Panel4.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel4.BackColor = System.Drawing.Color.Black
        Me.Panel4.Location = New System.Drawing.Point(-1, 498)
        Me.Panel4.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel4.Name = "Panel4"
        Me.Panel4.Size = New System.Drawing.Size(722, 1)
        Me.Panel4.TabIndex = 82
        '
        'txtRemind_Number
        '
        Me.txtRemind_Number.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.txtRemind_Number.Enabled = False
        Me.txtRemind_Number.Font = New System.Drawing.Font("JF Flat", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtRemind_Number.Location = New System.Drawing.Point(371, 108)
        Me.txtRemind_Number.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtRemind_Number.Name = "txtRemind_Number"
        Me.txtRemind_Number.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtRemind_Number.Size = New System.Drawing.Size(145, 40)
        Me.txtRemind_Number.TabIndex = 7
        Me.txtRemind_Number.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'btnSave
        '
        Me.btnSave.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSave.BackColor = System.Drawing.Color.Transparent
        Me.btnSave.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Add
        Me.btnSave.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnSave.Location = New System.Drawing.Point(551, 469)
        Me.btnSave.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnSave.Name = "btnSave"
        Me.btnSave.Size = New System.Drawing.Size(134, 59)
        Me.btnSave.TabIndex = 1
        Me.btnSave.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnSave.UseVisualStyleBackColor = False
        '
        'cmbCustomers
        '
        Me.cmbCustomers.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.cmbCustomers.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbCustomers.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbCustomers.Font = New System.Drawing.Font("JF Flat", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbCustomers.FormattingEnabled = True
        Me.cmbCustomers.Location = New System.Drawing.Point(50, 156)
        Me.cmbCustomers.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbCustomers.Name = "cmbCustomers"
        Me.cmbCustomers.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.cmbCustomers.Size = New System.Drawing.Size(467, 41)
        Me.cmbCustomers.TabIndex = 1
        '
        'dtpCapital_Date
        '
        Me.dtpCapital_Date.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.dtpCapital_Date.CustomFormat = "dd/MM/yyyy"
        Me.dtpCapital_Date.Font = New System.Drawing.Font("JF Flat", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.dtpCapital_Date.Format = System.Windows.Forms.DateTimePickerFormat.[Short]
        Me.dtpCapital_Date.Location = New System.Drawing.Point(332, 253)
        Me.dtpCapital_Date.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.dtpCapital_Date.Name = "dtpCapital_Date"
        Me.dtpCapital_Date.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.dtpCapital_Date.RightToLeftLayout = True
        Me.dtpCapital_Date.Size = New System.Drawing.Size(184, 40)
        Me.dtpCapital_Date.TabIndex = 7
        '
        'LblEsal
        '
        Me.LblEsal.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.LblEsal.AutoSize = True
        Me.LblEsal.BackColor = System.Drawing.Color.Transparent
        Me.LblEsal.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.LblEsal.ForeColor = System.Drawing.Color.Black
        Me.LblEsal.Location = New System.Drawing.Point(537, 113)
        Me.LblEsal.Name = "LblEsal"
        Me.LblEsal.Size = New System.Drawing.Size(91, 24)
        Me.LblEsal.TabIndex = 9
        Me.LblEsal.Text = "رقم العملية"
        '
        'Label4
        '
        Me.Label4.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label4.AutoSize = True
        Me.Label4.BackColor = System.Drawing.Color.Transparent
        Me.Label4.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.Label4.ForeColor = System.Drawing.Color.Black
        Me.Label4.Location = New System.Drawing.Point(537, 258)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(50, 24)
        Me.Label4.TabIndex = 6
        Me.Label4.Text = "التاريخ"
        '
        'txtNotes
        '
        Me.txtNotes.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.txtNotes.Font = New System.Drawing.Font("JF Flat", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtNotes.Location = New System.Drawing.Point(50, 301)
        Me.txtNotes.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtNotes.Multiline = True
        Me.txtNotes.Name = "txtNotes"
        Me.txtNotes.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.txtNotes.Size = New System.Drawing.Size(467, 127)
        Me.txtNotes.TabIndex = 8
        Me.txtNotes.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        '
        'Label6
        '
        Me.Label6.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label6.AutoSize = True
        Me.Label6.BackColor = System.Drawing.Color.Transparent
        Me.Label6.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.Label6.ForeColor = System.Drawing.Color.Black
        Me.Label6.Location = New System.Drawing.Point(537, 345)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(44, 24)
        Me.Label6.TabIndex = 9
        Me.Label6.Text = "البيان"
        '
        'rdoRemind_Confirm_Hanging_View
        '
        Me.rdoRemind_Confirm_Hanging_View.AutoSize = True
        Me.rdoRemind_Confirm_Hanging_View.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.rdoRemind_Confirm_Hanging_View.Location = New System.Drawing.Point(20, 64)
        Me.rdoRemind_Confirm_Hanging_View.Name = "rdoRemind_Confirm_Hanging_View"
        Me.rdoRemind_Confirm_Hanging_View.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.rdoRemind_Confirm_Hanging_View.Size = New System.Drawing.Size(69, 28)
        Me.rdoRemind_Confirm_Hanging_View.TabIndex = 2
        Me.rdoRemind_Confirm_Hanging_View.Text = "معلق"
        Me.rdoRemind_Confirm_Hanging_View.UseVisualStyleBackColor = True
        '
        'chkRemind_Confirm_Active_ALL
        '
        Me.chkRemind_Confirm_Active_ALL.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.chkRemind_Confirm_Active_ALL.AutoSize = True
        Me.chkRemind_Confirm_Active_ALL.Checked = True
        Me.chkRemind_Confirm_Active_ALL.CheckState = System.Windows.Forms.CheckState.Checked
        Me.chkRemind_Confirm_Active_ALL.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.chkRemind_Confirm_Active_ALL.Location = New System.Drawing.Point(443, 47)
        Me.chkRemind_Confirm_Active_ALL.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.chkRemind_Confirm_Active_ALL.Name = "chkRemind_Confirm_Active_ALL"
        Me.chkRemind_Confirm_Active_ALL.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkRemind_Confirm_Active_ALL.Size = New System.Drawing.Size(61, 28)
        Me.chkRemind_Confirm_Active_ALL.TabIndex = 419
        Me.chkRemind_Confirm_Active_ALL.Text = "الكل"
        Me.chkRemind_Confirm_Active_ALL.UseVisualStyleBackColor = True
        '
        'FrmRemindAppointment
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.WhiteSmoke
        Me.ClientSize = New System.Drawing.Size(1191, 716)
        Me.Controls.Add(Me.PanelAddNew)
        Me.Controls.Add(Me.Panel_Search)
        Me.Controls.Add(Me.Panel3)
        Me.Controls.Add(Me.Panel6)
        Me.Controls.Add(Me.Panel5)
        Me.Controls.Add(Me.Panel2)
        Me.Controls.Add(Me.bgHeader)
        Me.Controls.Add(Me.Label12)
        Me.Controls.Add(Me.txtsearsh)
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Name = "FrmRemindAppointment"
        Me.RightToLeftLayout = True
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "تذكير بالمواعيد"
        Me.bgHeader.ResumeLayout(False)
        Me.bgHeader.PerformLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel_Search.ResumeLayout(False)
        CType(Me.DataGridView1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel8.ResumeLayout(False)
        Me.Panel8.PerformLayout()
        Me.PanelRemind_Confirm_Active_ALL.ResumeLayout(False)
        Me.PanelRemind_Confirm_Active_ALL.PerformLayout()
        Me.PanelAddNew.ResumeLayout(False)
        Me.PanelAddNew.PerformLayout()
        Me.Panel13.ResumeLayout(False)
        Me.Panel13.PerformLayout()
        CType(Me.PicRemind_Confirm, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        Me.Panel7.ResumeLayout(False)
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents txtsearsh As System.Windows.Forms.TextBox
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Public WithEvents bgHeader As System.Windows.Forms.Panel
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Public WithEvents Label26 As System.Windows.Forms.Label
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents Panel5 As System.Windows.Forms.Panel
    Friend WithEvents Panel6 As System.Windows.Forms.Panel
    Friend WithEvents Panel3 As System.Windows.Forms.Panel
    Friend WithEvents Panel_Search As Panel
    Friend WithEvents DataGridView1 As DataGridView
    Friend WithEvents Panel8 As Panel
    Friend WithEvents cmbCustomersView As ComboBox
    Friend WithEvents Label13 As Label
    Friend WithEvents btnShow As Button
    Friend WithEvents ChkWithoutDate As CheckBox
    Friend WithEvents Label7 As Label
    Friend WithEvents DateTimePicker2 As DateTimePicker
    Friend WithEvents Label5 As Label
    Friend WithEvents DateTimePicker1 As DateTimePicker
    Friend WithEvents chkAll As CheckBox
    Friend WithEvents txtRemind_Number_View As TextBox
    Friend WithEvents Label9 As Label
    Friend WithEvents PanelAddNew As Panel
    Friend WithEvents Panel12 As Panel
    Friend WithEvents Panel11 As Panel
    Friend WithEvents Panel10 As Panel
    Friend WithEvents Panel9 As Panel
    Friend WithEvents Label1 As Label
    Friend WithEvents cmbRemind_Confirmation As ComboBox
    Friend WithEvents Label11 As Label
    Friend WithEvents Panel1 As Panel
    Friend WithEvents Label2 As Label
    Friend WithEvents Panel7 As Panel
    Friend WithEvents btnClose As Button
    Friend WithEvents Panel4 As Panel
    Friend WithEvents txtRemind_Number As TextBox
    Friend WithEvents btnSave As Button
    Friend WithEvents cmbCustomers As ComboBox
    Friend WithEvents dtpCapital_Date As DateTimePicker
    Friend WithEvents LblEsal As Label
    Friend WithEvents Label4 As Label
    Friend WithEvents txtNotes As TextBox
    Friend WithEvents Label6 As Label
    Friend WithEvents DateTimePicker3 As DateTimePicker
    Friend WithEvents Label3 As Label
    Friend WithEvents btnAdd As Button
    Friend WithEvents btnDelete As Button
    Friend WithEvents btnEdit As Button
    Friend WithEvents Label8 As Label
    Friend WithEvents Panel13 As Panel
    Friend WithEvents rdoRemind_Confirm_Inactive As RadioButton
    Friend WithEvents rdoRemind_Confirm_Active As RadioButton
    Friend WithEvents PanelRemind_Confirm_Active_ALL As Panel
    Friend WithEvents rdoRemind_Confirm_Inactive_View As RadioButton
    Friend WithEvents rdoRemind_Confirm_Active_View As RadioButton
    Friend WithEvents Label10 As Label
    Friend WithEvents PicRemind_Confirm As PictureBox
    Friend WithEvents rdoRemind_Confirm_Hanging As RadioButton
    Friend WithEvents rdoRemind_Confirm_Hanging_View As RadioButton
    Friend WithEvents chkRemind_Confirm_Active_ALL As CheckBox
End Class
