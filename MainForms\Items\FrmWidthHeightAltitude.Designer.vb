﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class FrmWidthHeightAltitude
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.PanelHeightWidth = New System.Windows.Forms.Panel()
        Me.btnOK = New System.Windows.Forms.Button()
        Me.Label33 = New System.Windows.Forms.Label()
        Me.txtDensity = New System.Windows.Forms.TextBox()
        Me.Label32 = New System.Windows.Forms.Label()
        Me.txtAltitude = New System.Windows.Forms.TextBox()
        Me.Label19 = New System.Windows.Forms.Label()
        Me.txtWidth = New System.Windows.Forms.TextBox()
        Me.Label30 = New System.Windows.Forms.Label()
        Me.txtHeight = New System.Windows.Forms.TextBox()
        Me.Panel8 = New System.Windows.Forms.Panel()
        Me.Panel9 = New System.Windows.Forms.Panel()
        Me.Panel10 = New System.Windows.Forms.Panel()
        Me.Panel27 = New System.Windows.Forms.Panel()
        Me.Label31 = New System.Windows.Forms.Label()
        Me.chkprintNotInvoiceWidthHeight = New System.Windows.Forms.CheckBox()
        Me.PanelHeightWidth.SuspendLayout()
        Me.Panel27.SuspendLayout()
        Me.SuspendLayout()
        '
        'PanelHeightWidth
        '
        Me.PanelHeightWidth.Controls.Add(Me.chkprintNotInvoiceWidthHeight)
        Me.PanelHeightWidth.Controls.Add(Me.btnOK)
        Me.PanelHeightWidth.Controls.Add(Me.Label33)
        Me.PanelHeightWidth.Controls.Add(Me.txtDensity)
        Me.PanelHeightWidth.Controls.Add(Me.Label32)
        Me.PanelHeightWidth.Controls.Add(Me.txtAltitude)
        Me.PanelHeightWidth.Controls.Add(Me.Label19)
        Me.PanelHeightWidth.Controls.Add(Me.txtWidth)
        Me.PanelHeightWidth.Controls.Add(Me.Label30)
        Me.PanelHeightWidth.Controls.Add(Me.txtHeight)
        Me.PanelHeightWidth.Controls.Add(Me.Panel8)
        Me.PanelHeightWidth.Controls.Add(Me.Panel9)
        Me.PanelHeightWidth.Controls.Add(Me.Panel10)
        Me.PanelHeightWidth.Controls.Add(Me.Panel27)
        Me.PanelHeightWidth.Dock = System.Windows.Forms.DockStyle.Fill
        Me.PanelHeightWidth.Location = New System.Drawing.Point(0, 0)
        Me.PanelHeightWidth.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.PanelHeightWidth.Name = "PanelHeightWidth"
        Me.PanelHeightWidth.Size = New System.Drawing.Size(356, 331)
        Me.PanelHeightWidth.TabIndex = 439
        '
        'btnOK
        '
        Me.btnOK.BackColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(173, Byte), Integer))
        Me.btnOK.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnOK.Font = New System.Drawing.Font("JF Flat", 10.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnOK.ForeColor = System.Drawing.Color.White
        Me.btnOK.ImeMode = System.Windows.Forms.ImeMode.NoControl
        Me.btnOK.Location = New System.Drawing.Point(21, 264)
        Me.btnOK.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Size = New System.Drawing.Size(320, 46)
        Me.btnOK.TabIndex = 440
        Me.btnOK.Text = "تحديد"
        Me.btnOK.UseVisualStyleBackColor = False
        '
        'Label33
        '
        Me.Label33.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label33.AutoSize = True
        Me.Label33.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label33.Location = New System.Drawing.Point(243, 181)
        Me.Label33.Name = "Label33"
        Me.Label33.Size = New System.Drawing.Size(67, 26)
        Me.Label33.TabIndex = 214
        Me.Label33.Text = "الكثافة"
        '
        'txtDensity
        '
        Me.txtDensity.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtDensity.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtDensity.Location = New System.Drawing.Point(91, 176)
        Me.txtDensity.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtDensity.MaxLength = 50
        Me.txtDensity.Name = "txtDensity"
        Me.txtDensity.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtDensity.Size = New System.Drawing.Size(144, 34)
        Me.txtDensity.TabIndex = 213
        Me.txtDensity.Text = "0"
        Me.txtDensity.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label32
        '
        Me.Label32.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label32.AutoSize = True
        Me.Label32.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label32.Location = New System.Drawing.Point(243, 141)
        Me.Label32.Name = "Label32"
        Me.Label32.Size = New System.Drawing.Size(67, 26)
        Me.Label32.TabIndex = 212
        Me.Label32.Text = "الارتفاع"
        '
        'txtAltitude
        '
        Me.txtAltitude.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtAltitude.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtAltitude.Location = New System.Drawing.Point(91, 136)
        Me.txtAltitude.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtAltitude.MaxLength = 50
        Me.txtAltitude.Name = "txtAltitude"
        Me.txtAltitude.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtAltitude.Size = New System.Drawing.Size(144, 34)
        Me.txtAltitude.TabIndex = 211
        Me.txtAltitude.Text = "0"
        Me.txtAltitude.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label19
        '
        Me.Label19.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label19.AutoSize = True
        Me.Label19.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label19.Location = New System.Drawing.Point(243, 101)
        Me.Label19.Name = "Label19"
        Me.Label19.Size = New System.Drawing.Size(56, 26)
        Me.Label19.TabIndex = 210
        Me.Label19.Text = "العرض"
        '
        'txtWidth
        '
        Me.txtWidth.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtWidth.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtWidth.Location = New System.Drawing.Point(91, 97)
        Me.txtWidth.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtWidth.MaxLength = 50
        Me.txtWidth.Name = "txtWidth"
        Me.txtWidth.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtWidth.Size = New System.Drawing.Size(144, 34)
        Me.txtWidth.TabIndex = 209
        Me.txtWidth.Text = "0"
        Me.txtWidth.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label30
        '
        Me.Label30.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label30.AutoSize = True
        Me.Label30.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label30.Location = New System.Drawing.Point(243, 62)
        Me.Label30.Name = "Label30"
        Me.Label30.Size = New System.Drawing.Size(55, 26)
        Me.Label30.TabIndex = 208
        Me.Label30.Text = "الطول"
        '
        'txtHeight
        '
        Me.txtHeight.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtHeight.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtHeight.Location = New System.Drawing.Point(91, 57)
        Me.txtHeight.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtHeight.MaxLength = 50
        Me.txtHeight.Name = "txtHeight"
        Me.txtHeight.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtHeight.Size = New System.Drawing.Size(144, 34)
        Me.txtHeight.TabIndex = 207
        Me.txtHeight.Text = "0"
        Me.txtHeight.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Panel8
        '
        Me.Panel8.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel8.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel8.Location = New System.Drawing.Point(0, 34)
        Me.Panel8.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel8.Name = "Panel8"
        Me.Panel8.Size = New System.Drawing.Size(6, 291)
        Me.Panel8.TabIndex = 205
        '
        'Panel9
        '
        Me.Panel9.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel9.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel9.Location = New System.Drawing.Point(350, 34)
        Me.Panel9.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel9.Name = "Panel9"
        Me.Panel9.Size = New System.Drawing.Size(6, 291)
        Me.Panel9.TabIndex = 204
        '
        'Panel10
        '
        Me.Panel10.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel10.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel10.Location = New System.Drawing.Point(0, 325)
        Me.Panel10.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel10.Name = "Panel10"
        Me.Panel10.Size = New System.Drawing.Size(356, 6)
        Me.Panel10.TabIndex = 203
        '
        'Panel27
        '
        Me.Panel27.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel27.Controls.Add(Me.Label31)
        Me.Panel27.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel27.Location = New System.Drawing.Point(0, 0)
        Me.Panel27.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel27.Name = "Panel27"
        Me.Panel27.Size = New System.Drawing.Size(356, 34)
        Me.Panel27.TabIndex = 202
        '
        'Label31
        '
        Me.Label31.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label31.AutoSize = True
        Me.Label31.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label31.ForeColor = System.Drawing.Color.White
        Me.Label31.Location = New System.Drawing.Point(119, 6)
        Me.Label31.Name = "Label31"
        Me.Label31.Size = New System.Drawing.Size(160, 26)
        Me.Label31.TabIndex = 207
        Me.Label31.Text = "بيانات الطول والعرض"
        '
        'chkprintNotInvoiceWidthHeight
        '
        Me.chkprintNotInvoiceWidthHeight.AutoSize = True
        Me.chkprintNotInvoiceWidthHeight.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.chkprintNotInvoiceWidthHeight.ForeColor = System.Drawing.Color.Black
        Me.chkprintNotInvoiceWidthHeight.Location = New System.Drawing.Point(32, 224)
        Me.chkprintNotInvoiceWidthHeight.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.chkprintNotInvoiceWidthHeight.Name = "chkprintNotInvoiceWidthHeight"
        Me.chkprintNotInvoiceWidthHeight.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkprintNotInvoiceWidthHeight.Size = New System.Drawing.Size(299, 30)
        Me.chkprintNotInvoiceWidthHeight.TabIndex = 441
        Me.chkprintNotInvoiceWidthHeight.Text = "عرض بدون فاتورة عرض وطول وارتفاع"
        Me.chkprintNotInvoiceWidthHeight.UseVisualStyleBackColor = True
        '
        'FrmWidthHeightAltitude
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(356, 331)
        Me.Controls.Add(Me.PanelHeightWidth)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow
        Me.Name = "FrmWidthHeightAltitude"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.PanelHeightWidth.ResumeLayout(False)
        Me.PanelHeightWidth.PerformLayout()
        Me.Panel27.ResumeLayout(False)
        Me.Panel27.PerformLayout()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents PanelHeightWidth As Panel
    Friend WithEvents Label33 As Label
    Friend WithEvents txtDensity As TextBox
    Friend WithEvents Label32 As Label
    Friend WithEvents txtAltitude As TextBox
    Friend WithEvents Label19 As Label
    Friend WithEvents txtWidth As TextBox
    Friend WithEvents Label30 As Label
    Friend WithEvents txtHeight As TextBox
    Friend WithEvents Panel8 As Panel
    Friend WithEvents Panel9 As Panel
    Friend WithEvents Panel10 As Panel
    Friend WithEvents Panel27 As Panel
    Friend WithEvents Label31 As Label
    Friend WithEvents btnOK As Button
    Friend WithEvents chkprintNotInvoiceWidthHeight As CheckBox
End Class
