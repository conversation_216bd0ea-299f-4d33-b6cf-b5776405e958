﻿Public Class Form4
    Private Sub btnSearchItems_Click(sender As Object, e As EventArgs) Handles btnSearchItems.Click
        'DTGVUC.Visible = True
        DTGVUC.Size = New System.Drawing.Size(773, 421)
    End Sub

    Private Sub txtUserControl_TextChanged(sender As Object, e As EventArgs) Handles txtUserControl.TextChanged
        If txtUserControl.Text <> "" Then
            'DTGVUC.Visible = True
            connect()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Dim X As String = "%" & txtUserControl.Text.Trim & "%"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "Select sname as [الاسم],SalPrice as [السعر] from items where sname like N'" & X & "'"
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGVUC.DataSource = Cls.PopulateDataView(dr)
            DTGVUC.Size = New System.Drawing.Size(773, 421)

        End If
    End Sub

    Private Sub txtUserControl_KeyDown(sender As Object, e As KeyEventArgs) Handles txtUserControl.KeyDown
        If ((e.KeyCode = 8)) Then
            'DTGVUC.Visible = False
            DTGVUC.Size = New System.Drawing.Size(773, 11)
            'Me.ClientSize = New System.Drawing.Size(773, 40)
        End If
        If ((e.KeyCode = 40)) Then
            DTGVUC.Focus()
        End If
    End Sub

    Private Sub btnVisbileItems_Click(sender As Object, e As EventArgs) Handles btnVisbileItems.Click
        'DTGVUC.Visible = False
        DTGVUC.Size = New System.Drawing.Size(773, 11)
    End Sub

    Private Sub Form4_MouseClick(sender As Object, e As MouseEventArgs) Handles MyBase.MouseClick
        'DTGVUC.Visible = False
    End Sub

    Private Sub Form4_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub

End Class