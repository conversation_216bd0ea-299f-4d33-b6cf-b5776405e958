﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FrmFilter
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FrmFilter))
        Me.bgHeader = New System.Windows.Forms.Panel()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.Label26 = New System.Windows.Forms.Label()
        Me.Panel6 = New System.Windows.Forms.Panel()
        Me.Panel8 = New System.Windows.Forms.Panel()
        Me.Panel5 = New System.Windows.Forms.Panel()
        Me.Panel25 = New System.Windows.Forms.Panel()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.rdoNameFilter = New System.Windows.Forms.RadioButton()
        Me.rdoNumberFilter = New System.Windows.Forms.RadioButton()
        Me.rdoDateFilter = New System.Windows.Forms.RadioButton()
        Me.btnFilter = New System.Windows.Forms.Button()
        Me.bgHeader.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'bgHeader
        '
        Me.bgHeader.BackColor = System.Drawing.SystemColors.HotTrack
        Me.bgHeader.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.bgHeader.Controls.Add(Me.PictureBox1)
        Me.bgHeader.Controls.Add(Me.Label26)
        Me.bgHeader.Cursor = System.Windows.Forms.Cursors.Default
        Me.bgHeader.Dock = System.Windows.Forms.DockStyle.Top
        Me.bgHeader.Font = New System.Drawing.Font("Arial", 8.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bgHeader.ForeColor = System.Drawing.SystemColors.WindowText
        Me.bgHeader.Location = New System.Drawing.Point(0, 0)
        Me.bgHeader.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.bgHeader.Name = "bgHeader"
        Me.bgHeader.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.bgHeader.Size = New System.Drawing.Size(306, 59)
        Me.bgHeader.TabIndex = 206
        Me.bgHeader.TabStop = True
        '
        'PictureBox1
        '
        Me.PictureBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureBox1.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Filter
        Me.PictureBox1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.PictureBox1.Location = New System.Drawing.Point(252, 17)
        Me.PictureBox1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(49, 31)
        Me.PictureBox1.TabIndex = 11
        Me.PictureBox1.TabStop = False
        '
        'Label26
        '
        Me.Label26.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label26.AutoSize = True
        Me.Label26.BackColor = System.Drawing.Color.Transparent
        Me.Label26.Cursor = System.Windows.Forms.Cursors.Default
        Me.Label26.Font = New System.Drawing.Font("JF Flat", 13.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label26.ForeColor = System.Drawing.Color.White
        Me.Label26.Location = New System.Drawing.Point(67, 8)
        Me.Label26.Name = "Label26"
        Me.Label26.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.Label26.Size = New System.Drawing.Size(152, 41)
        Me.Label26.TabIndex = 10
        Me.Label26.Text = "تحديد البحث"
        '
        'Panel6
        '
        Me.Panel6.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel6.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel6.Location = New System.Drawing.Point(300, 59)
        Me.Panel6.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel6.Name = "Panel6"
        Me.Panel6.Size = New System.Drawing.Size(6, 391)
        Me.Panel6.TabIndex = 207
        '
        'Panel8
        '
        Me.Panel8.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel8.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel8.Location = New System.Drawing.Point(0, 65)
        Me.Panel8.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel8.Name = "Panel8"
        Me.Panel8.Size = New System.Drawing.Size(6, 385)
        Me.Panel8.TabIndex = 209
        '
        'Panel5
        '
        Me.Panel5.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel5.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel5.Location = New System.Drawing.Point(0, 59)
        Me.Panel5.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel5.Name = "Panel5"
        Me.Panel5.Size = New System.Drawing.Size(300, 6)
        Me.Panel5.TabIndex = 208
        '
        'Panel25
        '
        Me.Panel25.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel25.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel25.Location = New System.Drawing.Point(6, 444)
        Me.Panel25.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel25.Name = "Panel25"
        Me.Panel25.Size = New System.Drawing.Size(294, 6)
        Me.Panel25.TabIndex = 379
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel1.Controls.Add(Me.rdoNameFilter)
        Me.Panel1.Controls.Add(Me.rdoNumberFilter)
        Me.Panel1.Controls.Add(Me.rdoDateFilter)
        Me.Panel1.Location = New System.Drawing.Point(38, 92)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(232, 204)
        Me.Panel1.TabIndex = 409
        '
        'rdoNameFilter
        '
        Me.rdoNameFilter.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.rdoNameFilter.AutoSize = True
        Me.rdoNameFilter.BackColor = System.Drawing.Color.Transparent
        Me.rdoNameFilter.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdoNameFilter.ForeColor = System.Drawing.Color.Black
        Me.rdoNameFilter.Location = New System.Drawing.Point(79, 135)
        Me.rdoNameFilter.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.rdoNameFilter.Name = "rdoNameFilter"
        Me.rdoNameFilter.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.rdoNameFilter.Size = New System.Drawing.Size(74, 30)
        Me.rdoNameFilter.TabIndex = 366
        Me.rdoNameFilter.Text = "الاسم"
        Me.rdoNameFilter.UseVisualStyleBackColor = False
        '
        'rdoNumberFilter
        '
        Me.rdoNumberFilter.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.rdoNumberFilter.AutoSize = True
        Me.rdoNumberFilter.BackColor = System.Drawing.Color.Transparent
        Me.rdoNumberFilter.Checked = True
        Me.rdoNumberFilter.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdoNumberFilter.ForeColor = System.Drawing.Color.Black
        Me.rdoNumberFilter.Location = New System.Drawing.Point(92, 64)
        Me.rdoNumberFilter.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.rdoNumberFilter.Name = "rdoNumberFilter"
        Me.rdoNumberFilter.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.rdoNumberFilter.Size = New System.Drawing.Size(61, 30)
        Me.rdoNumberFilter.TabIndex = 364
        Me.rdoNumberFilter.Text = "رقم"
        Me.rdoNumberFilter.UseVisualStyleBackColor = False
        '
        'rdoDateFilter
        '
        Me.rdoDateFilter.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.rdoDateFilter.AutoSize = True
        Me.rdoDateFilter.BackColor = System.Drawing.Color.Transparent
        Me.rdoDateFilter.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.rdoDateFilter.ForeColor = System.Drawing.Color.Black
        Me.rdoDateFilter.Location = New System.Drawing.Point(75, 99)
        Me.rdoDateFilter.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.rdoDateFilter.Name = "rdoDateFilter"
        Me.rdoDateFilter.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.rdoDateFilter.Size = New System.Drawing.Size(78, 30)
        Me.rdoDateFilter.TabIndex = 365
        Me.rdoDateFilter.Text = "التاريخ"
        Me.rdoDateFilter.UseVisualStyleBackColor = False
        '
        'btnFilter
        '
        Me.btnFilter.BackColor = System.Drawing.Color.Transparent
        Me.btnFilter.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Filter
        Me.btnFilter.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnFilter.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnFilter.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.btnFilter.Location = New System.Drawing.Point(38, 332)
        Me.btnFilter.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnFilter.Name = "btnFilter"
        Me.btnFilter.Size = New System.Drawing.Size(232, 73)
        Me.btnFilter.TabIndex = 410
        Me.btnFilter.Text = "تحديد"
        Me.btnFilter.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnFilter.UseVisualStyleBackColor = False
        '
        'FrmFilter
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(306, 450)
        Me.Controls.Add(Me.btnFilter)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.Panel25)
        Me.Controls.Add(Me.Panel8)
        Me.Controls.Add(Me.Panel5)
        Me.Controls.Add(Me.Panel6)
        Me.Controls.Add(Me.bgHeader)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "FrmFilter"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "تحديد البحث"
        Me.bgHeader.ResumeLayout(False)
        Me.bgHeader.PerformLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Public WithEvents bgHeader As Panel
    Friend WithEvents PictureBox1 As PictureBox
    Public WithEvents Label26 As Label
    Friend WithEvents Panel6 As Panel
    Friend WithEvents Panel8 As Panel
    Friend WithEvents Panel5 As Panel
    Friend WithEvents Panel25 As Panel
    Friend WithEvents Panel1 As Panel
    Friend WithEvents rdoNumberFilter As RadioButton
    Friend WithEvents rdoDateFilter As RadioButton
    Friend WithEvents btnFilter As Button
    Friend WithEvents rdoNameFilter As RadioButton
End Class
