﻿Public Class FrmOnlineStoreProduct
    Dim Product_ID As String
    Dim ActivEdit As Boolean = False

    Private Sub Frm_Group_Branch_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Cos.Fill_ComboBox_OnlineStore("[Product] Where (IsDelete = 0)", "Name", cmbNameView)
        Cos.Fill_ComboBox_OnlineStore("[Category] Where (IsDelete = 0)", "Name", cmbCategoryView)
        Cos.Fill_ComboBox_OnlineStore("[Company] Where (IsDelete = 0)", "Name", cmbCompanyView)
        Cos.Fill_ComboBox_OnlineStore("[Category] Where (IsDelete = 0)", "Name", cmbCategory)
        Cos.Fill_ComboBox_OnlineStore("[Company] Where (IsDelete = 0)", "Name", cmbCompany)

        Headerx()
        PanelAddNew.Dock = DockStyle.None
        PanelAddNew.Top = 5000
    End Sub

    Private Sub btnsave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnsave.Click
        If ValidateSave() = False Then Exit Sub

        Dim CategoryId As String = Cos.GetCodeValueOnlineStore("Category", "Id", "Name", cmbCategory.Text)
        Dim CompanyId As String = Cos.GetCodeValueOnlineStore("Company", "Id", "Name", cmbCompany.Text)

        If Not ConnectingOnlineStore() Is Nothing Then
            If ActivEdit = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Product set Name =N'" & txtName.Text & "',Code =N'" & txtCode.Text & "',Tag =N'" & txtTag.Text & "',Description =N'" & txtDescription.Text & "',CategoryId =N'" & CategoryId & "',CompanyId =N'" & CompanyId & "',Price =N'" & txtPrice1.Text & "',DiscountedPrice =N'" & txtDiscountedPrice1.Text & "',Price2 =N'" & txtPrice2.Text & "',DiscountedPrice2 =N'" & txtDiscountedPrice2.Text & "',Price3 =N'" & txtPrice3.Text & "',DiscountedPrice3 =N'" & txtDiscountedPrice3.Text & "',Stock =N'" & txtStock.Text & "',LimitQuantity =N'" & txtLimitQuantity.Text & "' where Id= '" & txtID.Text & "'"
                cmd.ExecuteNonQuery()
                Cn.Close()
                connect()
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Product (Name, Code, Tag, Description, CategoryId, CompanyId, Price, DiscountedPrice, Price2, DiscountedPrice2, Price3, DiscountedPrice3, Stock, LimitQuantity, IsDelete)"
                S = S & " values (N'" & txtName.Text.Trim & "',N'" & txtCode.Text & "',N'" & txtTag.Text & "',N'" & txtDescription.Text & "',N'" & CategoryId & "',N'" & CompanyId & "',N'" & txtPrice1.Text & "',N'" & txtDiscountedPrice1.Text & "',N'" & txtPrice2.Text & "',N'" & txtDiscountedPrice2.Text & "',N'" & txtPrice3.Text & "',N'" & txtDiscountedPrice3.Text & "',N'" & txtStock.Text & "',N'" & txtLimitQuantity.Text & "',N'0')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
                Cn.Close()
                connect()
            End If
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If

        cmbCompany.Text = ""
        cmbCategory.Text = ""
        ActivEdit = False
        btnsave.Text = "إضافة"
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.save_32

        PanelAddNew.Dock = DockStyle.None
        PanelAddNew.Top = 5000
        Headerx()

    End Sub

    Private Sub MAXRECORD()
        If Not ConnectingOnlineStore() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from Product"
            dr = cmd.ExecuteReader

            Dim dt As New DataTable
            dt.Load(dr)

            If dt.Rows.Count = 0 Then
                txtID.Text = 1
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(CAST(Id As float)) as mb FROM Product where Id <> N''"
                dr = cmd.ExecuteReader
                dr.Read()

                Dim sh As Long
                sh = dr("mb")
                txtID.Text = sh + 1
            End If
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If

    End Sub

    Function ValidateSave() As Boolean

        If cmbCategory.Text = "" Then MsgBox("فضلا أدخل الفئة", MsgBoxStyle.Exclamation) : cmbCategory.Focus() : Return False
        If cmbCompany.Text = "" Then MsgBox("فضلا أدخل الشركة", MsgBoxStyle.Exclamation) : cmbCompany.Focus() : Return False
        If txtName.Text = "" Then MsgBox("فضلا أدخل أسم المنتج", MsgBoxStyle.Exclamation) : txtName.Focus() : Return False
        If txtCode.Text = "" Then MsgBox("فضلا أدخل كود المنتج", MsgBoxStyle.Exclamation) : txtCode.Focus() : Return False

        If Not ConnectingOnlineStore() Is Nothing Then
            If ActivEdit = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select count(*) from Product where Name =N'" & txtName.Text.Trim & "'" : H = cmd.ExecuteScalar
                If H > 0 Then
                    MsgBox(" اسم المنتج مسجلة مسبقاً بنفس الاسم", MsgBoxStyle.Exclamation) : txtName.Focus() : Return False
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select count(*) from Product where Code =N'" & txtCode.Text.Trim & "'" : H = cmd.ExecuteScalar
                If H > 0 Then
                    MsgBox(" كود المنتج مسجلة مسبقاً بنفس الاسم", MsgBoxStyle.Exclamation) : txtCode.Focus() : Return False
                End If
            End If
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If

        Return True
    End Function

    Private Sub Headerx()
        If Not ConnectingOnlineStore() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "Select dbo.Product.Id As [ID], dbo.Product.Name As [المنتج], dbo.Product.Code As [كود], dbo.Product.Tag As [علامة], dbo.Product.Description As [وصف], dbo.Category.Name As [فئات], dbo.Company.Name As [شركات],   dbo.Product.Price AS [سعر 1], dbo.Product.DiscountedPrice As [سعر الخصم 1], dbo.Product.Price2 As [سعر 2], dbo.Product.DiscountedPrice2 As [سعر الخصم 2], dbo.Product.Price3 As [سعر 3],   dbo.Product.DiscountedPrice3 AS [سعر الخصم 3], dbo.Product.Stock As [المخزون], dbo.Product.LimitQuantity As [حد اقصى للحسب], dbo.Product.IsDelete  From dbo.Product INNER Join  dbo.Category ON dbo.Product.CategoryId = dbo.Category.Id INNER Join  dbo.Company ON dbo.Product.CompanyId = dbo.Company.Id Where (dbo.Product.Id <> '')"
            If chkAll.Checked = False Then
                If txtCodeView.Text <> "" Then
                    S = S & " And dbo.Product.Code =N'" & txtCodeView.Text.Trim & "'"
                End If
                If cmbCategoryView.Text <> "" Then
                    S = S & " And dbo.Category.Name =N'" & cmbCategoryView.Text.Trim & "'"
                End If
                If cmbCompanyView.Text <> "" Then
                    S = S & " And dbo.Company.Name = N'" & cmbCompanyView.Text.Trim & "'"
                End If
                If cmbNameView.Text <> "" Then
                    S = S & " And dbo.Product.Name = N'" & cmbNameView.Text.Trim & "'"
                End If
            End If
            If chkIsDelete.Checked = True Then
                S = S & " and dbo.Product.IsDelete =N'True'"
            Else
                S = S & " and dbo.Product.IsDelete =N'False'"
            End If
            S = S & " order by dbo.Product.Id"
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)
            DTGV.Columns(0).Visible = False
            DTGV.Columns(15).Visible = False
            txtNumber.Text = DTGV.RowCount
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Sub

    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If Not ConnectingOnlineStore() Is Nothing Then
            If DTGV.Rows.Count = 0 Then
                MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
                Exit Sub
            End If
            Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
            If x = vbCancel Then Exit Sub
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To DTGV.SelectedRows.Count - 1
                If DTGV.RowCount = 0 Then Beep() : Exit Sub
                If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
                Dim ItmID As String
                ItmID = DTGV.SelectedRows(i).Cells(0).Value

                cmd.CommandText = "update Product set IsDelete =N'1' where Id= '" & ItmID & "'"
                cmd.ExecuteNonQuery()
            Next
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If

        Headerx()
        ActivEdit = False
        btnsave.Text = "إضافة"
    End Sub

    Private Sub cmbcats_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCategory.KeyUp
        If e.KeyCode = 13 Then
            cmbCompany.Focus()
        End If
    End Sub

    Private Sub cmbbranch_Name_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCompany.KeyUp
        If e.KeyCode = 13 Then
            txtPrice1.Focus()
        End If
    End Sub

    Private Sub chkIsDelete_CheckedChanged(sender As Object, e As EventArgs) Handles chkIsDelete.CheckedChanged
        Headerx()
    End Sub

    Private Sub DTGV_DoubleClick(sender As Object, e As EventArgs) Handles DTGV.DoubleClick
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        ActivEdit = True
        btnsave.Text = "تعديل"

        txtID.Text = DTGV.SelectedRows(0).Cells(0).Value
        txtName.Text = DTGV.SelectedRows(0).Cells(1).Value
        txtCode.Text = DTGV.SelectedRows(0).Cells(2).Value
        txtTag.Text = DTGV.SelectedRows(0).Cells(3).Value
        txtDescription.Text = DTGV.SelectedRows(0).Cells(4).Value
        cmbCategory.Text = DTGV.SelectedRows(0).Cells(5).Value
        cmbCompany.Text = DTGV.SelectedRows(0).Cells(6).Value
        txtPrice1.Text = DTGV.SelectedRows(0).Cells(7).Value
        txtDiscountedPrice1.Text = DTGV.SelectedRows(0).Cells(8).Value
        txtPrice2.Text = DTGV.SelectedRows(0).Cells(9).Value
        txtDiscountedPrice2.Text = DTGV.SelectedRows(0).Cells(10).Value
        txtPrice3.Text = DTGV.SelectedRows(0).Cells(11).Value
        txtDiscountedPrice3.Text = DTGV.SelectedRows(0).Cells(12).Value
        txtStock.Text = DTGV.SelectedRows(0).Cells(13).Value
        txtLimitQuantity.Text = DTGV.SelectedRows(0).Cells(14).Value

        ActivEdit = True
        PanelAddNew.Top = 20
        PanelAddNew.Dock = DockStyle.Fill
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.Edit_1

    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbCategoryView.Enabled = False
            cmbCompanyView.Enabled = False
            cmbNameView.Enabled = False
        Else
            cmbCategoryView.Enabled = True
            cmbCompanyView.Enabled = True
            cmbNameView.Enabled = True
        End If
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        PanelAddNew.Dock = DockStyle.None
        PanelAddNew.Top = 5000
        ActivEdit = False
        btnsave.Text = "إضافة"
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.save_32
    End Sub

    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        PanelAddNew.Top = 20
        PanelAddNew.Dock = DockStyle.Fill
        cmbCategory.Focus()
        ActivEdit = False
        btnsave.Text = "إضافة"
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.save_32
        cmbCompany.Text = ""
        cmbCategory.Text = ""
        MAXRECORD()
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs) Handles btnEdit.Click
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        ActivEdit = True
        btnsave.Text = "تعديل"
        txtID.Text = DTGV.SelectedRows(0).Cells(0).Value
        txtName.Text = DTGV.SelectedRows(0).Cells(1).Value
        txtCode.Text = DTGV.SelectedRows(0).Cells(2).Value
        txtTag.Text = DTGV.SelectedRows(0).Cells(3).Value
        txtDescription.Text = DTGV.SelectedRows(0).Cells(4).Value
        cmbCategory.Text = DTGV.SelectedRows(0).Cells(5).Value
        cmbCompany.Text = DTGV.SelectedRows(0).Cells(6).Value
        txtPrice1.Text = DTGV.SelectedRows(0).Cells(7).Value
        txtDiscountedPrice1.Text = DTGV.SelectedRows(0).Cells(8).Value
        txtPrice2.Text = DTGV.SelectedRows(0).Cells(9).Value
        txtDiscountedPrice2.Text = DTGV.SelectedRows(0).Cells(10).Value
        txtPrice3.Text = DTGV.SelectedRows(0).Cells(11).Value
        txtDiscountedPrice3.Text = DTGV.SelectedRows(0).Cells(12).Value
        txtStock.Text = DTGV.SelectedRows(0).Cells(13).Value
        txtLimitQuantity.Text = DTGV.SelectedRows(0).Cells(14).Value

        ActivEdit = True
        PanelAddNew.Top = 20
        PanelAddNew.Dock = DockStyle.Fill
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.Edit_1
    End Sub

    Private Sub btnShow_Click(sender As Object, e As EventArgs) Handles btnShow.Click
        Headerx()
    End Sub

    Private Sub txtOrderLimit_KeyUp(sender As Object, e As KeyEventArgs) Handles txtPrice1.KeyUp
        If e.KeyCode = 13 Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub txtPrice1_TextChanged(sender As Object, e As EventArgs) Handles txtPrice1.TextChanged
        MyVars.CheckNumber(txtPrice1)
    End Sub

    Private Sub txtDiscountedPrice1_TextChanged(sender As Object, e As EventArgs) Handles txtDiscountedPrice1.TextChanged
        MyVars.CheckNumber(txtDiscountedPrice1)
    End Sub

    Private Sub txtPrice2_TextChanged(sender As Object, e As EventArgs) Handles txtPrice2.TextChanged
        MyVars.CheckNumber(txtPrice2)
    End Sub

    Private Sub txtDiscountedPrice2_TextChanged(sender As Object, e As EventArgs) Handles txtDiscountedPrice2.TextChanged
        MyVars.CheckNumber(txtDiscountedPrice2)
    End Sub

    Private Sub txtPrice3_TextChanged(sender As Object, e As EventArgs) Handles txtPrice3.TextChanged
        MyVars.CheckNumber(txtPrice3)
    End Sub

    Private Sub txtDiscountedPrice3_TextChanged(sender As Object, e As EventArgs) Handles txtDiscountedPrice3.TextChanged
        MyVars.CheckNumber(txtDiscountedPrice3)
    End Sub

    Private Sub txtStock_TextChanged(sender As Object, e As EventArgs) Handles txtStock.TextChanged
        MyVars.CheckNumber(txtStock)
    End Sub

    Private Sub txtLimitQuantity_TextChanged(sender As Object, e As EventArgs) Handles txtLimitQuantity.TextChanged
        MyVars.CheckNumber(txtLimitQuantity)
    End Sub

End Class