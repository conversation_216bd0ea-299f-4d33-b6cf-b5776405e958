﻿Imports System.IO.Ports

Module ScreenNCR

    Public Sub ViewScreenNCR(ByVal Total As String)
        Try
            NameEnCompany = Cls.Get_Code_Value_More("Company", "CMPNameEnglish", "CMPSerial =N'1'")

            Dim sp As SerialPort = New SerialPort()

            sp.PortName = ScreenNCRHomePortName 'COM5
            sp.BaudRate = ScreenNCRHomeBaudRate '9600
            sp.Parity = Parity.None
            sp.DataBits = 8
            sp.StopBits = StopBits.One
            sp.Open()
            sp.Write(Convert.ToString(ChrW(12)))
            sp.WriteLine(NameEnCompany)
            sp.WriteLine("Total: " & Total & "")
            sp.Close()
            sp.Dispose()
            sp = Nothing

        Catch ex As Exception
        End Try
    End Sub

    Public Sub ViewScreenNCRHome()
        Try
            NameEnCompany = Cls.Get_Code_Value_More("Company", "CMPNameEnglish", "CMPSerial =N'1'")
            CmpFax = Cls.Get_Code_Value_More("Company", "CMPFax", "CMPSerial =N'1'")

            Dim sp As SerialPort = New SerialPort()
            sp.PortName = ScreenNCRHomePortName 'COM5
            sp.BaudRate = ScreenNCRHomeBaudRate '9600
            sp.Parity = Parity.None
            sp.DataBits = 8
            sp.StopBits = StopBits.One
            sp.Open()
            sp.Write(Convert.ToString(ChrW(12)))
            sp.WriteLine(NameEnCompany)
            sp.WriteLine(CmpFax)
            sp.Close()
            sp.Dispose()
            sp = Nothing
        Catch ex As Exception
        End Try

    End Sub

End Module
