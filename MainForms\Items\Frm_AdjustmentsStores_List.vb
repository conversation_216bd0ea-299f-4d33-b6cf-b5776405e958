﻿Public Class Frm_AdjustmentsStores_List
    Dim RNXD As Integer
    Dim Actions As Boolean = False
    Private Sub Frm_AdjustmentsStores_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Bra.Fil("groups", "g_name", cmbcats)
        Bra.Fil("groups", "g_name", cmbCatsView)
        Cls.fill_combo_Branch("stores", "store", cmbStores)
        Cls.fill_combo_Branch("stores", "store", cmbStoresView)
        cmbStores.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        PanelViewAdjustmentsStores.Top = 5000
        txtsearsh_TextChanged(sender, e)
        GetDateNotBeenActivatedPrograms(dtpDateItem)
    End Sub

    Private Sub cmbcats_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbcats.SelectedIndexChanged
        If txtprc.Text = "" Then
            If Actions = False Then
                If cmbcats.Text.Trim = "" Then Exit Sub
                Cls.fill_combo_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbname)
                cmbname.Text = ""
                cmbname.Focus()
            End If
        End If
    End Sub

    Private Sub cmbname_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbname.SelectedIndexChanged
        If txtprc.Text = "" Then
            If Actions = False Then
                txtprc.Text = Cls.Get_Code_Value_Stores_More("items", "itm_id", "sname=N'" & cmbname.Text.Trim & "' and Stores =N'" & cmbStores.Text.Trim & "'")
                txtStore.Text = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)
                txtqunt.Text = "0"
                txtqunt.SelectAll()
                txtqunt.Focus()
            End If
        End If

    End Sub

    Private Sub Clear_All()
        cmbcats.Text = ""
        cmbname.Text = ""
        txtqunt.Text = ""
        txtprc.Text = ""
        txtStore.Text = ""
    End Sub

    Private Sub btnSaveAll_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        If ValidateSave() = False Then Exit Sub
        Dim xid As String = ""
        If rdoEqual.Checked = True Then
            Dim TotalEqual As String
            If Convert.ToDouble(txtqunt.Text) >= Convert.ToDouble(txtStore.Text) Then
                TotalEqual = txtqunt.Text - txtStore.Text
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into BilltINData(Company_Branch_ID,bill_no,itm_id,itm_cat,itm_name,qu,qu_unity,Stores,username,bill_date)  values("
                S = S & "N'" & Company_Branch_ID & "',N'تسوية',N'" & txtprc.Text & "',N'" & cmbcats.Text & "',N'" & cmbname.Text & "',N'" & TotalEqual & "',N'" & TotalEqual & "',N'" & cmbStores.Text & "',N'" & UserName & "',N'" & Cls.C_date(dtpDateItem.Text) & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                IM.Store(txtprc.Text, cmbStores.Text)

                If ConnectOnlineStore = "YES" Then
                    Dim sname As String = Cls.Get_Code_Value("Items", "sname", "itm_id", txtprc.Text)
                    StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", txtprc.Text)
                    Cos.UpdateProductStock(StockOnline, txtprc.Text, sname)
                End If

                xid = Cls.Get_Code_Value_More_ALL_Select("Select Top(100) PERCENT id, bill_no, itm_id, Stores  From dbo.BilltINData  Where (itm_id = N'" & txtprc.Text & "') AND (bill_no = N'تسوية') AND (Stores = N'" & cmbStores.Text & "')  Order By id DESC")

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where id =N'" & xid & "' and itm_id = N'" & txtprc.Text & "' and Stores =N'" & cmbStores.Text & "'" : cmd.ExecuteNonQuery()
            End If
            If Convert.ToDouble(txtqunt.Text) < Convert.ToDouble(txtStore.Text) Then
                TotalEqual = txtStore.Text - txtqunt.Text
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into BillsalData(Company_Branch_ID,bill_no,itm_id,itm_cat,itm_name,qu,qu_unity,Stores,username,bill_date,Vendorname,Stat)  values("
                S = S & "N'" & Company_Branch_ID & "',N'تسوية',N'" & txtprc.Text & "',N'" & cmbcats.Text & "',N'" & cmbname.Text & "',N'" & TotalEqual & "',N'" & TotalEqual & "',N'" & cmbStores.Text & "',N'" & UserName & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'نقداً',N'نقدا')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                IM.Store(txtprc.Text, cmbStores.Text)

                If ConnectOnlineStore = "YES" Then
                    Dim sname As String = Cls.Get_Code_Value("Items", "sname", "itm_id", txtprc.Text)
                    StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", txtprc.Text)
                    Cos.UpdateProductStock(StockOnline, txtprc.Text, sname)
                End If

                xid = Cls.Get_Code_Value_More_ALL_Select("Select Top(100) PERCENT id, bill_no, itm_id, Stores  From dbo.BillsalData  Where (itm_id = N'" & txtprc.Text & "') AND (bill_no = N'تسوية') AND (Stores = N'" & cmbStores.Text & "')  Order By id DESC")

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BillsalData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where id =N'" & xid & "' and itm_id = N'" & txtprc.Text & "' and Stores =N'" & cmbStores.Text & "'" : cmd.ExecuteNonQuery()

            End If
        Else
            If rdoIncrease.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into BilltINData(Company_Branch_ID,bill_no,itm_id,itm_cat,itm_name,qu,qu_unity,Stores,username,bill_date)  values("
                S = S & "N'" & Company_Branch_ID & "',N'تسوية',N'" & txtprc.Text & "',N'" & cmbcats.Text & "',N'" & cmbname.Text & "',N'" & txtqunt.Text & "',N'" & txtqunt.Text & "',N'" & cmbStores.Text & "',N'" & UserName & "',N'" & Cls.C_date(dtpDateItem.Text) & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                IM.Store(txtprc.Text, cmbStores.Text)

                If ConnectOnlineStore = "YES" Then
                    Dim sname As String = Cls.Get_Code_Value("Items", "sname", "itm_id", txtprc.Text)
                    StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", txtprc.Text)
                    Cos.UpdateProductStock(StockOnline, txtprc.Text, sname)
                End If

                xid = Cls.Get_Code_Value_More_ALL_Select("Select Top(100) PERCENT id, bill_no, itm_id, Stores  From dbo.BilltINData  Where (itm_id = N'" & txtprc.Text & "') AND (bill_no = N'تسوية') AND (Stores = N'" & cmbStores.Text & "')  Order By id DESC")

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where id =N'" & xid & "' and itm_id = N'" & txtprc.Text & "' and Stores =N'" & cmbStores.Text & "'" : cmd.ExecuteNonQuery()

            End If

            If rdoDeposit.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into BillsalData(Company_Branch_ID,bill_no,itm_id,itm_cat,itm_name,qu,qu_unity,Stores,username,bill_date,Vendorname,Stat)  values("
                S = S & "N'" & Company_Branch_ID & "',N'تسوية',N'" & txtprc.Text & "',N'" & cmbcats.Text & "',N'" & cmbname.Text & "',N'" & txtqunt.Text & "',N'" & txtqunt.Text & "',N'" & cmbStores.Text & "',N'" & UserName & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'نقداً',N'نقدا')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                IM.Store(txtprc.Text, cmbStores.Text)

                If ConnectOnlineStore = "YES" Then
                    Dim sname As String = Cls.Get_Code_Value("Items", "sname", "itm_id", txtprc.Text)
                    StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", txtprc.Text)
                    Cos.UpdateProductStock(StockOnline, txtprc.Text, sname)
                End If

                xid = Cls.Get_Code_Value_More_ALL_Select("Select Top(100) PERCENT id, bill_no, itm_id, Stores  From dbo.BillsalData  Where (itm_id = N'" & txtprc.Text & "') AND (bill_no = N'تسوية') AND (Stores = N'" & cmbStores.Text & "')  Order By id DESC")

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BillsalData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where id =N'" & xid & "' and itm_id = N'" & txtprc.Text & "' and Stores =N'" & cmbStores.Text & "'" : cmd.ExecuteNonQuery()

            End If
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Items set InventoryDone = " & Val(1) & " where itm_id = N'" & txtprc.Text & "' and Stores =N'" & cmbStores.Text & "'" : cmd.ExecuteNonQuery()

        txtsearsh_TextChanged(sender, e)

        Clear_All()
        'MsgBox("تمت عملية التسجيل بنجاح", MsgBoxStyle.Information)
    End Sub

    Private Sub txtprc_KeyUp(sender As Object, e As KeyEventArgs) Handles txtprc.KeyUp
        If e.KeyCode = 13 Then
            If txtprc.Text.Trim = "" Then
            Else
                Bol = True
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select sname,group_name from items where itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    cmbname.Text = dr("sname")
                    cmbcats.Text = dr("group_name")
                End If
                txtqunt.Text = 0
                txtqunt.Focus()
                txtqunt.SelectAll()

                txtStore.Text = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)

                Bol = False
            End If
        End If
    End Sub

    Function ValidateSave() As Boolean
        If cmbcats.Text = "" Then MsgBox("فضلا أدخل مجموعة الصنف", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbname.Text = "" Then MsgBox("فضلا أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbname.Focus() : Return False
        If cmbStores.Text = "" Then MsgBox("من فضلك اختر المخزن", MsgBoxStyle.Exclamation) : cmbStores.Focus() : Return False
        If txtqunt.Text = "" Then MsgBox("فضلا أدخل الكمية ", MsgBoxStyle.Exclamation) : txtqunt.Focus() : Return False
        If txtqunt.Text = "0" Then MsgBox("فضلا أدخل الكمية ", MsgBoxStyle.Exclamation) : txtqunt.Focus() : Return False
        If txtprc.Text = "" Then MsgBox("فضلا أدخل باركود الصنف", MsgBoxStyle.Exclamation) : Return False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from Items where itm_id =N'" & txtprc.Text.Trim & "'and group_name =N'" & cmbcats.Text.Trim & "'and sname =N'" & cmbname.Text.Trim & "'and Stores =N'" & cmbStores.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H = 0 Then
            MsgBox("الصنف غير مطابق من فضلك راجع الصنف ", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
        End If

        Return True
    End Function

    Private Sub btnCloseAdjustmentsStores_Click(sender As Object, e As EventArgs) Handles btnCloseAdjustmentsStores.Click
        PanelViewAdjustmentsStores.Dock = DockStyle.None
        PanelViewAdjustmentsStores.Top = 5000
    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            txtprcView.Enabled = False
            cmbCatsView.Enabled = False
            cmbStoresView.Enabled = False
            cmbNameView.Enabled = False
        Else
            txtprcView.Enabled = True
            cmbCatsView.Enabled = True
            cmbStoresView.Enabled = True
            cmbNameView.Enabled = True
        End If
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(sender As Object, e As EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub

            Dim ItmID As String = DataGridView1.SelectedRows(i).Cells(0).Value
            Dim ItmParcode As String = DataGridView1.SelectedRows(i).Cells(2).Value
            Dim ItmStores As String = DataGridView1.SelectedRows(i).Cells(6).Value

            If rdoIncreaseView.Checked = True Then
                cmd.CommandText = "delete from BilltINData where id =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
            End If
            If rdoDepositView.Checked = True Then
                cmd.CommandText = "delete from BillsalData where id =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
            End If

            IM.Store(ItmParcode, ItmStores)

            If ConnectOnlineStore = "YES" Then
                EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", ItmParcode)
                StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", ItmParcode)
                Cos.UpdateProductStock(StockOnline, ItmParcode, EditItmId)
            End If

        Next
        btnShow.PerformClick()
    End Sub

    Private Sub BtnFind_Click(sender As Object, e As EventArgs) Handles BtnFind.Click
        PanelViewAdjustmentsStores.Top = 20
        PanelViewAdjustmentsStores.Dock = DockStyle.Fill
        btnShow.PerformClick()
    End Sub

    Private Sub dtpDateItem_KeyUp(sender As Object, e As KeyEventArgs) Handles dtpDateItem.KeyUp
        If e.KeyCode = 13 Then
            cmbStores.Focus()
        End If
    End Sub

    Private Sub cmbStores_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbStores.KeyUp
        If e.KeyCode = 13 Then
            txtprc.Focus()
        End If
    End Sub

    Private Sub txtqunt_KeyUp(sender As Object, e As KeyEventArgs) Handles txtqunt.KeyUp
        If e.KeyCode = 13 Then
            btnSave.PerformClick()
        End If
    End Sub

    Private Sub cmbcats_DropDown(sender As Object, e As EventArgs) Handles cmbcats.DropDown
        cmbcats.Text = ""
        cmbname.Text = ""
        txtprc.Text = ""
        txtqunt.Text = ""
        txtStore.Text = ""
    End Sub

    Private Sub cmbname_DropDown(sender As Object, e As EventArgs) Handles cmbname.DropDown
        cmbname.Text = ""
        txtprc.Text = ""
        txtqunt.Text = ""
        txtStore.Text = ""
    End Sub

    Private Sub btnShow_Click(sender As Object, e As EventArgs) Handles btnShow.Click
        DataGridView1.DataSource = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If rdoIncreaseView.Checked = True Then
            S = Cls.Get_Select_Grid_S("id as [رقم],bill_date as [التاريخ],itm_id as [الباركود],itm_cat as [المجموعة],itm_name as [الصنف],qu_unity as [الكمية],Stores as [المخزن]", "BilltINData", "bill_no =N'تسوية'")
        End If
        If rdoDepositView.Checked = True Then
            S = Cls.Get_Select_Grid_S("id as [رقم],bill_date as [التاريخ],itm_id as [الباركود],itm_cat as [المجموعة],itm_name as [الصنف],qu_unity as [الكمية],Stores as [المخزن]", "BillsalData", "bill_no =N'تسوية'")
        End If
        If chkAll.Checked = False Then
            If txtprcView.Text <> "" Then
                S = S & " and itm_id =N'" & txtprcView.Text.Trim & "'"
            End If
            If cmbCatsView.Text <> "" Then
                S = S & " and itm_cat =N'" & cmbCatsView.Text.Trim & "'"
            End If
            If cmbNameView.Text <> "" Then
                S = S & " and itm_name =N'" & cmbNameView.Text.Trim & "'"
            End If
            If cmbStoresView.Text <> "" Then
                S = S & " and Stores =N'" & cmbStoresView.Text.Trim & "'"
            End If
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [التاريخ]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [الصنف]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
        DataGridView1.Columns(2).Width = 90
        DataGridView1.Columns(3).Width = 120

        DataGridView1.Columns(0).Visible = False

        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(1).Value)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(1).Value = SM
        Next

        Dim SM1 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM1 = SM1 + DataGridView1.Rows(i).Cells(5).Value
        Next
        txtNumberQunt.Text = SM1

    End Sub

    Private Sub txtprcView_TextChanged(sender As Object, e As EventArgs) Handles txtprcView.TextChanged
        GetBarcodeMore(txtprcView.Text) : If ParcodeMore <> "" Then : If ParcodeMore <> 0 Then : txtprcView.Text = ParcodeMore : End If : End If

    End Sub

    Private Sub txtprc_TextChanged(sender As Object, e As EventArgs) Handles txtprc.TextChanged
        If Actions = False Then
            GetBarcodeMore(txtprc.Text) : If ParcodeMore <> "" Then : If ParcodeMore <> 0 Then : txtprc.Text = ParcodeMore : End If : End If
        End If
    End Sub

    Private Sub txtsearsh_TextChanged(sender As Object, e As EventArgs) Handles txtsearsh.TextChanged
        'Try
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
                S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الصنف],store as [المخزون],Stores as [المخزن],InventoryDone as [تم الجرد]", "items", "id<>''")
            Else
                S = Cls.Get_Select_Grid_S("itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الصنف],store as [المخزون],Stores as [المخزن],InventoryDone as [تم الجرد]", "items", "sname Like N'%" & txtsearsh.Text & "%'")
            End If
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)

            DTGV.Columns(5).Visible = False

            For i As Integer = 0 To DTGV.RowCount - 1
            If Val(DTGV.Rows(i).Cells(5).Value.ToString()) = 1 Then
                DTGV.Rows(i).DefaultCellStyle.BackColor = Color.Red
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.White
            Else
                DTGV.Rows(i).DefaultCellStyle.BackColor = Color.White
                    DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Black
                End If
            Next


        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub

    Private Sub DTGV_DoubleClick(sender As Object, e As EventArgs) Handles DTGV.DoubleClick
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Actions = True
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select  itm_id,group_name,sname,store,Stores from items where itm_id =N'" & DTGV.SelectedRows(0).Cells(0).Value & "' and Stores=N'" & DTGV.SelectedRows(0).Cells(4).Value & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            txtprc.Text = dr(0).ToString
            cmbcats.Text = dr(1).ToString
            cmbname.Text = dr(2).ToString
            'txtqunt.Text = dr(3).ToString
            cmbStores.Text = dr(4).ToString
        End If
        txtqunt.Text = 1
        txtStore.Text = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)

        txtqunt.Focus()
        txtqunt.SelectAll()
        Actions = False
    End Sub

    Private Sub txtqunt_TextChanged(sender As Object, e As EventArgs) Handles txtqunt.TextChanged
        MyVars.CheckNumber(txtqunt)
    End Sub

    Private Sub btnInventoryDone_Click(sender As Object, e As EventArgs) Handles btnInventoryDone.Click
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Items set InventoryDone = " & Val(0) & "" : cmd.ExecuteNonQuery()

        txtsearsh_TextChanged(sender, e)

    End Sub

    Private Sub PictureBox2_Click(sender As Object, e As EventArgs) Handles PictureBox2.Click
        txtsearsh_TextChanged(sender, e)
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class