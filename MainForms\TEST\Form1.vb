﻿Imports System.Data.SqlClient
Imports System.Drawing.Drawing2D

Public Class Form1
    Dim billno As String = ""
    Private Sub Form1_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        cboServer.Items.Add(".")
        cboServer.Items.Add("(LocalDb)\MSSQLLocalDB")
        cboServer.Items.Add(".\SQLEXPRESS")
        cboServer.Items.Add(String.Format("{0}\SQLEXPRESS", Environment.MachineName))

    End Sub

    Private Sub Headerx()
        'Dim Cn As New SqlConnection()
        'If NetworkName = "No" Then
        '    constring = "Data Source=" + ComputerName + ";Initial Catalog=" + DataBaseName + ";Integrated Security=True"
        'End If
        'If NetworkName = "Yes" Then
        '    constring = "Data Source=" + ComputerName + ";Initial Catalog=" + DataBaseName + ";Password=" + PasswordServer + ";Persist Security Info=True;User ID=" + UserNameServer + ";Connection Timeout=200;pooling=true;max pool size=2500"
        'End If
        'If NetworkName = "Source" Then
        '    constring = "Data Source=.\SQLEXPRESS;AttachDbFilename=|DataDirectory|\DatabaseElectric.mdf;Integrated Security=True;User Instance=True"
        'End If
        'If NetworkName = "LocalDB" Then
        '    constring = My.Computer.FileSystem.ReadAllText(Application.StartupPath & "\ConnectionString.txt")
        'End If
        'mykey.SetValue("FITSOFTConstring", constring)

        'Cn.ConnectionString = constring
        'cmd.Connection = Cn
        'If Cn.State = ConnectionState.Closed Then
        '    Cn.Open()
        '    trans = Cn.BeginTransaction
        '    cmd.Transaction = trans
        'End If

        Try
            connectionStringTransaction()
            MAXRECORD()

            'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            'S = "insert into Sales_Bill(Company_Branch_ID,bill_No,Vendorname) values ("
            'S = S & "N'" & Company_Branch_ID & "',N'" & billno & "' ,N'AAAA')"
            'cmd.CommandText = S : cmd.ExecuteNonQuery()

            'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            'S = "insert into BillsalData(Company_Branch_ID, bill_no, itm_id, itm_name,)  values("
            'S = S & "N'" & Company_Branch_ID & "',N'" & billno & "',N'10102064',N'BBBBBB')"
            'cmd.CommandText = S : cmd.ExecuteNonQuery()

            trans.Commit()
        Catch ex As Exception
            trans.Rollback()
            ErrorHandling(ex, Me.Text)
        End Try
        connectionStringClose()
    End Sub

    Private Sub btnsave_Click(sender As Object, e As EventArgs) Handles btnsave.Click
        Headerx()
    End Sub

    Private Sub MAXRECORD()
        Try

            Dim NumMax1 As Boolean = False
            Dim NumMax2 As Boolean = False
            Dim sh As Long
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from Sales_Bill  where bill_No <> N'جرد'"
            dr = cmd.ExecuteReader
            Dim dt As New DataTable
            dt.Load(dr)

            If dt.Rows.Count = 0 Then
                sh = 1
                NumMax1 = True
            Else
                NumMax1 = False
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(CAST(bill_No As float)) as mb FROM Sales_Bill where bill_No <> N'جرد'"
                dr = cmd.ExecuteReader
                dr.Read()
                sh = dr("mb")
            End If

            '======================================================
            Dim sh2 As Long
            Dim dt2 As New DataTable
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from Receive_Sales_Bill"
            dr = cmd.ExecuteReader
            dt2.Load(dr)

            If dt2.Rows.Count = 0 Then
                sh2 = 1
                NumMax2 = True
            Else
                NumMax2 = False
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(CAST(bill_No As float)) as mb FROM Receive_Sales_Bill"
                dr = cmd.ExecuteReader
                dr.Read()
                sh2 = dr("mb")
            End If

            If NumMax1 = True And NumMax2 = True Then
                billno = 1
            Else
                If sh > sh2 Then
                    billno = sh + 1
                Else
                    billno = sh2 + 1
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub sdfsd()

        'dbConnection()
        Using transaction As SqlTransaction = Cn.BeginTransaction
            Try

                cmd = New SqlCommand
                With cmd
                    'For Each row As DataGridViewRow In dgw.Rows
                    '    If CheckExistingStu(row.Cells(2).Value) <> True Then
                    '        txt = Environment.NewLine &
                    '        "insert into tblStudent(AdmissionNo,awrno,boardregno,AdmissionDate,
                    '        StudentName,FatherName,DOB,Gender,FatherCNIC, FatherCNO, TemporaryAddress,Religion) 
                    '        VALUES (@d3,@d4,@d5,@d6,@d10,@d11,@d12,@d13,@d14,@d15,@d16,@d17)" & Environment.NewLine & "

                    '    INSERT INTO tblEnrollment ([StudentID],[SectionID],[CategoryID]
                    '           ,[Is_Active])
                    '     VALUES ((Select StudentID From tblStudent WHERE AdmissionNo=@d3),
                    '        (Select tblSection.SectionID From tblSection
                    'Where tblSection.SectionName=@d9 AND 
                    '        tblSection.ClassID = (Select ClassID From tblClass Where tblClass.ClassName = @d8 
                    'AND tblClass.SessionID = (Select SessionID From tblSession 
                    '        WHERE tblSession.SessionName=@d7))),(Select CategoryID From tblStuCategory WHERE Category=@d18),1)
                    '" & Environment.NewLine

                    '                        Dim Jointarray As List(Of String) = New List(Of String)()
                    '                        Jointarray.Clear()
                    '                        test = row.Cells(18).Value.ToString
                    '                        If Not String.IsNullOrWhiteSpace(test) Then
                    '                            Jointarray.AddRange(test.Split(New Char() {","c}))
                    '                            For Each str As String In Jointarray
                    '                                txt = txt + "
                    'insert Into StudentDocSubmitted(DocID,AdmissionNo,DocName) VALUES ((SELECT [DocID]
                    '  FROM [Document] WHERE [DocName] = '" & Trim(str) & "'),@d3,'" & Trim(str) & "')" & Environment.NewLine
                    '                            Next
                    '                        End If

                    '                        Sql = Sql + txt

                    '                        .Connection = conn
                    '                        .CommandText = Sql
                    '                        .Parameters.Clear()
                    '                        .Parameters.AddWithValue("@d2", row.Cells(1).Value.ToString)
                    '                        .Parameters.AddWithValue("@d3", row.Cells(2).Value.ToString)
                    '                        .Parameters.AddWithValue("@d4", row.Cells(3).Value.ToString)
                    '                        .Parameters.AddWithValue("@d5", row.Cells(4).Value.ToString)
                    '                        .Parameters.AddWithValue("@d6", Convert.ToDateTime(row.Cells(5).Value).ToString("yyyy-MM-dd"))
                    '                        .Parameters.AddWithValue("@d7", row.Cells(6).Value.ToString)
                    '                        .Parameters.AddWithValue("@d8", row.Cells(7).Value.ToString)
                    '                        .Parameters.AddWithValue("@d9", row.Cells(8).Value.ToString)
                    '                        .Parameters.AddWithValue("@d10", row.Cells(9).Value.ToString)
                    '                        .Parameters.AddWithValue("@d11", row.Cells(10).Value.ToString)
                    '                        .Parameters.AddWithValue("@d12", Convert.ToDateTime(row.Cells(11).Value).ToString("yyyy-MM-dd"))
                    '                        .Parameters.AddWithValue("@d13", row.Cells(12).Value.ToString)
                    '                        .Parameters.AddWithValue("@d14", row.Cells(13).Value.ToString)
                    '                        .Parameters.AddWithValue("@d15", row.Cells(14).Value.ToString)
                    '                        .Parameters.AddWithValue("@d16", row.Cells(15).Value.ToString)
                    '                        .Parameters.AddWithValue("@d17", Trim(row.Cells(16).Value.ToString))
                    '                        .Parameters.AddWithValue("@d18", row.Cells(17).Value.ToString)

                    '                    End If
                    '                Next
                    '                Result = .ExecuteNonQuery
                    transaction.Commit()
                End With
            Catch ex As Exception
                Try
                    transaction.Rollback()
                Catch exRollback As Exception
                    ' Do logging or something when rollback failed.
                End Try
                MsgBox("Transaction failed check data / database settings")
            End Try
        End Using

    End Sub
End Class
