﻿Imports CrystalDecisions.CrystalReports.Engine
Public Class frmShowSalary
    Dim WithEvents BS As New BindingSource
    Dim Yers As String
    Private Sub frmShowSalary_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbNameEmployee)
        GetData()
        PanelSalaryEmploye.Top = 5000
        If PermtionName <> "مدير" Then
            btnDelete.Visible = False
            btnEdit.Visible = False
        End If
    End Sub

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT SLYID as [مسلسل],NameEmployee as [أسم الموظف],Month as [الشهر],SalarySystemName as [نظام الراتب],SalaryDate as [التاريخ],BasicSalary as [المرتب الاساسى],ValueAdditional as [قيمة الاضافى],ValuePremium as [قيمة العلاوة],ValueIncentive as [قيمة الحافز],Reward as [مكافأة],RatherNatureWork as [بدل طبيعة عمل],TransferAllowance as [بدل نقل],HousingAllowance as [بدل سكن],OtherAddition as [اضافات آخرى],ChnagedSalary as [الاجر المتغير],Insurances as [التامينات],WorkTax as [ضريبة كسب عمل],Advance as [سلفة],DiscountsDelay as [خصومات التاخير],DiscountsAbsence as [خصومات الغياب],DiscountsSanctions as [خصومات الجزاءات],OtherDiscount as [خصومات أخرى],NetSalary as [صافى المرتب] FROM View_Salary where SLYID <> N''"
        If chkAll.Checked = False Then
            If cmbNameEmployee.Text <> Nothing Then
                S = S & "and NameEmployee =N'" & cmbNameEmployee.Text.Trim & "'"
            End If
            If cmbMonth.Text <> Nothing Then
                S = S & "and Month =N'" & cmbMonth.Text.Trim & "'"
            End If
            If cmbSalaryPaymentSystem.Text <> Nothing Then
                S = S & "and SalarySystemName =N'" & cmbSalaryPaymentSystem.Text.Trim & "'"
            End If
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and SalaryDate >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and SalaryDate <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [مسلسل]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [التاريخ]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [أسم الموظف]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

        Dim SM1 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM1 = SM1 + DataGridView1.Rows(i).Cells(22).Value
        Next
        txtTotalNetSalary.Text = SM1

        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(4).Value)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(4).Value = SM
        Next
    End Sub

    Private Sub btnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnShow.Click
        GetData()
    End Sub

    Private Sub btnEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnEdit.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        EditItmId = DataGridView1.SelectedRows(0).Cells(0).Value
        Action = "Edit"
        frmSalary.Show()
        Me.Close()
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        Dim NumberID, XDate As String
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub

            NumberID = DataGridView1.SelectedRows(0).Cells(0).Value

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete from Salary where SLYID =N'" & NumberID & "'" : cmd.ExecuteNonQuery()

            XDate = DataGridView1.SelectedRows(i).Cells(4).Value
            Get_Movement_In_Out_Money(XDate, Treasury_Code)
        Next
        GetData()
    End Sub

    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click
        
        If SettingPrinterAuto = "YES" Then
            Dim DefaultPrinterBill As String = mykey.GetValue("DefaultPrinterA4", "DefaultPrinterA4")
            If Not Cls.SetDefaultSystemPrinter4(DefaultPrinterBill) Then
                MessageBox.Show("Error occured will trying to set the default printer!")
            End If
        End If

        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        S = "SELECT SLYID as [مسلسل],NameEmployee as [أسم الموظف],Month as [الشهر],SalarySystemName as [نظام الراتب],SalaryDate as [التاريخ],BasicSalary as [المرتب الاساسى],ValueAdditional as [قيمة الاضافى],ValuePremium as [قيمة العلاوة],ValueIncentive as [قيمة الحافز],Reward as [مكافأة],RatherNatureWork as [بدل طبيعة عمل],TransferAllowance as [بدل نقل],HousingAllowance as [بدل سكن],OtherAddition as [اضافات آخرى],ChnagedSalary as [الاجر المتغير],Insurances as [التامينات],WorkTax as [ضريبة كسب عمل],Advance as [سلفة],DiscountsDelay as [خصومات التاخير],DiscountsAbsence as [خصومات الغياب],DiscountsSanctions as [خصومات الجزاءات],OtherDiscount as [خصومات أخرى],NetSalary as [صافى المرتب] FROM View_Salary where SLYID <> N''"

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            S = "insert into PrintSalesPurchases(Company_Branch_ID,BILL_NO,itm_id,itm_cat,itm_name,Unity,disc,BEY,STAYING,qu,store,totalprice,totalpricebeforedisc,totalpriceafterdisc,TotalBeforeDisc,TotalDisc,TotalafterDisc,TotalBay,Totalstaying,vintinval,vndiscount,VnPay,vnamntdebit,vnamntcredit,det)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView1.Rows(i).Cells(0).Value & "',N'" & DataGridView1.Rows(i).Cells(1).Value & "',N'" & DataGridView1.Rows(i).Cells(2).Value & "',N'" & DataGridView1.Rows(i).Cells(3).Value & "',N'" & DataGridView1.Rows(i).Cells(4).Value & "',N'" & DataGridView1.Rows(i).Cells(5).Value & "',N'" & DataGridView1.Rows(i).Cells(6).Value & "',N'" & DataGridView1.Rows(i).Cells(7).Value & "',N'" & DataGridView1.Rows(i).Cells(8).Value & "',N'" & DataGridView1.Rows(i).Cells(9).Value & "',N'" & DataGridView1.Rows(i).Cells(10).Value & "',N'" & DataGridView1.Rows(i).Cells(11).Value & "',N'" & DataGridView1.Rows(i).Cells(12).Value & "',N'" & DataGridView1.Rows(i).Cells(13).Value & "',N'" & DataGridView1.Rows(i).Cells(14).Value & "',N'" & DataGridView1.Rows(i).Cells(15).Value & "',N'" & DataGridView1.Rows(i).Cells(16).Value & "',N'" & DataGridView1.Rows(i).Cells(17).Value & "',N'" & DataGridView1.Rows(i).Cells(18).Value & "',N'" & DataGridView1.Rows(i).Cells(19).Value & "',N'" & DataGridView1.Rows(i).Cells(20).Value & "',N'" & DataGridView1.Rows(i).Cells(21).Value & "',N'" & DataGridView1.Rows(i).Cells(22).Value & "',N'" & txtTotalNetSalary.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New rptSalary

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        AddReportView()
        Dim txtname, txtNameAr, txtNameEn As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير بالمرتبات"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "تقرير بالمرتبات"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If

    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(sender As Object, e As EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub BtnPrintDetails_Click(sender As Object, e As EventArgs) Handles BtnPrintDetails.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintAllItems")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintAllItems(Company_Branch_ID,itm_id, group_name,Stores, sname, Unity, rng, qunt, TinPrice, SalPrice, tin, sal, btin, bsal, expe, decayed, tinpricetotal, salpricetotal, btinpricetotal, decayedpricetotal, ValStore, profits, Valu_purch)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & txtEmp.Text.Trim & "',N'" & txtMonth.Text.Trim & "',N'" & txtSalaryPaymentSystem.Text.Trim & "',N'" & txtDate.Text.Trim & "',N'" & txtBasicSalary.Text.Trim & "',N'" & txtValueAdditional.Text.Trim & "',N'" & txtValuePremium.Text.Trim & "',N'" & txtValueIncentive.Text.Trim & "',N'" & txtReward.Text.Trim & "',N'" & txtRatherNatureWork.Text.Trim & "',N'" & txtTransferAllowance.Text.Trim & "',N'" & txtHousingAllowance.Text.Trim & "',N'" & txtOtherAddition.Text.Trim & "',N'" & txtChnagedSalary.Text.Trim & "',N'" & txtInsurances.Text.Trim & "',N'" & txtWorkTax.Text.Trim & "',N'" & txtAdvance.Text.Trim & "',N'" & txtDiscountsDelay.Text.Trim & "',N'" & txtDiscountsAbsence.Text.Trim & "',N'" & txtDiscountsSanctions.Text.Trim & "',N'" & txtOtherDiscount.Text.Trim & "',N'" & txtNetSalary.Text.Trim & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New rptSalaryEmp
        Dim txtNameAr, txtNameEn As TextObject

        Cls.Select_More_Data_Branch_Print("PrintAllItems", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txtNameAr = rpt.Section1.ReportObjects("Text1")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("Text10")
        txtNameEn.Text = NameEnCompany
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub DataGridView1_DoubleClick(sender As Object, e As EventArgs) Handles DataGridView1.DoubleClick
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        PanelSalaryEmploye.Top = 130
        PanelSalaryEmploye.Dock = DockStyle.Fill

        txtEmp.Text = DataGridView1.SelectedRows(0).Cells(1).Value
        txtMonth.Text = DataGridView1.SelectedRows(0).Cells(2).Value
        txtSalaryPaymentSystem.Text = DataGridView1.SelectedRows(0).Cells(3).Value
        txtDate.Text = DataGridView1.SelectedRows(0).Cells(4).Value
        txtBasicSalary.Text = DataGridView1.SelectedRows(0).Cells(5).Value
        txtValueAdditional.Text = DataGridView1.SelectedRows(0).Cells(6).Value
        txtValuePremium.Text = DataGridView1.SelectedRows(0).Cells(7).Value
        txtValueIncentive.Text = DataGridView1.SelectedRows(0).Cells(8).Value
        txtReward.Text = DataGridView1.SelectedRows(0).Cells(9).Value
        txtRatherNatureWork.Text = DataGridView1.SelectedRows(0).Cells(10).Value
        txtTransferAllowance.Text = DataGridView1.SelectedRows(0).Cells(11).Value
        txtHousingAllowance.Text = DataGridView1.SelectedRows(0).Cells(12).Value
        txtOtherAddition.Text = DataGridView1.SelectedRows(0).Cells(13).Value
        txtChnagedSalary.Text = DataGridView1.SelectedRows(0).Cells(14).Value
        txtInsurances.Text = DataGridView1.SelectedRows(0).Cells(15).Value
        txtWorkTax.Text = DataGridView1.SelectedRows(0).Cells(16).Value
        txtAdvance.Text = DataGridView1.SelectedRows(0).Cells(17).Value
        txtDiscountsDelay.Text = DataGridView1.SelectedRows(0).Cells(18).Value
        txtDiscountsAbsence.Text = DataGridView1.SelectedRows(0).Cells(19).Value
        txtDiscountsSanctions.Text = DataGridView1.SelectedRows(0).Cells(20).Value
        txtOtherDiscount.Text = DataGridView1.SelectedRows(0).Cells(21).Value
        txtNetSalary.Text = DataGridView1.SelectedRows(0).Cells(22).Value

    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbNameEmployee.Enabled = False
            cmbMonth.Enabled = False
            cmbSalaryPaymentSystem.Enabled = False
            cmbNameEmployee.SelectedIndex = -1
            cmbMonth.SelectedIndex = -1
            cmbSalaryPaymentSystem.SelectedIndex = -1
        ElseIf chkAll.Checked = False Then
            cmbNameEmployee.Enabled = True
            cmbMonth.Enabled = True
            cmbSalaryPaymentSystem.Enabled = True
            cmbNameEmployee.SelectedIndex = -1
            cmbMonth.SelectedIndex = -1
        End If
    End Sub

    Private Sub btnCloseSalaryEmploye_Click(sender As Object, e As EventArgs) Handles btnCloseSalaryEmploye.Click
        PanelSalaryEmploye.Dock = DockStyle.None
        PanelSalaryEmploye.Top = 5000
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub

    'Private Sub SplitYersMonth(ByVal SDate As String)
    '    Dim PRC As String = SDate
    '    Dim split As String() = New String() {"/"}
    '    Dim itemsSplit As String() = PRC.Split(split, StringSplitOptions.None)
    '    Yers = itemsSplit(0).ToString()
    'End Sub
End Class