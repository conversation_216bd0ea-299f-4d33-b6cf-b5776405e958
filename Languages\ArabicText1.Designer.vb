﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:4.0.30319.42000
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On

Imports System

Namespace My.Resources
    
    'This class was auto-generated by the StronglyTypedResourceBuilder
    'class via a tool like ResGen or Visual Studio.
    'To add or remove a member, edit your .ResX file then rerun ResGen
    'with the /str option, or rebuild your VS project.
    '''<summary>
    '''  A strongly-typed resource class, for looking up localized strings, etc.
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0"),  _
     Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.Runtime.CompilerServices.CompilerGeneratedAttribute()>  _
    Friend Class ArabicText1
        
        Private Shared resourceMan As Global.System.Resources.ResourceManager
        
        Private Shared resourceCulture As Global.System.Globalization.CultureInfo
        
        <Global.System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")>  _
        Friend Sub New()
            MyBase.New
        End Sub
        
        '''<summary>
        '''  Returns the cached ResourceManager instance used by this class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend Shared ReadOnly Property ResourceManager() As Global.System.Resources.ResourceManager
            Get
                If Object.ReferenceEquals(resourceMan, Nothing) Then
                    Dim temp As Global.System.Resources.ResourceManager = New Global.System.Resources.ResourceManager("FIT_SOFT.ArabicText1", GetType(ArabicText1).Assembly)
                    resourceMan = temp
                End If
                Return resourceMan
            End Get
        End Property
        
        '''<summary>
        '''  Overrides the current thread's CurrentUICulture property for all
        '''  resource lookups using this strongly typed resource class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend Shared Property Culture() As Global.System.Globalization.CultureInfo
            Get
                Return resourceCulture
            End Get
            Set
                resourceCulture = value
            End Set
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to المندوبين.
        '''</summary>
        Friend Shared ReadOnly Property Delegates() As String
            Get
                Return ResourceManager.GetString("Delegates", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to الموظفين.
        '''</summary>
        Friend Shared ReadOnly Property Employees() As String
            Get
                Return ResourceManager.GetString("Employees", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to المصروفات.
        '''</summary>
        Friend Shared ReadOnly Property Expenses() As String
            Get
                Return ResourceManager.GetString("Expenses", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to تقارير مالية.
        '''</summary>
        Friend Shared ReadOnly Property FinancialReports() As String
            Get
                Return ResourceManager.GetString("FinancialReports", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to الأصناف.
        '''</summary>
        Friend Shared ReadOnly Property Items() As String
            Get
                Return ResourceManager.GetString("Items", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to تقارير أصناف.
        '''</summary>
        Friend Shared ReadOnly Property ItemsReports() As String
            Get
                Return ResourceManager.GetString("ItemsReports", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to الصيانة.
        '''</summary>
        Friend Shared ReadOnly Property Maintenance() As String
            Get
                Return ResourceManager.GetString("Maintenance", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to التصنيع.
        '''</summary>
        Friend Shared ReadOnly Property Manufacturing() As String
            Get
                Return ResourceManager.GetString("Manufacturing", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to المشتريات.
        '''</summary>
        Friend Shared ReadOnly Property Purchases() As String
            Get
                Return ResourceManager.GetString("Purchases", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to مرتجعات.
        '''</summary>
        Friend Shared ReadOnly Property Returns() As String
            Get
                Return ResourceManager.GetString("Returns", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to المبيعات.
        '''</summary>
        Friend Shared ReadOnly Property Sales() As String
            Get
                Return ResourceManager.GetString("Sales", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to تقارير مخازن.
        '''</summary>
        Friend Shared ReadOnly Property StoresReports() As String
            Get
                Return ResourceManager.GetString("StoresReports", resourceCulture)
            End Get
        End Property
    End Class
End Namespace
