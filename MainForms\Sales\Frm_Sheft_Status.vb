﻿Public Class Frm_Sheft_Status

    Dim Sheft_Number As String
    Dim ActionXX As Boolean = False

    Private Sub Frm_Sheft_Status_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        MAXRECORD_SheftNumber()
        If ActionXX = False Then
            Check_Sheft_Status()
        End If
        Headerx()
    End Sub

    Private Sub Check_Sheft_Status()

        Dim Sheft_Stat As String = ""
        Cls.Select_More_Data_Branch("Sheft_Status", "Sheft_Stat", "UserName=N'" & UserName & "' and Sheft_Number =N'" & txtSheft_Number.Text & "' and Sheft_Stat =N'اغلاق'")
        If dr.HasRows = False Then
            btnSheft_StatusClose.Enabled = True
            btnSheft_StatusOpen.Enabled = False
            Exit Sub
        Else
            If dr(0) Is DBNull.Value Then
            Else
                Sheft_Stat = dr(0)
            End If
        End If

        If Sheft_Stat = "اغلاق" Then
            MAXRECORD_SheftNumber2()
            btnSheft_StatusClose.Enabled = False
            btnSheft_StatusOpen.Enabled = True
        Else
            MAXRECORD_SheftNumber()
            MAXRECORD_SheftNumberClose()
            btnSheft_StatusClose.Enabled = True
            btnSheft_StatusOpen.Enabled = False
        End If

    End Sub

    Private Sub MAXRECORD_SheftNumber()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Sheft_Status"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            txtSheft_Number.Text = 1
            btnSheft_StatusClose.Enabled = False
            btnSheft_StatusOpen.Enabled = True
            ActionXX = True
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(Sheft_Number As float)) as mb FROM Sheft_Status where Sheft_ID <> N''"
            dr = cmd.ExecuteReader
            dr.Read()
            If dr.HasRows = True Then
                Dim sh As Long
                sh = dr("mb").ToString()
                txtSheft_Number.Text = sh
            End If
        End If

    End Sub

    Private Sub MAXRECORD_SheftNumberClose()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Sheft_Status"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            txtSheft_Number.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(Sheft_Number As float)) as mb FROM Sheft_Status where UserName=N'" & UserName & "' and Sheft_Stat =N'فتح' and Sheft_Number =N'" & txtSheft_Number.Text & "'"
            dr = cmd.ExecuteReader
            dr.Read()
            If dr.HasRows = True Then
                Dim sh As Long
                sh = dr("mb")
                txtSheft_Number.Text = sh
            End If
        End If

    End Sub

    Private Sub MAXRECORD_SheftNumber2()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Sheft_Status"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            txtSheft_Number.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(Sheft_Number As float)) as mb FROM Sheft_Status where Sheft_ID <> N''"
            dr = cmd.ExecuteReader
            dr.Read()
            If dr.HasRows = True Then
                Dim sh As Long
                sh = dr("mb").ToString()
                txtSheft_Number.Text = sh + 1

            End If
        End If

    End Sub

    Private Sub btnSheft_Status_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSheft_StatusOpen.Click
        Check_Sheft_Status()

        Dim Sheft_Stat As String = ""

        Cls.Select_More_Data_Branch("Sheft_Status", "Sheft_Stat", "UserName=N'" & UserName & "'  and Sheft_Number =N'" & txtSheft_Number.Text & "'")
        If dr.HasRows = False Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Sheft_Status(Company_Branch_ID,Sheft_Number,Sheft_Stat,UserName,Sheft_Date) values ("
            S = S & "N'" & Company_Branch_ID & "',N'" & txtSheft_Number.Text & "' ,N'فتح',N'" & UserName & "',N'" & Cls.C_date(DateTimePicker1.Text) & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
            btnSheft_StatusClose.Enabled = True
            btnSheft_StatusOpen.Enabled = False
            MAXRECORD_SheftNumberClose()
            Exit Sub
        Else
            If dr(0) Is DBNull.Value Then
            Else
                Sheft_Stat = dr(0)
            End If
        End If



        If Sheft_Stat = "اغلاق" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Sheft_Status(Company_Branch_ID,Sheft_Number,Sheft_Stat,UserName,Sheft_Date) values ("
            S = S & "N'" & Company_Branch_ID & "',N'" & txtSheft_Number.Text & "' ,N'فتح',N'" & UserName & "',N'" & Cls.C_date(DateTimePicker1.Text) & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
            btnSheft_StatusClose.Enabled = False
            btnSheft_StatusOpen.Enabled = True
        End If
        MAXRECORD_SheftNumberClose()

        Headerx()
    End Sub

    Private Sub btnSheft_StatusClose_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSheft_StatusClose.Click
        Check_Sheft_Status()

        Dim Sheft_Stat As String = Cls.Get_Code_Value_Stores_More("items", "Sheft_Stat", "UserName=N'" & UserName & "'  and Sheft_Number =N'" & txtSheft_Number.Text & "'")

        If Sheft_Stat = "فتح" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Sheft_Status(Company_Branch_ID,Sheft_Number,Sheft_Stat,UserName,Sheft_Date) values ("
            S = S & "N'" & Company_Branch_ID & "',N'" & txtSheft_Number.Text & "' ,N'اغلاق',N'" & UserName & "',N'" & Cls.C_date(DateTimePicker1.Text) & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
            btnSheft_StatusClose.Enabled = False
            btnSheft_StatusOpen.Enabled = True
        End If
        MAXRECORD_SheftNumber2()

        Headerx()
    End Sub

    Private Sub Headerx()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If PermtionName = "مدير" Then
            S = "select Sheft_ID as [رقم],UserName  as [المستخدم], Sheft_Number as [رقم الشيفت] ,Sheft_Stat as [الحالة],Sheft_Date as [التاريخ] from Sheft_Status  order by 1"
        Else
            S = "select Sheft_ID as [رقم],UserName  as [المستخدم], Sheft_Number as [رقم الشيفت] ,Sheft_Stat as [الحالة],Sheft_Date as [التاريخ] from Sheft_Status where Company_Branch_ID =N'" & Company_Branch_ID & "'  order by 1"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        Dgv_Add.DataSource = Cls.PopulateDataView(dr)
        Dgv_Add.Columns(1).Width = 80
        Dgv_Add.Columns(2).Width = 80
        Dgv_Add.Columns(3).Width = 80
        Dgv_Add.Columns(4).Width = 70
        Dgv_Add.Columns(0).Visible = False

        Dim SM As String
        For i As Integer = 0 To Dgv_Add.RowCount - 1
            SM = Val(Dgv_Add.Rows(i).Cells(4).Value)
            SM = Cls.R_date(SM)
            Dgv_Add.Rows(i).Cells(4).Value = SM
        Next

    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If Dgv_Add.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To Dgv_Add.SelectedRows.Count - 1
            If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
            If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim Sheft_ID As String
            Sheft_ID = Dgv_Add.SelectedRows(0).Cells(0).Value

            Cls.delete_Branch("Sheft_Status", "Sheft_ID =N'" & Sheft_ID & "'")
        Next

        MAXRECORD_SheftNumber()
        Check_Sheft_Status()
        Headerx()
    End Sub
End Class