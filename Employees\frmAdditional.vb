﻿Public Class frmAdditional

    Private Sub frmAdditional_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        On Error Resume Next
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If Action = "Edit" Then
            FillData()
            btnSave.Text = "تعديل"
        Else
            MAXRECORD()
            dtpTimeAttendance.Text = "08:00 AM"
            dtpTimeLeave.Text = "04:00 PM"
            cmbNameEmployee.Focus()
        End If
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbNameEmployee)
        GetDateNotBeenActivatedPrograms(DateTimePicker1)
    End Sub

    Private Sub MAXRECORD()
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select * from Additional"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            txtSeries.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(AddID As float)) as mb FROM Additional"
            dr = cmd.ExecuteReader
            dr.Read()
            Dim sh As Long
            sh = dr("mb")
            txtSeries.Text = sh + 1
        End If
    End Sub

    Private Sub FillData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select AddID,NameEmployee,Month,TimeAttendance,TimeLeave,HoursBasicLabor,AdditionalHours FROM Additional where AddID =N'" & EditItmId & "'"
        dr = cmd.ExecuteReader : dr.Read()
        Dim XAddID, XNameEmployee, XMonth, XTimeAttendance, XTimeLeave, XHoursBasicLabor, XAdditionalHours As String
        XAddID = dr("AddID")
        XNameEmployee = dr("NameEmployee")
        XMonth = dr("Month")
        XTimeAttendance = dr("TimeAttendance")
        XTimeLeave = dr("TimeLeave")
        XHoursBasicLabor = dr("HoursBasicLabor")
        XAdditionalHours = dr("AdditionalHours")


        txtSeries.Text = XAddID
        cmbNameEmployee.Text = XNameEmployee
        cmbMonth.Text = XMonth
        dtpTimeAttendance.Text = XTimeAttendance
        dtpTimeLeave.Text = XTimeLeave
        txtHoursBasicLabor.Text = XHoursBasicLabor
        txtAdditionalHours.Text = XAdditionalHours
    End Sub

    Private Sub cmbNameEmployee_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbNameEmployee.KeyUp
        If e.KeyCode = 13 Then
            cmbMonth.Focus()
        End If
    End Sub

    Private Sub cmbMonth_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbMonth.KeyUp
        If e.KeyCode = 13 Then
            dtpTimeAttendance.Focus()
        End If
    End Sub

    Private Sub dtpTimeAttendance_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpTimeAttendance.KeyUp
        If e.KeyCode = 13 Then
            dtpTimeLeave.Focus()
        End If
    End Sub

    Private Sub dtpTimeLeave_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpTimeLeave.KeyUp
        If e.KeyCode = 13 Then
            txtHoursBasicLabor.Focus()
        End If
    End Sub

    Private Sub txtHoursBasicLabor_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtHoursBasicLabor.KeyUp
        If e.KeyCode = 13 Then
            txtAdditionalHours.Focus()
        End If
    End Sub

    Private Sub txtAdditionalHours_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtAdditionalHours.KeyUp
        If e.KeyCode = 13 Then
            btnSave.PerformClick()
        End If
    End Sub

    Private Sub txtHoursBasicLabor_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtHoursBasicLabor.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtHoursBasicLabor)
        Dim F As Double
        F = DateDiff(DateInterval.Minute, dtpTimeAttendance.Value, dtpTimeLeave.Value) / 60
        txtAdditionalHours.Text = Format(F - Val(txtHoursBasicLabor.Text), "Fixed")
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateTextAdd() = False Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If Action = "Edit" Then
            cmd.CommandText = "delete From  Additional where AddID =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Additional(Company_Branch_ID,AddID,NameEmployee,Month,SDate,TimeAttendance,TimeLeave,HoursBasicLabor,AdditionalHours,UserName) values ("
        S = S & "N'" & Company_Branch_ID & "',N'" & txtSeries.Text & "',N'" & cmbNameEmployee.Text.Trim & "',N'" & cmbMonth.Text.Trim & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & dtpTimeAttendance.Text & "',"
        S = S & "N'" & dtpTimeLeave.Text & "',N'" & txtHoursBasicLabor.Text.Trim & "',N'" & txtAdditionalHours.Text.Trim & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Action = "Edit" Then
            Action = "Add"
            Me.Close()
            frmShowAdditional.btnShow.PerformClick()
        Else
            MsgBox("تمت عملية الحفظ بنجاح", MsgBoxStyle.Information)
            ClearSave()
            MAXRECORD()
            dtpTimeAttendance.Text = "08:00 AM"
            dtpTimeLeave.Text = "04:00 PM"
            cmbNameEmployee.Focus()
            Cls.fill_combo_Branch("Employees", "NameEmployee", cmbNameEmployee)
        End If
    End Sub

    Function ValidateTextAdd() As Boolean
        If cmbNameEmployee.Text = "" Then MsgBox("فضلا ادخل أسم الموظف", MsgBoxStyle.Exclamation) : cmbNameEmployee.Focus() : Return False
        If cmbMonth.Text = "" Then MsgBox("فضلا ادخل الشهر", MsgBoxStyle.Exclamation) : cmbMonth.Focus() : Return False
        If txtHoursBasicLabor.Text = "" Then MsgBox("فضلا ادخل ساعات العمل الاساسية", MsgBoxStyle.Exclamation) : txtHoursBasicLabor.Focus() : Return False
        Return True
    End Function

    Private Sub ClearSave()
        Cls.clear(Me)
        cmbNameEmployee.Focus()
    End Sub

    Private Sub BtnFind_Click(sender As Object, e As EventArgs) Handles BtnFind.Click
        frmShowAdditional.Show()
    End Sub

    Private Sub cmbNameEmployee_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbNameEmployee.SelectedIndexChanged
        Employees_Count_Hour()
    End Sub

    Private Sub Employees_Count_Hour()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Emp_Count_Hour from Employees where NameEmployee=N'" & cmbNameEmployee.Text & "'"
        dr = cmd.ExecuteReader()
        If dr.Read = True Then
            txtHoursBasicLabor.Text = dr("Emp_Count_Hour").ToString
        End If
    End Sub

    Private Sub dtpTimeAttendance_ValueChanged(sender As Object, e As EventArgs) Handles dtpTimeAttendance.ValueChanged
        Employees_Count_Hour()
    End Sub

    Private Sub dtpTimeLeave_ValueChanged(sender As Object, e As EventArgs) Handles dtpTimeLeave.ValueChanged
        Employees_Count_Hour()
    End Sub

    Private Sub txtSeries_TextChanged(sender As Object, e As EventArgs) Handles txtSeries.TextChanged
        MyVars.CheckNumber(txtSeries)
    End Sub
End Class