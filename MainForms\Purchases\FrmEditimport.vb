﻿Imports vb = Microsoft.VisualBasic
Public Class FrmEditimport
    Dim archiveManager As New Cls_ArchiveManager

    Private Sub GetData()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If rdoDismissalNotice.Checked = True Then
                S = "SELECT CAST(bill_No As float) As [رقم الفاتورة],Vendorname AS [اسم المورد], bill_date AS [تاريخ الفاتورة], billtime AS [وقت الفاتورة], totalpricebeforedisc AS [القيمة قبل الخصم], disc AS [الخصم], totalpriceafterdisc AS [القيمة بعد الخصم], Stat  AS [الحالة],Notes as [ملاحظات] FROM purchase_bill where BILL_NO <> N'جرد1'"
            End If
            If rdoReceivingPermission.Checked = True Then
                S = "SELECT CAST(bill_No As float) As [رقم الفاتورة],Vendorname AS [اسم المورد], bill_date AS [تاريخ الفاتورة], billtime AS [وقت الفاتورة], totalpricebeforedisc AS [القيمة قبل الخصم], disc AS [الخصم], totalpriceafterdisc AS [القيمة بعد الخصم], Stat  AS [الحالة],Notes as [ملاحظات] FROM Receive_Purchase_bill where BILL_NO <> N'جرد1'"
            End If
            If ChkAll.Checked = False Then
                If cmbvendornameshow.Text <> "" Then
                    S = S & " and  Vendorname  =N'" & cmbvendornameshow.Text.Trim & "'"
                End If
                If txtbillnoSearch.Text <> "" Then
                    S = S & " and  BILL_NO  =N'" & txtbillnoSearch.Text.Trim & "'"
                End If
            End If
            If rdoCurrentDiscTinPrice.Checked = True Then
                S = S & " and DeferredCurrentDiscount =N'0'"
            Else
                S = S & " and DeferredCurrentDiscount =N'1'"
            End If
            If ChkWithoutDate.Checked = False Then
                S = S & " and  bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
            End If
            If FilterSelect = "Number" Then
                S = S & " order by [رقم الفاتورة]"
            End If
            If FilterSelect = "Date" Then
                S = S & " order by [تاريخ الفاتورة]"
            End If
            If FilterSelect = "Name" Then
                S = S & " order by [اسم المورد]"
            End If

            cmd.CommandText = S : dr = cmd.ExecuteReader
            DataGridView1.DataSource = Cls.PopulateDataView(dr)

            Dim SM As String
            For i As Integer = 0 To DataGridView1.RowCount - 1
                SM = Val(DataGridView1.Rows(i).Cells(2).Value)
                SM = Cls.R_date(SM)
                DataGridView1.Rows(i).Cells(2).Value = SM
            Next
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        SumDGV() : SumDetails()
    End Sub

    Private Sub btnprint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnprint.Click
        GetData()
    End Sub

    Private Sub ChkAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkAll.CheckedChanged
        If ChkAll.Checked = True Then
            cmbvendornameshow.Enabled = False
            txtbillnoSearch.Enabled = False
            cmbvendornameshow.SelectedIndex = -1
        Else
            cmbvendornameshow.Enabled = True
            txtbillnoSearch.Enabled = True
        End If
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False

        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub
    Private Sub GetDetails()
        Try
            'If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            'If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim ItmID As String = 0
            If DataGridView1.RowCount <> 0 Then
                ItmID = DataGridView1.SelectedRows(0).Cells(0).Value
            End If
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If rdoDismissalNotice.Checked = True Then
                cmd.CommandText = "select itm_id as [الباركود],itm_cat as [المجموعة] ,itm_name as [اسم الصنف],price as [السعر] ,qu as [1الكمية],qu_unity as [الكمية],itm_Unity as [الوحدة]  ,totalprice as [الأجمالي],Stores as [المخزن],bill_EndDate as [تاريخ الصلاحية] from BilltINData where bill_no =N'" & ItmID & "'"
            End If
            If rdoReceivingPermission.Checked = True Then
                cmd.CommandText = "select itm_id as [الباركود],itm_cat as [المجموعة] ,itm_name as [اسم الصنف],price as [السعر] ,qu as [1الكمية],qu_unity as [الكمية],itm_Unity as [الوحدة]  ,totalprice as [الأجمالي],Stores as [المخزن],bill_EndDate as [تاريخ الصلاحية] from Receive_BilltINData where bill_no =N'" & ItmID & "'"
            End If
            dr = cmd.ExecuteReader
            DataGridView3.DataSource = Cls.PopulateDataView(dr) : SumDetails()
            DataGridView3.Columns(1).Visible = False
            DataGridView3.Columns(4).Visible = False

            txtNumberItems.Text = DataGridView3.RowCount
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub DataGridView1_CellClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridView1.CellClick
        GetDetails()
    End Sub

    Private Sub SumDGV()
        Try
            Dim SM As Double
            For i As Integer = 0 To DataGridView1.Rows.Count - 1
                SM = SM + DataGridView1.Rows(i).Cells(4).Value
            Next
            txttotalpricebefor.Text = SM
            Dim SM2 As Double
            For i As Integer = 0 To DataGridView1.Rows.Count - 1
                SM2 = SM2 + DataGridView1.Rows(i).Cells(5).Value
            Next
            txttotaldisc.Text = SM2

            Dim SM3 As Double
            For i As Integer = 0 To DataGridView1.Rows.Count - 1
                SM3 = SM3 + DataGridView1.Rows(i).Cells(6).Value
            Next
            txttotalpriceafter.Text = SM3
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub SumDetails()
        If DataGridView1.RowCount = 0 Then Exit Sub
        txpricebefor.Text = DataGridView1.SelectedRows(0).Cells(4).Value
        txtdisc.Text = DataGridView1.SelectedRows(0).Cells(5).Value
        txtpriceafter.Text = DataGridView1.SelectedRows(0).Cells(6).Value
    End Sub

    Dim aray_itm_id As New ArrayList
    Dim aray_Stores As New ArrayList

    Private Sub Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button3.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        Try
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim ItmID, XDate As String
            Dim NameVendor As String
            Dim Vendorname As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
                ItmID = DataGridView1.SelectedRows(i).Cells(0).Value
                Vendorname = DataGridView1.SelectedRows(i).Cells(1).Value


                '=========================================== Archive Manager ============================================================
                archiveManager.ArchiveAndDeletePurchaseBill("Delete", ItmID, Vendorname, UserName, "تم حذف فاتورة المشتريات لعدم النشاط")
                archiveManager.ArchiveAndDeleteBilltINData2("Delete", ItmID, UserName, "تم حذف فاتورة المشتريات لعدم النشاط")
                '=========================================== Archive Manager ============================================================


                aray_itm_id.Clear()
                aray_Stores.Clear()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select itm_id,Stores from BilltINData where bill_no =N'" & ItmID & "'"
                dr = cmd.ExecuteReader
                Do While dr.Read = True
                    aray_itm_id.Add(dr(0))
                    aray_Stores.Add(dr(1))
                Loop

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                If rdoDismissalNotice.Checked = True Then
                    cmd.CommandText = "delete From  purchase_bill where bill_no =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
                    'If NotUnityItemsProgram = "YES" Then
                    cmd.CommandText = "delete From  BilltINData where bill_no =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
                    'End If
                End If
                If rdoReceivingPermission.Checked = True Then
                    cmd.CommandText = "delete From  Receive_purchase_bill where bill_no =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
                    cmd.CommandText = "delete From  Receive_BilltINData where bill_no =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
                End If

                cmd.CommandText = "delete From  vndr_disc where TIN_NO =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  vnd where BillNo =N'" & ItmID & "'" : cmd.ExecuteNonQuery()

                cmd.CommandText = "delete From  MOVES where bill_no =N'" & ItmID & "' and MOVStatement =N'مشتريات'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'مدفوعات موردين'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'خصومات موردين'" : cmd.ExecuteNonQuery()


                NameVendor = DataGridView1.SelectedRows(i).Cells(1).Value
                IM.VendorAccountTotal(NameVendor)

                For T As Integer = 0 To aray_itm_id.Count - 1
                    If NotUnityItemsProgram = "YES" Then
                        Dim bill_no_Expired As String = Cls.Get_Code_Value_Stores_More("BilltINData", "bill_no_Expired", "bill_no =N'" & ItmID & "' and itm_id =N'" & aray_itm_id(T) & "' and Stores =N'" & aray_Stores(T) & "'")
                        Dim bill_EndDate As String = Cls.Get_Code_Value_Stores_More("BilltINData", "bill_EndDate", "bill_no =N'" & ItmID & "' and itm_id =N'" & aray_itm_id(T) & "' and Stores =N'" & aray_Stores(T) & "'")
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "delete From  BilltINData where bill_no =N'" & ItmID & "'" : cmd.ExecuteNonQuery()

                        IM.StoreExpired(aray_itm_id(T).ToString(), aray_Stores(T).ToString(), bill_EndDate.ToString(), bill_no_Expired)
                    End If
                    IM.Store(aray_itm_id(T), aray_Stores(T))

                    If ConnectOnlineStore = "YES" Then
                        EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", aray_itm_id(T))
                        StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", aray_itm_id(T))
                        Cos.UpdateProductStock(StockOnline, aray_itm_id(T), EditItmId)
                    End If
                Next

                XDate = DataGridView1.SelectedRows(i).Cells(2).Value
                Get_Movement_In_Out_Money(XDate, Treasury_Code)
            Next
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        'GetData() : GetDetails()
    End Sub

    Private Sub DateTimePicker1_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DateTimePicker1.ValueChanged
        GetData()
    End Sub

    Private Sub DateTimePicker2_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DateTimePicker2.ValueChanged
        GetData()
    End Sub

    Private Sub FrmEditimport_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Me.MdiParent = MDIParent1
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cls.fill_combo("vendors", "Vendorname", cmbvendornameshow)
        PanelDeferredCurrentDiscount.Top = 10000
    End Sub

    Private Sub BtnPrintBill_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnPrintBill.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        EditItmId = DataGridView1.SelectedRows(0).Cells(0).Value

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete from tmpBillTinData" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete from tmppurchase_bill" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "insert into tmpBillTinData (bill_no,itm_id,itm_cat,itm_name,price,qu,qu_unity,itm_Unity,totalprice,username,bill_date,Stores,bill_no_Expired,bill_EndDate,RateVAT,BeforeVAT,ValueVAT,Expired,Discounts,DiscountsValue,Discount_Price_After) select bill_no,itm_id,itm_cat,itm_name,price,qu,qu_unity,itm_Unity,totalprice,username , bill_date,Stores,bill_no_Expired,bill_EndDate,RateVAT,BeforeVAT,ValueVAT,Expired,Discounts,DiscountsValue,Discount_Price_After from BillTinData where bill_no =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "insert into tmppurchase_bill (bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,username,Notes) select bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,username,Notes from purchase_bill where bill_no =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        FrmEditimportad.Show() : Frmimport.Close()
    End Sub

    Dim TotalPriceBeforeAverage As Double
    Private Sub PriceTinAverage(ByVal Parcode As String, ByVal Stores As String, ByVal Unity As String, ByVal qunt As Double, ByVal TotalPrice As Double)

        Dim StoreItems, TotalPriceTinAverage, BalanceBeforeBuying, TotalBalanceBeforeBuying As Double

        Dim TotalTinPrice As Double

        '================================================ المخزون الحالى =====================================================

        StoreItems = IM.Get_Itm_Store(Parcode, Stores)
        Try
            '================================================ متوسط السعر =====================================================
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TinPriceAverage from Items where itm_id =N'" & Parcode & "' and Stores =N'" & Stores & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
            Else
                Dim xx As String = dr("TinPriceAverage").ToString()
                If xx = "" Then
                    Dim LoopTotalTinPrice As Long
                    Dim aray_totalprice As New ArrayList
                    aray_totalprice.Clear()
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "Select totalprice from BilltINData  where itm_id =N'" & Parcode & "' and Stores =N'" & Stores & "'"
                    dr = cmd.ExecuteReader
                    Do While dr.Read = True
                        LoopTotalTinPrice += dr(0).ToString()
                    Loop
                    TotalTinPrice = LoopTotalTinPrice

                    If StoreItems <> 0 Then
                        TotalPriceTinAverage = Math.Round(TotalTinPrice, 2) / Math.Round(StoreItems, 2)
                        TotalPriceTinAverage = Math.Round(TotalPriceTinAverage, 2)
                    Else
                        TotalPriceTinAverage = 0
                    End If
                Else
                    TotalPriceTinAverage = xx
                End If
            End If

            Dim Xqunt As Double = qunt
            If NotUnityItemsProgram = "YES" Then
                NumberPieces = Cls.Get_Code_Value_Branch_More("ItemsUnity", "NumberPieces", "itm_id=N'" & Parcode & "' and Unity_Name=N'" & Unity & "'")
                If NumberPieces <> 1 Then
                    Xqunt = Val(NumberPieces) * Val(qunt)
                End If
            End If
            '================================================ متوسط سعر الشراء الجديد =====================================================
            BalanceBeforeBuying = Val(TotalPriceTinAverage) * Val(StoreItems)

            TotalBalanceBeforeBuying = Val(TotalPrice) + Val(BalanceBeforeBuying)
            TotalBalanceBeforeBuying = Math.Round(TotalBalanceBeforeBuying, 2)

            Dim TotalTotal As Double = StoreItems + Xqunt
            If TotalTotal = 0 Then
                TotalPriceBeforeAverage = 0
            Else
                TotalPriceBeforeAverage = TotalBalanceBeforeBuying / TotalTotal
                If TinPriceAverageThreeDigits = "NO" Then
                    TotalPriceBeforeAverage = Math.Round(TotalPriceBeforeAverage, 2)
                Else
                    TotalPriceBeforeAverage = Math.Round(TotalPriceBeforeAverage, 3)
                End If
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update items set TinPriceAverage = " & Val(TotalPriceBeforeAverage) & " where itm_id =N'" & Parcode & "' and Stores =N'" & Stores & "'" : cmd.ExecuteNonQuery()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub btnCloseDeferredCurrentDiscount_Click(sender As Object, e As EventArgs) Handles btnCloseDeferredCurrentDiscount.Click
        PanelDeferredCurrentDiscount.Top = 10000
    End Sub

    Private Sub btnDeferredCurrentDiscount_Click(sender As Object, e As EventArgs) Handles btnDeferredCurrentDiscount.Click
        PanelDeferredCurrentDiscount.Top = 61
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class
