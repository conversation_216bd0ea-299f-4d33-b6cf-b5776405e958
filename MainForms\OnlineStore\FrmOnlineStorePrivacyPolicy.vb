﻿Public Class FrmOnlineStorePrivacyPolicy
    Dim Area_ID As String
    Dim ActivEdit As Boolean = False

    Private Sub Frm_Group_Branch_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Headerx()
        PanelAddNew.Dock = DockStyle.None
        PanelAddNew.Top = 5000
    End Sub

    Private Sub btnsave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnsave.Click
        If ValidateSave() = False Then Exit Sub

        If Not ConnectingOnlineStore() Is Nothing Then
            If ActivEdit = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update PrivacyPolicy set Title =N'" & txtTitle.Text & "',Description =N'" & txtDescription.Text & "' where Id= '" & txtID.Text & "'"
                cmd.ExecuteNonQuery()
                Cn.Close()
                connect()
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into PrivacyPolicy (Title,Description,IsDelete)"
                S = S & " values (N'" & txtTitle.Text.Trim & "',N'" & txtDescription.Text & "',N'0')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
                Cn.Close()
                connect()
            End If
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If

        txtTitle.Text = ""
        txtDescription.Text = ""
        ActivEdit = False
        btnsave.Text = "إضافة"
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.save_32

        PanelAddNew.Dock = DockStyle.None
        PanelAddNew.Top = 5000
        Headerx()

    End Sub

    Private Sub MAXRECORD()
        If Not ConnectingOnlineStore() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from PrivacyPolicy"
            dr = cmd.ExecuteReader

            Dim dt As New DataTable
            dt.Load(dr)

            If dt.Rows.Count = 0 Then
                txtID.Text = 1
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(CAST(Id As float)) as mb FROM PrivacyPolicy where Id <> N''"
                dr = cmd.ExecuteReader
                dr.Read()

                Dim sh As Long
                sh = dr("mb")
                txtID.Text = sh + 1
            End If
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Sub

    Function ValidateSave() As Boolean

        If txtTitle.Text = "" Then MsgBox("فضلا أدخل العنوان", MsgBoxStyle.Exclamation) : txtTitle.Focus() : Return False
        If txtDescription.Text = "" Then MsgBox("فضلا أدخل التفاصيل", MsgBoxStyle.Exclamation) : txtDescription.Focus() : Return False

        If Not ConnectingOnlineStore() Is Nothing Then
            If ActivEdit = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select count(*) from PrivacyPolicy where Title =N'" & txtTitle.Text.Trim & "'" : H = cmd.ExecuteScalar
                If H > 0 Then
                    MsgBox(" العنوان مسجل مسبقاً بنفس الاسم", MsgBoxStyle.Exclamation) : txtTitle.Focus() : Return False
                End If
            End If
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If

        Return True
    End Function

    Private Sub Headerx()
        If Not ConnectingOnlineStore() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "Select Id As [كود], Title As [عنوان], Description As [تفاصيل], IsDelete From dbo.PrivacyPolicy Where (IsDelete = 0)"
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)
            DTGV.Columns(0).Width = 80
            DTGV.Columns(1).Width = 100
            DTGV.Columns(2).Width = 250
            DTGV.Columns(3).Visible = False
            txtNumber.Text = DTGV.RowCount
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Sub

    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If Not ConnectingOnlineStore() Is Nothing Then
            If DTGV.Rows.Count = 0 Then
                MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
                Exit Sub
            End If
            Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
            If x = vbCancel Then Exit Sub
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To DTGV.SelectedRows.Count - 1
                If DTGV.RowCount = 0 Then Beep() : Exit Sub
                If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
                Dim ItmID As String
                ItmID = DTGV.SelectedRows(i).Cells(0).Value

                cmd.CommandText = "update PrivacyPolicy set IsDelete =N'1' where Id= '" & ItmID & "'"
                cmd.ExecuteNonQuery()
            Next
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If

        Headerx()
        ActivEdit = False
        btnsave.Text = "إضافة"
    End Sub

    Private Sub chkIsDelete_CheckedChanged(sender As Object, e As EventArgs)
        Headerx()
    End Sub

    Private Sub DTGV_DoubleClick(sender As Object, e As EventArgs) Handles DTGV.DoubleClick
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        ActivEdit = True
        txtID.Text = DTGV.SelectedRows(0).Cells(0).Value
        txtTitle.Text = DTGV.SelectedRows(0).Cells(1).Value
        txtDescription.Text = DTGV.SelectedRows(0).Cells(2).Value
        btnsave.Text = "تعديل"
        ActivEdit = True
        PanelAddNew.Top = 20
        PanelAddNew.Dock = DockStyle.Fill
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.Edit_1

    End Sub

    Private Sub cmbArea_DropDown(sender As Object, e As EventArgs)
        ActivEdit = False
        btnsave.Text = "إضافة"
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        PanelAddNew.Dock = DockStyle.None
        PanelAddNew.Top = 5000
        ActivEdit = False
        btnsave.Text = "إضافة"
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.save_32
    End Sub

    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        PanelAddNew.Top = 20
        PanelAddNew.Dock = DockStyle.Fill
        txtTitle.Focus()
        ActivEdit = False
        btnsave.Text = "إضافة"
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.save_32
        txtTitle.Text = ""
        txtDescription.Text = ""
        MAXRECORD()
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs) Handles btnEdit.Click
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        ActivEdit = True
        txtID.Text = DTGV.SelectedRows(0).Cells(0).Value
        txtTitle.Text = DTGV.SelectedRows(0).Cells(1).Value
        txtDescription.Text = DTGV.SelectedRows(0).Cells(2).Value
        btnsave.Text = "تعديل"
        ActivEdit = True
        PanelAddNew.Top = 20
        PanelAddNew.Dock = DockStyle.Fill
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.Edit_1
    End Sub

End Class