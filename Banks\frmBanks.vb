﻿Public Class frmBanks

    Private Sub frmBanks_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        On Error Resume Next
        If Action = "Edit" Then
            FillData()
            btnSave.Text = "تعديل"
        Else
            MAXRECORD()
            dtpDateMovement.Focus()
        End If
        FillComboBox()

    End Sub

    Private Sub FillComboBox()
        'cmbAccountNumber.Items.Clear()
        'cmbAccountNumber2.Items.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select distinct AccountNumber from Banks"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    cmbAccountNumber.Items.Add(dr("AccountNumber"))
        '    cmbAccountNumber2.Items.Add(dr("AccountNumber"))
        'Loop

        'cmbBank.Items.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select distinct Bank from Banks"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    cmbBank.Items.Add(dr("Bank"))
        'Loop

        Bra.Fil("BankData", "AccountNumber", cmbAccountNumber)
        Bra.Fil("BankData", "AccountNumber", cmbAccountNumber2)

        Bra.Fil("BankData", "BankName", cmbBank)

        Bra.Fil("Type_Currency", "Name_Currency", cmbCurrency)

    End Sub

    Private Sub MAXRECORD()
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select * from Banks"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            txtSeries.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(BNKID As float)) as mb FROM Banks"
            dr = cmd.ExecuteReader
            dr.Read()
            Dim sh As Long
            sh = dr("mb")
            txtSeries.Text = sh + 1
        End If
    End Sub

    Private Sub FillData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select BNKID,DateMovement,TypeMovement,PaymentMethod,AccountNumber,DocumentNumber,PreviousBalance,Debtor,Creditor,CurrentBalance,DateCollection,Bank,Notes FROM Banks where BNKID =N'" & EditItmId & "'"
        dr = cmd.ExecuteReader : dr.Read()
        Dim XBNKID, XDateMovement, XTypeMovement, XPaymentMethod, XAccountNumber, XDocumentNumber, XPreviousBalance, XDebtor, XCreditor, XCurrentBalance, XDateCollection, XBank, XNotes As String
        XBNKID = dr("BNKID")
        XDateMovement = dr("DateMovement")
        XTypeMovement = dr("TypeMovement")
        XPaymentMethod = dr("PaymentMethod")
        XAccountNumber = dr("AccountNumber")
        XDocumentNumber = dr("DocumentNumber")
        XPreviousBalance = dr("PreviousBalance")
        XDebtor = dr("Debtor")
        XCreditor = dr("Creditor")
        XCurrentBalance = dr("CurrentBalance")
        XDateCollection = dr("DateCollection")
        XBank = dr("Bank")
        XNotes = dr("Notes")


        txtSeries.Text = XBNKID
        dtpDateMovement.Text = XDateMovement
        cmbTypeMovement.Text = XTypeMovement
        cmbPaymentMethod.Text = XPaymentMethod
        cmbAccountNumber.Text = XAccountNumber
        txtDocumentNumber.Text = XDocumentNumber
        txtPreviousBalance.Text = XPreviousBalance
        txtDebtor.Text = XDebtor
        txtCreditor.Text = XCreditor
        txtCurrentBalance.Text = XCurrentBalance
        dtpDateCollection.Text = XDateCollection
        cmbBank.Text = XBank
        txtNotes.Text = XNotes
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub dtpDateMovement_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDateMovement.KeyUp
        If e.KeyCode = 13 Then
            cmbTypeMovement.Focus()
        End If
    End Sub

    Private Sub cmbTypeMovement_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbTypeMovement.KeyUp
        If e.KeyCode = 13 Then
            cmbPaymentMethod.Focus()
        End If
    End Sub

    Private Sub cmbPaymentMethod_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbPaymentMethod.KeyUp
        If e.KeyCode = 13 Then
            cmbAccountNumber.Focus()
        End If
    End Sub

    Private Sub cmbAccountNumber_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbAccountNumber.KeyUp
        If e.KeyCode = 13 Then
            txtDocumentNumber.Focus()
        End If
        SearchBalanceBanks()
    End Sub

    Private Sub txtDocumentNumber_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtDocumentNumber.KeyUp
        If e.KeyCode = 13 Then
            txtPreviousBalance.Focus()
        End If
    End Sub

    Private Sub txtPreviousBalance_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtPreviousBalance.KeyUp
        If e.KeyCode = 13 Then
            txtDebtor.Focus()
        End If
    End Sub

    Private Sub txtDebtor_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtDebtor.KeyUp
        If e.KeyCode = 13 Then
            txtCreditor.Focus()
        End If
    End Sub

    Private Sub txtCreditor_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtCreditor.KeyUp
        If e.KeyCode = 13 Then
            txtCurrentBalance.Focus()
        End If
    End Sub

    Private Sub txtCurrentBalance_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtCurrentBalance.KeyUp
        If e.KeyCode = 13 Then
            dtpDateCollection.Focus()
        End If
    End Sub

    Private Sub dtpDateCollection_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDateCollection.KeyUp
        If e.KeyCode = 13 Then
            cmbBank.Focus()
        End If
    End Sub

    Private Sub cmbBank_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbBank.KeyUp
        If e.KeyCode = 13 Then
            txtNotes.Focus()
        End If
    End Sub

    Private Sub txtNotes_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtNotes.KeyUp
        If e.KeyCode = 13 Then
            btnSave.PerformClick()
        End If
    End Sub

    Private Sub txtDocumentNumber_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtDocumentNumber.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtDocumentNumber)
    End Sub

    Private Sub txtPreviousBalance_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtPreviousBalance.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtPreviousBalance)
        SumTotal()
    End Sub

    Private Sub txtDebtor_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtDebtor.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtDebtor)
        SumTotal()
    End Sub

    Private Sub SumTotal()
        txtCurrentBalance.Text = Format(Val(txtPreviousBalance.Text) + Val(txtCreditor.Text) - Val(txtDebtor.Text), "Fixed")
    End Sub

    Private Sub txtCreditor_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtCreditor.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtCreditor)
        SumTotal()
    End Sub

    Private Sub txtCurrentBalance_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtCurrentBalance.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtCurrentBalance)
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        If ValidateTextAdd() = False Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If Action = "Edit" Then
            cmd.CommandText = "delete From  Banks where BNKID =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'If cmbTypeMovement.Text = "تحويل" Then
        '    S = "insert into BanksTransfer(AccountNumberFrom,AccountNumberTo,Transfer) values ("
        '    S = S & "N'" & cmbAccountNumber.Text & "',N'" & cmbAccountNumber2.Text & "',N'" & txtAmountConversion.Text & "')"
        '    cmd.CommandText = S : cmd.ExecuteNonQuery()
        'Else

        'If cmbTypeMovement.Text = "تحويل" Then
        '    S = "insert into Banks(BNKID,DateMovement,TypeMovement,PaymentMethod,AccountNumber,AccountNumberTo,DocumentNumber,PreviousBalance,Debtor,Creditor,CurrentBalance,DateCollection,Bank,Notes,UserName,Currency_Name) values ("
        '    S = S & "N'" & txtSeries.Text & "',N'" & Cls.C_date(dtpDateMovement.Text) & "',N'" & cmbTypeMovement.Text & "',N'" & cmbPaymentMethod.Text & "',"
        '    S = S & "N'" & cmbAccountNumber.Text & "',N'" & cmbAccountNumber2.Text & "',N'" & txtDocumentNumber.Text & "',N'" & txtPreviousBalance.Text & "',"
        '    S = S & "N'" & txtDebtor.Text & "',N'" & txtAmountConversion.Text & "',N'" & txtCurrentBalance.Text & "',"
        '    S = S & "N'" & Cls.C_date(dtpDateCollection.Text) & "',N'" & cmbBank.Text & "',N'" & txtNotes.Text & "',N'" & UserName & "',N'" & cmbCurrency.Text & "')"
        '    cmd.CommandText = S : cmd.ExecuteNonQuery()
        'Else
        '    S = "insert into Banks(BNKID,DateMovement,TypeMovement,PaymentMethod,AccountNumber,AccountNumberTo,DocumentNumber,PreviousBalance,Debtor,Creditor,CurrentBalance,DateCollection,Bank,Notes,UserName,Currency_Name) values ("
        '    S = S & "N'" & txtSeries.Text & "',N'" & Cls.C_date(dtpDateMovement.Text) & "',N'" & cmbTypeMovement.Text & "',N'" & cmbPaymentMethod.Text & "',"
        '    S = S & "N'" & cmbAccountNumber.Text & "',N'" & cmbAccountNumber2.Text & "',N'" & txtDocumentNumber.Text & "',N'" & txtPreviousBalance.Text & "',"
        '    S = S & "N'" & txtDebtor.Text & "',N'" & txtCreditor.Text & "',N'" & txtAmountConversion.Text & "',N'" & txtCurrentBalance.Text & "',"
        '    S = S & "N'" & Cls.C_date(dtpDateCollection.Text) & "',N'" & cmbBank.Text & "',N'" & txtNotes.Text & "',N'" & UserName & "',N'" & cmbCurrency.Text & "')"
        '    cmd.CommandText = S : cmd.ExecuteNonQuery()
        'End If


        Dim TypeTransfer As String
        If cmbTypeMovement.Text = "تحويل" Then
            TypeTransfer = 1
        Else
            TypeTransfer = 0
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Banks(BNKID,DateMovement,TypeMovement,PaymentMethod,AccountNumber,AccountNumberTo,DocumentNumber,PreviousBalance,Debtor,Creditor,Transfer,CurrentBalance,DateCollection,Bank,Notes,UserName,Currency_Name,TypeTransfer) values ("
        S = S & "N'" & txtSeries.Text & "',N'" & Cls.C_date(dtpDateMovement.Text) & "',N'" & cmbTypeMovement.Text & "',N'" & cmbPaymentMethod.Text & "',"
        S = S & "N'" & cmbAccountNumber.Text & "',N'" & cmbAccountNumber2.Text & "',N'" & txtDocumentNumber.Text & "',N'" & txtPreviousBalance.Text & "',"
        S = S & "N'" & txtDebtor.Text & "',N'" & txtCreditor.Text & "',N'" & txtAmountConversion.Text & "',N'" & txtCurrentBalance.Text & "',"
        S = S & "N'" & Cls.C_date(dtpDateCollection.Text) & "',N'" & cmbBank.Text & "',N'" & txtNotes.Text & "',N'" & UserName & "',N'" & cmbCurrency.Text & "',N'" & TypeTransfer & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        '===============================================================================
        Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
        If UseAccounts = "YES" Then
            Daily_Restrictions()
        End If
        '===============================================================================

        If Action = "Edit" Then
            Action = "Add"
            Me.Close()
            frmShowBanks.btnShow.PerformClick()
        Else
            MsgBox("تمت عملية الحفظ بنجاح", MsgBoxStyle.Information)
            ClearSave()
            MAXRECORD()
            FillComboBox()
        End If
    End Sub

    Function ValidateTextAdd() As Boolean
        If cmbTypeMovement.Text = "صرف" Or cmbTypeMovement.Text = "ايداع" Or cmbTypeMovement.Text = "تحويل" Then
        Else
            If cmbTypeMovement.Text = "" Then
                MsgBox("فضلا أدخل نوع الحركة", MsgBoxStyle.Exclamation) : cmbTypeMovement.Focus()
                Exit Function
            End If
            MsgBox("فضلا أدخل نوع الحركة", MsgBoxStyle.Exclamation) : cmbTypeMovement.Focus() : Return False
        End If
        If cmbPaymentMethod.Text = "" Then MsgBox("فضلا ادخل طريقة الدفع", MsgBoxStyle.Exclamation) : cmbPaymentMethod.Focus() : Return False
        If cmbAccountNumber.Text = "" Then MsgBox("فضلا ادخل رقم الحساب", MsgBoxStyle.Exclamation) : cmbAccountNumber.Focus() : Return False
        If txtDocumentNumber.Text = "" Then MsgBox("فضلا ادخل رقم المستند", MsgBoxStyle.Exclamation) : txtDocumentNumber.Focus() : Return False
        If cmbBank.Text = "" Then MsgBox("فضلا ادخل أسم البنك", MsgBoxStyle.Exclamation) : cmbBank.Focus() : Return False
        If cmbCurrency.Text = "" Then MsgBox("فضلا ادخل نوع العملة", MsgBoxStyle.Exclamation) : cmbCurrency.Focus() : Return False
        Return True
    End Function

    Private Sub ClearSave()
        Cls.clear(Me)
        dtpDateMovement.Focus()
    End Sub


    Private Sub SearchBalanceBanks()
        'On Error Resume Next

        If cmbCurrency.Text = "" Then MsgBox("فضلا أختر العملة", MsgBoxStyle.Exclamation) : cmbCurrency.Focus() : Exit Sub

        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandType = CommandType.Text
        'cmd.CommandText = "SELECT AccountNumber as mb FROM Banks where AccountNumber =N'" & cmbAccountNumber.Text & "' and Currency_Name =N'" & cmbCurrency.Text & "'"
        'dr = cmd.ExecuteReader
        'dr.Read()
        'If dr.HasRows = True Then
        '    Dim sh As Long
        '    sh = dr("mb")
        'End If
        'If sh <> Val(cmbAccountNumber.Text) Then
        '    txtPreviousBalance.Text = ""
        '    txtDebtor.Text = ""
        '    txtCreditor.Text = ""
        '    Exit Sub
        'End If

        Dim aray_BNKID As New ArrayList
        Dim aray_AccountNumberTo As New ArrayList

        aray_BNKID.Clear()
        aray_AccountNumberTo.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select BNKID,AccountNumberTo from BANKS where AccountNumber =N'" & cmbAccountNumber.Text & "' and Currency_Name =N'" & cmbCurrency.Text & "'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_BNKID.Add(dr("BNKID").ToString())
            aray_AccountNumberTo.Add(dr("AccountNumberTo").ToString())
        Loop

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select BNKID,AccountNumberTo from BANKS where AccountNumberTo =N'" & cmbAccountNumber.Text & "' and Currency_Name =N'" & cmbCurrency.Text & "'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_BNKID.Add(dr("BNKID").ToString())
            aray_AccountNumberTo.Add(dr("AccountNumberTo").ToString())
        Loop


        Dim SM, SM1 As Double
        For i As Integer = 0 To aray_BNKID.Count - 1

            Dim BNKID As Integer = aray_BNKID(i)
            Dim AccountNumberTo As Integer = Val(aray_AccountNumberTo(i).ToString())

            Dim TransferTO, Transfer, Creditor, Debtor As Double
            Dim TypeTransfer As Integer

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Debtor from BANKS where BNKID =N'" & BNKID & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Debtor = 0 Else Debtor = dr(0)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Creditor from BANKS where BNKID =N'" & BNKID & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Creditor = 0 Else Creditor = dr(0)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Transfer from BANKS where BNKID =N'" & BNKID & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Transfer = 0 Else Transfer = dr(0)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(Transfer) from BANKS where BNKID =N'" & BNKID & "' and AccountNumberTo =N'" & AccountNumberTo & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then TransferTO = 0 Else TransferTO = dr(0)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TypeTransfer from BANKS where BNKID =N'" & BNKID & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then TypeTransfer = 0 Else TypeTransfer = dr(0)


            If TypeTransfer = 0 Then
                If AccountNumberTo = 0 Then
                    SM1 = Creditor - Debtor + Transfer
                    SM += SM1
                Else
                    SM1 = Creditor - Debtor - Transfer
                    SM += SM1
                End If
            Else
                If Creditor > Transfer Then
                    SM1 = Creditor - Debtor - Transfer
                    SM += SM1
                Else
                    SM1 = Creditor - Debtor + Transfer
                    SM += SM1
                End If
            End If
        Next

        txtPreviousBalance.Text = SM

        'Dim con As New SqlClient.SqlConnection(constring)
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'Dim strsq1 As String = "SELECT Sum(BANKS.Debtor) AS SumDEBIT,Sum(BANKS.Creditor) AS SumCREDIT FROM BANKS  WHERE (BANKS.AccountNumber)=N'" & cmbAccountNumber.Text & "'AND BANKS.BNKID <'" & txtSeries.Text & "'"
        'Dim ds As New DataSet
        'Dim Adp1 As New SqlClient.SqlDataAdapter(strsq1, con)
        'ds.Clear()
        'Adp1.Fill(ds, "BANKS")
        'If ds.Tables(0).Rows.Count > 0 Then
        '    txtPreviousBalance.Text = Val(ds.Tables(0).Rows(0).Item(1)) - Val(ds.Tables(0).Rows(0).Item(0))
        'Else
        '    txtPreviousBalance.Text = "0"
        'End If
        'Adp1.Dispose()
        'con.Close()
    End Sub

    Private Sub cmbTypeMovement_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbTypeMovement.SelectedIndexChanged
        On Error Resume Next
        txtDebtor.Text = ""
        txtCreditor.Text = ""
        txtAmountConversion.Text = ""
        Select Case cmbTypeMovement.Text
            Case "ايداع"
                txtCreditor.Enabled = True
                txtDebtor.Enabled = False
                txtAmountConversion.Enabled = False
                lblACCOUNTNUMBER2.Visible = False
                lblACCOUNTNUMBER.Text = "رقم الحساب"
                cmbAccountNumber2.Visible = False
            Case "صرف"
                txtCreditor.Enabled = False
                txtDebtor.Enabled = True
                txtAmountConversion.Enabled = False
                lblACCOUNTNUMBER2.Visible = False
                lblACCOUNTNUMBER.Text = "رقم الحساب"
                cmbAccountNumber2.Visible = False
            Case "تحويل"
                txtAmountConversion.Enabled = True
                txtCreditor.Enabled = False
                txtDebtor.Enabled = False
                lblACCOUNTNUMBER2.Visible = True
                lblACCOUNTNUMBER.Text = "من حساب"
                cmbAccountNumber2.Visible = True
        End Select
    End Sub

    Private Sub cmbAccountNumber_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbAccountNumber.SelectedIndexChanged
        SearchBalanceBanks()
    End Sub

    Private Sub cmbCurrency_DropDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbCurrency.DropDown
        cmbAccountNumber.Text = ""
    End Sub

    Private Sub txtAmountConversion_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtAmountConversion.TextChanged
        MyVars.CheckNumber(txtAmountConversion)
    End Sub

    Private Sub cmbAccountNumber2_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbAccountNumber2.SelectedIndexChanged

        If cmbAccountNumber.Text = cmbAccountNumber2.Text Then MsgBox("فضلا ادخل رقم الحساب متطابق", MsgBoxStyle.Exclamation)
    End Sub

    Private Sub Daily_Restrictions()
        Dim Creditor As String = ""
        Dim CreditorCode As String = ""

        Dim Debtor As String = ""
        Dim DebtorCode As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'ايداع بنكى'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            Creditor = dr("Link_AccountsTree")
            CreditorCode = dr("ACCNumber")
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'صرف بنكى'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            Debtor = dr("Link_AccountsTree")
            DebtorCode = dr("ACCNumber")
        End If

        '========================================================================================

        If cmbTypeMovement.Text = "ايداع" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MOVESDATA (MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
            S = S & "N'0',N'" & Cls.C_date(dtpDateMovement.Text) & "',N'" & CreditorCode & "',N'" & Creditor & "',N'0',N'" & txtCreditor.Text & "',N'" & Creditor & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If


        If cmbTypeMovement.Text = "صرف" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MOVESDATA (MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
            S = S & "N'0',N'" & Cls.C_date(dtpDateMovement.Text) & "',N'" & DebtorCode & "',N'" & Debtor & "',N'" & txtDebtor.Text & "',N'0',N'" & Debtor & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If


    End Sub

    Private Sub txtSeries_TextChanged(sender As Object, e As EventArgs) Handles txtSeries.TextChanged
        MyVars.CheckNumber(txtSeries)
    End Sub
End Class