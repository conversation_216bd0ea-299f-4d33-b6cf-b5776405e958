﻿Public Class Frm_IM_Outcome_Cat

    Sub FilList()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "Select G_name from Cat_Expenses"
        dr = cmd.ExecuteReader
        ListBox1.Items.Clear()
        Do While dr.Read

            ListBox1.Items.Add(dr("G_name"))

        Loop
    End Sub

    Private Sub btnsave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnsave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If txtcats.Text.Trim = "" Then MsgBox("من فضلا أدخل اسم البند", MsgBoxStyle.Exclamation) : txtcats.Focus() : Exit Sub

        REM للتاكد من عدم التكرار
        Dim TableName, FieldName, StringFind As String
        Dim S As String
        TableName = "Cat_Expenses"
        FieldName = "g_name"
        StringFind = txtcats.Text
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select * From " & TableName & " Where " & FieldName & "=N'" & StringFind & "'"

        cmd.CommandType = CommandType.Text
        cmd.CommandText = S
        dr = cmd.ExecuteReader

        If dr.HasRows = True Then
            MsgBox("تم اضافه هذه البنده مسبقا", MsgBoxStyle.Exclamation)
            txtcats.Focus()
            dr.Close()
            Exit Sub
        End If
        dr.Close()
        REM لحفظ البنده
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Cat_Expenses(g_name,UserName) values (N'" & StringFind & "',N'" & UserName & "')"
        cmd.CommandText = S
        cmd.ExecuteNonQuery()

        FilList()
        txtcats.Text = ""
        Bra.Fil("Cat_Expenses", "g_name", FrmItemsNew.cmbcats)
        'Bra.Fil("Cat_Expenses", "g_name", Frmimport.cmbcats)

    End Sub

    Private Sub txtcats_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtcats.KeyUp
        If e.KeyCode = 13 Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub FrmCats_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Me.MdiParent = MDIParent1
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        FilList()
    End Sub

    Private Sub btndelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btndelete.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ListBox1.Text.Trim = "" Then Beep() : Exit Sub
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete from Cat_Expenses where g_name =N'" & ListBox1.Text & "'"
        cmd.ExecuteNonQuery()
        FilList()
        Bra.Fil("Cat_Expenses", "g_name", FrmItemsNew.cmbcats)

    End Sub

    Private Sub txtsearch_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtsearch.TextChanged
        If txtsearch.Text.Trim = "" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select G_name from Cat_Expenses"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                ListBox1.Items.Add(dr("G_name"))
            Loop
        End If

        Dim X As String = txtsearch.Text & "%"
        ListBox1.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select G_name from Cat_Expenses where g_name like N'" & X & "'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            ListBox1.Items.Add(dr("G_name"))
        Loop
    End Sub
End Class
