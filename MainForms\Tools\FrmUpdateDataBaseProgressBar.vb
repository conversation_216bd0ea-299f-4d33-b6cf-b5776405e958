﻿Imports System.Data.SqlClient

Public Class FrmUpdateDataBaseProgressBar

    Private Sub Form2_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        BackgroundWorker1.RunWorkerAsync()
        Control.CheckForIllegalCrossThreadCalls = False
    End Sub

    Public Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork
        'For i = 0 To 100
        '    TextBox1.Text = i
        '    BackgroundWorker1.ReportProgress(i)
        '    Threading.Thread.Sleep(300)
        'Next


        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select count(*) from Items" : H = cmd.ExecuteScalar
        'If H = 0 Then
        '    MsgBox("الصنف غير مطابق من فضلك راجع الصنف ", MsgBoxStyle.Exclamation)
        'End If

        ValueProgressBar = ValueTextProgressBar
        ValueNumberProgressBar = CountLoopProgressBar
        CountLoopProgressBar += 1
        If CountLoopProgressBar = 100 Then
            CountLoopProgressBar = 0
        End If
        BackgroundWorker1.ReportProgress(CountLoopProgressBar)
        Threading.Thread.Sleep(300)


        'Me.Close()
    End Sub

    Private Sub BackgroundWorker1_ProgressChanged(sender As Object, e As System.ComponentModel.ProgressChangedEventArgs) Handles BackgroundWorker1.ProgressChanged
        ProgressBar1.Value = e.ProgressPercentage
    End Sub

    Public Shared Sub ProgressBarBackgroundWorker(ByVal CountLoop As Long, ByVal ValueNumber As String, ByVal lblValueText As Label, ByVal ValueText As String)
        For i = 0 To 100
            lblValueText.Text = ValueText
            ValueNumber = CountLoop
            CountLoop += 1
            If CountLoop = 100 Then
                CountLoop = 0
            End If
            FrmUpdateDataBaseProgressBar.BackgroundWorker1.ReportProgress(CountLoop)
            Threading.Thread.Sleep(300)
        Next
    End Sub

End Class