﻿Imports System.Windows.Forms
Imports System.Data
Imports System.Data.SqlClient
Imports vb = Microsoft.VisualBasic
Imports CrystalDecisions.CrystalReports.Engine
Imports System.IO
Public Class Frm_IM_BTin
    Dim WithEvents BS As New BindingSource

    Dim R() As String
    Dim Mnm As String
    Dim _totalItem As Double
    Dim TotalPriceBeforeAverage As Double
    Dim DiscountsValue As Double = 0
    Dim TotalDiscountsValue As Double = 0
    Dim StateDisc As String = ""
    Dim DiscTotal As Double
    Dim VendorsAddress As String = ""
    Dim VendorsTel As String = ""
    Dim Resp As String = ""
    Dim Rtel As String = ""

    Dim Amntcredit, Amntdebit, AmntcreditPrevious, AmntdebitPrevious, TotalVendoreBalancePrevious, XCreditDebit As Double
    Dim TotalAccountAfterInvoice As String
    Dim Cls_Altfiqith As New Class_Altfiqith

    Private Sub sales_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cls.fill_combo("vendors", "Vendorname", cmbvendores)
        Cls.FillComboDataAdapterQuickSearch("Items", "sname", cmbname)
        ClearSave()

        Bra.Fil("groups", "g_name", cmbcats)
        Cls.fill_combo_Branch("stores", "store", cmbStores)
        MAXRECORD()
        cmbStores.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        SearchForProduct()
        PaneldiscBill.Top = 10000
        PanelSearch.Top = 5000
        GetDateNotBeenActivatedPrograms(DateTimePicker1)
        GetDateNotBeenActivatedOutcome()
        GetHideItemGroupSearchForProduct()
    End Sub

    Private Sub GetHideItemGroupSearchForProduct()
        Dim HideItemGroupSearchForProduct As String = mykey.GetValue("HideItemGroupSearchForProduct", "NO")
        If HideItemGroupSearchForProduct = "NO" Then
            cmbcats.Visible = False
            Labelcats.Visible = False
        Else
            cmbcats.Visible = True
            Labelcats.Visible = True
        End If
    End Sub

    Private Sub GetDateNotBeenActivatedOutcome()
        If PermtionName <> "مدير" Then
            If DateNotBeenActivatedOutcome = "NO" Then
                DateTimePicker1.Enabled = True
            Else
                DateTimePicker1.Enabled = False
            End If
        End If
    End Sub

    Private Sub cmbvendores_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbvendores.KeyUp
        If e.KeyCode = 13 Then
            txtbillno.Focus()
        End If
    End Sub
    Private Sub txtbillno_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtbillno.KeyUp
        If e.KeyCode = 13 Then
            DateTimePicker1.Focus()
        End If
    End Sub
    Private Sub DateTimePicker1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles DateTimePicker1.KeyUp
        If e.KeyCode = 13 Then
            txtprc.Focus()
        End If
    End Sub
    Private Sub cmbcats_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            If cmbcats.Text.Trim = "" Then
                txtdisc.Focus()
            Else
                cmbname.Focus()
            End If
        End If
    End Sub

    Private Sub txtprice_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtprice.KeyUp
        If e.KeyCode = 13 Then
            txtquntUnity.Focus()
        End If
    End Sub

    Private Sub txtprice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtprice.TextChanged
        MyVars.CheckNumber(txtprice)
        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If
        Dim XTotal As Double = Val(txtprice.Text) * Val(txtquntUnity.Text)
        TotalValueVAT = Format(Val(XTotal) * Val(ItemsRateVAT) / 100, "Fixed")
        txtTotalTotal.Text = Math.Round(TotalValueVAT, 2) + Math.Round(XTotal, 2)
        txtTotalTotal.Text = Math.Round(Convert.ToDouble(txtTotalTotal.Text), 2)
    End Sub
    Private Sub txtqunt_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtqunt.KeyUp

        If e.KeyCode = 13 Then
            txtprice.Focus()
        End If
    End Sub

    Private Sub txtqunt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtqunt.TextChanged
        MyVars.CheckNumber(txtqunt)
        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If

        Dim XTotal As Double = Val(txtprice.Text) * Val(txtquntUnity.Text)
        TotalValueVAT = Format(Val(XTotal) * Val(ItemsRateVAT) / 100, "Fixed")
        txtTotalTotal.Text = Math.Round(TotalValueVAT, 2) + Math.Round(XTotal, 2)
        txtTotalTotal.Text = Math.Round(Convert.ToDouble(txtTotalTotal.Text), 2)
    End Sub

    Function ValidateTextAdd() As Boolean
        If cmbvendores.Text = "" Then MsgBox("فضلا أختر المورد", MsgBoxStyle.Exclamation) : cmbvendores.Focus() : Return False
        If txtprc.Text = "" Then MsgBox("فضلا ادخل الباركود", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
        If txtbillno.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الفاتورة", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        If cmbcats.Text = "" Then MsgBox("فضلا حدد المجموعة", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbname.Text = "" Then MsgBox("فضلا أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbname.Focus() : Return False
        If cmbUnityItems.Text = "" Then MsgBox("فضلا أدخل وحدة القياس", MsgBoxStyle.Exclamation) : cmbUnityItems.Focus() : Return False
        'If Val(txtprice.Text.Trim) = 0  Then MsgBox("فضلا أدخل السعر", MsgBoxStyle.Exclamation) : txtprice.Focus() : Return False
        If txtprice.Text.Trim = "" Then MsgBox("فضلا أدخل السعر", MsgBoxStyle.Exclamation) : txtprice.Focus() : Return False
        If Val(txtqunt.Text.Trim) = 0 Then MsgBox("فضلا أدخل الكمية ", MsgBoxStyle.Exclamation) : txtqunt.Focus() : Return False
        If cmbStores.Text = "" Then MsgBox("فضلا أدخل اسم المخزن", MsgBoxStyle.Exclamation) : cmbStores.Focus() : Return False

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where itm_id =N'" & txtprc.Text.Trim & "'and group_name =N'" & cmbcats.Text.Trim & "'and sname =N'" & cmbname.Text.Trim & "'and Stores =N'" & cmbStores.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H = 0 Then
                MsgBox("الصنف غير مطابق من فضلك راجع الصنف ", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
            End If

            Dim ReturnInvoice As String = mykey.GetValue("ReturnInvoice", "NO")
            If ReturnInvoice = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select count(*) from purchase_bill where bill_no =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
                If H = 0 Then
                    MsgBox("رقم الفاتورة غير مسجل مسبقا بنفس فاتورة المشتريات", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
                ElseIf H > 0 Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select count(*) from purchase_bill where bill_no =N'" & txtbillno.Text.Trim & "' and Vendorname =N'" & cmbvendores.Text.Trim & "'" : H = cmd.ExecuteScalar
                    If H = 0 Then
                        MsgBox("رقم الفاتورة غير مسجل باسم هذا المورد", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
                    End If
                End If
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select count(*) from BilltINData where bill_no =N'" & txtbillno.Text.Trim & "' and itm_id =N'" & txtprc.Text.Trim & "'" : H = cmd.ExecuteScalar
                If H = 0 Then
                    MsgBox("هذا الصنف غير مسجل بهذه الفاتورة ", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
                End If
            End If

            For i As Integer = 0 To Dt_AddBill.Rows.Count - 1
                If Dgv_Add.Rows(i).Cells(0).Value = txtprc.Text.Trim Then MsgBox("صنف مكرر بنفس الفاتورة", MsgBoxStyle.Exclamation) : txtprc.Focus() : txtprc.SelectAll() : Return False
            Next

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from IM_Btin where bill_no =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
            End If

            If AllowCustOrSupplierReturnItemNotMovement = "NO" Then
                If cmbvendores.Text <> "نقداً" Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select itm_id from View_BilltINData where Vendorname=N'" & cmbvendores.Text & "' and itm_id=N'" & txtprc.Text & "'"
                    dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = False Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "select itm_id from View_BilltINData where ExVendorname=N'" & cmbvendores.Text & "' and itm_id=N'" & txtprc.Text & "'"
                        dr = cmd.ExecuteReader : dr.Read()
                        If dr.HasRows = False Then
                            MsgBox("هذا المورد لم يتم شراء منه هذا الصنف الذى تم اختيارة", MsgBoxStyle.Exclamation)
                            Return False
                        End If
                    End If
                End If
            End If

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        Return True
    End Function
    Function ValidateTextSave() As Boolean
        Try
            If cmbvendores.Text = "" Then MsgBox("فضلا أختر المورد", MsgBoxStyle.Exclamation) : cmbvendores.Focus() : Return False
            If txtbillno.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الفاتورة", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
            If txtdisc.Text = "" Then
                txtdisc.Text = "0"
            End If
            If txtpaying.Text = "" Then
                txtpaying.Text = "0"
            End If
            If Dgv_Add.Rows.Count < 1 Then MsgBox("فضلا أكمل بيانات الفاتورة", MsgBoxStyle.Exclamation) : cmbvendores.Focus() : Return False
            Return True
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Function
    Friend Sub DTV_Width()
        Try
            If Dgv_Add.Rows.Count > 0 Then
                Dgv_Add.Columns(0).Width = 70
                Dgv_Add.Columns(1).Width = 90
                Dgv_Add.Columns(2).Width = 130
                Dgv_Add.Columns(3).Width = 60
                Dgv_Add.Columns(4).Width = 60
                Dgv_Add.Columns(5).Width = 75
                Dgv_Add.Columns(6).Width = 70
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
    Private Sub GetDataByPrc()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select group_name , sname ,tinPrice from items where itm_id=N'" & txtprc.Text & "' order by 1"
            Else
                cmd.CommandText = "select group_name , sname ,tinPrice from items where itm_id=N'" & txtprc.Text & "' and Company_Branch_ID =N'" & Company_Branch_ID & "' order by 1"
            End If
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Dim xcmbcats, xcmbname As String
                xcmbcats = dr(0)
                xcmbname = dr(1)
                If dr(2) Is DBNull.Value Then
                    txtprice.Text = 0
                    cmbcats.Text = xcmbcats
                    cmbname.Text = xcmbname
                    GoTo 1
                End If
                txtprice.Text = dr(2)
                cmbcats.Text = xcmbcats
                cmbname.Text = xcmbname
            End If
1:
            txtStoreTotal.Text = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        txtprice.SelectAll()
        txtqunt.Text = 1 : txtqunt.SelectAll() : txtprice.SelectAll() : txtprice.Focus()
    End Sub
    Private Sub BtnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAdd.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateTextAdd() = False Then Exit Sub
        'IM.Store(txtprc.Text.Trim, cmbStores.Text)

        If ConnectOnlineStore = "YES" Then
            EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", txtprc.Text)
            StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", txtprc.Text)
            Cos.UpdateProductStock(StockOnline, txtprc.Text, EditItmId)
        End If

        Try
            Cls.Get_Value_Count_More("items", "itm_id =N'" & txtprc.Text.Trim & "' and group_name =N'" & cmbcats.Text.Trim & "' and sname =N'" & cmbname.Text.Trim & "'")
            If H = 0 Then
                GetDataByPrc()
            End If

            If cmbcats.Text = "" Or cmbname.Text = "" Or txtprice.Text = "" Or txtquntUnity.Text = "" Then
                MsgBox("اكمل البيانات")
                Exit Sub
            End If


            Dim UnitySize As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select UnitySize_Name from View_ItemsUnitySize where itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                UnitySize = dr(0).ToString
            End If

            Dgv_Add.DataSource = Fn_AddBill(txtprc.Text, cmbcats.Text, cmbname.Text, Val(txtprice.Text), Val(txtqunt.Text), Val(txtquntUnity.Text), cmbUnityItems.Text, Val(txtTotalTotal.Text), cmbStores.Text, UnitySize, TotalValueVAT, TotalBeforeVAT, ItemsRateVAT, Val(txtdiscBill.Text), DiscountsValue, lblDiscount_Price_After.Text)
            ClearAdd() : SumAllPrice() : FocusText()
            Dgv_Add.Columns(1).Visible = False
            Dgv_Add.Columns(4).Visible = False
            Dgv_Add.Columns(9).Visible = False
            Dgv_Add.Columns(10).Visible = False
            Dgv_Add.Columns(11).Visible = False
            Dgv_Add.Columns(12).Visible = False
            Dgv_Add.Columns(13).Visible = False
            Dgv_Add.Columns(14).Visible = False
            Dgv_Add.Columns(15).Visible = False


            Dgv_Add.Columns(0).ReadOnly = True
            Dgv_Add.Columns(2).ReadOnly = True
            Dgv_Add.Columns(3).ReadOnly = False
            Dgv_Add.Columns(5).ReadOnly = True
            Dgv_Add.Columns(6).ReadOnly = True
            Dgv_Add.Columns(7).ReadOnly = True
            Dgv_Add.Columns(8).ReadOnly = True

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
    Private Sub FocusText()
        Dim FocusText As String = mykey.GetValue("FocusText", "NO")
        If FocusText = "YES" Then
            txtprc.Focus()
        Else
            cmbcats.Focus()
        End If
    End Sub
    Private Sub ClearAdd()
        cmbUnityItems.Text = ""
        'cmbcats.Text = ""
        cmbname.Text = ""
        txtqunt.Text = ""
        txtprice.Text = ""
        txtprc.Text = ""
        txtTotalTotal.Text = ""
        txtStoreTotal.Text = ""
        txtdiscBill.Text = ""
    End Sub
    Dim Dt_AddBill As New DataTable
    Friend Function Fn_AddBill(ByVal Col_Prc As String, ByVal Col_Cats As String, ByVal Col_Name As String _
, ByVal Col_Price As Double, ByVal Col_Quant As Double, ByVal Col_qu_unity As Double, ByVal Col_Unity As String, ByVal Col_Total As Double, ByVal Col_Stores As String, ByVal Col_Unity_Name As String, ByVal Col_ValueVAT As Double, ByVal Col_TotalBeforeVAT As Double, ByVal Col_ItemsRateVAT As Double, ByVal Col_Dsic As Double, ByVal Col_DiscountsValue As Double, ByVal Col_UnitPriceAfterDisc As Double) As DataTable
        Try
            If Dt_AddBill.Columns.Count = 0 Then
                Dt_AddBill.Columns.Add("الباركود", GetType(String))
                Dt_AddBill.Columns.Add("المجموعة", GetType(String))
                Dt_AddBill.Columns.Add("الصنف", GetType(String))
                Dt_AddBill.Columns.Add("سعر الشراء", GetType(Double))
                Dt_AddBill.Columns.Add("الكمية1", GetType(Double))
                Dt_AddBill.Columns.Add("الكمية", GetType(Double))
                Dt_AddBill.Columns.Add("الوحدة", GetType(String))
                Dt_AddBill.Columns.Add("الإجمالي", GetType(Double))
                Dt_AddBill.Columns.Add("المخزن", GetType(String))
                Dt_AddBill.Columns.Add("حجم الوحدة", GetType(String))
                Dt_AddBill.Columns.Add("ValueVAT", GetType(Double))
                Dt_AddBill.Columns.Add("TotalBeforeVAT", GetType(Double))
                Dt_AddBill.Columns.Add("ItemsRateVAT", GetType(Double))
                Dt_AddBill.Columns.Add("الخصم", GetType(Double))
                Dt_AddBill.Columns.Add("قيمة الخصم", GetType(Double))
                Dt_AddBill.Columns.Add("سعر الشراء بعد الخصم", GetType(Double))
            End If

            DTV_Width()
            Dt_AddBill.Rows.Add(Col_Prc, Col_Cats, Col_Name, Col_Price, Col_Quant, Col_qu_unity, Col_Unity, Col_Total, Col_Stores, Col_Unity_Name, Col_ValueVAT, Col_TotalBeforeVAT, Col_ItemsRateVAT, Col_Dsic, Col_DiscountsValue, Col_UnitPriceAfterDisc)
            Return Dt_AddBill
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Function

    Private Sub Dgv_Add_CellContentClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles Dgv_Add.CellContentClick
        On Error Resume Next
        If e.ColumnIndex = 0 Then
            txtprc.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Prc").Value.ToString
            cmbcats.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Cats").Value.ToString
            cmbname.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Name").Value.ToString
            txtprice.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Price").Value
            txtqunt.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Quant").Value
            Dgv_Add.Rows.RemoveAt(e.RowIndex)
        End If
        SumAllPrice()
    End Sub

    Private Sub BtnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnSave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        SumAllPrice()
        If ValidateTextSave() = False Then Exit Sub

        Dim x As String = MsgBox("هل تريد بالفعل إتمام عملية مرتجعات المشتريات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        Dim STAT As String
        If ChkState.Checked = True Then
            STAT = "نقداً"
        Else
            STAT = "آجل"
        End If

        If ChkCent.Checked = True Then
            DiscTotal = Val(txttotalpeforedisc.Text) - Val(txttotalafterdisc.Text)
            DiscTotal = Math.Round(DiscTotal, 1)
        Else
            DiscTotal = txtdisc.Text
        End If

        'Try
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into IM_Btin(Company_Branch_ID,bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,UserName,ValueVAT,Treasury_Code,DiscountsValue) values ("
            S = S & "N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "' ,N'" & cmbvendores.Text.Trim & "' ,N'" & Cls.C_date(DateTimePicker1.Text) & "' ,N'" & Cls.get_time(True) & "' ," & Val(txttotalpeforedisc.Text.Trim) & "," & Val(DiscTotal) & " ," & Val(txttotalafterdisc.Text.Trim) & " ,N'" & STAT & "',N'" & txtpaying.Text.Trim & "',N'" & txtstaying.Text.Trim & "',N'" & UserName & "',N'" & txtTotalValueVAT.Text & "',N'" & Treasury_Code & "',N'" & TotalDiscountsValue & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()


            Dim bill_EndDate As String = "" : Dim bill_no_Expired As String = ""
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                PriceTinAverage(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value, Dgv_Add.Rows(i).Cells(6).Value, Dgv_Add.Rows(i).Cells(5).Value, Dgv_Add.Rows(i).Cells(7).Value)

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select Top(100) PERCENT MIN(bill_no_Expired) As Exbill_no_Expired, bill_EndDate, qu_expired, itm_id, Stores From dbo.BilltINData Group By itm_id, Stores, bill_EndDate, qu_expired HAVING(itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "') AND (Stores = N'" & Dgv_Add.Rows(i).Cells(8).Value & "') AND (qu_expired <> 0)  ORDER BY MIN(bill_no_Expired)" : dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    bill_no_Expired = dr(0).ToString
                    bill_EndDate = dr(1).ToString
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into IM_Btin_Data (Company_Branch_ID,bill_no,itm_id,itm_cat,itm_name,price,qu,qu_unity,itm_Unity,totalprice,Stores,username , bill_date,TinPriceAverage,ValueVAT,RateVAT,BeforeVAT,Treasury_Code,bill_EndDate,bill_no_Expired,Price_Unity,Discounts,DiscountsValue,Discount_Price_After)  values (N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Dgv_Add.Rows(i).Cells(3).Value & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "',N'" & UserName & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Val(TotalPriceBeforeAverage) & "',N'" & Dgv_Add.Rows(i).Cells(10).Value & "',N'" & Dgv_Add.Rows(i).Cells(11).Value & "',N'" & Dgv_Add.Rows(i).Cells(12).Value & "',N'" & Treasury_Code & "',N'" & bill_EndDate & "',N'" & bill_no_Expired & "',N'" & Price_Unity & "',N'" & Dgv_Add.Rows(i).Cells(13).Value & "',N'" & Dgv_Add.Rows(i).Cells(14).Value & "',N'" & Dgv_Add.Rows(i).Cells(15).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

                IM.StoreExpired(Dgv_Add.Rows(i).Cells(0).Value.ToString(), Dgv_Add.Rows(i).Cells(8).Value.ToString(), bill_EndDate.ToString(), bill_no_Expired)

            Next

            If Val(txtpaying.Text) > 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into IM_Vnd (Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,billno,VND_no,UserName,Treasury_Code) values"
                S = S & " (N'" & Company_Branch_ID & "',N'" & cmbvendores.Text & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(DateTimePicker1.Text) & "'," & txtpaying.Text.Trim & ",N'بفاتورة',N'بفاتورة',N'بفاتورة',N'" & txtbillno.Text & "',N'دفعة نقدية',N'" & UserName & "',N'" & Treasury_Code & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If

            If Val(txtdisc.Text) > 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into IM_vndr_disc (Company_Branch_ID,Vendorname,amnt,pdate,det,TIN_NO,UserName,Treasury_Code) values (N'" & Company_Branch_ID & "',N'" & cmbvendores.Text.Trim & "'," & Val(DiscTotal) & ",N'" & Cls.C_date(DateTimePicker1.Text) & "',N'خصم على فاتورة مباشرة',N'" & txtbillno.Text.Trim & "',N'" & UserName & "',N'" & Treasury_Code & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try

        ImageUpdateBill()

        'UpdatePriceItems()

        '===============================================================================
        Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
        If UseAccounts = "YES" Then
            Daily_Restrictions()
        End If
        '===============================================================================

        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            IM.Store(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Btin_Data set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbillno.Text & "' and itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "' and Stores =N'" & Dgv_Add.Rows(i).Cells(8).Value & "'" : cmd.ExecuteNonQuery()
        Next
        IM.VendorAccountTotal(cmbvendores.Text.Trim)

        Get_Movement_In_Out_Money(DateTimePicker1.Text, Treasury_Code)

        GetDebtorlCreditorPrevious()

        GetDebtorlCreditor()

        Amntcredit = Amntcredit - Amntdebit
        AmntcreditPrevious = AmntcreditPrevious - AmntdebitPrevious

        Try
            Dim CurrentBalanceCustVnd As String = GetCurrentBalanceCustVnd(cmbvendores.Text)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Btin set CreditPrevious = " & Val(AmntcreditPrevious) & ",DebitPrevious = " & Val(AmntdebitPrevious) & ",CreditCurrent = " & Val(Amntcredit) & ",DebitCurrent = " & Val(Amntdebit) & ",CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where bill_No =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
            If Val(txtpaying.Text) > 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update IM_Vnd set CreditPrevious = " & Val(AmntcreditPrevious) & ",DebitPrevious = " & Val(AmntdebitPrevious) & ",CreditCurrent = " & Val(Amntcredit) & ",DebitCurrent = " & Val(Amntdebit) & ",CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where BillNo =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
            End If
            If Val(txtdisc.Text) > 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update IM_vndr_disc set CreditPrevious = " & Val(AmntcreditPrevious) & ",DebitPrevious = " & Val(AmntdebitPrevious) & ",CreditCurrent = " & Val(Amntcredit) & ",DebitCurrent = " & Val(Amntdebit) & ",CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where TIN_NO =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        MsgBox("تمت عملية مرتجع المشتريات بنجاح", MsgBoxStyle.Information) : txtprc.Focus()
        If chkprint.Checked = True Then
            PrintReport()
        End If
        ClearSave()
        MAXRECORD()
        Dt_AddBill.Rows.Clear()
    End Sub

    Private Sub UpdatePriceItems()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                If Dgv_Add.Rows(i).Cells(9).Value = "الاصغر" Then
                    cmd.CommandText = "update items set tinprice = " & Val(Dgv_Add.Rows(i).Cells(3).Value) & " where itm_id =N'" & Dgv_Add.Rows(i).Cells(0).Value & "'" : cmd.ExecuteNonQuery()
                End If
            Next
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub PrintReport()
        GetVendorsAddress()
        AddReportView()
        Try

            Cls.GetDefaultPrinterA4()

            If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

            Cls.delete_Branch_All("PrintSalesPurchases")

            Dim BillSerialNumber As Double = 0
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                BillSerialNumber += 1
                S = "insert into PrintSalesPurchases(Company_Branch_ID,itm_id,itm_cat,itm_name,price,qu,Unity,totalprice,store,BILL_NO,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,BEY,STAYING,Totalreturns,Delivery_Date,Recipient,vnamntcredit,vnamntdebit,TotalCreditor,TotalDebtor,NumberInt1,TotalBeforeDisc,Name10)  values("
                S = S & "N'" & Company_Branch_ID & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Dgv_Add.Rows(i).Cells(3).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "',N'" & txtbillno.Text & "',N'" & cmbvendores.Text & "',N'" & DateTimePicker1.Text & "',N'" & Cls.Get_Time_AM_PM(Cls.get_time(True)) & "',N'" & txttotalpeforedisc.Text & "',N'" & txtdisc.Text & "',N'" & txttotalafterdisc.Text & "',N'" & txtpaying.Text & "',N'" & txtstaying.Text & "',N'" & Val(txtTotalValueVAT.Text) & "',N'" & DefaultCurrencyProgram & "',N'" & UserName & "',N'" & Amntcredit & "',N'" & Amntdebit & "',N'" & AmntcreditPrevious & "',N'" & AmntdebitPrevious & "',N'" & BillSerialNumber & "',N'" & BillSerialNumber & "',N'" & txtTotalCountItems.Text & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next

            Dim txt, txtname, txtNameAr, txtNameEn, txtCmpAddress, txtCmpEmail, txtCmpTel, txtCmpMobile, txtCmpFax, txtDelegateName, txtCustomerAddressSales, txtAltfiqith, txtCustomerTel, txtEndorsement, txtPhoneEmployee, txtProgramNameBill, txtObjectUserName, txtObjectCommercialAndIndustrialProfitsTax As TextObject

            Dim rpt
            If PrintSmall = "YES" Then
                Cls.GetDefaultPrinterBill()
                If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                    rpt = New Rpt_SoldSmall
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                    rpt = New Rpt_SoldSmall_2
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                    rpt = New Rpt_SoldSmall_3
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                    rpt = New Rpt_SoldSmall_4
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                    rpt = New Rpt_SoldSmall_5
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = UserName
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                    rpt = New Rpt_SoldSmall_6
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                    rpt = New Rpt_SoldSmall_7
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                    rpt = New Rpt_SoldSmall_8
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                    rpt = New Rpt_SoldSmall_9
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                    rpt = New Rpt_SoldSmall_10
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                    rpt = New Rpt_SoldSmall_11
                    If ShowCustomerAddressSales = "YES" Then
                        If VendorsAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = "عنوان المورد" + ": " + VendorsAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = "رقم تليفون المورد" + ": " + VendorsTel
                        End If
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = UserName
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                    rpt = New Rpt_SoldSmall_12
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                    rpt = New Rpt_SoldSmall_13
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = ""
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                    rpt = New Rpt_SoldSmall_14
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                    rpt = New Rpt_SoldSmall_15
                    txtObjectUserName = rpt.Section1.ReportObjects("UserName")
                    txtObjectUserName.Text = UserName
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                    rpt = New Rpt_SoldSmall_16
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                    rpt = New Rpt_SoldSmall_17
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                    rpt = New Rpt_SoldSmall_18
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                    rpt = New Rpt_SoldSmall_19
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                    rpt = New Rpt_SoldSmall_20
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                    rpt = New Rpt_SoldSmall_21
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                    rpt = New Rpt_SoldSmall_10
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                    rpt = New Rpt_SoldSmall_10
                End If

                If SalesInvoicePrintingLanguage = "English" Then
                    rpt = New Rpt_SoldSmall_EN
                End If
            End If

            If PrintSmall = "NO" Then
                If txtPurchaseTax.Text = 0 Then
                    If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                        rpt = New Rpt_SalesPurchases
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                        rpt = New Rpt_SalesBill_2
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                        rpt = New Rpt_SalesBill_3
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                        rpt = New Rpt_SalesBill_4
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                        rpt = New Rpt_SalesBill_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                        rpt = New Rpt_SalesBill_6
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                        rpt = New Rpt_SalesBill_7
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                        rpt = New Rpt_SalesBill_8
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                        rpt = New Rpt_SalesBill_9
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                        rpt = New Rpt_SalesBill_10
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                        rpt = New Rpt_SalesBill_11
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                        rpt = New Rpt_SalesBill_11
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                        rpt = New Rpt_SalesBill_Delegate_2
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = Resp
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = VendorsAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = VendorsTel
                        txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                        txtPhoneEmployee.Text = Rtel
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                        rpt = New Rpt_SalesBill_11
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                        rpt = New Rpt_SalesBill_15
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = ""
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                        rpt = New Rpt_SalesBill_16
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                        rpt = New Rpt_SalesBill_17
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                        rpt = New Rpt_SalesBill_18
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                        rpt = New Rpt_SalesBill_19
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                        rpt = New Rpt_SalesBill_20
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                        rpt = New Rpt_SalesBill_21
                        If CommercialAndIndustrialProfitsTax = 0 Then
                            txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                            txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                        End If
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                        rpt = New Rpt_SalesBill_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                        rpt = New Rpt_SalesBill_23
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                        rpt = New Rpt_SalesBill_24
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                        rpt = New Rpt_SalesBill_25
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                        rpt = New Rpt_SalesBill_26
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                        rpt = New Rpt_SalesBill_27
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                        rpt = New Rpt_SalesBill_28
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                        rpt = New Rpt_SalesBill_29
                    End If
                    txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
                    txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(txttotalafterdisc.Text)
                Else
                    rpt = New Rpt_SalesPurchasesTax
                End If
            End If

            If PrintSmall = "A5" Then
                If txtPurchaseTax.Text = 0 Then
                    If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                        rpt = New Rpt_SalesBill_A5_1
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                        rpt = New Rpt_SalesBill_BalanceCust_A5_2
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                        rpt = New Rpt_SalesBill_A5_3
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                        rpt = New Rpt_SalesBill_A5_4
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                        rpt = New Rpt_SalesBill_A5_6
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                        rpt = New Rpt_SalesBill_A5_7
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                        rpt = New Rpt_SalesBill_A5_9
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = ""
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                        rpt = New Rpt_SalesBill_A5_6_Delegate_2
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = Resp
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = VendorsAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = VendorsTel
                        txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                        txtPhoneEmployee.Text = Rtel
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = VendorsAddress
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                        rpt = New Rpt_SalesBill_A5_6_Delegate_2
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = CMPEndorsement
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = Resp
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = VendorsAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = VendorsTel
                        txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                        txtPhoneEmployee.Text = Rtel
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = VendorsAddress
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                        rpt = New Rpt_SalesBill_BalanceCust_A5_14
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                        rpt = New Rpt_SalesBill_A5_15
                        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                        txtEndorsement.Text = ""
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                        rpt = New Rpt_SalesBill_A5_5
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                    If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                        rpt = New Rpt_SalesBill_A5_22
                    End If
                Else
                    rpt = New Rpt_SalesBill_Tax_A5
                End If

                txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
                txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(txttotalafterdisc.Text)
            End If

            If ShowValueVAT = "YES" Then
                If PrintSmall = "YES" Then
                    rpt = New Rpt_SoldSmall_VAT
                End If
                If PrintSmall = "NO" Then
                    rpt = New Rpt_SalesBill_Cash_VAT
                End If
                If PrintSmall = "A5" Then
                    rpt = New Rpt_SalesBill_BalanceCust_A5_VAT
                End If
            End If

            Cls.Select_More_Data_Branch_Print_Orderby("PrintSalesPurchases", "*", "NumberInt1")
            Dim dt As New DataTable
            dt.Load(dr)
            rpt.SetDataSource(dt)
            txt = rpt.Section1.ReportObjects("txtTitelAddress")
            txt.Text = "تقرير مرتجعات مشتريات"
            txtname = rpt.Section1.ReportObjects("txtName")
            txtname.Text = "أسم المورد"
            txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
            txtNameAr.Text = NameArCompay
            txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
            txtNameEn.Text = NameEnCompany
            txtCmpAddress = rpt.Section1.ReportObjects("txtAddress")
            txtCmpAddress.Text = CmpAddress
            txtCmpEmail = rpt.Section1.ReportObjects("txtEmail")
            txtCmpEmail.Text = CmpEmail
            txtCmpTel = rpt.Section1.ReportObjects("txtTel")
            txtCmpTel.Text = CmpTel
            txtCmpMobile = rpt.Section1.ReportObjects("txtMobile")
            txtCmpMobile.Text = CmpMobile
            txtCmpFax = rpt.Section1.ReportObjects("txtFax")
            txtCmpFax.Text = CmpFax
            If SelectLogoPathOther = "YES" Then
                rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
            Else
                rpt.SetParameterValue("ImageURL", CMPLogoPath)
            End If
            Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
            Frm_PrintReports.Text = "تقرير مرتجعات مشتريات"
            Frm_PrintReports.Show()

            If RunDatabaseInternet = "YES" Then : connect() : End If

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
    Sub ForPrintAll(ByVal itm_id As String, ByVal itm_cat As String, ByVal itm_name As String, ByVal price As String, ByVal qu As String, ByVal totalprice As String, ByVal stors As String, _
                    ByVal BILL_NO As String, ByVal Vendorname As String, ByVal bill_date As String, ByVal billtime As String, _
                    ByVal totalpricebeforedisc As String, ByVal disc As String, ByVal totalpriceafterdisc As String, ByVal BEY As String, ByVal STAYING As String)
        Try

            cmd.CommandType = CommandType.StoredProcedure
            cmd.CommandText = "Sp_PrintSalesPurchases"
            cmd.Parameters.Clear()

            cmd.Parameters.AddWithValue("@itm_id", itm_id)
            cmd.Parameters.AddWithValue("@itm_cat", itm_cat)
            cmd.Parameters.AddWithValue("@itm_name", itm_name)
            cmd.Parameters.AddWithValue("@price", price)
            cmd.Parameters.AddWithValue("@qu", qu)
            cmd.Parameters.AddWithValue("@totalprice", totalprice)
            cmd.Parameters.AddWithValue("@store", stors)
            cmd.Parameters.AddWithValue("@BILL_NO", BILL_NO)
            cmd.Parameters.AddWithValue("@Vendorname", Vendorname)
            cmd.Parameters.AddWithValue("@bill_date", bill_date)
            cmd.Parameters.AddWithValue("@billtime", billtime)
            cmd.Parameters.AddWithValue("@totalpricebeforedisc", totalpricebeforedisc)
            cmd.Parameters.AddWithValue("@disc", disc)
            cmd.Parameters.AddWithValue("@totalpriceafterdisc", totalpriceafterdisc)
            cmd.Parameters.AddWithValue("@BEY", BEY)
            cmd.Parameters.AddWithValue("@STAYING", STAYING)

            cmd.ExecuteNonQuery()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
    Private Sub ClearSave()
        GrpMain.Enabled = True
        cmbvendores.SelectedIndex = -1
        txtbillno.Text = ""
        txtprc.Text = ""
        txttotalafterdisc.Text = "0"
        txttotalpeforedisc.Text = "0"
        txtdisc.Text = "0"
        txtpaying.Text = "0"
        txtstaying.Text = "0"
        lblDiscount_Price_After.Text = "0"
    End Sub
    Private Sub SumAllPrice()
        Dim SM, SMVAT As Double
        Dim TotalCountItems As Integer
        Dim Price, Qunt, Total, TotalRateVAT, RateVAT, TotalVAT, TotalRateVATFor As Double
        For i As Integer = 0 To Dgv_Add.RowCount - 1
            Price = Dgv_Add.Rows(i).Cells(3).Value
            Qunt = Dgv_Add.Rows(i).Cells(5).Value
            RateVAT = Dgv_Add.Rows(i).Cells(12).Value
            Total = Val(Price) * Val(Qunt)
            If ShowValueVAT = "YES" Then
                TotalRateVAT += Format(Val(Total) * Val(RateVAT) / 100, "Fixed")
                TotalRateVATFor = Format(Val(Total) * Val(RateVAT) / 100, "Fixed")
                TotalVAT = Math.Round(TotalRateVATFor, 2) + Math.Round(Total, 2)
            End If

            Dgv_Add.Rows(i).Cells(7).Value = Total

            SM = SM + Dgv_Add.Rows(i).Cells(7).Value - TotalRateVAT
            SMVAT = SMVAT + Dgv_Add.Rows(i).Cells(10).Value

            TotalCountItems += Dgv_Add.Rows(i).Cells(5).Value
        Next
        txtTotalCountItems.Text = TotalCountItems
        txttotalpeforedisc.Text = SM
        txtTotalValueVAT.Text = Math.Round(TotalRateVAT, 2)

    End Sub

    Private Sub txttotalpeforedisc_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txttotalpeforedisc.TextChanged
        MyVars.CheckNumber(txttotalpeforedisc)

        sumdisc()
    End Sub

    Private Sub sumdisc()
        Dim DiscVal, TotalPurchaseTax As Double

        If PurchaseTax <> "0" Then
            TotalPurchaseTax = Format(Val(txttotalpeforedisc.Text) * Val(txtPurchaseTax.Text) / 100, "Fixed")
        End If

        If ChkCent.Checked = True Then
            DiscVal = Val((Val(txttotalpeforedisc.Text) * (100 - Val(txtdisc.Text))) / 100)
            DiscVal = Math.Round(DiscVal, 4)
        ElseIf ChkVal.Checked = True Then
            DiscVal = Val(txttotalpeforedisc.Text) - Val(txtdisc.Text)
        End If
        txttotalafterdisc.Text = DiscVal + TotalPurchaseTax + Val(txtTotalValueVAT.Text)

        If ChkState.Checked = False Then
            txtstaying.Text = Val(txttotalafterdisc.Text) - Val(txtpaying.Text)
        Else
            txtstaying.Text = "0"
        End If

        If txtstaying.Text = "0" Then
            txtpaying.Text = Val(txttotalafterdisc.Text) - Val(txtstaying.Text)
        End If


        'Dim DiscVal As Double
        'If ChkCent.Checked = True Then
        '    DiscVal = Val((Val(txttotalpeforedisc.Text) * (100 - Val(txtdisc.Text))) / 100)
        '    DiscVal = Math.Round(DiscVal, 4)
        'ElseIf ChkVal.Checked = True Then
        '    DiscVal = Val(txttotalpeforedisc.Text) - Val(txtdisc.Text)
        'End If
        'txttotalafterdisc.Text = DiscVal
        'txtstaying.Text = Val(txttotalafterdisc.Text) - Val(txtpaying.Text)

    End Sub

    Private Sub txtdisc_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtdisc.KeyUp
        If e.KeyCode = 13 Then
            txtpaying.Focus()
        End If
    End Sub

    Private Sub txtdisc_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtdisc.TextChanged
        MyVars.CheckNumber(txtdisc)

        sumdisc()
    End Sub

    Private Sub txttotalafterdisc_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txttotalafterdisc.TextChanged
        MyVars.CheckNumber(txttotalafterdisc)

        sumdisc()
    End Sub

    Private Sub txtpaying_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtpaying.KeyUp
        If e.KeyCode = 13 Then
            BtnSave.PerformClick()
        End If
    End Sub

    Private Sub txtpaying_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtpaying.TextChanged
        MyVars.CheckNumber(txtpaying)

        sumdisc()
    End Sub

    Private Sub txtstaying_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtstaying.TextChanged
        MyVars.CheckNumber(txtstaying)

        sumdisc()
    End Sub

    Dim pay As String
    Private Sub ChkState_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkState.CheckedChanged
        If ChkState.Checked = True Then
            pay = txtpaying.Text
            txtpaying.Text = txttotalafterdisc.Text : txtpaying.Enabled = False
        Else
            txtpaying.Text = pay
            txtpaying.Enabled = True
        End If
    End Sub

    Private Sub txtbillno_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtbillno.TextChanged
        MyVars.CheckNumber(txtbillno)

    End Sub

    Private Sub txtprc_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtprc.KeyUp
        If e.KeyCode = 13 Then
            If txtprc.Text.Trim = "" Then
                txtdisc.Focus()
            Else
                Bol = True
                If txtprc.Text.Trim = "" Then Exit Sub

                ParcodeMore = txtprc.Text
                GetBarcodeMore(txtprc.Text) : If ParcodeMore <> "" Then : If ParcodeMore <> 0 Then : txtprc.Text = ParcodeMore : End If : End If
                If ParcodeMore = "0" Then
                    Dim PrcUnity As String = Cls.Get_Code_Value("ItemsUnity", "itm_id", "itm_id_Unity", ParcodeMore)
                    If PrcUnity <> "0" Then
                        txtprc.Text = PrcUnity
                    End If
                End If

                Try
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select group_name , sname,TinPriceAverage,RateVAT,RateDiscTinPrice from items where itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "' and QuickSearch=0"
                    dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then
                        cmbcats.Text = dr(0).ToString
                        cmbname.Text = dr(1).ToString
                        txtprice.Text = Val(dr(2).ToString)
                        ItemsRateVAT = Val(dr(3).ToString)
                        txtdiscBill.Text = Val(dr(4).ToString)
                    Else
                        Cls.Select_More_Data_Stores("items", "group_name,sname,SalPrice,RateVAT,Stores,RateDiscTinPrice", "itm_id=N'" & txtprc.Text & "' and QuickSearch=0")
                        If dr.HasRows = True Then
                            cmbcats.Text = dr("group_name").ToString
                            cmbname.Text = dr("sname").ToString
                            txtprice.Text = dr("SalPrice").ToString
                            ItemsRateVAT = Val(dr("RateVAT").ToString)
                            cmbStores.Text = dr("Stores").ToString
                            txtdiscBill.Text = Val(dr("RateDiscTinPrice").ToString)
                        End If
                    End If
                Catch ex As Exception
                    ErrorHandling(ex, Me.Text)
                End Try

                txtStoreTotal.Text = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)

                GetItemsUnity(cmbUnityItems, txtprc.Text)

                Dim ItemsUnity As String = Cls.Get_Code_Value("ItemsUnity", "Unity_Name", "itm_id_Unity", ParcodeMore)
                If ItemsUnity <> "0" Then
                    cmbUnityItems.Text = ItemsUnity
                End If

                SetItemsUnity()

                GetItemsUnityTotalCarton()

                txtquntUnity.Focus()
                txtquntUnity.SelectAll()
                txtqunt.Text = 1
                txtquntUnity.Text = 1

                Bol = False
                ParcodeMore = 0
            End If

        End If
    End Sub

    Private Sub cmbname_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbname.DropDown
        cmbname.Text = ""
        txtqunt.Text = ""
        txtquntUnity.Text = ""
        txtprc.Text = ""
        txtprice.Text = ""
        txtStoreTotal.Text = ""
        txtTotalTotal.Text = ""
        cmbUnityItems.Text = ""
        txtdiscBill.Text = ""
    End Sub

    Private Sub cmbname_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbname.KeyUp
        If e.KeyCode = 13 Then
            cmbStores.Focus()
        End If
        'If e.KeyCode = 8 Then
        '    txtqunt.Text = ""
        '    txtprc.Text = ""
        '    txtprice.Text = ""
        '    txtStoreTotal.Text = ""
        '    txtTotalTotal.Text = ""
        'End If
    End Sub

    Private Sub cmbname_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbname.SelectedIndexChanged
        If txtprc.Text = "" Then
            GetDataIM_BTin()
        End If
    End Sub

    Private Sub GetDataIM_BTin()
        Bol = True
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id,group_name,TinPrice,RateVAT,RateDiscTinPrice from items where sname=N'" & cmbname.Text & "'  and Stores=N'" & cmbStores.Text & "' and QuickSearch=0"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                txtprc.Text = dr(0).ToString
                cmbcats.Text = dr(1).ToString
                txtprice.Text = dr(2).ToString
                ItemsRateVAT = Val(dr(3).ToString)
                txtdiscBill.Text = Val(dr(4).ToString)
            Else
                Cls.Select_More_Data_Stores("items", "itm_id,group_name,SalPrice,RateVAT,Stores,RateDiscTinPrice", "sname=N'" & cmbname.Text & "' and QuickSearch=0")
                If dr.HasRows = True Then
                    txtprc.Text = dr("itm_id").ToString
                    cmbcats.Text = dr("group_name").ToString
                    txtprice.Text = dr("SalPrice").ToString
                    ItemsRateVAT = Val(dr("RateVAT").ToString)
                    cmbStores.Text = dr("Stores").ToString
                    txtdiscBill.Text = Val(dr("RateDiscTinPrice").ToString)
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        txtStoreTotal.Text = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)

        '====================================================

        GetItemsUnity(cmbUnityItems, txtprc.Text)

        SetItemsUnity()

        GetItemsUnityTotalCarton()

        txtqunt.Text = 1
        txtquntUnity.Text = 1

        txtprice.Focus()
        txtprice.SelectAll()
        Bol = False

    End Sub

    Private Sub cmbcats_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If cmbcats.Text = "" Then GoTo 1
        If Bol = True Then GoTo 1
        Bol = True
        Try
            Cls.fill_combo_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbname)
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        cmbname.Text = ""
1:
        Bol = False
    End Sub

    Private Sub ChkVal_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkVal.CheckedChanged
        sumdisc()
    End Sub

    Private Sub ChkCent_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkCent.CheckedChanged
        sumdisc()
    End Sub

    Dim RNXD As Integer
    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        For i As Integer = 0 To Dgv_Add.SelectedRows.Count - 1
            RNXD = Dgv_Add.CurrentRow.Index
            Dgv_Add.Rows.RemoveAt(RNXD)
        Next
        SumAllPrice() : sumdisc()
    End Sub

    Private Sub BtnAddVendor_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAddVendor.Click
        frmvendors.Show()
    End Sub

    Private Sub BtnClear_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnClear.Click
        Dt_AddBill.Rows.Clear()
        SumAllPrice() : sumdisc()
        ClearSave()
        Exit Sub
        'For RNXD = 0 To Dgv_Add.Rows.Count
        '    RNXD = Dgv_Add.CurrentRow.Index
        '    Dgv_Add.Rows.RemoveAt(RNXD)
        'Next
        'Exit Sub


        ' MsgBox(Dgv_Add.Rows.Count)
        'For i As Integer = 0 To Dgv_Add.Rows.Count
        Dgv_Add.DataSource = ""
        Dt_AddBill.Columns.Add("الباركود", GetType(String))
        Dt_AddBill.Columns.Add("المجموعة", GetType(String))
        Dt_AddBill.Columns.Add("الاسم", GetType(String))
        Dt_AddBill.Columns.Add("السعر", GetType(Double))
        Dt_AddBill.Columns.Add("الكمية", GetType(Integer))
        Dt_AddBill.Columns.Add("الإجمالي", GetType(Double))
        '  Next
        MAXRECORD()
    End Sub
    Private Sub MAXRECORD()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "select * from IM_Btin"
            dr = cmd.ExecuteReader
            Dim dt As New DataTable
            dt.Load(dr)

            If dt.Rows.Count = 0 Then
                Me.txtbillno.Text = 1
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(CAST(bill_No As float)) as mb FROM IM_Btin"

                dr = cmd.ExecuteReader
                dr.Read()
                Dim sh As Integer
                sh = dr("mb")
                Me.txtbillno.Text = sh + 1
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbcats_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbcats.DropDown
        cmbname.Text = ""
        cmbcats.Text = ""
        'txtqunt.Text = ""
        txtprc.Text = ""
        txtprice.Text = ""
    End Sub

    Private Sub cmbcats_KeyUp1(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbcats.KeyUp
        If e.KeyCode = 13 Then
            cmbname.Focus()
        End If
    End Sub

    Private Sub cmbcats_SelectedIndexChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbcats.SelectedIndexChanged
        If cmbname.Text = "" Then
            Try
                If txtprc.Text = "" Then
                    If cmbcats.Text.Trim = "" Then Exit Sub
                    'Cls.fill_combo_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbname)
                    Cls.fill_combo_DataAdapter_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbname)

                    'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    'If Company_Branch_ID = "0" Then
                    '    cmd.CommandText = "select distinct sname from Items where group_name =N'" & cmbcats.Text & "' and QuickSearch=0 order by 1"
                    'Else
                    '    cmd.CommandText = "select distinct sname from Items where group_name =N'" & cmbcats.Text & "' and QuickSearch=0 and Company_Branch_ID = N'" & Company_Branch_ID & "'  or Company_Branch_ID =N'0' order by 1"
                    'End If
                    'dr = cmd.ExecuteReader
                    'Do While dr.Read = True
                    '    cmbname.Items.Add(Trim(dr(0)))
                    'Loop
                    cmbname.Text = ""
                End If
            Catch ex As Exception
                ErrorHandling(ex, Me.Text)
            End Try
        End If
    End Sub

    Private Sub cmbStores_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbStores.KeyUp
        If e.KeyCode = 13 Then
            GetDataIM_BTin()
        End If
    End Sub

    Private Sub btnAddImageBill_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddImageBill.Click
        Try
            OpenFileDialog1.Filter = "Image Files (*.png *.jpg *.bmp *.JPE *.JPEG) |*.png; *.jpg; *.bmp; *.JPE; *.JPEG|All Files(*.*) |*.*"
            With Me.OpenFileDialog1
                .FilterIndex = 1
                .Title = "أختر صورة الفاتورة"
                .ShowDialog()
                If Len(.FileName) > 0 Then
                    PicBill.Image = Image.FromFile(OpenFileDialog1.FileName)
                End If
            End With
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub ImageUpdateBill()
        Try
            If OpenFileDialog1.FileName <> "" Then
                connectionStringOpen()
                Dim cmd2 As SqlClient.SqlCommand = New SqlClient.SqlCommand
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = " Update IM_Btin SET  Image_Bill = @Image_Bill WHERE bill_No =N'" & txtbillno.Text & "'"
                cmd2.CommandType = CommandType.Text
                cmd2.Connection = Cn
                Dim fs As FileStream = New FileStream(OpenFileDialog1.FileName, FileMode.Open, FileAccess.Read)
                Dim r As BinaryReader = New BinaryReader(fs)
                Dim FileByteArray(fs.Length - 1) As Byte
                r.Read(FileByteArray, 0, CInt(fs.Length))
                With cmd2
                    .CommandType = CommandType.Text
                    .Connection = Cn
                    .Parameters.Add("@Image_Bill", SqlDbType.Image).Value = FileByteArray
                    .CommandText = S
                End With
                cmd2.ExecuteNonQuery()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub PriceTinAverage(ByVal Parcode As String, ByVal Stores As String, ByVal Unity As String, ByVal qunt As Double, ByVal TotalPrice As Double)
        Dim StoreItems, TotalPriceTinAverage, BalanceBeforeBuying, TotalBalanceBeforeBuying As Double
        '================================================ المخزون الحالى =====================================================
        Dim Xqunt As Double = qunt

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select store,TinPriceAverage from Items where itm_id =N'" & Parcode & "' and Stores =N'" & Stores & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            StoreItems = dr("store").ToString()
            Dim xx As String = dr("TinPriceAverage").ToString()
            If xx = "" Then
                TotalPriceTinAverage = 0
            Else
                TotalPriceTinAverage = xx
            End If
        End If

        Price_Unity = Cls.Get_Code_Value_Stores_More("Items", "TinPrice", "itm_id =N'" & Parcode & "' and Stores=N'" & Stores & "'")
        If NotUnityItemsProgram = "YES" Then
            NumberPieces = Cls.Get_Code_Value_Branch_More("ItemsUnity", "NumberPieces", "itm_id=N'" & Parcode & "' and Unity_Name=N'" & Unity & "'")
            If NumberPieces <> 1 Then
                Price_Unity = Cls.Get_Code_Value_Stores_More("Items", "TinPrice", "itm_id =N'" & Parcode & "' and Stores=N'" & Stores & "'")
                Xqunt = Val(NumberPieces) * Val(qunt)
            End If
        End If

        '================================================ متوسط سعر الشراء الجديد =====================================================

        'Try
        BalanceBeforeBuying = Val(TotalPriceTinAverage) * Val(StoreItems)

            TotalBalanceBeforeBuying = Val(TotalPrice) + Val(BalanceBeforeBuying)
            TotalBalanceBeforeBuying = Math.Round(TotalBalanceBeforeBuying, 2)

            Dim TotalTotal As Double = Val(StoreItems) + Val(Xqunt)
        If TotalBalanceBeforeBuying = 0 And TotalTotal = 0 Then
            TotalPriceBeforeAverage = 0
        Else
            If TotalTotal <> 0 Then
                If TotalBalanceBeforeBuying <> 0 Then
                    TotalPriceBeforeAverage = Val(TotalBalanceBeforeBuying) / Val(TotalTotal)
                End If
            End If

            If TinPriceAverageThreeDigits = "NO" Then
                    TotalPriceBeforeAverage = Math.Round(TotalPriceBeforeAverage, 2)
                Else
                    TotalPriceBeforeAverage = Math.Round(TotalPriceBeforeAverage, 3)
                End If
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update items set TinPriceAverage = " & Val(TotalPriceBeforeAverage) & " where itm_id =N'" & Parcode & "' and Stores =N'" & Stores & "'" : cmd.ExecuteNonQuery()
        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub

    Private Sub SearchForProduct()
        Dim SearchForProduct As String = mykey.GetValue("SearchForProduct", "NO")
        If SearchForProduct = "YES" Then
            cmbcats.Visible = False
            Labelcats.Visible = False
            cmbname.Size = New System.Drawing.Size(330, 21)
        Else
            cmbcats.Visible = True
            Labelcats.Visible = True
        End If
    End Sub

    Private Sub Daily_Restrictions()

        Dim Account As String = "" : Dim AccountCode As String = "" : Dim AccountPaying As String = "" : Dim PayingCode As String = ""
        Dim Discounts As String = "" : Dim DiscountsCode As String = "" : Dim AccountTreasury As String = "" : Dim AccountCodeTreasury As String = ""

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مرتجعات مشتريات'" : dr = cmd.ExecuteReader
            If dr.Read Then
                Account = dr("Link_AccountsTree") : AccountCode = dr("ACCNumber")
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مرتجعات مدفوعات موردين'" : dr = cmd.ExecuteReader
            If dr.Read Then
                AccountPaying = dr("Link_AccountsTree") : PayingCode = dr("ACCNumber")
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مرتجعات خصومات موردين'" : dr = cmd.ExecuteReader
            If dr.Read Then
                Discounts = dr("Link_AccountsTree") : DiscountsCode = dr("ACCNumber")
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select ACCName,ACCNumber from AccountsTree where ACCName=N'الخزينة'" : dr = cmd.ExecuteReader
            If dr.Read Then
                AccountTreasury = dr("ACCName") : AccountCodeTreasury = dr("ACCNumber")
            End If

            '========================================================================================

            Dim MOVID As String = Cls.MAXRECORD("MOVES", "MOVID")
            Dim MOVRegNumber As String = Cls.MAXRECORD("MOVES", "MOVRegNumber")

            If ChkState.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Account & "',N'" & txttotalpeforedisc.Text & "',N'" & txttotalpeforedisc.Text & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If

            If ChkState.Checked = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Account & "',N'" & txttotalpeforedisc.Text & "',N'" & txttotalpeforedisc.Text & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If

            If Val(txtpaying.Text) > 0 Then
                'Dim bill_no As String = Cls.MAXRECORD("Vst", "id") - 1

                ' من حساب / الخزينة
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'" & txtpaying.Text & "',N'0',N'" & AccountTreasury & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' الى حساب / مرتجعات مدفوعات موردين
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & PayingCode & "',N'" & AccountPaying & "',N'0',N'" & txtpaying.Text & "',N'" & AccountPaying & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If

            If Val(txtdisc.Text) > 0 Then
                'Dim bill_no As String = Cls.MAXRECORD("Vst_disc", "id") - 1

                ' من حساب / مرتجعات خصومات موردين
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & DiscountsCode & "',N'" & Discounts & "',N'" & DiscTotal & "',N'0',N'" & Discounts & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' الى حساب / الخزينة
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & txtbillno.Text & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'0',N'" & DiscTotal & "',N'" & AccountTreasury & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub


    Private Sub txtquntUnity_TextChanged(sender As Object, e As EventArgs) Handles txtquntUnity.TextChanged
        cmbUnityItems_SelectedIndexChanged(sender, e)
        MyVars.CheckNumber(txtqunt)
        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If

        Dim XTotal As Double = Val(txtprice.Text) * Val(txtquntUnity.Text)
        TotalValueVAT = Format(Val(XTotal) * Val(ItemsRateVAT) / 100, "Fixed")
        txtTotalTotal.Text = Math.Round(TotalValueVAT, 2) + Math.Round(XTotal, 2)
        txtTotalTotal.Text = Math.Round(Convert.ToDouble(txtTotalTotal.Text), 2)
    End Sub

    Private Sub cmbUnityItems_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbUnityItems.SelectedIndexChanged
        Try
            If Bol = False Then
                If NotUnityItemsProgram = "YES" Then
                    Dim TinPriceUnit As String = 0
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select NumberPieces,TinPriceUnit from View_ItemsUnitySize where itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then : NumberPieces = dr(0).ToString : TinPriceUnit = dr(1).ToString : Else NumberPieces = 1 : TinPriceUnit = 0 : End If
                    txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
                    If TinPriceUnit <> 0 Then
                        txtprice.Text = TinPriceUnit
                    End If
                Else
                    txtqunt.Text = Val(txtquntUnity.Text)
                End If

                GetItemsUnityTotalCarton()

                sumdisc1()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub txtquntUnity_KeyUp(sender As Object, e As KeyEventArgs) Handles txtquntUnity.KeyUp
        If e.KeyCode = 13 Then
            If NotUnityItemsProgram = "YES" Then
                cmbUnityItems.Focus()
                cmbUnityItems.SelectAll()
            Else
                BtnAdd.PerformClick()
            End If
        End If
    End Sub

    Private Sub cmbUnityItems_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbUnityItems.KeyUp
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub SetItemsUnity()
        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 1 : End If
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(txtquntUnity.Text)
        End If
    End Sub

    Private Sub txtSalestax_TextChanged(sender As Object, e As EventArgs) Handles txtPurchaseTax.TextChanged
        MyVars.CheckNumber(txtPurchaseTax)

        sumdisc()
    End Sub

    Private Sub txtTotalValueVAT_TextChanged(sender As Object, e As EventArgs) Handles txtTotalValueVAT.TextChanged
        sumdisc()
    End Sub

    Private Sub GetItemsUnityTotalCarton()
        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from View_ItemsUnity where itm_id=N'" & txtprc.Text & "' and Unity_Name =N'" & cmbUnityItems.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                NumberPieces = dr(0).ToString
            End If
            Dim Itm_Store As Double = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)
            If NumberPieces = 0 Or NumberPieces = 1 Then
                txtStoreTotal.Text = Itm_Store
            Else
                If Itm_Store = 0 And NumberPieces = 0 Then
                    txtStoreTotal.Text = 0
                Else
                    txtStoreTotal.Text = Val(Itm_Store) / Val(NumberPieces)
                    txtStoreTotal.Text = Math.Round(Val(txtStoreTotal.Text), 2)
                End If
            End If
        Else
            txtqunt.Text = Val(txtquntUnity.Text)
        End If


    End Sub

    Private Sub txtdiscBill_MouseDoubleClick(sender As Object, e As MouseEventArgs) Handles txtdiscBill.MouseDoubleClick
        PaneldiscBill.Top = 200
    End Sub

    Private Sub txtdiscBill_KeyUp(sender As Object, e As KeyEventArgs) Handles txtdiscBill.KeyUp
        If ((e.KeyCode = Keys.Enter)) Then
            txtprice.Focus()
        End If
    End Sub

    Private Sub txtdiscBill_TextChanged(sender As Object, e As EventArgs) Handles txtdiscBill.TextChanged
        MyVars.CheckNumber(txtdiscBill)
        sumdisc1()
    End Sub

    Private Sub sumdisc1()
        Dim DiscVal As Double
        Dim TotalPriseQunt As Double = Val(txtprice.Text) * Val(txtquntUnity.Text)
        If ChkCent2.Checked = True Then
            DiscVal = Val((Val(TotalPriseQunt) * (100 - Val(txtdiscBill.Text))) / 100)
            DiscVal = Math.Round(DiscVal, 2)
        ElseIf ChkVal2.Checked = True Then
            DiscVal = Val(TotalPriseQunt) - Val(txtdiscBill.Text)
        End If
        txtTotalTotal.Text = Math.Round(TotalValueVAT, 2) + Math.Round(DiscVal, 2)

        If ChkCent2.Checked = True Then
            StateDisc = "نسبة"
            lblDiscount_Price_After.Text = Val((Val(Val(txtprice.Text) * Val(1)) * (100 - Val(txtdiscBill.Text))) / 100)
            Dim XVal As String = Format(Val(txtprice.Text) * Val(txtdiscBill.Text) / 100, "Fixed")
            DiscountsValue = Val(XVal) * Val(txtquntUnity.Text)
        Else
            StateDisc = "قيمة"
            DiscountsValue = Val(txtdiscBill.Text)
        End If

    End Sub

    Private Sub ChkCent2_CheckedChanged(sender As Object, e As EventArgs) Handles ChkCent2.CheckedChanged
        sumdisc1()
    End Sub

    Private Sub ChkVal2_CheckedChanged(sender As Object, e As EventArgs) Handles ChkVal2.CheckedChanged
        sumdisc1()
    End Sub

    Private Sub txtprc_KeyDown(sender As Object, e As KeyEventArgs) Handles txtprc.KeyDown
        If ((e.KeyCode = 8)) Then
            cmbname.Text = ""
            txtqunt.Text = ""
            txtquntUnity.Text = ""
            txtprice.Text = ""
            txtStoreTotal.Text = ""
            txtTotalTotal.Text = ""
            cmbUnityItems.Text = ""
            txtdiscBill.Text = ""
        End If
    End Sub

    Private Sub txtsearsh_TextChanged(sender As Object, e As EventArgs) Handles txtsearsh.TextChanged
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
                S = Cls.Get_Select_Grid_S("itm_id As [الباركود],sname as [الاسم],SalPrice As [سعر التجزئة],WholePrice As [سعر الجملة],TinPrice As [سعر الشراء],store as [المخزون]", "items", "Stores=N'" & cmbStores.Text & "'  and QuickSearch=0 and sname <> ''")
            Else
                S = Cls.Get_Select_Grid_S("itm_id as [الباركود],sname as [الاسم],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],TinPrice as [سعر الشراء],store as [المخزون]", "items", "Stores=N'" & cmbStores.Text & "'  and QuickSearch=0 and sname Like N'%" & txtsearsh.Text & "%'")
            End If
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)
            DTGV.Columns(0).Width = 130
            DTGV.Columns(1).Width = 400
            DTGV.Columns(2).Width = 100
            DTGV.Columns(3).Width = 100
            DTGV.Columns(4).Width = 100
            DTGV.Columns(5).Width = 100
            DTGV.Columns(3).Visible = False
            DTGV.Columns(4).Visible = False

            Dim HideQuntItems As String = mykey.GetValue("HideQuntItems", "NO")
            If HideQuntItems = "YES" Then
                DTGV.Columns(5).Visible = False
            Else
                DTGV.Columns(5).Visible = True
            End If

            If PermtionName = "مدير" Then
                DTGV.Columns(3).Visible = True
                DTGV.Columns(4).Visible = True
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbFindCats_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbFindCats.SelectedIndexChanged
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If cmbFindCats.Text = "" Then
                S = Cls.Get_Select_Grid_S("itm_id as [الباركود],sname as [الاسم],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],TinPrice as [سعر الشراء],store as [المخزون]", "items", "Stores=N'" & cmbStores.Text & "' and sname <> ''")
            Else
                S = Cls.Get_Select_Grid_S("itm_id as [الباركود],sname as [الاسم],SalPrice as [سعر التجزئة],WholePrice as [سعر الجملة],TinPrice as [سعر الشراء],store as [المخزون]", "items", "Stores=N'" & cmbStores.Text & "' and group_name =N'" & cmbFindCats.Text & "'")
            End If
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)
            DTGV.Columns(0).Width = 130
            DTGV.Columns(1).Width = 400
            DTGV.Columns(2).Width = 100
            DTGV.Columns(3).Width = 100
            DTGV.Columns(4).Width = 100
            DTGV.Columns(5).Width = 100
            DTGV.Columns(3).Visible = False
            DTGV.Columns(4).Visible = False

            Dim HideQuntItems As String = mykey.GetValue("HideQuntItems", "NO")
            If HideQuntItems = "YES" Then
                DTGV.Columns(5).Visible = False
            Else
                DTGV.Columns(5).Visible = True
            End If

            If PermtionName = "مدير" Then
                DTGV.Columns(3).Visible = True
                DTGV.Columns(4).Visible = True
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub DTGV_DoubleClick(sender As Object, e As EventArgs) Handles DTGV.DoubleClick
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        txtprc.Text = DTGV.SelectedRows(0).Cells(0).Value
        cmbname.Text = DTGV.SelectedRows(0).Cells(1).Value
        GetDataIM_BTin()
        txtsearsh.Text = ""
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        PanelSearch.Top = 5000
        PanelSearch.Dock = DockStyle.None
        MaximizeButtons = True
    End Sub

    Private Sub btnMaximizeButtons_Click(sender As Object, e As EventArgs) Handles btnMaximizeButtons.Click
        If MaximizeButtons = True Then
            PanelSearch.Dock = DockStyle.Fill
            MaximizeButtons = False
        Else
            PanelSearch.Dock = DockStyle.None
            MaximizeButtons = True
        End If
        PanelSearch.Location = New System.Drawing.Point(5, 200)
        PanelSearch.Size = New System.Drawing.Size(800, 240)

    End Sub

    Private Sub btnSearchItems_Click(sender As Object, e As EventArgs) Handles btnSearchItems.Click
        PanelSearch.Location = New System.Drawing.Point(5, 200)
        txtsearsh.Focus()
        PanelSearch.Size = New System.Drawing.Size(800, 240)
        txtsearsh_TextChanged(sender, e)
    End Sub

    Private Sub btnAddNewItems_Click(sender As Object, e As EventArgs) Handles btnAddNewItems.Click
        ActionAddNewItems = True
        FrmItemsNew.ShowDialog()
    End Sub

    Private Sub Dgv_Add_CellValueChanged(sender As Object, e As DataGridViewCellEventArgs) Handles Dgv_Add.CellValueChanged
        If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Add.Rows.Count) = 0 Then Beep() : Exit Sub

        SumAllPrice()
    End Sub

    Private Sub btnClosediscBill_Click(sender As Object, e As EventArgs) Handles btnClosediscBill.Click
        PaneldiscBill.Top = 10000
    End Sub

    Private Sub Frm_IM_BTin_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        If txttotalpeforedisc.Text <> "0" Then
            Dim x As String = MsgBox("يوجد فاتورة لم يتم حفظها هل تريد حفظ الفاتورة قبل الاغلاق", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
            If x = vbCancel Then
                Exit Sub
            Else
                BtnSave.PerformClick()
            End If
        End If
    End Sub

    Private Sub GetDebtorlCreditorPrevious()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select vnamntcredit , vnamntdebit from vendors where Vendorname=N'" & cmbvendores.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                AmntcreditPrevious = dr("vnamntcredit")
                AmntdebitPrevious = dr("vnamntdebit")
                TotalVendoreBalancePrevious = AmntcreditPrevious - AmntdebitPrevious
            Else
                TotalVendoreBalancePrevious = "0"
            End If
            TotalAccountAfterInvoice = Val(TotalVendoreBalancePrevious) + Val(txttotalafterdisc.Text)
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetDebtorlCreditor()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select vnamntcredit,vnamntdebit from vendors where Vendorname =N'" & cmbvendores.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Amntcredit = Val(dr("vnamntcredit"))
                Amntdebit = Val(dr("vnamntdebit"))
                XCreditDebit = Amntcredit - Amntdebit
            Else
                XCreditDebit = "0"
            End If
            'TotalAccountAfterInvoice = Val(XCreditDebit) + Val(txttotalafterdisc.Text)
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetVendorsAddress()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select addr,tel1,resp,rtel  from vendors where Vendorname=N'" & cmbvendores.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                VendorsAddress = dr(0).ToString
                VendorsTel = dr(1).ToString
                Resp = dr(2).ToString
                Rtel = dr(3).ToString
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
End Class
