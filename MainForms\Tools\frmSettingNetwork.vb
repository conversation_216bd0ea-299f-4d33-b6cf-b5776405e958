﻿Imports System.Data.SqlClient
Imports System.IO
Imports System.Security.AccessControl
Imports Microsoft.Win32
Public Class frmSettingNetwork
    Dim Reg As Registry
    Dim Startup As String = "Software\Microsoft\Windows\CurrentVersion\Run"
    Dim SQLServerName As String
    Dim FileNames As String

    Private Sub SettingNetwork_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        LoadServerNames()
        LoadDataBaseName()

        PanelLocalDB.Top = 5000
        PanelHelp.Top = 5000
        'SQLServerName = Environment.MachineName
        'SQLServerName = ".\SQLEXPRESS"
        SQLServerName = "(LocalDB)\MSSQLLocalDB"

        If NetworkName = "Source" Then
            rdoSource.Checked = True
        End If
        If NetworkName = "No" Then
            rdoNoNetwork.Checked = True
        End If
        If NetworkName = "Yes" Then
            rdoYesNetwork.Checked = True
        End If
        If NetworkName = "LocalDB" Then
            rdoLocalDB.Checked = True
            txtLocalDB.Text = My.Computer.FileSystem.ReadAllText(Application.StartupPath & "\ConnectionString.txt")
        End If

        Dim UseExternalServer As String = mykey.GetValue("UseExternalServer", "No")
        If UseExternalServer = "No" Then
            rdoNO_ExternalServer.Checked = True
        End If
        If UseExternalServer = "Yes" Then
            rdoYES_ExternalServer.Checked = True
        End If

        txtDataBaseName.Text = mykey.GetValue("DatabaseElectric", "DatabaseElectric")
        txtServerName.Text = mykey.GetValue("ServerName", SQLServerName)
        txtUserName.Text = mykey.GetValue("UserName", "sa")
        txtPassword.Text = mykey.GetValue("Password", "")

        Dim SettingNetworkDefault As String = mykey.GetValue("SettingNetworkDefault", "True")
        If SettingNetworkDefault = "True" Then
            chkSettingNetworkDefault.Checked = True
        End If
        If SettingNetworkDefault = "False" Then
            chkSettingNetworkDefault.Checked = False
        End If


        type_connection_server()
    End Sub

    Private Sub ButtonAPPLY_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAPPLY.Click
        If chkSettingNetworkDefault.Checked = True Then
            chkSettingNetworkDefault.Visible = False
            chkSettingNetworkDefault.Checked = False

            mykey.SetValue("SettingNetworkDefault", "False")

            If rdoYesNetwork.Checked = True Then
                mykey.SetValue("NetworkNameDefault", "Yes")
            ElseIf rdoNoNetwork.Checked = True Then
                mykey.SetValue("NetworkNameDefault", "No")
            ElseIf rdoSource.Checked = True Then
                mykey.SetValue("NetworkNameDefault", "Source")
            ElseIf rdoLocalDB.Checked = True Then
                mykey.SetValue("NetworkNameDefault", "LocalDB")
                If IO.File.Exists(Application.StartupPath & "\ConnectionString.txt") = False Then
                    IO.File.CreateText(Application.StartupPath & "\ConnectionString.txt").Close()
                End If
                IO.File.WriteAllText(Application.StartupPath & "\ConnectionString.txt", txtLocalDB.Text, System.Text.Encoding.UTF8)
            End If

            If rdoNO_ExternalServer.Checked = True Then
                mykey.SetValue("UseExternalServerDefault", "No")
            ElseIf rdoYES_ExternalServer.Checked = True Then
                mykey.SetValue("UseExternalServerDefault", "Yes")
            End If
            mykey.SetValue("DatabaseElectricDefault", txtDataBaseName.Text)
            mykey.SetValue("ServerNameDefault", txtServerName.Text)
            mykey.SetValue("UserNameDefault", txtUserName.Text)
            mykey.SetValue("PasswordDefault", txtPassword.Text)
        End If

        '=============================================================================
        If rdoYesNetwork.Checked = True Then
            mykey.SetValue("NetworkName", "Yes")
            NetworkName = "Yes"
        ElseIf rdoNoNetwork.Checked = True Then
            mykey.SetValue("NetworkName", "No")
            NetworkName = "No"
        ElseIf rdoSource.Checked = True Then
            mykey.SetValue("NetworkName", "Source")
            NetworkName = "Source"
        ElseIf rdoLocalDB.Checked = True Then
            mykey.SetValue("NetworkName", "LocalDB")
            NetworkName = "LocalDB"
            If IO.File.Exists(Application.StartupPath & "\ConnectionString.txt") = False Then
                IO.File.CreateText(Application.StartupPath & "\ConnectionString.txt").Close()
            End If
            IO.File.WriteAllText(Application.StartupPath & "\ConnectionString.txt", txtLocalDB.Text, System.Text.Encoding.UTF8)

        End If

        If rdoNO_ExternalServer.Checked = True Then
            mykey.SetValue("UseExternalServer", "No")
        ElseIf rdoYES_ExternalServer.Checked = True Then
            mykey.SetValue("UseExternalServer", "Yes")
        End If
        mykey.SetValue("DatabaseElectric", txtDataBaseName.Text)
        mykey.SetValue("ServerName", txtServerName.Text)
        mykey.SetValue("UserName", txtUserName.Text)
        mykey.SetValue("Password", txtPassword.Text)
        'ApplicationRestart = True
        Try
            System.Diagnostics.Process.Start("Horse Store.exe")
        Catch ex As Exception

        End Try

        End


        Me.Hide()
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        End
    End Sub

    Private Sub rdoYesNetwork_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdoYesNetwork.CheckedChanged
        type_connection_server()
    End Sub

    Private Sub rdoNoNetwork_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdoNoNetwork.CheckedChanged
        type_connection_server()
    End Sub

    Private Sub rdoSource_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdoSource.CheckedChanged
        type_connection_server()
    End Sub
    Private Sub type_connection_server()
        If rdoYesNetwork.Checked = True Then
            txtDataBaseName.Enabled = True
            txtServerName.Enabled = True
            txtPassword.Enabled = True
            txtUserName.Enabled = True
        End If
        If rdoNoNetwork.Checked = True Then
            txtDataBaseName.Enabled = True
            txtServerName.Enabled = True
            txtPassword.Enabled = False
            txtUserName.Enabled = False
        End If
        If rdoSource.Checked = True Then
            txtDataBaseName.Enabled = False
            txtServerName.Enabled = False
            txtPassword.Enabled = False
            txtUserName.Enabled = False
        End If
        If rdoLocalDB.Checked = True Then
            txtDataBaseName.Enabled = False
            txtServerName.Enabled = False
            txtPassword.Enabled = False
            txtUserName.Enabled = False
        End If
    End Sub

    Private Sub btnAttachDatabase_Click(sender As Object, e As EventArgs) Handles btnAttachDatabase.Click
        On Error Resume Next
        OpenFileDialog1.Filter = "(*.mdf;*.ldf)| *.mdf;*.ldf |All Files(*.*)|*.*"
        With Me.OpenFileDialog1
            .FilterIndex = 1
            .Title = "حدد مكان قاعدة البيانات"
            .ShowDialog()
            If Len(.FileName) > 0 Then

                Dim FileNameX As String = OpenFileDialog1.FileName
                Dim split As String() = New String() {"\DatabaseElectric."}
                Dim itemsSplit As String() = FileNameX.Split(split, StringSplitOptions.None)
                FileNames = itemsSplit(0).ToString()

                Security_Permission_Access_Full_control()

                attach("DatabaseElectric", "" + FileNames & "\DatabaseElectric.mdf" + "", "" + FileNames & "\DatabaseElectric_log.ldf" + "")

            End If
        End With
    End Sub

    'Security Permission Access Full Control
    Private Sub Security_Permission_Access_Full_control()
        Try
            Dim FilePathMDF As String = FileNames & "\DatabaseElectric.mdf"

            Dim ParmissonMDF As FileSecurity = File.GetAccessControl(FilePathMDF)
            ParmissonMDF.SetAccessRuleProtection(True, False)
            Dim rules As AuthorizationRuleCollection =
            ParmissonMDF.GetAccessRules(True, True, GetType(System.Security.Principal.NTAccount))

            For Each rule As AuthorizationRule In rules
                If TypeOf rule Is AccessRule Then
                    ParmissonMDF.RemoveAccessRule(DirectCast(rule, FileSystemAccessRule))
                End If
            Next

            'ParmissonMDF.AddAccessRule(New FileSystemAccessRule _
            '("Administrators", FileSystemRights.FullControl, AccessControlType.Allow))
            ParmissonMDF.AddAccessRule(New FileSystemAccessRule _
     ("Everyone", FileSystemRights.FullControl, AccessControlType.Allow))
            File.SetAccessControl(FilePathMDF, ParmissonMDF)


            Dim FilePathLDF As String = FileNames & "\DatabaseElectric_log.ldf"

            Dim ParmissonLDF As FileSecurity = File.GetAccessControl(FilePathMDF)
            ParmissonLDF.SetAccessRuleProtection(True, False)
            Dim RulesLDF As AuthorizationRuleCollection =
            ParmissonLDF.GetAccessRules(True, True, GetType(System.Security.Principal.NTAccount))

            For Each RuleLDF As AuthorizationRule In RulesLDF
                If TypeOf RuleLDF Is AccessRule Then
                    ParmissonLDF.RemoveAccessRule(DirectCast(RuleLDF, FileSystemAccessRule))
                End If
            Next

            'ParmissonLDF.AddAccessRule(New FileSystemAccessRule _
            '("Administrators", FileSystemRights.FullControl, AccessControlType.Allow))
            ParmissonLDF.AddAccessRule(New FileSystemAccessRule _
        ("Everyone", FileSystemRights.FullControl, AccessControlType.Allow))
            File.SetAccessControl(FilePathLDF, ParmissonLDF)
        Catch ex As Exception

        End Try
    End Sub

    Friend Sub attach(ByVal Dbname As String, ByVal mdfName As String, ByVal ldfname As String)
        Dim Cn2 As New SqlConnection()
        Dim cmd2 As New SqlCommand
        Dim constring2 As String

        constring2 = "Data Source=" + txtServerName.Text + ";Initial Catalog=Master;Integrated Security=True"
        Cn2.ConnectionString = constring2
        cmd2.Connection = Cn2
        If Cn2.State = ConnectionState.Closed Then
            Cn2.Open()
        End If
        Using Cmd As SqlCommand = Cn2.CreateCommand
            Cmd.CommandType = CommandType.Text
            Cmd.CommandType = CommandType.StoredProcedure
            Cmd.CommandText = "sp_attach_db"
            Cmd.Parameters.AddWithValue("@dbname", Dbname)
            Cmd.Parameters.AddWithValue("@filename1", mdfName)
            Cmd.Parameters.AddWithValue("@filename2", ldfname)
            'Try
            Cmd.ExecuteNonQuery()
            MessageBox.Show("تم رفع قاعدة البيانات للسيرفر", "Attach ...", MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading)
            'Catch ex As SqlException
            '    MsgBox("يجب تثبيت برنامج سكوال سيرفر قبل الدخول في عمليات علي البرنامج برجاء الخروج ثم تثبيت البرامج اولا")
            'End Try
        End Using
    End Sub

    Private Sub frmSettingNetwork_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If ((e.KeyCode = Keys.Control And Keys.F1)) Then
            chkSettingNetworkDefault.Visible = True
        End If
        If ((e.KeyCode = Keys.Control And Keys.F2)) Then
            chkSettingNetworkDefault.Visible = False
        End If
    End Sub

    Private Sub btnSettingNetworkDefaultBack_Click(sender As Object, e As EventArgs) Handles btnSettingNetworkDefaultBack.Click
        Dim XNetwork As String = mykey.GetValue("NetworkNameDefault", "Source")
        If XNetwork = "Source" Then
            rdoSource.Checked = True
        End If
        If XNetwork = "No" Then
            rdoNoNetwork.Checked = True
        End If
        If XNetwork = "Yes" Then
            rdoYesNetwork.Checked = True
        End If

        Dim UseExternalServer As String = mykey.GetValue("UseExternalServerDefault", "No")
        If UseExternalServer = "No" Then
            rdoNO_ExternalServer.Checked = True
        End If
        If UseExternalServer = "Yes" Then
            rdoYES_ExternalServer.Checked = True
        End If

        txtDataBaseName.Text = mykey.GetValue("DatabaseElectricDefault", "DatabaseElectric")
        txtServerName.Text = mykey.GetValue("ServerNameDefault", SQLServerName)
        txtUserName.Text = mykey.GetValue("UserNameDefault", "sa")
        txtPassword.Text = mykey.GetValue("PasswordDefault", "")
    End Sub

    Private Sub ToolStripMenuItemRolesTrue_Click(sender As Object, e As EventArgs) Handles ToolStripMenuItemRolesTrue.Click
        chkSettingNetworkDefault.Visible = True
    End Sub

    Private Sub ToolStripMenuItemRolesFalse_Click(sender As Object, e As EventArgs) Handles ToolStripMenuItemRolesFalse.Click
        chkSettingNetworkDefault.Visible = False
    End Sub

    Private Sub btnHelp_Click(sender As Object, e As EventArgs) Handles btnHelp.Click
        PanelHelp.Top = 200
        PanelHelp.Dock = DockStyle.Fill
    End Sub

    Private Sub btnCloseHelp_Click(sender As Object, e As EventArgs) Handles btnCloseHelp.Click
        PanelHelp.Top = 5000
        PanelHelp.Dock = DockStyle.None
    End Sub

    Private Sub btnCloseLocalDB_Click(sender As Object, e As EventArgs) Handles btnCloseLocalDB.Click
        PanelLocalDB.Dock = DockStyle.None
        PanelLocalDB.Top = 5000
    End Sub

    Private Sub btnLocalDB_Click(sender As Object, e As EventArgs) Handles btnLocalDB.Click
        PanelLocalDB.Top = 200
        PanelLocalDB.Dock = DockStyle.Fill
    End Sub

    Private Sub rdoLocalDB_CheckedChanged(sender As Object, e As EventArgs) Handles rdoLocalDB.CheckedChanged
        type_connection_server()
    End Sub

    Private Sub btnCloseServerData_Click(sender As Object, e As EventArgs) Handles btnCloseServerData.Click
        PanelServerData.Top = 5000
    End Sub

    Private Sub btnAddServerData_Click(sender As Object, e As EventArgs) Handles btnAddServerData.Click
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select count(*) from ConnectOnlineStore where DataBaseName =N'" & txtDataBaseNameOnlineStore.Text.Trim & "' and ServerName =N'" & txtServerNameOnlineStore.Text.Trim & "'" : H = cmd.ExecuteScalar
        'If H > 0 Then
        '    MsgBox(" البيانات مسجلة مسبقا", MsgBoxStyle.Exclamation) : txtDataBaseNameOnlineStore.Focus() : Exit Sub
        'End If

        'Dim OnlineStoreActive As String = ""
        'If rdoOnlineStoreYES.Checked = True Then
        '    OnlineStoreActive = "YES"
        'Else
        '    OnlineStoreActive = "NO"
        'End If
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'S = "insert into ConnectOnlineStore (DataBaseName,ServerName,UserName,Password,OnlineStoreActive)"
        'S = S & " values (N'" & txtDataBaseNameOnlineStore.Text & "',N'" & txtServerNameOnlineStore.Text & "',N'" & txtUserNameOnlineStore.Text & "',N'" & txtPasswordOnlineStore.Text.Trim & "',N'" & OnlineStoreActive & "')"
        'cmd.CommandText = S : cmd.ExecuteNonQuery()

        'GetServerData()

    End Sub

    Private Sub btnDelServerData_Click(sender As Object, e As EventArgs) Handles btnDelServerData.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If dgvServerData.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If

        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To dgvServerData.SelectedRows.Count - 1
            If dgvServerData.RowCount = 0 Then Beep() : Exit Sub
            If (dgvServerData.SelectedRows.Count) = 0 Then Beep() : Exit Sub

            Dim DataBaseName As String = dgvServerData.SelectedRows(i).Cells(0).Value
            Dim ServerName As String = dgvServerData.SelectedRows(i).Cells(1).Value

            Try
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete from Items where DataBaseName =N'" & DataBaseName & "' and ServerName =N'" & ServerName & "'" : cmd.ExecuteNonQuery()

            Catch ex As Exception
                ErrorHandling(ex, Me.Text)
            End Try
        Next
        GetServerData()
    End Sub

    Private Sub GetServerData()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = Cls.Get_Select_Grid_S_OrderBy_Name("id as [رقم],DataBaseName  as [قاعدة البيانات], ServerName as [أسم السيرفر], UserName as [أسم المستخدم] ,Password as [الباسورد],OnlineStoreActive as [تفعيل]", "ConnectOnlineStore", "id<>''", "Id")
            cmd.CommandText = S : dr = cmd.ExecuteReader
            dgvServerData.DataSource = Cls.PopulateDataView(dr)
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub LoadServerNames()
        ' مسح العناصر الحالية
        txtServerName.Items.Clear()

        ' قراءة أسماء الخوادم باستخدام الموديول
        Dim serverNames() As String = ServerNameModule.ReadServerNames()

        ' إضافة الأسماء إلى ComboBox
        txtServerName.Items.AddRange(serverNames)
    End Sub

    Private Sub LoadDataBaseName()
        ' مسح العناصر الحالية
        txtDataBaseName.Items.Clear()

        ' قراءة أسماء قواعد البيانات باستخدام الموديول
        Dim dataBaseNames() As String = DataBaseNameModule.ReadDataBaseNames()

        ' إضافة الأسماء إلى ComboBox
        txtDataBaseName.Items.AddRange(dataBaseNames)
    End Sub

    Private Sub btnAddServerName_Click(sender As Object, e As EventArgs) Handles btnAddServerName.Click
        ' التأكد من إدخال اسم الخادم
        If Not String.IsNullOrWhiteSpace(txtServerName.Text) Then
            ' محاولة إضافة اسم الخادم
            If ServerNameModule.AddServerName(txtServerName.Text.Trim()) Then
                ' إعادة تحميل أسماء الخوادم
                LoadServerNames()

                ' مسح مربع الإدخال
                txtServerName.Items.Clear()
            End If
        Else
            MessageBox.Show("يرجى إدخال اسم الخادم", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub btnAddDataBaseName_Click(sender As Object, e As EventArgs) Handles btnAddDataBaseName.Click
        ' التأكد من إدخال اسم قاعدة البيانات
        If Not String.IsNullOrWhiteSpace(txtDataBaseName.Text) Then
            ' محاولة إضافة اسم قاعدة البيانات
            If DataBaseNameModule.AddDataBaseName(txtDataBaseName.Text.Trim()) Then
                ' إعادة تحميل أسماء قواعد البيانات
                LoadDataBaseName()

                ' مسح مربع الإدخال
                txtDataBaseName.Items.Clear()
            End If
        Else
            MessageBox.Show("يرجى إدخال اسم قاعدة البيانات", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub btnDeleteServerName_Click(sender As Object, e As EventArgs) Handles btnDeleteServerName.Click
        ' التأكد من اختيار عنصر للحذف
        If txtServerName.SelectedIndex <> -1 Then
            ' محاولة حذف اسم الخادم المحدد
            Dim selectedServer As String = txtServerName.SelectedItem.ToString()
            If ServerNameModule.RemoveServerName(selectedServer) Then
                ' إعادة تحميل أسماء الخوادم
                LoadServerNames()
            End If
        Else
            MessageBox.Show("يرجى اختيار اسم الخادم للحذف", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub btnDeleteDataBaseName_Click(sender As Object, e As EventArgs) Handles btnDeleteDataBaseName.Click
        ' التأكد من اختيار عنصر للحذف
        If txtDataBaseName.SelectedIndex <> -1 Then
            ' محاولة حذف اسم الخادم المحدد
            Dim selectedDataBase As String = txtDataBaseName.SelectedItem.ToString()
            If DataBaseNameModule.RemoveDataBaseName(selectedDataBase) Then
                ' إعادة تحميل أسماء الخوادم
                LoadDataBaseName()
            End If
        Else
            MessageBox.Show("يرجى اختيار اسم قاعدة البيانات للحذف", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

End Class