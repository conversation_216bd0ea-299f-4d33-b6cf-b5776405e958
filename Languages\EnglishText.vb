﻿Imports System.Data.SqlClient
Public Class EnglishText

    '=========================================== Manu ====================================================
    Friend Items As String = "Items"
    Friend Purchases As String = "Purchases"
    Friend Sales As String = "Sales"
    Friend Returns As String = "Returns"
    Friend Expenses As String = "Expenses"
    Friend Employees As String = "Employees"
    Friend Delegates As String = "Delegate"
    Friend Maintenance As String = "Maintenance"
    Friend Manufacturing As String = "Manufacturing"
    Friend Banks As String = "Banks"
    Friend Accounts As String = "Accounts"
    Friend FinancialReports As String = "Financial Reports"
    Friend StoresReports As String = "Stores Reports"
    Friend ItemsReports As String = "Items Reports"
    Friend ReportsCertainPeriod As String = "Reports Certain Period"
    Friend UserReports As String = "User Reports"
    Friend Tools As String = "Tools"

    '=========================================== Items ====================================================
    Friend RegisterGroupsItems As String = "Register Groups Items"
    Friend RegisterBranchesItems As String = "Register Branches Items"
    Friend RegisterItems As String = "Register Items"
    Friend ModifiedItems As String = "Modified Items"
    Friend SearchItems As String = "Search Items"
    Friend StoreRegistration As String = "Store Registration"
    Friend ConvertBetweenStores As String = "Convert Between Stores"
    Friend MaxAndMinPriceItems As String = "Max And Min Price Items"
    Friend StoreAdjustments As String = "Store Adjustments"
    Friend OffersDiscounts As String = "Offers Discounts"
    Friend PriceProposal As String = "Price Proposal"
    Friend DelegatesManagement As String = "Delegates Management"
    Friend WithdrawalsAndDepositsOfPartners As String = "Withdrawals And Deposits Of Partners"
    Friend OtherIncome As String = "Other Income"
    Friend Treasury As String = "Treasury"
    Friend Exits As String = "Exit"


    Friend ProductManufacturing As String = "Product Manufacturing"
    Friend AmountDisabilityAndIncreaseDelegates As String = "Amount Disability And Increase Delegates"


    '=========================================== Purchases ====================================================
    Friend SupplierDataRegistration As String = "Supplier Data Registration"
    Friend PurchasesProcess As String = "Purchases Process"
    Friend ModifyAndDeletePurchases As String = "Modify And Delete Purchases"
    Friend ViewAndPrintPurchases As String = "View And Print Purchases"
    Friend ReportDailyPurchases As String = "Report Daily Purchases"
    Friend SupplierPayments As String = "Supplier Payments"
    Friend DeleteSupplierPayments As String = "Delete Supplier Payments"
    Friend SupplierDiscounts As String = "Supplier Discounts"
    Friend DeleteSupplierDiscounts As String = "Delete Supplier Discounts"

    '=========================================== Sales ====================================================
    Friend SalesScreen As String = "Sales Screen"
    Friend FastSales As String = "Fast Sales"
    Friend Sheft As String = "Sheft"
    Friend DeleteAndModifySales As String = "Delete And Modify Sales"
    Friend ViewAndPrintSales As String = "View And Print Sales"
    Friend DailySalesReport As String = "Daily Sales Report"
    Friend DetailedSalesReport As String = "Detailed Sales Report"
    Friend SalesPricesForTheCustomer As String = "Sales Prices For The Customer"
    Friend CustomerData As String = "Customer Data"
    Friend CustomerPayments As String = "Customer Payments"
    Friend ChecksPayable As String = "Checks Payable"
    Friend DeleteCustomerPayments As String = "Delete Customer Payments"
    Friend CustomerDiscounts As String = "Customer Discounts"
    Friend DeleteCustomerDiscounts As String = "Delete Customer Discounts"
    Friend OtherDiscounts As String = "Other Discounts"

    '=============== Returns ==============================
    Friend RecordPurchaseReturns As String = "Record purchase returns"
    Friend DeleteAndModifyPurchaseReturns As String = "Delete and modify purchase returns"
    Friend DefineAndPrintPurchaseReturns As String = "Define and print purchase returns"
    Friend DailyPurchaseReturnsReport As String = "Daily purchase returns report"
    Friend RecordSalesReturns As String = "Record sales returns"
    Friend DeleteAndModifySalesReturns As String = "Delete and modify sales returns"
    Friend DefineAndPrintSalesReturns As String = "Define and print sales returns"
    Friend DailySalesReturnsReport As String = "Daily sales returns report"
    Friend RecordDamages As String = "Record damages"
    Friend DefineAndDeleteDamageReturns As String = "Define and delete damage returns"

    '=========================================== Store ====================================================
    Friend RegisterStores As String = "Register Stores"
    Friend Store As String = "Store"
    Friend Stores As String = "Stores"
    Friend StoreType As String = "Store Type"
    Friend Main As String = "Main"
    Friend Subs As String = "Sub"
    Friend News As String = "New"
    Friend Save As String = "Save"
    Friend Edit As String = "Edit"
    Friend Delete As String = "Delete"
    Friend Added As String = "Add"
    Friend PleaseEnterValidData As String = "Please Enter Valid Data"
    Friend SorryRecordedStatementBy As String = "sorry recorded a statement by"
    Friend SuccessfullySaved As String = "Successfully Saved"
    Friend PleaseSelectValidStatement As String = "Please Select A Valid Statement"
    '=========================================== ADD ====================================================
    Friend HideInSearch As String = "Hide Search"
    Friend HideTheGroup As String = "Hide Group"
    Friend ChooseGroupPhoto As String = "Choose Group Photo"
    Friend GroupName As String = "Group Name"

    '===============================================================================================

    ' Main Headers
    Friend ItemDataInEnglish As String = "Item Data"
    Friend TodayInEnglish As String = "Today"
    Friend DateInEnglish As String = "Date"
    Friend TimeInEnglish As String = "Time"
    Friend ShfitInEnglish As String = "Shfit"

    ' Buttons and Actions
    Friend DisplayAndPrintSalesInEnglish As String = "Display and Print Sales"
    Friend AddDeleteSalesInEnglish As String = "Add and Delete Sales"
    Friend RegisterCustomerDataInEnglish As String = "Register Customer Data"
    Friend SalesReturnsInEnglish As String = "Sales Returns"
    Friend ApplyInvoiceInEnglish As String = "Apply Invoice"
    Friend PrintInvoiceInEnglish As String = "Print Invoice"
    Friend SelectPrinterInEnglish As String = "Select Printer"
    Friend DirectPrintInEnglish As String = "Direct Print"
    Friend PrintPreviewInEnglish As String = "Print Preview"

    ' Sales Form Fields
    Friend BarcodeInEnglish As String = "Barcode"
    Friend CustomerInEnglish As String = "Customer"
    Friend WarehouseInEnglish As String = "Warehouse"
    Friend TreasuryInEnglish As String = "Treasury"
    Friend RetailPriceInEnglish As String = "Retail Price"
    Friend WholesalePriceInEnglish As String = "Wholesale Price"
    Friend BulkPriceInEnglish As String = "Bulk Price"
    Friend CashInEnglish As String = "Cash"
    Friend CreditInEnglish As String = "Credit"
    Friend BankInEnglish As String = "Bank"
    Friend WalletInEnglish As String = "Wallet"
    Friend InvoiceNumberInEnglish As String = "Invoice Number"
    Friend EnterSalesInEnglish As String = "Enter Sales"
    Friend AnyWarehouseInEnglish As String = "Any Warehouse"
    Friend BarcodeSearchInEnglish As String = "Barcode Search"
    Friend GroupSearchInEnglish As String = "Group Search"

    ' Table Headers
    Friend GroupInEnglish As String = "Group"
    Friend ItemNameInEnglish As String = "Item Name"
    Friend DiscountInEnglish As String = "Discount"
    Friend PriceInEnglish As String = "Price"
    Friend QuantityInEnglish As String = "Quantity"
    Friend UnityInEnglish As String = "Unity"
    Friend TotalInEnglish As String = "Total"
    Friend CurrentQuantityInEnglish As String = "Current Quant"
    Friend ExpiryDateInEnglish As String = "Expiry Date"

    ' Search Options
    Friend SearchByNameInEnglish As String = "Search by Name"
    Friend SearchByPhoneInEnglish As String = "Search by Phone"
    Friend SearchByMobileInEnglish As String = "Search by Mobile"
    Friend SearchByCustomerCodeInEnglish As String = "Search by Customer Code"
    Friend SearchInItemsInEnglish As String = "Search in Items"

    ' Delivery Options
    Friend DeliveryOrdersInEnglish As String = "Delivery Orders"
    Friend CustomerAddressInEnglish As String = "Customer Address"
    Friend DeliveryServiceInEnglish As String = "Delivery Service"
    Friend DeliveryPersonInEnglish As String = "Delivery Person"
    Friend DeliveryPercentageInEnglish As String = "Driver Percentage"
    Friend DriverAmountInEnglish As String = "Driver Amount"

    ' Invoice Actions
    Friend AddOrderInEnglish As String = "Add Order"
    Friend SaveInvoiceInEnglish As String = "Save Invoice"
    Friend DirectInvoiceSaveInEnglish As String = "Direct Invoice Save"
    Friend PrintedInvoiceInEnglish As String = "Printed Invoice"
    Friend CustomInvoiceInEnglish As String = "Custom Invoice"


    ' Summary Fields
    Friend ItemCountInEnglish As String = "Item Count"
    Friend ItemQuantitiesInEnglish As String = "Item Quantities"
    Friend ExpensesInEnglish As String = "Expenses"
    Friend CustomerAccountInEnglish As String = "Customer Account"
    Friend MobileInEnglish As String = "Mobile"
    Friend RepresentativeInEnglish As String = "Representative"
    Friend NotesInEnglish As String = "Notes"

    ' Invoice Details
    Friend BeforeDiscountInEnglish As String = "Before Discount"
    Friend AfterDiscountInEnglish As String = "After Discount"
    Friend RemainingInEnglish As String = "Remaining"
    Friend PercentageInEnglish As String = "Percentage"
    Friend RateInEnglish As String = "Rate"
    Friend ValueInEnglish As String = "Value"
    Friend TaxValueInEnglish As String = "Tax Value"
    Friend VATDiscountInEnglish As String = "VAT Discount"
    Friend PaidInEnglish As String = "Paid"
    Friend AllWarehousesInEnglish As String = "All Warehouses"
    Friend ItemDiscountInEnglish As String = "Item Discount"
    Friend NumberItemsInEnglish As String = "Number Items"
    Friend QuantitiesItemsInEnglish As String = "Quantities Items"
    Friend DischargeInEnglish As String = "Discharge"

    '===============================================================================================
    Friend PerformingPurchasingOperationsInEnglish As String = "Performing Purchasing Operations"
    Friend PaidInCashInEnglish As String = "Paid in Cash"
    Friend VendorInEnglish As String = "Vendor"
    Friend SubCategoryInEnglish As String = "Sub Category"
    Friend PurchasePriceInEnglish As String = "Purchase Price"
    Friend PurchaseCostAvgInEnglish As String = "Purchase Cost Avg."
    Friend SellingPriceInEnglish As String = "Selling Price"
    Friend ReorderLimitInEnglish As String = "Reorder Limit"
    Friend CurrencyInEnglish As String = "Currency"
    Friend PurchaseEntryInEnglish As String = "Purchase Entry"
    Friend ReceivingVoucherInEnglish As String = "Receiving Voucher"
    Friend InvoiceImageInEnglish As String = "Invoice Image"
    Friend TotalItemsCountInEnglish As String = "Total Items Count"
    Friend SupplierAccountInEnglish As String = "Supplier Account"
    Friend PaperTypeInEnglish As String = "Paper Type"
    Friend InvoiceDateInEnglish As String = "Invoice Date"
    Friend ImportFromExcelInEnglish As String = "Import From Excel"
    '===============================================================================================

    Friend ItemCategoryInEnglish As String = "Item Category"
    Friend ItemSubcategoryInEnglish As String = "Item Subcategory"
    Friend ProductionDateInEnglish As String = "Production Date"
    Friend ExpiryTypeInEnglish As String = "Expiry Type"
    Friend PriceIncludesVATInEnglish As String = "Price Includes VAT"
    Friend ShowInSearchInEnglish As String = "Show in Search"
    Friend AutoBarcodeInEnglish As String = "Auto Barcode"
    Friend AddItemToAllStoresInEnglish As String = "Add Item To All Stores"
    Friend VATInEnglish As String = "VAT"
    Friend MinPriceLimitInEnglish As String = "MinPriceLimit"
    Friend PrintBarcodeInEnglish As String = "Print Barcode"
    Friend NumberInEnglish As String = "Number"
    Friend UnitSizeInEnglish As String = "Unit Size"
    Friend UnitBarcodeInEnglish As String = "Unit Barcode"
    Friend ItemImageInEnglish As String = "Item Image"
    Friend BarcodeScaleInEnglish As String = "Barcode Scale"
    Friend InventoryInEnglish As String = "Inventory"


End Class
