﻿Imports System.Data
Imports System.Data.SqlClient
Public Class Frm_Roles
    Dim C_Data As New Cls_Users
    Dim C_Validate As New Cls_Validation
    Dim c_Const As New Cls_Constant
    Dim Bool_Addnew As Boolean = False
    Dim Bool_Save As Boolean = False
    Dim Err_ As Int16
    Dim identity As Integer
    Dim per As New ArrayList
    Dim dr As SqlDataReader
    Dim Bool_ActionLoad As Boolean = False

    Private Sub Btn_Exit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Btn_Exit.Click
        Me.Dispose()
    End Sub

    Private Sub Btn_Save_Edit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Btn_Save_Edit.Click
        connect()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from Users where UserName =N'" & Txt_UserName.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H > 0 Then
            MsgBox(" أسم المستخدم مسجل مسبقا", MsgBoxStyle.Exclamation) : Txt_UserName.Focus() : Exit Sub
        End If
        If MessageBox.Show(Cls_Constant.Save_sure, Cls_Constant.Save_TitleMsg, MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading, False) = Windows.Forms.DialogResult.No Then
            Exit Sub
        End If
        save()
        ChkFalse()
        Check_All.Checked = False
        cmbCompanyBranchName.Text = "كل الفروع"
        Txt_UserName.Enabled = False
        Txt_PassWord.Enabled = False
        connect()
    End Sub

    Sub case1()
        C_Data.Sb_Sp_FillComboUsername(Cmb_UserName)
        Bool_Addnew = True
        Bool_Save = False
        Btn_Add.Text = "تراجع"
        Btn_Save_Edit.Enabled = True
        Cmb_UserName.Enabled = False
        Btn_Exit.Enabled = False
        'Grb_UserData.Enabled = True
        Grp_PermissionData.Enabled = True
        C_Validate.clean(Me)
        clre()
    End Sub

    Sub case2()
        Bool_Addnew = False
        Bool_Save = True
        Btn_Add.Text = "اضافة"
        Btn_Save_Edit.Enabled = False
        'Grb_UserData.Enabled = False
        Cmb_UserName.Enabled = True
        Btn_Exit.Enabled = True
        Grp_PermissionData.Enabled = False
        C_Validate.clean(Me)
        clre()
        C_Data.Sb_Sp_FillComboUsername(Cmb_UserName)
    End Sub

    Sub case3()
        Bool_Addnew = False
        Bool_Save = True
        Btn_Add.Text = "اضافة"
        Btn_Exit.Enabled = True
        Cmb_UserName.Enabled = True
        Btn_Save_Edit.Enabled = False
    End Sub

    Private Sub Btn_Add_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Btn_Add.Click
        add()
        ChkFalse()
        Check_All.Checked = False
        cmbCompanyBranchName.Text = "كل الفروع"
        Txt_UserName.Enabled = True
        Txt_PassWord.Enabled = True
        connect()
    End Sub

    Sub add()
        If Bool_Addnew = False Then
            case1()
        Else
            case2()
        End If
    End Sub

    Sub save()
        validate_()
        If Err_ = 1 Then
            Exit Sub
        End If
        'Dim Recase As Integer = 0
        'If Chk_Admin.Checked = False Then
        '    Recase = 1
        'Else
        '    Recase = 0
        'End If

        Dim Branch_ID As String
        Dim Stores_ID As String
        Dim Treasury_Code As String
        Dim Treasury_Code_Transfer As String

        If cmbCompanyBranchName.Text = "كل الفروع" Then
            Branch_ID = 0
        Else
            Branch_ID = Cls.Get_Code_Value_Branch("Company_Branch", "Company_Branch_ID", "Company_Branch_Name", cmbCompanyBranchName.Text)
        End If

        Stores_ID = Cls.Get_Code_Value_Branch("Stores", "id", "store", cmbStores.Text)
        Treasury_Code = Cls.Get_Code_Value_Branch("Treasury", "Treasury_Code", "Treasury_Name", cmbTreasury.Text)
        Treasury_Code_Transfer = Cls.Get_Code_Value_Branch("Treasury", "Treasury_Code", "Treasury_Name", cmbTransferTreasury.Text)

        Cls.insert("Users", "Company_Branch_ID,UserName,UserPassword,Recase,Stores_ID,Treasury_Code,Treasury_Code_Transfer", "N'" & Branch_ID & "',N'" & Txt_UserName.Text & "',N'" & Txt_PassWord.Text & "',N'1',N'" & Stores_ID & "',N'" & Treasury_Code & "',N'" & Treasury_Code_Transfer & "'")

        Dim UserID As String = Cls.Get_Code_Value_Branch("Users", "UserID", "UserName", Txt_UserName.Text)

        For index As Integer = 0 To LstPermtion.Items.Count - 1
            Cls.insert("Permtions", "Company_Branch_ID,UserID,PermtionName", "N'" & Branch_ID & "',N'" & UserID & "',N'" & LstPermtion.Items(index).ToString() & "'")
        Next

        'identity = C_Data.fn_Sp_InsertUser(Txt_UserName.Text, Txt_PassWord.Text)
        'For index As Integer = 0 To LstPermtion.Items.Count - 1
        '    'C_Data.fn_Sp_InsertPermation(identity, LstPermtion.Items(index).ToString())
        'Next
        case2()
    End Sub

    Sub validate_()
        ErrProvid_TextValidation.Clear()
        Err_ = 0
        If Txt_PassWord.Text = "" Then
            ErrProvid_TextValidation.SetError(Txt_PassWord, "يجب ادخال كلمة مرور ")
            Err_ = 1
        End If
        If Txt_PassWord.Text.Length < 4 Then
            ErrProvid_TextValidation.SetError(Txt_PassWord, "لابد ان لا تقل كلمة المرور عن اربعة احرف")
            Err_ = 1
        End If
        If Txt_UserName.Text = "" Then
            ErrProvid_TextValidation.SetError(Txt_UserName, "يجب ادخال اسم المستخدم")
            Err_ = 1
        End If
    End Sub

    Private Sub LstPermtion_MouseDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles LstPermtion.MouseDoubleClick
        Try
            LstPermtion.Items.RemoveAt(LstPermtion.SelectedIndex)
        Catch ex As Exception

        End Try
    End Sub

    Sub clre()
        Chk_Debts.Checked = False
        Chk_Admin.Checked = False
        Chk_Expenses.Checked = False
        Chk_FastBill.Checked = False
        Chk_InsertData.Checked = False
        Chk_Purchase.Checked = False
        Chk_Reports.Checked = False
        Chk_Sell.Checked = False
        LstPermtion.Items.Clear()
    End Sub

    Private Sub Frm_Roles_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        'Cls.fill_combo("Users", "UserName", Cmb_UserName)

        C_Data.Sb_Sp_FillComboUsername(Cmb_UserName)
        Bra.Fil("Company_Branch", "Company_Branch_Name", cmbCompanyBranchName)
        cmbCompanyBranchName.Items.AddRange(New Object() {"كل الفروع"})
        cmbCompanyBranchName.Text = "كل الفروع"
        'cmbStores.Items.AddRange(New Object() {""})
        'cmbTreasury.Items.AddRange(New Object() {""})
        Bra.Fil("Stores", "store", cmbStores)
        Bra.Fil("Treasury", "Treasury_Name", cmbTreasury)
        Bra.Fil("Treasury", "Treasury_Name", cmbTransferTreasury)
        cmbStores.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        cmbTreasury.Text = mykey.GetValue("TreasuryName", "الخزينة الرئيسية")
        cmbTransferTreasury.Text = mykey.GetValue("TreasuryName", "الخزينة الرئيسية")

        'Me.MdiParent = Frm_Home
        Bool_ActionLoad = True
        PanelCompanyBranchName.Top = 5000

        'Dim Treasury_Code As String = Cls.Get_Code_Value_Branch("Users", "Treasury_Code", "UserName", Txt_UserName.Text)
        'cmbTreasury.Text = Cls.Get_Code_Value_Branch("Treasury", "Treasury_Name", "Treasury_Code", Treasury_Code)

        'Dim Stores_ID As String = Cls.Get_Code_Value_Branch("Users", "Stores_ID", "UserName", Txt_UserName.Text)
        'cmbStores.Text = Cls.Get_Code_Value_Branch("Stores", "store", "id", Stores_ID)
    End Sub

    Private Sub Chk_Admin_CheckedChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Chk_Admin.CheckedChanged
        If Chk_Admin.Checked = True Then
            LstPermtion.Items.Add(Chk_Admin.Text)

            Check_All.Enabled = False
            Grb_BasicData.Enabled = False
            GroupBox2.Enabled = False
            GroupBox8.Enabled = False
            GroupBox5.Enabled = False
            Grb_OprationData.Enabled = False
            GroupBox6.Enabled = False
            Grb_Salary.Enabled = False
            GroupBox7.Enabled = False
            GroupBox4.Enabled = False
            Grb_Expanses.Enabled = False
            GroupBox1.Enabled = False
        End If
        If Chk_Admin.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مدير" Then

                    LstPermtion.Items.RemoveAt(index)
                End If
                'MsgBox(LstPermtion.Items(index).ToString())
            Next
            LstPermtion.Items.Clear()
            Check_All.Enabled = True
            Grb_BasicData.Enabled = True
            GroupBox2.Enabled = True
            GroupBox8.Enabled = True
            GroupBox5.Enabled = True
            Grb_OprationData.Enabled = True
            GroupBox6.Enabled = True
            Grb_Salary.Enabled = True
            GroupBox7.Enabled = True
            GroupBox4.Enabled = True
            Grb_Expanses.Enabled = True
            GroupBox1.Enabled = True
        End If
    End Sub

    Private Sub Chk_InsertData_CheckedChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Chk_InsertData.CheckedChanged
        On Error Resume Next

        If Chk_InsertData.Checked = True Then
            LstPermtion.Items.Add("تسجيل مجموعات الأصناف")
        End If
        If Chk_InsertData.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تسجيل مجموعات الأصناف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub Chk_Sell_CheckedChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Chk_Sell.CheckedChanged
        On Error Resume Next
        If Chk_Sell.Checked = True Then
            LstPermtion.Items.Add("تسجيل  أصناف")
        End If
        If Chk_Sell.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تسجيل  أصناف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub Chk_FastBill_CheckedChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Chk_FastBill.CheckedChanged
        On Error Resume Next
        If Chk_FastBill.Checked = True Then
            LstPermtion.Items.Add("تسجيل بيانات موردين")
        End If
        If Chk_FastBill.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تسجيل بيانات موردين" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub Chk_Purchase_CheckedChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Chk_Purchase.CheckedChanged
        On Error Resume Next
        If Chk_Purchase.Checked = True Then
            LstPermtion.Items.Add("خصومات الموردين")
        End If
        If Chk_Purchase.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "خصومات الموردين" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub Chk_Debts_CheckedChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Chk_Debts.CheckedChanged
        On Error Resume Next
        If Chk_Debts.Checked = True Then
            LstPermtion.Items.Add("إجراء عملية مشتريات")
        End If
        If Chk_Debts.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "إجراء عملية مشتريات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub Chk_Expenses_CheckedChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Chk_Expenses.CheckedChanged
        On Error Resume Next
        If Chk_Expenses.Checked = True Then
            LstPermtion.Items.Add("تقرير حد الطلب للصنف")
        End If
        If Chk_Expenses.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تقرير حد الطلب للصنف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub Chk_Reports_CheckedChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Chk_Reports.CheckedChanged
        On Error Resume Next
        If Chk_Reports.Checked = True Then
            LstPermtion.Items.Add(Chk_Reports.Text)
        End If
        If Chk_Reports.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تقرير أصناف نفذت" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub Btn_Delete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Btn_Delete.Click
        On Error Resume Next
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        C_Data.Sb_Sp_DeleteUsers(Cmb_UserName.SelectedValue)
        case2()
        Txt_UserName.Enabled = False
        Txt_PassWord.Enabled = False
        cmbCompanyBranchName.Text = "كل الفروع"
        connect()
    End Sub

    Private Sub Chk_SearchApsent_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Chk_SearchApsent.CheckedChanged
        On Error Resume Next
        If Chk_SearchApsent.Checked = True Then
            LstPermtion.Items.Add(Chk_SearchApsent.Text)
        End If
        If Chk_SearchApsent.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تقرير ارصدة الاصناف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox1.CheckedChanged
        On Error Resume Next
        If CheckBox1.Checked = True Then
            LstPermtion.Items.Add(CheckBox1.Text)
        End If
        If CheckBox1.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "بيانات صنف كاملة" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox2_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox2.CheckedChanged
        On Error Resume Next
        If CheckBox2.Checked = True Then
            LstPermtion.Items.Add(CheckBox2.Text)
        End If
        If CheckBox2.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مشتريات صنف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox3_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox3.CheckedChanged
        On Error Resume Next
        If CheckBox3.Checked = True Then
            LstPermtion.Items.Add(CheckBox3.Text)
        End If
        If CheckBox3.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مبيعات صنف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox6_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox6.CheckedChanged
        On Error Resume Next
        If CheckBox6.Checked = True Then
            LstPermtion.Items.Add(CheckBox6.Text)
        End If
        If CheckBox6.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مرتجعات مشتريات صنف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox5_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox5.CheckedChanged
        On Error Resume Next
        If CheckBox5.Checked = True Then
            LstPermtion.Items.Add(CheckBox5.Text)
        End If
        If CheckBox5.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مرتجعات مبيعات صنف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox4_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox4.CheckedChanged
        On Error Resume Next
        If CheckBox4.Checked = True Then
            LstPermtion.Items.Add(CheckBox4.Text)
        End If
        If CheckBox4.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "أرباح صنف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox7_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox7.CheckedChanged
        On Error Resume Next
        If CheckBox7.Checked = True Then
            LstPermtion.Items.Add(CheckBox7.Text)
        End If
        If CheckBox7.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تسجيل مصروفات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox9_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox9.CheckedChanged
        On Error Resume Next
        If CheckBox9.Checked = True Then
            LstPermtion.Items.Add(CheckBox9.Text)
        End If
        If CheckBox9.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "عرض المصروفات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox8_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox8.CheckedChanged
        On Error Resume Next
        If CheckBox8.Checked = True Then
            LstPermtion.Items.Add(CheckBox8.Text)
        End If
        If CheckBox8.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مشتريات فترة معينة" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox10_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox10.CheckedChanged
        On Error Resume Next
        If CheckBox10.Checked = True Then
            LstPermtion.Items.Add(CheckBox10.Text)
        End If
        If CheckBox10.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مدفوعات موردين" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox12_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox12.CheckedChanged
        On Error Resume Next
        If CheckBox12.Checked = True Then
            LstPermtion.Items.Add(CheckBox12.Text)
        End If
        If CheckBox12.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "كشف حساب مورد" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox11_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox11.CheckedChanged
        On Error Resume Next
        If CheckBox11.Checked = True Then
            LstPermtion.Items.Add(CheckBox11.Text)
        End If
        If CheckBox11.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مبيعات فترة معينة" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox18_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox18.CheckedChanged
        On Error Resume Next

        If CheckBox18.Checked = True Then
            LstPermtion.Items.Add(CheckBox18.Text)
        End If
        If CheckBox18.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = " حفظ واسترجاع نسخه من قاعدة البيانات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox14_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox14.CheckedChanged
        On Error Resume Next

        If CheckBox14.Checked = True Then
            LstPermtion.Items.Add(CheckBox14.Text)
        End If
        If CheckBox14.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مرتجع مشتريات فترة معينة" Then

                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox16_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox16.CheckedChanged
        On Error Resume Next
        If CheckBox16.Checked = True Then
            LstPermtion.Items.Add(CheckBox16.Text)
        End If
        If CheckBox16.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مرتجع مبيعات فترة معينة" Then

                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox19_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox19.CheckedChanged
        On Error Resume Next
        If CheckBox19.Checked = True Then
            LstPermtion.Items.Add(CheckBox19.Text)
        End If
        If CheckBox19.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تقرير الارباح والخسائر" Then

                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox20_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox20.CheckedChanged
        On Error Resume Next
        If CheckBox20.Checked = True Then
            LstPermtion.Items.Add(CheckBox20.Text)
        End If
        If CheckBox20.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "الحركة المالية لصنف" Then

                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox21_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox21.CheckedChanged
        On Error Resume Next
        If CheckBox21.Checked = True Then
            LstPermtion.Items.Add(CheckBox21.Text)
        End If
        If CheckBox21.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "حركة الخزينة" Then

                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox22_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox22.CheckedChanged
        On Error Resume Next
        If CheckBox22.Checked = True Then
            LstPermtion.Items.Add(CheckBox22.Text)
        End If
        If CheckBox22.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "طباعة الباركود" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox23_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox23.CheckedChanged
        On Error Resume Next
        If CheckBox23.Checked = True Then
            LstPermtion.Items.Add(CheckBox23.Text)
        End If
        If CheckBox23.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مشتريات مورد" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox24_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox24.CheckedChanged
        On Error Resume Next
        If CheckBox24.Checked = True Then
            LstPermtion.Items.Add(CheckBox24.Text)
        End If
        If CheckBox24.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مبيعات عميل" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox25_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox25.CheckedChanged
        On Error Resume Next
        If CheckBox25.Checked = True Then
            LstPermtion.Items.Add(CheckBox25.Text)
        End If
        If CheckBox25.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مرتجع مشتريات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox26_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox26.CheckedChanged
        On Error Resume Next
        If CheckBox26.Checked = True Then
            LstPermtion.Items.Add(CheckBox26.Text)
        End If
        If CheckBox26.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مرتجع مبيعات عميل" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox27_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox27.CheckedChanged
        On Error Resume Next
        If CheckBox27.Checked = True Then
            LstPermtion.Items.Add(CheckBox27.Text)
        End If
        If CheckBox27.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مدفوعات مورد" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox28_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox28.CheckedChanged
        On Error Resume Next
        If CheckBox28.Checked = True Then
            LstPermtion.Items.Add(CheckBox28.Text)
        End If
        If CheckBox28.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مصروفات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox29_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox29.CheckedChanged
        On Error Resume Next
        If CheckBox29.Checked = True Then
            LstPermtion.Items.Add(CheckBox29.Text)
        End If
        If CheckBox29.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "خصومات مورد" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox30_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox30.CheckedChanged
        On Error Resume Next
        If CheckBox30.Checked = True Then
            LstPermtion.Items.Add(CheckBox30.Text)
        End If
        If CheckBox30.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تسجيل بنود" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox31_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub Chk_Sales_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Chk_Sales.CheckedChanged
        On Error Resume Next

        If Chk_Sales.Checked = True Then
            LstPermtion.Items.Add(Chk_Sales.Text)
        End If
        If Chk_Sales.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "المبيعات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If

    End Sub

    Private Sub Chk_CloseEdit_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Chk_CloseEdit.CheckedChanged
        On Error Resume Next
        If Chk_CloseEdit.Checked = True Then
            LstPermtion.Items.Add("تعديل أصناف")
        End If
        If Chk_CloseEdit.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تعديل أصناف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If

    End Sub

    Private Sub CheckBox31_CheckedChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox31.CheckedChanged
        On Error Resume Next

        If CheckBox31.Checked = True Then
            LstPermtion.Items.Add(CheckBox31.Text)
        End If
        If CheckBox31.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مدفوعات الموردين" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If

    End Sub

    Private Sub CheckBox34_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox34.CheckedChanged
        On Error Resume Next
        If CheckBox34.Checked = True Then
            LstPermtion.Items.Add(CheckBox34.Text)
        End If
        If CheckBox34.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "بيانات العملاء" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox35_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox35.CheckedChanged
        On Error Resume Next
        If CheckBox35.Checked = True Then
            LstPermtion.Items.Add(CheckBox35.Text)
        End If
        If CheckBox35.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مقبوضات العملاء" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If


    End Sub

    Private Sub CheckBox37_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox37.CheckedChanged
        On Error Resume Next
        If CheckBox37.Checked = True Then
            LstPermtion.Items.Add(CheckBox37.Text)
        End If
        If CheckBox37.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "كشف حساب عميل" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox38_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox38.CheckedChanged
        On Error Resume Next
        If CheckBox38.Checked = True Then
            LstPermtion.Items.Add(CheckBox38.Text)
        End If
        If CheckBox38.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "خصومات العملاء" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox32_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox32.CheckedChanged
        On Error Resume Next
        If CheckBox32.Checked = True Then
            LstPermtion.Items.Add(CheckBox32.Text)
        End If
        If CheckBox32.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "حذف وتعديل المبيعات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox33_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox33.CheckedChanged
        On Error Resume Next
        If CheckBox33.Checked = True Then
            LstPermtion.Items.Add(CheckBox33.Text)
        End If
        If CheckBox33.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "عرض وطباعة المبيعات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox36_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox36.CheckedChanged
        On Error Resume Next
        If CheckBox36.Checked = True Then
            LstPermtion.Items.Add(CheckBox36.Text)
        End If
        If CheckBox36.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تسجيل مرتجعات مشتريات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox39_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox39.CheckedChanged
        On Error Resume Next
        If CheckBox39.Checked = True Then
            LstPermtion.Items.Add(CheckBox39.Text)
        End If
        If CheckBox39.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مرتجعات مبيعات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox40_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Check_All.CheckedChanged
        If Check_All.Checked = True Then
            chkAccounts.Checked = True
            chkEmployees.Checked = True
            chkBanks.Checked = True
            chkDelegate.Checked = True
            chkMaintenance.Checked = True
            chkManufacturing.Checked = True
            Chk_Expenses.Checked = True
            Chk_Reports.Checked = True
            Chk_SearchApsent.Checked = True
            Chk_Sales.Checked = True
            Chk_InsertData.Checked = True
            Chk_Sell.Checked = True
            Chk_CloseEdit.Checked = True
            Chk_FastBill.Checked = True
            Chk_Debts.Checked = True
            Chk_Purchase.Checked = True
            CheckBox1.Checked = True
            CheckBox2.Checked = True
            CheckBox3.Checked = True
            CheckBox4.Checked = True
            CheckBox5.Checked = True
            CheckBox6.Checked = True
            CheckBox7.Checked = True
            CheckBox8.Checked = True
            CheckBox9.Checked = True
            CheckBox10.Checked = True
            CheckBox11.Checked = True
            CheckBox12.Checked = True
            CheckBox14.Checked = True
            CheckBox15.Checked = True
            CheckBox16.Checked = True
            CheckBox17.Checked = True
            CheckBox18.Checked = True
            CheckBox19.Checked = True
            CheckBox20.Checked = True
            CheckBox21.Checked = True
            CheckBox22.Checked = True
            CheckBox23.Checked = True
            CheckBox24.Checked = True
            CheckBox25.Checked = True
            CheckBox26.Checked = True
            CheckBox27.Checked = True
            CheckBox28.Checked = True
            CheckBox29.Checked = True
            CheckBox30.Checked = True
            CheckBox31.Checked = True
            CheckBox32.Checked = True
            CheckBox33.Checked = True
            CheckBox34.Checked = True
            CheckBox35.Checked = True
            CheckBox36.Checked = True
            CheckBox37.Checked = True
            CheckBox38.Checked = True
            CheckBox39.Checked = True
            CheckBox13.Checked = True
            CheckBox41.Checked = True
            CheckBox44.Checked = True
            CheckBox45.Checked = True
            CheckBox42.Checked = True
            CheckBox43.Checked = True
            CheckBox46.Checked = True
            CheckBox47.Checked = True
            CheckBox48.Checked = True
            CheckBox49.Checked = True
            CheckBox40.Checked = True
            CheckBox50.Checked = True
            CheckBox51.Checked = True
            CheckBox52.Checked = True
            CheckBox53.Checked = True
            CheckBox54.Checked = True
            CheckBox55.Checked = True
            CheckBox56.Checked = True
            CheckBox57.Checked = True
            CheckBox58.Checked = True
            CheckBox59.Checked = True
            CheckBox60.Checked = True
            CheckBox61.Checked = True
            CheckBox62.Checked = True
            CheckBox63.Checked = True
            CheckBox64.Checked = True
            chkEmployees.Checked = True
            chkDelegate.Checked = True
            CheckBox67.Checked = True
            CheckBox68.Checked = True
            CheckBox69.Checked = True
            CheckBox70.Checked = True
            CheckBox71.Checked = True
            CheckBox72.Checked = True
            CheckBox73.Checked = True
            CheckBox74.Checked = True
            CheckBox75.Checked = True
            CheckBox76.Checked = True
            CheckBox77.Checked = True
            CheckBox78.Checked = True
            CheckBox79.Checked = True
            CheckBox80.Checked = True
            CheckBox81.Checked = True
            CheckBox82.Checked = True
            CheckBox83.Checked = True
            CheckBox84.Checked = True
            CheckBox85.Checked = True
            CheckBox65.Checked = True
            CheckBox66.Checked = True
            CheckBox86.Checked = True
            CheckBox65.Checked = True
            CheckBox66.Checked = True
            CheckBox87.Checked = True
            CheckBox88.Checked = True
            CheckBox89.Checked = True
            CheckBox90.Checked = True
            Chk_Admin.Enabled = False
        Else
            Chk_Admin.Enabled = True
            ChkFalse()
        End If
    End Sub

    Sub ChkFalse()
        LstPermtion.Items.Clear()
        Chk_Expenses.Checked = False
        Chk_Reports.Checked = False
        Chk_SearchApsent.Checked = False
        Chk_Sales.Checked = False
        Chk_InsertData.Checked = False
        Chk_Sell.Checked = False
        Chk_CloseEdit.Checked = False
        Chk_FastBill.Checked = False
        Chk_Debts.Checked = False
        Chk_Purchase.Checked = False
        CheckBox1.Checked = False
        CheckBox2.Checked = False
        CheckBox3.Checked = False
        CheckBox4.Checked = False
        CheckBox5.Checked = False
        CheckBox6.Checked = False
        CheckBox7.Checked = False
        CheckBox8.Checked = False
        CheckBox9.Checked = False
        CheckBox10.Checked = False
        CheckBox11.Checked = False
        CheckBox12.Checked = False
        CheckBox14.Checked = False
        CheckBox15.Checked = False
        CheckBox16.Checked = False
        CheckBox17.Checked = False
        CheckBox18.Checked = False
        CheckBox19.Checked = False
        CheckBox20.Checked = False
        CheckBox21.Checked = False
        CheckBox22.Checked = False
        CheckBox23.Checked = False
        CheckBox24.Checked = False
        CheckBox25.Checked = False
        CheckBox26.Checked = False
        CheckBox27.Checked = False
        CheckBox28.Checked = False
        CheckBox29.Checked = False
        CheckBox30.Checked = False
        CheckBox31.Checked = False
        CheckBox32.Checked = False
        CheckBox33.Checked = False
        CheckBox34.Checked = False
        CheckBox35.Checked = False
        CheckBox36.Checked = False
        CheckBox37.Checked = False
        CheckBox38.Checked = False
        CheckBox39.Checked = False
        CheckBox13.Checked = False
        CheckBox41.Checked = False
        CheckBox44.Checked = False
        CheckBox45.Checked = False
        CheckBox42.Checked = False
        CheckBox43.Checked = False
        CheckBox46.Checked = False
        CheckBox47.Checked = False
        CheckBox48.Checked = False
        CheckBox49.Checked = False
        CheckBox40.Checked = False
        CheckBox50.Checked = False
        CheckBox51.Checked = False
        CheckBox51.Checked = False
        CheckBox52.Checked = False
        CheckBox53.Checked = False
        CheckBox54.Checked = False
        CheckBox55.Checked = False
        CheckBox56.Checked = False
        CheckBox57.Checked = False
        CheckBox58.Checked = False
        CheckBox58.Checked = False
        CheckBox59.Checked = False
        CheckBox60.Checked = False
        CheckBox61.Checked = False
        CheckBox62.Checked = False
        CheckBox63.Checked = False
        CheckBox64.Checked = False
        CheckBox67.Checked = False
        CheckBox68.Checked = False
        CheckBox69.Checked = False
        CheckBox70.Checked = False
        CheckBox71.Checked = False
        CheckBox72.Checked = False
        CheckBox73.Checked = False
        CheckBox74.Checked = False
        CheckBox75.Checked = False
        CheckBox76.Checked = False
        CheckBox77.Checked = False
        CheckBox78.Checked = False
        CheckBox79.Checked = False
        CheckBox80.Checked = False
        CheckBox81.Checked = False
        CheckBox82.Checked = False
        CheckBox83.Checked = False
        CheckBox84.Checked = False
        CheckBox85.Checked = False
        chkAccounts.Checked = False
        chkEmployees.Checked = False
        chkBanks.Checked = False
        chkDelegate.Checked = False
        chkMaintenance.Checked = False
        chkManufacturing.Checked = False
        CheckBox65.Checked = False
        CheckBox66.Checked = False
        CheckBox86.Checked = False
        CheckBox65.Checked = False
        CheckBox66.Checked = False
        CheckBox87.Checked = False
        CheckBox88.Checked = False
        CheckBox89.Checked = False
        CheckBox90.Checked = False

    End Sub

    Private Sub CheckBox40_CheckedChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox40.CheckedChanged
        On Error Resume Next
        If CheckBox40.Checked = True Then
            LstPermtion.Items.Add(CheckBox40.Text)
        End If
        If CheckBox40.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مقبوضات عملاء" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub


    Private Sub CheckBox13_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox13.CheckedChanged
        On Error Resume Next
        If CheckBox13.Checked = True Then
            LstPermtion.Items.Add(CheckBox13.Text)
        End If
        If CheckBox13.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تعديل وحذف عمليات المشتريات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox41_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox41.CheckedChanged
        On Error Resume Next
        If CheckBox41.Checked = True Then
            LstPermtion.Items.Add(CheckBox41.Text)
        End If
        If CheckBox41.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "عرض وطباعة عمليات المشتريات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox42_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox42.CheckedChanged
        On Error Resume Next
        If CheckBox42.Checked = True Then
            LstPermtion.Items.Add(CheckBox42.Text)
        End If
        If CheckBox42.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "حـذف مقبوضات العملاء" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox43_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox43.CheckedChanged
        On Error Resume Next
        If CheckBox43.Checked = True Then
            LstPermtion.Items.Add(CheckBox43.Text)
        End If
        If CheckBox43.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "حـذف خصومات العملاء" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox44_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox44.CheckedChanged
        On Error Resume Next
        If CheckBox44.Checked = True Then
            LstPermtion.Items.Add(CheckBox44.Text)
        End If
        If CheckBox44.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "حذف مدفوعات الموردين" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox45_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox45.CheckedChanged
        On Error Resume Next
        If CheckBox45.Checked = True Then
            LstPermtion.Items.Add(CheckBox45.Text)
        End If
        If CheckBox45.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "حذف خصومات الموردين" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox46_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox46.CheckedChanged
        On Error Resume Next
        If CheckBox46.Checked = True Then
            LstPermtion.Items.Add(CheckBox46.Text)
        End If
        If CheckBox46.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "حذف وتعديل مرتجعات مشتريات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox47_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox47.CheckedChanged
        On Error Resume Next
        If CheckBox47.Checked = True Then
            LstPermtion.Items.Add(CheckBox47.Text)
        End If
        If CheckBox47.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "عرض وطباعة مرتجعات مشتريات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub LinkLabel1_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles LinkLabel1.LinkClicked
        LstPermtion.Items.Clear()
    End Sub

    Private Sub CheckBox48_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox48.CheckedChanged
        On Error Resume Next
        If CheckBox48.Checked = True Then
            LstPermtion.Items.Add(CheckBox48.Text)
        End If
        If CheckBox48.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "حذف وتعديل مرتجعات مبيعات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox49_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox49.CheckedChanged
        On Error Resume Next
        If CheckBox49.Checked = True Then
            LstPermtion.Items.Add(CheckBox49.Text)
        End If
        If CheckBox49.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "عرض وطباعة مرتجعات مبيعات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox50_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox50.CheckedChanged
        On Error Resume Next
        If CheckBox50.Checked = True Then
            LstPermtion.Items.Add("بحث عن الاصناف")
        End If
        If CheckBox50.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "بحث عن الاصناف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox51_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox51.CheckedChanged
        On Error Resume Next
        If CheckBox51.Checked = True Then
            LstPermtion.Items.Add("تقرير بالمبيعات اليومية")
        End If
        If CheckBox51.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تقرير بالمبيعات اليومية" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox55_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox55.CheckedChanged
        On Error Resume Next
        If CheckBox55.Checked = True Then
            LstPermtion.Items.Add(CheckBox55.Text)
        End If
        If CheckBox55.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "دخول بضائع المخازن" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox56_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox56.CheckedChanged
        On Error Resume Next
        If CheckBox56.Checked = True Then
            LstPermtion.Items.Add(CheckBox56.Text)
        End If
        If CheckBox56.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "خروج بضائع من المخازن" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox57_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox57.CheckedChanged
        On Error Resume Next
        If CheckBox57.Checked = True Then
            LstPermtion.Items.Add(CheckBox57.Text)
        End If
        If CheckBox56.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مدفوعات عميل" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox58_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox58.CheckedChanged
        On Error Resume Next
        If CheckBox58.Checked = True Then
            LstPermtion.Items.Add(CheckBox58.Text)
        End If
        If CheckBox56.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "خصومات العملاء" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub chkPassword_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkPassword.CheckedChanged
        If chkPassword.Checked = True Then
            Txt_PassWord.PasswordChar = ""
        Else
            Txt_PassWord.PasswordChar = "*"
        End If
    End Sub

    Private Sub btnEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnEdit.Click
        connect()
        Dim Stores_ID As String = ""
        Dim Treasury_Code As String = ""
        Dim Treasury_Code_Transfer As String = ""

        Stores_ID = Cls.Get_Code_Value_Branch("Stores", "id", "store", cmbStores.Text)
        Treasury_Code = Cls.Get_Code_Value_Branch("Treasury", "Treasury_Code", "Treasury_Name", cmbTreasury.Text)
        Treasury_Code_Transfer = Cls.Get_Code_Value_Branch("Treasury", "Treasury_Code", "Treasury_Name", cmbTransferTreasury.Text)

        connect()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Users set UserPassword = N'" & Txt_PassWord.Text.Trim & "',Stores_ID = N'" & Stores_ID & "',Treasury_Code = N'" & Treasury_Code & "',Treasury_Code_Transfer = N'" & Treasury_Code_Transfer & "' where UserName =N'" & Txt_UserName.Text & "'"
        cmd.ExecuteNonQuery()

        Dim UserID As String = Cls.Get_Code_Value_Branch("Users", "UserID", "UserName", Txt_UserName.Text)

        connect()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete from Permtions where UserID =N'" & UserID & "'" : cmd.ExecuteNonQuery()


        Dim Branch_ID As String
        If Chk_Admin.Checked = True Then
            Branch_ID = 0
        Else
            If cmbCompanyBranchName.Text = "كل الفروع" Then
                Branch_ID = 0
            Else
                Branch_ID = Cls.Get_Code_Value_Branch("Company_Branch", "Company_Branch_ID", "Company_Branch_Name", cmbCompanyBranchName.Text)
            End If
        End If
        For index As Integer = 0 To LstPermtion.Items.Count - 1
            Cls.insert("Permtions", "Company_Branch_ID,UserID,PermtionName", "N'" & Branch_ID & "',N'" & UserID & "',N'" & LstPermtion.Items(index).ToString() & "'")
        Next

        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        case2()
        Btn_Delete.Enabled = False
        btnEdit.Enabled = False
        Txt_UserName.Enabled = False
        Txt_PassWord.Enabled = False
        cmbCompanyBranchName.Text = "كل الفروع"
        connect()
    End Sub

    Private Sub Cmb_UserName_SelectedIndexChanged(sender As Object, e As EventArgs) Handles Cmb_UserName.SelectedIndexChanged

        Grp_PermissionData.Enabled = True
        C_Validate.clean(Me)
        clre()
        ChkFalse()
        Check_All.Checked = False

        If Cmb_UserName.Text = "" Then
            Exit Sub
        Else
            'On Error Resume Next
            Txt_UserName.Text = Cls.Get_Code_Value("Users", "UserName", "UserName", Cmb_UserName.Text)
            Txt_PassWord.Text = Cls.Get_Code_Value("Users", "UserPassword", "UserName", Cmb_UserName.Text)
            Dim Branch_ID As String = Cls.Get_Code_Value("Users", "Company_Branch_ID", "UserName", Cmb_UserName.Text)
            cmbCompanyBranchName.Text = Cls.Get_Code_Value("Company_Branch", "Company_Branch_Name", "Company_Branch_ID", Branch_ID)
            Dim Stores_ID As String = Cls.Get_Code_Value("Users", "Stores_ID", "UserName", Cmb_UserName.Text)
            cmbStores.Text = Cls.Get_Code_Value("Stores", "store", "id", Stores_ID)

            Dim Treasury_Code As String = Cls.Get_Code_Value("Users", "Treasury_Code", "UserName", Cmb_UserName.Text)
            cmbTreasury.Text = Cls.Get_Code_Value("Treasury", "Treasury_Name", "Treasury_Code", Treasury_Code)

            Dim Treasury_Code_Transfer As String = Cls.Get_Code_Value("Users", "Treasury_Code_Transfer", "UserName", Cmb_UserName.Text)
            cmbTransferTreasury.Text = Cls.Get_Code_Value("Treasury", "Treasury_Name", "Treasury_Code", Val(Treasury_Code_Transfer))


            'dr = C_Data.fn_Sp_SelectUser(Cmb_UserName.SelectedValue)
            'Txt_UserName.Text = dr(0)
            'Txt_PassWord.Text = dr(1)
            Try
                C_Data.fn_Sp_Selectpermtion(Cmb_UserName.SelectedValue, Me)
            Catch ex As Exception
            End Try
            Btn_Delete.Enabled = True
            btnEdit.Enabled = True
            Txt_UserName.Enabled = True
            Txt_PassWord.Enabled = True

            GetDataPermtionName()
        End If
        connect()
    End Sub

    Private Sub CheckBox52_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox52.CheckedChanged
        On Error Resume Next
        If CheckBox52.Checked = True Then
            LstPermtion.Items.Add(CheckBox52.Text)
        End If
        If CheckBox52.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تقرير بالمشتريات اليومية" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox53_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox53.CheckedChanged
        On Error Resume Next
        If CheckBox53.Checked = True Then
            LstPermtion.Items.Add("تسجيل فروع الاصناف")
        End If
        If CheckBox53.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تسجيل فروع الاصناف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox54_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox54.CheckedChanged
        On Error Resume Next
        If CheckBox54.Checked = True Then
            LstPermtion.Items.Add("تسجيل المخازن")
        End If
        If CheckBox54.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تسجيل المخازن" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox59_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox59.CheckedChanged
        On Error Resume Next
        If CheckBox59.Checked = True Then
            LstPermtion.Items.Add("أذن تحويل بين المخازن")
        End If
        If CheckBox59.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "أذن تحويل بين المخازن" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox60_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox60.CheckedChanged
        On Error Resume Next
        If CheckBox60.Checked = True Then
            LstPermtion.Items.Add("زيادة ونقصان الاسعار")
        End If
        If CheckBox60.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "زيادة ونقصان الاسعار" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox61_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox61.CheckedChanged
        On Error Resume Next
        If CheckBox61.Checked = True Then
            LstPermtion.Items.Add("تسويات المخازن")
        End If
        If CheckBox61.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تسويات المخازن" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox62_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox62.CheckedChanged
        On Error Resume Next
        If CheckBox62.Checked = True Then
            LstPermtion.Items.Add("عروض الخصومات")
        End If
        If CheckBox62.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "عروض الخصومات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox63_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox63.CheckedChanged
        On Error Resume Next
        If CheckBox63.Checked = True Then
            LstPermtion.Items.Add("الخزينة")
        End If
        If CheckBox63.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "الخزينة" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox64_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox64.CheckedChanged
        On Error Resume Next
        If CheckBox64.Checked = True Then
            LstPermtion.Items.Add("مسحوبات وأيداعات جارى الشركاء")
        End If
        If CheckBox64.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مسحوبات وأيداعات جارى الشركاء" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox67_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox67.CheckedChanged
        On Error Resume Next
        If CheckBox67.Checked = True Then
            LstPermtion.Items.Add("الشفتات")
        End If
        If CheckBox67.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "الشفتات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox68_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox68.CheckedChanged
        On Error Resume Next
        If CheckBox68.Checked = True Then
            LstPermtion.Items.Add("أسعار البيع للعميل")
        End If
        If CheckBox68.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "أسعار البيع للعميل" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox69_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox69.CheckedChanged
        On Error Resume Next
        If CheckBox69.Checked = True Then
            LstPermtion.Items.Add("شيكات مستحقة الدفع")
        End If
        If CheckBox69.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "شيكات مستحقة الدفع" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox70_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox70.CheckedChanged
        On Error Resume Next
        If CheckBox70.Checked = True Then
            LstPermtion.Items.Add("خصومات أخرى")
        End If
        If CheckBox70.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "خصومات أخرى" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox15_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox15.CheckedChanged
        On Error Resume Next
        If CheckBox15.Checked = True Then
            LstPermtion.Items.Add("تقرير بمرتجعات المشتريات اليومية")
        End If
        If CheckBox15.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تقرير بمرتجعات المشتريات اليومية" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox71_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox71.CheckedChanged
        On Error Resume Next
        If CheckBox71.Checked = True Then
            LstPermtion.Items.Add("تقرير بمرتجعات المبيعات اليومية")
        End If
        If CheckBox71.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تقرير بمرتجعات المبيعات اليومية" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox72_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox72.CheckedChanged
        On Error Resume Next
        If CheckBox72.Checked = True Then
            LstPermtion.Items.Add("تسجيل توالف")
        End If
        If CheckBox72.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تسجيل توالف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox17_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox17.CheckedChanged
        On Error Resume Next
        If CheckBox17.Checked = True Then
            LstPermtion.Items.Add("عرض وحذف مرتجعات التوالف")
        End If
        If CheckBox17.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "عرض وحذف مرتجعات التوالف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox73_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox73.CheckedChanged
        On Error Resume Next
        If CheckBox73.Checked = True Then
            LstPermtion.Items.Add("تقرير كارت صنف")
        End If
        If CheckBox73.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تقرير كارت صنف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox74_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox74.CheckedChanged
        On Error Resume Next
        If CheckBox74.Checked = True Then
            LstPermtion.Items.Add("تقرير التحويلات بين المخازن")
        End If
        If CheckBox74.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تقرير التحويلات بين المخازن" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox75_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox75.CheckedChanged
        On Error Resume Next
        If CheckBox75.Checked = True Then
            LstPermtion.Items.Add("بضاعة أول المدة")
        End If
        If CheckBox75.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "بضاعة أول المدة" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox76_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox76.CheckedChanged
        On Error Resume Next
        If CheckBox76.Checked = True Then
            LstPermtion.Items.Add("تقرير أنتهاء صلاحية صنف")
        End If
        If CheckBox76.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تقرير أنتهاء صلاحية صنف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox77_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox77.CheckedChanged
        On Error Resume Next
        If CheckBox77.Checked = True Then
            LstPermtion.Items.Add("تقرير الاقرب لأنتهاء صلاحية صنف")
        End If
        If CheckBox77.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تقرير الاقرب لأنتهاء صلاحية صنف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox82_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox82.CheckedChanged
        On Error Resume Next
        If CheckBox82.Checked = True Then
            LstPermtion.Items.Add("التقرير المالى الشهرى")
        End If
        If CheckBox82.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "التقرير المالى الشهرى" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox78_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox78.CheckedChanged
        On Error Resume Next
        If CheckBox78.Checked = True Then
            LstPermtion.Items.Add("كشف حساب مورد وعميل")
        End If
        If CheckBox78.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "كشف حساب مورد وعميل" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox79_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox79.CheckedChanged
        On Error Resume Next
        If CheckBox79.Checked = True Then
            LstPermtion.Items.Add("كشف حساب مفصل لليوميات")
        End If
        If CheckBox79.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "كشف حساب مفصل لليوميات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox80_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox80.CheckedChanged
        On Error Resume Next
        If CheckBox80.Checked = True Then
            LstPermtion.Items.Add("كشف حساب المندوبين")
        End If
        If CheckBox80.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "كشف حساب المندوبين" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox81_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox81.CheckedChanged
        On Error Resume Next
        If CheckBox81.Checked = True Then
            LstPermtion.Items.Add("كشف حساب عملاء المندوب")
        End If
        If CheckBox81.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "كشف حساب عملاء المندوب" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox83_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox83.CheckedChanged
        On Error Resume Next
        If CheckBox83.Checked = True Then
            LstPermtion.Items.Add("توالف صنف")
        End If
        If CheckBox83.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "توالف صنف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox84_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox84.CheckedChanged
        On Error Resume Next
        If CheckBox84.Checked = True Then
            LstPermtion.Items.Add("التحكم فى تقرير الاصناف")
        End If
        If CheckBox84.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "التحكم فى تقرير الاصناف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox85_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox85.CheckedChanged
        On Error Resume Next
        If CheckBox85.Checked = True Then
            LstPermtion.Items.Add("تقرير بالاكثر والاقل جدول ورسم بيانى")
        End If
        If CheckBox85.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تقرير بالاكثر والاقل جدول ورسم بيانى" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox86_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox86.CheckedChanged
        On Error Resume Next
        If CheckBox86.Checked = True Then
            LstPermtion.Items.Add(CheckBox86.Text)
        End If
        If CheckBox86.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "أعدادات البرنامج" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub GetDataPermtionName()
        If Bool_ActionLoad = True Then
            connect()
            Dim aray_1 As New ArrayList
            Dim aray_2 As New ArrayList
            Dim aray_3 As New ArrayList
            aray_1.Clear() : aray_2.Clear() : aray_3.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select dbo.Permtions.PermtionID, dbo.Users.UserName, dbo.Permtions.PermtionName From dbo.Users RIGHT OUTER Join  dbo.Permtions ON dbo.Users.UserID = dbo.Permtions.UserID Group By dbo.Permtions.PermtionID, dbo.Users.UserName, dbo.Permtions.PermtionName HAVING(dbo.Users.UserName = N'" & Cmb_UserName.Text & "')"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_1.Add(dr(0)) : aray_2.Add(dr(1)) : aray_3.Add(dr(2))
            Loop
            Dim PermtionName As String = ""
            For i As Integer = 0 To aray_1.Count - 1
                PermtionName = aray_3(i).ToString

                If PermtionName = "مدير" Then
                    Chk_Admin.Checked = True
                End If
                If PermtionName = "تسجيل مجموعات الأصناف" Then
                    Chk_InsertData.Checked = True
                End If
                If PermtionName = "تسجيل  أصناف" Then
                    Chk_Sell.Checked = True
                End If
                If PermtionName = "تعديل أصناف" Then
                    Chk_CloseEdit.Checked = True
                End If
                If PermtionName = "بحث عن الاصناف" Then
                    CheckBox50.Checked = True
                End If
                If PermtionName = "تسجيل فروع الاصناف" Then
                    CheckBox53.Checked = True
                End If
                If PermtionName = "تسجيل المخازن" Then
                    CheckBox54.Checked = True
                End If
                If PermtionName = "أذن تحويل بين المخازن" Then
                    CheckBox59.Checked = True
                End If
                If PermtionName = "زيادة ونقصان الاسعار" Then
                    CheckBox60.Checked = True
                End If
                If PermtionName = "تسويات المخازن" Then
                    CheckBox61.Checked = True
                End If
                If PermtionName = "عروض الخصومات" Then
                    CheckBox62.Checked = True
                End If
                If PermtionName = "الخزينة" Then
                    CheckBox63.Checked = True
                End If
                If PermtionName = "مسحوبات وأيداعات جارى الشركاء" Then
                    CheckBox64.Checked = True
                End If
                If PermtionName = "إدارة المندوبين" Then
                    chkEmployees.Checked = True
                End If
                If PermtionName = "مبلغ العجز والزيادة للمندوبين" Then
                    chkDelegate.Checked = True
                End If
                If PermtionName = "المبيعات" Then
                    Chk_Sales.Checked = True
                End If
                If PermtionName = "مبيعات سريعة" Then
                    CheckBox90.Checked = True
                End If
                If PermtionName = "الشفتات" Then
                    CheckBox67.Checked = True
                End If
                If PermtionName = "حذف وتعديل المبيعات" Then
                    CheckBox32.Checked = True
                End If
                If PermtionName = "عرض وطباعة المبيعات" Then
                    CheckBox33.Checked = True
                End If
                If PermtionName = "تقرير بالمبيعات اليومية" Then
                    CheckBox51.Checked = True
                End If
                If PermtionName = "أسعار البيع للعميل" Then
                    CheckBox68.Checked = True
                End If
                If PermtionName = "بيانات العملاء" Then
                    CheckBox34.Checked = True
                End If
                If PermtionName = "مقبوضات العملاء" Then
                    CheckBox35.Checked = True
                End If
                If PermtionName = "شيكات مستحقة الدفع" Then
                    CheckBox69.Checked = True
                End If
                If PermtionName = "حـذف مقبوضات العملاء" Then
                    CheckBox42.Checked = True
                End If
                If PermtionName = "خصومات العملاء" Then
                    CheckBox38.Checked = True
                End If
                If PermtionName = "حـذف خصومات العملاء" Then
                    CheckBox43.Checked = True
                End If
                If PermtionName = "خصومات أخرى" Then
                    CheckBox70.Checked = True
                End If
                If PermtionName = "تسجيل بيانات موردين" Then
                    Chk_FastBill.Checked = True
                End If
                If PermtionName = "إجراء عملية مشتريات" Then
                    Chk_Debts.Checked = True
                End If
                If PermtionName = "تعديل وحذف عمليات المشتريات" Then
                    CheckBox13.Checked = True
                End If
                If PermtionName = "عرض وطباعة عمليات المشتريات" Then
                    CheckBox41.Checked = True
                End If
                If PermtionName = "تقرير بالمشتريات اليومية" Then
                    CheckBox52.Checked = True
                End If
                If PermtionName = "مدفوعات الموردين" Then
                    CheckBox31.Checked = True
                End If
                If PermtionName = "حذف مدفوعات الموردين" Then
                    CheckBox44.Checked = True
                End If
                If PermtionName = "خصومات الموردين" Then
                    Chk_Purchase.Checked = True
                End If
                If PermtionName = "حذف خصومات الموردين" Then
                    CheckBox45.Checked = True
                End If
                If PermtionName = "تسجيل مرتجعات مشتريات" Then
                    CheckBox36.Checked = True
                End If
                If PermtionName = "حذف وتعديل مرتجعات مشتريات" Then
                    CheckBox46.Checked = True
                End If
                If PermtionName = "عرض وطباعة مرتجعات مشتريات" Then
                    CheckBox47.Checked = True
                End If
                If PermtionName = "تقرير بمرتجعات المشتريات اليومية" Then
                    CheckBox15.Checked = True
                End If
                If PermtionName = "تسجيل مرتجعات مبيعات" Then
                    CheckBox39.Checked = True
                End If
                If PermtionName = "حذف وتعديل مرتجعات مبيعات" Then
                    CheckBox48.Checked = True
                End If
                If PermtionName = "عرض وطباعة مرتجعات مبيعات" Then
                    CheckBox49.Checked = True
                End If
                If PermtionName = "تقرير بمرتجعات المبيعات اليومية" Then
                    CheckBox71.Checked = True
                End If
                If PermtionName = "تسجيل توالف" Then
                    CheckBox72.Checked = True
                End If
                If PermtionName = "عرض وحذف مرتجعات التوالف" Then
                    CheckBox17.Checked = True
                End If
                If PermtionName = "تسجيل بنود" Then
                    CheckBox30.Checked = True
                End If
                If PermtionName = "تسجيل مصروفات" Then
                    CheckBox7.Checked = True
                End If
                If PermtionName = "عرض المصروفات" Then
                    CheckBox9.Checked = True
                End If
                If PermtionName = "تقرير حد الطلب للصنف" Then
                    Chk_Expenses.Checked = True
                End If
                If PermtionName = "تقرير أصناف نفذت" Then
                    Chk_Reports.Checked = True
                End If
                If PermtionName = "تقرير ارصدة الاصناف" Then
                    Chk_SearchApsent.Checked = True
                End If
                If PermtionName = "تقرير كارت صنف" Then
                    CheckBox73.Checked = True
                End If
                If PermtionName = "دخول بضائع المخازن" Then
                    CheckBox55.Checked = True
                End If
                If PermtionName = "خروج بضائع من المخازن" Then
                    CheckBox56.Checked = True
                End If
                If PermtionName = "تقرير التحويلات بين المخازن" Then
                    CheckBox74.Checked = True
                End If
                If PermtionName = "بضاعة أول المدة" Then
                    CheckBox75.Checked = True
                End If
                If PermtionName = "تقرير أنتهاء صلاحية صنف" Then
                    CheckBox76.Checked = True
                End If
                If PermtionName = "تقرير الاقرب لأنتهاء صلاحية صنف" Then
                    CheckBox77.Checked = True
                End If
                If PermtionName = "بيانات صنف كاملة" Then
                    CheckBox1.Checked = True
                End If
                If PermtionName = "مشتريات صنف" Then
                    CheckBox2.Checked = True
                End If
                If PermtionName = "مبيعات صنف" Then
                    CheckBox3.Checked = True
                End If
                If PermtionName = "مرتجعات مشتريات صنف" Then
                    CheckBox6.Checked = True
                End If
                If PermtionName = "مرتجعات مبيعات صنف" Then
                    CheckBox5.Checked = True
                End If
                If PermtionName = "أرباح صنف" Then
                    CheckBox4.Checked = True
                End If
                If PermtionName = "مشتريات فترة معينة" Then
                    CheckBox8.Checked = True
                End If
                If PermtionName = "مبيعات فترة معينة" Then
                    CheckBox11.Checked = True
                End If
                If PermtionName = "مرتجع مشتريات فترة معينة" Then
                    CheckBox14.Checked = True
                End If
                If PermtionName = "مرتجع مبيعات فترة معينة" Then
                    CheckBox16.Checked = True
                End If
                If PermtionName = "توالف صنف" Then
                    CheckBox83.Checked = True
                End If
                If PermtionName = "التحكم فى تقرير الاصناف" Then
                    CheckBox84.Checked = True
                End If
                If PermtionName = "تقرير بالاكثر والاقل جدول ورسم بيانى" Then
                    CheckBox85.Checked = True
                End If
                If PermtionName = "مدفوعات موردين" Then
                    CheckBox10.Checked = True
                End If
                If PermtionName = "كشف حساب مورد" Then
                    CheckBox12.Checked = True
                End If
                If PermtionName = "مقبوضات عملاء" Then
                    CheckBox40.Checked = True
                End If
                If PermtionName = "كشف حساب عميل" Then
                    CheckBox37.Checked = True
                End If
                If PermtionName = "كشف حساب مورد وعميل" Then
                    CheckBox78.Checked = True
                End If
                If PermtionName = "كشف حساب مفصل لليوميات" Then
                    CheckBox79.Checked = True
                End If
                If PermtionName = "كشف حساب المندوبين" Then
                    CheckBox80.Checked = True
                End If
                If PermtionName = "كشف حساب عملاء المندوب" Then
                    CheckBox81.Checked = True
                End If
                If PermtionName = "الحركة المالية لصنف" Then
                    CheckBox20.Checked = True
                End If
                If PermtionName = "تقرير الارباح والخسائر" Then
                    CheckBox19.Checked = True
                End If
                If PermtionName = "حركة الخزينة" Then
                    CheckBox21.Checked = True
                End If
                If PermtionName = "التقرير المالى الشهرى" Then
                    CheckBox82.Checked = True
                End If
                If PermtionName = "مشتريات مورد" Then
                    CheckBox23.Checked = True
                End If
                If PermtionName = "مبيعات عميل" Then
                    CheckBox24.Checked = True
                End If
                If PermtionName = "مرتجع مشتريات" Then
                    CheckBox25.Checked = True
                End If
                If PermtionName = "مرتجع مبيعات عميل" Then
                    CheckBox26.Checked = True
                End If
                If PermtionName = "مدفوعات مورد" Then
                    CheckBox27.Checked = True
                End If
                If PermtionName = "مدفوعات عميل" Then
                    CheckBox57.Checked = True
                End If
                If PermtionName = "مصروفات" Then
                    CheckBox28.Checked = True
                End If
                If PermtionName = "خصومات مورد" Then
                    CheckBox29.Checked = True
                End If
                If PermtionName = "خصومات عميل" Then
                    CheckBox58.Checked = True
                End If
                If PermtionName = "حفظ واسترجاع نسخة من قاعدة البيانات" Then
                    CheckBox18.Checked = True
                End If
                If PermtionName = "طباعة الباركود" Then
                    CheckBox22.Checked = True
                End If
                If PermtionName = "أعدادات البرنامج" Then
                    CheckBox86.Checked = True
                End If
                If PermtionName = "الموظفين" Then
                    chkEmployees.Checked = True
                End If
                If PermtionName = "المندوبين" Then
                    chkDelegate.Checked = True
                End If
                If PermtionName = "الحسابات" Then
                    chkAccounts.Checked = True
                End If
                If PermtionName = "التصنيع" Then
                    chkManufacturing.Checked = True
                End If
                If PermtionName = "البنوك" Then
                    chkBanks.Checked = True
                End If
                If PermtionName = "الصيانة" Then
                    chkMaintenance.Checked = True
                End If
                If PermtionName = "عروض الاسعار" Then
                    CheckBox65.Checked = True
                End If
                If PermtionName = "إيرادات أخرى" Then
                    CheckBox66.Checked = True
                End If
                If PermtionName = "بيانات المنشأة" Then
                    CheckBox87.Checked = True
                End If
                If PermtionName = "تقرير حركة صنف مفصل" Then
                    CheckBox88.Checked = True
                End If
                If PermtionName = "تقرير ربح اليومية بالاصناف" Then
                    CheckBox89.Checked = True
                End If
            Next
        End If

    End Sub

    Private Sub chkEmployees_CheckedChanged(sender As Object, e As EventArgs) Handles chkEmployees.CheckedChanged
        On Error Resume Next
        If chkEmployees.Checked = True Then
            LstPermtion.Items.Add("الموظفين")
        End If
        If chkEmployees.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "الموظفين" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub chkDelegate_CheckedChanged(sender As Object, e As EventArgs) Handles chkDelegate.CheckedChanged
        On Error Resume Next
        If chkDelegate.Checked = True Then
            LstPermtion.Items.Add("المندوبين")
        End If
        If chkDelegate.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "المندوبين" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub chkAccounts_CheckedChanged(sender As Object, e As EventArgs) Handles chkAccounts.CheckedChanged
        On Error Resume Next
        If chkAccounts.Checked = True Then
            LstPermtion.Items.Add("الحسابات")
        End If
        If chkAccounts.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "الحسابات" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub chkManufacturing_CheckedChanged(sender As Object, e As EventArgs) Handles chkManufacturing.CheckedChanged
        On Error Resume Next
        If chkManufacturing.Checked = True Then
            LstPermtion.Items.Add("التصنيع")
        End If
        If chkManufacturing.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "التصنيع" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub chkBanks_CheckedChanged(sender As Object, e As EventArgs) Handles chkBanks.CheckedChanged
        On Error Resume Next
        If chkBanks.Checked = True Then
            LstPermtion.Items.Add("البنوك")
        End If
        If chkBanks.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "البنوك" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub chkMaintenance_CheckedChanged(sender As Object, e As EventArgs) Handles chkMaintenance.CheckedChanged
        On Error Resume Next
        If chkMaintenance.Checked = True Then
            LstPermtion.Items.Add("الصيانة")
        End If
        If chkMaintenance.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "الصيانة" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox65_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox65.CheckedChanged
        On Error Resume Next
        If CheckBox65.Checked = True Then
            LstPermtion.Items.Add("عروض الاسعار")
        End If
        If CheckBox65.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "عروض الاسعار" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox66_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox66.CheckedChanged
        On Error Resume Next
        If CheckBox66.Checked = True Then
            LstPermtion.Items.Add("إيرادات أخرى")
        End If
        If CheckBox66.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "إيرادات أخرى" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox87_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox87.CheckedChanged
        On Error Resume Next
        If CheckBox87.Checked = True Then
            LstPermtion.Items.Add("بيانات المنشأة")
        End If
        If CheckBox87.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "بيانات المنشأة" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox88_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox88.CheckedChanged
        On Error Resume Next
        If CheckBox88.Checked = True Then
            LstPermtion.Items.Add("تقرير حركة صنف مفصل")
        End If
        If CheckBox88.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تقرير حركة صنف مفصل" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub btnCloseItemDiscountRate_Click(sender As Object, e As EventArgs) Handles btnCloseItemDiscountRate.Click
        PanelCompanyBranchName.Top = 5000
    End Sub

    Private Sub btnLinkingUserBranch_Click(sender As Object, e As EventArgs) Handles btnLinkingUserBranch.Click
        PanelCompanyBranchName.Top = 184
        PanelCompanyBranchName.Size = New System.Drawing.Size(400, 200)
    End Sub

    Private Sub CheckBox89_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox89.CheckedChanged
        On Error Resume Next
        If CheckBox89.Checked = True Then
            LstPermtion.Items.Add("تقرير ربح اليومية بالاصناف")
        End If
        If CheckBox89.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "تقرير ربح اليومية بالاصناف" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub

    Private Sub CheckBox90_CheckedChanged(sender As Object, e As EventArgs) Handles CheckBox90.CheckedChanged
        On Error Resume Next
        If CheckBox90.Checked = True Then
            LstPermtion.Items.Add("مبيعات سريعة")
        End If
        If CheckBox90.Checked = False Then
            For index As Integer = 0 To LstPermtion.Items.Count - 1
                If LstPermtion.Items(index).ToString() = "مبيعات سريعة" Then
                    LstPermtion.Items.RemoveAt(index)
                End If
            Next
        End If
    End Sub
End Class