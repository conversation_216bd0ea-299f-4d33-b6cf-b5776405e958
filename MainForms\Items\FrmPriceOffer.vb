﻿Imports vb = Microsoft.VisualBasic
Imports System.Data.OleDb
Imports CrystalDecisions.CrystalReports.Engine

Public Class FrmPriceOffer
    Dim ListBoxSelectedIndex As Integer
    Dim WithEvents BS As New BindingSource
    Dim itm_name As String = ""
    Dim ActivUpdate As Boolean = False
    Dim DiscountsValue As Double = 0
    Dim TotalDiscountsValue As Double = 0
    Dim StateDisc As String = ""
    Dim AlaertParcode As Boolean
    Dim SMHeightWidth, SMTotalSquareMeter As Double


    Private Sub FrmItemsNew_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyBase.KeyUp
    End Sub
    Private Sub FrmItemsNew_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cls.FillComboDataAdapterQuickSearch("Items", "sname", cmbitmnm)

        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", cmbCustomers)
            Cls.fill_combo("Customers", "Vendorname", cmbCustomersView)
        End If
        Cls.fill_combo_Branch("stores", "store", cmbStores)
        cmbStores.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        Bra.Fil("groups", "g_name", cmbcats)
        cmbCustomers.Items.Add("نقداً")
        MAXRECORD()
        PanelViewPriceOffer.Top = 5000
        PanelPrice.Top = 10000
        GetDateNotBeenActivatedPrograms(dtpbill_date)

        If Show_Height_Width_Altitude_Density = "YES" Then
            lblTotalSquareMeter.Visible = True
            txtTotalSquareMeter.Visible = True
            chkHeightWidthShowPrice.Visible = True
            lblTotalWidthHeight.Visible = True
        End If
    End Sub

    Function ValidateSave() As Boolean
        If DTGV.Rows.Count < 1 Then MsgBox("فضلا أكمل بيانات الفاتورة", MsgBoxStyle.Exclamation) : Return False
        If txtbillno.Text = "" Then MsgBox("فضلا أدخل رقم العرض", MsgBoxStyle.Exclamation) : Return False
        If cmbCustomers.Text = "" Then MsgBox("فضلا أدخل أسم العميل", MsgBoxStyle.Exclamation) : Return False

        If ActivUpdate = False Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from PriceOffer where bill_no =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                MsgBox("رقم العرض مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
            End If
        End If


        Return True
    End Function

    Sub Clear_All()
        cmbitmnm.Text = ""
        txtPrice.Text = ""
        txtTotalPrice.Text = ""
        cmbitmnm.Text = ""
        cmbitmnm.Focus()
        DTGV.DataSource = ""
    End Sub

    Private Sub btnsave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnsave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateTextAdd() = False Then Exit Sub

        GetHeightWidthAltitudeDensityTotal()

        Dim SalPrice As Double = 0
        If ShowDiscountRateItemSales = "NO" Then
            SalPrice = txtPrice.Text
        Else
            If txtRateDiscPriceAfter.Text <> 0 Then
                SalPrice = txtRateDiscPriceAfter.Text

            Else
                SalPrice = txtPrice.Text
                txtRateDiscPriceAfter.Text = txtPrice.Text
            End If
        End If

        Dim itm_id As String = Cls.Get_Code_Value_Branch_More("Items", "itm_id", "sname=N'" & cmbitmnm.Text.Trim & "'")
        If cmbcats.Text = "" Then
            cmbcats.Text = Cls.Get_Code_Value_Branch_More("Items", "group_name", "sname=N'" & cmbitmnm.Text.Trim & "'")
        End If

        DTGV.DataSource = Fn_AddBill(itm_id, cmbcats.Text, cmbitmnm.Text.Trim, cmbUnity.Text.Trim, Val(SalPrice), Val(txtqunt.Text), Val(txtTotalPrice.Text), cmbStores.Text.Trim, Val(txtdiscBill.Text), Val(DiscountsValue), Val(lblDiscount_Price_After.Text), txtHeight, txtWidth, txtAltitude, txtDensity, TotalSquareMeter, TotalWidthHeight)
        ClearAdd()
        cmbitmnm.Focus()
        SumAllPrice()
        DTGV.Columns(0).Visible = False
        DTGV.Columns(1).Visible = False
        'DTGV.Columns(8).Visible = False
        DTGV.Columns(9).Visible = False
        DTGV.Columns(10).Visible = False
        If Show_Height_Width_Altitude_Density = "NO" Then
            DTGV.Columns(11).Visible = False
            DTGV.Columns(12).Visible = False
            DTGV.Columns(13).Visible = False
            DTGV.Columns(14).Visible = False
            DTGV.Columns(15).Visible = False
            DTGV.Columns(16).Visible = False
        End If
    End Sub

    Private Sub SumAllPrice()
        Dim SM, SMQu, XHeight, XWidth, XAltitude, Qunt, Price As Double

        For i As Integer = 0 To DTGV.Rows.Count - 1
            SM = SM + DTGV.Rows(i).Cells(6).Value
            SMQu = SMQu + DTGV.Rows(i).Cells(5).Value
        Next
        txttotalpeforedisc.Text = SM
        txtTotalQunt.Text = SMQu

        If Show_Height_Width_Altitude_Density = "YES" Then
            For i As Integer = 0 To DTGV.Rows.Count - 1
                Price = DTGV.Rows(i).Cells(4).Value
                Qunt = DTGV.Rows(i).Cells(5).Value
                XHeight = DTGV.Rows(i).Cells(11).Value
                XWidth = DTGV.Rows(i).Cells(12).Value
                XAltitude = DTGV.Rows(i).Cells(13).Value

                If Show_Height_Width_Altitude_Density = "YES" Then
                    TotalSquareMeter = Val(Qunt) * Val(XHeight) * Val(XWidth) * Val(XAltitude) / 1000000
                    TotalSquareMeter = Math.Round(TotalSquareMeter, 1)

                    TotalWidthHeight = Val(Qunt) * Val(XHeight) * Val(XWidth) * Val(XAltitude) * Val(Price) / 1000000

                    TotalWidthHeight = Math.Round(TotalWidthHeight, 2)
                    SMHeightWidth += TotalWidthHeight
                    txttotalpeforedisc.Text = SMHeightWidth

                    SMTotalSquareMeter += TotalSquareMeter
                    SMHeightWidth += TotalWidthHeight
                End If
            Next

            txtTotalSquareMeter.Text = SMTotalSquareMeter
            lblTotalWidthHeight.Text = SMHeightWidth
        End If

    End Sub

    Dim Dt_AddBill As New DataTable
    Friend Function Fn_AddBill(ByVal Col_itm_id As String, ByVal Col_itm_cat As String, ByVal Col_Name As String, ByVal Col_Unity As String, ByVal Col_Price As Double, ByVal Col_Quant As Double, ByVal Col_Total As Double, ByVal Col_Stores As String, ByVal Col_Dsic As Double, ByVal Col_DiscountsValue As Double, ByVal Col_UnitPriceAfterDisc As Double, ByVal Col_Height As Double, ByVal Col_Width As Double, ByVal Col_Altitude As Double, ByVal Col_Density As Double, ByVal Col_TotalSquareMeter As Double, ByVal Col_TotalWidthHeight As Double) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("الباركود", GetType(String))
            Dt_AddBill.Columns.Add("المجموعة", GetType(String))
            Dt_AddBill.Columns.Add("أسم الصنف", GetType(String))
            Dt_AddBill.Columns.Add("الوحدة", GetType(String))
            Dt_AddBill.Columns.Add("السعر", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية", GetType(Double))
            Dt_AddBill.Columns.Add("الاجمالى", GetType(Double))
            Dt_AddBill.Columns.Add("المخزن", GetType(String))
            Dt_AddBill.Columns.Add("الخصم", GetType(Double))
            Dt_AddBill.Columns.Add("قيمة الخصم", GetType(Double))
            Dt_AddBill.Columns.Add("سعر البيع بعد خصم الصنف", GetType(Double))
            Dt_AddBill.Columns.Add("الطول", GetType(String)) '11
            Dt_AddBill.Columns.Add("العرض", GetType(String)) '12
            Dt_AddBill.Columns.Add("الارتفاع", GetType(String)) '13
            Dt_AddBill.Columns.Add("الكثافة", GetType(String)) '14
            Dt_AddBill.Columns.Add("إجمالى م3", GetType(Double)) '15
            Dt_AddBill.Columns.Add("إجمالى", GetType(Double)) '16
        End If

        Dt_AddBill.Rows.Add(Col_itm_id, Col_itm_cat, Col_Name, Col_Unity, Col_Price, Col_Quant, Col_Total, Col_Stores, Col_Dsic, Col_DiscountsValue, Col_UnitPriceAfterDisc, Col_Height, Col_Width, Col_Altitude, Col_Density, Col_TotalSquareMeter, Col_TotalWidthHeight)
        Return Dt_AddBill
    End Function

    Private Sub ClearAdd()
        cmbitmnm.Text = ""
        txtPrice.Text = ""
        txtTotalPrice.Text = ""
        cmbitmnm.Text = ""
        cmbUnity.Text = ""
        cmbcats.Text = ""
        TxtPrc.Text = ""
        cmbitmnm.Focus()
    End Sub

    Function ValidateTextAdd() As Boolean
        If cmbitmnm.Text = "" Then MsgBox("فضلا أدخل أسم الصنف", MsgBoxStyle.Exclamation) : Return False
        If txtbillno.Text = "" Then MsgBox("فضلا أدخل رقم العرض", MsgBoxStyle.Exclamation) : Return False
        If cmbCustomers.Text = "" Then MsgBox("فضلا أدخل أسم العميل", MsgBoxStyle.Exclamation) : Return False
        If cmbUnity.Text = "" Then MsgBox("فضلا أدخل الوحدة", MsgBoxStyle.Exclamation) : Return False
        If cmbStores.Text = "" Then MsgBox("فضلا أدخل المخزن", MsgBoxStyle.Exclamation) : Return False

        If cmbCustomers.Text = "نقداً" Then

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Customers where Vendorname =N'" & cmbCustomers.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H = 0 Then
                Dim Cust_Code As String = Cls.MAXRECORD("customers", "Cust_Code")

                Cls.insert("customers", "Company_Branch_ID,Cust_Code,Vendorname,vintinval,vndiscount,VnPay,vnamntdebit,vnamntcredit,UserName,GeoArea_Code,PriceType_ID", "N'" & Company_Branch_ID & "',N'" & Cust_Code & "',N'" & cmbCustomers.Text.Trim & "',0,0,0,0,0,N'" & UserName & "',0,0")

            End If

        End If

        Return True
    End Function

    Function ValidatAdd() As Boolean
        If cmbitmnm.Text = "" Then MsgBox("فضلا أدخل أسم الصنف", MsgBoxStyle.Exclamation) : Return False
        If txtbillno.Text = "" Then MsgBox("فضلا أدخل رقم العرض", MsgBoxStyle.Exclamation) : Return False
        If cmbCustomers.Text = "" Then MsgBox("فضلا أدخل أسم العميل", MsgBoxStyle.Exclamation) : Return False
        If cmbUnity.Text = "" Then MsgBox("فضلا أدخل الوحدة", MsgBoxStyle.Exclamation) : Return False

        Return True
    End Function

    Private Sub txtqunt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If Not IsNumeric(txtqunt.Text) Then
            System.Windows.Forms.SendKeys.Send("{backspace}")
        End If
        If ShowDiscountRateItemSales = "NO" Then
            txtTotalPrice.Text = Val(txtPrice.Text) * Val(txtqunt.Text)
            txtTotalPrice.Text = Math.Round(Convert.ToDouble(txtTotalPrice.Text), 2)
        Else
            txtTotalPrice.Text = Val(txtRateDiscPriceAfter.Text) * Val(txtqunt.Text)
            txtTotalPrice.Text = Math.Round(Convert.ToDouble(txtTotalPrice.Text), 2)
        End If
    End Sub

    Private Sub cmbitmnm_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbitmnm.DropDown
        txtTotalPrice.Text = ""
        cmbitmnm.Text = ""
        txtPrice.Text = ""
        txtqunt.Text = ""
        TxtPrc.Text = ""
    End Sub

    Private Sub btnSaveAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSaveAll.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        If ValidateSave() = False Then Exit Sub

        If ActivUpdate = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete From  PriceOfferBill where bill_no =N'" & txtbillno.Text.Trim & "'" : cmd.ExecuteNonQuery()
        End If

        Dim Cust_Code As String = Cls.Get_Code_Value_Branch_More("Customers", "Cust_Code", "Vendorname=N'" & cmbCustomers.Text.Trim & "'")
        Dim AutoID As String
        If ActivUpdate = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update PriceOffer set Company_Branch_ID =N'" & Company_Branch_ID & "',Cust_Code =N'" & Cust_Code & "',bill_date =N'" & Cls.C_date(dtpbill_date.Text) & "',totalprice =N'" & Val(txttotalpeforedisc.Text.Trim) & "' where bill_no =N'" & txtbillno.Text.Trim & "'" : cmd.ExecuteNonQuery()
        Else
            AutoID = MaxRecordTables("PriceOffer", "id")

            Try
                Dim xx As String = Cls.C_date(dtpbill_date.Text)
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into PriceOffer (id,Company_Branch_ID,bill_no,Cust_Code,totalprice,bill_date,UserName)  values("
                S = S & "N'" & AutoID & "',N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "',N'" & Cust_Code & "',N'" & txttotalpeforedisc.Text.Trim & "',N'" & Cls.C_date(dtpbill_date.Text) & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Catch ex As Exception
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into PriceOffer (Company_Branch_ID,bill_no,Cust_Code,totalprice,bill_date,UserName)  values("
                S = S & "N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "',N'" & Cust_Code & "',N'" & txttotalpeforedisc.Text.Trim & "',N'" & Cls.C_date(dtpbill_date.Text) & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End Try
        End If


        For i As Integer = 0 To DTGV.Rows.Count - 1

            AutoID = MaxRecordTables("PriceOfferBill", "id")
            Try
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into PriceOfferBill (id,Company_Branch_ID,Cust_Code,bill_no,itm_id,itm_cat,itm_name,Unity,price,qu,totalprice,Stores,Discounts,DiscountsValue,Discount_Price_After,bill_date,Height,Width,Altitude,Density,TotalSquareMeter,TotalWidthHeight,UserName)  values("
                S = S & "N'" & AutoID & "',N'" & Company_Branch_ID & "',N'" & Cust_Code & "',N'" & txtbillno.Text.Trim & "',N'" & DTGV.Rows(i).Cells(0).Value & "',N'" & DTGV.Rows(i).Cells(1).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(3).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(5).Value & "',N'" & DTGV.Rows(i).Cells(6).Value & "',N'" & DTGV.Rows(i).Cells(7).Value.ToString & "',N'" & DTGV.Rows(i).Cells(8).Value.ToString & "',N'" & DTGV.Rows(i).Cells(9).Value.ToString & "',N'" & DTGV.Rows(i).Cells(10).Value.ToString & "',N'" & Cls.C_date(dtpbill_date.Text) & "',N'" & DTGV.Rows(i).Cells(11).Value.ToString & "',N'" & DTGV.Rows(i).Cells(12).Value.ToString & "',N'" & DTGV.Rows(i).Cells(13).Value.ToString & "',N'" & DTGV.Rows(i).Cells(14).Value.ToString & "',N'" & DTGV.Rows(i).Cells(15).Value.ToString & "',N'" & DTGV.Rows(i).Cells(16).Value.ToString & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Catch ex As Exception
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into PriceOfferBill (Company_Branch_ID,Cust_Code,bill_no,itm_id,itm_cat,itm_name,Unity,price,qu,totalprice,Stores,Discounts,DiscountsValue,Discount_Price_After,bill_date,Height,Width,Altitude,Density,TotalSquareMeter,TotalWidthHeight,UserName)  values("
                S = S & "N'" & Company_Branch_ID & "',N'" & Cust_Code & "',N'" & txtbillno.Text.Trim & "',N'" & DTGV.Rows(i).Cells(0).Value & "',N'" & DTGV.Rows(i).Cells(1).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(3).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(5).Value & "',N'" & DTGV.Rows(i).Cells(6).Value & "',N'" & DTGV.Rows(i).Cells(7).Value.ToString & "',N'" & DTGV.Rows(i).Cells(8).Value.ToString & "',N'" & DTGV.Rows(i).Cells(9).Value.ToString & "',N'" & DTGV.Rows(i).Cells(10).Value.ToString & "',N'" & Cls.C_date(dtpbill_date.Text) & "',N'" & DTGV.Rows(i).Cells(11).Value.ToString & "',N'" & DTGV.Rows(i).Cells(12).Value.ToString & "',N'" & DTGV.Rows(i).Cells(13).Value.ToString & "',N'" & DTGV.Rows(i).Cells(14).Value.ToString & "',N'" & DTGV.Rows(i).Cells(15).Value.ToString & "',N'" & DTGV.Rows(i).Cells(16).Value.ToString & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End Try
        Next

        If chkViewPrint.Checked = True Then
            PrintReport()
        End If

        MAXRECORD()

        Dt_AddBill.Rows.Clear()
        btnSaveAll.Text = "حــــفظ"
    End Sub

    Private Sub PrintReport()
        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")
        Dim BillSerialNumber As Double = 0

        For i As Integer = 0 To DTGV.Rows.Count - 1
            BillSerialNumber += 1

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases(itm_id,itm_name,Unity,price,qu,totalprice,totalpriceafterdisc,BILL_NO,CustomerName,Number1,Name1,Name2,Name3,Name4,Name11,Name12,Number2,Number3,Number4) values"
            S = S & " (N'" & DTGV.Rows(i).Cells(0).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(3).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(5).Value & "',N'" & DTGV.Rows(i).Cells(6).Value & "',N'" & txttotalpeforedisc.Text & "',N'" & txtbillno.Text & "',N'" & cmbCustomers.Text & "',N'" & BillSerialNumber & "',N'" & DTGV.Rows(i).Cells(11).Value & "',N'" & DTGV.Rows(i).Cells(12).Value & "',N'" & DTGV.Rows(i).Cells(13).Value & "',N'" & DTGV.Rows(i).Cells(14).Value & "',N'" & DTGV.Rows(i).Cells(15).Value & "',N'" & DTGV.Rows(i).Cells(16).Value & "',N'" & txtTotalQunt.Text & "',N'" & txtTotalSquareMeter.Text & "',N'" & lblTotalWidthHeight.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()

        Dim txt, txtNameAr, txtNameEn, txtDateItem, txtCmpAddress, txtCmpTel, txtCmpMobile, txtEndorsement, txtCmpEmail, txtFax, txtCmpUnderBILL As TextObject

        Dim rpt
        If PrintSmall = "NO" Then
            If chkPrintImageItems.Checked = False Then
                rpt = New Rpt_PriceOffer
                Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
            Else
                rpt = New Rpt_PriceOffer_Image
                Cls.Select_More_Data_Branch_Print_Orderby("View_PrintSalesPurchases_Image", "*", "Number1")
            End If
        End If
        If PrintSmall = "YES" Then
            rpt = New Rpt_PriceOfferSmall
            Cls.Select_More_Data_Branch_Print_Orderby("PrintSalesPurchases", "*", "Number1")
        End If
        If PrintSmall = "A5" Then
            If chkPrintImageItems.Checked = False Then
                rpt = New Rpt_PriceOffer
                Cls.Select_More_Data_Branch_Print_Orderby("PrintSalesPurchases", "*", "Number1")
            Else
                rpt = New Rpt_PriceOffer_Image
                Cls.Select_More_Data_Branch_Print_Orderby("View_PrintSalesPurchases_Image", "*", "Number1")
            End If
        End If

        If Show_Height_Width_Altitude_Density = "YES" Then
            If chkHeightWidthShowPrice.Checked = False Then
                rpt = New Rpt_PriceOffer_Width_Height_Altitude
            Else
                rpt = New Rpt_PriceOffer_Width_Height_Altitude_Price
            End If
        End If

        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txt = rpt.Section1.ReportObjects("txtReportTitel")
        txt.Text = "عرض أسعار"
        txtNameAr = rpt.Section1.ReportObjects("txtTitelNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelNameEn")
        txtNameEn.Text = NameEnCompany
        txtDateItem = rpt.Section1.ReportObjects("txtDateItem")
        txtDateItem.Text = dtpbill_date.Text
        txtCmpAddress = rpt.Section1.ReportObjects("txtAddress")
        txtCmpAddress.Text = CmpAddress
        txtCmpTel = rpt.Section1.ReportObjects("txtTel")
        txtCmpTel.Text = CmpTel
        txtCmpMobile = rpt.Section1.ReportObjects("txtMobile")
        txtCmpMobile.Text = CmpMobile
        txtCmpEmail = rpt.Section1.ReportObjects("txtEmail")
        txtCmpEmail.Text = CmpEmail
        txtFax = rpt.Section1.ReportObjects("txtFax")
        txtFax.Text = CmpFax
        txtCmpUnderBILL = rpt.Section1.ReportObjects("txtUnderBILL")
        txtCmpUnderBILL.Text = CMPUnderBILL
        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
        txtEndorsement.Text = CMPEndorsement
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "عرض أسعار"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub


    Sub ClearGrid()
        Dt_AddBill.Rows.Clear() : Clear_All()
        DTGV.DataSource = ""
        Clear_All()
    End Sub

    Dim RNXD As Integer
    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        Try
            If DTGV.RowCount = 0 Then Beep() : Exit Sub
            If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            RNXD = DTGV.CurrentRow.Index
            DTGV.Rows.RemoveAt(RNXD)
        Catch ex As Exception

        End Try
    End Sub

    Private Sub txtNumberTransfer_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs)

    End Sub

    Private Sub txtquntUnity_TextChanged(sender As Object, e As EventArgs) Handles txtqunt.TextChanged
        MyVars.CheckNumber(txtqunt)
        If ShowDiscountRateItemSales = "NO" Then
            txtTotalPrice.Text = Val(txtPrice.Text) * Val(txtqunt.Text)
            txtTotalPrice.Text = Math.Round(Convert.ToDouble(txtTotalPrice.Text), 2)
        Else
            txtTotalPrice.Text = Val(txtRateDiscPriceAfter.Text) * Val(txtqunt.Text)
            txtTotalPrice.Text = Math.Round(Convert.ToDouble(txtTotalPrice.Text), 2)
        End If
    End Sub

    Private Sub txtquntUnity_KeyUp(sender As Object, e As KeyEventArgs) Handles txtqunt.KeyUp
        If e.KeyCode = 13 Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub cmbitmnm_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbitmnm.SelectedIndexChanged
        If AlaertParcode = False Then
            ItemsUnityNumber = 0
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id,group_name,SalPrice,RateDiscSalPriceAfter from items where sname=N'" & cmbitmnm.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                TxtPrc.Text = dr("itm_id").ToString()
                cmbcats.Text = dr("group_name").ToString()
                txtPrice.Text = Val(dr("SalPrice").ToString())
                txtRateDiscPriceAfter.Text = Val(dr("RateDiscSalPriceAfter").ToString)
            End If

            txtqunt.Text = 1
            txtquntUnity.Text = 1

            GetItemsUnity(cmbUnity, TxtPrc.Text)

            SetItemsUnity()

            GetHeightWidthAltitudeDensity()

            Dim DiscountItems As String = mykey.GetValue("DiscountItems", "0")
            If DiscountItems <> 0 Then
                txtdiscBill.Text = mykey.GetValue("DiscountItems", "0")
            End If

            If txtRateDiscPriceAfter.Text = "0" Then
                txtRateDiscPriceAfter.Text = txtPrice.Text
            End If

            'If ShowDiscountRateItemSales = "YES" Then
            '    txtPrice.Text = txtRateDiscPriceAfter.Text
            'End If

            WholeasalePrice()

            If NotUnityItemsProgram = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & TxtPrc.Text & "' and Unity_Name=N'" & cmbUnity.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 1 : End If
                txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)

                If ItemsUnityNumber <> 1 Then
                    If ItemsUnityNumber <> 0 Then
                        Dim XCode As String = Cls.Get_Code_Value_Branch_More("ItemsUnity", "SalPriceUnit", "itm_id=N'" & TxtPrc.Text & "' and Unity_Name=N'" & cmbUnity.Text & "'")
                        If XCode <> "0" Then
                            If Val(txtPrice.Text) < Val(XCode) Then
                                txtPrice.Text = XCode
                            End If
                        End If
                    End If
                End If
            Else
                txtqunt.Text = Val(txtquntUnity.Text)
            End If

            sumdisc1()

            txtPrice.Focus()
            txtPrice.SelectAll()
        End If
    End Sub

    Private Sub GetHeightWidthAltitudeDensity()
        Try
            If Show_Height_Width_Altitude_Density = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select Height,Width,Altitude,Density from Items where itm_id=N'" & TxtPrc.Text & "' and Stores=N'" & cmbStores.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    txtHeight = Val(dr(0).ToString)
                    txtWidth = Val(dr(1).ToString)
                    txtAltitude = Val(dr(2).ToString)
                    txtDensity = Val(dr(3).ToString)
                End If
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub SetItemsUnity()
        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & TxtPrc.Text & "' and Unity_Name=N'" & cmbUnity.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 1 : End If
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(txtquntUnity.Text)
        End If
    End Sub


    Private Sub txtPrice_KeyUp(sender As Object, e As KeyEventArgs) Handles txtPrice.KeyUp
        If e.KeyCode = 13 Then
            txtqunt.Focus()
        End If
    End Sub

    Private Sub txtPrice_TextChanged(sender As Object, e As EventArgs) Handles txtPrice.TextChanged
        MyVars.CheckNumber(txtPrice)
        If ShowDiscountRateItemSales = "NO" Then
            txtTotalPrice.Text = Val(txtPrice.Text) * Val(txtqunt.Text)
            txtTotalPrice.Text = Math.Round(Convert.ToDouble(txtTotalPrice.Text), 2)
        Else
            txtTotalPrice.Text = Val(txtRateDiscPriceAfter.Text) * Val(txtqunt.Text)
            txtTotalPrice.Text = Math.Round(Convert.ToDouble(txtTotalPrice.Text), 2)
        End If
    End Sub

    Private Sub MAXRECORD()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from PriceOffer"
            dr = cmd.ExecuteReader

            Dim dt As New DataTable
            dt.Load(dr)

            If dt.Rows.Count = 0 Then
                Me.txtbillno.Text = 1
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(bill_no) as mb FROM PriceOffer"
                dr = cmd.ExecuteReader
                dr.Read()

                Dim sh As Long
                sh = dr("mb")
                Me.txtbillno.Text = sh + 1
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub btnCloseAdjustmentsStores_Click(sender As Object, e As EventArgs) Handles btnCloseAdjustmentsStores.Click
        ActivUpdate = False
        btnSaveAll.Text = "حــــفظ"
        PanelViewPriceOffer.Dock = DockStyle.None
        PanelViewPriceOffer.Top = 5000
    End Sub

    Private Sub btnView_Click(sender As Object, e As EventArgs) Handles btnView.Click
        PanelViewPriceOffer.Top = 20
        PanelViewPriceOffer.Dock = DockStyle.Fill
        btnShow_Click(sender, e)
    End Sub

    Private Sub btnShow_Click(sender As Object, e As EventArgs) Handles btnShow.Click
        If chkFreeSearchByItems.Checked = True Then
            DataGridView2.DataSource = ""

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = Cls.Get_Select_Grid_S("id as [ID],bill_no as [رقم العرض],Vendorname as [أسم العميل],bill_date as [التاريخ],itm_id as [الباركود],itm_name as [الصنف],price as [السعر],qu as [الكمية],Unity as [الوحدة],totalprice as [الاجمالى]", "View_PriceOfferBill", "id <>N''")
            If chkAll.Checked = False Then
                If txtbillnoView.Text <> "" Then
                    S = S & " and bill_no =N'" & txtbillnoView.Text.Trim & "'"
                End If
                If cmbCustomersView.Text <> "" Then
                    S = S & " and Vendorname =N'" & cmbCustomersView.Text.Trim & "'"
                End If
                If txtPrcView.Text <> "" Then
                    S = S & " and itm_id =N'" & txtPrcView.Text.Trim & "'"
                End If
                If txtFinditm_name.Text <> "" Then
                    S = S & " and itm_name Like N'%" & txtFinditm_name.Text & "%'"
                End If
            End If
            If ChkWithoutDate.Checked = False Then
                S = S & " and bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
            End If
            If FilterSelect = "Number" Then
                S = S & " order by [رقم العرض]"
            End If
            If FilterSelect = "Date" Then
                S = S & " order by [التاريخ]"
            End If
            If FilterSelect = "Name" Then
                S = S & " order by [أسم العميل]"
            End If

            cmd.CommandText = S : dr = cmd.ExecuteReader
            DataGridView2.DataSource = Cls.PopulateDataView(dr)
            DataGridView2.Columns(1).Width = 60
            DataGridView2.Columns(4).Width = 90
            DataGridView2.Columns(5).Width = 200
            DataGridView2.Columns(0).Visible = False
            DataGridView2.Columns(4).Visible = False

            'DataGridView2.DataSource = Cls.PopulateDataViewVertical(dr)
            'DataGridView2.Columns(0).Width = 150 ' عرض عمود الخصائص
            'DataGridView2.Columns(1).Width = 250 ' عرض عمود القيم
            'DataGridView2.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None

            Dim SM As String
            For i As Integer = 0 To DataGridView2.RowCount - 1
                SM = Val(DataGridView2.Rows(i).Cells(3).Value.ToString)
                SM = Cls.R_date(SM)
                DataGridView2.Rows(i).Cells(3).Value = SM
            Next

            Dim SMQu, SM2 As Double
            For i As Integer = 0 To DataGridView2.Rows.Count - 1
                SMQu = SMQu + DataGridView2.Rows(i).Cells(7).Value
                SM2 = SM2 + DataGridView2.Rows(i).Cells(9).Value

            Next
            txtTotalQuntALL.Text = SMQu
            txtTotalALL.Text = SM2
        Else
            DataGridView1.DataSource = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = Cls.Get_Select_Grid_S("id as [ID],bill_no as [رقم العرض],Vendorname as [أسم العميل],bill_date as [التاريخ],totalprice as [الاجمالى]", "View_PriceOffer", "id <>N''")
            If chkAll.Checked = False Then
                If txtbillnoView.Text <> "" Then
                    S = S & " and bill_no =N'" & txtbillnoView.Text.Trim & "'"
                End If
                If cmbCustomersView.Text <> "" Then
                    S = S & " and Vendorname =N'" & cmbCustomersView.Text.Trim & "'"
                End If
            End If
            If ChkWithoutDate.Checked = False Then
                S = S & " and bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
            End If
            S = S & " order by bill_date"

            cmd.CommandText = S : dr = cmd.ExecuteReader
            DataGridView1.DataSource = Cls.PopulateDataView(dr)
            DataGridView1.Columns(1).Width = 90
            DataGridView1.Columns(2).Width = 120

            DataGridView1.Columns(0).Visible = False

            Dim SM As String
            For i As Integer = 0 To DataGridView1.RowCount - 1
                SM = Val(DataGridView1.Rows(i).Cells(3).Value.ToString)
                SM = Cls.R_date(SM)
                DataGridView1.Rows(i).Cells(3).Value = SM
            Next

            Dim SM2 As Double
            For i As Integer = 0 To DataGridView2.Rows.Count - 1
                SM2 = SM2 + DataGridView2.Rows(i).Cells(4).Value
            Next
            txtTotalALL.Text = SM2
        End If

        ActivUpdate = True
        btnSaveAll.Text = "تـعديل"
    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbCustomersView.Enabled = False
            txtbillnoView.Enabled = False
            txtFinditm_name.Enabled = False
            txtPrcView.Enabled = False
        Else
            cmbCustomersView.Enabled = True
            txtbillnoView.Enabled = True
            txtFinditm_name.Enabled = True
            txtPrcView.Enabled = True
        End If
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(sender As Object, e As EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub DataGridView1_DoubleClick(sender As Object, e As EventArgs) Handles DataGridView1.DoubleClick

        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            txtbillno.Text = DataGridView1.SelectedRows(0).Cells(1).Value

            PanelViewPriceOffer.Dock = DockStyle.None
            PanelViewPriceOffer.Top = 5000

            Dt_AddBill.Rows.Clear()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_PriceOffer where bill_no =N'" & txtbillno.Text & "'"
            dr = cmd.ExecuteReader
            If dr.Read Then
                dtpbill_date.Text = Cls.R_date(dr("bill_date").ToString())
                cmbCustomers.Text = dr("Vendorname").ToString
                txttotalpeforedisc.Text = dr("totalprice").ToString
            End If

            ', Val(txtdiscBill.Text), Val(DiscountsValue), Val(lblDiscount_Price_After.Text)
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select itm_id,itm_cat,itm_name,Unity,price,qu,totalprice,Stores,Discounts,DiscountsValue,Discount_Price_After,Height,Width,Altitude,Density,TotalSquareMeter,TotalWidthHeight from PriceOfferBill where bill_no =N'" & txtbillno.Text & "'  order by 1"
            dr = cmd.ExecuteReader
            Do While dr.Read
                DTGV.DataSource = Fn_AddBill(dr("itm_id").ToString, dr("itm_cat").ToString, dr("itm_name").ToString, dr("Unity").ToString, dr("price").ToString, dr("qu").ToString, dr("totalprice").ToString, dr("Stores").ToString, Val(dr("Discounts").ToString), Val(dr("DiscountsValue").ToString), Val(dr("Discount_Price_After").ToString), txtHeight, txtWidth, txtAltitude, txtDensity, TotalSquareMeter, TotalWidthHeight)
            Loop

            SumAllPrice()

            DTGV.Columns(0).Visible = False
            DTGV.Columns(1).Visible = False
            DTGV.Columns(8).Visible = False
            DTGV.Columns(9).Visible = False
            DTGV.Columns(10).Visible = False
            If Show_Height_Width_Altitude_Density = "NO" Then
                DTGV.Columns(11).Visible = False
                DTGV.Columns(12).Visible = False
                DTGV.Columns(13).Visible = False
                DTGV.Columns(14).Visible = False
                DTGV.Columns(15).Visible = False
                DTGV.Columns(16).Visible = False
            End If

    End Sub

    Private Sub btnDeleteAll_Click(sender As Object, e As EventArgs) Handles btnDeleteAll.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim bill_no As String

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            bill_no = DataGridView1.SelectedRows(i).Cells(1).Value

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete From  PriceOffer where bill_no =N'" & bill_no & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  PriceOfferBill where bill_no =N'" & bill_no & "'" : cmd.ExecuteNonQuery()
        Next

        btnShow_Click(sender, e)
    End Sub

    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        ActivUpdate = False
        btnSaveAll.Text = "حــــفظ"
        Clear_All()
        MAXRECORD()
        Dt_AddBill.Rows.Clear()
    End Sub

    Private Sub cmbUnity_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbUnity.SelectedIndexChanged
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id from items where sname=N'" & cmbitmnm.Text.Trim & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                TxtPrc.Text = dr("itm_id").ToString()
            End If

            If NotUnityItemsProgram = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & TxtPrc.Text & "' and Unity_Name=N'" & cmbUnity.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 1 : End If
                txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)

                If ItemsUnityNumber <> 1 Then
                    If ItemsUnityNumber <> 0 Then
                        Dim XCode As String = Cls.Get_Code_Value_Branch_More("ItemsUnity", "SalPriceUnit", "itm_id=N'" & TxtPrc.Text & "' and Unity_Name=N'" & cmbUnity.Text & "'")
                        If XCode <> "0" Then
                            If Val(txtPrice.Text) < Val(XCode) Then
                                txtPrice.Text = XCode
                            End If
                        End If
                    End If
                End If
            Else
                txtqunt.Text = Val(txtquntUnity.Text)
            End If

            If ShowDiscountRateItemSales = "NO" Then
                txtTotalPrice.Text = Val(txtPrice.Text) * Val(txtqunt.Text)
                txtTotalPrice.Text = Math.Round(Convert.ToDouble(txtTotalPrice.Text), 2)
            Else
                txtTotalPrice.Text = Val(txtRateDiscPriceAfter.Text) * Val(txtqunt.Text)
                txtTotalPrice.Text = Math.Round(Convert.ToDouble(txtTotalPrice.Text), 2)
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub txtquntUnity_TextChanged_1(sender As Object, e As EventArgs) Handles txtquntUnity.TextChanged
        MyVars.CheckNumber(txtqunt)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id from items where sname=N'" & cmbitmnm.Text.Trim & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            TxtPrc.Text = dr("itm_id").ToString()
        End If

        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & TxtPrc.Text & "' and Unity_Name=N'" & cmbUnity.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 1 : End If
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)

            If ItemsUnityNumber <> 1 Then
                If ItemsUnityNumber <> 0 Then
                    Dim XCode As String = Cls.Get_Code_Value_Branch_More("ItemsUnity", "SalPriceUnit", "itm_id=N'" & TxtPrc.Text & "' and Unity_Name=N'" & cmbUnity.Text & "'")
                    If XCode <> "0" Then
                        If Val(txtPrice.Text) < Val(XCode) Then
                            txtPrice.Text = XCode
                        End If
                    End If
                End If
            End If
        Else
            txtqunt.Text = Val(txtquntUnity.Text)
        End If

        If ShowDiscountRateItemSales = "NO" Then
            txtTotalPrice.Text = Val(txtPrice.Text) * Val(txtqunt.Text)
            txtTotalPrice.Text = Math.Round(Convert.ToDouble(txtTotalPrice.Text), 2)
        Else
            txtTotalPrice.Text = Val(txtRateDiscPriceAfter.Text) * Val(txtqunt.Text)
            txtTotalPrice.Text = Math.Round(Convert.ToDouble(txtTotalPrice.Text), 2)
        End If

        sumdisc1()
    End Sub

    Private Sub txtbillno_TextChanged(sender As Object, e As EventArgs) Handles txtbillno.TextChanged
        MyVars.CheckNumber(txtbillno)
    End Sub

    Private Sub btnAddBill_Click(sender As Object, e As EventArgs) Handles btnAddBill.Click
        PriceOfferBill = True
        OfferNumber = txtbillno.Text
        CustomersName = cmbCustomers.Text
        btnNew_Click(sender, e)
        Dim newForm As New List(Of FrmSales)
        newForm.Add(New FrmSales)
        newForm(0).Show()
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        PanelPrice.Top = 10000
    End Sub

    Private Sub WholeasalePrice()
        Try
            If rdoSectors.Checked = True Then
                txtPrice.Text = Cls.Get_Code_Value_Stores_More("items", "SalPrice", "sname=N'" & cmbitmnm.Text & "' and Stores=N'" & cmbStores.Text & "'")
                txtRateDiscPriceAfter.Text = Cls.Get_Code_Value_Stores_More("items", "RateDiscSalPriceAfter", "sname=N'" & cmbitmnm.Text & "' and Stores=N'" & cmbStores.Text & "'")
                txtdiscBill.Text = Cls.Get_Code_Value_Stores_More("items", "RateDiscSalPrice", "sname=N'" & cmbitmnm.Text & "' and Stores=N'" & cmbStores.Text & "'")
            End If

            If rdoWhole.Checked = True Then
                txtPrice.Text = Cls.Get_Code_Value_Stores_More("items", "WholePrice", "sname=N'" & cmbitmnm.Text & "' and Stores=N'" & cmbStores.Text & "'")
                txtRateDiscPriceAfter.Text = Cls.Get_Code_Value_Stores_More("items", "RateDiscWholePriceAfter", "sname=N'" & cmbitmnm.Text & "' and Stores=N'" & cmbStores.Text & "'")
                txtdiscBill.Text = Cls.Get_Code_Value_Stores_More("items", "RateDiscWholePrice", "sname=N'" & cmbitmnm.Text & "' and Stores=N'" & cmbStores.Text & "'")
            End If

            If rdoWholeWhole.Checked = True Then
                txtPrice.Text = Cls.Get_Code_Value_Stores_More("items", "WholeWholePrice", "sname=N'" & cmbitmnm.Text & "' and Stores=N'" & cmbStores.Text & "'")
                txtRateDiscPriceAfter.Text = Cls.Get_Code_Value_Stores_More("items", "RateDiscWholeWholePriceAfter", "sname=N'" & cmbitmnm.Text & "' and Stores=N'" & cmbStores.Text & "'")
                txtdiscBill.Text = Cls.Get_Code_Value_Stores_More("items", "RateDiscWholeWholePrice", "sname=N'" & cmbitmnm.Text & "' and Stores=N'" & cmbStores.Text & "'")
            End If
            'If rdoSectors.Checked = True Then
            '    txtPrice.Text = Cls.Get_Code_Value_Stores_More("items", "SalPrice", "sname=N'" & cmbitmnm.Text & "' and Stores=N'" & cmbStores.Text & "'")
            'End If

            'If rdoWhole.Checked = True Then
            '    txtPrice.Text = Cls.Get_Code_Value_Stores_More("items", "WholePrice", "sname=N'" & cmbitmnm.Text & "' and Stores=N'" & cmbStores.Text & "'")
            'End If

            'If rdoWholeWhole.Checked = True Then
            '    txtPrice.Text = Cls.Get_Code_Value_Stores_More("items", "WholeWholePrice", "sname=N'" & cmbitmnm.Text & "' and Stores=N'" & cmbStores.Text & "'")
            'End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub rdoSectors_CheckedChanged(sender As Object, e As EventArgs) Handles rdoSectors.CheckedChanged
        WholeasalePrice()
    End Sub

    Private Sub rdoWhole_CheckedChanged(sender As Object, e As EventArgs) Handles rdoWhole.CheckedChanged
        WholeasalePrice()
    End Sub

    Private Sub rdoWholeWhole_CheckedChanged(sender As Object, e As EventArgs) Handles rdoWholeWhole.CheckedChanged
        WholeasalePrice()
    End Sub

    Private Sub btnHelp_Click(sender As Object, e As EventArgs) Handles btnHelp.Click
        PanelPrice.Top = 250
    End Sub

    Private Sub cmbcats_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbcats.SelectedIndexChanged
        If cmbitmnm.Text = "" Then
            Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
            Try
                If cmbcats.Text.Trim = "" Then Exit Sub
                Cls.fill_combo_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbitmnm)
                cmbitmnm.Text = ""
            Catch ex As Exception
                ErrorHandling(ex, Me.Text)
            End Try
        End If
    End Sub

    Private Sub DataGridView2_DoubleClick(sender As Object, e As EventArgs) Handles DataGridView2.DoubleClick
        If DataGridView2.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView2.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        txtbillno.Text = DataGridView2.SelectedRows(0).Cells(1).Value

        PanelViewPriceOffer.Dock = DockStyle.None
        PanelViewPriceOffer.Top = 5000

        Dt_AddBill.Rows.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * from View_PriceOffer where bill_no =N'" & txtbillno.Text & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            dtpbill_date.Text = Cls.R_date(dr("bill_date").ToString())
            cmbCustomers.Text = dr("Vendorname").ToString
            txttotalpeforedisc.Text = dr("totalprice").ToString
        End If


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select itm_id,itm_cat,itm_name,Unity,price,qu,totalprice,Stores,Discounts,DiscountsValue,Discount_Price_After,Height,Width,Altitude,Density,TotalSquareMeter,TotalWidthHeight from PriceOfferBill where bill_no =N'" & txtbillno.Text & "'  order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            DTGV.DataSource = Fn_AddBill(dr("itm_id").ToString, dr("itm_cat").ToString, dr("itm_name").ToString, dr("Unity").ToString, dr("price").ToString, dr("qu").ToString, dr("totalprice").ToString, dr("Stores").ToString, Val(dr("Discounts").ToString), Val(dr("DiscountsValue").ToString), Val(dr("Discount_Price_After").ToString), txtHeight, txtWidth, txtAltitude, txtDensity, TotalSquareMeter, TotalWidthHeight)
        Loop

        SumAllPrice()
        DTGV.Columns(0).Visible = False
        DTGV.Columns(1).Visible = False
        DTGV.Columns(8).Visible = False
        DTGV.Columns(9).Visible = False
        DTGV.Columns(10).Visible = False
        If Show_Height_Width_Altitude_Density = "NO" Then
            DTGV.Columns(11).Visible = False
            DTGV.Columns(12).Visible = False
            DTGV.Columns(13).Visible = False
            DTGV.Columns(14).Visible = False
            DTGV.Columns(15).Visible = False
            DTGV.Columns(16).Visible = False
        End If
    End Sub

    Private Sub txtFinditm_name_TextChanged(sender As Object, e As EventArgs) Handles txtFinditm_name.TextChanged
        btnShow_Click(sender, e)
    End Sub

    Private Sub chkFreeSearchByItems_CheckedChanged(sender As Object, e As EventArgs) Handles chkFreeSearchByItems.CheckedChanged
        If chkFreeSearchByItems.Checked = True Then
            DataGridView1.Visible = False
            DataGridView2.Visible = True
        Else
            DataGridView1.Visible = True
            DataGridView2.Visible = False
        End If
        btnShow_Click(sender, e)
    End Sub

    Private Sub txtdiscBill_TextChanged(sender As Object, e As EventArgs) Handles txtdiscBill.TextChanged
        MyVars.CheckNumber(txtdiscBill)
        sumdisc1()
    End Sub

    Private Sub sumdisc1()
        Dim DiscVal As Double
        Dim TotalPriseQunt As Double
        If ShowDiscountRateItemSales = "NO" Then
            TotalPriseQunt = Val(txtPrice.Text) * Val(txtquntUnity.Text)
            TotalPriseQunt = Math.Round(Convert.ToDouble(TotalPriseQunt), 2)
        Else
            If txtRateDiscPriceAfter.Text = "0" Then
                txtRateDiscPriceAfter.Text = txtPrice.Text
            End If
            TotalPriseQunt = Val(txtRateDiscPriceAfter.Text) * Val(txtquntUnity.Text)
            TotalPriseQunt = Math.Round(Convert.ToDouble(TotalPriseQunt), 2)
        End If

        If ChkCent2.Checked = True Then
            If ShowDiscountRateItemSales = "NO" Then
                DiscVal = Val((Val(TotalPriseQunt) * (100 - Val(txtdiscBill.Text))) / 100)
            Else
                DiscVal = Val(TotalPriseQunt)
            End If
            DiscVal = Math.Round(DiscVal, 2)
        ElseIf ChkVal2.Checked = True Then
            DiscVal = Val(TotalPriseQunt) - Val(txtdiscBill.Text)
        End If
        txtTotalPrice.Text = Math.Round(TotalValueVAT, 2) + Math.Round(DiscVal, 2)

        If ChkCent2.Checked = True Then
            StateDisc = "نسبة"
            If ShowDiscountRateItemSales = "NO" Then
                lblDiscount_Price_After.Text = Val((Val(Val(txtPrice.Text) * Val(1)) * (100 - Val(txtdiscBill.Text))) / 100)
                Dim XVal As String = Format(Val(txtPrice.Text) * Val(txtdiscBill.Text) / 100, "Fixed")
                DiscountsValue = Val(XVal) * Val(txtquntUnity.Text)
            Else
                lblDiscount_Price_After.Text = Val((Val(Val(txtPrice.Text) * Val(1)) * (100 + Val(txtdiscBill.Text))) / 100)
                Dim XVal As String = Format(Val(txtPrice.Text) * Val(txtdiscBill.Text) / 100, "Fixed")
                DiscountsValue = Val(XVal) * Val(txtquntUnity.Text)
            End If
        Else
            StateDisc = "قيمة"
            DiscountsValue = Val(txtdiscBill.Text)
        End If
    End Sub

    Private Sub ChkCent2_CheckedChanged(sender As Object, e As EventArgs) Handles ChkCent2.CheckedChanged
        sumdisc1()
    End Sub

    Private Sub ChkVal2_CheckedChanged(sender As Object, e As EventArgs) Handles ChkVal2.CheckedChanged
        sumdisc1()
    End Sub

    Private Sub TxtPrc_KeyUp(sender As Object, e As KeyEventArgs) Handles TxtPrc.KeyUp
        If e.KeyCode = 13 Then
            AlaertParcode = True
            If TxtPrc.Text.Trim = "" Then
            Else
                Bol = True
                Try
                    ItemsUnityNumber = 0
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select group_name,sname,SalPrice,RateDiscSalPriceAfter from items where itm_id=N'" & TxtPrc.Text & "'"
                    dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then
                        cmbcats.Text = dr("group_name").ToString()
                        cmbitmnm.Text = dr("sname").ToString()
                        txtPrice.Text = Val(dr("SalPrice").ToString())
                        txtRateDiscPriceAfter.Text = Val(dr("RateDiscSalPriceAfter").ToString)
                    End If

                    txtqunt.Text = 1
                    txtquntUnity.Text = 1

                    GetItemsUnity(cmbUnity, TxtPrc.Text)

                    SetItemsUnity()

                    GetHeightWidthAltitudeDensity()

                    Dim DiscountItems As String = mykey.GetValue("DiscountItems", "0")
                    If DiscountItems <> 0 Then
                        txtdiscBill.Text = mykey.GetValue("DiscountItems", "0")
                    End If

                    If txtRateDiscPriceAfter.Text = "0" Then
                        txtRateDiscPriceAfter.Text = txtPrice.Text
                    End If

                    'If ShowDiscountRateItemSales = "YES" Then
                    '    txtPrice.Text = txtRateDiscPriceAfter.Text
                    'End If

                    WholeasalePrice()

                    If NotUnityItemsProgram = "YES" Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & TxtPrc.Text & "' and Unity_Name=N'" & cmbUnity.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
                        If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 1 : End If
                        txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)

                        If ItemsUnityNumber <> 1 Then
                            If ItemsUnityNumber <> 0 Then
                                Dim XCode As String = Cls.Get_Code_Value_Branch_More("ItemsUnity", "SalPriceUnit", "itm_id=N'" & TxtPrc.Text & "' and Unity_Name=N'" & cmbUnity.Text & "'")
                                If XCode <> "0" Then
                                    If Val(txtPrice.Text) < Val(XCode) Then
                                        txtPrice.Text = XCode
                                    End If
                                End If
                            End If
                        End If
                    Else
                        txtqunt.Text = Val(txtquntUnity.Text)
                    End If

                    sumdisc1()

                    txtPrice.Focus()
                    txtPrice.SelectAll()

                Catch ex As Exception
                End Try

                Bol = False

            End If

            AlaertParcode = False
        End If
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub

    Private Sub cmbitmnm_KeyDown(sender As Object, e As KeyEventArgs) Handles cmbitmnm.KeyDown
        If ((e.KeyCode = Keys.H) AndAlso (e.Modifiers = (Keys.Control))) Then
            If Show_Height_Width_Altitude_Density = "YES" Then
                FrmWidthHeightAltitude.Show()
            End If
        End If
    End Sub

    Private Sub GetHeightWidthAltitudeDensityTotal()
        Try
            If Show_Height_Width_Altitude_Density = "YES" Then
                TotalSquareMeter = Val(txtquntUnity.Text) * Val(txtHeight) * Val(txtWidth) * Val(txtAltitude) / 1000000
                TotalSquareMeter = Math.Round(TotalSquareMeter, 1)

                TotalWidthHeight = Val(txtquntUnity.Text) * Val(txtHeight) * Val(txtWidth) * Val(txtAltitude) * Val(txtPrice.Text) / 1000000
                TotalWidthHeight = Math.Round(TotalWidthHeight, 2)
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
End Class