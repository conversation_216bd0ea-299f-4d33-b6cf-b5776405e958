﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Frm_Sheft_Status
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle5 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle6 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.btnSheft_StatusOpen = New System.Windows.Forms.Button()
        Me.txtSheft_Number = New System.Windows.Forms.TextBox()
        Me.Dgv_Add = New System.Windows.Forms.DataGridView()
        Me.btnSheft_StatusClose = New System.Windows.Forms.Button()
        Me.btnDelete = New System.Windows.Forms.Button()
        Me.DateTimePicker1 = New System.Windows.Forms.DateTimePicker()
        CType(Me.Dgv_Add, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btnSheft_StatusOpen
        '
        Me.btnSheft_StatusOpen.Font = New System.Drawing.Font("Tahoma", 11.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnSheft_StatusOpen.Location = New System.Drawing.Point(579, 35)
        Me.btnSheft_StatusOpen.Name = "btnSheft_StatusOpen"
        Me.btnSheft_StatusOpen.Size = New System.Drawing.Size(158, 56)
        Me.btnSheft_StatusOpen.TabIndex = 0
        Me.btnSheft_StatusOpen.Text = "فتح شيفت"
        Me.btnSheft_StatusOpen.UseVisualStyleBackColor = True
        '
        'txtSheft_Number
        '
        Me.txtSheft_Number.Enabled = False
        Me.txtSheft_Number.Font = New System.Drawing.Font("Tahoma", 18.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtSheft_Number.Location = New System.Drawing.Point(218, 42)
        Me.txtSheft_Number.Multiline = True
        Me.txtSheft_Number.Name = "txtSheft_Number"
        Me.txtSheft_Number.Size = New System.Drawing.Size(178, 42)
        Me.txtSheft_Number.TabIndex = 1
        Me.txtSheft_Number.Text = "0"
        Me.txtSheft_Number.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Dgv_Add
        '
        Me.Dgv_Add.AllowUserToAddRows = False
        Me.Dgv_Add.AllowUserToDeleteRows = False
        Me.Dgv_Add.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Dgv_Add.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill
        Me.Dgv_Add.BackgroundColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle4.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle4.Font = New System.Drawing.Font("Segoe UI", 11.25!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle4.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle4.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle4.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.Dgv_Add.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle4
        Me.Dgv_Add.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        DataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle5.BackColor = System.Drawing.SystemColors.Window
        DataGridViewCellStyle5.Font = New System.Drawing.Font("Tahoma", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle5.ForeColor = System.Drawing.SystemColors.ControlText
        DataGridViewCellStyle5.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle5.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.Dgv_Add.DefaultCellStyle = DataGridViewCellStyle5
        Me.Dgv_Add.Location = New System.Drawing.Point(2, 127)
        Me.Dgv_Add.Name = "Dgv_Add"
        Me.Dgv_Add.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        DataGridViewCellStyle6.Font = New System.Drawing.Font("Tahoma", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle6.SelectionBackColor = System.Drawing.Color.SteelBlue
        Me.Dgv_Add.RowsDefaultCellStyle = DataGridViewCellStyle6
        Me.Dgv_Add.RowTemplate.Height = 25
        Me.Dgv_Add.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.Dgv_Add.Size = New System.Drawing.Size(744, 359)
        Me.Dgv_Add.TabIndex = 90
        '
        'btnSheft_StatusClose
        '
        Me.btnSheft_StatusClose.Font = New System.Drawing.Font("Tahoma", 11.25!)
        Me.btnSheft_StatusClose.Location = New System.Drawing.Point(415, 35)
        Me.btnSheft_StatusClose.Name = "btnSheft_StatusClose"
        Me.btnSheft_StatusClose.Size = New System.Drawing.Size(158, 56)
        Me.btnSheft_StatusClose.TabIndex = 91
        Me.btnSheft_StatusClose.Text = "إغلاق شيفت"
        Me.btnSheft_StatusClose.UseVisualStyleBackColor = True
        '
        'btnDelete
        '
        Me.btnDelete.Font = New System.Drawing.Font("Tahoma", 11.25!)
        Me.btnDelete.Location = New System.Drawing.Point(12, 35)
        Me.btnDelete.Name = "btnDelete"
        Me.btnDelete.Size = New System.Drawing.Size(158, 56)
        Me.btnDelete.TabIndex = 92
        Me.btnDelete.Text = "حذف"
        Me.btnDelete.UseVisualStyleBackColor = True
        '
        'DateTimePicker1
        '
        Me.DateTimePicker1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DateTimePicker1.CustomFormat = "dd/MM/yyyy"
        Me.DateTimePicker1.Font = New System.Drawing.Font("Tahoma", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.DateTimePicker1.Format = System.Windows.Forms.DateTimePickerFormat.[Short]
        Me.DateTimePicker1.Location = New System.Drawing.Point(218, 9)
        Me.DateTimePicker1.Name = "DateTimePicker1"
        Me.DateTimePicker1.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.DateTimePicker1.RightToLeftLayout = True
        Me.DateTimePicker1.Size = New System.Drawing.Size(178, 27)
        Me.DateTimePicker1.TabIndex = 93
        '
        'Frm_Sheft_Status
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(749, 487)
        Me.Controls.Add(Me.DateTimePicker1)
        Me.Controls.Add(Me.btnDelete)
        Me.Controls.Add(Me.btnSheft_StatusClose)
        Me.Controls.Add(Me.Dgv_Add)
        Me.Controls.Add(Me.txtSheft_Number)
        Me.Controls.Add(Me.btnSheft_StatusOpen)
        Me.MaximizeBox = False
        Me.Name = "Frm_Sheft_Status"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "الشيفتات"
        CType(Me.Dgv_Add, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents btnSheft_StatusOpen As System.Windows.Forms.Button
    Friend WithEvents txtSheft_Number As System.Windows.Forms.TextBox
    Friend WithEvents Dgv_Add As System.Windows.Forms.DataGridView
    Friend WithEvents btnSheft_StatusClose As System.Windows.Forms.Button
    Friend WithEvents btnDelete As System.Windows.Forms.Button
    Friend WithEvents DateTimePicker1 As System.Windows.Forms.DateTimePicker
End Class
