﻿Imports CrystalDecisions.CrystalReports.Engine
Imports System.IO
Public Class frmShowAccounts
    Dim rpt As New Rpt_Accounts
    Dim WithEvents BS As New BindingSource
    Dim Simage As Image
    Friend WithEvents TreeView1 As System.Windows.Forms.TreeView

    Private Sub BtnPrt_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnPrt.Click
        Dim txt, txtNameAr, txtNameEn As TextObject
        Dim f As New Frm_PrintReports
        On Error Resume Next
        AddReportView()
        GETSERVERNAMEANDDATABASENAME(rpt, DataBaseName, PasswordServer, UserNameServer)
        If ChbAll.Checked = False Then
            rpt.RecordSelectionFormula = "{View_AccountsTree.ACCName} =N'" & cmbNameAccount.Text & "'"
        End If

        If ChbAll.Checked = True Then
            rpt.RecordSelectionFormula = ""
        End If
        txt = rpt.Section1.ReportObjects("Text12")
        txt.Text = "تقرير شجرة الحسابات"
        txtNameAr = rpt.Section1.ReportObjects("Text1")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("Text2")
        txtNameEn.Text = NameEnCompany
        f.CrystalReportViewer1.ReportSource = rpt
        'f.CrystalReportViewer1.Zoom(65%)
        f.CrystalReportViewer1.RefreshReport()
        f.Show()
    End Sub

    Private Sub frmAccountsShow_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Bra.Fil("Accounts_Main", "Main_Name", cmbMainAccount)
        GetData()
    End Sub
    Private Sub GetData()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select Main_Name,Groub_Name,ACCNumber,ACCName,ACCDebtor,ACCCreditor,ACCNotes From View_AccountsTree where Main_Code <> N''"

        If cmbMainAccount.Text <> "" Then
            S = S & " and Main_Name =N'" & cmbMainAccount.Text.Trim & "'"
        End If
        If cmbGroubAccount.Text <> "" Then
            S = S & " and Groub_Name =N'" & cmbGroubAccount.Text.Trim & "'"
        End If
        If cmbNameAccount.Text <> "" Then
            S = S & " and ACCName =N'" & cmbNameAccount.Text.Trim & "'"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
        DataGridView1.Columns(0).HeaderCell.Value = "الحساب الرئيسى"
        DataGridView1.Columns(1).HeaderCell.Value = "مجموعة الحساب"
        DataGridView1.Columns(2).HeaderCell.Value = "رقم الحساب"
        DataGridView1.Columns(3).HeaderCell.Value = "أسم الحساب"
        DataGridView1.Columns(4).HeaderCell.Value = "رصيد مدين افتتاحى"
        DataGridView1.Columns(5).HeaderCell.Value = "رصيد دائن افتتاحى"
        DataGridView1.Columns(6).HeaderCell.Value = "ملاحظات"
    End Sub

    Private Sub BtnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnShow.Click
        GetData()
    End Sub

    Private Sub ChbAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChbAll.CheckedChanged
        If ChbAll.Checked = True Then
            cmbNameAccount.Enabled = False
            cmbNameAccount.SelectedIndex = -1
        Else
            cmbNameAccount.Enabled = True
        End If
    End Sub

    Private Sub cmbMainAccount_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbMainAccount.SelectedIndexChanged
        If cmbMainAccount.Text.Trim = "" Then Exit Sub
        cmbGroubAccount.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select distinct Groub_Name from View_AccountsTree where Main_Name =N'" & cmbMainAccount.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbGroubAccount.Items.Add(Trim(dr(0)))
        Loop
        cmbGroubAccount.SelectedIndex = -1
    End Sub

    Private Sub cmbGroubAccount_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbGroubAccount.SelectedIndexChanged
        If cmbGroubAccount.Text.Trim = "" Then Exit Sub
        cmbNameAccount.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select distinct ACCName from View_AccountsTree where Groub_Name =N'" & cmbGroubAccount.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbNameAccount.Items.Add(Trim(dr(0)))
        Loop
        cmbNameAccount.Text = ""
    End Sub

    Private Sub chkMain_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkMain.CheckedChanged
        If chkMain.Checked = True Then
            cmbMainAccount.Enabled = False
            cmbMainAccount.SelectedIndex = -1
        Else
            cmbMainAccount.Enabled = True
        End If
    End Sub

    Private Sub chkGroub_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkGroub.CheckedChanged
        If chkGroub.Checked = True Then
            cmbGroubAccount.Enabled = False
            cmbGroubAccount.SelectedIndex = -1
        Else
            cmbGroubAccount.Enabled = True
        End If
    End Sub
End Class