﻿Imports System.Data.SqlClient

Public Class FrmPleaseWait
    Private Sub Form2_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Location = New Point(CInt((Screen.PrimaryScreen.WorkingArea.Width / 2) - (Me.Width / 2)), CInt((Screen.PrimaryScreen.WorkingArea.Height / 2) - (Me.Height / 2)))
        Button1.Left = CInt((Me.ClientRectangle.Width / 2) - (Button1.Width / 2))
        PictureBox1.Size = My.Resources.Loading.Size
        PictureBox1.Image = My.Resources.Loading
        PictureBox1.Left = CInt((Me.ClientRectangle.Width / 2) - (PictureBox1.Width / 2))
        PictureBox1.Visible = False
        BackgroundWorker1.WorkerSupportsCancellation = True
        Button1_Click(sender, e)
    End Sub

    Private Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork

        'Dim sw As New Stopwatch
        'sw.Start()
        ''------------------------------
        Dim <PERSON>Loop As Long


        Cn.Close()
        connect()

        'MsgBox(Cls_Constant.ErrMsg)


        ''------------------------------
        'sw.Stop()
        'MsgBox(sw.Elapsed.ToString)

        Me.Close()
    End Sub

    Private Sub BackgroundWorker1_ProgressChanged(sender As Object, e As System.ComponentModel.ProgressChangedEventArgs) Handles BackgroundWorker1.ProgressChanged
        'ProgressBar1.Value = e.ProgressPercentage
    End Sub

    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        'MsgBox("You Don App ......", MsgBoxStyle.Information)
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Button1.Refresh()
        PictureBox1.Visible = True
        PictureBox1.Refresh()
        BackgroundWorker1.RunWorkerAsync()
        Control.CheckForIllegalCrossThreadCalls = False
    End Sub
End Class