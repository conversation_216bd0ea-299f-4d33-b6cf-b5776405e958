﻿Public Class Frm_Product_Manufacturing
    Dim Dt_AddBill As New DataTable
    Dim RNXD As Integer
    Dim itm_id As String = ""
    Dim TinPrice As String = ""
    Dim Manufacturing_ID As String
    Dim AutoNumber As Integer
    Private Sub Frm_Offers_Items_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Bra.Fil("groups", "g_name", cmbCatsMaterial)
        Bra.Fil("groups", "g_name", cmbCatsManufacturing)
        Cls.fill_combo_Branch("stores", "store", cmbStoreManufacturing)
        Cls.fill_combo_Branch("stores", "store", cmbStoreMaterial)
    End Sub

    Private Sub btnAddNewItems_Click(sender As Object, e As EventArgs) Handles btnAddNewItems.Click
        FrmItemsNew.ShowDialog()
    End Sub

    Private Sub cmbCatsManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbCatsManufacturing.SelectedIndexChanged
        If cmbCatsManufacturing.Text.Trim = "" Then Exit Sub
        cmbItemsManufacturing.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sname from Items where group_name =N'" & cmbCatsManufacturing.Text & "' and Stores =N'" & cmbStoreManufacturing.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbItemsManufacturing.Items.Add(Trim(dr(0)))
        Loop
        cmbItemsManufacturing.Text = ""
    End Sub

    Private Sub cmbCatsMaterial_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbCatsMaterial.SelectedIndexChanged
        If cmbCatsMaterial.Text.Trim = "" Then Exit Sub
        cmbItemsMaterial.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sname from Items where group_name =N'" & cmbCatsMaterial.Text & "' and Stores =N'" & cmbStoreMaterial.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbItemsMaterial.Items.Add(Trim(dr(0)))
        Loop
        cmbItemsMaterial.Text = ""
    End Sub

    Private Sub BtnAdd_Click(sender As Object, e As EventArgs) Handles BtnAdd.Click
        If ValidateTextAdd() = False Then Exit Sub

        Dim XTotal As Double = Val(txtTinPrice.Text) * Val(txtqunt.Text)
        XTotal = Math.Round(XTotal, 2)

        txtAutoNumber.Text = 1
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            txtAutoNumber.Text += 1
        Next


        Dgv_Add.DataSource = Fn_AddBill(txtItm_idMaterial.Text, cmbCatsMaterial.Text, cmbItemsMaterial.Text, Val(txtTinPrice.Text), Val(txtqunt.Text), Val(txtquntUnity.Text), cmbUnity.Text, Val(XTotal), cmbStoreMaterial.Text, txtAutoNumber.Text)

        cmbItemsMaterial.Text = ""
        txtqunt.Text = ""
        txtItm_idMaterial.Text = ""
        txtTinPrice.Text = ""
        txtquntUnity.Text = ""
        cmbUnity.Text = ""
        cmbItemsMaterial.Focus()

        SumTotal()

        Dgv_Add.Columns(0).ReadOnly = True
        Dgv_Add.Columns(1).ReadOnly = True
        Dgv_Add.Columns(2).ReadOnly = True
        Dgv_Add.Columns(3).ReadOnly = False
        Dgv_Add.Columns(4).ReadOnly = True
        Dgv_Add.Columns(6).ReadOnly = True
        Dgv_Add.Columns(7).ReadOnly = True
        Dgv_Add.Columns(4).Visible = False
        Dgv_Add.Columns(9).Visible = False
    End Sub

    Private Sub SumTotal()
        Dim SM As Double
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            SM = SM + Dgv_Add.Rows(i).Cells(7).Value
        Next
        txtCostPrice.Text = SM + Val(txtFilling_Allowance.Text) + Val(txtManufacturing_Allowance.Text)

        Dim SM1 As Double
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            SM1 = SM1 + Dgv_Add.Rows(i).Cells(5).Value
        Next
        txtWeight.Text = SM1

        txtNumberMaterial.Text = Dgv_Add.RowCount
    End Sub

    Friend Function Fn_AddBill(ByVal Col_Prc As String, ByVal Col_Cats As String, ByVal Col_Name As String, ByVal Col_Price As Double, ByVal Col_qunt As Double, ByVal Col_qu_unity As Double, ByVal Col_itm_Unity As String, ByVal Col_Total As Double, ByVal Col_Store As String, Col_AutoNumber As Integer) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("الباركود", GetType(String))
            Dt_AddBill.Columns.Add("أسم المجموعة", GetType(String))
            Dt_AddBill.Columns.Add("أسم الصنف", GetType(String))
            Dt_AddBill.Columns.Add("السعر", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية1", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية", GetType(Double))
            Dt_AddBill.Columns.Add("الوحدة", GetType(String))
            Dt_AddBill.Columns.Add("الاجمالى", GetType(Double))
            Dt_AddBill.Columns.Add("المخزن", GetType(String))
            Dt_AddBill.Columns.Add("AutoNumber", GetType(Integer))
        End If

        DTV_Width()


        Dt_AddBill.Rows.Add(Col_Prc, Col_Cats, Col_Name, Col_Price, Col_qunt, Col_qu_unity, Col_itm_Unity, Col_Total, Col_Store, Col_AutoNumber)
        Return Dt_AddBill
    End Function

    Friend Sub DTV_Width()
        If Dgv_Add.Rows.Count > 10000 Then
            Dgv_Add.Columns(0).Width = 90
            Dgv_Add.Columns(1).Width = 100
            Dgv_Add.Columns(2).Width = 150
            Dgv_Add.Columns(3).Width = 90
            Dgv_Add.Columns(4).Width = 90
            Dgv_Add.Columns(5).Width = 90
            Dgv_Add.Columns(6).Width = 90
        End If
    End Sub

    Function ValidateTextAdd() As Boolean
        If cmbStoreMaterial.Text = "" Then MsgBox("فضلا أختر أسم المخزن", MsgBoxStyle.Exclamation) : cmbStoreMaterial.Focus() : Return False
        If cmbCatsMaterial.Text = "" Then MsgBox("فضلا أختر أسم المجموعة", MsgBoxStyle.Exclamation) : cmbCatsMaterial.Focus() : Return False
        If cmbItemsMaterial.Text = "" Then MsgBox("فضلا أدخل أسم الخامة", MsgBoxStyle.Exclamation) : cmbItemsMaterial.Focus() : Return False
        If cmbStoreManufacturing.Text = "" Then MsgBox("فضلا أختر أسم المخزن", MsgBoxStyle.Exclamation) : cmbStoreManufacturing.Focus() : Return False
        If cmbCatsManufacturing.Text = "" Then MsgBox("فضلا أختر أسم المجموعة", MsgBoxStyle.Exclamation) : cmbCatsManufacturing.Focus() : Return False
        If cmbItemsManufacturing.Text = "" Then MsgBox("فضلا أدخل أسم الخامة", MsgBoxStyle.Exclamation) : cmbItemsManufacturing.Focus() : Return False
        If txtqunt.Text = "" Then MsgBox("فضلا أدخل الكمية", MsgBoxStyle.Exclamation) : txtqunt.Focus() : Return False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from Items where group_name =N'" & cmbCatsMaterial.Text.Trim & "'and sname =N'" & cmbItemsMaterial.Text.Trim & "'and Stores =N'" & cmbStoreMaterial.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H = 0 Then
            MsgBox("الصنف غير مطابق من فضلك راجع الصنف ", MsgBoxStyle.Exclamation) : cmbItemsMaterial.Focus() : Return False
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from Items where group_name =N'" & cmbCatsManufacturing.Text.Trim & "'and sname =N'" & cmbItemsManufacturing.Text.Trim & "'and Stores =N'" & cmbStoreManufacturing.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H = 0 Then
            MsgBox("الصنف غير مطابق من فضلك راجع الصنف ", MsgBoxStyle.Exclamation) : cmbItemsManufacturing.Focus() : Return False
        End If

        'For i As Integer = 0 To Dt_AddBill.Rows.Count - 1
        '    If Dgv_Add.Rows(i).Cells(2).Value = cmbItemsMaterial.Text.Trim Then MsgBox("صنف مكرر بنفس القائمة", MsgBoxStyle.Exclamation) : cmbItemsMaterial.Focus() : cmbItemsMaterial.SelectAll() : Return False
        'Next
        Return True
    End Function

    Private Sub cmbStoreManufacturing_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbStoreManufacturing.KeyUp
        If e.KeyCode = 13 Then
            cmbCatsManufacturing.Focus()
        End If
    End Sub

    Private Sub cmbCatsManufacturing_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbCatsManufacturing.KeyUp
        If e.KeyCode = 13 Then
            cmbItemsManufacturing.Focus()
        End If
    End Sub

    Private Sub cmbStoreMaterial_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbStoreMaterial.KeyUp
        If e.KeyCode = 13 Then
            cmbCatsMaterial.Focus()
        End If
    End Sub

    Private Sub cmbCatsMaterial_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbCatsMaterial.KeyUp
        If e.KeyCode = 13 Then
            cmbItemsMaterial.Focus()
        End If
    End Sub

    Private Sub cmbItemsMaterial_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbItemsMaterial.KeyUp
        If e.KeyCode = 13 Then
            txtquntUnity.Focus()
        End If
    End Sub

    Private Sub ClearSave()
        cmbCatsMaterial.Text = ""
        cmbCatsManufacturing.Text = ""
        cmbStoreMaterial.Text = ""
        cmbStoreManufacturing.Text = ""
        cmbItemsMaterial.Text = ""
        cmbItemsManufacturing.Text = ""
        txtNumberMaterial.Text = ""
        txtqunt.Text = ""
        txtItm_idMaterial.Text = ""
        txtItm_idManufacturing.Text = ""
        txtWeight.Text = ""
        txtCostPrice.Text = ""
        txtNumberMaterial.Text = ""
        txtFilling_Allowance.Text = ""
        txtManufacturing_Allowance.Text = ""
        txtTinPrice.Text = ""
        txtAutoNumber.Text = 1
        Dt_AddBill.Rows.Clear()
    End Sub

    Private Sub BtnClear_Click(sender As Object, e As EventArgs) Handles BtnClear.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        ClearSave()
    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs) Handles BtnDelete.Click
        If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        For i As Integer = 0 To Dgv_Add.SelectedRows.Count - 1
            RNXD = Dgv_Add.CurrentRow.Index
            Dgv_Add.Rows.RemoveAt(RNXD)
        Next
        txtNumberMaterial.Text = Dgv_Add.RowCount

        txtAutoNumber.Text = 1
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            txtAutoNumber.Text += 1
        Next
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateTextSave() = False Then Exit Sub

        MAXRECORD()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into ManufacturingProduct(Manufacturing_ID,itm_id_Manufacturing,Stores_Manufacturing,Manufacturing_Allowance,Filling_Allowance,CostPrice,Weightqunt,NumberMaterial,UserName) values ("
        S = S & "N'" & Manufacturing_ID & "' ,N'" & txtItm_idManufacturing.Text.Trim & "' ,N'" & cmbStoreManufacturing.Text & "',N'" & txtManufacturing_Allowance.Text & "',N'" & txtFilling_Allowance.Text & "',N'" & txtCostPrice.Text & "',N'" & txtWeight.Text & "',N'" & txtNumberMaterial.Text & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1

            S = "insert into ManufacturingProductAdd(Manufacturing_ID,itm_id,qu,qu_unity,itm_Unity,totalprice,Stores,UserName,AutoNumber)  values("
            S = S & "N'" & Manufacturing_ID & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "',N'" & UserName & "',N'" & Dgv_Add.Rows(i).Cells(9).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            IM.Store(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value)

            If ConnectOnlineStore = "YES" Then
                EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
                StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
                Cos.UpdateProductStock(StockOnline, Dgv_Add.Rows(i).Cells(0).Value, EditItmId)
            End If
        Next

        ClearSave()
        MsgBox("تم حفظ بيانات المنتج بنجاح", MsgBoxStyle.Information)
    End Sub

    Function ValidateTextSave() As Boolean
        If cmbStoreManufacturing.Text = "" Then MsgBox("فضلا أختر أسم المخزن", MsgBoxStyle.Exclamation) : cmbStoreManufacturing.Focus() : Return False
        If cmbCatsManufacturing.Text = "" Then MsgBox("فضلا أختر أسم المجموعة", MsgBoxStyle.Exclamation) : cmbCatsManufacturing.Focus() : Return False
        If cmbItemsManufacturing.Text = "" Then MsgBox("فضلا أدخل أسم الخامة", MsgBoxStyle.Exclamation) : cmbItemsManufacturing.Focus() : Return False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from Items where group_name =N'" & cmbCatsManufacturing.Text.Trim & "'and sname =N'" & cmbItemsManufacturing.Text.Trim & "'and Stores =N'" & cmbStoreManufacturing.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H = 0 Then
            MsgBox("الصنف غير مطابق من فضلك راجع الصنف ", MsgBoxStyle.Exclamation) : cmbItemsManufacturing.Focus() : Return False
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from ManufacturingProduct where itm_id_Manufacturing =N'" & txtItm_idManufacturing.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H <> 0 Then
            MsgBox("الصنف مسجل مسبقا فى المنتجات المصنعة", MsgBoxStyle.Exclamation) : cmbItemsManufacturing.Focus() : Return False
        End If

        Return True
    End Function

    Private Sub MAXRECORD()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from ManufacturingProduct"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Manufacturing_ID = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(Manufacturing_ID As float)) as mb FROM ManufacturingProduct where Manufacturing_ID <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            Manufacturing_ID = sh + 1
        End If

    End Sub

    Private Sub cmbItemsMaterial_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbItemsMaterial.SelectedIndexChanged
        Get_ITMID(cmbItemsMaterial.Text, cmbCatsMaterial.Text, cmbStoreMaterial.Text)
        txtItm_idMaterial.Text = itm_id
        txtTinPrice.Text = TinPrice

        GetItemsUnity(cmbUnity, txtItm_idMaterial.Text)

        txtqunt.Text = 1
        txtquntUnity.Text = 1
        txtquntUnity.Focus()
        txtquntUnity.SelectAll()
    End Sub

    Private Sub Get_ITMID(ByVal Items As String, ByVal Cats As String, ByVal Store As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id,TinPrice from items where sname=N'" & Items & "' and group_name=N'" & Cats & "' and Stores=N'" & Store & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            itm_id = dr("itm_id")
            TinPrice = dr("TinPrice")
        End If
    End Sub

    Private Sub cmbItemsManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbItemsManufacturing.SelectedIndexChanged
        Get_ITMID(cmbItemsManufacturing.Text, cmbCatsManufacturing.Text, cmbStoreManufacturing.Text)
        txtItm_idManufacturing.Text = itm_id
        txtManufacturing_Allowance.Focus()
    End Sub

    Private Sub cmbStoreManufacturing_DropDown(sender As Object, e As EventArgs) Handles cmbStoreManufacturing.DropDown
        cmbCatsManufacturing.Text = ""
        cmbItemsManufacturing.Text = ""
        txtItm_idManufacturing.Text = ""
    End Sub

    Private Sub cmbCatsManufacturing_DropDown(sender As Object, e As EventArgs) Handles cmbCatsManufacturing.DropDown
        cmbItemsManufacturing.Text = ""
        txtItm_idManufacturing.Text = ""
    End Sub

    Private Sub cmbStoreMaterial_DropDown(sender As Object, e As EventArgs) Handles cmbStoreMaterial.DropDown
        cmbCatsMaterial.Text = ""
        cmbItemsMaterial.Text = ""
        txtquntUnity.Text = ""
        cmbUnity.Text = ""
    End Sub

    Private Sub cmbCatsMaterial_DropDown(sender As Object, e As EventArgs) Handles cmbCatsMaterial.DropDown
        cmbItemsMaterial.Text = ""
        txtquntUnity.Text = ""
        cmbUnity.Text = ""
    End Sub

    Private Sub cmbStoreManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbStoreManufacturing.SelectedIndexChanged
        If cmbStoreManufacturing.Text.Trim = "" Then Exit Sub
        cmbCatsManufacturing.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select distinct group_name from Items where Stores =N'" & cmbStoreManufacturing.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbCatsManufacturing.Items.Add(Trim(dr(0)))
        Loop
        cmbCatsManufacturing.Text = ""
    End Sub

    Private Sub cmbStoreMaterial_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbStoreMaterial.SelectedIndexChanged
        If cmbStoreMaterial.Text.Trim = "" Then Exit Sub
        cmbCatsMaterial.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select distinct group_name from Items where Stores =N'" & cmbStoreMaterial.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbCatsMaterial.Items.Add(Trim(dr(0)))
        Loop
        cmbCatsMaterial.Text = ""
    End Sub

    Private Sub cmbItemsManufacturing_DropDown(sender As Object, e As EventArgs) Handles cmbItemsManufacturing.DropDown
        cmbItemsManufacturing.Text = ""
        txtItm_idManufacturing.Text = ""
        txtItm_idManufacturing.Text = ""
    End Sub

    Private Sub cmbItemsMaterial_DropDown(sender As Object, e As EventArgs) Handles cmbItemsMaterial.DropDown
        cmbItemsMaterial.Text = ""
        txtItm_idMaterial.Text = ""
        txtqunt.Text = ""
        txtquntUnity.Text = ""
        cmbUnity.Text = ""
    End Sub

    Private Sub txtqunt_KeyUp(sender As Object, e As KeyEventArgs) Handles txtqunt.KeyUp
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub


    Private Sub txtquntManufacturing_KeyUp(sender As Object, e As KeyEventArgs)
        If e.KeyCode = 13 Then
            cmbStoreMaterial.Focus()
        End If
    End Sub

    Private Sub txtTinPrice_TextChanged(sender As Object, e As EventArgs) Handles txtTinPrice.TextChanged
        MyVars.CheckNumber(txtTinPrice)
    End Sub

    Private Sub txtqunt_TextChanged(sender As Object, e As EventArgs) Handles txtqunt.TextChanged

    End Sub

    Private Sub txtManufacturing_Allowance_TextChanged(sender As Object, e As EventArgs) Handles txtManufacturing_Allowance.TextChanged
        MyVars.CheckNumber(txtManufacturing_Allowance)
        SumTotal()
    End Sub

    Private Sub txtFilling_Allowance_TextChanged(sender As Object, e As EventArgs) Handles txtFilling_Allowance.TextChanged
        MyVars.CheckNumber(txtFilling_Allowance)
        SumTotal()
    End Sub

    Private Sub txtManufacturing_Allowance_KeyUp(sender As Object, e As KeyEventArgs) Handles txtManufacturing_Allowance.KeyUp
        If e.KeyCode = 13 Then
            txtFilling_Allowance.Focus()
        End If
    End Sub

    Private Sub txtquntUnity_TextChanged(sender As Object, e As EventArgs) Handles txtquntUnity.TextChanged
        MyVars.CheckNumber(txtquntUnity)
        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If
    End Sub

    Private Sub txtquntUnity_KeyUp(sender As Object, e As KeyEventArgs) Handles txtquntUnity.KeyUp
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub cmbUnity_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbUnity.SelectedIndexChanged
        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & txtItm_idMaterial.Text & "' and Unity_Name=N'" & cmbUnity.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 0 : End If
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(txtquntUnity.Text)
        End If
    End Sub
End Class
