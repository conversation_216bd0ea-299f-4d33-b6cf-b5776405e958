﻿Imports vb = Microsoft.VisualBasic
Public Class Frm_IM_Decayed_Edit


    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT CAST(bill_no as float) AS [رقم الفاتورة],Vendorname AS [أسم العميل], bill_date AS [تاريخ الفاتورة], billtime AS [وقت الفاتورة], totalpriceafterdisc AS [الاجمالى],Stat as [الحالة] FROM decayed where bill_No <> N'جرد'"
        If ChkAll.Checked = False Then
            S = S & " and Vendorname =N'" & cmbvendornameshow.Text.Trim & "'"
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and  bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم الفاتورة]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [تاريخ الفاتورة]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [اسم العميل]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
        SumDGV()
        SumDetails() : DataGridView1.Columns(0).Width = 0
        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(2).Value)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(2).Value = SM
        Next


    End Sub

    Private Sub btnprint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnprint.Click
        GetData()
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False

        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub
    Private Sub GetDetails()
        'If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        'If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim ItmID As String = 0
        If DataGridView1.RowCount <> 0 Then
            ItmID = DataGridView1.SelectedRows(0).Cells(0).Value
        End If
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id as [الباركود],itm_cat as [أسم المجموعة] ,itm_name as [اسم الصنف],price as [السعر] ,qu as [الكمية] ,totalprice as [الأجمالي],Stores as [المخزن] from data_decayed where bill_no =N'" & ItmID & "'"
        dr = cmd.ExecuteReader
        DataGridView3.DataSource = Cls.PopulateDataView(dr) : SumDetails()
    End Sub

    Private Sub DataGridView1_CellClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridView1.CellClick
        GetDetails()
    End Sub

    Private Sub SumDGV()
        Dim SM As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM = SM + DataGridView1.Rows(i).Cells(4).Value
        Next
        txttotalpricebefor.Text = SM

    End Sub
    Private Sub SumDetails()
        If DataGridView1.RowCount = 0 Then Exit Sub
        'txpricebefor.Text = DataGridView1.SelectedRows(0).Cells(6).Value
        txtdisc.Text = DataGridView1.SelectedRows(0).Cells(4).Value
        'txtpriceafter.Text = DataGridView1.SelectedRows(0).Cells(5).Value
    End Sub

    Private Sub Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button3.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim ItmID As String
        Dim NameVendor As String
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            ItmID = DataGridView1.SelectedRows(i).Cells(0).Value
            cmd.CommandText = "delete From  decayed where bill_no =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  data_decayed where bill_no =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
            NameVendor = DataGridView1.SelectedRows(i).Cells(1).Value
            IM.CustomerAccountTotal(NameVendor)
        Next
        GetData() : GetDetails()
    End Sub

    Private Sub DateTimePicker1_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DateTimePicker1.ValueChanged
        GetData()
    End Sub

    Private Sub DateTimePicker2_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DateTimePicker2.ValueChanged
        GetData()
    End Sub

    Private Sub FrmEditimport_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Me.MdiParent = MDIParent1
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", cmbvendornameshow)

        End If
        cmbvendornameshow.Items.Add("نقدا")

        GetData()
    End Sub

    Private Sub BtnPrintBill_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnPrintBill.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        EditItmId = DataGridView1.SelectedRows(0).Cells(0).Value

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete from TmpBillsalData" : cmd.ExecuteNonQuery()
        cmd.CommandText = "delete from tmpSales_Bill" : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "insert into TmpBillsalData (bill_no,itm_id,itm_cat,itm_name,price,qu,totalprice,Stores,username ) select bill_no,itm_id,itm_cat,itm_name,price,qu,totalprice,Stores,username  from IM_Bsal_Data where bill_no =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
        cmd.CommandText = "insert into tmpSales_Bill (bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,username) select bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,username from IM_Bsal where bill_no =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
        Frm_IM_BSal_Edit_Ad.Show() : Frmimport.Close()
    End Sub


    Private Sub ChkAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkAll.CheckedChanged
        If ChkAll.Checked = True Then
            cmbvendornameshow.Enabled = False
            cmbvendornameshow.SelectedIndex = -1
        Else
            cmbvendornameshow.Enabled = True
        End If
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class
