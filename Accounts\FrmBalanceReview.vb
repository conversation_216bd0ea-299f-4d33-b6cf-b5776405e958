﻿Imports CrystalDecisions.CrystalReports.Engine
Imports System.IO
Imports System.Drawing.Imaging

Public Class FrmBalanceReview
    Dim WithEvents BS As New BindingSource
    Dim rpt As New rpt_AssetsItems
    Dim Simage As Image

    Private Sub FrmItemsMin_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Bra.Fil("AccountsTree", "ACCName", cmbNameAccount)
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        FillData()
    End Sub

    Private Sub FillData()
        Try
            DataGridView1.DataSource = ""

            ' بناء الاستعلام الآمن
            Dim baseQuery As String = "SELECT ACCNumber as [رقم الحساب], ACCName as [أسم الحساب], ACCDebtor as [مدين افتتاحى], ACCCreditor as [دائن افتتاحى], SUMDEBIT as [مدين], SUMCREDIT as [دائن], EXSUMDEBIT as [مدين لتاريخة], EXSUMCREDIT as [دائن لتاريخة] FROM FINALBALANCE WHERE ACCNumber <> @excludeValue"

            Dim queryBuilder As New SafeQueryBuilder(baseQuery)
            queryBuilder.AddCustomCondition("", New Dictionary(Of String, Object) From {{"@excludeValue", ""}})

            ' إضافة شرط اسم الحساب إذا تم تحديده
            If Not String.IsNullOrEmpty(cmbNameAccount.Text.Trim()) Then
                queryBuilder.AddWhereCondition("ACCName", cmbNameAccount.Text.Trim())
            End If

            ' تنفيذ الاستعلام الآمن
            Dim result As DataTable = queryBuilder.ExecuteQuery()
            DataGridView1.DataSource = result

            ' حساب المجاميع
            CalculateTotals()

            ' تنسيق عرض الأعمدة
            SetColumnWidths()

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في تحميل بيانات الميزانية", ex)
            MessageBox.Show("حدث خطأ في تحميل البيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' حساب المجاميع
    ''' </summary>
    Private Sub CalculateTotals()
        Try
            Dim totalDebtor As Double = 0
            Dim totalCreditor As Double = 0

            For i As Integer = 0 To DataGridView1.Rows.Count - 1
                If DataGridView1.Rows(i).Cells(6).Value IsNot Nothing Then
                    totalDebtor += Val(DataGridView1.Rows(i).Cells(6).Value.ToString())
                End If

                If DataGridView1.Rows(i).Cells(7).Value IsNot Nothing Then
                    totalCreditor += Val(DataGridView1.Rows(i).Cells(7).Value.ToString())
                End If
            Next

            txtACCDebtor.Text = totalDebtor.ToString()
            txtACCCreditor.Text = totalCreditor.ToString()

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في حساب المجاميع", ex)
        End Try
    End Sub

    ''' <summary>
    ''' تنسيق عرض الأعمدة
    ''' </summary>
    Private Sub SetColumnWidths()
        Try
            If DataGridView1.Columns.Count > 0 Then
                DataGridView1.Columns(0).Width = 110  ' رقم الحساب
                DataGridView1.Columns(1).Width = 250  ' اسم الحساب
            End If
        Catch ex As Exception
            ErrorHandler.LogError("خطأ في تنسيق الأعمدة", ex)
        End Try
    End Sub

    Private Sub BtnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnPrint.Click

        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintAllItems")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.Rows.Count - 1

            S = "insert into PrintAllItems(Company_Branch_ID,itm_id,sname,TinPrice,SalPrice,tinpricetotal,salpricetotal,btinpricetotal,bsalpricetotal)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView1.Rows(i).Cells(0).Value & "',N'" & DataGridView1.Rows(i).Cells(1).Value & "',N'" & DataGridView1.Rows(i).Cells(2).Value & "',N'" & DataGridView1.Rows(i).Cells(3).Value & "',N'" & DataGridView1.Rows(i).Cells(4).Value & "',N'" & DataGridView1.Rows(i).Cells(5).Value & "',N'" & DataGridView1.Rows(i).Cells(6).Value & "',N'" & DataGridView1.Rows(i).Cells(7).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

        Next
        AddReportView()
        Dim rpt As New rptBalanceReview

        Cls.Select_More_Data_Branch_Print("PrintAllItems", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        Dim txtname, txtNameAr, txtNameEn, txtTotalACCDebtor, txtTotalACCCreditor As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "ميزان المراجعة"
        txtNameAr = rpt.Section1.ReportObjects("txtTitelNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelNameEn")
        txtNameEn.Text = NameEnCompany
        txtTotalACCDebtor = rpt.Section1.ReportObjects("txtTotalACCDebtor")
        txtTotalACCDebtor.Text = txtACCDebtor.Text
        txtTotalACCCreditor = rpt.Section1.ReportObjects("txtTotalACCCreditor")
        txtTotalACCCreditor.Text = txtACCCreditor.Text
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "ميزان المراجعة"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub ChbAll_CheckedChanged(sender As Object, e As EventArgs) Handles ChbAll.CheckedChanged
        If ChbAll.Checked = True Then
            cmbNameAccount.Enabled = False
            cmbNameAccount.SelectedIndex = -1
        Else
            cmbNameAccount.Enabled = True
        End If
    End Sub


End Class
