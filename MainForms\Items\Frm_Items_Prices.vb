﻿Public Class Frm_Items_Prices

    Private Sub btnItems_Prices_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnItems_Prices.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Dim aray_1 As New ArrayList
        Dim aray_2 As New ArrayList
        Dim aray_3 As New ArrayList
        Dim aray_4 As New ArrayList
        Dim aray_5 As New ArrayList
        Dim aray_6 As New ArrayList
        aray_1.Clear() : aray_2.Clear() : aray_3.Clear() : aray_4.Clear() : aray_5.Clear() : aray_6.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select itm_id,SalPrice,TinPrice,TinPriceAverage,WholePrice,WholeWholePrice from Items where id <> ''"
        If chkSelectItems.Checked = True Then
            Dim Loopx As Integer = 0
            For Each checkedItem In CheckedListBoxItems.CheckedItems
                If Loopx = 0 Then
                    S = S & " and sname =N'" & checkedItem & "'"
                Else
                    S = S & " OR sname =N'" & checkedItem & "'"
                End If
                Loopx += 1
            Next
            If cmbStores.Text <> "" Then
                S = S & " and Stores =N'" & cmbStores.Text.Trim & "'"
            End If
            If cmbVendorname.Text <> "" Then
                S = S & " and Vendorname =N'" & cmbVendorname.Text.Trim & "'"
            End If
        End If
        If chkSelectItems.Checked = False Then
            If chkAll.Checked = False Then
                If chkSelectGroups.Checked = False Then
                    If cmbcats.Text <> "" Then
                        S = S & " and group_name =N'" & cmbcats.Text.Trim & "'"
                    End If
                    If cmbGroup_Branch.Text <> "" Then
                        S = S & " and group_branch =N'" & cmbGroup_Branch.Text.Trim & "'"
                    End If
                Else
                    Dim Loopx As Integer = 0
                    For Each checkedItem In CheckedListBoxGroups.CheckedItems
                        If Loopx = 0 Then
                            S = S & " and group_name =N'" & checkedItem & "'"
                        Else
                            S = S & " OR group_name =N'" & checkedItem & "'"
                        End If
                        Loopx += 1
                    Next
                End If
                If cmbStores.Text <> "" Then
                    S = S & " and Stores =N'" & cmbStores.Text.Trim & "'"
                End If
                If cmbVendorname.Text <> "" Then
                    S = S & " and Vendorname =N'" & cmbVendorname.Text.Trim & "'"
                End If
            End If
        End If
        cmd.CommandText = S
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr("itm_id"))
            If chkSalPrice.Checked = True Then
                aray_2.Add(dr("SalPrice"))
            End If
            If chkTinPrice.Checked = True Then
                aray_3.Add(dr("TinPrice"))
                aray_4.Add(dr("TinPriceAverage"))
            End If
            If chkWholePrice.Checked = True Then
                aray_5.Add(dr("WholePrice"))
            End If
            If chkWholeWholePrice.Checked = True Then
                aray_6.Add(dr("WholeWholePrice"))
            End If
        Loop



        '======================================================================
        Dim IDTM As String = ""
        Dim SalPrice As String = ""
        Dim TinPrice As String = ""
        Dim TinPriceAverage As String = ""
        Dim WholePrice As String = ""
        Dim WholeWholePrice As String = ""
        Dim RateSaleVal, RateTinPriceVal, RateTinPriceAverageVal, RateWholePriceVal, RateWholeWholePriceVal As Double
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To aray_1.Count - 1
            IDTM = aray_1(i)
            If chkSalPrice.Checked = True Then
                SalPrice = aray_2(i)
                RateSaleVal = Val((Val(SalPrice) * (100 + Val(txtItemsPrices.Text))) / 100)
                RateSaleVal = Math.Round(RateSaleVal, 1)

                cmd.CommandText = "update items set SalPrice = " & Val(RateSaleVal) & " where itm_id =N'" & IDTM & "'" : cmd.ExecuteNonQuery()
            End If
            If chkTinPrice.Checked = True Then
                TinPrice = aray_3(i)
                TinPriceAverage = aray_4(i)
                RateTinPriceVal = Val((Val(TinPrice) * (100 + Val(txtItemsPrices.Text))) / 100)
                RateTinPriceVal = Math.Round(RateTinPriceVal, 1)

                RateTinPriceAverageVal = Val((Val(TinPriceAverage) * (100 + Val(txtItemsPrices.Text))) / 100)
                RateTinPriceAverageVal = Math.Round(RateTinPriceAverageVal, 1)

                cmd.CommandText = "update items set TinPrice = " & Val(RateTinPriceVal) & ",TinPriceAverage = " & Val(RateTinPriceAverageVal) & " where itm_id =N'" & IDTM & "'" : cmd.ExecuteNonQuery()
            End If
            If chkWholePrice.Checked = True Then
                WholePrice = aray_5(i)

                RateWholePriceVal = Val((Val(WholePrice) * (100 + Val(txtItemsPrices.Text))) / 100)
                RateWholePriceVal = Math.Round(RateWholePriceVal, 1)

                cmd.CommandText = "update items set WholePrice = " & Val(RateWholePriceVal) & " where itm_id =N'" & IDTM & "'" : cmd.ExecuteNonQuery()
            End If
            If chkWholeWholePrice.Checked = True Then
                WholeWholePrice = aray_6(i)

                RateWholeWholePriceVal = Val((Val(WholeWholePrice) * (100 + Val(txtItemsPrices.Text))) / 100)
                RateWholeWholePriceVal = Math.Round(RateWholeWholePriceVal, 1)

                cmd.CommandText = "update items set WholeWholePrice = " & Val(RateWholeWholePriceVal) & " where itm_id =N'" & IDTM & "'" : cmd.ExecuteNonQuery()
            End If
        Next

        txtItemsPrices.Text = ""
        txtLowerPrice.Text = ""
        cmbcats.Text = ""
        chkSelectGroups_CheckedChanged(sender, e)
        chkSelectItems_CheckedChanged(sender, e)
        MsgBox("تمت العملية بنجاح", MsgBoxStyle.Information)


    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Dim aray_1 As New ArrayList
        Dim aray_2 As New ArrayList
        Dim aray_3 As New ArrayList
        Dim aray_4 As New ArrayList
        Dim aray_5 As New ArrayList
        Dim aray_6 As New ArrayList
        aray_1.Clear() : aray_2.Clear() : aray_3.Clear() : aray_4.Clear() : aray_5.Clear() : aray_6.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select itm_id,SalPrice,TinPrice,TinPriceAverage,WholePrice,WholeWholePrice from Items where id <> ''"
        If chkSelectItems.Checked = True Then
            Dim Loopx As Integer = 0
            For Each checkedItem In CheckedListBoxItems.CheckedItems
                If Loopx = 0 Then
                    S = S & " and sname =N'" & checkedItem & "'"
                Else
                    S = S & " OR sname =N'" & checkedItem & "'"
                End If
                Loopx += 1
            Next
            If cmbStores.Text <> "" Then
                S = S & " and Stores =N'" & cmbStores.Text.Trim & "'"
            End If
            If cmbVendorname.Text <> "" Then
                S = S & " and Vendorname =N'" & cmbVendorname.Text.Trim & "'"
            End If
        End If
        If chkSelectItems.Checked = False Then
            If chkAll.Checked = False Then
                If chkSelectGroups.Checked = False Then
                    If cmbcats.Text <> "" Then
                        S = S & " and group_name =N'" & cmbcats.Text.Trim & "'"
                    End If
                Else
                    Dim Loopx As Integer = 0
                    For Each checkedItem In CheckedListBoxGroups.CheckedItems
                        If Loopx = 0 Then
                            S = S & " and group_name =N'" & checkedItem & "'"
                        Else
                            S = S & " OR group_name =N'" & checkedItem & "'"
                        End If
                        Loopx += 1
                    Next
                End If
                If cmbStores.Text <> "" Then
                    S = S & " and Stores =N'" & cmbStores.Text.Trim & "'"
                End If
                If cmbVendorname.Text <> "" Then
                    S = S & " and Vendorname =N'" & cmbVendorname.Text.Trim & "'"
                End If
            End If
        End If
        cmd.CommandText = S
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr("itm_id"))
            If chkSalPrice.Checked = True Then
                aray_2.Add(dr("SalPrice"))
            End If
            If chkTinPrice.Checked = True Then
                aray_3.Add(dr("TinPrice"))
                aray_4.Add(dr("TinPriceAverage"))
            End If
            If chkWholePrice.Checked = True Then
                aray_5.Add(dr("WholePrice"))
            End If
            If chkWholeWholePrice.Checked = True Then
                aray_6.Add(dr("WholeWholePrice"))
            End If
        Loop

        Dim IDTM As String = ""
        Dim SalPrice As String = ""
        Dim TinPrice As String = ""
        Dim TinPriceAverage As String = ""
        Dim WholePrice As String = ""
        Dim WholeWholePrice As String = ""
        Dim RateSaleVal, RateTinPriceVal, RateTinPriceAverageVal, RateWholePriceVal, RateWholeWholePriceVal As Double
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To aray_1.Count - 1
            IDTM = aray_1(i)
            If chkSalPrice.Checked = True Then
                SalPrice = aray_2(i)
                RateSaleVal = Val((Val(SalPrice) * (100 - Val(txtLowerPrice.Text))) / 100)
                RateSaleVal = Math.Round(RateSaleVal, 1)

                cmd.CommandText = "update items set SalPrice = " & Val(RateSaleVal) & " where itm_id =N'" & IDTM & "'" : cmd.ExecuteNonQuery()
            End If
            If chkTinPrice.Checked = True Then
                TinPrice = aray_3(i)
                TinPriceAverage = aray_4(i)
                RateTinPriceVal = Val((Val(TinPrice) * (100 - Val(txtLowerPrice.Text))) / 100)
                RateTinPriceVal = Math.Round(RateTinPriceVal, 1)

                RateTinPriceAverageVal = Val((Val(TinPriceAverage) * (100 - Val(txtLowerPrice.Text))) / 100)
                RateTinPriceAverageVal = Math.Round(RateTinPriceAverageVal, 1)

                cmd.CommandText = "update items set TinPrice = " & Val(RateTinPriceVal) & ",TinPriceAverage = " & Val(RateTinPriceAverageVal) & " where itm_id =N'" & IDTM & "'" : cmd.ExecuteNonQuery()
            End If
            If chkWholePrice.Checked = True Then
                WholePrice = aray_5(i)

                RateWholePriceVal = Val((Val(WholePrice) * (100 - Val(txtLowerPrice.Text))) / 100)
                RateWholePriceVal = Math.Round(RateWholePriceVal, 1)

                cmd.CommandText = "update items set WholePrice = " & Val(RateWholePriceVal) & " where itm_id =N'" & IDTM & "'" : cmd.ExecuteNonQuery()
            End If
            If chkWholeWholePrice.Checked = True Then
                WholeWholePrice = aray_6(i)

                RateWholeWholePriceVal = Val((Val(WholeWholePrice) * (100 - Val(txtLowerPrice.Text))) / 100)
                RateWholeWholePriceVal = Math.Round(RateWholeWholePriceVal, 1)

                cmd.CommandText = "update items set WholeWholePrice = " & Val(RateWholeWholePriceVal) & " where itm_id =N'" & IDTM & "'" : cmd.ExecuteNonQuery()
            End If
        Next

        txtItemsPrices.Text = ""
        txtLowerPrice.Text = ""
        cmbcats.Text = ""
        chkSelectGroups_CheckedChanged(sender, e)
        chkSelectItems_CheckedChanged(sender, e)

        MsgBox("تمت العملية بنجاح", MsgBoxStyle.Information)

    End Sub

    Private Sub Frm_Items_Prices_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cls.fill_combo_Branch("stores", "store", cmbStores)
        cmbStores.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        Bra.Fil("groups", "g_name", cmbcats)
        Cls.fill_combo("vendors", "Vendorname", cmbVendorname)
    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbStores.Enabled = False
        Else
            cmbStores.Enabled = True
        End If
    End Sub

    Private Sub txtItemsPrices_TextChanged(sender As Object, e As EventArgs) Handles txtItemsPrices.TextChanged
        If txtItemsPrices.Text = "" And txtLowerPrice.Text = "" Then
            txtLowerPrice.Enabled = True
            txtItemsPrices.Enabled = True
            Button1.Enabled = True
            btnItems_Prices.Enabled = True
        End If
        If txtItemsPrices.Text = "" Then
            txtLowerPrice.Enabled = True
            Button1.Enabled = True
        Else
            txtLowerPrice.Enabled = False
            Button1.Enabled = False
        End If
    End Sub

    Private Sub txtLowerPrice_TextChanged(sender As Object, e As EventArgs) Handles txtLowerPrice.TextChanged
        If txtItemsPrices.Text = "" And txtLowerPrice.Text = "" Then
            txtLowerPrice.Enabled = True
            txtItemsPrices.Enabled = True
            Button1.Enabled = True
            btnItems_Prices.Enabled = True
        End If
        If txtLowerPrice.Text = "" Then
            txtItemsPrices.Enabled = True
            btnItems_Prices.Enabled = True
        Else
            txtItemsPrices.Enabled = False
            btnItems_Prices.Enabled = False
        End If
    End Sub

    Private Sub FillCheckedListBoxAllGroups()
        CheckedListBoxGroups.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT G_name FROM Groups ORDER BY G_name ASC"
        dr = cmd.ExecuteReader()
        Do While dr.Read = True
            CheckedListBoxGroups.Items.Add(dr("G_name"))
        Loop
    End Sub

    Private Sub FillCheckedListBoxAllItems()
        CheckedListBoxItems.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT sname FROM Items ORDER BY sname ASC"
        dr = cmd.ExecuteReader()
        Do While dr.Read = True
            CheckedListBoxItems.Items.Add(dr("sname"))
        Loop
    End Sub

    Private Sub CheckedListBoxGroups_ItemCheck(sender As Object, e As ItemCheckEventArgs) Handles CheckedListBoxGroups.ItemCheck
        Dim selectedItem As String = CheckedListBoxGroups.Items(e.Index).ToString()
        If e.NewValue = CheckState.Checked Then
            chkSelectGroups.Checked = True
        End If
    End Sub

    Private Sub cmbcats_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbcats.SelectedIndexChanged
        If chkSelectItems.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "SELECT sname FROM Items where group_name=N'" & cmbcats.Text & "' ORDER BY sname ASC"
            dr = cmd.ExecuteReader()
            Do While dr.Read = True
                CheckedListBoxItems.Items.Add(dr("sname"))
            Loop
        End If

        Dim ShowGroupBranch As String = mykey.GetValue("GroupBranch", "NO")
        If ShowGroupBranch = "YES" Then
            cmbGroup_Branch.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()

            cmd.CommandText = "SELECT dbo.Groups.G_name, dbo.Group_Branch.branch_Name FROM dbo.Groups INNER JOIN  dbo.Group_Branch ON dbo.Groups.id = dbo.Group_Branch.Group_Name_ID WHERE     (dbo.Groups.G_name =N'" & cmbcats.Text & "')  order by 1"
            dr = cmd.ExecuteReader
            Do While dr.Read
                cmbGroup_Branch.Items.Add(Trim(dr(1)))
            Loop
            cmbGroup_Branch.Text = ""
        End If
    End Sub

    Private Sub chkSelectItems_CheckedChanged(sender As Object, e As EventArgs) Handles chkSelectItems.CheckedChanged
        If chkSelectItems.Checked = True Then
            If cmbcats.Text = "" Then
                FillCheckedListBoxAllItems()
            Else
                CheckedListBoxItems.Items.Clear()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "SELECT sname FROM Items where group_name=N'" & cmbcats.Text & "' ORDER BY sname ASC"
                dr = cmd.ExecuteReader()
                Do While dr.Read = True
                    CheckedListBoxItems.Items.Add(dr("sname"))
                Loop
            End If
        Else
            CheckedListBoxItems.Items.Clear()
        End If
    End Sub

    Private Sub chkSelectGroups_CheckedChanged(sender As Object, e As EventArgs) Handles chkSelectGroups.CheckedChanged
        If chkSelectGroups.Checked = True Then
            FillCheckedListBoxAllGroups()
        Else
            CheckedListBoxGroups.Items.Clear()
        End If
    End Sub

    Private Sub cmbVendorname_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbVendorname.SelectedIndexChanged
        If chkSelectGroups.Checked = True Then
            CheckedListBoxGroups.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select distinct dbo.Items.group_name, dbo.vendors.Vendorname From dbo.vendors INNER Join dbo.Items ON dbo.vendors.Vendorname = dbo.Items.Vendorname Where (dbo.vendors.Vendorname = N'" & cmbVendorname.Text & "') ORDER BY dbo.Items.group_name"
            dr = cmd.ExecuteReader()
            Do While dr.Read = True
                CheckedListBoxGroups.Items.Add(dr("group_name"))
            Loop
        End If

        If chkSelectItems.Checked = True Then
            CheckedListBoxItems.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "SELECT sname FROM Items where Vendorname=N'" & cmbVendorname.Text & "' ORDER BY sname ASC"
            dr = cmd.ExecuteReader()
            Do While dr.Read = True
                CheckedListBoxItems.Items.Add(dr("sname"))
            Loop
        End If
    End Sub
End Class