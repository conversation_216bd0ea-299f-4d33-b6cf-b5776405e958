﻿Imports System.Data.SqlClient

Public Class Form2
    Private Sub TextBox1_TextChanged(sender As Object, e As EventArgs) Handles TextBox1.TextChanged
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        connectionStringOpen()
        'Try
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.<PERSON>()
        If TextBox1.Text <> " بحث ...." Or TextBox1.Text <> "" Then
            DTGV.Visible = True
            Dim X As String = "%" & TextBox1.Text.Trim & "%"
            S = Cls.Get_Select_Grid_S_OrderBy_Name("sname as [الاسم],SalPrice as [سعر التجزئة]", "items", "sname like N'" & X & "'", "sname")
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)
    End Sub

    Private Sub btnSearchItems_Click(sender As Object, e As EventArgs) Handles btnSearchItems.Click
        DTGV.Visible = True
    End Sub

    Private Sub TextBox1_KeyDown(sender As Object, e As KeyEventArgs) Handles TextBox1.KeyDown
        If ((e.KeyCode = 8)) Then
            DTGV.Visible = False
        End If
        If ((e.KeyCode = 40)) Then
            DTGV.Focus()
        End If
    End Sub

    Private Sub DTGV_DoubleClick(sender As Object, e As EventArgs) Handles DTGV.DoubleClick
        DTGV.Visible = False
    End Sub

    Private Sub DTGV_KeyUp(sender As Object, e As KeyEventArgs) Handles DTGV.KeyUp
        If e.KeyCode = 13 Then
            DTGV.Visible = False
        End If
    End Sub
End Class