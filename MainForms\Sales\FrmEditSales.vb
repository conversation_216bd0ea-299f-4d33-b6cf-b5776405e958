﻿Imports vb = Microsoft.VisualBasic
Public Class FrmEditSales
    Dim archiveManager As New Cls_ArchiveManager

    Private Sub GetData()
        Try
            If rdoDismissalNotice.Checked = True Then
                S = Cls.Get_Select_Grid_S_Branch("BILL_NO AS [رقم الفاتورة],Vendorname AS [اسم العميل], bill_date AS [تاريخ الفاتورة], billtime AS [وقت الفاتورة], totalpricebeforedisc AS [القيمة قبل الخصم], disc AS [الخصم], totalpriceafterdisc AS [القيمة بعد الخصم],EmpName as [المندوب],Treasury_Code  as [الخزينة],Notes as [ملاحظات]", "Sales_Bill", "BILL_NO <> N'جرد'")
            End If
            If rdoReceivingPermission.Checked = True Then
                S = Cls.Get_Select_Grid_S_Branch("BILL_NO AS [رقم الفاتورة],Vendorname AS [اسم العميل], bill_date AS [تاريخ الفاتورة], billtime AS [وقت الفاتورة], totalpricebeforedisc AS [القيمة قبل الخصم], disc AS [الخصم], totalpriceafterdisc AS [القيمة بعد الخصم],EmpName as [المندوب],Treasury_Code  as [الخزينة],Notes as [ملاحظات]", "Receive_Sales_Bill", "BILL_NO <> N'جرد'")
            End If
            If ChkAll.Checked = False Then
                If cmbvendornameshow.Text <> "" Then
                    S = S & " and  Vendorname  =N'" & cmbvendornameshow.Text.Trim & "'"
                End If
                If txtbillnoSearch.Text <> "" Then
                    S = S & " and  BILL_NO  =N'" & txtbillnoSearch.Text.Trim & "'"
                End If
                If txtNotesView.Text <> "" Then
                    S = S & " and Notes =N'" & txtNotesView.Text.Trim & "'"
                End If
            End If
            If chkPendingBill.Checked = True Then
                S = S & " and PendingBill =N'1'"
            Else
                S = S & " and PendingBill =N'0'"
            End If
            If ChkWithoutDate.Checked = False Then
                S = S & " and  bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
            End If
            If FilterSelect = "Number" Then
                S = S & " order by [رقم الفاتورة]"
            End If
            If FilterSelect = "Date" Then
                S = S & " order by [تاريخ الفاتورة]"
            End If
            If FilterSelect = "Name" Then
                S = S & " order by [اسم العميل]"
            End If

            cmd.CommandText = S : dr = cmd.ExecuteReader
            DataGridView1.DataSource = Cls.PopulateDataView(dr)

            DataGridView1.Columns(7).Visible = False
            DataGridView1.Columns(8).Visible = False
            Dim SM As String
            For i As Integer = 0 To DataGridView1.RowCount - 1
                SM = Val(DataGridView1.Rows(i).Cells(2).Value)
                SM = Cls.R_date(SM)
                DataGridView1.Rows(i).Cells(2).Value = SM
            Next
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        SumDGV() : SumDetails()

    End Sub

    Private Sub btnprint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnprint.Click
        GetData()
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False

        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub GetDetails()
        Try
            'If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            'If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim ItmID As String = 0
            If DataGridView1.RowCount <> 0 Then
                ItmID = DataGridView1.SelectedRows(0).Cells(0).Value
            End If
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If rdoDismissalNotice.Checked = True Then
                cmd.CommandText = "select itm_id as [الباركود],itm_cat as [المجموعة] ,itm_name as [اسم الصنف],price as [السعر] ,qu as [1الكمية],qu_unity as [الكمية],itm_Unity as [الوحدة] ,totalprice as [الأجمالي],Stores as [المخزن] from BillsalData where bill_no =N'" & ItmID & "'"
            End If
            If rdoReceivingPermission.Checked = True Then
                cmd.CommandText = "select itm_id as [الباركود],itm_cat as [المجموعة] ,itm_name as [اسم الصنف],price as [السعر] ,qu as [1الكمية],qu_unity as [الكمية],itm_Unity as [الوحدة] ,totalprice as [الأجمالي],Stores as [المخزن] from Receive_BillsalData where bill_no =N'" & ItmID & "'"
            End If
            dr = cmd.ExecuteReader
            DataGridView3.DataSource = Cls.PopulateDataView(dr) : SumDetails()
            DataGridView3.Columns(1).Visible = False
            DataGridView3.Columns(4).Visible = False

            txtNumberItems.Text = DataGridView3.RowCount
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub DataGridView1_CellClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridView1.CellClick
        GetDetails()
    End Sub

    Private Sub SumDGV()
        Try
            Dim SM As Double
            For i As Integer = 0 To DataGridView1.Rows.Count - 1
                SM = SM + DataGridView1.Rows(i).Cells(4).Value
            Next
            txttotalpricebefor.Text = SM
            Dim SM2 As Double
            For i As Integer = 0 To DataGridView1.Rows.Count - 1
                SM2 = SM2 + DataGridView1.Rows(i).Cells(5).Value
            Next
            txttotaldisc.Text = SM2

            Dim SM3 As Double
            For i As Integer = 0 To DataGridView1.Rows.Count - 1
                SM3 = SM3 + DataGridView1.Rows(i).Cells(6).Value
            Next
            txttotalpriceafter.Text = SM3
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

    End Sub
    Private Sub SumDetails()
        If DataGridView1.RowCount = 0 Then Exit Sub
        txpricebefor.Text = DataGridView1.SelectedRows(0).Cells(3).Value
        txtdisc.Text = DataGridView1.SelectedRows(0).Cells(4).Value
        txtpriceafter.Text = DataGridView1.SelectedRows(0).Cells(5).Value
    End Sub

    Dim aray_itm_id As New ArrayList
    Dim aray_Stores As New ArrayList

    Private Sub Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button3.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim ItmID, XDate As String
        Dim NameVendor As String
        Dim Emp As String
        Dim Treasury_Code_ID As String

        Try
            Dim TextManufacturingProduct As Boolean = False
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from ManufacturingProduct"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                TextManufacturingProduct = True
            End If

            Dim Vendorname As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
                ItmID = DataGridView1.SelectedRows(i).Cells(0).Value
                Vendorname = DataGridView1.SelectedRows(i).Cells(1).Value
                Emp = DataGridView1.SelectedRows(i).Cells(7).Value.ToString
                Treasury_Code_ID = DataGridView1.SelectedRows(i).Cells(8).Value.ToString

                '=========================================== Archive Manager ============================================================
                archiveManager.ArchiveAndDeleteSalesBill("Delete", ItmID, Vendorname, UserName, "تم حذف فاتورة المبيعات لعدم النشاط")
                archiveManager.ArchiveAndDeleteBillsalData("Delete", ItmID, UserName, "تم حذف فاتورة المبيعات لعدم النشاط")
                '=========================================== Archive Manager ============================================================


                aray_itm_id.Clear()
                aray_Stores.Clear()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select itm_id,Stores from BillsalData where bill_no =N'" & ItmID & "'"
                dr = cmd.ExecuteReader
                Do While dr.Read = True
                    aray_itm_id.Add(dr(0))
                    aray_Stores.Add(dr(1))
                Loop

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                If rdoDismissalNotice.Checked = True Then
                    cmd.CommandText = "delete From  Sales_Bill where bill_no =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
                    'If NotUnityItemsProgram = "YES" Then
                    cmd.CommandText = "delete From  BillsalData where bill_no =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
                    'End If
                End If
                If rdoReceivingPermission.Checked = True Then
                    cmd.CommandText = "delete From  Receive_Sales_Bill where bill_no =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
                    cmd.CommandText = "delete From  Receive_BillsalData where bill_no =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
                End If

                cmd.CommandText = "delete From  Vst_disc where TIN_NO =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  Vst where BillNo =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  BillsalData_SerialNumber where bill_no =N'" & ItmID & "'" : cmd.ExecuteNonQuery()

                cmd.CommandText = "delete From  MOVES where bill_no =N'" & ItmID & "' and MOVStatement =N'مبيعات'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'مقبوضات عملاء'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'خصومات عملاء'" : cmd.ExecuteNonQuery()


                '===============================================================================
                If TextManufacturingProduct = True Then
                    cmd.CommandText = "delete From  Manufacturing_BillsalData where bill_no =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
                End If
                '===============================================================================

                NameVendor = DataGridView1.SelectedRows(i).Cells(1).Value
                IM.CustomerAccountTotal(NameVendor)

                For T As Integer = 0 To aray_itm_id.Count - 1
                    If NotUnityItemsProgram = "YES" Then
                        Dim bill_no_Expired As String = Cls.Get_Code_Value_Stores_More("BillsalData", "bill_no_Expired", "bill_no =N'" & ItmID & "' and itm_id =N'" & aray_itm_id(T) & "' and Stores =N'" & aray_Stores(T) & "'")
                        Dim bill_EndDate As String = Cls.Get_Code_Value_Stores_More("BillsalData", "bill_EndDate", "bill_no =N'" & ItmID & "' and itm_id =N'" & aray_itm_id(T) & "' and Stores =N'" & aray_Stores(T) & "'")
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "delete From  BillsalData where bill_no =N'" & ItmID & "'" : cmd.ExecuteNonQuery()

                        IM.StoreExpired(aray_itm_id(T).ToString(), aray_Stores(T).ToString(), bill_EndDate.ToString(), bill_no_Expired)
                    End If
                    IM.Store(aray_itm_id(T), aray_Stores(T))

                    If ConnectOnlineStore = "YES" Then
                        EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", aray_itm_id(T))
                        StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", aray_itm_id(T))
                        Cos.UpdateProductStock(StockOnline, aray_itm_id(T), EditItmId)
                    End If
                Next

                '=================================================================================================================
                ' التصنيع
                If TextManufacturingProduct = True Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "delete From  purchase_bill where Sales_Bill_NO =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
                    cmd.CommandText = "delete From  BilltINData where Sales_Bill_NO =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
                    cmd.CommandText = "delete From  vnd where Sales_Bill_NO =N'" & ItmID & "'" : cmd.ExecuteNonQuery()

                    NameVendor = DataGridView1.SelectedRows(i).Cells(1).Value
                    IM.VendorAccountTotal(NameVendor)

                    aray_itm_id.Clear()
                    aray_Stores.Clear()
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "Select itm_id,Stores from Items"
                    dr = cmd.ExecuteReader
                    Do While dr.Read = True
                        aray_itm_id.Add(dr(0))
                        aray_Stores.Add(dr(1))
                    Loop

                    For N As Integer = 0 To aray_itm_id.Count - 1
                        IM.Store(aray_itm_id(N), aray_Stores(N))

                        If ConnectOnlineStore = "YES" Then
                            EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", aray_itm_id(N))
                            StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", aray_itm_id(N))
                            Cos.UpdateProductStock(StockOnline, aray_itm_id(N), EditItmId)
                        End If
                    Next
                End If

                '===========================================================
                If MDIParent1.ToolStripMenuItem69.Visible = True Then
                    ' حسابات المندوبين
                    IM.EmployeesAccountTotal(Emp)
                End If

                XDate = DataGridView1.SelectedRows(i).Cells(2).Value
                Get_Movement_In_Out_Money(XDate, Treasury_Code_ID)
            Next
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        GetData() : GetDetails()

    End Sub

    Private Sub DateTimePicker1_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DateTimePicker1.ValueChanged
        GetData()
    End Sub

    Private Sub DateTimePicker2_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DateTimePicker2.ValueChanged
        GetData()
    End Sub

    Private Sub FrmEditimport_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", cmbvendornameshow)

        End If
        cmbvendornameshow.Items.Add("نقدا")
        GetData()

        PermtionName = Cls.Get_Code_Value("Permtions", "PermtionName", "UserID", UserID)
        If PermtionName = "مدير" Then
            Button3.Visible = True
            'BtnPrintBill.Visible = True
        Else
            Button3.Visible = False
            'BtnPrintBill.Visible = False
        End If
    End Sub

    Private Sub BtnPrintBill_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnPrintBill.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Try
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            EditItmId = DataGridView1.SelectedRows(0).Cells(0).Value

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete from TmpBillsalData" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete from tmpSales_Bill" : cmd.ExecuteNonQuery()

            If rdoDismissalNotice.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "insert into TmpBillsalData (bill_no,itm_id,itm_cat,itm_name,price,qu,qu_unity,itm_Unity,totalprice,Stores,username , bill_date,bill_EndDate,bill_no_Expired,RateVAT,BeforeVAT,ValueVAT,Discounts,DiscountsValue,StateDisc,DiscountsTin,Discount_Price_After) select bill_no,itm_id,itm_cat,itm_name,price,qu,qu_unity,itm_Unity,totalprice,Stores,username , bill_date,bill_EndDate,bill_no_Expired,RateVAT,BeforeVAT,ValueVAT,Discounts,DiscountsValue,StateDisc,DiscountsTin,Discount_Price_After from BillsalData where bill_no =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "insert into tmpSales_Bill (bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,username) select bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,username from Sales_Bill where bill_no =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
            End If
            If rdoReceivingPermission.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "insert into TmpBillsalData (bill_no,itm_id,itm_cat,itm_name,price,qu,qu_unity,itm_Unity,totalprice,Stores,username , bill_date,bill_EndDate,bill_no_Expired,RateVAT,BeforeVAT,ValueVAT,Discounts,DiscountsValue,StateDisc,DiscountsTin,Discount_Price_After) select bill_no,itm_id,itm_cat,itm_name,price,qu,qu_unity,itm_Unity,totalprice,Stores,username , bill_date,bill_EndDate,bill_no_Expired,RateVAT,BeforeVAT,ValueVAT,Discounts,DiscountsValue,StateDisc,DiscountsTin,Discount_Price_After from Receive_BillsalData where bill_no =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "insert into tmpSales_Bill (bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,username) select bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,username from Receive_Sales_Bill where bill_no =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        If rdoReceivingPermission.Checked = True Then
            Dismissal_Notice = True
        Else
            Dismissal_Notice = False
        End If

        FrmEditsaleAd.Show() : Frmimport.Close()

    End Sub


    Private Sub ChkAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkAll.CheckedChanged
        If ChkAll.Checked = True Then
            cmbvendornameshow.Enabled = False
            txtbillnoSearch.Enabled = False
            txtNotesView.Enabled = False
            cmbvendornameshow.SelectedIndex = -1
        Else
            cmbvendornameshow.Enabled = True
            txtbillnoSearch.Enabled = True
            txtNotesView.Enabled = True
        End If
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class