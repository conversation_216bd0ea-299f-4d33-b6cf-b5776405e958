﻿Imports System.Windows.Forms
Imports System.Data
Imports System.Data.SqlClient
Public Class IMHM_Point_Sales
    Friend Function CtmSalAll_F() As Double ' قيمة مبيعات لعميل
        ' مبيعات
        Try
            Dim vintinval As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalpriceafterdisc) from Sales_bill" : dr = cmd.ExecuteReader : dr.<PERSON>()
            If dr(0) Is DBNull.Value Then vintinval = 0 Else vintinval = dr(0)
            Return vintinval
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function
    Friend Function CtmbSalAll_F() As Double ' قيمة مرتجع مبيعات لعميل
        ' مرتجع مبيعات
        Try
            Dim vinbtinval As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalpriceafterdisc) from IM_Bsal" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vinbtinval = 0 Else vinbtinval = dr(0)
            Return vinbtinval
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function CtmPayAll_F() As Double 'قيمة مقبوضات من عميل
        Try
            Dim VnPay As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(VND_amx) from vst" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VnPay = 0 Else VnPay = dr(0)
            Return VnPay
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function VnTinAll_F() As Double 'قيمة المشتريات من  مورد
        ' مشتريات
        Try
            Dim vintinval As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalpriceafterdisc) from purchase_bill" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vintinval = 0 Else vintinval = dr(0)

            Return vintinval
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function
    Friend Function VnBTinAll_F() As Double 'قيمة مرتجعات المشتريات من مورد
        ' مرتجع مشتريات
        Try
            Dim vinbtinval As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalpriceafterdisc) from IM_Btin" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vinbtinval = 0 Else vinbtinval = dr(0)

            Return vinbtinval
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function
    Friend Function VnDiscAll_F() As Double ' قيمة خصومات لمورد
        'خصومات 
        Try
            Dim vndiscount As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(amnt) from vndr_disc" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vndiscount = 0 Else vndiscount = dr(0)

            Return vndiscount
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function
    Friend Function VnPayAll_F() As Double ' قيمة المدفوعات لمورد
        'مدفوعات 
        Try
            Dim VnPay As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(VND_amx) from vnd" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VnPay = 0 Else VnPay = dr(0)

            Return VnPay
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function ValTinAll_F() As Double ' قيمة مشتريات صنف
        ' قيمة مشتريات 
        Try
            Dim VTin As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from BilltINData " : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VTin = 0 Else VTin = dr(0)
            Return VTin
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function ValSalAll_F() As Double ' قيمة مبيعات صنف
        'مبيعات 
        Try
            Dim VSal As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from BillsalData " : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VSal = 0 Else VSal = dr(0)

            Return VSal
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function ValBtinAll_F() As Double 'قيمة مرتجع مشتريات صنف
        ' قيمة مرتجع مشتريات
        Try
            Dim VBtin As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from IM_Btin_Data " : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VBtin = 0 Else VBtin = dr(0)

            Return VBtin
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function VBsalAll_F() As Double 'قيمة مرتجع مبيعات
        ' قيمة مرتجع مبيعات
        Try
            Dim VBsal As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from IM_Bsal_Data " : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VBsal = 0 Else VBsal = dr(0)

            Return VBsal
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function ValdecayedAll_F() As Double 'قيمة توالف صنف
        'قيمة توالف
        Try
            Dim Vdecayed As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from decayed " : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Vdecayed = 0 Else Vdecayed = dr(0)

            Return Vdecayed
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function
    Friend Function TinAll_F() As Double ' مشتريات صنف
        'مشتريات 
        Try
            Dim Tin As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from BilltINData " : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Tin = 0 Else Tin = dr(0)

            Return Tin
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function SalAll_F() As Double 'مبيعات صنف
        ' مبيعات
        Try
            Dim sal As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from BillsalData " : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then sal = 0 Else sal = dr(0)

            Return sal
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function BtinAll_F() As Double 'مرتجع مشتريات صنف
        'مرتجع مشتريات
        Try
            Dim Btin As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from IM_Btin_Data " : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Btin = 0 Else Btin = dr(0)

            Return Btin
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function BsalAll_F() As Double 'مرتجع مبيعات صنف
        Dim Bsal As Double
        'مرتع مبيعات
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from IM_Bsal_Data " : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Bsal = 0 Else Bsal = dr(0)
            Return Bsal
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function decayedAll_F() As Double 'توالف صنف
        ' توالف
        Try
            Dim decayed As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from decayed " : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then decayed = 0 Else decayed = dr(0)

            Return decayed
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    ' #######################################################

    Friend Function CtmSalF(ByVal Cmbvendorname As String) As Double ' قيمة مبيعات لعميل
        If Cmbvendorname = "" Then Exit Function
        ' مبيعات
        Try
            Dim vintinval As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalpriceafterdisc) from Sales_bill where Vendorname =N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vintinval = 0 Else vintinval = dr(0)

            Return vintinval
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function
    Friend Function CtmbSalF(ByVal Cmbvendorname As String) As Double ' قيمة مرتجع مبيعات لعميل
        If Cmbvendorname = "" Then Exit Function
        ' مرتجع مبيعات
        Try
            Dim vinbtinval As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalpriceafterdisc) from IM_Bsal where Vendorname =N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vinbtinval = 0 Else vinbtinval = dr(0)

            Return vinbtinval
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function CtmPayF(ByVal Cmbvendorname As String) As Double 'قيمة مقبوضات من عميل
        If Cmbvendorname = "" Then Exit Function
        Dim VnPay As Double
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(VND_amx) from vst where Vendorname =N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VnPay = 0 Else VnPay = dr(0)

            Return VnPay
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function VnTinF(ByVal Cmbvendorname As String) As Double 'قيمة المشتريات من  مورد
        If Cmbvendorname = "" Then Exit Function
        ' مشتريات
        Try
            Dim vintinval As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalpriceafterdisc) from purchase_bill where Vendorname =N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vintinval = 0 Else vintinval = dr(0)

            Return vintinval
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function
    Friend Function VnBTinF(ByVal Cmbvendorname As String) As Double 'قيمة مرتجعات المشتريات من مورد
        If Cmbvendorname = "" Then Exit Function
        ' مرتجع مشتريات
        Try
            Dim vinbtinval As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalpriceafterdisc) from IM_Btin where Vendorname =N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vinbtinval = 0 Else vinbtinval = dr(0)

            Return vinbtinval
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function
    Friend Function VnDiscF(ByVal Cmbvendorname As String) As Double ' قيمة خصومات لمورد
        If Cmbvendorname = "" Then Exit Function
        'خصومات 
        Try
            Dim vndiscount As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(amnt) from vndr_disc where Vendorname =N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vndiscount = 0 Else vndiscount = dr(0)

            Return vndiscount
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function
    Friend Function VnPayF(ByVal Cmbvendorname As String) As Double ' قيمة المدفوعات لمورد
        If Cmbvendorname = "" Then Exit Function
        'مدفوعات 
        Try
            Dim VnPay As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(VND_amx) from vnd where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VnPay = 0 Else VnPay = dr(0)

            Return VnPay
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function ValTinF(ByVal IdItems As String) As Double ' قيمة مشتريات صنف
        If IdItems = "" Then Exit Function
        ' قيمة مشتريات 
        Try
            Dim VTin As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from BilltINData where itm_id = N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VTin = 0 Else VTin = dr(0)

            Return VTin
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function ValSalF(ByVal IdItems As String) As Double ' قيمة مبيعات صنف
        If IdItems = "" Then Exit Function
        'مبيعات 
        Try
            Dim VSal As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from BillsalData where itm_id = N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VSal = 0 Else VSal = dr(0)

            Return VSal
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function ValBtinF(ByVal IdItems As String) As Double 'قيمة مرتجع مشتريات صنف
        If IdItems = "" Then Exit Function
        ' قيمة مرتجع مشتريات
        Try
            Dim VBtin As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from IM_Btin_Data where itm_id = N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VBtin = 0 Else VBtin = dr(0)

            Return VBtin
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function VBsalF(ByVal IdItems As String) As Double 'قيمة مرتجع مبيعات
        If IdItems = "" Then Exit Function
        ' قيمة مرتجع مبيعات
        Try
            Dim VBsal As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from IM_Bsal_Data where itm_id = N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VBsal = 0 Else VBsal = dr(0)

            Return VBsal
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function ValdecayedF(ByVal IdItems As String) As Double 'قيمة توالف صنف
        'قيمة توالف
        Try
            Dim Vdecayed As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from decayed where itm_id = N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Vdecayed = 0 Else Vdecayed = dr(0)

            Return Vdecayed
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function
    Friend Function TinF(ByVal IdItems As String) As Double ' مشتريات صنف
        If IdItems = "" Then Exit Function
        'مشتريات 
        Try
            Dim Tin As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from BilltINData where itm_id = N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Tin = 0 Else Tin = dr(0)

            Return Tin
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function SalF(ByVal IdItems As String) As Double 'مبيعات صنف
        If IdItems = "" Then Exit Function
        ' مبيعات
        Try
            Dim sal As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from BillsalData where itm_id = N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then sal = 0 Else sal = dr(0)

            Return sal
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function BtinF(ByVal IdItems As String) As Double 'مرتجع مشتريات صنف
        If IdItems = "" Then Exit Function
        'مرتجع مشتريات
        Try
            Dim Btin As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from IM_Btin_Data where itm_id = N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Btin = 0 Else Btin = dr(0)

            Return Btin
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function BsalF(ByVal IdItems As String) As Double 'مرتجع مشتريات صنف
        If IdItems = "" Then Exit Function
        'مرتجع مشتريات
        Try
            Dim Bsal As Double
            'مرتع مبيعات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from IM_Bsal_Data where itm_id = N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Bsal = 0 Else Bsal = dr(0)

            Return Bsal
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function decayedF(ByVal IdItems As String) As Double 'توالف صنف
        If IdItems = "" Then Exit Function
        ' توالف
        Try
            Dim decayed As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from decayed where itm_id = N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then decayed = 0 Else decayed = dr(0)

            Return decayed
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Sub VendorAccountTotal(ByVal Cmbvendorname As String)
        Try
            If Cmbvendorname = "" Then Exit Sub
            Dim vintinval, vinbtinval, vndiscount, Btinvndiscount, VnPay, VnReceipts, BtinVnPay, TaxValue, TotalActual, Totalvndiscount, TotalVnPay, Totalvintinval, VendorExpenses As Double
            Dim Status As String
            ' مشتريات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalpricebeforedisc) from purchase_bill where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vintinval = 0 Else vintinval = dr(0)
            vintinval = Math.Round(vintinval, 2)

            ' مرتجع مشتريات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalpricebeforedisc) from IM_Btin where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vinbtinval = 0 Else vinbtinval = dr(0)
            vinbtinval = Math.Round(vinbtinval, 2)

            'خصومات 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(amnt) from vndr_disc where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vndiscount = 0 Else vndiscount = dr(0)
            vndiscount = Math.Round(vndiscount, 2)

            'مرتجع خصومات 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(amnt) from IM_vndr_disc where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Btinvndiscount = 0 Else Btinvndiscount = dr(0)
            Btinvndiscount = Math.Round(Btinvndiscount, 2)

            'مدفوعات 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(VND_amx) from vnd where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VnPay = 0 Else VnPay = dr(0)
            VnPay = Math.Round(VnPay, 2)

            'مرتجع مدفوعات 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(VND_amx) from IM_Vnd where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then BtinVnPay = 0 Else BtinVnPay = dr(0)
            BtinVnPay = Math.Round(BtinVnPay, 2)

            ' قيمة ضريبة مشتريات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(PurchaseTaxValue) from purchase_bill where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then TaxValue = 0 Else TaxValue = dr(0)
            TaxValue = Math.Round(TaxValue, 2)

            'مقبوضات موردين  
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(VND_amx) from Vnd_Receipts where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VnReceipts = 0 Else VnReceipts = dr(0)
            VnReceipts = Math.Round(VnReceipts, 2)

            'مصروفات موردين  
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(Exp_Value) from Expenses where Vendorname = N'" & Cmbvendorname & "' and Payment_Status =N'أجل'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VendorExpenses = 0 Else VendorExpenses = dr(0)
            VendorExpenses = Math.Round(VendorExpenses, 2)

            ''الحالة نسبة او قيمة
            'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            'cmd.CommandText = "select Vendorname from vendors where Vendorname= N'" & Cmbvendorname & "'"
            'dr = cmd.ExecuteReader : dr.Read()
            'If dr.HasRows = False Then Exit Sub
            'If dr(0) Is DBNull.Value Then
            'Else
            '    Status = dr(0)
            'End If
            'If hintaName = "نسبة" Then
            '    Totalvndiscount = Val((Val(vintinval) * (100 - Val(vndiscount))) / 100)
            '    Totalvndiscount = Math.Round(Totalvndiscount, 1)
            'Else
            Totalvndiscount = vndiscount - Btinvndiscount
            Totalvndiscount = Math.Round(Totalvndiscount, 2)
            'End If

            TotalVnPay = VnPay - BtinVnPay
            TotalVnPay = Math.Round(TotalVnPay, 2)

            Totalvintinval = vintinval - vinbtinval
            Totalvintinval = Math.Round(Totalvintinval, 2)

            TotalActual = Totalvintinval - Totalvndiscount - TotalVnPay + TaxValue + VnReceipts + VendorExpenses
            TotalActual = Math.Round(TotalActual, 2)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If TotalActual > 0 Then
                cmd.CommandText = "update vendors set BVndDiscount = " & Btinvndiscount & ",BVndPay = " & BtinVnPay & ",valuereturns = " & vinbtinval & ",vintinval = " & vintinval & ",vndiscount = " & vndiscount & " ,VnPay = " & VnPay & ",VnReceipts = " & VnReceipts & ",TaxValue = " & TaxValue & ",VendorExpenses = " & VendorExpenses & ", vnamntcredit = " & TotalActual & " , vnamntdebit = 0 where Vendorname = N'" & Cmbvendorname & "'" : cmd.ExecuteNonQuery()
            ElseIf TotalActual < 0 Then
                cmd.CommandText = "update vendors set BVndDiscount = " & Btinvndiscount & ",BVndPay = " & BtinVnPay & ",valuereturns = " & vinbtinval & ",vintinval = " & vintinval & ",vndiscount = " & vndiscount & " ,VnPay = " & VnPay & ",VnReceipts = " & VnReceipts & ",TaxValue = " & TaxValue & ",VendorExpenses = " & VendorExpenses & ", vnamntcredit = 0 , vnamntdebit = " & TotalActual * -1 & " where Vendorname = N'" & Cmbvendorname & "'" : cmd.ExecuteNonQuery()
            ElseIf TotalActual = 0 Then
                cmd.CommandText = "update vendors set BVndDiscount = " & Btinvndiscount & ",BVndPay = " & BtinVnPay & ",valuereturns = " & vinbtinval & ",vintinval = " & vintinval & ",vndiscount = " & vndiscount & " ,VnPay = " & VnPay & ",VnReceipts = " & VnReceipts & ",TaxValue = " & TaxValue & ",VendorExpenses = " & VendorExpenses & ", vnamntcredit = 0 , vnamntdebit = 0 where Vendorname = N'" & Cmbvendorname & "'" : cmd.ExecuteNonQuery()
            End If
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Sub

    Friend Sub CustomerAccountTotal(ByVal Cmbvendorname As String)
        If Cmbvendorname = "" Then Exit Sub
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        'Try
        Dim vintinval, vinbtinval, vndiscount, vndiscountOther, BVstdiscount, VnPay, VnReceipts, BVstPay, ValueVAT, TotalActual, TotalBVstdiscount, TotalBVstPay, Totalvintinval, Total_Qunt, Vst_Check_Type As Double
        ' مبيعات
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalpricebeforedisc) from Sales_bill where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vintinval = 0 Else vintinval = dr(0)
            vintinval = Math.Round(vintinval, 2)

            ' مرتجع مبيعات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalpricebeforedisc) from IM_Bsal where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vinbtinval = 0 Else vinbtinval = dr(0)
            vinbtinval = Math.Round(vinbtinval, 2)

            'خصومات 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(amnt) from Vst_disc where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vndiscount = 0 Else vndiscount = dr(0)
            vndiscount = Math.Round(vndiscount, 2)

            'خصومات أخرى 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(amnt) from Vst_disc_other where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vndiscountOther = 0 Else vndiscountOther = dr(0)
            vndiscountOther = Math.Round(vndiscountOther, 2)

            ' مرتجع خصومات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(amnt) from IM_Vst_disc where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then BVstdiscount = 0 Else BVstdiscount = dr(0)
            BVstdiscount = Math.Round(BVstdiscount, 2)

            'مقبوضات  
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(VND_amx) from vst where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VnPay = 0 Else VnPay = dr(0)
            VnPay = Math.Round(VnPay, 2)

            'مرتجع مقبوضات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(VND_amx) from IM_Vst where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then BVstPay = 0 Else BVstPay = dr(0)
            BVstPay = Math.Round(BVstPay, 2)

            'مدفوعات عملاء  
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(VND_amx) from Vst_Receipts where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VnReceipts = 0 Else VnReceipts = dr(0)
            VnReceipts = Math.Round(VnReceipts, 2)

            'ضريبة VAT 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(ValueVAT) from Sales_bill where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then ValueVAT = 0 Else ValueVAT = dr(0)
            ValueVAT = Math.Round(ValueVAT, 2)

            'كميات المبيعات  
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu_unity) from BillsalData where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Total_Qunt = 0 Else Total_Qunt = dr(0)
        Total_Qunt = Math.Round(Total_Qunt, 2)

        'أوراق القبض  
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(PaymentTotal) from Vst_Check_Type where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then Vst_Check_Type = 0 Else Vst_Check_Type = dr(0)
        Vst_Check_Type = Math.Round(Vst_Check_Type, 2)


        Totalvintinval = vintinval - vinbtinval
            Totalvintinval = Math.Round(Totalvintinval, 2)

            If BVstdiscount < vndiscount Then
                TotalBVstdiscount = vndiscount + vndiscountOther - BVstdiscount
            Else
                TotalBVstdiscount = BVstdiscount + vndiscountOther - vndiscount
            End If
            TotalBVstdiscount = Math.Round(TotalBVstdiscount, 2)

            If BVstPay < VnPay Then
                TotalBVstPay = VnPay - BVstPay
            Else
                TotalBVstPay = BVstPay - VnPay
            End If
            TotalBVstPay = Math.Round(TotalBVstPay, 2)

        TotalActual = Totalvintinval - TotalBVstdiscount - TotalBVstPay + VnReceipts + ValueVAT - Vst_Check_Type
        TotalActual = Math.Round(TotalActual, 2)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If TotalActual > 0 Then
            cmd.CommandText = "update customers set BVstDiscount = " & BVstdiscount & ",BVstPay = " & BVstPay & ",VstDiscOther = " & vndiscountOther & ",valuereturns = " & vinbtinval & ",vintinval = " & vintinval & ",vndiscount = " & vndiscount & " ,VnPay = " & VnPay & ",VnReceipts = " & VnReceipts & ",ValueVAT = " & ValueVAT & ",Total_Qunt = " & Total_Qunt & ",Vst_Check_Type = " & Vst_Check_Type & ", vnamntcredit = " & TotalActual & " , vnamntdebit = 0 where Vendorname = N'" & Cmbvendorname & "'" : cmd.ExecuteNonQuery()
            ElseIf TotalActual < 0 Then
            cmd.CommandText = "update customers set BVstDiscount = " & BVstdiscount & ",BVstPay = " & BVstPay & ",VstDiscOther = " & vndiscountOther & ",valuereturns = " & vinbtinval & ",vintinval = " & vintinval & ",vndiscount = " & vndiscount & " ,VnPay = " & VnPay & ",VnReceipts = " & VnReceipts & ",ValueVAT = " & ValueVAT & ",Total_Qunt = " & Total_Qunt & ",Vst_Check_Type = " & Vst_Check_Type & ", vnamntcredit = 0 , vnamntdebit = " & TotalActual * -1 & " where Vendorname = N'" & Cmbvendorname & "'" : cmd.ExecuteNonQuery()
            ElseIf TotalActual = 0 Then
            cmd.CommandText = "update customers set BVstDiscount = " & BVstdiscount & ",BVstPay = " & BVstPay & ",VstDiscOther = " & vndiscountOther & ",valuereturns = " & vinbtinval & ",vintinval = " & vintinval & ",vndiscount = " & vndiscount & " ,VnPay = " & VnPay & ",VnReceipts = " & VnReceipts & ",ValueVAT = " & ValueVAT & ",Total_Qunt = " & Total_Qunt & ",Vst_Check_Type = " & Vst_Check_Type & ", vnamntcredit = 0 , vnamntdebit = 0 where Vendorname = N'" & Cmbvendorname & "'" : cmd.ExecuteNonQuery()
            End If
        'Catch ex As Exception
        '    ErrorHandling(ex, "IMHM_Point_Sales")
        'End Try
    End Sub

    Friend Sub EmployeesAccountTotal(ByVal EmpName As String)
        If EmpName = "" Then Exit Sub
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        Try
            Dim vinsalval, vinbsalval, TotalAmountSal, TotalAmountBSal, TotalAmount, TotalActual As Double
            ' مبيعات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from BillsalData where EmpName = N'" & EmpName & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vinsalval = 0 Else vinsalval = dr(0)

            ' مرتجع مبيعات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from IM_Bsal_Data where EmpName = N'" & EmpName & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then vinbsalval = 0 Else vinbsalval = dr(0)

            ' كميات المبيعات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from BillsalData where EmpName = N'" & EmpName & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then TotalAmountSal = 0 Else TotalAmountSal = dr(0)

            ' كميات مرتجعات المبيعات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from IM_Bsal_Data where EmpName = N'" & EmpName & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then TotalAmountBSal = 0 Else TotalAmountBSal = dr(0)


            TotalActual = vinsalval - vinbsalval
            TotalActual = Math.Round(TotalActual, 2)
            TotalAmount = TotalAmountSal - TotalAmountBSal
            TotalAmount = Math.Round(TotalAmount, 2)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update EmployeesAccount set vintinval = " & TotalActual & ",TotalAmount = " & TotalAmount & " where NameEmployee = N'" & EmpName & "'" : cmd.ExecuteNonQuery()
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Sub

    Friend Function Get_Itm_Store2(ByVal IdItems As String)
        If IdItems = "" Then Exit Function
        Dim TIN As Double
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select store from Items where itm_id = N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then TIN = 0 : dr.Close() : Return TIN : Exit Function
            If dr(0) Is DBNull.Value Then TIN = 0 Else TIN = dr(0)
            Return TIN
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function Get_Itm_Store(ByVal IdItems As String, ByVal Stores As String)
        If IdItems = "" Then Exit Function
        Dim TIN As Double
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select store from Items where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then TIN = 0 : Return TIN : Exit Function
            If dr(0) Is DBNull.Value Then TIN = 0 Else TIN = dr(0)
            Return TIN
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function Get_Itm_StoreCarton(ByVal IdItems As String, ByVal Stores As String)
        If IdItems = "" Then Exit Function
        Dim TIN As Double
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select StoreCarton from Items where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then TIN = 0 : Return TIN : Exit Function
            If dr(0) Is DBNull.Value Then TIN = 0 Else TIN = dr(0)
            Return TIN
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Function Get_Itm_Range(ByVal IdItems As String, ByVal Stores As String)
        If IdItems = "" Then Exit Function
        Dim TIN As Double
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select rng from Items where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then TIN = 0 : dr.Close() : Return TIN : Exit Function
            If dr(0) Is DBNull.Value Then TIN = 0 Else TIN = dr(0)
            Return TIN
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Friend Sub Store(ByVal IdItems As String, ByVal Stores As String)
        If IdItems = "" Then Exit Sub
        Dim Tin, Sal, Btin, Bsal, decayed, SalManufacturing, TinManufacturing, SalTransfer, TinTransfer, VTin, VSal, VBtin, VBsal, Vdecayed, ValSalTin, VManufacturing, Store, Profits, ProfitsSal, ProfitsBSal, ValStore, TinPrice, PriceMedium, PriceSmall, StoreCarton, TotalStoreCarton As Double
        Dim group_name As String = ""
        Dim sname As String = ""
        'Try
        'مشتريات 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(qu) from BilltINData where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Tin = 0 Else Tin = dr(0)

            'مبيعات 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(qu) from BillsalData where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Sal = 0 Else Sal = dr(0)

        'مرتجع مشتريات
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from IM_Btin_Data where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Btin = 0 Else Btin = dr(0)

            'مرتجع مبيعات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from IM_Bsal_Data where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Bsal = 0 Else Bsal = dr(0)

            ' توالف
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from data_decayed where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then decayed = 0 Else decayed = dr(0)

            'مبيعات تصنبع 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from Manufacturing_BillsalData where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then SalManufacturing = 0 Else SalManufacturing = dr(0)

            'مشتريات تصنيع 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from Manufacturing_BilltINData where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then TinManufacturing = 0 Else TinManufacturing = dr(0)

            'مبيعات تحويل بين المخازن 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from ItemsTransfer_BillsalData where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then SalTransfer = 0 Else SalTransfer = dr(0)

            'مشتريات تحويل بين المخازن 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from ItemsTransfer_BilltINData where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then TinTransfer = 0 Else TinTransfer = dr(0)


        ''تحويل بضاعة من مخزن الى مخزن أخر 
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select sum(qu) from ItemsTransfer where itm_id = N'" & IdItems & "'  and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
        'If dr(0) Is DBNull.Value Then Transfer = 0 Else Transfer = dr(0)

        ''تحويل بضاعة تحديد نوع التحويل 
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select sum(TypeTransfer) from ItemsTransfer where itm_id = N'" & IdItems & "'  and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
        'If dr(0) Is DBNull.Value Then TypeTransfer = 0 Else TypeTransfer = dr(0)

        ''الكمية الاكبر والمتوسط ةالاصغر 
        'Cls.Select_More_Data_Stores("items", "PriceMedium,PriceSmall", "itm_id = N'" & IdItems & "'  and Stores =N'" & Stores & "'")
        'If dr.HasRows = True Then
        'PriceMedium = Val(dr(0).ToString)
        'PriceSmall = Val(dr(1).ToString)
        'End If


        Store = Tin - Sal + Bsal - Btin - decayed + TinManufacturing - SalManufacturing + TinTransfer - SalTransfer
        CurrentStock = Store

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select NumberPieces from View_ItemsUnity where itm_id=N'" & IdItems & "' and Stores=N'" & Stores & "' and UnitySize_ID =N'3'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            NumberPieces = dr(0).ToString
            If Store = 0 And NumberPieces = 0 Then
                TotalStoreCarton = 0
            Else
                TotalStoreCarton = Val(Store) / Val(NumberPieces)
                TotalStoreCarton = Math.Round(Val(TotalStoreCarton), 2)
            End If
            StoreCarton = TotalStoreCarton
        End If

        ' XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
        ' قيمة مشتريات 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(totalprice) from BilltINData where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VTin = 0 Else VTin = dr(0)
            VTin = Math.Round(VTin, 2)

            'قيمة مبيعات  
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(totalprice) from BillsalData where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VSal = 0 Else VSal = dr(0)
        VSal = Math.Round(VSal, 2)

        'قيمة مبيعات بسعر الشراء  
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(SalTinTotalprice) from BillsalData where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then ValSalTin = 0 Else ValSalTin = dr(0)
        ValSalTin = Math.Round(ValSalTin, 2)

        ' قيمة مرتجع مشتريات
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from IM_Btin_Data where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VBtin = 0 Else VBtin = dr(0)
            VBtin = Math.Round(VBtin, 2)

            ' قيمة مرتجع مبيعات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from IM_Bsal_Data where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VBsal = 0 Else VBsal = dr(0)
            VBsal = Math.Round(VBsal, 2)

            'قيمة توالف
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from data_decayed where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Vdecayed = 0 Else Vdecayed = dr(0)
            Vdecayed = Math.Round(Vdecayed, 2)

            'قيمة الخامات  
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from Manufacturing_BillsalData where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VManufacturing = 0 Else VManufacturing = dr(0)
            VManufacturing = Math.Round(VManufacturing, 2)


            ' """"""""""""""""""""""""""""""

            ' قيمة المخزون 
            If LastTinPriceItems = "YES" Then
                Cls.Select_More_Data_Stores("items", "TinPrice", "itm_id = N'" & IdItems & "'  and Stores =N'" & Stores & "'")
            Else
                Cls.Select_More_Data_Stores("items", "TinPriceAverage", "itm_id = N'" & IdItems & "'  and Stores =N'" & Stores & "'")
            End If
            If dr.HasRows = True Then
            TinPrice = Val(dr(0).ToString)
        End If

            ValStore = Store * TinPrice
        ValStore = Math.Round(ValStore, 2)
        CurrentStockTotal = ValStore
        '"""""""""""""""""""""""""""""""

        ' ارباح المبيعات
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(Profits) from BillsalData where  itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then ProfitsSal = 0 Else ProfitsSal = dr(0)

            ' مرتجع ارباح المبيعات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(Profits) from IM_Bsal_Data where  itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then ProfitsBSal = 0 Else ProfitsBSal = dr(0)

            Profits = ProfitsSal - ProfitsBSal
            Profits = Math.Round(Profits, 2)

            '"""""""""""""""""""""""""""""""

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "update Items set tin = " & Tin & " , sal = " & Sal & ", btin = " & Btin & ", bsal = " & Bsal & ",decayed = " & decayed & ""
        S = S & ",tinpricetotal = " & VTin & "  ,salpricetotal= " & VSal & "  , btinpricetotal = " & VBtin & "  , bsalpricetotal = " & VBsal & "  , decayedpricetotal = " & Vdecayed & ",store = " & Store & " ,ValStore = " & ValStore & ",profits = " & Profits & ",StoreCarton = " & StoreCarton & ", saltinpricetotal = " & ValSalTin & " where itm_id =N'" & IdItems & "' and Stores =N'" & Stores & "'"
        cmd.CommandText = S : cmd.ExecuteNonQuery()


        'Catch ex As Exception
        '    ErrorHandling(ex, "IMHM_Point_Sales")
        'End Try
    End Sub

    Friend Sub StoreExpired(ByVal IdItems As String, ByVal Stores As String, ByVal bill_EndDate As String, ByVal bill_no As String)
        If IdItems = "" Then Exit Sub
        Dim Tin, Sal, Btin, Bsal, decayed, SalManufacturing, TinManufacturing, SalTransfer, TinTransfer, Store, StoreCarton, TotalStoreCarton As Double
        'Dim bill_EndDate As String = ""

        If bill_EndDate = "" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select itm_id, itm_name, MIN(bill_EndDate) As Exbill_EndDate, Expired, qu_expired, Stores From dbo.BilltINData Group By itm_id, itm_name, Expired, qu_expired, Stores  HAVING(itm_id = N'" & IdItems & "') AND (Stores = N'" & Stores & "') AND (qu_expired <> 0)"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                bill_EndDate = dr(2).ToString
            End If
        End If

        'Try
        'مشتريات 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(qu) from BilltINData where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "' and bill_EndDate =N'" & bill_EndDate & "' and bill_no_Expired =N'" & bill_no & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then Tin = 0 Else Tin = dr(0)

        'مبيعات 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(qu) from BillsalData where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "' and bill_EndDate =N'" & bill_EndDate & "' and bill_no_Expired =N'" & bill_no & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then Sal = 0 Else Sal = dr(0)

        'مرتجع مشتريات
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(qu) from IM_Btin_Data where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "' and bill_EndDate =N'" & bill_EndDate & "' and bill_no_Expired =N'" & bill_no & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then Btin = 0 Else Btin = dr(0)

        'مرتجع مبيعات
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(qu) from IM_Bsal_Data where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "' and bill_EndDate =N'" & bill_EndDate & "' and bill_no_Expired =N'" & bill_no & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then Bsal = 0 Else Bsal = dr(0)

        ' توالف
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(qu) from data_decayed where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "' and bill_EndDate =N'" & bill_EndDate & "' and bill_no_Expired =N'" & bill_no & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then decayed = 0 Else decayed = dr(0)

        'مبيعات تصنبع 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(qu) from Manufacturing_BillsalData where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "' and bill_EndDate =N'" & bill_EndDate & "' and bill_no_Expired =N'" & bill_no & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then SalManufacturing = 0 Else SalManufacturing = dr(0)

        'مشتريات تصنيع 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(qu) from Manufacturing_BilltINData where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "' and bill_EndDate =N'" & bill_EndDate & "' and bill_no_Expired =N'" & bill_no & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then TinManufacturing = 0 Else TinManufacturing = dr(0)

        'مبيعات تحويل بين المخازن 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(qu) from ItemsTransfer_BillsalData where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "' and bill_EndDate =N'" & bill_EndDate & "' and bill_no_Expired =N'" & bill_no & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then SalTransfer = 0 Else SalTransfer = dr(0)

        'مشتريات تحويل بين المخازن 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(qu) from ItemsTransfer_BilltINData where itm_id = N'" & IdItems & "' and Stores =N'" & Stores & "' and bill_EndDate =N'" & bill_EndDate & "' and bill_no_Expired =N'" & bill_no & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then TinTransfer = 0 Else TinTransfer = dr(0)


        Store = Tin - Sal + Bsal - Btin - decayed + TinManufacturing - SalManufacturing + TinTransfer - SalTransfer


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select NumberPieces from View_ItemsUnity where itm_id=N'" & IdItems & "' and Stores=N'" & Stores & "' and UnitySize_ID =N'3'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            NumberPieces = dr(0).ToString
            If Store = 0 And NumberPieces = 0 Then
                TotalStoreCarton = 0
            Else
                TotalStoreCarton = Val(Store) / Val(NumberPieces)
                TotalStoreCarton = Math.Round(Val(TotalStoreCarton), 2)
            End If
            StoreCarton = TotalStoreCarton
        End If

        '"""""""""""""""""""""""""""""""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update BilltINData set qu_expired = " & Store & ",qu_expired_carton = " & StoreCarton & " where itm_id =N'" & IdItems & "' and Stores =N'" & Stores & "' and bill_EndDate =N'" & bill_EndDate & "' and bill_no_Expired =N'" & bill_no & "'"
        cmd.CommandText = S : cmd.ExecuteNonQuery()
        'Catch ex As Exception
        '    ErrorHandling(ex, "IMHM_Point_Sales")
        'End Try
    End Sub


    Friend Sub StoreTransfer(ByVal IdItems As String, ByVal QuntTransfer As String)
        If IdItems = "" Then Exit Sub
        Dim Tin, Sal, Btin, Bsal, decayed, VTin, VSal, VBtin, VBsal, Vdecayed, Store, Profits, ValStore, TinPrice, TotalTin As Double
        Try
            'مشتريات 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from BilltINData where itm_id =N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Tin = 0 Else Tin = dr(0)
            'مبيعات 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from BillsalData where itm_id =N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Sal = 0 Else Sal = dr(0)

            'مرتجع مشتريات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from IM_Btin_Data where itm_id =N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Btin = 0 Else Btin = dr(0)
            'مرتجع مبيعات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from IM_Bsal_Data where itm_id =N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Bsal = 0 Else Bsal = dr(0)
            ' توالف
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from data_decayed where itm_id =N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then decayed = 0 Else decayed = dr(0)
            Store = Tin - QuntTransfer - Sal + Bsal - Btin - decayed
            TotalTin = Tin - QuntTransfer
            ' XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
            ' قيمة مشتريات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from BilltINData where itm_id =N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VTin = 0 Else VTin = dr(0)
            'قيمة مبيعات  
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from BillsalData where itm_id =N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VSal = 0 Else VSal = dr(0)
            ' قيمة مرتجع مشتريات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from IM_Btin_Data where itm_id =N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VBtin = 0 Else VBtin = dr(0)
            ' قيمة مرتجع مبيعات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from IM_Bsal_Data where itm_id =N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VBsal = 0 Else VBsal = dr(0)
            'قيمة توالف
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from data_decayed where itm_id =N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Vdecayed = 0 Else Vdecayed = dr(0)

            ' """"""""""""""""""""""""""""""
            ' قيمة المخزون 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TinPrice from items where  itm_id =N'" & IdItems & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then TinPrice = 0 Else TinPrice = dr(0)

            ValStore = Store * TinPrice
            '"""""""""""""""""""""""""""""""
            Profits = ValStore + VSal - VTin - VBsal + VBtin - Vdecayed

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "update Items set tin = " & TotalTin & " , sal = " & Sal & ", btin = " & Btin & ", bsal = " & Bsal & ",decayed = " & decayed & ""
            S = S & ",tinpricetotal = " & VTin & "  ,salpricetotal= " & VSal & "  , btinpricetotal = " & VBtin & "  , bsalpricetotal = " & VBsal & "  , decayedpricetotal = " & Vdecayed & ",store = " & Store & " ,ValStore = " & ValStore & ",profits = " & Profits & " where itm_id =N'" & IdItems & "'"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Sub

    Friend Sub Store_By_Date(ByVal IdItems As String, ByVal Xdate As String)
        If IdItems = "" Then Exit Sub
        Dim Tin, Sal, Btin, Bsal, decayed, VTin, VSal, VBtin, VBsal, Vdecayed, Store, Profits, ValStore, TinPrice As Double
        Try
            'مشتريات 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from BilltINData where itm_id = N'" & IdItems & "' and bill_date =N'" & Xdate & "' and bill_date =N'" & Xdate & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Tin = 0 Else Tin = dr(0)

            'مبيعات 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from BillsalData where itm_id = N'" & IdItems & "' and bill_date =N'" & Xdate & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Sal = 0 Else Sal = dr(0)

            'مرتجع مشتريات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from IM_Btin_Data where itm_id = N'" & IdItems & "' and bill_date =N'" & Xdate & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Btin = 0 Else Btin = dr(0)

            'مرتع مبيعات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from IM_Bsal_Data where itm_id = N'" & IdItems & "' and bill_date =N'" & Xdate & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Bsal = 0 Else Bsal = dr(0)
            ' توالف
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(qu) from data_decayed where itm_id = N'" & IdItems & "' and bill_date =N'" & Xdate & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then decayed = 0 Else decayed = dr(0)

            Store = Tin - Sal + Bsal - Btin - decayed
            ' XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
            ' قيمة مشتريات 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from BilltINData where itm_id = N'" & IdItems & "' and bill_date =N'" & Xdate & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VTin = 0 Else VTin = dr(0)

            'قيمة مبيعات  
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from BillsalData where itm_id = N'" & IdItems & "' and bill_date =N'" & Xdate & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VSal = 0 Else VSal = dr(0)

            ' قيمة مرتجع مشتريات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from IM_Btin_Data where itm_id = N'" & IdItems & "' and bill_date =N'" & Xdate & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VBtin = 0 Else VBtin = dr(0)

            ' قيمة مرتجع مبيعات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from IM_Bsal_Data where itm_id = N'" & IdItems & "' and bill_date =N'" & Xdate & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then VBsal = 0 Else VBsal = dr(0)
            'قيمة توالف
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(totalprice) from data_decayed where itm_id = N'" & IdItems & "' and bill_date =N'" & Xdate & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Vdecayed = 0 Else Vdecayed = dr(0)

            ' """"""""""""""""""""""""""""""
            ' قيمة المخزون 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TinPrice from items where  itm_id = N'" & IdItems & "' and bill_date =N'" & Xdate & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then TinPrice = 0 Else TinPrice = dr(0)
            ValStore = Store * TinPrice
            '"""""""""""""""""""""""""""""""
            Profits = ValStore + VSal - VTin - VBsal + VBtin - Vdecayed

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "update Items set tin = " & Tin & " , sal = " & Sal & ", btin = " & Btin & ", bsal = " & Bsal & ",decayed = " & decayed & ""
            S = S & ",tinpricetotal = " & VTin & "  ,salpricetotal= " & VSal & "  , btinpricetotal = " & VBtin & "  , bsalpricetotal = " & VBsal & "  , decayedpricetotal = " & Vdecayed & ",store = " & Store & " ,ValStore = " & ValStore & ",profits = " & Profits & " where itm_id =N'" & IdItems & "'"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Sub

    Friend Function VNState(ByVal Cmbvendorname As String) As String
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select vnamntdebit ,vnamntcredit from vendors where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            Dim vnamntdebit, vnamntcredit As Double
            If dr.HasRows = True Then
                If dr(0) Is DBNull.Value Then vnamntdebit = 0 Else vnamntdebit = dr(0)
                If dr(1) Is DBNull.Value Then vnamntcredit = 0 Else vnamntcredit = dr(1)
            End If

            Dim XSTATE As String
            If vnamntdebit > vnamntcredit Then ' مدين
                XSTATE = "النوع : مدين بمبلغ" & " " & Return_Alf_Genih(vnamntdebit) & " " & "ج"
            ElseIf vnamntcredit > vnamntdebit Then  ' دائن 
                XSTATE = "النوع : دائن  بمبلغ " & " " & Return_Alf_Genih(vnamntcredit) & " " & "ج"
            ElseIf vnamntdebit = vnamntcredit Then  ' خالص
                XSTATE = "النوع : خالص"
            Else
                XSTATE = "##########"
            End If
            Return XSTATE
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function
    Friend Function VNStateAll() As String
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Sum(vnamntdebit) ,Sum(vnamntcredit) from vendors" : dr = cmd.ExecuteReader : dr.Read()

            Dim vnamntdebit, vnamntcredit As Double

            If dr(0) Is DBNull.Value Then vnamntdebit = 0 Else vnamntdebit = dr(0)
            If dr(1) Is DBNull.Value Then vnamntcredit = 0 Else vnamntcredit = dr(1)
            Dim XSTATE As String
            If vnamntdebit > vnamntcredit Then ' مدين
                XSTATE = "النوع : مدين بمبلغ" & " " & Return_Alf_Genih(vnamntdebit) & " " & "ج"
            ElseIf vnamntcredit > vnamntdebit Then  ' دائن 
                XSTATE = "النوع : دائن  بمبلغ " & " " & Return_Alf_Genih(vnamntcredit) & " " & "ج"
            ElseIf vnamntdebit = vnamntcredit Then  ' خالص
                XSTATE = "النوع : خالص"
            Else
                XSTATE = "##########"
            End If
            Return XSTATE
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function
    Friend Function CtmState(ByVal Cmbvendorname As String) As String
        Try
            Dim vnamntdebit, vnamntcredit As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select vnamntdebit ,vnamntcredit from Customers where Vendorname = N'" & Cmbvendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                If dr(0) Is DBNull.Value Then vnamntdebit = 0 Else vnamntdebit = dr(0)
                If dr(1) Is DBNull.Value Then vnamntcredit = 0 Else vnamntcredit = dr(1)
            End If


            Dim XSTATE As String
            If vnamntdebit > vnamntcredit Then ' مدين
                XSTATE = "النوع : مدين بمبلغ" & " " & Return_Alf_Genih(vnamntdebit) & " " & "ج"
            ElseIf vnamntcredit > vnamntdebit Then  ' دائن 
                XSTATE = "النوع : دائن  بمبلغ " & " " & Return_Alf_Genih(vnamntcredit) & " " & "ج"
            ElseIf vnamntdebit = vnamntcredit Then  ' خالص
                XSTATE = "النوع : خالص"
            Else
                XSTATE = "##########"
            End If
            Return XSTATE
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function
    Friend Function CtmStateAll() As String
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(vnamntdebit) ,sum(vnamntcredit) from Customers" : dr = cmd.ExecuteReader : dr.Read()

            Dim vnamntdebit, vnamntcredit As Double
            If dr(0) Is DBNull.Value Then vnamntdebit = 0 Else vnamntdebit = dr(0)
            If dr(1) Is DBNull.Value Then vnamntcredit = 0 Else vnamntcredit = dr(1)

            Dim XSTATE As String
            If vnamntdebit > vnamntcredit Then ' مدين
                XSTATE = "النوع : مدين بمبلغ" & " " & Return_Alf_Genih(vnamntdebit) & " " & "ج"
            ElseIf vnamntcredit > vnamntdebit Then  ' دائن 
                XSTATE = "النوع : دائن  بمبلغ " & " " & Return_Alf_Genih(vnamntcredit) & " " & "ج"
            ElseIf vnamntdebit = vnamntcredit Then  ' خالص
                XSTATE = "النوع : خالص"
            Else
                XSTATE = "##########"
            End If
            Return XSTATE
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function

    Function Return_Alf_Genih(ByVal X As Integer) As String
        If Len(X) <= 3 Then Return X : Exit Function
        If Len(X) > 3 Then
            Dim M As Integer
            M = Int(X - 1000 * Int(X / 1000))
            Dim MM As Integer = Int(X / 1000)
            Return M & " | " & MM
        End If
    End Function

    Dim aray_1 As New ArrayList
    Dim aray_2 As New ArrayList
    Dim aray_3 As New ArrayList
    Dim aray_4 As New ArrayList
    Dim aray_5 As New ArrayList
    Friend Sub UpdateDataBase()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete from Date_Finance" : cmd.ExecuteNonQuery()

            aray_1.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select distinct Vendorname from vendors"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_1.Add(dr(0))
            Loop

            aray_2.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select distinct Vendorname from customers"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_2.Add(dr(0))
            Loop


            aray_3.Clear()
            aray_4.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select itm_id,Stores from Items"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_3.Add(dr(0))
                aray_4.Add(dr(1))
            Loop

            aray_5.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select distinct NameEmployee from Employees"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_5.Add(dr(0))
            Loop

        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try

        For i As Integer = 0 To aray_1.Count - 1
            IM.VendorAccountTotal(aray_1(i))
        Next

        For i As Integer = 0 To aray_2.Count - 1
            IM.CustomerAccountTotal(aray_2(i))
        Next

        For i As Integer = 0 To aray_3.Count - 1
            IM.Store(aray_3(i), aray_4(i))
        Next

        For i As Integer = 0 To aray_5.Count - 1
            IM.EmployeesAccountTotal(aray_5(i))
        Next

    End Sub
    Friend Function fn_summettion(ByVal gridview As Object, ByVal columan As Integer) As Integer
        Dim SM As Double
        For i As Integer = 0 To gridview.Rows.Count - 1
            SM += gridview.Rows(i).Cells(columan).Value
        Next
        Return SM
    End Function
    Friend Function FN_SURE_DATA(ByVal TABLE_NAME As String, ByVal FILD_AND_WHERE As String) As Boolean
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "SELECT  * FROM  " & TABLE_NAME & " WHERE  " & FILD_AND_WHERE
            dr = cmd.ExecuteReader : Dim A As Boolean = dr.HasRows

            Return A
        Catch ex As Exception
            ErrorHandling(ex, "IMHM_Point_Sales")
        End Try
    End Function
End Class
