﻿Imports CrystalDecisions.CrystalReports.Engine

Public Class Frm_Product_Order_Filling_Manufacturing_Show
    Dim Dt_AddBill As New DataTable
    Dim RNXD As Integer
    Dim Manufacturing_ID As String
    Dim ActionRead As Boolean = False
    Dim WithEvents BS As New BindingSource
    Dim ListBoxSelectedIndex As Integer
    Dim QuntAll As Integer
    Dim AlaertParcode As Boolean
    Dim itm_id As String = ""
    Dim itm_cat As String = ""
    Dim itm_name As String = ""
    Dim Unity As String = ""
    Dim qunt As String = ""
    Dim qunt_unity As String = ""
    Dim quntTotal As String = ""
    Dim StoresFrom As String = ""
    Dim StoresTo As String = ""
    Dim QuntFrom As Double
    Dim TinPrice As String = ""
    Dim StausMainStores As Integer = 0

#Region "View Items"
    Private Sub Frm_Offers_Items_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Bra.Fil("groups", "g_name", cmbCatsManufacturingView)
        Cls.fill_combo_Branch("stores", "store", cmbStoreView)
        Cls.fill_combo_Branch("stores", "store", cmbStoreManufacturing)

        Cls.FillComboDataAdapterQuickSearch("Items", "sname", cmbitmnm)

        Cls.fill_combo_Branch("stores", "store", cmbStoresFrom)
        Cls.fill_combo_Branch("stores", "store", cmbStoresTo)
        Cls.fill_combo_Branch("stores", "store", cmbStoreManufacturing)
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployees)
        PanelEdit.Top = 5000
    End Sub

    Private Sub cmbStoreManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbStoreView.SelectedIndexChanged
        If cmbStoreView.Text.Trim = "" Then Exit Sub
        cmbCatsManufacturingView.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select distinct group_name from Items where Stores =N'" & cmbStoreView.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbCatsManufacturingView.Items.Add(Trim(dr(0)))
        Loop
        cmbCatsManufacturingView.Text = ""
        cmbCatsManufacturingView.Focus()
    End Sub

    Private Sub cmbCatsManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbCatsManufacturingView.SelectedIndexChanged, cmbCatsManufacturing.SelectedIndexChanged
        If ActionRead = False Then
            If cmbCatsManufacturingView.Text.Trim = "" Then Exit Sub
            cmbItemsManufacturingView.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sname from Items where group_name =N'" & cmbCatsManufacturingView.Text & "' and Stores =N'" & cmbStoreManufacturing.Text & "' order by 1"
            dr = cmd.ExecuteReader
            Do While dr.Read
                cmbItemsManufacturingView.Items.Add(Trim(dr(0)))
            Loop
            cmbItemsManufacturingView.Text = ""
            cmbItemsManufacturingView.Focus()
        End If
    End Sub

    Private Sub cmbStoreManufacturing_DropDown(sender As Object, e As EventArgs) Handles cmbStoreView.DropDown
        cmbCatsManufacturingView.Text = ""
        cmbItemsManufacturingView.Text = ""
    End Sub

    Private Sub cmbCatsManufacturing_DropDown(sender As Object, e As EventArgs) Handles cmbCatsManufacturingView.DropDown
        cmbItemsManufacturingView.Text = ""
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        GetData()
        GetDetails()
    End Sub

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select Id As [#], bill_no As [رقم أمر الشغل], bill_date As التاريخ, sname As [أسم الصنف], Stores_From As [من مخزن], Stores_TO As [الى مخزن المصنع], Total_qu As [إجمالى الكميات], TotalCostPrice As [إجمالى التكلفة]  From dbo.View_ManufacturingFillOrder Group By Id, bill_no, bill_date, sname, Stores_From, Stores_TO, Total_qu, TotalCostPrice HAVING(Id <> N'')"
        If chkAll.Checked = False Then
            If cmbStoreView.Text <> "" Then
                S = S & " and  Stores_Manufacturing =N'" & cmbStoreView.Text & "'"
            End If
            If cmbCatsManufacturingView.Text <> "" Then
                S = S & " and  group_name =N'" & cmbCatsManufacturingView.Text & "'"
            End If
            If cmbItemsManufacturingView.Text <> "" Then
                S = S & " and  sname =N'" & cmbItemsManufacturingView.Text & "'"
            End If
            If txtbillnoSearch.Text <> "" Then
                S = S & " and  bill_no =N'" & txtbillnoSearch.Text & "'"
            End If
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and  bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم أمر الشغل]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [التاريخ]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [أسم الصنف]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        dgv_Manufacturing.DataSource = Cls.PopulateDataView(dr)

        Dim SM2 As String
        For i As Integer = 0 To dgv_Manufacturing.RowCount - 1
            SM2 = Val(dgv_Manufacturing.Rows(i).Cells(2).Value)
            SM2 = Cls.R_date(SM2)
            dgv_Manufacturing.Rows(i).Cells(2).Value = SM2
        Next

        Dim SM As Double
        For i As Integer = 0 To dgv_Manufacturing.Rows.Count - 1
            SM = SM + Val(dgv_Manufacturing.Rows(i).Cells(6).Value.ToString)
        Next
        txtTotalWeight.Text = SM

        Dim SM1 As Double
        For i As Integer = 0 To dgv_Manufacturing.Rows.Count - 1
            SM1 = SM1 + Val(dgv_Manufacturing.Rows(i).Cells(7).Value.ToString)
        Next
        txtTotalCostPrice.Text = SM

        dgv_Manufacturing.Columns(0).Visible = False
        dgv_Manufacturing.Columns(3).Width = 300
    End Sub

    Private Sub dgv_Manufacturing_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgv_Manufacturing.CellClick
        GetDetails()
    End Sub

    Private Sub GetDetails()
        If dgv_Manufacturing.RowCount = 0 Then Beep() : Exit Sub
        If (dgv_Manufacturing.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim bill_no As String
        bill_no = dgv_Manufacturing.SelectedRows(0).Cells(1).Value
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id as [الباركود] ,sname as [اسم الصنف],TinPrice as [سعر التكلفة] ,qu as [الكمية],totalprice as [إجمالى التكلفة],Stores as [المخزن] from View_ManufacturingFillOrderData where bill_no =N'" & bill_no & "'  order by 1"
        dr = cmd.ExecuteReader
        dgv_Material.DataSource = Cls.PopulateDataView(dr)
        dgv_Material.Columns(1).Width = 400

        Dim SM2 As String
        For i As Integer = 0 To dgv_Material.RowCount - 1
            SM2 = Val(dgv_Material.Rows(i).Cells(2).Value) * Val(dgv_Material.Rows(i).Cells(3).Value)
            dgv_Material.Rows(i).Cells(4).Value = SM2
        Next

    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbCatsManufacturingView.Enabled = False
            cmbItemsManufacturingView.Enabled = False
            cmbStoreView.Enabled = False
            txtbillnoSearch.Enabled = False
        Else
            cmbCatsManufacturingView.Enabled = True
            cmbItemsManufacturingView.Enabled = True
            cmbStoreView.Enabled = True
            txtbillnoSearch.Enabled = True
        End If
    End Sub
#End Region

#Region "Delete Items"
    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If dgv_Manufacturing.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If dgv_Manufacturing.RowCount = 0 Then Beep() : Exit Sub
        If (dgv_Manufacturing.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim bill_no As String

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To dgv_Manufacturing.SelectedRows.Count - 1
            bill_no = dgv_Manufacturing.SelectedRows(i).Cells(1).Value

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete From  ManufacturingFillOrder where bill_no =N'" & bill_no & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  Manufacturing_BillsalData where bill_no =N'" & bill_no & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  Manufacturing_BilltINData where bill_no =N'" & bill_no & "'" : cmd.ExecuteNonQuery()
        Next
        IM.UpdateDataBase()

        GetData()
        GetDetails()
    End Sub

#End Region

#Region "Print"
    Private Sub btnPrint_Click(sender As Object, e As EventArgs) Handles btnPrint.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If dgv_Manufacturing.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If dgv_Manufacturing.RowCount = 0 Then Beep() : Exit Sub
        If (dgv_Manufacturing.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        Dim bill_no As String = dgv_Manufacturing.SelectedRows(0).Cells(1).Value
        Dim bill_date As String = dgv_Manufacturing.SelectedRows(0).Cells(2).Value
        Dim sname As String = dgv_Manufacturing.SelectedRows(0).Cells(3).Value
        Dim Stores_From As String = dgv_Manufacturing.SelectedRows(0).Cells(4).Value
        Dim Stores_TO As String = dgv_Manufacturing.SelectedRows(0).Cells(5).Value
        Dim Total_qu As String = dgv_Manufacturing.SelectedRows(0).Cells(6).Value
        Dim TotalCostPrice As String = dgv_Manufacturing.SelectedRows(0).Cells(7).Value


        Dim CostPrice As String = ""
        Dim Manufacturing_Allowance As String = ""
        Dim Filling_Allowance As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from View_Product_Manufacturing where  SnameManufacturing = N'" & sname & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            CostPrice = dr("TinPrice")
            Manufacturing_Allowance = dr("Manufacturing_Allowance")
            Filling_Allowance = dr("Filling_Allowance")
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To dgv_Material.Rows.Count - 1
            TotalCostPrice = Val(dgv_Material.Rows(i).Cells(2).Value) * Val(dgv_Material.Rows(i).Cells(3).Value)
            S = "insert into PrintSalesPurchases(Company_Branch_ID,bill_no,bill_date,itm_id,itm_name,price,qu,totalpriceafterdisc,store,totalprice) values"
            S = S & " (N'" & Company_Branch_ID & "',N'" & dgv_Manufacturing.SelectedRows(i).Cells(1).Value & "',N'" & dgv_Manufacturing.SelectedRows(i).Cells(2).Value & "',N'" & dgv_Material.Rows(i).Cells(0).Value & "',N'" & dgv_Material.Rows(i).Cells(1).Value & "',N'" & dgv_Material.Rows(i).Cells(2).Value & "',N'" & dgv_Material.Rows(i).Cells(3).Value & "',N'" & TotalCostPrice & "',N'" & dgv_Material.Rows(i).Cells(5).Value & "',N'" & txtTotal_qu.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        AddReportView()
        Dim rpt As New Rpt_Product_Manufacturing

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        AddReportView()
        Dim txtname, txtNameAr, txtNameEn, bill_date_Object, CostPrice_Object, Weightqunt_Object, Filling_Allowance_Object, TotalCostPrice_Object, Manufacturing_Allowance_Object, txtbill_no_Object, txtbill_no_ObjectText As TextObject

        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "أمر شغل تعبئة وتصنيع"
        txtNameAr = rpt.Section1.ReportObjects("txtTitelNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelNameEn")
        txtNameEn.Text = NameEnCompany
        txtbill_no_Object = rpt.Section1.ReportObjects("txtbill_no")
        txtbill_no_Object.Text = bill_no
        txtbill_no_ObjectText = rpt.Section1.ReportObjects("txtbill_noText")
        txtbill_no_ObjectText.Text = "رقم أمر الشغل"
        bill_date_Object = rpt.Section1.ReportObjects("txtbill_date")
        bill_date_Object.Text = bill_date
        CostPrice_Object = rpt.Section1.ReportObjects("txtsname")
        CostPrice_Object.Text = sname
        Weightqunt_Object = rpt.Section1.ReportObjects("txtStores")
        Weightqunt_Object.Text = Stores_From
        TotalCostPrice_Object = rpt.Section1.ReportObjects("txtWeightqunt")
        TotalCostPrice_Object.Text = Total_qu
        Manufacturing_Allowance_Object = rpt.Section1.ReportObjects("txtTotalCostPrice")
        Manufacturing_Allowance_Object.Text = TotalCostPrice
        CostPrice_Object = rpt.Section1.ReportObjects("txtCostPrice")
        CostPrice_Object.Text = CostPrice
        Manufacturing_Allowance_Object = rpt.Section1.ReportObjects("txtManufacturing_Allowance")
        Manufacturing_Allowance_Object.Text = Manufacturing_Allowance
        Filling_Allowance_Object = rpt.Section1.ReportObjects("txtFilling_Allowance")
        Filling_Allowance_Object.Text = Filling_Allowance
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "أمر شغل تعبئة وتصنيع"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

#End Region

#Region "Edit Items"

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        PanelEdit.Dock = DockStyle.None
        PanelEdit.Top = 1000
    End Sub

    Private Sub cmbStoreManufacturingEdit_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbStoreManufacturing.SelectedIndexChanged
        If ActionRead = False Then
            If cmbStoreManufacturing.Text.Trim = "" Then Exit Sub
            cmbCatsManufacturing.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct group_name from Items where Stores =N'" & cmbStoreManufacturing.Text & "' order by 1"
            dr = cmd.ExecuteReader
            Do While dr.Read
                cmbCatsManufacturing.Items.Add(Trim(dr(0)))
            Loop
            cmbCatsManufacturing.Text = ""
        End If
    End Sub

    Private Sub cmbItemsManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbItemsManufacturing.SelectedIndexChanged
        If ActionRead = False Then
            Dim itm_id_Manufacturing As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id from Items where  sname = N'" & cmbItemsManufacturing.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : txtitm_id_Manufacturing.Text = dr("itm_id") : Else txtitm_id_Manufacturing.Text = 0 : End If
        End If
    End Sub

    Private Sub txtbill_no_KeyUp(sender As Object, e As KeyEventArgs) Handles txtbill_no.KeyUp
        If e.KeyCode = 13 Then
            dtpDateItem.Focus()
        End If
    End Sub

    Private Sub dtpDateItem_KeyUp(sender As Object, e As KeyEventArgs) Handles dtpDateItem.KeyUp
        If e.KeyCode = 13 Then
            cmbStoresFrom.Focus()
        End If
    End Sub

    Private Sub cmbEmployees_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbEmployees.KeyUp
        If e.KeyCode = 13 Then
            cmbStoresFrom.Focus()
            cmbStoresFrom.SelectAll()
        End If
    End Sub

    Private Sub cmbStoresFrom_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbStoresFrom.KeyUp
        If e.KeyCode = 13 Then
            cmbStoresTo.Focus()
        End If
    End Sub

    Private Sub GetDataStores()
        Bol = True

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id from items where sname=N'" & cmbitmnm.Text.Trim & "' and Stores =N'" & cmbStoresTo.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            TxtPrc.Text = dr(0)
        End If

        TxtPrc.Focus()
        TxtPrc.SelectAll()

        Bol = False
    End Sub

    Private Sub cmbStoresTo_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbStoresTo.KeyUp
        If e.KeyCode = 13 Then
            GetDataStores()
            TxtPrc.Focus()
        End If
    End Sub

    Private Sub TxtPrc_KeyUp(sender As Object, e As KeyEventArgs) Handles TxtPrc.KeyUp
        If e.KeyCode = 13 Then
            AlaertParcode = True
            Bol = True
            QuntFrom = 0
1:
            Try
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select itm_id,group_name,Unity,store from items where itm_id=N'" & TxtPrc.Text & "' and Stores=N'" & cmbStoresFrom.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    TxtPrc.Text = dr("itm_id").ToString()
                    cmbcats.Text = dr("group_name").ToString()
                    cmbUnity.Text = dr("Unity").ToString()
                    QuntAll = dr("store").ToString()
                End If
            Catch ex As Exception
                GoTo 1
            End Try
            '==========================================================
            For i As Integer = 0 To DTGV.Rows.Count - 1
                If DTGV.Rows(i).Cells(8).Value = cmbStoresFrom.Text Then
                    If DTGV.Rows(i).Cells(1).Value = TxtPrc.Text Then
                        QuntFrom += DTGV.Rows(i).Cells(6).Value
                    End If
                End If
            Next

            txtquntAll.Text = QuntAll - QuntFrom

            GetItemsUnity(cmbUnity, TxtPrc.Text)

            txtqunt.Text = 0
            txtquntUnity.Text = 0
            txtquntUnity.Focus()
            txtquntUnity.SelectAll()
            Bol = False
            AlaertParcode = False
        End If

    End Sub

    Private Sub TxtPrc_KeyDown(sender As Object, e As KeyEventArgs) Handles TxtPrc.KeyDown
        If ((e.KeyCode = Keys.S) AndAlso (e.Modifiers = (Keys.Control))) Then
            btnSaveAll.PerformClick()
        End If
    End Sub

    Private Sub cmbitmnm_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbitmnm.KeyUp
        If e.KeyCode = 13 Then
            If AlaertParcode = False Then
                GetDataSales()
            End If
        End If
    End Sub

    Private Sub cmbUnity_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbUnity.KeyUp
        If e.KeyCode = 13 Then
            cmbStoresFrom.Focus()
        End If
    End Sub

    Private Sub cmbUnity_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbUnity.SelectedIndexChanged
        If ActionRead = False Then
            If NotUnityItemsProgram = "YES" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & TxtPrc.Text & "' and Unity_Name=N'" & cmbUnity.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 0 : End If
                txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
            Else
                txtqunt.Text = Val(txtquntUnity.Text)
            End If
        End If
    End Sub

    Private Sub txtquntUnity_TextChanged(sender As Object, e As EventArgs) Handles txtquntUnity.TextChanged
        MyVars.CheckNumber(txtquntUnity)
        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If

    End Sub

    Private Sub txtqunt_TextChanged(sender As Object, e As EventArgs) Handles txtqunt.TextChanged
        If Not IsNumeric(txtqunt.Text) Then
            System.Windows.Forms.SendKeys.Send("{backspace}")
        End If
        txtquntAll.Text = Val(QuntAll) - Val(QuntFrom) - Val(txtqunt.Text)

    End Sub

    Private Sub txtquntUnity_KeyUp(sender As Object, e As KeyEventArgs) Handles txtquntUnity.KeyUp
        If e.KeyCode = 13 Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub btnsave_Click(sender As Object, e As EventArgs) Handles btnsave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateTextAdd() = False Then Exit Sub

        DTGV.DataSource = Fn_AddBill(0, TxtPrc.Text, cmbcats.Text, cmbitmnm.Text, cmbUnity.Text, TinPrice, txtqunt.Text, txtquntUnity.Text, txtquntAll.Text, cmbStoresFrom.Text, cmbStoresTo.Text)
        ClearAdd()
        TxtPrc.Focus()
        SumAllPrice()
        DTGV.Columns(0).Visible = False
        DTGV.Columns(6).Visible = False
        DTGV.Columns(11).Visible = False

    End Sub

    Friend Function Fn_AddBill(ByVal Col_IDTM As String, ByVal Col_Prc As String, ByVal Col_Cats As String, ByVal Col_Name As String, ByVal Col_Unity As String, ByVal Col_TinPrice As String _
, ByVal Col_Quant As Double, ByVal Col_qu_unity As Double, ByVal Col_QuantRemainder As Double, ByVal Col_StoreFrom As String, ByVal Col_StoreTo As String) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("IDTM", GetType(String))
            Dt_AddBill.Columns.Add("الباركود", GetType(String))
            Dt_AddBill.Columns.Add("أسم المجموعة", GetType(String))
            Dt_AddBill.Columns.Add("أسم الصنف", GetType(String))
            Dt_AddBill.Columns.Add("وحدة القياس", GetType(String))
            Dt_AddBill.Columns.Add("سعر الشراء", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية", GetType(Double))
            Dt_AddBill.Columns.Add("1الكمية", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية الباقية", GetType(Double))
            Dt_AddBill.Columns.Add("من مخزن", GetType(String))
            Dt_AddBill.Columns.Add("الى مخزن", GetType(String))
        End If

        'DTV_Width()

        Dt_AddBill.Rows.Add(Col_IDTM, Col_Prc, Col_Cats, Col_Name, Col_Unity, Col_TinPrice, Col_Quant, Col_qu_unity, Col_QuantRemainder, Col_StoreFrom, Col_StoreTo)
        Return Dt_AddBill
    End Function

    Private Sub ClearAdd()
        cmbUnity.Text = ""
        cmbcats.Text = ""
        cmbitmnm.Text = ""
        txtqunt.Text = ""
        txtquntUnity.Text = ""
        txtquntAll.Text = ""
        TxtPrc.Text = ""
        cmbUnity.Text = ""
        TinPrice = ""
    End Sub

    Private Sub btnDeleteEdit_Click(sender As Object, e As EventArgs) Handles btnDeleteEdit.Click
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        RNXD = DTGV.CurrentRow.Index
        DTGV.Rows.RemoveAt(RNXD)
    End Sub

    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Clear_All()
        MAXRECORD()
    End Sub

    Private Sub btnSaveAll_Click(sender As Object, e As EventArgs) Handles btnSaveAll.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateSave() = False Then Exit Sub

        ItemsTransferBill()

        If chkprint.Checked = True Then
            PrintReport()
        End If
        Bra.Fil("groups", "g_name", cmbcats)
        Cls.fill_combo_Branch("stores", "store", cmbStoresFrom)
        Dt_AddBill.Rows.Clear()
        Clear_All()
        MAXRECORD()
        MsgBox("تمت عملية التحويل بنجاح", MsgBoxStyle.Information)

        Cls.fill_combo("Items", "sname", cmbitmnm)
    End Sub

    Function ValidateSave() As Boolean

        If txtbill_no.Text = "" Then MsgBox("فضلا أدخل رقم أذن التحويل", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbStoresFrom.Text = "" Then MsgBox("من فضلك اختر المخزن المحول منه", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If cmbStoresTo.Text = "" Then MsgBox("من فضلك اختر المخزن المحول البه", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If DTGV.Rows.Count < 1 Then MsgBox("فضلا أكمل بيانات الفاتورة", MsgBoxStyle.Exclamation) : Return False

        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select count(*) from ManufacturingFillOrder where bill_no =N'" & txtbill_no.Text.Trim & "'" : H = cmd.ExecuteScalar
        'If H > 0 Then
        '    MsgBox("رقم أمر الشغل مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbill_no.Focus() : Return False
        'End If
        Return True
    End Function

    Private Sub ItemsTransferBill()

        Dim EMPID As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select EMPID from Employees where  NameEmployee = N'" & cmbEmployees.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then : EMPID = dr("EMPID") : Else EMPID = 0 : End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete From  Manufacturing_BilltINData where bill_no =N'" & txtbill_no.Text & "'" : cmd.ExecuteNonQuery()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete From  Manufacturing_BillsalData where bill_no =N'" & txtbill_no.Text & "'" : cmd.ExecuteNonQuery()


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update ManufacturingFillOrder set bill_date =N'" & Cls.C_date(dtpDateItem.Text) & "',itm_id_Manufacturing =N'" & txtitm_id_Manufacturing.Text & "',Stores_Manufacturing =N'" & cmbStoreManufacturing.Text & "',Stores_From =N'" & cmbStoresFrom.Text & "',Stores_TO =N'" & cmbStoresTo.Text & "',Total_qu =N'" & txtTotal_qu.Text & "',TotalCostPrice =N'" & txtCostPrice.Text & "',EMPID =N'" & EMPID & "',UserName =N'" & UserName & "' where bill_no =N'" & txtbill_no.Text & "'" : cmd.ExecuteNonQuery()

        For i As Integer = 0 To DTGV.Rows.Count - 1
            itm_id = DTGV.Rows(i).Cells(1).Value
            itm_cat = DTGV.Rows(i).Cells(2).Value
            itm_name = DTGV.Rows(i).Cells(3).Value
            Unity = DTGV.Rows(i).Cells(4).Value
            qunt = DTGV.Rows(i).Cells(6).Value
            qunt_unity = DTGV.Rows(i).Cells(7).Value
            quntTotal = DTGV.Rows(i).Cells(8).Value
            StoresFrom = DTGV.Rows(i).Cells(9).Value
            StoresTo = DTGV.Rows(i).Cells(10).Value

            'مشتريات تحويل
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Manufacturing_BilltINData (bill_no,itm_id,itm_Unity,qu,qu_unity,Stores,bill_date,UserName)  values("
            S = S & "N'" & txtbill_no.Text.Trim & "',N'" & itm_id & "',N'" & Unity & "',N'" & qunt & "',N'" & qunt_unity & "',N'" & StoresTo & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            'مبيعات تحويل
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Manufacturing_BillsalData (bill_no,itm_id,itm_Unity,qu,qu_unity,Stores,bill_date,UserName)  values("
            S = S & "N'" & txtbill_no.Text.Trim & "',N'" & itm_id & "',N'" & Unity & "',N'" & qunt & "',N'" & qunt_unity & "',N'" & StoresFrom & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            IM.Store(itm_id, StoresFrom)

            If ConnectOnlineStore = "YES" Then
                EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", itm_id)
                StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", itm_id)
                Cos.UpdateProductStock(StockOnline, itm_id, EditItmId)
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Manufacturing_BillsalData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbill_no.Text & "' and itm_id = N'" & itm_id & "' and Stores =N'" & StoresFrom & "'" : cmd.ExecuteNonQuery()

            IM.Store(itm_id, StoresTo)

            If ConnectOnlineStore = "YES" Then
                EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", itm_id)
                StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", itm_id)
                Cos.UpdateProductStock(StockOnline, itm_id, EditItmId)
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Manufacturing_BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbill_no.Text & "' and itm_id = N'" & itm_id & "' and Stores =N'" & StoresTo & "'" : cmd.ExecuteNonQuery()

        Next

    End Sub

    Private Sub GetDataSales()
        Bol = True
        QuntFrom = 0
1:
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id,group_name,Unity,store,TinPrice from items where sname=N'" & cmbitmnm.Text & "' and Stores=N'" & cmbStoresFrom.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                TxtPrc.Text = dr("itm_id").ToString()
                cmbcats.Text = dr("group_name").ToString()
                cmbUnity.Text = dr("Unity").ToString()
                QuntAll = dr("store").ToString()
                TinPrice = dr("TinPrice").ToString()
            End If
        Catch ex As Exception
            GoTo 1
        End Try

        '==========================================================

        For i As Integer = 0 To DTGV.Rows.Count - 1
            If DTGV.Rows(i).Cells(9).Value = cmbStoresFrom.Text Then
                If DTGV.Rows(i).Cells(1).Value = TxtPrc.Text Then
                    QuntFrom += DTGV.Rows(i).Cells(7).Value
                End If
            End If
        Next

        txtquntAll.Text = QuntAll - QuntFrom

        GetItemsUnity(cmbUnity, TxtPrc.Text)

        txtqunt.Text = 0
        txtquntUnity.Text = 0
        txtquntUnity.Focus()
        txtquntUnity.SelectAll()
        Bol = False

    End Sub

    Function ValidateTextAdd() As Boolean
        If txtbill_no.Text = "" Then MsgBox("فضلا أدخل رقم أذن التحويل", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbcats.Text = "" Then MsgBox("فضلا أدخل مجموعة الصنف", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbitmnm.Text = "" Then MsgBox("فضلا أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbitmnm.Focus() : Return False
        If cmbStoresFrom.Text = "" Then MsgBox("من فضلك اختر المخزن المحول منه", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If cmbStoresTo.Text = "" Then MsgBox("من فضلك اختر المخزن المحول البه", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If txtqunt.Text = "" Then MsgBox("فضلا أدخل الكمية ", MsgBoxStyle.Exclamation) : txtqunt.Focus() : Return False
        If TxtPrc.Text = "" Then MsgBox("فضلا أدخل باركود الصنف", MsgBoxStyle.Exclamation) : Return False
        If cmbItemsManufacturing.Text = "" Then MsgBox("فضلا أدخل أسم المنتج المصنع", MsgBoxStyle.Exclamation) : Return False

        Dim XMSG As String

        Dim Xstore As Double = IM.Get_Itm_Store(TxtPrc.Text, cmbStoresFrom.Text)
        If cmbStoresFrom.Text = cmbStoresTo.Text Then
            MsgBox("لايمكن التحويل من المخزن الى نفسة اختار مخزن اخر", MsgBoxStyle.Critical)
            Return False
        End If
        Dim IncreaseQuantity As String = mykey.GetValue("IncreaseQuantity", "NO")
        If IncreaseQuantity <> "YES" Then
            If Val(txtqunt.Text) > Xstore Then
                MsgBox("الكمية بالمخزن لا تكفي الكمية التى سيتم نقلها من المخزن", MsgBoxStyle.Critical)
                Return False
            End If
        End If


        If Xstore < 1 Then
            XMSG = MsgBox("الكمية بالمخزن قد نفذت أو أنك لم تقم بتسجيل آخر عملية مشتريات " & Environment.NewLine & MsgBoxStyle.MsgBoxRight + MsgBoxStyle.Exclamation)
            Exit Function
        End If

        If txtquntAll.Text < 0 Then
            MsgBox("لايمكن التحويل من المخزن لان الكمية لا تكفى لتحويل القيمة المسموح به", MsgBoxStyle.Critical)
            Return False
        End If

        Return True
    End Function

    Private Sub SumAllPrice()
        Dim SM As Double
        For i As Integer = 0 To DTGV.Rows.Count - 1
            SM = SM + DTGV.Rows(i).Cells(6).Value
        Next
        txtTotal_qu.Text = SM

        Dim SM1 As Double
        For i As Integer = 0 To DTGV.Rows.Count - 1
            SM1 += DTGV.Rows(i).Cells(5).Value * DTGV.Rows(i).Cells(6).Value
        Next
        txtCostPrice.Text = SM1

    End Sub

    Sub Clear_All()
        cmbitmnm.Text = ""
        TxtPrc.Text = ""
        txtqunt.Text = ""
        txtquntUnity.Text = ""
        txtquntAll.Text = ""
        cmbStoresFrom.Text = ""
        cmbStoresTo.Text = ""
        txtquntUnity.Text = ""
        cmbitmnm.Focus()
        DTGV.DataSource = ""
        cmbItemsManufacturing.Text = ""
        cmbCatsManufacturing.Text = ""
        cmbStoreManufacturing.Text = ""
        cmbEmployees.Text = ""
        cmbUnity.Text = ""
    End Sub

    Private Sub MAXRECORD()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from ManufacturingFillOrder"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Me.txtbill_no.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(bill_no As float)) as mb FROM ManufacturingFillOrder where bill_no <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            Me.txtbill_no.Text = sh + 1
        End If
    End Sub

    Private Sub PrintReport()
        Dim CostPrice As String = ""
        Dim Manufacturing_Allowance As String = ""
        Dim Filling_Allowance As String = ""
        Dim TotalCostPrice As String = ""
        Dim itm_id_Manufacturing As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from View_Product_Manufacturing where  SnameManufacturing = N'" & cmbItemsManufacturing.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            itm_id_Manufacturing = dr("itm_id_Manufacturing")
            CostPrice = dr("TinPrice")
            Manufacturing_Allowance = dr("Manufacturing_Allowance")
            Filling_Allowance = dr("Filling_Allowance")
        End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        For i As Integer = 0 To DTGV.Rows.Count - 1
            TotalCostPrice = Val(DTGV.Rows(i).Cells(5).Value) * Val(DTGV.Rows(i).Cells(6).Value)
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases(Company_Branch_ID,bill_no,bill_date,itm_id,itm_cat,itm_name,Unity,price,qu,totalpriceafterdisc,store,Stat,totalprice) values"
            S = S & " (N'" & Company_Branch_ID & "',N'" & txtbill_no.Text & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & DTGV.Rows(i).Cells(1).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(3).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(5).Value & "',N'" & DTGV.Rows(i).Cells(6).Value & "',N'" & TotalCostPrice & "',N'" & DTGV.Rows(i).Cells(9).Value & "',N'" & DTGV.Rows(i).Cells(10).Value & "',N'" & txtTotal_qu.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New Rpt_Product_Manufacturing
        Dim txtname, txtNameAr, txtNameEn, sname_Object, CostPrice_Object, Weightqunt_Object, Stores_Manufacturing_Object, TotalCostPrice_Object, Manufacturing_Allowance_Object, Filling_Allowance_Object, txtbill_no_Object, txtbill_no_ObjectText, bill_date_Object As TextObject

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "امر شغل تعبئة وتصنيع"
        txtNameAr = rpt.Section1.ReportObjects("txtTitelNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelNameEn")
        txtNameEn.Text = NameEnCompany
        txtbill_no_Object = rpt.Section1.ReportObjects("txtbill_no")
        txtbill_no_Object.Text = txtbill_no.Text
        txtbill_no_ObjectText = rpt.Section1.ReportObjects("txtbill_noText")
        txtbill_no_ObjectText.Text = "رقم أمر الشغل"
        sname_Object = rpt.Section1.ReportObjects("txtsname")
        sname_Object.Text = cmbItemsManufacturing.Text
        CostPrice_Object = rpt.Section1.ReportObjects("txtCostPrice")
        CostPrice_Object.Text = CostPrice
        Weightqunt_Object = rpt.Section1.ReportObjects("txtWeightqunt")
        Weightqunt_Object.Text = txtTotal_qu.Text
        Stores_Manufacturing_Object = rpt.Section1.ReportObjects("txtStores")
        Stores_Manufacturing_Object.Text = cmbStoreManufacturing.Text
        TotalCostPrice_Object = rpt.Section1.ReportObjects("txtTotalCostPrice")
        TotalCostPrice_Object.Text = txtCostPrice.Text
        Manufacturing_Allowance_Object = rpt.Section1.ReportObjects("txtManufacturing_Allowance")
        Manufacturing_Allowance_Object.Text = Manufacturing_Allowance
        Filling_Allowance_Object = rpt.Section1.ReportObjects("txtFilling_Allowance")
        Filling_Allowance_Object.Text = Filling_Allowance
        bill_date_Object = rpt.Section1.ReportObjects("txtbill_date")
        bill_date_Object.Text = dtpDateItem.Text
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "امر شغل تعبئة وتصنيع"
        Frm_PrintReports.Show()

    End Sub

    Private Sub dgv_Manufacturing_DoubleClick(sender As Object, e As EventArgs) Handles dgv_Manufacturing.DoubleClick
        If dgv_Manufacturing.RowCount = 0 Then Beep() : Exit Sub
        If (dgv_Manufacturing.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        txtbill_no.Text = dgv_Manufacturing.SelectedRows(0).Cells(1).Value

        PanelEdit.Top = 80
        PanelEdit.Dock = DockStyle.Fill

        Dt_AddBill.Rows.Clear()

        ActionRead = True

        Try


            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select itm_id,group_name,sname,itm_Unity,TinPrice,qu,qu_unity,totalprice,Stores,Stores_TO from View_ManufacturingFillOrderData where bill_no =N'" & txtbill_no.Text & "'  order by 1"
            dr = cmd.ExecuteReader
            Do While dr.Read
                DTGV.DataSource = Fn_AddBill(0, dr("itm_id").ToString, dr("group_name").ToString, dr("sname").ToString, dr("itm_Unity").ToString, Val(dr("TinPrice").ToString), Val(dr("qu")), Val(dr("qu_unity").ToString), Val(dr("totalprice").ToString), dr("Stores").ToString, dr("Stores_TO").ToString)
            Loop

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_ManufacturingFillOrder where bill_no =N'" & txtbill_no.Text & "'"
            dr = cmd.ExecuteReader
            If dr.Read Then
                cmbStoreManufacturing.Text = dr("Stores_Manufacturing").ToString
                dtpDateItem.Text = Cls.R_date(dr("bill_date").ToString())
                cmbCatsManufacturing.Text = dr("group_name").ToString
                cmbItemsManufacturing.Text = dr("sname").ToString
                txtitm_id_Manufacturing.Text = dr("itm_id_Manufacturing").ToString
                cmbStoresFrom.Text = dr("Stores_From").ToString
                cmbStoresTo.Text = dr("Stores_TO").ToString
                cmbEmployees.Text = dr("NameEmployee").ToString
                txtCostPrice.Text = dr("TotalCostPrice").ToString
                txtTotal_qu.Text = dr("Total_qu").ToString
            End If

            ActionRead = False

            SumAllPrice()
            DTGV.Columns(0).Visible = False
            DTGV.Columns(7).Visible = False
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub txtbill_no_TextChanged(sender As Object, e As EventArgs) Handles txtbill_no.TextChanged
        MyVars.CheckNumber(txtbill_no)
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub


#End Region



End Class
