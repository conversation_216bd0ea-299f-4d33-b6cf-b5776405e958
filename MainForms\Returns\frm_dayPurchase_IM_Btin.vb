﻿Imports CrystalDecisions.CrystalReports.Engine
Imports System.IO
Public Class frm_dayPurchase_IM_Btin
    Dim WithEvents BS As New BindingSource
    Private Sub frm_dayPurchase_IM_Btin_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Bra.Fil("groups", "g_name", CmbCats)
        txtsearsh.Text = " بحث ...." : txtsearsh.Font = New Font(txtsearsh.Font, FontStyle.Italic)

        FillData()
    End Sub

    Private Sub FillData()
        DataGridView1.DataSource = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT bill_no AS [رقم الفاتورة], bill_date as [التاريخ],itm_id as [الباركود],itm_cat as [المجموعة],itm_name as [الصنف],price as [السعر],qu_unity as [الكمية],totalprice as [اجمالى السعر ],Stores as [المخزن] FROM IM_Btin_Data where bill_no <> N'جرد'"
        If Cmbname.Text.Trim <> "" Then
            S = S & " and itm_name =N'" & Cmbname.Text.Trim & "'"
        End If
        If CmbCats.Text.Trim <> "" Then
            S = S & " and itm_cat  =N'" & CmbCats.Text.Trim & "'"
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " and Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم الفاتورة]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [التاريخ]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [الصنف]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

        DataGridView1.Columns(0).Width = 85
        DataGridView1.Columns(1).Width = 75
        DataGridView1.Columns(2).Width = 90
        DataGridView1.Columns(3).Width = 90
        DataGridView1.Columns(4).Width = 150
        DataGridView1.Columns(5).Width = 90
        DataGridView1.Columns(6).Width = 70
        DataGridView1.Columns(7).Width = 70

        Dim SM2 As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM2 = Val(DataGridView1.Rows(i).Cells(1).Value)
            SM2 = Cls.R_date(SM2)
            DataGridView1.Rows(i).Cells(1).Value = SM2
        Next

        Dim SM As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM = SM + DataGridView1.Rows(i).Cells(7).Value
        Next
        Totalprise.Text = SM


        Dim SM1 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM1 = SM1 + DataGridView1.Rows(i).Cells(6).Value
        Next
        TotalQunt.Text = SM1

    End Sub

    Private Sub Chk_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Chk.CheckedChanged
        If Chk.Checked = True Then
            CmbCats.Enabled = False
            Cmbname.Enabled = False
            CmbCats.Text = ""
            Cmbname.Text = ""
        Else
            CmbCats.Enabled = True
            Cmbname.Enabled = True
        End If
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False

        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub CmbCats_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CmbCats.SelectedIndexChanged
        If CmbCats.Text.Trim = "" Then Exit Sub
        Cmbname.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If PermtionName = "مدير" Then
            cmd.CommandText = "select distinct sname from Items where group_name =N'" & CmbCats.Text & "' order by 1"
        Else
            cmd.CommandText = "select distinct sname from Items where group_name =N'" & CmbCats.Text & "' and Company_Branch_ID =N'" & Company_Branch_ID & "' order by 1"
        End If
        dr = cmd.ExecuteReader
        Do While dr.Read
            Cmbname.Items.Add(Trim(dr(0)))
        Loop
        Cmbname.Text = ""
        Cmbname.SelectedIndex = -1
    End Sub

    Private Sub BtnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnShow.Click
        FillData()
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            S = "insert into PrintSalesPurchases(Company_Branch_ID,BILL_NO,bill_date,itm_id,itm_cat,itm_name,store,price,qu,totalprice,totalpriceafterdisc,totalpricebeforedisc)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView1.Rows(i).Cells(0).Value & "',N'" & DataGridView1.Rows(i).Cells(1).Value & "',N'" & DataGridView1.Rows(i).Cells(2).Value & "',N'" & DataGridView1.Rows(i).Cells(3).Value & "',N'" & DataGridView1.Rows(i).Cells(4).Value & "',N'" & DataGridView1.Rows(i).Cells(5).Value & "',N'" & DataGridView1.Rows(i).Cells(6).Value & "',N'" & DataGridView1.Rows(i).Cells(7).Value & "',N'" & DataGridView1.Rows(i).Cells(8).Value & "',N'" & TotalQunt.Text & "',N'" & Totalprise.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New Rpt_DaySales

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        Dim txtNameAr, txtNameEn, txtTitel As TextObject
        txtNameAr = rpt.Section1.ReportObjects("Text2")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("Text3")
        txtNameEn.Text = NameEnCompany
        txtTitel = rpt.Section1.ReportObjects("txtTitel")
        txtTitel.Text = "مرتجعات المشتريات اليومية"
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()
        Frm_PrintReports.Text = "تقرير بمرتجعات المشتريات اليومية"

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Sub ForPrintAll(ByVal BILL_NO As String, ByVal bill_date As String, ByVal itm_id As String, ByVal itm_cat As String, ByVal itm_name As String, ByVal stors As String, _
                    ByVal price As String, ByVal qu As String, ByVal totalprice As String, ByVal totalpriceafterdisc As String, ByVal totalpricebeforedisc As String)

        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "Sp_PrintDaySales"
        cmd.Parameters.Clear()

        cmd.Parameters.AddWithValue("@BILL_NO", BILL_NO)
        cmd.Parameters.AddWithValue("@bill_date", bill_date)
        cmd.Parameters.AddWithValue("@itm_id", itm_id)
        cmd.Parameters.AddWithValue("@itm_cat", itm_cat)
        cmd.Parameters.AddWithValue("@itm_name", itm_name)
        cmd.Parameters.AddWithValue("@store", stors)
        cmd.Parameters.AddWithValue("@price", price)
        cmd.Parameters.AddWithValue("@qu", qu)
        cmd.Parameters.AddWithValue("@totalprice", totalprice)
        cmd.Parameters.AddWithValue("@totalpriceafterdisc", totalpriceafterdisc)
        cmd.Parameters.AddWithValue("@totalpricebeforedisc", totalpricebeforedisc)

        cmd.ExecuteNonQuery()

    End Sub

    Private Sub txtsearsh_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles txtsearsh.MouseClick
        If txtsearsh.Text = " بحث ...." Then txtsearsh.SelectAll() : txtsearsh.Font = New Font(txtsearsh.Font, FontStyle.Regular)
    End Sub

    Private Sub txtsearsh_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtsearsh.TextChanged

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
            S = "SELECT bill_no as [رقم الفاتورة], bill_date as [التاريخ],itm_id as [الباركود],itm_cat as [المجموعة],itm_name as [الصنف],price as [السعر],qu_unity as [الكمية],totalprice as [اجمالى السعر ],Stores as [المخزن] FROM IM_Btin_Data where bill_no <> N''"
            If Cmbname.Text.Trim <> "" Then
                S = S & " and itm_name =N'" & Cmbname.Text.Trim & "'"
            End If
            If CmbCats.Text.Trim <> "" Then
                S = S & " and itm_cat  =N'" & CmbCats.Text.Trim & "'"
            End If
            If ChkWithoutDate.Checked = False Then
                S = S & " and bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
            End If
        Else
            Dim X As String = "%" & txtsearsh.Text.Trim & "%"
            S = "SELECT bill_no as [رقم الفاتورة], bill_date as [التاريخ],itm_id as [الباركود],itm_cat as [المجموعة],itm_name as [الصنف],price as [السعر],qu_unity as [الكمية],totalprice as [اجمالى السعر ],Stores as [المخزن] from IM_Btin_Data where  itm_name like N'" & X & "'"
            If Cmbname.Text.Trim <> "" Then
                S = "SELECT bill_no as [رقم الفاتورة], bill_date as [التاريخ],itm_id as [الباركود],itm_cat as [المجموعة],itm_name as [الصنف],price as [السعر],qu_unity as [الكمية],totalprice as [اجمالى السعر ],Stores as [المخزن] from IM_Btin_Data where  itm_name like N'" & X & "' and itm_name =N'" & Cmbname.Text.Trim & "'"
            End If
            If CmbCats.Text.Trim <> "" Then
                S = "SELECT bill_no as [رقم الفاتورة], bill_date as [التاريخ],itm_id as [الباركود],itm_cat as [المجموعة],itm_name as [الصنف],price as [السعر],qu_unity as [الكمية],totalprice as [اجمالى السعر ],Stores as [المخزن] from IM_Btin_Data where  itm_name like N'" & X & "' and itm_cat  =N'" & CmbCats.Text.Trim & "'"
            End If
            If ChkWithoutDate.Checked = False Then
                S = "SELECT bill_no as [رقم الفاتورة], bill_date as [التاريخ],itm_id as [الباركود],itm_cat as [المجموعة],itm_name as [الصنف],price as [السعر],qu_unity as [الكمية],totalprice as [اجمالى السعر ],Stores as [المخزن] from IM_Btin_Data where  itm_name like N'" & X & "' and bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
            End If
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
        DataGridView1.Columns(0).Width = 85
        DataGridView1.Columns(1).Width = 75
        DataGridView1.Columns(2).Width = 90
        DataGridView1.Columns(3).Width = 90
        DataGridView1.Columns(4).Width = 150
        DataGridView1.Columns(5).Width = 90
        DataGridView1.Columns(6).Width = 70
        DataGridView1.Columns(7).Width = 70

        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(1).Value)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(1).Value = SM
        Next

    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class