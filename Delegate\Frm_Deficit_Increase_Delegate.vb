﻿Public Class Frm_Deficit_Increase_Delegate


    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If ValidateTextAdd() = False Then Exit Sub

        Custmersfinance()

        cmbEmployees.SelectedIndex = -1

        txtReceipt_Number.Text = ""
        txtAmount.Text = ""
        cmbEmployees.Text = ""
        MAXRECORD()
        MsgBox("تم إجراء العملية بنجاح", MsgBoxStyle.Information)
        cmbEmployees.Focus()
        cmbEmployees.SelectAll()

    End Sub
    Function ValidateTextAdd() As Boolean

        If Trim(cmbEmployees.Text) = "" Then
            MsgBox("فضلاً أختر أسم المندوب", MsgBoxStyle.Exclamation)
            cmbEmployees.Focus()
            Exit Function
        End If

        If Val(txtAmount.Text) = 0 Then
            MsgBox("فضلاً أدخل المبلغ", MsgBoxStyle.Exclamation)
            txtAmount.Focus()
            Return False
        End If

        If Trim(txtReceipt_Number.Text) = "" Then
            MsgBox("فضلاً أدخل رقم الإيصال", MsgBoxStyle.Exclamation)
            txtReceipt_Number.Focus()
            Return False
        End If
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * From Employee_Deficit_Increase where Receipt_Number =N'" & txtReceipt_Number.Text & "'"
        dr = cmd.ExecuteReader()
        If dr.HasRows = True Then
            MsgBox("عفواً يوجد رقم ايصال مسجل مسبقا بنفس الرقم", MsgBoxStyle.Exclamation)
            Return False : Exit Function
        Else
        End If

        Return True
    End Function

    Sub Custmersfinance()
        Dim Employees_Code As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select EMPID from Employees where NameEmployee=N'" & cmbEmployees.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Employees_Code = dr("EMPID")
        End If

        Dim Deficit_Increase_Code As Integer
        If rdoDeficit.Checked = True Then
            Deficit_Increase_Code = 1
        End If
        If rdoIncrease.Checked = True Then
            Deficit_Increase_Code = 2
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Employee_Deficit_Increase (Company_Branch_ID,Receipt_Number,Employee_Code,Employee_Name,Deficit_Increase_Code,Amount,Emp_Time,Emp_Date,Notes,UserName,Treasury_Code) values"
        S = S & " (N'" & Company_Branch_ID & "',N'" & txtReceipt_Number.Text & "'," & Employees_Code & ",N'" & cmbEmployees.Text & "'," & Deficit_Increase_Code & "," & txtAmount.Text.Trim & ",N'" & Cls.get_time(True) & "',N'" & Cls.C_date(dtpEmp_Date.Text) & "',N'" & txtNotes.Text.Trim & "',N'" & UserName & "',N'" & Treasury_Code & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

    End Sub


    Private Sub MAXRECORD()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Employee_Deficit_Increase"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Me.txtReceipt_Number.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(Receipt_Number As float)) as mb FROM Employee_Deficit_Increase"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            Me.txtReceipt_Number.Text = sh + 1
        End If

    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        GetData()
    End Sub

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select dbo.Employee_Deficit_Increase.id, dbo.Employee_Deficit_Increase.Receipt_Number As [رقم الايصال], dbo.Employee_Deficit_Increase.Employee_Name As [اسم المندوب], dbo.Employee_Deficit_Increase.Amount As المبلغ,   dbo.Employee_Deficit_Increase.Emp_Date As [التاريخ], dbo.Employee_Type_Deficit_Increase.Type_Deficit_Increase As الحالة, dbo.Employee_Deficit_Increase.Notes As ملاحظات ,dbo.Employee_Deficit_Increase.Company_Branch_ID From dbo.Employee_Deficit_Increase INNER Join dbo.Employee_Type_Deficit_Increase On dbo.Employee_Deficit_Increase.Deficit_Increase_Code = dbo.Employee_Type_Deficit_Increase.Deficit_Increase_Code Where (dbo.Employee_Deficit_Increase.id <> N'')"
        If chkAllEmp.Checked = False Then
            S = S & " and dbo.Employee_Deficit_Increase.Employee_Name = N'" & cmbEmployeesView.Text.Trim & "'"
        End If
        If chkDeficit_Increase.Checked = False Then
            If rdoDeficitView.Checked = True Then
                S = S & " and dbo.Employee_Type_Deficit_Increase.Type_Deficit_Increase = N'" & rdoDeficitView.Text.Trim & "'"
            End If
            If rdoIncreaseView.Checked = True Then
                S = S & " and dbo.Employee_Type_Deficit_Increase.Type_Deficit_Increase = N'" & rdoIncreaseView.Text.Trim & "'"
            End If
        End If
        If chkAllDate.Checked = False Then
            S = S & " and dbo.Employee_Deficit_Increase.Emp_Date >=N'" & Cls.C_date(Dtp_from.Text) & "' and dbo.Employee_Deficit_Increase.Emp_Date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " and dbo.Employee_Deficit_Increase.Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم الايصال]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [التاريخ]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [اسم المندوب]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)
        DTGV.Columns(0).Visible = False
        DTGV.Columns(7).Visible = False
    End Sub

    Private Sub Frm_Deficit_Increase_Delegate_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployees)
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployeesView)
        MAXRECORD()
        PanelAddNew.Top = 5000
    End Sub

    Private Sub cmbEmployees_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbEmployees.KeyUp
        If e.KeyCode = 13 Then
            txtAmount.Focus()
        End If
    End Sub

    Private Sub txtAmount_KeyUp(sender As Object, e As KeyEventArgs) Handles txtAmount.KeyUp
        If e.KeyCode = 13 Then
            txtNotes.Focus()
        End If
    End Sub

    Private Sub txtNotes_KeyUp(sender As Object, e As KeyEventArgs) Handles txtNotes.KeyUp
        If e.KeyCode = 13 Then
            Button1.PerformClick()
        End If
    End Sub

    Private Sub btnAdd_Click(sender As Object, e As EventArgs) Handles btnAdd.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        PanelAddNew.Top = 20
        PanelAddNew.Dock = DockStyle.Fill
        MAXRECORD()
        cmbEmployees.Focus()
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        PanelAddNew.Dock = DockStyle.None
        PanelAddNew.Top = 5000
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DTGV.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.SelectedRows.Count - 1
            If DTGV.RowCount = 0 Then Beep() : Exit Sub
            If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub

            Dim Receipt_Number As String = DTGV.SelectedRows(i).Cells(1).Value

            cmd.CommandText = "delete from Employee_Deficit_Increase where Receipt_Number =N'" & Receipt_Number & "'" : cmd.ExecuteNonQuery()
        Next

        GetData()

    End Sub

    Private Sub chkAllEmp_CheckedChanged(sender As Object, e As EventArgs) Handles chkAllEmp.CheckedChanged
        If chkAllEmp.Checked = True Then
            cmbEmployeesView.Enabled = False
        Else
            cmbEmployeesView.Enabled = True
        End If
    End Sub

    Private Sub chkDeficit_Increase_CheckedChanged(sender As Object, e As EventArgs) Handles chkDeficit_Increase.CheckedChanged
        If chkDeficit_Increase.Checked = True Then
            rdoDeficitView.Enabled = False
            rdoIncreaseView.Enabled = False
        Else
            rdoDeficitView.Enabled = True
            rdoIncreaseView.Enabled = True
        End If
    End Sub

    Private Sub chkAllDate_CheckedChanged(sender As Object, e As EventArgs) Handles chkAllDate.CheckedChanged
        If chkAllDate.Checked = True Then
            Dtp_from.Enabled = False
            Dtp_To.Enabled = False
        Else
            Dtp_from.Enabled = True
            Dtp_To.Enabled = True
        End If
    End Sub

    Private Sub txtReceipt_Number_TextChanged(sender As Object, e As EventArgs) Handles txtReceipt_Number.TextChanged
        MyVars.CheckNumber(txtReceipt_Number)
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class
