﻿Imports System.Data.SqlClient
Public Class FrenchText

    '=========================================== Manu ====================================================
    Friend Items As String = "Articles"
    Friend Purchases As String = "Achats"
    Friend Sales As String = "Ventes"
    Friend Returns As String = "Retours"
    Friend Expenses As String = "Dépenses"
    Friend Employees As String = "Employés"
    Friend Delegates As String = "Délégué"
    Friend Maintenance As String = "Maintenance"
    Friend Manufacturing As String = "Fabrication"
    Friend Banks As String = "Banques"
    Friend Accounts As String = "Comptes"
    Friend FinancialReports As String = "Rapports Financiers"
    Friend StoresReports As String = "Rapports de Stocks"
    Friend ItemsReports As String = "Rapports des Articles"
    Friend ReportsCertainPeriod As String = "Rapports Par Période"
    Friend UserReports As String = "Rapports Utilisateurs"
    Friend Tools As String = "Outils"

    '=========================================== Items ====================================================
    Friend RegisterGroupsItems As String = "Enregistrer les Groupes d'Articles"
    Friend RegisterBranchesItems As String = "Enregistrer les Branches d'Articles"
    Friend RegisterItems As String = "Enregistrer les Articles"
    Friend ModifiedItems As String = "Articles Modifiés"
    Friend SearchItems As String = "Rechercher des Articles"
    Friend StoreRegistration As String = "Enregistrement du Magasin"
    Friend ConvertBetweenStores As String = "Transfert Entre Magasins"
    Friend MaxAndMinPriceItems As String = "Prix Maximum et Minimum des Articles"
    Friend StoreAdjustments As String = "Ajustements de Stock"
    Friend OffersDiscounts As String = "Offres et Remises"
    Friend PriceProposal As String = "Proposition de Prix"
    Friend DelegatesManagement As String = "Gestion des Délégués"
    Friend WithdrawalsAndDepositsOfPartners As String = "Retraits et Dépôts des Partenaires"
    Friend OtherIncome As String = "Autres Revenus"
    Friend Treasury As String = "Trésorerie"
    Friend Exits As String = "Sortie"

    Friend ProductManufacturing As String = "Fabrication de Produits"
    Friend AmountDisabilityAndIncreaseDelegates As String = "Montant Incapacité et Augmentation des Délégués"

    '=========================================== Purchases ====================================================
    Friend SupplierDataRegistration As String = "Enregistrement des Données Fournisseurs"
    Friend PurchasesProcess As String = "Processus d'Achat"
    Friend ModifyAndDeletePurchases As String = "Modifier et Supprimer les Achats"
    Friend ViewAndPrintPurchases As String = "Voir et Imprimer les Achats"
    Friend ReportDailyPurchases As String = "Rapport Quotidien des Achats"
    Friend SupplierPayments As String = "Paiements Fournisseurs"
    Friend DeleteSupplierPayments As String = "Supprimer les Paiements Fournisseurs"
    Friend SupplierDiscounts As String = "Remises Fournisseurs"
    Friend DeleteSupplierDiscounts As String = "Supprimer les Remises Fournisseurs"

    '=========================================== Sales ====================================================
    Friend SalesScreen As String = "Écran de Vente"
    Friend FastSales As String = "Vente Rapide"
    Friend Sheft As String = "Équipe"
    Friend DeleteAndModifySales As String = "Supprimer et Modifier les Ventes"
    Friend ViewAndPrintSales As String = "Voir et Imprimer les Ventes"
    Friend DailySalesReport As String = "Rapport Quotidien des Ventes"
    Friend DetailedSalesReport As String = "Rapport Détaillé des Ventes"
    Friend SalesPricesForTheCustomer As String = "Prix de Vente pour le Client"
    Friend CustomerData As String = "Données Client"
    Friend CustomerPayments As String = "Paiements Clients"
    Friend ChecksPayable As String = "Chèques à Payer"
    Friend DeleteCustomerPayments As String = "Supprimer les Paiements Clients"
    Friend CustomerDiscounts As String = "Remises Clients"
    Friend DeleteCustomerDiscounts As String = "Supprimer les Remises Clients"
    Friend OtherDiscounts As String = "Autres Remises"

    '=============== Returns ==============================
    Friend RecordPurchaseReturns As String = "Enregistrer les Retours d'Achats"
    Friend DeleteAndModifyPurchaseReturns As String = "Supprimer et Modifier les Retours d'Achats"
    Friend DefineAndPrintPurchaseReturns As String = "Définir et Imprimer les Retours d'Achats"
    Friend DailyPurchaseReturnsReport As String = "Rapport Quotidien des Retours d'Achats"
    Friend RecordSalesReturns As String = "Enregistrer les Retours de Vente"
    Friend DeleteAndModifySalesReturns As String = "Supprimer et Modifier les Retours de Vente"
    Friend DefineAndPrintSalesReturns As String = "Définir et Imprimer les Retours de Vente"
    Friend DailySalesReturnsReport As String = "Rapport Quotidien des Retours de Vente"
    Friend RecordDamages As String = "Enregistrer les Dommages"
    Friend DefineAndDeleteDamageReturns As String = "Définir et Supprimer les Retours de Dommages"

    '=========================================== Store ====================================================
    Friend RegisterStores As String = "Enregistrer les Magasins"
    Friend Store As String = "Magasin"
    Friend Stores As String = "Magasins"
    Friend StoreType As String = "Type de Magasin"
    Friend Main As String = "Principal"
    Friend Subs As String = "Secondaire"
    Friend News As String = "Nouveau"
    Friend Save As String = "Enregistrer"
    Friend Edit As String = "Modifier"
    Friend Delete As String = "Supprimer"
    Friend Added As String = "Ajouter"
    Friend PleaseEnterValidData As String = "Veuillez Entrer des Données Valides"
    Friend SorryRecordedStatementBy As String = "Désolé, déclaration enregistrée par"
    Friend SuccessfullySaved As String = "Enregistré avec Succès"
    Friend PleaseSelectValidStatement As String = "Veuillez Sélectionner une Déclaration Valide"
    '=========================================== ADD ====================================================
    Friend HideInSearch As String = "Masquer dans la Recherche"
    Friend HideTheGroup As String = "Masquer le Groupe"
    Friend ChooseGroupPhoto As String = "Choisir la Photo du Groupe"
    Friend GroupName As String = "Nom du Groupe"

    '===============================================================================================

    ' Main Headers
    Friend ItemDataInEnglish As String = "Données de l'Article"
    Friend TodayInEnglish As String = "Aujourd'hui"
    Friend DateInEnglish As String = "Date"
    Friend TimeInEnglish As String = "Heure"
    Friend ShfitInEnglish As String = "Équipe"

    ' Buttons and Actions
    Friend DisplayAndPrintSalesInEnglish As String = "Afficher et Imprimer les Ventes"
    Friend AddDeleteSalesInEnglish As String = "Ajouter et Supprimer les Ventes"
    Friend RegisterCustomerDataInEnglish As String = "Enregistrer les Données Client"
    Friend SalesReturnsInEnglish As String = "Retours de Vente"
    Friend ApplyInvoiceInEnglish As String = "Appliquer la Facture"
    Friend PrintInvoiceInEnglish As String = "Imprimer la Facture"
    Friend SelectPrinterInEnglish As String = "Sélectionner l'Imprimante"
    Friend DirectPrintInEnglish As String = "Impression Directe"
    Friend PrintPreviewInEnglish As String = "Aperçu avant Impression"

    ' Sales Form Fields
    Friend BarcodeInEnglish As String = "Code-barres"
    Friend CustomerInEnglish As String = "Client"
    Friend WarehouseInEnglish As String = "Entrepôt"
    Friend TreasuryInEnglish As String = "Trésorerie"
    Friend RetailPriceInEnglish As String = "Prix de Détail"
    Friend WholesalePriceInEnglish As String = "Prix de Gros"
    Friend BulkPriceInEnglish As String = "Prix en Vrac"
    Friend CashInEnglish As String = "Espèces"
    Friend CreditInEnglish As String = "Crédit"
    Friend BankInEnglish As String = "Banque"
    Friend WalletInEnglish As String = "Portefeuille"
    Friend InvoiceNumberInEnglish As String = "Numéro de Facture"
    Friend EnterSalesInEnglish As String = "Saisir les Ventes"
    Friend AnyWarehouseInEnglish As String = "N'importe quel Entrepôt"

    ' Table Headers
    Friend GroupInEnglish As String = "Groupe"
    Friend ItemNameInEnglish As String = "Nom de l'Article"
    Friend DiscountInEnglish As String = "Remise"
    Friend PriceInEnglish As String = "Prix"
    Friend QuantityInEnglish As String = "Quantité"
    Friend UnityInEnglish As String = "Unité"
    Friend TotalInEnglish As String = "Total"
    Friend CurrentQuantityInEnglish As String = "Quantité Actuelle"
    Friend ExpiryDateInEnglish As String = "Date d'Expiration"

    ' Search Options
    Friend SearchByNameInEnglish As String = "Rechercher par Nom"
    Friend SearchByPhoneInEnglish As String = "Rechercher par Téléphone"
    Friend SearchByMobileInEnglish As String = "Rechercher par Mobile"
    Friend SearchByCustomerCodeInEnglish As String = "Rechercher par Code Client"
    Friend SearchInItemsInEnglish As String = "Rechercher dans les Articles"

    ' Delivery Options
    Friend DeliveryOrdersInEnglish As String = "Commandes en Livraison"
    Friend CustomerAddressInEnglish As String = "Adresse du Client"
    Friend DeliveryServiceInEnglish As String = "Service de Livraison"
    Friend DeliveryPersonInEnglish As String = "Livreur"
    Friend DeliveryPercentageInEnglish As String = "Pourcentage du Livreur"
    Friend DriverAmountInEnglish As String = "Montant du Livreur"

    ' Invoice Actions
    Friend AddOrderInEnglish As String = "Ajouter une Commande"
    Friend SaveInvoiceInEnglish As String = "Enregistrer la Facture"
    Friend DirectInvoiceSaveInEnglish As String = "Enregistrement Direct de la Facture"
    Friend PrintedInvoiceInEnglish As String = "Facture Imprimée"
    Friend CustomInvoiceInEnglish As String = "Facture Personnalisée"

    ' Summary Fields
    Friend ItemCountInEnglish As String = "Nombre d'Articles"
    Friend ItemQuantitiesInEnglish As String = "Quantités d'Articles"
    Friend ExpensesInEnglish As String = "Dépenses"
    Friend CustomerAccountInEnglish As String = "Compte Client"
    Friend MobileInEnglish As String = "Mobile"
    Friend RepresentativeInEnglish As String = "Représentant"
    Friend NotesInEnglish As String = "Notes"

    ' Invoice Details
    Friend BeforeDiscountInEnglish As String = "Avant Remise"
    Friend AfterDiscountInEnglish As String = "Après Remise"
    Friend RemainingInEnglish As String = "Restant"
    Friend PercentageInEnglish As String = "Pourcentage"
    Friend RateInEnglish As String = "Taux"
    Friend ValueInEnglish As String = "Valeur"
    Friend TaxValueInEnglish As String = "Valeur Taxe"
    Friend VATDiscountInEnglish As String = "Remise TVA"
    Friend PaidInEnglish As String = "Payé"
    Friend AllWarehousesInEnglish As String = "Tous les Entrepôts"
    Friend ItemDiscountInEnglish As String = "Remise sur Article"
    Friend NumberItemsInEnglish As String = "Nombre d'Articles"
    Friend QuantitiesItemsInEnglish As String = "Quantités d'Articles"
    Friend DischargeInEnglish As String = "Décharge"

    '===============================================================================================
    Friend PerformingPurchasingOperationsInEnglish As String = "Réalisation des Opérations d'Achat"
    Friend PaidInCashInEnglish As String = "Payé en Espèces"
    Friend VendorInEnglish As String = "Fournisseur"
    Friend SubCategoryInEnglish As String = "Sous-catégorie"
    Friend PurchasePriceInEnglish As String = "Prix d'Achat"
    Friend SellingPriceInEnglish As String = "Prix de Vente"
    Friend ReorderLimitInEnglish As String = "Seuil de Réapprovisionnement"
    Friend CurrencyInEnglish As String = "Devise"
    Friend PurchaseEntryInEnglish As String = "Entrée d'Achat"
    Friend ReceivingVoucherInEnglish As String = "Bon de Réception"
    Friend InvoiceImageInEnglish As String = "Image de la Facture"
    Friend TotalItemsCountInEnglish As String = "Nombre Total d'Articles"
    Friend SupplierAccountInEnglish As String = "Compte Fournisseur"
    Friend PaperTypeInEnglish As String = "Type de Papier"
    Friend InvoiceDateInEnglish As String = "Date de la Facture"
    Friend ImportFromExcelInEnglish As String = "Importer depuis Excel"
    '===============================================================================================

    Friend ItemCategoryInEnglish As String = "Catégorie d'Article"
    Friend ItemSubcategoryInEnglish As String = "Sous-catégorie d'Article"
    Friend ProductionDateInEnglish As String = "Date de Production"
    Friend ExpiryTypeInEnglish As String = "Type d'Expiration"
    Friend PriceIncludesVATInEnglish As String = "Prix TTC"
    Friend ShowInSearchInEnglish As String = "Afficher dans la Recherche"
    Friend AutoBarcodeInEnglish As String = "Code-barres Automatique"
    Friend AddItemToAllStoresInEnglish As String = "Ajouter l'Article à Tous les Magasins"
    Friend VATInEnglish As String = "TVA"
    Friend MinPriceLimitInEnglish As String = "Limite de Prix Minimum"
    Friend PrintBarcodeInEnglish As String = "Imprimer le Code-barres"
    Friend NumberInEnglish As String = "Numéro"
    Friend UnitSizeInEnglish As String = "Taille de l'Unité"
    Friend UnitBarcodeInEnglish As String = "Code-barres de l'Unité"
    Friend ItemImageInEnglish As String = "Image de l'Article"
    Friend BarcodeScaleInEnglish As String = "Échelle du Code-barres"
    Friend InventoryInEnglish As String = "Inventaire"

End Class
