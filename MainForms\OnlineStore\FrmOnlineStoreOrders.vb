﻿Imports vb = Microsoft.VisualBasic
Imports System.Data.OleDb
Imports CrystalDecisions.CrystalReports.Engine

Public Class FrmOnlineStoreOrders
    Dim ListBoxSelectedIndex As Integer
    Dim WithEvents BS As New BindingSource
    Dim itm_name As String = ""
    Dim ActivUpdate As Boolean = False
    Dim DiscountsValue As Double = 0
    Dim TotalDiscountsValue As Double = 0
    Dim StateDisc As String = ""
    Dim AlaertParcode As Boolean
    Dim CustomersCode As String

    Private Sub FrmItemsNew_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyBase.KeyUp
    End Sub
    Private Sub FrmItemsNew_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Cos.Fill_ComboBox_OnlineStore("[User]", "UserName", cmbCustomersView)
    End Sub

    Private Sub btnShow_Click(sender As Object, e As EventArgs) Handles btnShow.Click

        CheckOnlineStoreData()

        If Not ConnectingOnlineStore() Is Nothing Then
            DataGridView1.DataSource = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "Select dbo.[Order].Id, dbo.[Order].OrderNo, dbo.[User].ShopName, dbo.[Order].TotalValueDiscountOrder, dbo.[Order].TotalPriceOrder, dbo.OrderItem.ShippindAddress, dbo.OrderItem.Comment, dbo.OrderItem.PhoneNumber,     dbo.[Order].CreatedAt, dbo.OrderItem.OrderStatusId, dbo.[User].UserName From dbo.[User] INNER Join dbo.[Order] ON dbo.[User].Id = dbo.[Order].UserId LEFT OUTER Join  dbo.OrderItem ON dbo.[Order].Id = dbo.OrderItem.OrderId Group By dbo.[Order].OrderNo, dbo.[Order].TotalValueDiscountOrder, dbo.[Order].TotalPriceOrder, dbo.OrderItem.ShippindAddress, dbo.OrderItem.Comment, dbo.OrderItem.PhoneNumber, dbo.[Order].Id,    dbo.OrderItem.OrderStatusId, dbo.[Order].CreatedAt, dbo.[User].ShopName, dbo.[User].UserName HAVING (dbo.OrderItem.OrderStatusId = 2)"
            If chkAll.Checked = False Then
                If txtOrderNo.Text <> "" Then
                    S = S & " AND  (dbo.[Order].OrderNo =N'" & txtOrderNo.Text.Trim & "')"
                End If
                If cmbCustomersView.Text <> "" Then
                    S = S & " AND  (dbo.[User].UserName =N'" & cmbCustomersView.Text.Trim & "')"
                End If
            End If
            If ChkWithoutDate.Checked = False Then
                S = S & " AND  dbo.[Order].CreatedAt >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and dbo.[Order].CreatedAt <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
            End If

            S = S & " ORDER BY dbo.[Order].OrderNo"

            cmd.CommandText = S : dr = cmd.ExecuteReader
            DataGridView1.DataSource = Cls.PopulateDataView(dr)
            DataGridView1.Columns(4).Width = 90
            DataGridView1.Columns(5).Width = 120

            DataGridView1.Columns(1).HeaderText = "رقم الطلب"
            DataGridView1.Columns(2).HeaderText = "اسم المحل"
            DataGridView1.Columns(3).HeaderText = "اجمالى الخصومات"
            DataGridView1.Columns(4).HeaderText = "اجمالى الطلب"
            DataGridView1.Columns(5).HeaderText = "عنوان الشحن"
            DataGridView1.Columns(6).HeaderText = "ملاحظات الطلب"
            DataGridView1.Columns(7).HeaderText = "الموبايل"
            DataGridView1.Columns(8).HeaderText = "التاريخ"

            DataGridView1.Columns(0).Visible = False
            DataGridView1.Columns(9).Visible = False

            Dim SM As String
            For i As Integer = 0 To DataGridView1.RowCount - 1
                SM = Val(DataGridView1.Rows(i).Cells(8).Value.ToString)
                SM = Cls.R_date(SM)
                DataGridView1.Rows(i).Cells(8).Value = SM
            Next
            Cn.Close()
            connect()
            ActivUpdate = True
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbCustomersView.Enabled = False
            txtOrderNo.Enabled = False
        Else
            cmbCustomersView.Enabled = True
            txtOrderNo.Enabled = True
        End If
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(sender As Object, e As EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub DataGridView1_DoubleClick(sender As Object, e As EventArgs) Handles DataGridView1.DoubleClick
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim OrderNumber As String = DataGridView1.SelectedRows(0).Cells(1).Value
        CustomersName = DataGridView1.SelectedRows(0).Cells(2).Value
        TotalValueDiscountOrder = DataGridView1.SelectedRows(0).Cells(3).Value

        OrdersOnlineStore = True
        OrdersOnlineStoreAdd = True
        OrdersOnlineStoreNumber = OrderNumber



        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select count(*) from Customers where Vendorname =N'" & CustomersName & "'" : H = cmd.ExecuteScalar
        'If H = 0 Then
        '    MAXRECORDAutoALL("customers", "Cust_Code")
        '    Cls.insert("customers", "Company_Branch_ID,Cust_Code,Vendorname,vintinval,vndiscount,VnPay,vnamntdebit,vnamntcredit,UserName", "N'" & Company_Branch_ID & "',N'" & CustomersCode & "',N'" & CustomersName & "',0,0,0,0,0,N'" & UserName & "'")
        'End If

        Dim newForm As New List(Of FrmSales)
        newForm.Add(New FrmSales)
        newForm(0).Show()

    End Sub

    Private Sub MAXRECORDAutoALL(ByVal Tabel As String, ByVal Feild As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from " + Tabel + ""
            dr = cmd.ExecuteReader

            Dim dt As New DataTable
            dt.Load(dr)

            If dt.Rows.Count = 0 Then
                CustomersCode = 1
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(CAST(" + Feild + " As float)) as mb FROM " + Tabel + " where " + Feild + " <> 0"
                dr = cmd.ExecuteReader
                dr.Read()

                Dim sh As Long
                sh = dr("mb")
                CustomersCode = sh + 1
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub CheckOnlineStoreData()

        If ConnectOnlineStore = "YES" Then
            If CheckForInternetConnection() = False Then
                MsgBox("تأكد من الاتصال بالانترنت", MsgBoxStyle.Information)
                Exit Sub
            End If
            Dim aray_OrderNo As New ArrayList
            Dim aray_TotalValueDiscountOrder As New ArrayList
            Dim aray_Id As New ArrayList
            Dim aray_ProductId As New ArrayList


            Dim OrderNo As String
            Dim TotalValueDiscountOrder As String = ""
            Dim ProductId As String = ""
            Dim Id As String = ""
            Dim DiscountedPrice As String = ""
            Dim DiscountedPrice2 As String = ""
            Dim DiscountedPrice3 As String = ""
            Dim Price As String = ""
            Dim Price2 As String = ""
            Dim Price3 As String = ""

            If Not ConnectingOnlineStore() Is Nothing Then

                aray_Id.Clear() : aray_ProductId.Clear()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select Id,ProductId  From dbo.[OrderItem] Where (DiscountedPrice <> 0) OR (DiscountedPriceOrder <> 0) OR (TotalValueDiscountOrder <> 0)"
                dr = cmd.ExecuteReader
                Do While dr.Read = True
                    aray_Id.Add(dr(0))
                    aray_ProductId.Add(dr(1))
                Loop

                For i As Integer = 0 To aray_Id.Count - 1
                    Id = aray_Id(i).ToString()
                    ProductId = aray_ProductId(i).ToString()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select DiscountedPrice,DiscountedPrice2,DiscountedPrice3,Price,Price2,Price3 From dbo.[Product] Where (Id =N'" & ProductId & "')"
                    dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then
                        DiscountedPrice = dr(0).ToString
                        DiscountedPrice2 = dr(1).ToString
                        DiscountedPrice3 = dr(2).ToString
                        Price = dr(3).ToString
                        Price2 = dr(4).ToString
                        Price3 = dr(5).ToString
                    End If

                    If Val(DiscountedPrice) = 0 Or Val(DiscountedPrice2) = 0 Or Val(DiscountedPrice3) = 0 Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update [OrderItem] set DiscountedPrice =N'" & Val(0.00) & "',DiscountedPriceOrder =N'" & Val(0.00) & "',TotalValueDiscountOrder =N'" & Val(0.00) & "' where ProductId =N'" & ProductId & "'" : cmd.ExecuteNonQuery()
                    End If

                    If DiscountedPrice = "" Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update [Product] set DiscountedPrice =N'" & Val(0.00) & "' where Id =N'" & ProductId & "'" : cmd.ExecuteNonQuery()
                    End If
                    If DiscountedPrice2 = "" Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update [Product] set DiscountedPrice2 =N'" & Val(0.00) & "' where Id =N'" & ProductId & "'" : cmd.ExecuteNonQuery()
                    End If
                    If DiscountedPrice3 = "" Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update [Product] set DiscountedPrice3 =N'" & Val(0.00) & "' where Id =N'" & ProductId & "'" : cmd.ExecuteNonQuery()
                    End If

                    If Price = "" Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update [Product] set Price =N'" & Val(0.00) & "' where Id =N'" & ProductId & "'" : cmd.ExecuteNonQuery()
                    End If
                    If Price2 = "" Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update [Product] set Price2 =N'" & Val(0.00) & "' where Id =N'" & ProductId & "'" : cmd.ExecuteNonQuery()
                    End If
                    If Price3 = "" Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update [Product] set Price3 =N'" & Val(0.00) & "' where Id =N'" & ProductId & "'" : cmd.ExecuteNonQuery()
                    End If

                Next


                '===========================================================================================
                aray_OrderNo.Clear() : aray_TotalValueDiscountOrder.Clear()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select Id,OrderNo,TotalValueDiscountOrder  From dbo.[Order] Where (TotalValueDiscountOrder <> 0)"
                dr = cmd.ExecuteReader
                Do While dr.Read = True
                    aray_OrderNo.Add(dr(1))
                    aray_TotalValueDiscountOrder.Add(dr(2))
                Loop

                For i As Integer = 0 To aray_OrderNo.Count - 1
                    OrderNo = aray_OrderNo(i).ToString()
                    TotalValueDiscountOrder = 0

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "Select DiscountedPrice  From dbo.[OrderItem] Where (OrderNo =N'" & OrderNo & "')"
                    dr = cmd.ExecuteReader
                    Do While dr.Read = True
                        TotalValueDiscountOrder += Val(dr(0).ToString)
                    Loop
                    If TotalValueDiscountOrder = 0 Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update [Order] set TotalValueDiscountOrder =N'" & Val(0.00) & "' where OrderNo =N'" & OrderNo & "'" : cmd.ExecuteNonQuery()
                    End If
                Next
                '===========================================================================================

                aray_Id.Clear()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select Id From dbo.[Order] Where (UserID Is NULL) Or (OrderNo Is NULL) Or  (TotalOrder Is NULL) Or (CreatedAt Is NULL)"
                dr = cmd.ExecuteReader
                Do While dr.Read = True
                    aray_Id.Add(dr(0))
                Loop
                For i As Integer = 0 To aray_Id.Count - 1
                    Id = aray_Id(i).ToString()
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "delete From dbo.[Order] Where (Id =N'" & Id & "')" : cmd.ExecuteNonQuery()
                Next

                '===========================================================================================
                Dim aray_1 As New ArrayList
                ConnectingOnlineStore()
                aray_1.Clear()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select Id from [Product] where LimitQuantity IS NULL"
                dr = cmd.ExecuteReader
                Do While dr.Read = True
                    aray_1.Add(dr(0))
                Loop

                For i As Integer = 0 To aray_1.Count - 1
                    Id = aray_1(i).ToString()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update [Product] set LimitQuantity = '0' where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()
                Next

                '===============================================================================

                Cn.Close()
                connect()
            Else
                Cn.Close()
                connect()
                MsgBox(Cls_Constant.ErrMsg)
            End If

        End If

    End Sub

End Class