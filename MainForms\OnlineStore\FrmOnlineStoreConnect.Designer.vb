﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class FrmOnlineStoreConnect
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FrmOnlineStoreConnect))
        Me.Label9 = New System.Windows.Forms.Label()
        Me.Panel4 = New System.Windows.Forms.Panel()
        Me.rdoOnlineStoreNO = New System.Windows.Forms.RadioButton()
        Me.rdoOnlineStoreYES = New System.Windows.Forms.RadioButton()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.cmbAllowWithdrawalAfterRequestCustomer = New System.Windows.Forms.ComboBox()
        Me.Label21 = New System.Windows.Forms.Label()
        Me.Label82 = New System.Windows.Forms.Label()
        Me.Label84 = New System.Windows.Forms.Label()
        Me.txtPasswordOnlineStore = New System.Windows.Forms.TextBox()
        Me.txtServerNameOnlineStore = New System.Windows.Forms.TextBox()
        Me.Label85 = New System.Windows.Forms.Label()
        Me.txtDataBaseNameOnlineStore = New System.Windows.Forms.TextBox()
        Me.txtUserNameOnlineStore = New System.Windows.Forms.TextBox()
        Me.Label86 = New System.Windows.Forms.Label()
        Me.Label26 = New System.Windows.Forms.Label()
        Me.bgHeader = New System.Windows.Forms.Panel()
        Me.Image1 = New System.Windows.Forms.PictureBox()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.Panel5 = New System.Windows.Forms.Panel()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.Panel6 = New System.Windows.Forms.Panel()
        Me.Button1 = New System.Windows.Forms.Button()
        Me.ButtonAPPLY = New System.Windows.Forms.Button()
        Me.btnSyncPricesAndStore = New System.Windows.Forms.Button()
        Me.PanelServerData = New System.Windows.Forms.Panel()
        Me.btnDelServerData = New System.Windows.Forms.Button()
        Me.btnAddServerData = New System.Windows.Forms.Button()
        Me.dgvServerData = New System.Windows.Forms.DataGridView()
        Me.Panel19 = New System.Windows.Forms.Panel()
        Me.Panel20 = New System.Windows.Forms.Panel()
        Me.Panel21 = New System.Windows.Forms.Panel()
        Me.Panel22 = New System.Windows.Forms.Panel()
        Me.Label24 = New System.Windows.Forms.Label()
        Me.btnCloseServerData = New System.Windows.Forms.Button()
        Me.btnOrdersOnlineStore = New System.Windows.Forms.Button()
        Me.Panel4.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.bgHeader.SuspendLayout()
        CType(Me.Image1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelServerData.SuspendLayout()
        CType(Me.dgvServerData, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel22.SuspendLayout()
        Me.SuspendLayout()
        '
        'Label9
        '
        Me.Label9.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label9.AutoSize = True
        Me.Label9.Font = New System.Drawing.Font("JF Flat", 10.2!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label9.Location = New System.Drawing.Point(391, 61)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(380, 30)
        Me.Label9.TabIndex = 543
        Me.Label9.Text = "تفعيل ربط برنامج الحسابات بالمتجر الالكترونى"
        '
        'Panel4
        '
        Me.Panel4.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel4.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel4.Controls.Add(Me.rdoOnlineStoreNO)
        Me.Panel4.Controls.Add(Me.rdoOnlineStoreYES)
        Me.Panel4.Font = New System.Drawing.Font("JF Flat", 7.2!, System.Drawing.FontStyle.Bold)
        Me.Panel4.Location = New System.Drawing.Point(68, 58)
        Me.Panel4.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel4.Name = "Panel4"
        Me.Panel4.Size = New System.Drawing.Size(315, 37)
        Me.Panel4.TabIndex = 544
        '
        'rdoOnlineStoreNO
        '
        Me.rdoOnlineStoreNO.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.rdoOnlineStoreNO.AutoSize = True
        Me.rdoOnlineStoreNO.Checked = True
        Me.rdoOnlineStoreNO.Font = New System.Drawing.Font("JF Flat", 10.2!, System.Drawing.FontStyle.Bold)
        Me.rdoOnlineStoreNO.ForeColor = System.Drawing.SystemColors.Desktop
        Me.rdoOnlineStoreNO.Location = New System.Drawing.Point(223, 1)
        Me.rdoOnlineStoreNO.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.rdoOnlineStoreNO.Name = "rdoOnlineStoreNO"
        Me.rdoOnlineStoreNO.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.rdoOnlineStoreNO.Size = New System.Drawing.Size(56, 34)
        Me.rdoOnlineStoreNO.TabIndex = 475
        Me.rdoOnlineStoreNO.TabStop = True
        Me.rdoOnlineStoreNO.Text = "NO"
        Me.rdoOnlineStoreNO.UseVisualStyleBackColor = True
        '
        'rdoOnlineStoreYES
        '
        Me.rdoOnlineStoreYES.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.rdoOnlineStoreYES.AutoSize = True
        Me.rdoOnlineStoreYES.Font = New System.Drawing.Font("JF Flat", 10.2!, System.Drawing.FontStyle.Bold)
        Me.rdoOnlineStoreYES.ForeColor = System.Drawing.SystemColors.Desktop
        Me.rdoOnlineStoreYES.Location = New System.Drawing.Point(131, 1)
        Me.rdoOnlineStoreYES.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.rdoOnlineStoreYES.Name = "rdoOnlineStoreYES"
        Me.rdoOnlineStoreYES.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.rdoOnlineStoreYES.Size = New System.Drawing.Size(64, 34)
        Me.rdoOnlineStoreYES.TabIndex = 474
        Me.rdoOnlineStoreYES.Text = "YES"
        Me.rdoOnlineStoreYES.UseVisualStyleBackColor = True
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel1.Controls.Add(Me.cmbAllowWithdrawalAfterRequestCustomer)
        Me.Panel1.Controls.Add(Me.Label21)
        Me.Panel1.Controls.Add(Me.Label82)
        Me.Panel1.Controls.Add(Me.Label84)
        Me.Panel1.Controls.Add(Me.txtPasswordOnlineStore)
        Me.Panel1.Controls.Add(Me.txtServerNameOnlineStore)
        Me.Panel1.Controls.Add(Me.Label85)
        Me.Panel1.Controls.Add(Me.txtDataBaseNameOnlineStore)
        Me.Panel1.Controls.Add(Me.txtUserNameOnlineStore)
        Me.Panel1.Controls.Add(Me.Label86)
        Me.Panel1.Controls.Add(Me.Panel4)
        Me.Panel1.Controls.Add(Me.Label9)
        Me.Panel1.Font = New System.Drawing.Font("JF Flat", 7.2!, System.Drawing.FontStyle.Bold)
        Me.Panel1.Location = New System.Drawing.Point(63, 98)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(812, 317)
        Me.Panel1.TabIndex = 545
        '
        'cmbAllowWithdrawalAfterRequestCustomer
        '
        Me.cmbAllowWithdrawalAfterRequestCustomer.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbAllowWithdrawalAfterRequestCustomer.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.cmbAllowWithdrawalAfterRequestCustomer.Font = New System.Drawing.Font("JF Flat", 7.2!, System.Drawing.FontStyle.Bold)
        Me.cmbAllowWithdrawalAfterRequestCustomer.FormattingEnabled = True
        Me.cmbAllowWithdrawalAfterRequestCustomer.Items.AddRange(New Object() {"YES", "NO"})
        Me.cmbAllowWithdrawalAfterRequestCustomer.Location = New System.Drawing.Point(68, 21)
        Me.cmbAllowWithdrawalAfterRequestCustomer.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbAllowWithdrawalAfterRequestCustomer.Name = "cmbAllowWithdrawalAfterRequestCustomer"
        Me.cmbAllowWithdrawalAfterRequestCustomer.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.cmbAllowWithdrawalAfterRequestCustomer.Size = New System.Drawing.Size(315, 29)
        Me.cmbAllowWithdrawalAfterRequestCustomer.TabIndex = 554
        '
        'Label21
        '
        Me.Label21.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label21.AutoSize = True
        Me.Label21.Font = New System.Drawing.Font("JF Flat", 10.2!, System.Drawing.FontStyle.Bold)
        Me.Label21.Location = New System.Drawing.Point(404, 20)
        Me.Label21.Name = "Label21"
        Me.Label21.Size = New System.Drawing.Size(367, 30)
        Me.Label21.TabIndex = 553
        Me.Label21.Text = "السماح بالسحب المباشر بعدالطلب من العميل"
        '
        'Label82
        '
        Me.Label82.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label82.AutoSize = True
        Me.Label82.Font = New System.Drawing.Font("JF Flat", 10.2!, System.Drawing.FontStyle.Bold)
        Me.Label82.Location = New System.Drawing.Point(607, 112)
        Me.Label82.Name = "Label82"
        Me.Label82.Size = New System.Drawing.Size(164, 30)
        Me.Label82.TabIndex = 545
        Me.Label82.Text = "أسم قاعدة البيانات"
        '
        'Label84
        '
        Me.Label84.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label84.AutoSize = True
        Me.Label84.Font = New System.Drawing.Font("JF Flat", 10.2!, System.Drawing.FontStyle.Bold)
        Me.Label84.Location = New System.Drawing.Point(552, 158)
        Me.Label84.Name = "Label84"
        Me.Label84.Size = New System.Drawing.Size(219, 30)
        Me.Label84.TabIndex = 546
        Me.Label84.Text = "أسم الدخول  على السيرفر"
        '
        'txtPasswordOnlineStore
        '
        Me.txtPasswordOnlineStore.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtPasswordOnlineStore.Font = New System.Drawing.Font("JF Flat", 10.2!, System.Drawing.FontStyle.Bold)
        Me.txtPasswordOnlineStore.Location = New System.Drawing.Point(69, 253)
        Me.txtPasswordOnlineStore.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtPasswordOnlineStore.Name = "txtPasswordOnlineStore"
        Me.txtPasswordOnlineStore.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtPasswordOnlineStore.Size = New System.Drawing.Size(314, 37)
        Me.txtPasswordOnlineStore.TabIndex = 552
        Me.txtPasswordOnlineStore.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.txtPasswordOnlineStore.UseSystemPasswordChar = True
        '
        'txtServerNameOnlineStore
        '
        Me.txtServerNameOnlineStore.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtServerNameOnlineStore.Font = New System.Drawing.Font("JF Flat", 10.2!, System.Drawing.FontStyle.Bold)
        Me.txtServerNameOnlineStore.Location = New System.Drawing.Point(69, 156)
        Me.txtServerNameOnlineStore.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtServerNameOnlineStore.Name = "txtServerNameOnlineStore"
        Me.txtServerNameOnlineStore.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtServerNameOnlineStore.Size = New System.Drawing.Size(314, 37)
        Me.txtServerNameOnlineStore.TabIndex = 547
        Me.txtServerNameOnlineStore.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label85
        '
        Me.Label85.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label85.AutoSize = True
        Me.Label85.Font = New System.Drawing.Font("JF Flat", 10.2!, System.Drawing.FontStyle.Bold)
        Me.Label85.Location = New System.Drawing.Point(663, 257)
        Me.Label85.Name = "Label85"
        Me.Label85.Size = New System.Drawing.Size(108, 30)
        Me.Label85.TabIndex = 551
        Me.Label85.Text = "كلمة المرور"
        '
        'txtDataBaseNameOnlineStore
        '
        Me.txtDataBaseNameOnlineStore.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtDataBaseNameOnlineStore.Font = New System.Drawing.Font("JF Flat", 10.2!, System.Drawing.FontStyle.Bold)
        Me.txtDataBaseNameOnlineStore.Location = New System.Drawing.Point(69, 109)
        Me.txtDataBaseNameOnlineStore.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtDataBaseNameOnlineStore.Name = "txtDataBaseNameOnlineStore"
        Me.txtDataBaseNameOnlineStore.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtDataBaseNameOnlineStore.Size = New System.Drawing.Size(314, 37)
        Me.txtDataBaseNameOnlineStore.TabIndex = 548
        Me.txtDataBaseNameOnlineStore.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'txtUserNameOnlineStore
        '
        Me.txtUserNameOnlineStore.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtUserNameOnlineStore.Font = New System.Drawing.Font("JF Flat", 10.2!, System.Drawing.FontStyle.Bold)
        Me.txtUserNameOnlineStore.Location = New System.Drawing.Point(69, 203)
        Me.txtUserNameOnlineStore.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtUserNameOnlineStore.Name = "txtUserNameOnlineStore"
        Me.txtUserNameOnlineStore.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.txtUserNameOnlineStore.Size = New System.Drawing.Size(314, 37)
        Me.txtUserNameOnlineStore.TabIndex = 550
        Me.txtUserNameOnlineStore.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label86
        '
        Me.Label86.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label86.AutoSize = True
        Me.Label86.Font = New System.Drawing.Font("JF Flat", 10.2!, System.Drawing.FontStyle.Bold)
        Me.Label86.Location = New System.Drawing.Point(634, 206)
        Me.Label86.Name = "Label86"
        Me.Label86.Size = New System.Drawing.Size(137, 30)
        Me.Label86.TabIndex = 549
        Me.Label86.Text = "أسم المستخدم"
        '
        'Label26
        '
        Me.Label26.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label26.AutoSize = True
        Me.Label26.BackColor = System.Drawing.Color.Transparent
        Me.Label26.Cursor = System.Windows.Forms.Cursors.Default
        Me.Label26.Font = New System.Drawing.Font("JF Flat", 16.2!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label26.ForeColor = System.Drawing.Color.White
        Me.Label26.Location = New System.Drawing.Point(253, 12)
        Me.Label26.Name = "Label26"
        Me.Label26.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.Label26.Size = New System.Drawing.Size(411, 48)
        Me.Label26.TabIndex = 10
        Me.Label26.Text = "ربط البرنامج بالمتجر الالكترونى"
        '
        'bgHeader
        '
        Me.bgHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(99, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.bgHeader.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.bgHeader.Controls.Add(Me.Label26)
        Me.bgHeader.Controls.Add(Me.Image1)
        Me.bgHeader.Cursor = System.Windows.Forms.Cursors.Default
        Me.bgHeader.Dock = System.Windows.Forms.DockStyle.Top
        Me.bgHeader.Font = New System.Drawing.Font("Arial", 8.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bgHeader.ForeColor = System.Drawing.SystemColors.WindowText
        Me.bgHeader.Location = New System.Drawing.Point(0, 0)
        Me.bgHeader.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.bgHeader.Name = "bgHeader"
        Me.bgHeader.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.bgHeader.Size = New System.Drawing.Size(924, 67)
        Me.bgHeader.TabIndex = 546
        Me.bgHeader.TabStop = True
        '
        'Image1
        '
        Me.Image1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Image1.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.ConnectOnlineStore
        Me.Image1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.Image1.Cursor = System.Windows.Forms.Cursors.Default
        Me.Image1.Location = New System.Drawing.Point(825, 6)
        Me.Image1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Image1.Name = "Image1"
        Me.Image1.Size = New System.Drawing.Size(79, 59)
        Me.Image1.TabIndex = 12
        Me.Image1.TabStop = False
        '
        'Panel2
        '
        Me.Panel2.BackColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel2.Location = New System.Drawing.Point(0, 67)
        Me.Panel2.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(918, 6)
        Me.Panel2.TabIndex = 548
        '
        'Panel5
        '
        Me.Panel5.BackColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.Panel5.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel5.Location = New System.Drawing.Point(918, 67)
        Me.Panel5.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel5.Name = "Panel5"
        Me.Panel5.Size = New System.Drawing.Size(6, 459)
        Me.Panel5.TabIndex = 547
        '
        'Panel3
        '
        Me.Panel3.BackColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.Panel3.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel3.Location = New System.Drawing.Point(6, 520)
        Me.Panel3.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(912, 6)
        Me.Panel3.TabIndex = 550
        '
        'Panel6
        '
        Me.Panel6.BackColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.Panel6.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel6.Location = New System.Drawing.Point(0, 73)
        Me.Panel6.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel6.Name = "Panel6"
        Me.Panel6.Size = New System.Drawing.Size(6, 453)
        Me.Panel6.TabIndex = 549
        '
        'Button1
        '
        Me.Button1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Button1.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.Button1.Location = New System.Drawing.Point(591, 445)
        Me.Button1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(126, 42)
        Me.Button1.TabIndex = 552
        Me.Button1.Text = "إغلاق"
        Me.Button1.UseVisualStyleBackColor = True
        '
        'ButtonAPPLY
        '
        Me.ButtonAPPLY.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonAPPLY.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.ButtonAPPLY.Location = New System.Drawing.Point(732, 445)
        Me.ButtonAPPLY.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.ButtonAPPLY.Name = "ButtonAPPLY"
        Me.ButtonAPPLY.Size = New System.Drawing.Size(126, 42)
        Me.ButtonAPPLY.TabIndex = 551
        Me.ButtonAPPLY.Text = "تطبيق"
        Me.ButtonAPPLY.UseVisualStyleBackColor = True
        '
        'btnSyncPricesAndStore
        '
        Me.btnSyncPricesAndStore.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSyncPricesAndStore.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.btnSyncPricesAndStore.Location = New System.Drawing.Point(25, 445)
        Me.btnSyncPricesAndStore.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnSyncPricesAndStore.Name = "btnSyncPricesAndStore"
        Me.btnSyncPricesAndStore.Size = New System.Drawing.Size(234, 42)
        Me.btnSyncPricesAndStore.TabIndex = 553
        Me.btnSyncPricesAndStore.Text = "تحديث الاسعار والمخزون اونلاين"
        Me.btnSyncPricesAndStore.UseVisualStyleBackColor = True
        '
        'PanelServerData
        '
        Me.PanelServerData.Controls.Add(Me.btnDelServerData)
        Me.PanelServerData.Controls.Add(Me.btnAddServerData)
        Me.PanelServerData.Controls.Add(Me.dgvServerData)
        Me.PanelServerData.Controls.Add(Me.Panel19)
        Me.PanelServerData.Controls.Add(Me.Panel20)
        Me.PanelServerData.Controls.Add(Me.Panel21)
        Me.PanelServerData.Controls.Add(Me.Panel22)
        Me.PanelServerData.Location = New System.Drawing.Point(12, 170)
        Me.PanelServerData.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.PanelServerData.Name = "PanelServerData"
        Me.PanelServerData.Size = New System.Drawing.Size(138, 245)
        Me.PanelServerData.TabIndex = 554
        '
        'btnDelServerData
        '
        Me.btnDelServerData.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnDelServerData.BackColor = System.Drawing.Color.Transparent
        Me.btnDelServerData.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Delete
        Me.btnDelServerData.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnDelServerData.Location = New System.Drawing.Point(13, 200)
        Me.btnDelServerData.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnDelServerData.Name = "btnDelServerData"
        Me.btnDelServerData.Size = New System.Drawing.Size(114, 34)
        Me.btnDelServerData.TabIndex = 214
        Me.btnDelServerData.UseVisualStyleBackColor = False
        '
        'btnAddServerData
        '
        Me.btnAddServerData.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnAddServerData.BackColor = System.Drawing.Color.Transparent
        Me.btnAddServerData.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Items_add_1
        Me.btnAddServerData.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnAddServerData.Location = New System.Drawing.Point(12, 42)
        Me.btnAddServerData.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnAddServerData.Name = "btnAddServerData"
        Me.btnAddServerData.Size = New System.Drawing.Size(114, 34)
        Me.btnAddServerData.TabIndex = 209
        Me.btnAddServerData.UseVisualStyleBackColor = False
        '
        'dgvServerData
        '
        Me.dgvServerData.AllowUserToAddRows = False
        Me.dgvServerData.AllowUserToDeleteRows = False
        Me.dgvServerData.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.dgvServerData.BackgroundColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle1.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle1.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgvServerData.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle1
        Me.dgvServerData.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window
        DataGridViewCellStyle2.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.dgvServerData.DefaultCellStyle = DataGridViewCellStyle2
        Me.dgvServerData.Location = New System.Drawing.Point(10, 82)
        Me.dgvServerData.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.dgvServerData.Name = "dgvServerData"
        Me.dgvServerData.ReadOnly = True
        Me.dgvServerData.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        DataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle3.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle3.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle3.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle3.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgvServerData.RowHeadersDefaultCellStyle = DataGridViewCellStyle3
        Me.dgvServerData.RowTemplate.Height = 26
        Me.dgvServerData.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.dgvServerData.Size = New System.Drawing.Size(117, 111)
        Me.dgvServerData.TabIndex = 206
        '
        'Panel19
        '
        Me.Panel19.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel19.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel19.Location = New System.Drawing.Point(0, 34)
        Me.Panel19.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel19.Name = "Panel19"
        Me.Panel19.Size = New System.Drawing.Size(6, 205)
        Me.Panel19.TabIndex = 205
        '
        'Panel20
        '
        Me.Panel20.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel20.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel20.Location = New System.Drawing.Point(132, 34)
        Me.Panel20.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel20.Name = "Panel20"
        Me.Panel20.Size = New System.Drawing.Size(6, 205)
        Me.Panel20.TabIndex = 204
        '
        'Panel21
        '
        Me.Panel21.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel21.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel21.Location = New System.Drawing.Point(0, 239)
        Me.Panel21.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel21.Name = "Panel21"
        Me.Panel21.Size = New System.Drawing.Size(138, 6)
        Me.Panel21.TabIndex = 203
        '
        'Panel22
        '
        Me.Panel22.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel22.Controls.Add(Me.Label24)
        Me.Panel22.Controls.Add(Me.btnCloseServerData)
        Me.Panel22.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel22.Location = New System.Drawing.Point(0, 0)
        Me.Panel22.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel22.Name = "Panel22"
        Me.Panel22.Size = New System.Drawing.Size(138, 34)
        Me.Panel22.TabIndex = 202
        '
        'Label24
        '
        Me.Label24.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label24.AutoSize = True
        Me.Label24.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.Label24.ForeColor = System.Drawing.Color.White
        Me.Label24.Location = New System.Drawing.Point(9, 6)
        Me.Label24.Name = "Label24"
        Me.Label24.Size = New System.Drawing.Size(100, 24)
        Me.Label24.TabIndex = 207
        Me.Label24.Text = "بيانات السيرفر"
        '
        'btnCloseServerData
        '
        Me.btnCloseServerData.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCloseServerData.BackColor = System.Drawing.Color.Transparent
        Me.btnCloseServerData.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Delete
        Me.btnCloseServerData.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnCloseServerData.Location = New System.Drawing.Point(106, 1)
        Me.btnCloseServerData.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnCloseServerData.Name = "btnCloseServerData"
        Me.btnCloseServerData.Size = New System.Drawing.Size(29, 31)
        Me.btnCloseServerData.TabIndex = 206
        Me.btnCloseServerData.UseVisualStyleBackColor = False
        '
        'btnOrdersOnlineStore
        '
        Me.btnOrdersOnlineStore.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOrdersOnlineStore.Font = New System.Drawing.Font("JF Flat", 8.25!, System.Drawing.FontStyle.Bold)
        Me.btnOrdersOnlineStore.Location = New System.Drawing.Point(265, 445)
        Me.btnOrdersOnlineStore.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnOrdersOnlineStore.Name = "btnOrdersOnlineStore"
        Me.btnOrdersOnlineStore.Size = New System.Drawing.Size(234, 42)
        Me.btnOrdersOnlineStore.TabIndex = 555
        Me.btnOrdersOnlineStore.Text = "طلبات المتجر الالكترونى"
        Me.btnOrdersOnlineStore.UseVisualStyleBackColor = True
        '
        'FrmOnlineStoreConnect
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(924, 526)
        Me.Controls.Add(Me.PanelServerData)
        Me.Controls.Add(Me.btnSyncPricesAndStore)
        Me.Controls.Add(Me.Button1)
        Me.Controls.Add(Me.ButtonAPPLY)
        Me.Controls.Add(Me.Panel3)
        Me.Controls.Add(Me.Panel6)
        Me.Controls.Add(Me.Panel2)
        Me.Controls.Add(Me.Panel5)
        Me.Controls.Add(Me.bgHeader)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.btnOrdersOnlineStore)
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.Name = "FrmOnlineStoreConnect"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "ربط البرنامج بالمتجر الالكترونى"
        Me.Panel4.ResumeLayout(False)
        Me.Panel4.PerformLayout()
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        Me.bgHeader.ResumeLayout(False)
        Me.bgHeader.PerformLayout()
        CType(Me.Image1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelServerData.ResumeLayout(False)
        CType(Me.dgvServerData, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel22.ResumeLayout(False)
        Me.Panel22.PerformLayout()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents Label9 As Label
    Friend WithEvents Panel4 As Panel
    Friend WithEvents rdoOnlineStoreNO As RadioButton
    Friend WithEvents rdoOnlineStoreYES As RadioButton
    Friend WithEvents Panel1 As Panel
    Public WithEvents Label26 As Label
    Public WithEvents Image1 As PictureBox
    Public WithEvents bgHeader As Panel
    Friend WithEvents Panel2 As Panel
    Friend WithEvents Panel5 As Panel
    Friend WithEvents Panel3 As Panel
    Friend WithEvents Panel6 As Panel
    Friend WithEvents Label82 As Label
    Friend WithEvents Label84 As Label
    Friend WithEvents txtPasswordOnlineStore As TextBox
    Friend WithEvents txtServerNameOnlineStore As TextBox
    Friend WithEvents Label85 As Label
    Friend WithEvents txtDataBaseNameOnlineStore As TextBox
    Friend WithEvents txtUserNameOnlineStore As TextBox
    Friend WithEvents Label86 As Label
    Friend WithEvents Button1 As Button
    Friend WithEvents ButtonAPPLY As Button
    Friend WithEvents btnSyncPricesAndStore As Button
    Friend WithEvents PanelServerData As Panel
    Friend WithEvents btnDelServerData As Button
    Friend WithEvents btnAddServerData As Button
    Friend WithEvents dgvServerData As DataGridView
    Friend WithEvents Panel19 As Panel
    Friend WithEvents Panel20 As Panel
    Friend WithEvents Panel21 As Panel
    Friend WithEvents Panel22 As Panel
    Friend WithEvents Label24 As Label
    Friend WithEvents btnCloseServerData As Button
    Friend WithEvents btnOrdersOnlineStore As Button
    Friend WithEvents cmbAllowWithdrawalAfterRequestCustomer As ComboBox
    Friend WithEvents Label21 As Label
End Class
