﻿Public Class Frm_EMailMessageSetting
    Private Sub Frm_EMailMessageSetting_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        txtEmailSmtpServer.Text = mykey.GetValue("SmtpServerEmail", "<EMAIL>")
        txtPasswordSmtpServer.Text = mykey.GetValue("SmtpServerPassword", "agc*academy*01040")
        txtEmailFrom.Text = mykey.GetValue("SendEmailFrom", "<EMAIL>")
        txtEmailTO.Text = mykey.GetValue("SendEmailTO", "<EMAIL>")
        Dim ActivationEmail As String = mykey.GetValue("ActivationEmail", "NO")
        If ActivationEmail = "NO" Then
            chkActivationEmail.Checked = False
        Else
            chkActivationEmail.Checked = True
        End If
    End Sub

    Private Sub ButtonAPPLY_Click(sender As Object, e As EventArgs) Handles ButtonAPPLY.Click
        mykey.SetValue("SmtpServerEmail", txtEmailSmtpServer.Text)
        mykey.SetValue("SmtpServerPassword", txtPasswordSmtpServer.Text)
        mykey.SetValue("SendEmailFrom", txtEmailFrom.Text)
        mykey.SetValue("SendEmailTO", txtEmailTO.Text)
        If chkActivationEmail.Checked = False Then
            mykey.SetValue("ActivationEmail", "NO")
        ElseIf chkActivationEmail.Checked = True Then
            mykey.SetValue("ActivationEmail", "YES")
        End If

        Me.Close()
    End Sub
End Class