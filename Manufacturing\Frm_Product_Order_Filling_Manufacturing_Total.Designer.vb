﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class Frm_Product_Order_Filling_Manufacturing_Total
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Frm_Product_Order_Filling_Manufacturing_Total))
        Me.bgHeader = New System.Windows.Forms.Panel()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.Label26 = New System.Windows.Forms.Label()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.Panel5 = New System.Windows.Forms.Panel()
        Me.Panel6 = New System.Windows.Forms.Panel()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.cmbCatsManufacturingView = New System.Windows.Forms.ComboBox()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.cmbItemsManufacturingView = New System.Windows.Forms.ComboBox()
        Me.btnSearch = New System.Windows.Forms.Button()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.cmbStoreView = New System.Windows.Forms.ComboBox()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Panel8 = New System.Windows.Forms.Panel()
        Me.chkAll = New System.Windows.Forms.CheckBox()
        Me.dgv_Material = New System.Windows.Forms.DataGridView()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.btnPrint = New System.Windows.Forms.Button()
        Me.txtTotalCostPrice = New System.Windows.Forms.TextBox()
        Me.Label24 = New System.Windows.Forms.Label()
        Me.ChkWithoutDate = New System.Windows.Forms.CheckBox()
        Me.Label28 = New System.Windows.Forms.Label()
        Me.DateTimePicker2 = New System.Windows.Forms.DateTimePicker()
        Me.Label29 = New System.Windows.Forms.Label()
        Me.DateTimePicker1 = New System.Windows.Forms.DateTimePicker()
        Me.txtTotalWeight = New System.Windows.Forms.TextBox()
        Me.Label23 = New System.Windows.Forms.Label()
        Me.btnFilter = New System.Windows.Forms.Button()
        Me.bgHeader.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel8.SuspendLayout()
        CType(Me.dgv_Material, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'bgHeader
        '
        Me.bgHeader.BackColor = System.Drawing.Color.Silver
        Me.bgHeader.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.bgHeader.Controls.Add(Me.PictureBox1)
        Me.bgHeader.Controls.Add(Me.Label26)
        Me.bgHeader.Cursor = System.Windows.Forms.Cursors.Default
        Me.bgHeader.Dock = System.Windows.Forms.DockStyle.Top
        Me.bgHeader.Font = New System.Drawing.Font("Arial", 8.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bgHeader.ForeColor = System.Drawing.SystemColors.WindowText
        Me.bgHeader.Location = New System.Drawing.Point(0, 0)
        Me.bgHeader.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.bgHeader.Name = "bgHeader"
        Me.bgHeader.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.bgHeader.Size = New System.Drawing.Size(1214, 67)
        Me.bgHeader.TabIndex = 98
        Me.bgHeader.TabStop = True
        '
        'PictureBox1
        '
        Me.PictureBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureBox1.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Offers_Items
        Me.PictureBox1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.PictureBox1.Location = New System.Drawing.Point(1090, 5)
        Me.PictureBox1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(91, 58)
        Me.PictureBox1.TabIndex = 11
        Me.PictureBox1.TabStop = False
        '
        'Label26
        '
        Me.Label26.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label26.AutoSize = True
        Me.Label26.BackColor = System.Drawing.Color.Transparent
        Me.Label26.Cursor = System.Windows.Forms.Cursors.Default
        Me.Label26.Font = New System.Drawing.Font("JF Flat", 15.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label26.ForeColor = System.Drawing.Color.White
        Me.Label26.Location = New System.Drawing.Point(385, 10)
        Me.Label26.Name = "Label26"
        Me.Label26.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.Label26.Size = New System.Drawing.Size(465, 47)
        Me.Label26.TabIndex = 10
        Me.Label26.Text = "عرض اجمالى امر شغل تعبئة وتصنيع"
        '
        'Panel2
        '
        Me.Panel2.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel2.Location = New System.Drawing.Point(0, 67)
        Me.Panel2.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(1214, 6)
        Me.Panel2.TabIndex = 201
        '
        'Panel5
        '
        Me.Panel5.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel5.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel5.Location = New System.Drawing.Point(1208, 73)
        Me.Panel5.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel5.Name = "Panel5"
        Me.Panel5.Size = New System.Drawing.Size(6, 627)
        Me.Panel5.TabIndex = 203
        '
        'Panel6
        '
        Me.Panel6.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel6.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel6.Location = New System.Drawing.Point(0, 73)
        Me.Panel6.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel6.Name = "Panel6"
        Me.Panel6.Size = New System.Drawing.Size(6, 627)
        Me.Panel6.TabIndex = 204
        '
        'Panel3
        '
        Me.Panel3.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel3.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel3.Location = New System.Drawing.Point(6, 694)
        Me.Panel3.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(1202, 6)
        Me.Panel3.TabIndex = 205
        '
        'Label7
        '
        Me.Label7.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label7.Location = New System.Drawing.Point(1113, 124)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(87, 26)
        Me.Label7.TabIndex = 233
        Me.Label7.Text = "المجموعة"
        '
        'cmbCatsManufacturingView
        '
        Me.cmbCatsManufacturingView.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbCatsManufacturingView.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbCatsManufacturingView.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbCatsManufacturingView.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.cmbCatsManufacturingView.FormattingEnabled = True
        Me.cmbCatsManufacturingView.Location = New System.Drawing.Point(931, 119)
        Me.cmbCatsManufacturingView.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbCatsManufacturingView.Name = "cmbCatsManufacturingView"
        Me.cmbCatsManufacturingView.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.cmbCatsManufacturingView.Size = New System.Drawing.Size(173, 34)
        Me.cmbCatsManufacturingView.TabIndex = 232
        '
        'Label8
        '
        Me.Label8.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label8.AutoSize = True
        Me.Label8.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label8.ForeColor = System.Drawing.Color.Black
        Me.Label8.Location = New System.Drawing.Point(833, 107)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(98, 26)
        Me.Label8.TabIndex = 231
        Me.Label8.Text = "أسم الصنف"
        '
        'cmbItemsManufacturingView
        '
        Me.cmbItemsManufacturingView.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbItemsManufacturingView.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbItemsManufacturingView.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbItemsManufacturingView.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.cmbItemsManufacturingView.FormattingEnabled = True
        Me.cmbItemsManufacturingView.Location = New System.Drawing.Point(572, 102)
        Me.cmbItemsManufacturingView.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbItemsManufacturingView.Name = "cmbItemsManufacturingView"
        Me.cmbItemsManufacturingView.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.cmbItemsManufacturingView.Size = New System.Drawing.Size(259, 34)
        Me.cmbItemsManufacturingView.TabIndex = 230
        '
        'btnSearch
        '
        Me.btnSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSearch.BackColor = System.Drawing.Color.Transparent
        Me.btnSearch.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Search3
        Me.btnSearch.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnSearch.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.btnSearch.Location = New System.Drawing.Point(24, 87)
        Me.btnSearch.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnSearch.Name = "btnSearch"
        Me.btnSearch.Size = New System.Drawing.Size(119, 68)
        Me.btnSearch.TabIndex = 234
        Me.btnSearch.UseVisualStyleBackColor = False
        '
        'Label2
        '
        Me.Label2.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label2.Location = New System.Drawing.Point(1133, 84)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(60, 26)
        Me.Label2.TabIndex = 236
        Me.Label2.Text = "المخزن"
        '
        'cmbStoreView
        '
        Me.cmbStoreView.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbStoreView.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend
        Me.cmbStoreView.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.cmbStoreView.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbStoreView.FormattingEnabled = True
        Me.cmbStoreView.Location = New System.Drawing.Point(931, 79)
        Me.cmbStoreView.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbStoreView.Name = "cmbStoreView"
        Me.cmbStoreView.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.cmbStoreView.Size = New System.Drawing.Size(173, 34)
        Me.cmbStoreView.TabIndex = 235
        '
        'Label6
        '
        Me.Label6.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label6.AutoSize = True
        Me.Label6.BackColor = System.Drawing.Color.Transparent
        Me.Label6.Cursor = System.Windows.Forms.Cursors.Default
        Me.Label6.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.Label6.ForeColor = System.Drawing.Color.White
        Me.Label6.Location = New System.Drawing.Point(591, 4)
        Me.Label6.Name = "Label6"
        Me.Label6.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.Label6.Size = New System.Drawing.Size(74, 30)
        Me.Label6.TabIndex = 229
        Me.Label6.Text = "الخامات"
        '
        'Panel8
        '
        Me.Panel8.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel8.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel8.Controls.Add(Me.Label6)
        Me.Panel8.Location = New System.Drawing.Point(5, 164)
        Me.Panel8.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel8.Name = "Panel8"
        Me.Panel8.Size = New System.Drawing.Size(1214, 34)
        Me.Panel8.TabIndex = 229
        '
        'chkAll
        '
        Me.chkAll.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.chkAll.AutoSize = True
        Me.chkAll.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.chkAll.Location = New System.Drawing.Point(495, 105)
        Me.chkAll.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.chkAll.Name = "chkAll"
        Me.chkAll.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.chkAll.Size = New System.Drawing.Size(70, 34)
        Me.chkAll.TabIndex = 237
        Me.chkAll.Text = "الكل"
        Me.chkAll.UseVisualStyleBackColor = True
        '
        'dgv_Material
        '
        Me.dgv_Material.AllowUserToAddRows = False
        Me.dgv_Material.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.dgv_Material.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill
        Me.dgv_Material.BackgroundColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle1.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle1.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgv_Material.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle1
        Me.dgv_Material.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window
        DataGridViewCellStyle2.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.dgv_Material.DefaultCellStyle = DataGridViewCellStyle2
        Me.dgv_Material.Location = New System.Drawing.Point(7, 199)
        Me.dgv_Material.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.dgv_Material.Name = "dgv_Material"
        Me.dgv_Material.ReadOnly = True
        Me.dgv_Material.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        DataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.SteelBlue
        Me.dgv_Material.RowsDefaultCellStyle = DataGridViewCellStyle3
        Me.dgv_Material.RowTemplate.Height = 25
        Me.dgv_Material.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.dgv_Material.Size = New System.Drawing.Size(1203, 412)
        Me.dgv_Material.TabIndex = 238
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel1.Location = New System.Drawing.Point(7, 613)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1203, 6)
        Me.Panel1.TabIndex = 239
        '
        'btnPrint
        '
        Me.btnPrint.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnPrint.BackColor = System.Drawing.Color.Transparent
        Me.btnPrint.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Print_bill
        Me.btnPrint.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnPrint.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.btnPrint.Location = New System.Drawing.Point(24, 626)
        Me.btnPrint.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnPrint.Name = "btnPrint"
        Me.btnPrint.Size = New System.Drawing.Size(119, 62)
        Me.btnPrint.TabIndex = 242
        Me.btnPrint.UseVisualStyleBackColor = False
        '
        'txtTotalCostPrice
        '
        Me.txtTotalCostPrice.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtTotalCostPrice.Font = New System.Drawing.Font("Tahoma", 14.25!)
        Me.txtTotalCostPrice.Location = New System.Drawing.Point(915, 636)
        Me.txtTotalCostPrice.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtTotalCostPrice.Name = "txtTotalCostPrice"
        Me.txtTotalCostPrice.ReadOnly = True
        Me.txtTotalCostPrice.Size = New System.Drawing.Size(147, 36)
        Me.txtTotalCostPrice.TabIndex = 407
        Me.txtTotalCostPrice.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label24
        '
        Me.Label24.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label24.AutoSize = True
        Me.Label24.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label24.ForeColor = System.Drawing.Color.Black
        Me.Label24.Location = New System.Drawing.Point(1062, 641)
        Me.Label24.Name = "Label24"
        Me.Label24.Size = New System.Drawing.Size(130, 26)
        Me.Label24.TabIndex = 408
        Me.Label24.Text = "أجمالى التكلفة"
        '
        'ChkWithoutDate
        '
        Me.ChkWithoutDate.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ChkWithoutDate.AutoSize = True
        Me.ChkWithoutDate.Checked = True
        Me.ChkWithoutDate.CheckState = System.Windows.Forms.CheckState.Checked
        Me.ChkWithoutDate.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold)
        Me.ChkWithoutDate.Location = New System.Drawing.Point(220, 105)
        Me.ChkWithoutDate.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.ChkWithoutDate.Name = "ChkWithoutDate"
        Me.ChkWithoutDate.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.ChkWithoutDate.Size = New System.Drawing.Size(70, 34)
        Me.ChkWithoutDate.TabIndex = 417
        Me.ChkWithoutDate.Text = "الكل"
        Me.ChkWithoutDate.UseVisualStyleBackColor = True
        '
        'Label28
        '
        Me.Label28.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label28.AutoSize = True
        Me.Label28.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label28.Location = New System.Drawing.Point(451, 124)
        Me.Label28.Name = "Label28"
        Me.Label28.Size = New System.Drawing.Size(39, 26)
        Me.Label28.TabIndex = 416
        Me.Label28.Text = "الى"
        '
        'DateTimePicker2
        '
        Me.DateTimePicker2.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DateTimePicker2.CustomFormat = "dd/MM/yyyy"
        Me.DateTimePicker2.Enabled = False
        Me.DateTimePicker2.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.DateTimePicker2.Format = System.Windows.Forms.DateTimePickerFormat.[Short]
        Me.DateTimePicker2.Location = New System.Drawing.Point(292, 119)
        Me.DateTimePicker2.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.DateTimePicker2.Name = "DateTimePicker2"
        Me.DateTimePicker2.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.DateTimePicker2.RightToLeftLayout = True
        Me.DateTimePicker2.Size = New System.Drawing.Size(149, 34)
        Me.DateTimePicker2.TabIndex = 415
        '
        'Label29
        '
        Me.Label29.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label29.AutoSize = True
        Me.Label29.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label29.Location = New System.Drawing.Point(455, 85)
        Me.Label29.Name = "Label29"
        Me.Label29.Size = New System.Drawing.Size(33, 26)
        Me.Label29.TabIndex = 414
        Me.Label29.Text = "من"
        '
        'DateTimePicker1
        '
        Me.DateTimePicker1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DateTimePicker1.CustomFormat = "dd/MM/yyyy"
        Me.DateTimePicker1.Enabled = False
        Me.DateTimePicker1.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.DateTimePicker1.Format = System.Windows.Forms.DateTimePickerFormat.[Short]
        Me.DateTimePicker1.Location = New System.Drawing.Point(291, 80)
        Me.DateTimePicker1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.DateTimePicker1.Name = "DateTimePicker1"
        Me.DateTimePicker1.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.DateTimePicker1.RightToLeftLayout = True
        Me.DateTimePicker1.Size = New System.Drawing.Size(150, 34)
        Me.DateTimePicker1.TabIndex = 413
        '
        'txtTotalWeight
        '
        Me.txtTotalWeight.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtTotalWeight.Font = New System.Drawing.Font("Tahoma", 14.25!)
        Me.txtTotalWeight.Location = New System.Drawing.Point(631, 636)
        Me.txtTotalWeight.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtTotalWeight.Name = "txtTotalWeight"
        Me.txtTotalWeight.ReadOnly = True
        Me.txtTotalWeight.Size = New System.Drawing.Size(147, 36)
        Me.txtTotalWeight.TabIndex = 409
        Me.txtTotalWeight.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label23
        '
        Me.Label23.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label23.AutoSize = True
        Me.Label23.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label23.ForeColor = System.Drawing.Color.Black
        Me.Label23.Location = New System.Drawing.Point(784, 641)
        Me.Label23.Name = "Label23"
        Me.Label23.Size = New System.Drawing.Size(122, 26)
        Me.Label23.TabIndex = 410
        Me.Label23.Text = "إجمالى الكمية"
        '
        'btnFilter
        '
        Me.btnFilter.BackColor = System.Drawing.Color.Transparent
        Me.btnFilter.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Filter
        Me.btnFilter.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.btnFilter.FlatAppearance.BorderSize = 0
        Me.btnFilter.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.btnFilter.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.btnFilter.Location = New System.Drawing.Point(181, 113)
        Me.btnFilter.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnFilter.Name = "btnFilter"
        Me.btnFilter.Size = New System.Drawing.Size(18, 18)
        Me.btnFilter.TabIndex = 422
        Me.btnFilter.UseVisualStyleBackColor = False
        '
        'Frm_Product_Order_Filling_Manufacturing_Total
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1214, 700)
        Me.Controls.Add(Me.btnFilter)
        Me.Controls.Add(Me.Panel8)
        Me.Controls.Add(Me.ChkWithoutDate)
        Me.Controls.Add(Me.Label28)
        Me.Controls.Add(Me.DateTimePicker2)
        Me.Controls.Add(Me.Label29)
        Me.Controls.Add(Me.DateTimePicker1)
        Me.Controls.Add(Me.btnPrint)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.dgv_Material)
        Me.Controls.Add(Me.chkAll)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.cmbStoreView)
        Me.Controls.Add(Me.btnSearch)
        Me.Controls.Add(Me.Label7)
        Me.Controls.Add(Me.cmbCatsManufacturingView)
        Me.Controls.Add(Me.Label8)
        Me.Controls.Add(Me.cmbItemsManufacturingView)
        Me.Controls.Add(Me.Panel3)
        Me.Controls.Add(Me.Panel6)
        Me.Controls.Add(Me.Panel5)
        Me.Controls.Add(Me.Panel2)
        Me.Controls.Add(Me.bgHeader)
        Me.Controls.Add(Me.Label23)
        Me.Controls.Add(Me.txtTotalWeight)
        Me.Controls.Add(Me.Label24)
        Me.Controls.Add(Me.txtTotalCostPrice)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Name = "Frm_Product_Order_Filling_Manufacturing_Total"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "عرض اجمالى امر شغل تعبئة وتصنيع"
        Me.WindowState = System.Windows.Forms.FormWindowState.Maximized
        Me.bgHeader.ResumeLayout(False)
        Me.bgHeader.PerformLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel8.ResumeLayout(False)
        Me.Panel8.PerformLayout()
        CType(Me.dgv_Material, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Public WithEvents bgHeader As Panel
    Friend WithEvents PictureBox1 As PictureBox
    Public WithEvents Label26 As Label
    Friend WithEvents Panel2 As Panel
    Friend WithEvents Panel5 As Panel
    Friend WithEvents Panel6 As Panel
    Friend WithEvents Panel3 As Panel
    Friend WithEvents Label7 As Label
    Friend WithEvents cmbCatsManufacturingView As ComboBox
    Friend WithEvents Label8 As Label
    Friend WithEvents cmbItemsManufacturingView As ComboBox
    Friend WithEvents btnSearch As Button
    Friend WithEvents Label2 As Label
    Friend WithEvents cmbStoreView As ComboBox
    Public WithEvents Label6 As Label
    Friend WithEvents Panel8 As Panel
    Friend WithEvents chkAll As CheckBox
    Friend WithEvents dgv_Material As DataGridView
    Friend WithEvents Panel1 As Panel
    Friend WithEvents btnDelete As Button
    Friend WithEvents btnPrint As Button
    Friend WithEvents txtTotalCostPrice As TextBox
    Friend WithEvents Label24 As Label
    Friend WithEvents ChkWithoutDate As CheckBox
    Friend WithEvents Label28 As Label
    Friend WithEvents DateTimePicker2 As DateTimePicker
    Friend WithEvents Label29 As Label
    Friend WithEvents DateTimePicker1 As DateTimePicker
    Friend WithEvents txtTotalWeight As TextBox
    Friend WithEvents Label23 As Label
    Friend WithEvents btnFilter As Button
End Class
