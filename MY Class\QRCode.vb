﻿Imports System
Imports System.Text
Imports System.Drawing
Imports System.Collections.Generic
Imports ThoughtWorks.QRCode.Codec

Public Class QRCode
    Private Seller As Byte()
    Private VatNo As Byte()
    Private dateTime As Byte()
    Private Total As Byte()
    Private Tax As Byte()

    Public Sub New(ByVal Seller As System.[String], ByVal TaxNo As System.[String], ByVal dateTime As System.DateTime, ByVal Total As System.[Double], ByVal Tax As System.[Double])
        Me.Seller = System.Text.Encoding.UTF8.GetBytes(Seller)
        Me.VatNo = System.Text.Encoding.UTF8.GetBytes(TaxNo)
        Me.dateTime = System.Text.Encoding.UTF8.GetBytes(dateTime.ToString())
        Me.Total = System.Text.Encoding.UTF8.GetBytes(Total.ToString())
        Me.Tax = System.Text.Encoding.UTF8.GetBytes(Tax.ToString())
    End Sub

    Private Function getasText(ByVal Tag As Integer, ByVal Value As Byte()) As System.[String]
        Return (Tag).ToString("X2") & (Value.Length).ToString("X2") & System.BitConverter.ToString(CType((Value), System.[Byte]())).Replace("-", String.Empty)
    End Function

    Private Function getBytes(ByVal id As Integer, ByVal Value As Byte()) As Byte()
        Dim val As Byte() = New Byte(2 + Value.Length - 1) {}
        val(0) = CByte(id)
        val(1) = CByte(Value.Length)
        Value.CopyTo(val, 2)
        Return val
    End Function

    Private Function getString() As System.[String]
        Dim TLV_Text As System.[String] = ""
        TLV_Text += Me.getasText(1, Me.Seller)
        TLV_Text += Me.getasText(2, Me.VatNo)
        TLV_Text += Me.getasText(3, Me.dateTime)
        TLV_Text += Me.getasText(4, Me.Total)
        TLV_Text += Me.getasText(5, Me.Tax)
        Return TLV_Text
    End Function

    Public Overrides Function ToString() As String
        Return Me.getString()
    End Function

    Public Function ToBase64() As System.[String]
        Dim TLV_Bytes As System.Collections.Generic.List(Of Byte) = New System.Collections.Generic.List(Of Byte)()
        TLV_Bytes.AddRange(Me.getBytes(1, Me.Seller))
        TLV_Bytes.AddRange(Me.getBytes(2, Me.VatNo))
        TLV_Bytes.AddRange(Me.getBytes(3, Me.dateTime))
        TLV_Bytes.AddRange(Me.getBytes(4, Me.Total))
        TLV_Bytes.AddRange(Me.getBytes(5, Me.Tax))
        Return System.Convert.ToBase64String(TLV_Bytes.ToArray())
    End Function


End Class
