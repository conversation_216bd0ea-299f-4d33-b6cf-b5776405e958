# NOTE: Requires **VS2019 16.7** or later

# 'MicrosoftCodeAnalysisDocumentation' Rules from '2.9.8' release with 'Default' analysis mode escalated to 'error' severity
# Description: 'MicrosoftCodeAnalysisDocumentation' Rules with enabled-by-default state from '2.9.8' release with 'Default' analysis mode. Rules that are first released in a version later than '2.9.8' are disabled. Enabled rules with 'warning' severity are escalated to 'error' severity to respect 'CodeAnalysisTreatWarningsAsErrors' MSBuild property.

is_global = true

global_level = -99

