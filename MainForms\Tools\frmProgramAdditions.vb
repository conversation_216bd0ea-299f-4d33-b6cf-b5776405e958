﻿Imports System.Windows.Forms
Imports System.Data.SqlClient
Imports vb = Microsoft.VisualBasic
Imports System.IO

Public Class frmProgramAdditions
    Inherits System.Windows.Forms.Form
    Dim ReturnChk As Boolean = False
    Dim ActionLood As Boolean = True

    Private Sub frmCompany_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        ActionLood = True
        Dim ProgramAdditionsEmployees As String = mykey.GetValue("ProgramAdditionsEmployees", "NO")
        If ProgramAdditionsEmployees = "NO" Then
            chkEmployees.Checked = False
        Else
            chkEmployees.Checked = True
        End If

        Dim ProgramAdditionsDelegate As String = mykey.GetValue("ProgramAdditionsDelegate", "NO")
        If ProgramAdditionsDelegate = "NO" Then
            chkDelegate.Checked = False
        Else
            chkDelegate.Checked = True
        End If

        Dim ProgramAdditionsManufacturing As String = mykey.GetValue("ProgramAdditionsManufacturing", "NO")
        If ProgramAdditionsManufacturing = "NO" Then
            chkManufacturing.Checked = False
        Else
            chkManufacturing.Checked = True
        End If

        Dim ProgramAdditionsBanks As String = mykey.GetValue("ProgramAdditionsBanks", "NO")
        If ProgramAdditionsBanks = "NO" Then
            chkBanks.Checked = False
        Else
            chkBanks.Checked = True
        End If

        Dim ProgramAdditionsAccounts As String = mykey.GetValue("ProgramAdditionsAccounts", "NO")
        If ProgramAdditionsAccounts = "NO" Then
            chkAccounts.Checked = False
        Else
            chkAccounts.Checked = True
        End If

        Dim ProgramAdditionsMaintenance As String = mykey.GetValue("ProgramAdditionsMaintenance", "NO")
        If ProgramAdditionsMaintenance = "NO" Then
            chkMaintenance.Checked = False
        Else
            chkMaintenance.Checked = True
        End If

        Dim HideProgramNameBillNO As String = mykey.GetValue("HideProgramNameBill", "NO")
        If HideProgramNameBillNO = "NO" Then
            rdoHideProgramNameBillNO.Checked = True
        Else
            rdoHideProgramNameBillYES.Checked = True
        End If

        Dim ActivateOptionsPassword As String = mykey.GetValue("ActivateOptionsPassword", "NO")
        If ActivateOptionsPassword = "NO" Then
            rdoActivateOptionsPasswordNO.Checked = True
        Else
            rdoHideProgramNameBillYES.Checked = True
        End If

        'txtProgramNameBill.Text = mykey.GetValue("ProgramNameBill", "ProgramNameBill", "برنامج اف اى تى سوفت للبرمجيات 01156608276")


        ActionLood = False
        PanelInputBox.Dock = DockStyle.None
        PanelInputBox.Top = 5000
    End Sub

    Private Sub chkEmployees_CheckedChanged(sender As Object, e As EventArgs) Handles chkEmployees.CheckedChanged

        If ActionLood = True Then
            Exit Sub
        End If
        If ReturnChk = True Then
            ReturnChk = False
            Exit Sub
        End If
        If chkEmployees.Checked = False Then
            mykey.SetValue("ProgramAdditionsEmployees", "NO")
            Exit Sub
        End If

        MsgBoxInputBox = "Employees"
        InputBoxPasswordText = "0124363*Employees#"
        PanelInputBox.Location = New System.Drawing.Point(12, 12)
        chkEmployees.Checked = False

    End Sub

    Private Sub chkDelegate_CheckedChanged(sender As Object, e As EventArgs) Handles chkDelegate.CheckedChanged
        If ActionLood = True Then
            Exit Sub
        End If
        If ReturnChk = True Then
            ReturnChk = False
            Exit Sub
        End If
        If chkDelegate.Checked = False Then
            mykey.SetValue("ProgramAdditionsDelegate", "NO")
            Exit Sub
        End If

        MsgBoxInputBox = "Delegate"
        InputBoxPasswordText = "0124363*Delegate#"
        PanelInputBox.Location = New System.Drawing.Point(12, 12)
        chkDelegate.Checked = False

        'Dim MyString As String
        'MyString = InputBox("من فضلك أدخل كلمة المرور", "طلب معلومات", Nothing)

        'If MyString = "0124363*Delegate#" Then
        '    If chkDelegate.Checked = True Then
        '        mykey.SetValue("ProgramAdditionsDelegate", "YES")
        '    End If
        'Else
        '    ReturnChk = True
        '    MsgBox("عفواا كلمة السر خطأ", MsgBoxStyle.Exclamation)
        '    chkDelegate.Checked = False
        'End If
    End Sub

    Private Sub chkManufacturing_CheckedChanged(sender As Object, e As EventArgs) Handles chkManufacturing.CheckedChanged
        If ActionLood = True Then
            Exit Sub
        End If
        If ReturnChk = True Then
            ReturnChk = False
            Exit Sub
        End If
        If chkManufacturing.Checked = False Then
            mykey.SetValue("ProgramAdditionsManufacturing", "NO")
            Exit Sub
        End If

        MsgBoxInputBox = "Manufacturing"
        InputBoxPasswordText = "0124363*Manufacturing#"
        PanelInputBox.Location = New System.Drawing.Point(12, 12)
        chkManufacturing.Checked = False

    End Sub

    Private Sub chkBanks_CheckedChanged(sender As Object, e As EventArgs) Handles chkBanks.CheckedChanged
        If ActionLood = True Then
            Exit Sub
        End If
        If ReturnChk = True Then
            ReturnChk = False
            Exit Sub
        End If
        If chkBanks.Checked = False Then
            mykey.SetValue("ProgramAdditionsBanks", "NO")
            Exit Sub
        End If

        MsgBoxInputBox = "Banks"
        InputBoxPasswordText = "0124363*Banks#"
        PanelInputBox.Location = New System.Drawing.Point(12, 12)
        chkBanks.Checked = False

    End Sub

    Private Sub chkAccounts_CheckedChanged(sender As Object, e As EventArgs) Handles chkAccounts.CheckedChanged
        If ActionLood = True Then
            Exit Sub
        End If
        If ReturnChk = True Then
            ReturnChk = False
            Exit Sub
        End If
        If chkAccounts.Checked = False Then
            mykey.SetValue("ProgramAdditionsAccounts", "NO")
            Exit Sub
        End If

        MsgBoxInputBox = "Accounts"
        InputBoxPasswordText = "0124363*Accounts#"
        PanelInputBox.Location = New System.Drawing.Point(12, 12)
        chkAccounts.Checked = False

    End Sub

    Private Sub chkMaintenance_CheckedChanged(sender As Object, e As EventArgs) Handles chkMaintenance.CheckedChanged
        If ActionLood = True Then
            Exit Sub
        End If
        If ReturnChk = True Then
            ReturnChk = False
            Exit Sub
        End If
        If chkMaintenance.Checked = False Then
            mykey.SetValue("ProgramAdditionsMaintenance", "NO")
            Exit Sub
        End If

        MsgBoxInputBox = "Maintenance"
        InputBoxPasswordText = "0124363*Maintenance#"
        PanelInputBox.Location = New System.Drawing.Point(12, 12)
        chkMaintenance.Checked = False

    End Sub

    Private Sub rdoHideProgramNameBill_NO_CheckedChanged(sender As Object, e As EventArgs) Handles rdoHideProgramNameBillNO.CheckedChanged
        If ActionLood = True Then
            Exit Sub
        End If
        If rdoHideProgramNameBillNO.Checked = True Then
            mykey.SetValue("HideProgramNameBill", "NO")
            HideProgramNameBill = "NO"
        End If
        If rdoHideProgramNameBillYES.Checked = True Then
            mykey.SetValue("HideProgramNameBill", "YES")
            HideProgramNameBill = "YES"
        End If

        'If ReturnChk = True Then
        '    ReturnChk = False
        '    Exit Sub
        'End If
        'If rdoHideProgramNameBillNO.Checked = True Then
        '    mykey.SetValue("HideProgramNameBill", "NO")
        'End If
        'Dim MyString As String
        'MyString = InputBox("من فضلك أدخل كلمة المرور", "طلب معلومات", Nothing)
        'If MyString = "0124363*HideProBill#" Then
        '    If rdoHideProgramNameBillNO.Checked = True Then
        '        mykey.SetValue("HideProgramNameBill", "NO")
        '    End If
        'Else
        '    ReturnChk = True
        '    MsgBox("عفواا كلمة السر خطأ", MsgBoxStyle.Exclamation)
        '    If rdoHideProgramNameBillNO.Checked = False Then
        '        rdoHideProgramNameBillNO.Checked = False
        '    End If
        '    If rdoHideProgramNameBillYES.Checked = False Then
        '        rdoHideProgramNameBillYES.Checked = False
        '    End If
        'End If
    End Sub

    Private Sub rdoHideProgramNameBill_YES_CheckedChanged(sender As Object, e As EventArgs) Handles rdoHideProgramNameBillYES.CheckedChanged
        If ActionLood = True Then
            Exit Sub
        End If
        If rdoHideProgramNameBillNO.Checked = True Then
            mykey.SetValue("HideProgramNameBill", "NO")
            HideProgramNameBill = "NO"
        End If
        If rdoHideProgramNameBillYES.Checked = True Then
            mykey.SetValue("HideProgramNameBill", "YES")
            HideProgramNameBill = "YES"
        End If

        'If ReturnChk = True Then
        '    ReturnChk = False
        '    Exit Sub
        'End If
        'If rdoHideProgramNameBillYES.Checked = True Then
        '    mykey.SetValue("HideProgramNameBill", "YES")
        'End If
        'Dim MyString As String
        'MyString = InputBox("من فضلك أدخل كلمة المرور", "طلب معلومات", Nothing)
        'If MyString = "0124363*HideProBill#" Then
        '    If rdoHideProgramNameBillYES.Checked = True Then
        '        mykey.SetValue("HideProgramNameBill", "YES")
        '    End If
        'Else
        '    ReturnChk = True
        '    MsgBox("عفواا كلمة السر خطأ", MsgBoxStyle.Exclamation)
        '    If rdoHideProgramNameBillNO.Checked = False Then
        '        rdoHideProgramNameBillNO.Checked = False
        '    End If
        '    If rdoHideProgramNameBillYES.Checked = False Then
        '        rdoHideProgramNameBillYES.Checked = False
        '    End If
        'End If
    End Sub

    Private Sub frmProgramAdditions_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        mykey.SetValue("ProgramNameBill", txtProgramNameBill.Text)
        ProgramNameBill = txtProgramNameBill.Text

        ProgramNameBill = ""
        MDIParent1.GetProgramAdditions()
    End Sub

    Private Sub btnCloseInputBox_Click(sender As Object, e As EventArgs) Handles btnCloseInputBox.Click
        PanelInputBox.Dock = DockStyle.None
        PanelInputBox.Top = 5000
    End Sub

    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOK.Click
        If MsgBoxInputBox = "Employees" Then
            If txtPassword.Text = InputBoxPasswordText Then
                InputBoxPasswordText = "" : txtPassword.Text = ""
                mykey.SetValue("ProgramAdditionsEmployees", "YES")
                ReturnChk = True : chkEmployees.Checked = True : PanelInputBox.Top = 5000
            Else
                ReturnChk = True : chkEmployees.Checked = False
                MsgBox("عفواا كلمة السر خطأ", MsgBoxStyle.Exclamation)
            End If
        End If

        If MsgBoxInputBox = "Delegate" Then
            If txtPassword.Text = InputBoxPasswordText Then
                InputBoxPasswordText = "" : txtPassword.Text = ""
                mykey.SetValue("ProgramAdditionsDelegate", "YES")
                ReturnChk = True : chkDelegate.Checked = True : PanelInputBox.Top = 5000
            Else
                ReturnChk = True : chkDelegate.Checked = False
                MsgBox("عفواا كلمة السر خطأ", MsgBoxStyle.Exclamation)
            End If
        End If

        If MsgBoxInputBox = "Manufacturing" Then
            If txtPassword.Text = InputBoxPasswordText Then
                InputBoxPasswordText = "" : txtPassword.Text = ""
                mykey.SetValue("ProgramAdditionsManufacturing", "YES")
                ReturnChk = True : chkManufacturing.Checked = True : PanelInputBox.Top = 5000
            Else
                ReturnChk = True : chkManufacturing.Checked = False
                MsgBox("عفواا كلمة السر خطأ", MsgBoxStyle.Exclamation)
            End If
        End If

        If MsgBoxInputBox = "Banks" Then
            If txtPassword.Text = InputBoxPasswordText Then
                InputBoxPasswordText = "" : txtPassword.Text = ""
                mykey.SetValue("ProgramAdditionsBanks", "YES")
                ReturnChk = True : chkBanks.Checked = True : PanelInputBox.Top = 5000
            Else
                ReturnChk = True : chkBanks.Checked = False
                MsgBox("عفواا كلمة السر خطأ", MsgBoxStyle.Exclamation)
            End If
        End If

        If MsgBoxInputBox = "Accounts" Then
            If txtPassword.Text = InputBoxPasswordText Then
                InputBoxPasswordText = "" : txtPassword.Text = ""
                mykey.SetValue("ProgramAdditionsAccounts", "YES")
                ReturnChk = True : chkAccounts.Checked = True : PanelInputBox.Top = 5000
            Else
                ReturnChk = True : chkAccounts.Checked = False
                MsgBox("عفواا كلمة السر خطأ", MsgBoxStyle.Exclamation)
            End If
        End If

        If MsgBoxInputBox = "Maintenance" Then
            If txtPassword.Text = InputBoxPasswordText Then
                InputBoxPasswordText = "" : txtPassword.Text = ""
                mykey.SetValue("ProgramAdditionsMaintenance", "YES")
                ReturnChk = True : chkMaintenance.Checked = True : PanelInputBox.Top = 5000
            Else
                ReturnChk = True : chkMaintenance.Checked = False
                MsgBox("عفواا كلمة السر خطأ", MsgBoxStyle.Exclamation)
            End If
        End If

    End Sub

    Private Sub txtPassword_KeyUp(sender As Object, e As KeyEventArgs) Handles txtPassword.KeyUp
        If e.KeyCode = 13 Then
            btnOK.PerformClick()
        End If
    End Sub

    Private Sub rdoActivateOptionsPasswordNO_CheckedChanged(sender As Object, e As EventArgs) Handles rdoActivateOptionsPasswordNO.CheckedChanged
        If ActionLood = True Then
            Exit Sub
        End If
        If rdoActivateOptionsPasswordNO.Checked = True Then
            mykey.SetValue("ActivateOptionsPassword", "NO")
            ActivateOptionsPassword = "NO"
        End If
        If rdoActivateOptionsPasswordYES.Checked = True Then
            mykey.SetValue("ActivateOptionsPassword", "YES")
            ActivateOptionsPassword = "YES"
        End If
    End Sub

    Private Sub rdoActivateOptionsPasswordYES_CheckedChanged(sender As Object, e As EventArgs) Handles rdoActivateOptionsPasswordYES.CheckedChanged
        If ActionLood = True Then
            Exit Sub
        End If
        If rdoActivateOptionsPasswordNO.Checked = True Then
            mykey.SetValue("ActivateOptionsPassword", "NO")
            ActivateOptionsPassword = "NO"
        End If
        If rdoActivateOptionsPasswordYES.Checked = True Then
            mykey.SetValue("ActivateOptionsPassword", "YES")
            ActivateOptionsPassword = "YES"
        End If
    End Sub
End Class