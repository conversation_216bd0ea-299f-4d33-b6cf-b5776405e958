﻿Public Class FrmWidthHeightAltitude
    Private Sub FrmWidthHeightAltitude_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        txtWidth.Text = MyVars.txtHeight
        txtHeight.Text = MyVars.txtWidth
        txtAltitude.Text = MyVars.txtAltitude
        txtDensity.Text = MyVars.txtDensity
    End Sub

    Private Sub txtHeight_KeyUp(sender As Object, e As KeyEventArgs) Handles txtHeight.KeyUp
        If e.KeyCode = 13 Then
            txtWidth.Focus()
            txtWidth.SelectAll()
        End If
    End Sub

    Private Sub txtWidth_KeyUp(sender As Object, e As KeyEventArgs) Handles txtWidth.KeyUp
        If e.KeyCode = 13 Then
            txtAltitude.Focus()
            txtAltitude.SelectAll()
        End If
    End Sub

    Private Sub txtAltitude_KeyUp(sender As Object, e As KeyEventArgs) Handles txtAltitude.KeyUp
        If e.KeyCode = 13 Then
            txtDensity.Focus()
            txtDensity.SelectAll()
        End If
    End Sub

    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOK.Click
        MyVars.txtHeight = txtWidth.Text
        MyVars.txtWidth = txtHeight.Text
        MyVars.txtAltitude = txtAltitude.Text
        MyVars.txtDensity = txtDensity.Text
        Me.Close()
    End Sub

    Private Sub chkprintNotInvoiceWidthHeight_CheckedChanged(sender As Object, e As EventArgs) Handles chkprintNotInvoiceWidthHeight.CheckedChanged
        If chkprintNotInvoiceWidthHeight.Checked = True Then
            printNotInvoiceWidthHeight = True
        Else
            printNotInvoiceWidthHeight = False
        End If
    End Sub
End Class