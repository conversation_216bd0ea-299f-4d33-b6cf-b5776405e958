﻿Public Class Frmdecayed
    Dim _totalItem As Integer
    Dim Identity_ As Integer
    Private Sub Frmdecayed_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Cls.fill_combo("vendors", "Vendorname", cmbvendores)
        Bra.Fil("groups", "g_name", cmbcats)
        Cls.fill_combo_Branch("stores", "store", cmbStores)
        MAXRECORD()
        cmbStores.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        GetDateNotBeenActivatedPrograms(DateTimePicker1)
    End Sub

    Private Sub cmbsize_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbStores.KeyUp
        If e.KeyCode = 13 Then
            txtqunt.Focus()
        End If
    End Sub
    Private Sub cmbname_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbname.KeyUp
        If e.KeyCode = 13 Then
            cmbStores.Focus()
        End If
    End Sub
    Private Sub cmbvendores_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbvendores.KeyUp
        If e.KeyCode = 13 Then
            DateTimePicker1.Focus()
        End If
    End Sub
    Private Sub DateTimePicker1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles DateTimePicker1.KeyUp
        If e.KeyCode = 13 Then
            cmbcats.Focus()
        End If
    End Sub
    Private Sub cmbcats_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbcats.KeyUp
        If e.KeyCode = 13 Then
            cmbname.Focus()
        End If
    End Sub
    Private Sub txtprice_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            txtqunt.Focus()
        End If
    End Sub
    Private Sub txtqunt_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub txtqunt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        MyVars.CheckNumber(txtqunt)

        lbltotal.Text = "الإجمالي = " & Val(txtprice.Text) * Val(txtqunt.Text) & " ج"
        _totalItem = Val(txtprice.Text) * Val(txtqunt.Text)
    End Sub
    Private Sub txtprice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        MyVars.CheckNumber(txtprice)

        lbltotal.Text = "الإجمالي = " & Val(txtprice.Text) * Val(txtqunt.Text) & " قرش"
        _totalItem = Val(txtprice.Text) * Val(txtqunt.Text)
    End Sub

    Dim Dt_AddBill As New DataTable
    Friend Function Fn_AddBill(ByVal Col_Prc As String, ByVal Col_Cats As String, ByVal Col_Name As String _
, ByVal Col_Price As Double, ByVal Col_Quant As Double, ByVal Col_Total As Double, ByVal Col_Store As String) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("الباركود", GetType(String))
            Dt_AddBill.Columns.Add("أسم المجموعة", GetType(String))
            Dt_AddBill.Columns.Add("أسم الصنف", GetType(String))
            Dt_AddBill.Columns.Add("السعر", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية", GetType(Double))
            Dt_AddBill.Columns.Add("إجمالي", GetType(Double))
            Dt_AddBill.Columns.Add("المخزن", GetType(String))
        End If

        DTV_Width()

        Dt_AddBill.Rows.Add(Col_Prc, Col_Cats, Col_Name, Col_Price, Col_Quant, Col_Total, Col_Store)
        Return Dt_AddBill
    End Function

    Friend Sub DTV_Width()
        If Dgv_Add.Rows.Count > 10000 Then
            Dgv_Add.Columns(0).Width = 70
            Dgv_Add.Columns(1).Width = 90
            Dgv_Add.Columns(2).Width = 130
            Dgv_Add.Columns(3).Width = 60
            Dgv_Add.Columns(4).Width = 60
            Dgv_Add.Columns(5).Width = 75
            Dgv_Add.Columns(6).Width = 70
        End If
    End Sub


    Private Sub Dgv_Add_CellContentClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs)
        If e.ColumnIndex = 0 Then
            cmbcats.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Cats").Value.ToString
            cmbname.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Name").Value.ToString
            cmbStores.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Size").Value.ToString
            txtprice.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Price").Value
            txtqunt.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Quant").Value
            Txt_TotalBill.Text = Val(Txt_TotalBill.Text) - Dgv_Add.Rows(e.RowIndex).Cells("Col_Total").Value
            Dgv_Add.Rows.RemoveAt(e.RowIndex)
        End If
    End Sub
    Private Sub Button9_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button9.Click

        If ValidateTextSave() = False Then Exit Sub

        Dim tm As String = Cls.get_time(True)
        Dim stat As String
        If CheckBox1.Checked = True Then
            stat = "تم السداد"
        Else
            stat = "لم يتم السداد"
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into decayed(Company_Branch_ID,bill_No,Vendorname,bill_date,billtime,totalpriceafterdisc,Stat,UserName,Treasury_Code) values ("
        S = S & "N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "' ,N'" & cmbvendores.Text.Trim & "' ,N'" & Cls.C_date(DateTimePicker1.Text) & "' ,N'" & Cls.get_time(True) & "' ," & Val(Txt_TotalBill.Text.Trim) & ",N'" & stat & "',N'" & UserName & "',N'" & Treasury_Code & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            S = "insert into data_decayed (Company_Branch_ID,bill_no,itm_id,itm_cat,itm_name,price,qu,totalprice,Stores,bill_date,UserName,Treasury_Code)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Dgv_Add.Rows(i).Cells(3).Value & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & UserName & "',N'" & Treasury_Code & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        IM.CustomerAccountTotal(cmbvendores.Text)

        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            IM.Store(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(6).Value)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update data_decayed set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbillno.Text & "' and itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "' and Stores =N'" & Dgv_Add.Rows(i).Cells(6).Value & "'" : cmd.ExecuteNonQuery()

        Next

        Dt_AddBill.Rows.Clear()
    End Sub
    Private Sub BtnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAdd.Click
        If ValidateTextAdd() = False Then Exit Sub

        IM.Store(txtprc.Text.Trim, cmbStores.Text)

        Dgv_Add.DataSource = Fn_AddBill(txtprc.Text, cmbcats.Text, cmbname.Text, txtprice.Text, txtqunt.Text, Val(txtprice.Text) * Val(txtqunt.Text), cmbStores.Text)
        SumAllPrice()

        'Txt_TotalBill.Text = Val(Txt_TotalBill.Text) + _totalItem

        ClearAdd()

        Cls.clear(GroupBox2)
    End Sub
    Private Sub Btn_Edit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim tm As String = Cls.get_time(True)
        Dim stat_ As String
        If CheckBox1.Checked = True Then
            stat_ = "تم السداد"
        Else
            stat_ = "لم يتم السداد"
        End If
        'MyVars.Sb_Update("Sp_Updatedecayed", "decayedID", Lbl_ID.Text, txtbillno.Text, cmbvendores.Text, DateTimePicker1.Text, tm, Txt_TotalBill.Text, stat_)
        Dim row_count As Integer
        For row_count = 0 To Dgv_Add.Rows.Count - 2
            MyVars.Sb_Updatedata("Sp_Updatedata_decayed", "decayedID", Lbl_ID.Text, Dgv_Add.Rows(row_count).Cells("Col_Cats").Value, _
        Dgv_Add.Rows(row_count).Cells("Col_Name").Value.ToString, Dgv_Add.Rows(row_count).Cells("Col_Size").Value.ToString _
            , Dgv_Add.Rows(row_count).Cells("Col_Price").Value _
               , Dgv_Add.Rows(row_count).Cells("Col_Quant").Value _
                , Dgv_Add.Rows(row_count).Cells("Col_Total").Value, Dgv_Add.Rows(row_count).Cells("Col_Itm_ID").Value)
        Next
        Dt_AddBill.Rows.Clear()
    End Sub

    Private Sub txtbillno_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtbillno.TextChanged
        MyVars.CheckNumber(txtbillno)

    End Sub

    Private Sub cmbname_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbname.SelectedIndexChanged
        If txtprc.Text = "" Then
            If cmbname.Text.Trim = "" Then Exit Sub
            cmbStores.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select Stores from Items where sname =N'" & cmbname.Text.Trim & "' order by 1"
            Else
                cmd.CommandText = "select Stores from Items where sname =N'" & cmbname.Text.Trim & "' where Company_Branch_ID =N'" & Company_Branch_ID & "' order by 1"
            End If
            dr = cmd.ExecuteReader
            Do While dr.Read
                cmbStores.Items.Add(Trim(dr(0)))
            Loop
            'cmbname.Text = ""
        End If
    End Sub

    Private Sub cmbcats_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbcats.SelectedIndexChanged
        If txtprc.Text = "" Then
            If cmbcats.Text.Trim = "" Then Exit Sub
            cmbname.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select distinct sname from Items where group_name =N'" & cmbcats.Text & "' order by 1"
            Else
                cmd.CommandText = "select distinct sname from Items where group_name =N'" & cmbcats.Text & "' and Company_Branch_ID =N'" & Company_Branch_ID & "' order by 1"
            End If
            dr = cmd.ExecuteReader
            Do While dr.Read
                cmbname.Items.Add(Trim(dr(0)))
            Loop
            cmbname.Text = ""
            cmbname.SelectedIndex = -1
        End If
    End Sub

    Private Sub MAXRECORD()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from decayed"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Me.txtbillno.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(bill_No As float)) as mb FROM decayed where bill_No <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            Me.txtbillno.Text = sh + 1
        End If

    End Sub


    Private Sub cmbStores_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbStores.SelectedIndexChanged
        GetDataSales()
    End Sub

    Private Sub GetDataSales()
        If txtprc.Text = "" Then
            Bol = True
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id,SalPrice from items where sname=N'" & cmbname.Text.Trim & "' and Stores =N'" & cmbStores.Text.Trim & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then dr.Close() : Exit Sub
            If dr(0) Is DBNull.Value Then
            Else
                txtprc.Text = dr(0)
            End If
            If dr(1) Is DBNull.Value Then
                txtprice.Text = 0
            Else
                txtprice.Text = dr(1)
            End If

            txtqunt.Focus()
            txtqunt.SelectAll()
            txtqunt.Text = 1

            Bol = False
        End If
    End Sub

    Function ValidateTextAdd() As Boolean
        If cmbvendores.Text = "" Then MsgBox("فضلا أختر المورد", MsgBoxStyle.Exclamation) : cmbvendores.Focus() : Return False
        If txtprc.Text = "" Then MsgBox("فضلا ادخل الباركود", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
        If txtbillno.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الفاتورة", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        If cmbcats.Text = "" Then MsgBox("فضلا حدد المجموعة", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbname.Text = "" Then MsgBox("فضلا أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbname.Focus() : Return False
        If cmbStores.Text = "" Then MsgBox("من فضلك اختر المخزن المباع منه", MsgBoxStyle.Exclamation) : cmbStores.Focus() : Return False
        If Val(txtqunt.Text.Trim) = 0 Then MsgBox("فضلا أدخل الكمية ", MsgBoxStyle.Exclamation) : txtqunt.Focus() : Return False
        If Val(txtprice.Text.Trim) = 0 Then MsgBox("فضلا أدخل السعر", MsgBoxStyle.Exclamation) : txtprice.Focus() : Return False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from decayed where bill_no =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H > 0 Then
            MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        End If
        For i As Integer = 0 To Dt_AddBill.Rows.Count - 1

            If Dgv_Add.Rows(i).Cells(0).Value = txtprc.Text.Trim Then MsgBox("صنف مكرر بنفس الفاتورة", MsgBoxStyle.Exclamation) : txtprc.Focus() : txtprc.SelectAll() : Return False
        Next

        'Dim XMSG As String
        'Dim Xstore As Double = IM.Get_Itm_Store(txtprc.Text.Trim)

        'Dim UseOnlySales As String = mykey.GetValue("UseOnlySales", "NO")
        'If UseOnlySales <> "YES" Then
        '    If Val(txtqunt.Text) > Xstore Then
        '        MsgBox("الكمية بالمخزن لا تكفي الكمية المباعة", MsgBoxStyle.Critical) : Return False
        '    End If
        'End If

        'If Xstore < 1 Then
        '    XMSG = MsgBox("الكمية بالمخزن قد نفذت أو أنك لم تقم بتسجيل آخر عملية مشتريات " & Environment.NewLine & " هل تريد إتمام عملية البيع ؟", MsgBoxStyle.OkCancel + MsgBoxStyle.MsgBoxRight + MsgBoxStyle.Exclamation) : txtprice.Focus()
        '    If XMSG = vbCancel Then Return False
        'End If


        'Dim XMSG2 As String
        'If Xstore - Val(txtqunt.Text) = 0 Then
        '    XMSG2 = MsgBox("الكمية بالمخزن ستنفذ هل تريد الأستمرار", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        '    If XMSG2 = vbCancel Then Return False
        'End If

        'If Xstore - Val(txtqunt.Text) < IM.Get_Itm_Range(txtprc.Text.Trim) Then
        '    XMSG = MsgBox("الكمية بالمخزن قد وصلت للحد الأدنى", MsgBoxStyle.Information) : txtprice.Focus()
        'End If

        Return True
    End Function

    Private Sub ClearAdd()
        cmbStores.Text = ""
        cmbcats.Text = ""
        cmbname.Text = ""
        txtqunt.Text = ""
        txtprice.Text = ""
        txtprc.Text = ""
    End Sub

    Private Sub BtnClear_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnClear.Click
        ClearGrid()
        ClearSave()
        MAXRECORD()
        cmbStores.Enabled = True

    End Sub

    Sub ClearGrid()
        Dt_AddBill.Rows.Clear() : ClearSave()
        Exit Sub

        Dgv_Add.DataSource = ""
        Dt_AddBill.Columns.Add("الباركود", GetType(String))
        Dt_AddBill.Columns.Add("المجموعة", GetType(String))
        Dt_AddBill.Columns.Add("الاسم", GetType(String))
        Dt_AddBill.Columns.Add("السعر", GetType(Double))
        Dt_AddBill.Columns.Add("الكمية", GetType(Integer))
        Dt_AddBill.Columns.Add("إجمالي", GetType(Double))
        '  Next
    End Sub

    Private Sub ClearSave()
        GroupBox1.Enabled = True
        cmbvendores.SelectedIndex = -1
        cmbStores.Text = ""
        cmbname.Text = ""
        cmbcats.Text = ""
        txtprice.Text = ""
        txtqunt.Text = ""
        txtbillno.Text = ""
        txtprc.Text = ""
        Txt_TotalBill.Text = "0"
        cmbvendores.Focus()
    End Sub

    Dim RNXD As Integer
    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        For i As Integer = 0 To Dgv_Add.SelectedRows.Count - 1
            RNXD = Dgv_Add.CurrentRow.Index
            Dgv_Add.Rows.RemoveAt(RNXD)
        Next
        SumAllPrice()

    End Sub

    Private Sub SumAllPrice()
        Dim SM As Double
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            SM = SM + Dgv_Add.Rows(i).Cells(5).Value
        Next
        Txt_TotalBill.Text = SM
    End Sub

    Function ValidateTextSave() As Boolean
        If cmbvendores.Text = "" Then MsgBox("فضلا أختر المورد", MsgBoxStyle.Exclamation) : cmbvendores.Focus() : Return False
        If txtbillno.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الفاتورة", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        If Dgv_Add.Rows.Count < 1 Then MsgBox("فضلا أكمل بيانات الفاتورة", MsgBoxStyle.Exclamation) : Return False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from decayed where bill_No =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H > 0 Then
            MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        End If
        For i As Integer = 0 To Dt_AddBill.Rows.Count - 1

            If Dgv_Add.Rows(i).Cells(0).Value = txtprc.Text.Trim Then MsgBox("صنف مكرر بنفس الفاتورة", MsgBoxStyle.Exclamation) : txtprc.Focus() : txtprc.SelectAll() : Return False
        Next
        Return True
    End Function

End Class
