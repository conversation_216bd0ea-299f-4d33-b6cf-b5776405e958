﻿'أولا يجب إضافة الاداة 
'System.mangement
'عن طريق 
'Project - > add reference
'ثم في قسم الأستعلامات
'أول سطر أكتب
Imports System.Management
Imports System.Net

Module ModuleCpuserial


    Sub New()
        Dim x As New Cet_Cpu
        If x.ProcessorId.ToLower = "BFEBFBFF00000F33".ToLower Or _
        x.ProcessorId.ToLower = "BFEBFBFF00000F25".ToLower Or _
        x.ProcessorId.ToLower = "BFEBFBFF00000F29".ToLower Or _
        x.ProcessorId.ToLower = "3FEBF9FF00000F24".ToLower Or _
        x.ProcessorId.ToLower = "A7E9F9BF00000695".ToLower Or _
        x.ProcessorId.ToLower = "".ToLower Or _
        x.ProcessorId.ToLower = "".ToLower Or _
        x.ProcessorId.ToLower = "".ToLower Then
        Else
            Application.Exit()
        End If

    End Sub

    Public Class Cet_Cpu
        Dim con As ConnectionOptions = New ConnectionOptions
        Dim machineName As String = System.Environment.MachineName
        Dim mss As ManagementScope = New ManagementScope("\\" + machineName + "\root\cimv2", con)
        Dim oqj As ObjectQuery = New ObjectQuery("SELECT * FROM Win32_processor")
        Dim mos As ManagementObjectSearcher = New ManagementObjectSearcher(mss, oqj)
        Dim queryCollection As ManagementObjectCollection = mos.Get

        Public Function ProcessorId() As String
            On Error Resume Next
            ProcessorId = ""
            For Each getnow As ManagementObject In queryCollection
                ProcessorId = getnow("ProcessorId").ToString
            Next
        End Function
    End Class

    Public Class Cet_SerialNumber_HDD
        Public Function SerialNumber_HDD() As String
            On Error Resume Next
            SerialNumber_HDD = ""
            Dim hd As New ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMedia")
            For Each dvs As ManagementObject In hd.Get()
                SerialNumber_HDD = dvs("SerialNumber").ToString()
                Dim split As String() = New String() {" "}
                Dim itemsSplit As String() = SerialNumber_HDD.Split(split, StringSplitOptions.None)
                Dim PathSNHDD As String = itemsSplit(6).ToString()
                mykey.SetValue("SerialNumberHDD", PathSNHDD)
                SerialNumber_HDD = PathSNHDD
                GoTo 1
            Next
1:
        End Function
    End Class


    'End Class

End Module
