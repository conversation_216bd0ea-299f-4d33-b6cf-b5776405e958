﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FrmSyncDatabase
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.btnSyncToServer = New System.Windows.Forms.Button()
        Me.btnSyncFromServer = New System.Windows.Forms.Button()
        Me.txtLocalConnectionString = New System.Windows.Forms.TextBox()
        Me.txtServerConnectionString = New System.Windows.Forms.TextBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.btnFlashSyncToServer = New System.Windows.Forms.Button()
        Me.SuspendLayout()
        '
        'btnSyncToServer
        '
        Me.btnSyncToServer.Font = New System.Drawing.Font("JF Flat", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnSyncToServer.Location = New System.Drawing.Point(760, 309)
        Me.btnSyncToServer.Name = "btnSyncToServer"
        Me.btnSyncToServer.Size = New System.Drawing.Size(328, 92)
        Me.btnSyncToServer.TabIndex = 0
        Me.btnSyncToServer.Text = "مزامنة من الجهاز الى السيرفر"
        Me.btnSyncToServer.UseVisualStyleBackColor = True
        '
        'btnSyncFromServer
        '
        Me.btnSyncFromServer.Font = New System.Drawing.Font("JF Flat", 12.0!, System.Drawing.FontStyle.Bold)
        Me.btnSyncFromServer.Location = New System.Drawing.Point(48, 309)
        Me.btnSyncFromServer.Name = "btnSyncFromServer"
        Me.btnSyncFromServer.Size = New System.Drawing.Size(308, 92)
        Me.btnSyncFromServer.TabIndex = 1
        Me.btnSyncFromServer.Text = "مزامنة من السيرفر الى الجهاز"
        Me.btnSyncFromServer.UseVisualStyleBackColor = True
        '
        'txtLocalConnectionString
        '
        Me.txtLocalConnectionString.Font = New System.Drawing.Font("Tahoma", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtLocalConnectionString.Location = New System.Drawing.Point(48, 80)
        Me.txtLocalConnectionString.Name = "txtLocalConnectionString"
        Me.txtLocalConnectionString.Size = New System.Drawing.Size(1040, 32)
        Me.txtLocalConnectionString.TabIndex = 2
        Me.txtLocalConnectionString.Text = "server=YASSER-PC;database=DatabaseElectric;integrated security = true;"
        '
        'txtServerConnectionString
        '
        Me.txtServerConnectionString.Font = New System.Drawing.Font("Tahoma", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtServerConnectionString.Location = New System.Drawing.Point(48, 185)
        Me.txtServerConnectionString.Name = "txtServerConnectionString"
        Me.txtServerConnectionString.Size = New System.Drawing.Size(1040, 32)
        Me.txtServerConnectionString.TabIndex = 3
        Me.txtServerConnectionString.Text = "server=YASSER-PC;database=DatabaseElectricSERVER;integrated security = true;"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Tahoma", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.Location = New System.Drawing.Point(44, 42)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(219, 24)
        Me.Label1.TabIndex = 4
        Me.Label1.Text = "Local Connection String"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Tahoma", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.Location = New System.Drawing.Point(44, 158)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(230, 24)
        Me.Label2.TabIndex = 5
        Me.Label2.Text = "Server Connection String"
        '
        'btnFlashSyncToServer
        '
        Me.btnFlashSyncToServer.Font = New System.Drawing.Font("JF Flat", 12.0!, System.Drawing.FontStyle.Bold)
        Me.btnFlashSyncToServer.Location = New System.Drawing.Point(417, 309)
        Me.btnFlashSyncToServer.Name = "btnFlashSyncToServer"
        Me.btnFlashSyncToServer.Size = New System.Drawing.Size(284, 92)
        Me.btnFlashSyncToServer.TabIndex = 6
        Me.btnFlashSyncToServer.Text = "مزامنة عادلة"
        Me.btnFlashSyncToServer.UseVisualStyleBackColor = True
        '
        'FrmSyncDatabase
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1146, 450)
        Me.Controls.Add(Me.btnFlashSyncToServer)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.txtServerConnectionString)
        Me.Controls.Add(Me.txtLocalConnectionString)
        Me.Controls.Add(Me.btnSyncFromServer)
        Me.Controls.Add(Me.btnSyncToServer)
        Me.Name = "FrmSyncDatabase"
        Me.Text = "FrmSyncDatabase"
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents btnSyncToServer As Button
    Friend WithEvents btnSyncFromServer As Button
    Friend WithEvents txtLocalConnectionString As TextBox
    Friend WithEvents txtServerConnectionString As TextBox
    Friend WithEvents Label1 As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents btnFlashSyncToServer As Button
End Class
