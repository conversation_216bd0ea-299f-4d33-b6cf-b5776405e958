﻿Imports CrystalDecisions.CrystalReports.Engine

Public Class FrmMaintenanceTrakingMation

    Dim Dt_AddBill As New DataTable
    Dim Dt_AddBill_PriceOffer As New DataTable
    Dim Dt_AddBill_ReceivingCar As New DataTable
    Dim WithEvents BS As New BindingSource
    Dim CountCloseBill As Integer
    Dim SelectTypeMaintenanceDeviceCar As String = mykey.GetValue("SelectTypeMaintenanceDeviceCar", "مركز صيانة السيارات")
    Dim AddressCust, TelCust, MobileCust, RegionCust, MarkCust As String
    Dim ActiveSpace As Boolean = False
    Dim AddItemsID As String

    Private Sub FrmTrakingMation_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Bra.Fil("Customers", "Vendorname", cmbCustomerFind)
        Bra.Fil("MaintenanceTypeProduct", "TypeProduct_Name", cmbTypeProductFind)
        Cls.fill_combo_Branch("Maintenance_Car_Data", "Car_Number", cmbCarNumberFind)
        Cls.fill_combo("stores", "store", cmbStores)
        cmbStores.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        Cls.FillComboDataAdapterQuickSearch("Items", "sname", cmbname)

        Cls.fill_combo("MaintenanceDeviceBrand", "DeviceBrand_Name", cmbDeviceBrandView)
        PanelView.Top = 5000
        PanelRequiredRepair.Top = 5000
        PanelSaveEnd.Top = 5000
        PanelReceivingCar.Top = 5000
        PanelDetailed.Dock = DockStyle.Fill
        ShowSalesTax()
        SelectTypeDeviceCar()
        MaintenanceUnregisteredItems()
        TypeMaintenanceDeviceCar()
    End Sub

    Private Sub TypeMaintenanceDeviceCar()
        If SelectTypeMaintenanceDeviceCar = "مركز صيانة السيارات" Then
            lbllblTypeProductView.Text = "نوع المركبة"
            lblCarNumber.Text = "رقم السيارة"
            lblDeviceBrandView.Text = "الماركة"
        End If
        If SelectTypeMaintenanceDeviceCar = "مركز صيانة الاجهزة" Then
            lbllblTypeProductView.Text = "نوع الجهاز"
            lblCarNumber.Text = "سيريال الجهاز"
            lblDeviceBrandView.Text = "ماركة الجهاز"
        End If
    End Sub

    Private Sub MaintenanceUnregisteredItems()
        Dim MaintenanceUnregisteredItems As String = mykey.GetValue("MaintenanceUnregisteredItems", "NO")
        If MaintenanceUnregisteredItems = "NO" Then
            rdoUnregisteredItems.Checked = True
        End If
        If MaintenanceUnregisteredItems = "YES" Then
            rdoRegisteredItems.Checked = True
        End If
    End Sub


    Private Sub SelectTypeDeviceCar()
        If SelectTypeMaintenanceDeviceCar = "NULL" Then
            MsgBox("يجب الذهاب لشاشة الاعدادات لضبط تحديد بيئة العمل داخل الصيانة", MsgBoxStyle.Exclamation)
            Me.Close()
        End If
        'If SelectTypeMaintenanceDeviceCar = "CarMaintenance" Then
        '    lblTypeProduct.Text = "نوع المركبة"
        '    lbllblTypeProductView.Text = "نوع المركبة"
        '    lblOrderID.Text = "رقم الاذن"
        '    lblOrderIDView.Text = "رقم الاذن"
        '    lblVisit_Date.Visible = False
        '    dtpVisit_Date.Visible = False
        '    PanelCarMaintenance.Visible = True
        '    PanelDevicesMaintenance.Visible = False
        '    PanelCarMaintenance.Location = New System.Drawing.Point(679, 109)
        '    cmbCustomer.Location = New System.Drawing.Point(409, 85)
        '    cmbCustomer.Size = New System.Drawing.Size(302, 29)
        '    lblTypeProduct.Location = New System.Drawing.Point(294, 58)
        '    cmbTypeProduct.Location = New System.Drawing.Point(262, 86)
        'End If
        'If SelectTypeMaintenanceDeviceCar = "DevicesMaintenance" Then
        '    lblTypeProduct.Text = "نوع الجهاز"
        '    lbllblTypeProductView.Text = "نوع الجهاز"
        '    lblOrderID.Text = "رقم البلاغ"
        '    lblOrderIDView.Text = "رقم البلاغ"
        '    lblVisit_Date.Visible = True
        '    dtpVisit_Date.Visible = True
        '    lblCarNumber.Visible = False
        '    cmbCarNumberFind.Visible = False
        '    PanelDevicesMaintenance.Visible = True
        '    PanelCarMaintenance.Visible = False
        '    PanelDevicesMaintenance.Location = New System.Drawing.Point(679, 109)
        'End If
    End Sub


    Private Sub chkDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkDate.CheckedChanged
        If chkDate.Checked = True Then
            dtpFrom.Enabled = False
            dtpTo.Enabled = False
        Else
            dtpFrom.Enabled = True
            dtpTo.Enabled = True
        End If
    End Sub

    Sub GetData()
        'If SelectTypeMaintenanceDeviceCar = "CarMaintenance" Then
        'End If
        'If SelectTypeMaintenanceDeviceCar = "DevicesMaintenance" Then
        '    S = "SELECT OrderID as [رقم البلاغ],Vendorname AS [أسم العميل],OrderDate as [تاريخ الاستلام],Visit_Date as [تاريخ الزيارة],Delivery_Date as [تاريخ التسليم], TimeAMPM AS [الوقت],DeviceBrand_Name as [الماركة],DeviceModel_Name as [الموديل], TypeProduct_Name AS [نوع الجهاز],Supervisor as [المشرف على الاصلاح],Recipient as [المستلم],MaintenanceStatus_Name as [حالة الطلب] FROM View_MaintenanceOrderRunning where OrderDayID <> N''"
        'End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If SelectTypeMaintenanceDeviceCar = "مركز صيانة السيارات" Then
            S = "SELECT OrderID as [رقم الطلب], CarNumber AS [رقم السيارة],Vendorname AS [أسم العميل],OrderDate as [تاريخ الاستلام],Delivery_Date as [تاريخ التسليم],Visit_Date as [تاريخ الزيارة], Driv_Name AS [أسم السائق], KiloMeter AS [الكيلو متر], TimeAMPM AS [الوقت], TypeProduct_Name AS [نوع المركبة], DeviceBrand_Name AS [الماركة], DeviceModel_Name AS [الموديل],Supervisor as [المشرف على الاصلاح],Recipient as [المستلم],MaintenanceStatus_Name as [حالة الطلب],Description as [وصف المركبة] FROM View_MaintenanceOrderRunning where OrderDayID <> N''"
        End If
        If SelectTypeMaintenanceDeviceCar = "مركز صيانة الاجهزة" Then
            S = "SELECT OrderID as [رقم الطلب], CarNumber AS [سيريال الجهاز],Vendorname AS [أسم العميل],OrderDate as [تاريخ الاستلام],Delivery_Date as [تاريخ التسليم],Visit_Date as [تاريخ الزيارة], TimeAMPM AS [الوقت], TypeProduct_Name AS [نوع الجهاز], DeviceBrand_Name AS [ماركة الجهاز], DeviceModel_Name AS [موديل الجهاز],Supervisor as [المشرف على الاصلاح],Recipient as [المستلم],MaintenanceStatus_Name as [حالة الطلب],Description as [وصف الجهاز] FROM View_MaintenanceOrderRunningDevices where OrderDayID <> N''"
        End If

        If chkAll.Checked = False Then
            If cmbCustomerFind.Text <> "" Then
                S = S & " and Vendorname =N'" & cmbCustomerFind.Text.Trim & "'"
            End If
            If cmbTypeProductFind.Text <> "" Then
                S = S & " and TypeProduct_Name =N'" & cmbTypeProductFind.Text.Trim & "'"
            End If
            If cmbCarNumberFind.Text <> "" Then
                S = S & " and CarNumber Like N'%" & cmbCarNumberFind.Text.Trim & "%'"
            End If
            If txtbillnoFind.Text <> "" Then
                S = S & " and bill_No =N'" & txtbillnoFind.Text.Trim & "'"
            End If
            If cmbDeviceBrandView.Text <> "" Then
                S = S & " and DeviceBrand_Name =N'" & cmbDeviceBrandView.Text.Trim & "'"
            End If
            If txtTel.Text <> "" Then
                S = S & " and tel1 =N'" & txtTel.Text.Trim & "'"
            End If
        End If
        If chkStateAll.Checked = False Then
            If rdoReceivingReceipt.Checked = True Then
                S = S & " and MaintenanceStatus_Name =N'" & rdoReceivingReceipt.Text.Trim & "'"
            End If
            If rdomaintenance.Checked = True Then
                S = S & " and MaintenanceStatus_Name =N'" & rdomaintenance.Text.Trim & "'"
            End If
            If rdoSentdelivered.Checked = True Then
                S = S & " and MaintenanceStatus_Name =N'" & rdoSentdelivered.Text.Trim & "'"
            End If
            If rdoSentdelivered.Checked = True Then
                S = S & " and MaintenanceStatus_Name =N'" & rdoSentdelivered.Text.Trim & "'"
            End If
            If rdoReturnsMaintenance.Checked = True Then
                S = S & " and MaintenanceStatus_Name =N'" & rdoReturnsMaintenance.Text.Trim & "'"
            End If
        End If
        If chkDate.Checked = False Then
            S = S & " and OrderDate >=N'" & Cls.C_date(dtpFrom.Text) & "' and OrderDate <=N'" & Cls.C_date(dtpTo.Text) & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم الطلب]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [تاريخ الاستلام]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [أسم العميل]"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr) : dr.Close()


        Dim SM, SM1, SM2 As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(3).Value.ToString)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(3).Value = SM

            SM1 = Val(DataGridView1.Rows(i).Cells(4).Value.ToString)
            SM1 = Cls.R_date(SM1)
            DataGridView1.Rows(i).Cells(4).Value = SM1

            SM2 = Val(DataGridView1.Rows(i).Cells(5).Value.ToString)
            SM2 = Cls.R_date(SM2)
            DataGridView1.Rows(i).Cells(5).Value = SM2
        Next

        'If SelectTypeMaintenanceDeviceCar = "DevicesMaintenance" Then
        '    Dim SM As String
        '    For i As Integer = 0 To DataGridView1.RowCount - 1
        '        SM = Val(DataGridView1.Rows(i).Cells(2).Value)
        '        SM = Cls.R_date(SM)
        '        DataGridView1.Rows(i).Cells(2).Value = SM
        '    Next

        '    Dim SM1 As String
        '    For i As Integer = 0 To DataGridView1.RowCount - 1
        '        SM1 = Val(DataGridView1.Rows(i).Cells(3).Value)
        '        SM1 = Cls.R_date(SM1)
        '        DataGridView1.Rows(i).Cells(3).Value = SM1
        '    Next

        '    Dim SM2 As String
        '    For i As Integer = 0 To DataGridView1.RowCount - 1
        '        SM2 = Val(DataGridView1.Rows(i).Cells(4).Value)
        '        SM2 = Cls.R_date(SM2)
        '        DataGridView1.Rows(i).Cells(4).Value = SM2
        '    Next
        'End If

        txtTotalCount.Text = DataGridView1.RowCount

    End Sub

    Private Sub btnFind_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnFind.Click
        If rdoTable.Checked = True Then
            GetData()
        End If
        If rdoDetailed.Checked = True Then
            If chkStateAll.Checked = True Then
                FillFlowLayoutReceivingReceipt()
                FillFlowLayoutMaintenance()
            Else
                If rdoReceivingReceipt.Checked = True Then
                    FillFlowLayoutReceivingReceipt()
                End If
                If rdomaintenance.Checked = True Then
                    FillFlowLayoutMaintenance()
                End If
            End If
        End If
    End Sub

    Private Sub chkAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbCustomerFind.Enabled = False
            cmbTypeProductFind.Enabled = False
            cmbCarNumberFind.Enabled = False
            cmbDeviceBrandView.Enabled = False
            txtbillnoFind.Enabled = False
            txtTel.Enabled = False
            cmbCustomerFind.SelectedIndex = -1
            cmbTypeProductFind.SelectedIndex = -1
        ElseIf chkAll.Checked = False Then
            cmbCustomerFind.Enabled = True
            cmbTypeProductFind.Enabled = True
            cmbCarNumberFind.Enabled = True
            cmbDeviceBrandView.Enabled = True
            txtTel.Enabled = True
            txtbillnoFind.Enabled = True
            cmbCustomerFind.SelectedIndex = -1
            cmbTypeProductFind.SelectedIndex = -1
        End If
    End Sub

    Private Sub DataGridView1_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DataGridView1.DoubleClick
        Panel29.Dock = DockStyle.None
        Panel30.Dock = DockStyle.None

        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        Dim OrderID As String = DataGridView1.SelectedRows(0).Cells(0).Value.ToString()
        Dim Stat As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select bill_No,Stat from Sales_Bill where OrderID=N'" & OrderID & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            MAXRECORD()
        Else
            txtbillno.Text = dr("bill_No")
            Stat = dr("Stat")
        End If
        If Stat = "نقداً" Then
            ChkCash.Checked = True
        Else
            ChkState.Checked = True
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT  dbo.MaintenanceOrderRunningAdd.OrderAdd_ID AS [رقم], dbo.MaintenanceType.Maintenance_Name AS [نوع الصيانة],  dbo.MaintenanceOrderRunningAdd.RequiredRepair AS [المطلوب إصلاحة], dbo.MaintenanceOrderRunningAdd.OrderID FROM         dbo.MaintenanceOrderRunningAdd INNER JOIN  dbo.MaintenanceType ON dbo.MaintenanceOrderRunningAdd.MaintenanceType_ID = dbo.MaintenanceType.MaintenanceType_ID WHERE     (dbo.MaintenanceOrderRunningAdd.OrderID =N'" & OrderID & "')"
        dr = cmd.ExecuteReader
        DataGridView2.DataSource = Cls.PopulateDataView(dr)
        DataGridView2.Columns(0).Visible = False
        DataGridView2.Columns(3).Visible = False

        'DataGridView2.Columns(1).Width = 50

        'If SelectTypeMaintenanceDeviceCar = "CarMaintenance" Then
        txtOrderID.Text = DataGridView1.SelectedRows(0).Cells(0).Value.ToString()
        txtCarNumber.Text = DataGridView1.SelectedRows(0).Cells(1).Value.ToString()
        cmbCustomer.Text = DataGridView1.SelectedRows(0).Cells(2).Value.ToString()
        cmbDrivers.Text = DataGridView1.SelectedRows(0).Cells(3).Value.ToString()
        txtKiloMeter.Text = DataGridView1.SelectedRows(0).Cells(4).Value.ToString()
        dtpDate.Text = DataGridView1.SelectedRows(0).Cells(5).Value.ToString()
        dtpDelivery_Date.Text = DataGridView1.SelectedRows(0).Cells(6).Value.ToString()
        dtpVisit_Date.Text = DataGridView1.SelectedRows(0).Cells(7).Value.ToString()
        TxtHour.Text = DataGridView1.SelectedRows(0).Cells(8).Value.ToString()
        cmbTypeProduct.Text = DataGridView1.SelectedRows(0).Cells(9).Value.ToString()
        cmbDeviceBrand.Text = DataGridView1.SelectedRows(0).Cells(10).Value.ToString()
        cmbDeviceModel.Text = DataGridView1.SelectedRows(0).Cells(11).Value.ToString()
        txtSupervisor.Text = DataGridView1.SelectedRows(0).Cells(12).Value.ToString()
        txtRecipient.Text = DataGridView1.SelectedRows(0).Cells(13).Value.ToString()
        'End If
        'If SelectTypeMaintenanceDeviceCar = "DevicesMaintenance" Then
        '    txtOrderID.Text = DataGridView1.SelectedRows(0).Cells(0).Value.ToString()
        '    cmbCustomer.Text = DataGridView1.SelectedRows(0).Cells(1).Value.ToString()
        '    dtpDate.Text = DataGridView1.SelectedRows(0).Cells(2).Value.ToString()
        '    dtpVisit_Date.Text = DataGridView1.SelectedRows(0).Cells(3).Value.ToString()
        '    dtpDelivery_Date.Text = DataGridView1.SelectedRows(0).Cells(4).Value.ToString()
        '    TxtHour.Text = DataGridView1.SelectedRows(0).Cells(5).Value.ToString()
        '    cmbDeviceBrand.Text = DataGridView1.SelectedRows(0).Cells(6).Value.ToString()
        '    cmbDeviceModel.Text = DataGridView1.SelectedRows(0).Cells(7).Value.ToString()
        '    cmbTypeProduct.Text = DataGridView1.SelectedRows(0).Cells(8).Value.ToString()
        '    txtSupervisor.Text = DataGridView1.SelectedRows(0).Cells(9).Value.ToString()
        '    txtRecipient.Text = DataGridView1.SelectedRows(0).Cells(10).Value.ToString()
        'End If

        Dt_AddBill.Rows.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "select itm_id,itm_cat,itm_name, Price, qu, totalprice,Stores,TotalExpenses,TotalIncome from BillsalData where OrderID =N'" & txtOrderID.Text & "'" : cmd.CommandText = S : dr = cmd.ExecuteReader
        Do While dr.Read
            Dgv_Add.DataSource = Fn_AddBill(dr(0), dr(1), dr(2), dr(3), dr(4), dr(5), dr(6), "", dr(7), dr(8))
        Loop
        SumAllPrice()

        Try
            Dgv_Add.Columns(2).Width = 230
            Dgv_Add.Columns(7).Visible = False
        Catch ex As Exception

        End Try


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select amnt from Vst_disc where OrderID=N'" & OrderID & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            txtdisc.Text = "0"
        Else
            txtdisc.Text = dr("amnt")
        End If


        If ChkCash.Checked = True Then
            pay = txtpaying.Text
            txtpaying.Text = txttotalafterdisc.Text : txtpaying.Enabled = False
        End If
        If ChkState.Checked = True Then
            txtpaying.Text = pay
            txtpaying.Enabled = True
        End If


        PanelView.Visible = True
        PanelView.Top = 180
        PanelView.Dock = DockStyle.Fill
        Panel27.Top = 5000

        CountCloseBill = Dgv_Add.RowCount

        '=========================================================
        Dim MaintenanceStatus_ID As Integer
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select MaintenanceStatus_ID from MaintenanceOrderRunning where OrderID=N'" & OrderID & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            MaintenanceStatus_ID = dr("MaintenanceStatus_ID")
            If MaintenanceStatus_ID = 4 Then
                chkReturnsMaintenance.Checked = True
            Else
                chkReturnsMaintenance.Checked = False
            End If
        End If

    End Sub

    Friend Function Fn_AddBill(ByVal Col_Prc As String, ByVal Col_Cats As String, ByVal Col_Name As String _
, ByVal Col_Price As Double, ByVal Col_Quant As Double, ByVal Col_Total As Double, ByVal Col_Store As String, ByVal Col_RequiredRepair As String, ByVal Col_TotalExpenses As Double, ByVal Col_TotalIncome As Double) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("الباركود", GetType(String))
            Dt_AddBill.Columns.Add("أسم المجموعة", GetType(String))
            Dt_AddBill.Columns.Add("أسم الصنف", GetType(String))
            Dt_AddBill.Columns.Add("السعر", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية", GetType(Double))
            Dt_AddBill.Columns.Add("إجمالي", GetType(Double))
            Dt_AddBill.Columns.Add("المخزن", GetType(String))
            Dt_AddBill.Columns.Add("المطلوب أصلاحة", GetType(String))
            Dt_AddBill.Columns.Add("المصروفات", GetType(Double))
            Dt_AddBill.Columns.Add("الايرادات", GetType(Double))
        End If

        Dt_AddBill.Rows.Add(Col_Prc, Col_Cats, Col_Name, Col_Price, Col_Quant, Col_Total, Col_Store, Col_RequiredRepair, Col_TotalExpenses, Col_TotalIncome)
        Return Dt_AddBill
    End Function


    Private Sub btnClose_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClose.Click
        If CountCloseBill < Dgv_Add.RowCount Then
            MsgBox("لم تقم بحفظ الاصناف المضافة على الفاتورة من فضلك احفظ الفاتورة أولا ", MsgBoxStyle.Exclamation)
        Else
            PanelView.Visible = False
            PanelView.Dock = DockStyle.None
            PanelView.Top = 5000
        End If
        Panel29.Dock = DockStyle.Top
        Panel30.Dock = DockStyle.Bottom
    End Sub

    Private Sub btnClose2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClose2.Click
        PanelRequiredRepair.Visible = False
        PanelRequiredRepair.Dock = DockStyle.None
        PanelRequiredRepair.Top = 5000
    End Sub

    Private Sub btnRequiredRepair_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRequiredRepair.Click
        PanelRequiredRepair.Visible = True
        PanelRequiredRepair.Dock = DockStyle.Fill
        PanelRequiredRepair.Top = 180
    End Sub

    Private Sub MAXRECORD()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Sales_Bill"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Me.txtbillno.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(bill_No As int)) as mb FROM Sales_Bill where bill_No <> N'جرد'"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            Me.txtbillno.Text = sh + 1

        End If

    End Sub

    Sub ShowSalesTax()
        If ShowValueVAT = "YES" Then
            lblTaxSal.Visible = True
            lblPercent.Visible = False
            txtSalestax.Visible = True
            'txtTotalValueVAT.Visible = True
            Label33.Visible = True
            'lblVAT.Visible = True
        Else
            If ShowTax = "0" Then
                lblTaxSal.Visible = False
                lblPercent.Visible = False
                txtSalestax.Visible = False
                'txtTotalValueVAT.Visible = False
                Label33.Visible = False
                'lblVAT.Visible = False
            Else
                lblTaxSal.Visible = True
                lblPercent.Visible = True
                txtSalestax.Visible = True
                'txtTotalValueVAT.Visible = True
                Label33.Visible = True
                'lblVAT.Visible = True
            End If
        End If
    End Sub


    Private Sub txtParcode_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtprc.KeyUp
        If e.KeyCode = 13 Then
            If txtprc.Text.Trim = "" Then
                BtnSave.PerformClick()
            Else
                Bol = True
                If txtprc.Text.Trim = "" Then Exit Sub
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select group_name , sname,SalPrice,Stores from items where itm_id= N'" & txtprc.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    cmbcats.Text = dr("group_name")
                    cmbname.Text = dr("sname")
                    txtPrice.Text = dr("SalPrice")
                    'cmbStores.Text = dr("Stores")
                End If

1:
                txtqunt.Focus()
                txtqunt.Text = 1
                txtqunt.SelectAll()

                Bol = False
            End If
            Dim X As Double = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text)
            txtCurentStore.Text = X
        End If
    End Sub

    Private Sub GetDataSales()
        Bol = True
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id,group_name,Stores,SalPrice from items where sname=N'" & cmbname.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            txtprc.Text = dr("itm_id")
            cmbcats.Text = dr("group_name")
            txtPrice.Text = dr("SalPrice")
            'cmbStores.Text = dr("Stores")
        End If

        txtCurentStore.Text = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text)
        If rdoUnregisteredItems.Checked = True Then
            txtExpenses.Focus()
        Else
            txtqunt.Focus()
            txtqunt.SelectAll()
        End If
        txtqunt.Text = 1
        Bol = False
    End Sub

    Private Sub btnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAdd.Click
        If ValidateTextAdd() = False Then Exit Sub

        Dim TotalExpenses As Double = 0
        Dim TotalIncome As Double = 0
        If rdoUnregisteredItems.Checked = True Then
            TotalExpenses = Val(txtExpenses.Text) * Val(txtqunt.Text)
            TotalIncome = Val(txtPrice.Text) * Val(txtqunt.Text)
        End If

        If rdoUnregisteredItems.Checked = False Then
            Dim ItmID, Indexqunt As String
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
                ItmID = Dgv_Add.Rows(i).Cells(0).Value
                If txtprc.Text = ItmID Then
                    Indexqunt = Dgv_Add.Rows(i).Cells(4).Value
                    RNXD = Dgv_Add.Rows(i).Cells(0).RowIndex
                    Dgv_Add.Rows.RemoveAt(RNXD)

                    Dim XQunt As String = Val(txtqunt.Text) + Val(Indexqunt)
                    Dim XTotal As String = Val(txtPrice.Text) * Val(XQunt)

                    Dgv_Add.DataSource = Fn_AddBill(txtprc.Text, cmbcats.Text, cmbname.Text, txtPrice.Text, Val(txtqunt.Text) + Val(Indexqunt), XTotal, cmbStores.Text, "", TotalExpenses, TotalIncome)
                    GoTo 2
                End If
            Next
        End If
        Dgv_Add.DataSource = Fn_AddBill(txtprc.Text, cmbcats.Text, cmbname.Text, txtPrice.Text, txtqunt.Text, Val(txtPrice.Text) * Val(txtqunt.Text), cmbStores.Text, "", TotalExpenses, TotalIncome)
2:
        ClearAdd() : SumAllPrice() : FocusText()

        Dgv_Add.Columns(2).Width = 230
        Dgv_Add.Columns(7).Visible = False
        Dgv_Add.Columns(0).ReadOnly = True
        Dgv_Add.Columns(1).ReadOnly = True
        Dgv_Add.Columns(2).ReadOnly = True
        Dgv_Add.Columns(3).ReadOnly = False
        Dgv_Add.Columns(4).ReadOnly = False
        Dgv_Add.Columns(5).ReadOnly = True
        Dgv_Add.Columns(6).ReadOnly = True
        sumdisc()
    End Sub

    Private Sub SumAllPrice()
        Dim SM As Double
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            SM = SM + Dgv_Add.Rows(i).Cells(5).Value
        Next
        txttotalpeforedisc.Text = SM
    End Sub

    Private Sub FocusText()
        Dim FocusText As String = mykey.GetValue("FocusText", "NO")
        If FocusText = "YES" Then
            txtprc.Focus()
        Else
            cmbname.Focus()
        End If
    End Sub

    Private Sub ClearAdd()
        cmbcats.Text = ""
        cmbname.Text = ""
        txtqunt.Text = ""
        txtPrice.Text = ""
        txtprc.Text = ""
        txtTotalPrice.Text = ""
        txtCurentStore.Text = ""
        PictureBox1.Image = Nothing
    End Sub

    Private Sub sumdisc()

        Dim DiscVal, TaxVal, TotalVal As Double


        If ChkCent.Checked = True Then
            DiscVal = Val((Val(txttotalpeforedisc.Text) * (100 - Val(txtdisc.Text))) / 100)
            DiscVal = Math.Round(DiscVal, 4)
        ElseIf ChkVal.Checked = True Then
            DiscVal = Val(txttotalpeforedisc.Text) - Val(txtdisc.Text)
        End If
        TotalVal = DiscVal
        txttotalafterdisc.Text = TotalVal


        TaxVal = Format(Val(txttotalafterdisc.Text) * Val(txtSalestax.Text) / 100, "Fixed")

        If ChkState.Checked = True Then
            txtstaying.Text = Val(txttotalafterdisc.Text) - Val(txtpaying.Text) + TaxVal
        Else
            txtstaying.Text = "0"
        End If
        If txtstaying.Text = "0" Then
            txtpaying.Text = Val(txttotalafterdisc.Text) - Val(txtstaying.Text) + TaxVal
        End If

    End Sub


    Function ValidateTextAdd() As Boolean
        If txtbillno.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الفاتورة", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        If cmbCustomer.Text = "" Then MsgBox("فضلا أختر العميل", MsgBoxStyle.Exclamation) : cmbCustomer.Focus() : Return False
        If rdoRegisteredItems.Checked = True Then
            If txtprc.Text = "" Then MsgBox("فضلا ادخل الباركود", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
            If cmbcats.Text = "" Then MsgBox("فضلا حدد المجموعة", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
            If cmbStores.Text = "" Then MsgBox("من فضلك اختر المخزن المباع منه", MsgBoxStyle.Exclamation) : cmbStores.Focus() : Return False
        End If
        If cmbname.Text = "" Then MsgBox("فضلا أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbname.Focus() : Return False
        If Val(txtqunt.Text.Trim) = 0 Then MsgBox("فضلا أدخل الكمية ", MsgBoxStyle.Exclamation) : txtqunt.Focus() : Return False
        If Val(txtPrice.Text.Trim) = 0 Then MsgBox("فضلا أدخل السعر", MsgBoxStyle.Exclamation) : txtPrice.Focus() : Return False

        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select count(*) from Sales_Bill where bill_no =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
        'If H > 0 Then
        '    MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        'End If

        If rdoUnregisteredItems.Checked = False Then

            Dim TinPrice As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TinPrice from items where  itm_id =N'" & txtprc.Text.Trim & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then TinPrice = 0 Else TinPrice = dr(0)

            If Val(txtPrice.Text) < TinPrice Then
                MsgBox("سعر الشراء أكبر من سعر البيع لا يمكن أتمام عملية البيع", MsgBoxStyle.Exclamation) : txtPrice.Focus() : Return False
            End If


            If rdoRegisteredItems.Checked = True Then
                Dim XMSG As String
                Dim Xstore As Double = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text)
                Dim IncreaseQuantity As String = mykey.GetValue("IncreaseQuantity", "NO")
                If IncreaseQuantity <> "YES" Then
                    If Val(txtqunt.Text) > Xstore Then
                        MsgBox("الكمية بالمخزن لا تكفي الكمية المباعة", MsgBoxStyle.Critical) : Return False
                    End If
                End If

                If Xstore < 1 Then
                    XMSG = MsgBox("الكمية بالمخزن قد نفذت أو أنك لم تقم بتسجيل آخر عملية مشتريات " & Environment.NewLine & " هل تريد إتمام عملية البيع ؟", MsgBoxStyle.OkCancel + MsgBoxStyle.MsgBoxRight + MsgBoxStyle.Exclamation) : txtPrice.Focus()
                    If XMSG = vbCancel Then Return False
                End If


                Dim XMSG2 As String
                If Xstore - Val(txtqunt.Text) = 0 Then
                    XMSG2 = MsgBox("الكمية بالمخزن ستنفذ هل تريد الأستمرار", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
                    If XMSG2 = vbCancel Then Return False
                End If

                If Xstore - Val(txtqunt.Text) < IM.Get_Itm_Range(txtprc.Text.Trim, cmbStores.Text) Then
                    XMSG = MsgBox("الكمية بالمخزن قد وصلت للحد الأدنى", MsgBoxStyle.Information) : txtPrice.Focus()
                End If
            End If
        End If
        Return True
    End Function


    Private Sub cmbNameItems_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbname.SelectedIndexChanged
        If txtprc.Text = "" Then
            GetDataSales()
        End If
    End Sub

    Private Sub cmbNameItems_DropDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbname.DropDown
        cmbname.Text = ""
        cmbcats.Text = ""
        txtqunt.Text = ""
        txtprc.Text = ""
        txtPrice.Text = ""
        txtCurentStore.Text = ""
        txtTotalPrice.Text = ""
    End Sub

    Private Sub txtPrice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtPrice.TextChanged
        MyVars.CheckNumber(txtPrice)
        If rdoUnregisteredItems.Checked = True Then
            txtTotalPrice.Text = Val(txtPrice.Text) + Val(txtExpenses.Text) * Val(txtqunt.Text)
        Else
            txtTotalPrice.Text = Val(txtPrice.Text) * Val(txtqunt.Text)
        End If
    End Sub

    Private Sub txtqunt_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtqunt.KeyUp
        If e.KeyCode = 13 Then
            btnAdd.PerformClick()
        End If
    End Sub

    Private Sub txtqunt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtqunt.TextChanged
        MyVars.CheckNumber(txtqunt)
        txtTotalPrice.Text = Val(txtPrice.Text) * Val(txtqunt.Text)
    End Sub

    Private Sub txttotalpeforedisc_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txttotalpeforedisc.TextChanged
        sumdisc()
    End Sub

    Private Sub txtdisc_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtdisc.TextChanged
        MyVars.CheckNumber(txtdisc)
        sumdisc()
    End Sub

    Private Sub txtpaying_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtpaying.TextChanged
        sumdisc()
    End Sub

    Private Sub ChkVal_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkVal.CheckedChanged
        sumdisc()
    End Sub

    Private Sub ChkCent_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkCent.CheckedChanged
        sumdisc()
    End Sub

    Dim RNXD As Integer
    Private Sub btnDeleteBill_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDeleteBill.Click
        If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        RNXD = Dgv_Add.CurrentRow.Index
        Dgv_Add.Rows.RemoveAt(RNXD)
        SumAllPrice() : sumdisc()
    End Sub

    Private Sub BtnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnSave.Click
        Saveing()

        '==========================================================

        IM.CustomerAccountTotal(cmbCustomer.Text)
        Dt_AddBill.Rows.Clear()
        ClearSave()
        MAXRECORD()
        cmbStores.Enabled = True

        PanelView.Visible = False
        PanelView.Dock = DockStyle.None
        PanelView.Top = 5000

        Panel29.Dock = DockStyle.Top
        Panel30.Dock = DockStyle.Bottom
        '==========================================================

        btnFind_Click(sender, e)
    End Sub

    Private Sub ClearSave()
        cmbname.Text = ""
        cmbcats.Text = ""
        txtPrice.Text = ""
        txtqunt.Text = ""
        txtbillno.Text = ""
        txtprc.Text = ""
        cmbDrivers.Text = ""
        txtCarNumber.Text = ""
        txtKiloMeter.Text = ""
        txtRecipient.Text = ""
        txtSupervisor.Text = ""
        txttotalafterdisc.Text = "0"
        txttotalpeforedisc.Text = "0"
        txtdisc.Text = "0"
        txtpaying.Text = "0"
        txtprc.Focus()
    End Sub

    Private Sub UpdatePriceItems()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            cmd.CommandText = "update items set SalPrice = " & Val(Dgv_Add.Rows(i).Cells(3).Value) & " where itm_id =N'" & Dgv_Add.Rows(i).Cells(0).Value & "'" : cmd.ExecuteNonQuery()
        Next
    End Sub

    Function ValidateTextSave() As Boolean
        If cmbCustomer.Text = "كاش" Then
            MsgBox("فضلا أدخل أسم العميل", MsgBoxStyle.Exclamation) : cmbCustomer.Focus() : Exit Function
        End If
        If cmbCustomer.Text = "" Then MsgBox("فضلا أختر العميل", MsgBoxStyle.Exclamation) : cmbCustomer.Focus() : Return False
        If txtbillno.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الفاتورة", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        If txtdisc.Text = "" Then
            txtdisc.Text = "0"
        End If
        If txtpaying.Text = "" Then
            txtpaying.Text = "0"
        End If
        If Dgv_Add.Rows.Count < 1 Then MsgBox("فضلا أكمل بيانات الفاتورة", MsgBoxStyle.Exclamation) : Return False

        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select count(*) from Sales_Bill where bill_no =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
        'If H > 0 Then
        '    MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        'End If

        Return True
    End Function
    Dim pay As String
    Private Sub ChkCash_CheckedChanged(sender As Object, e As EventArgs) Handles ChkCash.CheckedChanged
        If ChkCash.Checked = True Then
            pay = txtpaying.Text
            txtpaying.Text = txttotalafterdisc.Text : txtpaying.Enabled = False
        End If
    End Sub

    Private Sub RadioButton1_CheckedChanged(sender As Object, e As EventArgs) Handles ChkState.CheckedChanged
        If ChkState.Checked = True Then
            txtpaying.Text = 0
            txtstaying.Text = pay
            txtpaying.Enabled = True
        End If
    End Sub

    Private Sub txtSalestax_TextChanged(sender As Object, e As EventArgs) Handles txtSalestax.TextChanged
        MyVars.CheckNumber(txtSalestax)
        sumdisc()
    End Sub

    Private Sub PrintAll_Click(sender As Object, e As EventArgs) Handles PrintAll.Click
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "Delete from PrintSalesPurchases"
        cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            S = "insert into PrintSalesPurchases(BILL_NO,Vendorname,Driv_Name,Driv_CarNumber,KiloMeter,bill_date,Delivery_Date,billtime,itm_id,itm_cat,itm_name,Unity,TotalafterDisc) values ("
            S = S & "N'" & DataGridView1.Rows(i).Cells(0).Value & "' ,N'" & DataGridView1.Rows(i).Cells(1).Value & "' ,N'" & DataGridView1.Rows(i).Cells(2).Value & "' ,N'" & DataGridView1.Rows(i).Cells(3).Value & "' ,N'" & DataGridView1.Rows(i).Cells(4).Value & "',N'" & DataGridView1.Rows(i).Cells(5).Value & "',"
            S = S & "N'" & DataGridView1.Rows(i).Cells(6).Value & "' ,N'" & DataGridView1.Rows(i).Cells(7).Value & "' ,N'" & DataGridView1.Rows(i).Cells(8).Value & "' ,N'" & DataGridView1.Rows(i).Cells(9).Value & "' ,N'" & DataGridView1.Rows(i).Cells(10).Value & "',N'" & DataGridView1.Rows(i).Cells(11).Value & "',N'" & txtTotalCount.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New Rpt_All_TrakingMation
        Dim txt, txtNameAr, txtNameEn As TextObject

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "Select * from PrintSalesPurchases"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
        txtNameEn.Text = NameEnCompany

        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "متابعة الصيانة"
        Frm_PrintReports.Show()
    End Sub

    Private Sub rdoRegisteredItems_CheckedChanged(sender As Object, e As EventArgs) Handles rdoRegisteredItems.CheckedChanged
        If rdoRegisteredItems.Checked = True Then
            cmbname.DropDownStyle = ComboBoxStyle.DropDown
            cmbname.AutoCompleteMode = AutoCompleteMode.SuggestAppend
            cmbname.AutoCompleteSource = AutoCompleteSource.ListItems
            'cmbname.Size = New System.Drawing.Size(428, 29)
            'cmbname.Location = New System.Drawing.Point(465, 167)
            lblExpenses.Visible = False
            txtExpenses.Visible = False
            lblPrice.Text = "السعر"
        End If
        If rdoUnregisteredItems.Checked = True Then
            cmbname.DropDownStyle = ComboBoxStyle.Simple
            cmbname.AutoCompleteMode = AutoCompleteMode.None
            cmbname.AutoCompleteSource = AutoCompleteSource.None
            'cmbname.Size = New System.Drawing.Size(356, 29)
            'cmbname.Location = New System.Drawing.Point(540, 167)
            lblExpenses.Visible = True
            txtExpenses.Visible = True
            lblPrice.Text = "الايرادات"
        End If

        If rdoUnregisteredItems.Checked = True Then
            mykey.SetValue("MaintenanceUnregisteredItems", "YES")
        ElseIf rdoRegisteredItems.Checked = True Then
            mykey.SetValue("MaintenanceUnregisteredItems", "NO")
        End If

    End Sub

    Private Sub rdoUnregisteredItems_CheckedChanged(sender As Object, e As EventArgs) Handles rdoUnregisteredItems.CheckedChanged
        If rdoRegisteredItems.Checked = True Then
            cmbname.DropDownStyle = ComboBoxStyle.DropDown
            lblPrice.Text = "السعر"
            'cmbname.Size = New System.Drawing.Size(428, 29)
            'cmbname.Location = New System.Drawing.Point(465, 167)
            lblExpenses.Visible = False
            txtExpenses.Visible = False
        End If
        If rdoUnregisteredItems.Checked = True Then
            cmbname.DropDownStyle = ComboBoxStyle.Simple
            'cmbname.Size = New System.Drawing.Size(356, 29)
            'cmbname.Location = New System.Drawing.Point(540, 167)
            lblExpenses.Visible = True
            txtExpenses.Visible = True
            lblPrice.Text = "الايرادات"
        End If
        If rdoUnregisteredItems.Checked = True Then
            mykey.SetValue("MaintenanceUnregisteredItems", "YES")
        ElseIf rdoRegisteredItems.Checked = True Then
            mykey.SetValue("MaintenanceUnregisteredItems", "NO")
        End If
    End Sub

    Private Sub rdoTimesDays_CheckedChanged(sender As Object, e As EventArgs) Handles rdoTimesDays.CheckedChanged
        Header_Times()
    End Sub

    Private Sub PrintReport()
        Dim txtNameAr, txtNameEN, txtNameArDown, txtTitel, txtVisit_Date, txtDeviceModel, txtDeviceBrand, txtTypeProduct, txtWebsite, txtMobile, txtTel, txtTelCust, txtMobileCust, txtRegion, txtMark, txtaddressCust, TextObjectSalestax As TextObject
        Dim txt, txtCmpAddress, txtCmpEmail, txtCmpTel, txtCmpMobile, txtCmpFax As TextObject

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete from PrintSalesPurchases"
        cmd.ExecuteNonQuery()
        Dim RequiredRepair As String = ""
        Dim Num As Long = 0
        Dim SumGrid As Integer = DataGridView2.Rows.Count - Dgv_Add.Rows.Count
3:
        If SumGrid > 0 Then
            If SumGrid <> Num Then
                Dgv_Add.DataSource = Fn_AddBill("", "", "", 0, 0, 0, "", RequiredRepair, 0, 0)
                Num += 1
                GoTo 3
            End If
        End If

        For i As Integer = 0 To DataGridView2.Rows.Count - 1
            RequiredRepair = DataGridView2.Rows(i).Cells(2).Value
            For M As Integer = 0 To Dgv_Add.Rows.Count - 1
                Dgv_Add.Rows(i).Cells(7).Value = RequiredRepair
                GoTo 2
            Next
2:
        Next


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            S = "insert into PrintSalesPurchases(itm_id,itm_cat,itm_name,price,qu,totalprice,bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,bey,STAYING,det,Driv_Name,Driv_CarNumber, KiloMeter, Supervisor_Reform, Recipient, Received_Date, Delivery_Date,TotalCreditor,Details) values ("
            S = S & "N'" & Dgv_Add.Rows(i).Cells(0).Value & "' ,N'" & Dgv_Add.Rows(i).Cells(1).Value & "' ,N'" & Dgv_Add.Rows(i).Cells(2).Value & "' ,N'" & Dgv_Add.Rows(i).Cells(3).Value & "' ,N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',"
            S = S & "N'" & txtbillno.Text.Trim & "' ,N'" & cmbCustomer.Text.Trim & "' ,N'" & dtpDate.Text & "' ,N'" & TxtHour.Text & "' ,N'" & txttotalpeforedisc.Text & "',N'" & txtdisc.Text & "',N'" & txttotalafterdisc.Text & "',N'" & txtpaying.Text & "',N'" & txtstaying.Text & "',N'" & txtSalestax.Text & "',"
            S = S & "N'" & cmbDrivers.Text.Trim & "',N'" & txtCarNumber.Text.Trim & "' ,N'" & txtKiloMeter.Text & "' ,N'" & txtSupervisor.Text & "' ,N'" & txtRecipient.Text & "',N'" & dtpDate.Text & "',N'" & dtpDelivery_Date.Text & "',N'" & cmbTypeProduct.Text & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update PrintSalesPurchases set price =N'',qu =N'',totalprice =N'' where itm_name =N''" : cmd.ExecuteNonQuery()

        AddReportView()
        GetDataCustomer()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select * from PrintSalesPurchases"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        Dim rpt
        If SelectTypeMaintenanceDeviceCar = "DevicesMaintenance" Then
            If chkReturnsMaintenance.Checked = True Then
                rpt = New Rpt_MaintenanceOrderRunningDevicesReceipt
                txtTitel = rpt.Section1.ReportObjects("txtTitel")
                txtTitel.Text = "مرتجع صيانة"
            Else
                rpt = New Rpt_MaintenanceOrderRunningDevicesGuarantee
            End If
        Else

            If txtSalestax.Text = "0" Or txtSalestax.Text = "" Then
                If rdoPrintBill1.Checked = True Then
                    rpt = New Rpt_Sales
                End If
                If rdoPrintBill2.Checked = True Then
                    rpt = New Rpt_Sales_2
                    txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
                    txtNameAr.Text = NameArCompay
                End If
            Else
                If rdoPrintBill1.Checked = True Then
                    rpt = New Rpt_SalesNotTax
                End If
                If rdoPrintBill2.Checked = True Then
                    rpt = New Rpt_SalesNotTax_2
                    txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
                    txtNameAr.Text = NameArCompay
                End If
            End If
        End If

        rpt.SetDataSource(dt)

        'txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
        'txtNameAr.Text = NameArCompay
        'txtNameArDown = rpt.Section1.ReportObjects("txtTitelArDown")
        'txtNameArDown.Text = CMPUnderBILL
        'txtNameEN = rpt.Section1.ReportObjects("txtTitelEn")
        'txtNameEN.Text = NameEnCompany

        'If SelectTypeMaintenanceDeviceCar = "CarMaintenance" Then
        txt = rpt.Section1.ReportObjects("txtprice")
        txt.Text = "فاتورة"
        txtCmpAddress = rpt.Section1.ReportObjects("txtAddress")
        txtCmpAddress.Text = CmpAddress
        txtCmpEmail = rpt.Section1.ReportObjects("txtEmail")
        txtCmpEmail.Text = CmpEmail
        txtCmpTel = rpt.Section1.ReportObjects("txtTel")
        txtCmpTel.Text = CmpTel
        txtCmpMobile = rpt.Section1.ReportObjects("txtMobile")
        txtCmpMobile.Text = CmpMobile
        txtCmpFax = rpt.Section1.ReportObjects("txtFax")
        txtCmpFax.Text = CmpFax
        'End If

        'If SelectTypeMaintenanceDeviceCar = "DevicesMaintenance" Then
        '    txtRegion = rpt.Section1.ReportObjects("txtRegion")
        '    txtRegion.Text = RegionCust
        '    txtTelCust = rpt.Section1.ReportObjects("txtTelCust")
        '    txtTelCust.Text = TelCust
        '    txtMobileCust = rpt.Section1.ReportObjects("txtMobileCust")
        '    txtMobileCust.Text = MobileCust
        '    txtaddressCust = rpt.Section1.ReportObjects("txtaddressCust")
        '    txtaddressCust.Text = AddressCust
        '    txtMark = rpt.Section1.ReportObjects("txtMark")
        '    txtMark.Text = MarkCust
        '    txtVisit_Date = rpt.Section1.ReportObjects("txtVisit_Date")
        '    txtVisit_Date.Text = Cls.C_date(dtpVisit_Date.Text)
        '    txtDeviceModel = rpt.Section1.ReportObjects("txtDeviceModel")
        '    txtDeviceModel.Text = cmbDeviceModel.Text
        '    txtDeviceBrand = rpt.Section1.ReportObjects("txtDeviceBrand")
        '    txtDeviceBrand.Text = cmbDeviceBrand.Text
        '    txtTypeProduct = rpt.Section1.ReportObjects("txtTypeProduct")
        '    txtTypeProduct.Text = cmbTypeProduct.Text
        '    txtWebsite = rpt.Section1.ReportObjects("txtWebsite")
        '    txtWebsite.Text = CMPWebsite
        '    If chkReturnsMaintenance.Checked = False Then
        '        txtMobile = rpt.Section1.ReportObjects("txtMobile")
        '        txtMobile.Text = CmpMobile
        '    Else
        '        txtTel = rpt.Section1.ReportObjects("txtTel")
        '        txtTel.Text = CmpMobile
        '    End If
        'End If
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        If rdoPrintPreview.Checked = True Then
            Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
            Frm_PrintReports.Text = "فاتـــــورة مبــيــعات"
            Frm_PrintReports.Show()
        End If
        If rdoPrintDirect.Checked = True Then
            rpt.PrintToPrinter(1, False, 0, 0)
        End If

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub rdoTimesTomorrw_CheckedChanged(sender As Object, e As EventArgs) Handles rdoTimesTomorrw.CheckedChanged
        Header_Times()
    End Sub

    Private Sub cmbname_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbname.KeyUp
        If e.KeyCode = 13 Then
            If rdoUnregisteredItems.Checked = False Then
                txtPrice.Focus()
            End If
        End If
    End Sub


    Private Sub chkReturnsMaintenance_CheckedChanged(sender As Object, e As EventArgs) Handles chkReturnsMaintenance.CheckedChanged
        If chkReturnsMaintenance.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update MaintenanceOrderRunning set MaintenanceStatus_ID = 4 where OrderID =N'" & txtOrderID.Text & "'" : cmd.ExecuteNonQuery()
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update MaintenanceOrderRunning set MaintenanceStatus_ID = 2 where OrderID =N'" & txtOrderID.Text & "'" : cmd.ExecuteNonQuery()
        End If
    End Sub

    Private Sub RadioButton4_CheckedChanged(sender As Object, e As EventArgs)

    End Sub

    Private Sub txtPrice_KeyUp(sender As Object, e As KeyEventArgs) Handles txtPrice.KeyUp
        If e.KeyCode = 13 Then
            txtqunt.Focus()
        End If
    End Sub

    Private Sub txtFreeTime_TextChanged(sender As Object, e As EventArgs) Handles txtFreeTime.TextChanged
        MyVars.CheckNumber(txtFreeTime)
        Header_Times()
    End Sub

    Private Sub Dgv_Add_CellValueChanged(sender As Object, e As DataGridViewCellEventArgs) Handles Dgv_Add.CellValueChanged
        If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        Dim Total As Double
        Dim Price As Double = Dgv_Add.SelectedRows(0).Cells(3).Value
        Dim Qunt As Double = Dgv_Add.SelectedRows(0).Cells(4).Value
        Total = Val(Price) * Val(Qunt)

        Dgv_Add.SelectedRows(0).Cells(5).Value = Total

        SumAllPrice()
    End Sub

    Private Sub Saveing()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select bill_No from Sales_Bill where OrderID=N'" & txtOrderID.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            MAXRECORD()
        End If

        SumAllPrice()
        If ValidateTextSave() = False Then Exit Sub

        Dim STAT As String
        If ChkCash.Checked = True Then
            STAT = "نقداً"
        End If
        If ChkState.Checked = True Then
            STAT = "آجل"
        End If

        Dim disc_type As String = ""
        If ChkVal.Checked = True Then
            disc_type = ChkVal.Text
        Else
            disc_type = ChkCent.Text
        End If

        Dim OrderIDTrue As Boolean = False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Sales_Bill where OrderID=N'" & txtOrderID.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            OrderIDTrue = False
        Else
            OrderIDTrue = True
        End If


        Dim Cust_id As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Cust_Code from Customers where Vendorname= N'" & cmbCustomer.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Cust_id = dr("Cust_Code").ToString
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update MaintenanceOrderRunning set Customer_ID =N'" & Cust_id & "',OrderDate =N'" & Cls.C_date(dtpDate.Text) & "',UserName =N'" & UserName & "' where OrderID =N'" & txtOrderID.Text & "'" : cmd.ExecuteNonQuery()


        If OrderIDTrue = False Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Sales_Bill(bill_No,OrderID,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,SalesTax,UserName,Driv_Name,Driv_CarNumber, KiloMeter, Supervisor_Reform, Recipient, Received_Date, Delivery_Date,disc_type,Treasury_Code) values ("
            S = S & "N'" & txtbillno.Text.Trim & "' ,N'" & txtOrderID.Text.Trim & "' ,N'" & cmbCustomer.Text.Trim & "' ,N'" & Cls.C_date(dtpDelivery_Date.Text) & "' ,N'" & Cls.get_time(True) & "' ," & Val(txttotalpeforedisc.Text.Trim) & "," & Val(txtdisc.Text.Trim) & " ," & Val(txttotalafterdisc.Text.Trim) & " ,N'" & STAT & "',N'" & txtpaying.Text.Trim & "',N'" & txtstaying.Text.Trim & "',N'" & txtSalestax.Text.Trim & "',N'" & UserName & "',"
            S = S & "N'" & cmbDrivers.Text.Trim & "' ,N'" & txtCarNumber.Text.Trim & "' ,N'" & txtKiloMeter.Text & "' ,N'" & txtSupervisor.Text & "' ,N'" & txtRecipient.Text & "',N'" & Cls.C_date(dtpDate.Text) & "',N'" & Cls.C_date(dtpDelivery_Date.Text) & "',N'" & disc_type & "',N'" & Treasury_Code & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Sales_Bill set bill_No =N'" & txtbillno.Text & "',OrderID =N'" & txtOrderID.Text & "',Vendorname =N'" & cmbCustomer.Text & "',bill_date =N'" & Cls.C_date(dtpDelivery_Date.Text) & "',billtime =N'" & Cls.get_time(True) & "',totalpricebeforedisc =N'" & Val(txttotalpeforedisc.Text.Trim) & "',disc =N'" & txtdisc.Text & "',totalpriceafterdisc =N'" & txttotalafterdisc.Text & "',stat =N'" & STAT & "',bey =N'" & txtpaying.Text & "',STAYING =N'" & txtstaying.Text & "',SalesTax =N'" & txtSalestax.Text & "',UserName =N'" & UserName & "',Driv_Name =N'" & cmbDrivers.Text & "',Driv_CarNumber =N'" & txtCarNumber.Text & "',KiloMeter =N'" & txtKiloMeter.Text & "',Supervisor_Reform =N'" & txtSupervisor.Text & "',Recipient =N'" & txtRecipient.Text & "',Received_Date =N'" & Cls.C_date(dtpDate.Text) & "',Delivery_Date =N'" & Cls.C_date(dtpDelivery_Date.Text) & "',Treasury_Code =N'" & Treasury_Code & "' where OrderID =N'" & txtOrderID.Text & "'" : cmd.ExecuteNonQuery()
        End If


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete From  BillsalData where OrderID =N'" & txtOrderID.Text & "'" : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            S = "insert into BillsalData (bill_no,OrderID,itm_id,itm_cat,itm_name,price,qu,totalprice,Stores,bill_date,UserName,TotalExpenses,TotalIncome,Vendorname,Stat,Treasury_Code)  values("
            S = S & "N'" & txtbillno.Text.Trim & "',N'" & txtOrderID.Text.Trim & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Dgv_Add.Rows(i).Cells(3).Value & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Cls.C_date(dtpDelivery_Date.Text) & "',N'" & UserName & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "',N'" & Dgv_Add.Rows(i).Cells(9).Value & "',N'" & cmbCustomer.Text.Trim & "',N'" & STAT & "',N'" & Treasury_Code & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete From  Expenses where OrderID =N'" & txtOrderID.Text & "'" : cmd.ExecuteNonQuery()

        Dim Category_Expenses As String = "أصناف خارجية"
        Dim Other_Income As String = "ايرادات أخرى"
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select G_name from Cat_Expenses where G_name=N'" & Category_Expenses & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Cat_Expenses(g_name,UserName) values (N'" & Category_Expenses & "',N'" & UserName & "')"
                cmd.CommandText = S
                cmd.ExecuteNonQuery()
            End If

            Dim Income_Category_ID As String = Cls.MAXRECORD("Other_Income_Category", "Income_Category_ID")
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Income_Category_Name from Other_Income_Category where Income_Category_Name=N'" & Other_Income & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Other_Income_Category(Income_Category_ID,Income_Category_Name) values (N'" & Income_Category_ID & "',N'" & Other_Income & "')"
                cmd.CommandText = S
                cmd.ExecuteNonQuery()
            End If

            If Dgv_Add.Rows(i).Cells(0).Value = "" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "Insert into Expenses(OrderID,Exp_Name,Exp_Value,Exp_Date,Exp_Notes,UserName,cats,Treasury_Code) values (N'" & txtOrderID.Text.Trim & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "'," & Dgv_Add.Rows(i).Cells(8).Value & ",N'" & Cls.C_date(dtpDelivery_Date.Text) & "',N'صنف خارجى',N'" & UserName & "',N'" & Category_Expenses & "',N'" & Treasury_Code & "')"
                cmd.CommandText = S
                cmd.ExecuteNonQuery()

                Dim Income_ID As String = Cls.MAXRECORD("Other_Income", "Income_ID")
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "Insert into Other_Income(OrderID,Income_ID,Income_Name,Amount,Income_Date,Note,UserName,Income_Category_ID,Treasury_Code) values (N'" & txtOrderID.Text.Trim & "',N'" & Income_ID & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "'," & Dgv_Add.Rows(i).Cells(9).Value & ",N'" & Cls.C_date(dtpDelivery_Date.Text) & "',N'صنف خارجى',N'" & UserName & "',N'" & Income_Category_ID & "',N'" & Treasury_Code & "')"
                cmd.CommandText = S
                cmd.ExecuteNonQuery()
            End If
        Next

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If OrderIDTrue = False Then
            If Val(txtpaying.Text) > 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into vst (OrderID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,billno,VND_no,UserName,Treasury_Code,CashBank) values"
                S = S & " (N'" & txtOrderID.Text & "',N'" & cmbCustomer.Text & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(dtpDelivery_Date.Text) & "'," & txtpaying.Text.Trim & ",N'بفاتورة',N'بفاتورة',N'بفاتورة',N'" & txtbillno.Text & "',N'دفعة نقدية',N'" & UserName & "',N'" & Treasury_Code & "',0)"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
            If Val(txtdisc.Text) > 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Vst_disc (OrderID,Vendorname,amnt,pdate,VND_XTM,det,TIN_NO,UserName,Treasury_Code) values (N'" & txtOrderID.Text & "',N'" & cmbCustomer.Text.Trim & "'," & Val(txtdisc.Text) & ",N'" & Cls.C_date(dtpDelivery_Date.Text) & "',N'" & Cls.get_time(True) & "',N'خصم على فاتورة مباشرة',N'" & txtbillno.Text.Trim & "',N'" & UserName & "',N'" & Treasury_Code & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        Else
            If Val(txtpaying.Text) > 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update vst set Vendorname =N'" & cmbCustomer.Text & "',VND_XTM =N'" & Cls.get_time(True) & "',VND_dt =N'" & Cls.C_date(dtpDelivery_Date.Text) & "',VND_amx =N'" & txtpaying.Text.Trim & "',UserName =N'" & UserName & "',Treasury_Code =N'" & Treasury_Code & "' where OrderID =N'" & txtOrderID.Text & "'" : cmd.ExecuteNonQuery()
            End If
            If Val(txtdisc.Text) > 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Vst_disc set Vendorname =N'" & cmbCustomer.Text & "',amnt =N'" & Val(txtdisc.Text) & "',pdate =N'" & Cls.C_date(dtpDelivery_Date.Text) & "',UserName =N'" & UserName & "',Treasury_Code =N'" & Treasury_Code & "' where OrderID =N'" & txtOrderID.Text & "'" : cmd.ExecuteNonQuery()
            End If
        End If

        If chkReturnsMaintenance.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update MaintenanceOrderRunning set MaintenanceStatus_ID = 4 where OrderID =N'" & txtOrderID.Text & "'" : cmd.ExecuteNonQuery()
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update MaintenanceOrderRunning set MaintenanceStatus_ID = 2 where OrderID =N'" & txtOrderID.Text & "'" : cmd.ExecuteNonQuery()
        End If

        '===============================================================================
        Dim UpdateSalPrice As String = mykey.GetValue("UpdateSalPrice", "NO")
        If UpdateSalPrice = "YES" Then
            UpdatePriceItems()
        End If
        '===============================================================================

        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            IM.Store(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(6).Value)

            If ConnectOnlineStore = "YES" Then
                EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
                StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
                Cos.UpdateProductStock(StockOnline, Dgv_Add.Rows(i).Cells(0).Value, EditItmId)
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbillno.Text & "' and itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "' and Stores =N'" & Dgv_Add.Rows(i).Cells(6).Value & "'" : cmd.ExecuteNonQuery()

        Next

        Get_Movement_In_Out_Money(dtpDelivery_Date.Text, Treasury_Code)

    End Sub

    Private Sub btnSaveEnd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSaveEnd.Click
        PanelSaveEnd.Location = New System.Drawing.Point(600, 134)
        PanelSaveEnd.Size = New System.Drawing.Size(259, 174)

        PanelSaveEnd.Top = 40
    End Sub

    Private Sub rdoTable_CheckedChanged(sender As Object, e As EventArgs) Handles rdoTable.CheckedChanged
        DataGridView1.Visible = True
        PanelDetailed.Visible = False
    End Sub

    Private Sub rdoDetailed_CheckedChanged(sender As Object, e As EventArgs) Handles rdoDetailed.CheckedChanged
        DataGridView1.Visible = False
        PanelDetailed.Visible = True
    End Sub

    Private Sub chkState_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkStateAll.CheckedChanged
        If chkStateAll.Checked = True Then
            rdoReceivingReceipt.Enabled = False
            rdomaintenance.Enabled = False
            rdoSentdelivered.Enabled = False
            rdoReturnsMaintenance.Enabled = False
        ElseIf chkStateAll.Checked = False Then
            rdoReceivingReceipt.Enabled = True
            rdomaintenance.Enabled = True
            rdoSentdelivered.Enabled = True
            rdoReturnsMaintenance.Enabled = True
        End If
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim OrderID, XDate, NameVendor As String

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            OrderID = DataGridView1.SelectedRows(i).Cells(0).Value
            cmd.CommandText = "delete From  MaintenanceOrderRunning where OrderID =N'" & OrderID & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  MaintenanceOrderRunningAdd where OrderID =N'" & OrderID & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  Sales_Bill where bill_no =N'" & OrderID & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  BillsalData where bill_no =N'" & OrderID & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  Vst_disc where TIN_NO =N'" & OrderID & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  Vst where BillNo =N'" & OrderID & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  Vst where BillNo =N'" & OrderID & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  Expenses where OrderID =N'" & OrderID & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  Other_Income where OrderID =N'" & OrderID & "'" : cmd.ExecuteNonQuery()
            NameVendor = DataGridView1.SelectedRows(i).Cells(1).Value
            IM.CustomerAccountTotal(NameVendor)

            If SelectTypeMaintenanceDeviceCar = "CarMaintenance" Then
                XDate = DataGridView1.SelectedRows(i).Cells(5).Value
            End If
            If SelectTypeMaintenanceDeviceCar = "DevicesMaintenance" Then
                XDate = DataGridView1.SelectedRows(i).Cells(2).Value
            End If
            Get_Movement_In_Out_Money(XDate, Treasury_Code)
        Next
        GetData()
        IM.UpdateDataBase()
    End Sub

    Public Sub Header_Times()
        Dim XXDate As String = DateTime.Now.ToString("yyyy/MM/dd")
        Dim AddDay As DateTime

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If SelectTypeMaintenanceDeviceCar = "CarMaintenance" Then
            S = "SELECT OrderID as [رقم الطلب],Vendorname AS [أسم العميل], Driv_Name AS [أسم السائق], CarNumber AS [رقم السيارة], KiloMeter AS [الكيلو متر],OrderDate as [التاريخ],Delivery_Date as [تاريخ التسليم], TimeAMPM AS [الوقت], TypeProduct_Name AS [نوع المركبة],Supervisor as [المشرف على الاصلاح],Recipient as [المستلم],MaintenanceStatus_Name as [حالة الطلب] FROM View_MaintenanceOrderRunning where OrderDayID <> N''"
        End If
        If SelectTypeMaintenanceDeviceCar = "DevicesMaintenance" Then
            S = "SELECT OrderID as [رقم البلاغ],Vendorname AS [أسم العميل],OrderDate as [تاريخ الاستلام],Visit_Date as [تاريخ الزيارة],Delivery_Date as [تاريخ التسليم], TimeAMPM AS [الوقت],DeviceBrand_Name as [الماركة],DeviceModel_Name as [الموديل], TypeProduct_Name AS [نوع الجهاز],Supervisor as [المشرف على الاصلاح],Recipient as [المستلم],MaintenanceStatus_Name as [حالة الطلب] FROM View_MaintenanceOrderRunning where OrderDayID <> N''"
        End If

        If chkStateAll.Checked = False Then
            If rdoReceivingReceipt.Checked = True Then
                S = S & " and MaintenanceStatus_Name =N'" & rdoReceivingReceipt.Text.Trim & "'"
            End If
            If rdomaintenance.Checked = True Then
                S = S & " and MaintenanceStatus_Name =N'" & rdomaintenance.Text.Trim & "'"
            End If
            If rdoSentdelivered.Checked = True Then
                S = S & " and MaintenanceStatus_Name =N'" & rdoSentdelivered.Text.Trim & "'"
            End If
        End If

        If txtFreeTime.Text = "" Then
            If rdoTimesDays.Checked = True Then
                S = S & " and Delivery_Date =N'" & Cls.C_date(XXDate) & "'"
            ElseIf rdoTimesTomorrw.Checked = True Then
                AddDay = XXDate
                AddDay = AddDay.AddDays(1)
                S = S & " and Delivery_Date >=N'" & Cls.C_date(XXDate) & "' and Delivery_Date <=N'" & Cls.C_date(AddDay) & "'"
            Else
                AddDay = XXDate
                AddDay = AddDay.AddDays(Val(txtFreeTime.Text))
                S = S & " and Delivery_Date >=N'" & Cls.C_date(XXDate) & "' and Delivery_Date <=N'" & Cls.C_date(AddDay) & "'"
            End If
        End If


        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr) : dr.Close()
        DataGridView1.Columns(1).Visible = False

        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(5).Value)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(5).Value = SM
        Next

        Dim SM1 As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM1 = Val(DataGridView1.Rows(i).Cells(6).Value)
            SM1 = Cls.R_date(SM1)
            DataGridView1.Rows(i).Cells(6).Value = SM1
        Next

        txtTotalCount.Text = DataGridView1.RowCount
    End Sub

    Private Sub GetDataCustomer()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Customers where Vendorname=N'" & cmbCustomer.Text & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            AddressCust = dr("addr").ToString
            TelCust = dr("tel1").ToString
            MobileCust = dr("Mobile").ToString
            RegionCust = dr("Region").ToString
            MarkCust = dr("Mark").ToString
        End If
    End Sub

    Private Sub FillFlowLayoutReceivingReceipt()
        Dim FromArgb As String = ""
        FlowLayout_ReceivingReceipt.Controls.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If SelectTypeMaintenanceDeviceCar = "مركز صيانة السيارات" Then
            S = "SELECT OrderID,Vendorname,CarNumber FROM View_MaintenanceOrderRunning where MaintenanceStatus_Name = N'قيد الاستلام'"
        End If
        If SelectTypeMaintenanceDeviceCar = "مركز صيانة الاجهزة" Then
            S = "SELECT OrderID,Vendorname,TypeProduct_Name FROM View_MaintenanceOrderRunningDevices where MaintenanceStatus_Name = N'قيد الاستلام'"
        End If

        If chkAll.Checked = False Then
            If cmbCustomerFind.Text <> "" Then
                S = S & " and Vendorname =N'" & cmbCustomerFind.Text.Trim & "'"
            End If
            If cmbTypeProductFind.Text <> "" Then
                S = S & " and TypeProduct_Name =N'" & cmbTypeProductFind.Text.Trim & "'"
            End If
            If cmbCarNumberFind.Text <> "" Then
                S = S & " and CarNumber Like N'%" & cmbCarNumberFind.Text.Trim & "%'"
            End If
            If txtbillnoFind.Text <> "" Then
                S = S & " and bill_No =N'" & txtbillnoFind.Text.Trim & "'"
            End If
        End If
        If chkDate.Checked = False Then
            S = S & " and OrderDate >=N'" & Cls.C_date(dtpFrom.Text) & "' and OrderDate <=N'" & Cls.C_date(dtpTo.Text) & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by OrderID"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by TypeProduct_Name"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by Vendorname"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        Do While dr.Read
            Dim btn As New Button
            AddHandler btn.Click, AddressOf ClickMe_ReceivingReceipt
            btn.Width = 178
            btn.Height = 125
            btn.FlatStyle = FlatStyle.Flat
            btn.Text = Trim(dr(0).ToString()) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Trim(dr(1).ToString()) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Trim(dr(2).ToString())
            btn.Visible = True
            btn.Tag = Trim(dr(0).ToString())
            FromArgb = Trim(dr(1).ToString())
            btn.BackColor = Color.FromArgb(0, 188, 212)
            btn.ForeColor = Color.White
            btn.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            'set_color_button(btn, FlowLay_Items)
            FlowLayout_ReceivingReceipt.Controls.Add(btn)
        Loop
    End Sub

    Private Sub btnCloseSaveEnd_Click(sender As Object, e As EventArgs) Handles btnCloseSaveEnd.Click
        PanelSaveEnd.Top = 5000
    End Sub

    Private Sub btnSaveEndDate_Click(sender As Object, e As EventArgs) Handles btnSaveEndDate.Click
        Saveing()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update MaintenanceOrderRunning set Delivery_Date =N'" & Cls.C_date(dtpDelivery_Date.Text) & "' where OrderID =N'" & txtOrderID.Text & "'" : cmd.ExecuteNonQuery()

        If chkprint.Checked = True Then
            PrintReport()
        End If

        IM.CustomerAccountTotal(cmbCustomer.Text)
        Dt_AddBill.Rows.Clear()
        ClearSave()
        MAXRECORD()
        cmbStores.Enabled = True

        PanelView.Visible = False
        PanelView.Dock = DockStyle.None
        PanelView.Top = 5000

        Panel29.Dock = DockStyle.Top
        Panel30.Dock = DockStyle.Bottom

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update MaintenanceOrderRunning set MaintenanceStatus_ID = 3 where OrderID =N'" & txtOrderID.Text & "'" : cmd.ExecuteNonQuery()

        btnFind_Click(sender, e)

        PanelSaveEnd.Top = 5000
    End Sub

    Private Sub txtExpenses_KeyUp(sender As Object, e As KeyEventArgs) Handles txtExpenses.KeyUp
        If e.KeyCode = 13 Then
            txtPrice.Focus()
        End If
    End Sub

    Private Sub txtExpenses_TextChanged(sender As Object, e As EventArgs) Handles txtExpenses.TextChanged
        MyVars.CheckNumber(txtExpenses)
        txtTotalPrice.Text = Val(txtPrice.Text) + Val(txtExpenses.Text) * Val(txtqunt.Text)
    End Sub

    Private Sub cmbCarNumberFind_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbCarNumberFind.KeyUp
        If e.KeyCode = 13 Then
            Dim result As String, str As String, ret As String
            Dim i As Integer
            Dim arr As Char()
            result = cmbCarNumberFind.Text
            result = StrReverse(result)
            i = 0
            ret = ""
            ' make a char array which each char is an own array element
            arr = result.Take(result.Length).ToArray
            'iterate through all elements
            For Each str In arr
                ' skip the first element . 
                ' only add a space every 3 elements 
                'If (i <> 0) And (i Mod 3 = 0) Then
                If str <> " " Then
                    ret = ret + " "
                    ret = ret + str
                    i = i + 1
                End If
                'End If
            Next
            ret = StrReverse(ret)
            cmbCarNumberFind.Text = ret.Trim
        End If
    End Sub

    Private Sub ClickMe_ReceivingReceipt(ByVal sender As Object, ByVal e As EventArgs)
        Dim btn As Button
        btn = CType(sender, Button)
        Dim str As String = btn.Tag

        ViewBillSales(str)

    End Sub

    Private Sub btnOrderRunning_Click(sender As Object, e As EventArgs) Handles btnOrderRunning.Click
        PrintReportOrderRunning()
    End Sub

    Private Sub btnPriceOffer_Click(sender As Object, e As EventArgs) Handles btnPriceOffer.Click
        PanelPriceOffer.Visible = True
        PanelPriceOffer.Dock = DockStyle.Fill
        PanelPriceOffer.Top = 180
        GetPriceOffer()
    End Sub

    Private Sub ViewBillSales(ByVal OrderID As String)
        ClearSave()
        Dim Stat As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select bill_No,Stat from Sales_Bill where OrderID=N'" & OrderID & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            MAXRECORD()
        Else
            txtbillno.Text = dr("bill_No")
            Stat = dr("Stat")
        End If
        If Stat = "نقداً" Then
            ChkCash.Checked = True
        Else
            ChkState.Checked = True
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT  dbo.MaintenanceOrderRunningAdd.OrderAdd_ID AS [رقم], dbo.MaintenanceType.Maintenance_Name AS [نوع الصيانة],  dbo.MaintenanceOrderRunningAdd.RequiredRepair AS [المطلوب إصلاحة], dbo.MaintenanceOrderRunningAdd.OrderID FROM         dbo.MaintenanceOrderRunningAdd INNER JOIN  dbo.MaintenanceType ON dbo.MaintenanceOrderRunningAdd.MaintenanceType_ID = dbo.MaintenanceType.MaintenanceType_ID WHERE     (dbo.MaintenanceOrderRunningAdd.OrderID =N'" & OrderID & "')"
        dr = cmd.ExecuteReader
        DataGridView2.DataSource = Cls.PopulateDataView(dr)
        DataGridView2.Columns(0).Visible = False
        DataGridView2.Columns(3).Visible = False

        'DataGridView2.Columns(1).Width = 50
        Dim Favorite As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If SelectTypeMaintenanceDeviceCar = "مركز صيانة السيارات" Then
            cmd.CommandText = "select Vendorname,Driv_Name,CarNumber,KiloMeter,OrderDate,Delivery_Date,TimeAMPM,TypeProduct_Name,Supervisor,Recipient from View_MaintenanceOrderRunning where OrderID=N'" & OrderID & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                txtOrderID.Text = OrderID
                cmbCustomer.Text = dr("Vendorname").ToString
                cmbDrivers.Text = dr("Driv_Name").ToString
                txtCarNumber.Text = dr("CarNumber").ToString
                txtKiloMeter.Text = dr("KiloMeter").ToString
                dtpDate.Text = Cls.R_date(dr("OrderDate").ToString)
                dtpDelivery_Date.Text = Cls.R_date(dr("OrderDate").ToString)
                TxtHour.Text = dr("TimeAMPM").ToString
                cmbTypeProduct.Text = dr("TypeProduct_Name").ToString
                txtSupervisor.Text = dr("Supervisor").ToString
                txtRecipient.Text = dr("Recipient").ToString
                'Favorite = dr("Favorite").ToString
            End If
        End If
        If SelectTypeMaintenanceDeviceCar = "مركز صيانة الاجهزة" Then
            cmd.CommandText = "select Vendorname,CarNumber,OrderDate,Delivery_Date,TimeAMPM,TypeProduct_Name,Supervisor,Recipient from View_MaintenanceOrderRunningDevices where OrderID=N'" & OrderID & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                txtOrderID.Text = OrderID
                cmbCustomer.Text = dr("Vendorname").ToString
                txtCarNumber.Text = dr("CarNumber").ToString
                dtpDate.Text = Cls.R_date(dr("OrderDate").ToString)
                dtpDelivery_Date.Text = Cls.R_date(dr("OrderDate").ToString)
                TxtHour.Text = dr("TimeAMPM").ToString
                cmbTypeProduct.Text = dr("TypeProduct_Name").ToString
                txtSupervisor.Text = dr("Supervisor").ToString
                txtRecipient.Text = dr("Recipient").ToString
                'Favorite = dr("Favorite").ToString
            End If
        End If


        Dt_AddBill.Rows.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "select itm_id,itm_cat,itm_name, Price, qu, totalprice,Stores,TotalExpenses,TotalIncome from BillsalData where OrderID =N'" & txtOrderID.Text & "'" : cmd.CommandText = S : dr = cmd.ExecuteReader
        Do While dr.Read
            Dgv_Add.DataSource = Fn_AddBill(dr(0), dr(1), dr(2), dr(3), dr(4), dr(5), dr(6), "", dr(7), dr(8))
        Loop
        SumAllPrice()
        Try
            Dgv_Add.Columns(2).Width = 230
            Dgv_Add.Columns(3).Visible = False
            Dgv_Add.Columns(8).Visible = False
            Dgv_Add.Columns(9).Visible = False
            Dgv_Add.Columns(0).ReadOnly = True
            Dgv_Add.Columns(1).ReadOnly = True
            Dgv_Add.Columns(2).ReadOnly = True
            Dgv_Add.Columns(4).ReadOnly = False
            Dgv_Add.Columns(5).ReadOnly = False
            Dgv_Add.Columns(6).ReadOnly = True
            Dgv_Add.Columns(7).ReadOnly = True
        Catch ex As Exception

        End Try

        Dim disc_type As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select disc,disc_type from Sales_Bill where OrderID=N'" & OrderID & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            txtdisc.Text = "0"
        Else
            txtdisc.Text = dr("disc")
            disc_type = dr("disc_type").ToString()
            If disc_type = "قيمة" Then
                ChkVal.Checked = True
            Else
                ChkCent.Checked = True
            End If
        End If


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select BEY from Sales_Bill where OrderID=N'" & OrderID & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            txtpaying.Text = "0"
        Else
            txtpaying.Text = dr("BEY")
        End If


        If ChkCash.Checked = True Then
            pay = txtpaying.Text
            txtpaying.Text = txttotalafterdisc.Text : txtpaying.Enabled = False
        End If
        If ChkState.Checked = True Then
            txtpaying.Enabled = True
        End If

        PanelView.Visible = True
        PanelView.Top = 180
        PanelView.Dock = DockStyle.Fill
        Panel27.Top = 5000

        CountCloseBill = Dgv_Add.RowCount
        txtprc.Focus()
    End Sub

    Private Sub btnClosePriceOffer_Click(sender As Object, e As EventArgs) Handles btnClosePriceOffer.Click
        PanelPriceOffer.Visible = False
        PanelPriceOffer.Dock = DockStyle.None
        PanelPriceOffer.Top = 5000
    End Sub

    Private Sub FillFlowLayoutMaintenance()
        On Error Resume Next
        Dim FromArgb As String = ""
        FlowLayout_Maintenance.Controls.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If SelectTypeMaintenanceDeviceCar = "مركز صيانة السيارات" Then
            S = "SELECT OrderID,Vendorname, CarNumber FROM View_MaintenanceOrderRunning where MaintenanceStatus_Name = N'صيانة'"
        End If
        If SelectTypeMaintenanceDeviceCar = "مركز صيانة الاجهزة" Then
            S = "SELECT OrderID,Vendorname, TypeProduct_Name FROM View_MaintenanceOrderRunningDevices where MaintenanceStatus_Name = N'صيانة'"
        End If

        If chkAll.Checked = False Then
            If cmbCustomerFind.Text <> "" Then
                S = S & " and Vendorname =N'" & cmbCustomerFind.Text.Trim & "'"
            End If
            If cmbTypeProductFind.Text <> "" Then
                S = S & " and TypeProduct_Name =N'" & cmbTypeProductFind.Text.Trim & "'"
            End If
            If cmbCarNumberFind.Text <> "" Then
                S = S & " and CarNumber Like N'%" & cmbCarNumberFind.Text.Trim & "%'"
            End If
            If txtbillnoFind.Text <> "" Then
                S = S & " and bill_No =N'" & txtbillnoFind.Text.Trim & "'"
            End If
        End If

        If chkDate.Checked = False Then
            S = S & " and OrderDate >=N'" & Cls.C_date(dtpFrom.Text) & "' and OrderDate <=N'" & Cls.C_date(dtpTo.Text) & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by OrderID"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by CarNumber"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by Vendorname"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        Do While dr.Read
            Dim btn2 As New Button
            AddHandler btn2.Click, AddressOf ClickMe_Maintenance
            btn2.Width = 178
            btn2.Height = 125
            btn2.FlatStyle = FlatStyle.Flat
            btn2.Text = Trim(dr(0).ToString) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Trim(dr(1).ToString) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Trim(dr(2).ToString)
            btn2.Visible = True
            btn2.Tag = Trim(dr(0).ToString)
            FromArgb = Trim(dr(1)).ToString
            btn2.BackColor = Color.FromArgb(219, 68, 55)
            btn2.ForeColor = Color.White
            btn2.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
            'set_color_button(btn, FlowLay_Items)
            FlowLayout_Maintenance.Controls.Add(btn2)
        Loop
    End Sub

    Private Sub btnReceivingCar_Click(sender As Object, e As EventArgs) Handles btnReceivingCar.Click
        PanelReceivingCar.Visible = True
        PanelReceivingCar.Dock = DockStyle.Fill
        PanelReceivingCar.Top = 180
        GetReceivingCar()
    End Sub

    Private Sub btnCloseReceivingCar_Click(sender As Object, e As EventArgs) Handles btnCloseReceivingCar.Click
        PanelReceivingCar.Visible = False
        PanelReceivingCar.Dock = DockStyle.None
        PanelReceivingCar.Top = 5000
    End Sub

    Private Sub ClickMe_Maintenance(ByVal sender As Object, ByVal e As EventArgs)
        Dim btn2 As Button
        btn2 = CType(sender, Button)
        Dim str As String = btn2.Tag
        ViewBillSales(str)
    End Sub

    Private Sub PrintReportOrderRunning()

        Dim TimeAMPM As String = "" : Dim CarNumber As String = "" : Dim Supervisor As String = "" : Dim Recipient As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select TimeAMPM,CarNumber,Supervisor,Recipient from View_MaintenanceOrderRunning where OrderID=N'" & txtOrderID.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            TimeAMPM = dr("TimeAMPM").ToString
            CarNumber = dr("CarNumber").ToString
            Supervisor = dr("Supervisor").ToString
            Recipient = dr("Recipient").ToString
        End If

        Dim txtNameAr, txtNameEN, txtNameArDown, txtVisit_Date, txtDeviceModel, txtDeviceBrand, txtTypeProduct, txtWebsite, txtMobile, txtTel, txtTelCust, txtMobileCust, txtRegion, txtMark, txtaddressCust, txtpercent As TextObject

        Cls.delete_Branch_All("PrintSalesPurchases")

        For i As Integer = 0 To DataGridView2.Rows.Count - 1

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases(Company_Branch_ID,BILL_NO, Vendorname, bill_date, billtime, KiloMeter,Driv_CarNumber, Supervisor_Reform, Recipient,Received_Date,Details) values ("
            S = S & "N'" & Company_Branch_ID & "',N'" & txtOrderID.Text.Trim & "',N'" & cmbCustomer.Text & "',N'" & Cls.C_date(dtpDate.Text) & "',N'" & TimeAMPM & "' ,N'" & txtKiloMeter.Text & "' ,N'" & CarNumber & "',N'" & Supervisor & "' ,N'" & Recipient & "',N'" & DataGridView2.Rows(i).Cells(1).Value & "',N'" & DataGridView2.Rows(i).Cells(2).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

        Next
        AddReportView()
        GetDataCustomer()

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)

        Dim rpt
        'Dim rpt2
        If rdoLarge.Checked = True Then
            'If SelectTypeMaintenanceDeviceCar = "DevicesMaintenance" Then
            '    'rpt = New Rpt_MaintenanceOrderRunningDevices
            '    'rpt2 = New Rpt_MaintenanceOrderRunningDevicesReceipt
            'Else
            rpt = New Rpt_MaintenanceOrderRunning
            'End If
        End If
        If rdoSmall.Checked = True Then
            rpt = New Rpt_MaintenanceOrderRunningSmall
        End If

        rpt.SetDataSource(dt)
        'rpt2.SetDataSource(dt)

        txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
        txtNameAr.Text = NameArCompay
        'txtNameArDown = rpt.Section1.ReportObjects("txtTitelArDown")
        'txtNameArDown.Text = CMPUnderBILL
        txtNameEN = rpt.Section1.ReportObjects("txtTitelEn")
        txtNameEN.Text = NameEnCompany

        If SelectTypeMaintenanceDeviceCar = "DevicesMaintenance" Then
            txtRegion = rpt.Section1.ReportObjects("txtRegion")
            txtRegion.Text = RegionCust
            txtTelCust = rpt.Section1.ReportObjects("txtTelCust")
            txtTelCust.Text = TelCust
            txtMobileCust = rpt.Section1.ReportObjects("txtMobileCust")
            txtMobileCust.Text = MobileCust
            txtaddressCust = rpt.Section1.ReportObjects("txtaddressCust")
            txtaddressCust.Text = AddressCust
            txtMark = rpt.Section1.ReportObjects("txtMark")
            txtMark.Text = MarkCust
            txtDeviceModel = rpt.Section1.ReportObjects("txtDeviceModel")
            txtDeviceModel.Text = cmbDeviceModel.Text
            txtDeviceBrand = rpt.Section1.ReportObjects("txtDeviceBrand")
            txtDeviceBrand.Text = cmbDeviceBrand.Text
            txtTypeProduct = rpt.Section1.ReportObjects("txtTypeProduct")
            txtTypeProduct.Text = cmbTypeProduct.Text
            txtWebsite = rpt.Section1.ReportObjects("txtWebsite")
            txtWebsite.Text = CMPWebsite
            txtTel = rpt.Section1.ReportObjects("txtTel")
            txtTel.Text = CmpTel
            txtpercent = rpt.Section1.ReportObjects("txtpercent")
            txtpercent.Text = ""
        End If
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If

        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "أوامر تشغيل الصيانة"
        Frm_PrintReports.Show()

    End Sub

    Private Sub btnDeleteReceivingCar_Click(sender As Object, e As EventArgs) Handles btnDeleteReceivingCar.Click
        If DGVReceivingCar.RowCount = 0 Then Beep() : Exit Sub
        If (DGVReceivingCar.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        RNXD = DGVReceivingCar.CurrentRow.Index
        DGVReceivingCar.Rows.RemoveAt(RNXD)
    End Sub

    Private Sub btnAddReceivingCar_Click(sender As Object, e As EventArgs) Handles btnAddReceivingCar.Click
        If cmbReceivingCar.Text = "" Then MsgBox("فضلا أدخل أستلام السيارة", MsgBoxStyle.Exclamation) : cmbReceivingCar.Focus() : Exit Sub

        Dim ReceivingCar_ID As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select ReceivingCar_ID from Maintenance_ReceivingCar where ReceivingCar=N'" & cmbReceivingCar.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            MAXRECORD("Maintenance_ReceivingCar", "ReceivingCar_ID")

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MaintenanceType(ReceivingCar_ID,ReceivingCar) values ("
            S = S & "N'" & AddItemsID & "',N'" & cmbReceivingCar.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ReceivingCar_ID from Maintenance_ReceivingCar where ReceivingCar=N'" & cmbReceivingCar.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                ReceivingCar_ID = dr("ReceivingCar_ID")
            End If
        Else
            If dr(0) Is DBNull.Value Then
            Else
                ReceivingCar_ID = dr("ReceivingCar_ID")
            End If
        End If

        Dim SlNo As Long
        If Me.DGVReceivingCar.Rows.Count = 0 Then
            SlNo = 1
        Else
            SlNo = Me.DGVReceivingCar.Rows.Count + 1
        End If

        DGVReceivingCar.DataSource = Fn_AddBill_ReceivingCar(SlNo, ReceivingCar_ID, cmbReceivingCar.Text)

        DGVReceivingCar.Columns(0).Width = 60
        DGVReceivingCar.Columns(1).Visible = False

        cmbReceivingCar.Text = ""
        cmbReceivingCar.Focus()
    End Sub

    Private Sub GetPriceOffer()

        Dt_AddBill_PriceOffer.Rows.Clear()
        cmbCustomersPriceOffer.Text = cmbCustomer.Text
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * from View_PriceOfferBill where Vendorname =N'" & cmbCustomer.Text & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            dtpdate_PriceOffer.Text = Cls.R_date(dr("bill_date").ToString())
            txtNumPriceOffer.Text = dr("bill_no").ToString
            txtTotalPriceOffer.Text = dr("totalprice").ToString
        End If


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select itm_name,price,qu from View_PriceOfferBill where Vendorname =N'" & cmbCustomer.Text & "'  order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            DGVPriceOffer.DataSource = Fn_AddBill_PriceOffer(dr("itm_name").ToString, dr("price").ToString, dr("qu").ToString)
        Loop
    End Sub

    Private Sub txtOrderID_TextChanged(sender As Object, e As EventArgs) Handles txtOrderID.TextChanged
        MyVars.CheckNumber(txtOrderID)
    End Sub

    Private Sub txtbillno_TextChanged(sender As Object, e As EventArgs) Handles txtbillno.TextChanged
        MyVars.CheckNumber(txtbillno)
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub

    Friend Function Fn_AddBill_PriceOffer(ByVal Col_Name As String, ByVal Col_Price As String, ByVal Col_Quant As String) As DataTable
        If Dt_AddBill_PriceOffer.Columns.Count = 0 Then
            Dt_AddBill_PriceOffer.Columns.Add("أسم الصنف", GetType(String))
            Dt_AddBill_PriceOffer.Columns.Add("السعر", GetType(Double))
            Dt_AddBill_PriceOffer.Columns.Add("الكمية", GetType(Double))
        End If

        Dt_AddBill_PriceOffer.Rows.Add(Col_Name, Col_Price, Col_Quant)
        Return Dt_AddBill_PriceOffer
    End Function

    Private Sub GetReceivingCar()
        Try
            Dt_AddBill_ReceivingCar.Rows.Clear()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select ReceivingCar_ID,ReceivingCar from View_Maintenance_ReceivingCar where OrderID =N'" & txtOrderID.Text & "'  order by 1"
            dr = cmd.ExecuteReader
            Do While dr.Read
                Dim SlNo As Long
                If Me.DGVReceivingCar.Rows.Count = 0 Then
                    SlNo = 1
                Else
                    SlNo = Me.DGVReceivingCar.Rows.Count + 1
                End If

                DGVReceivingCar.DataSource = Fn_AddBill_PriceOffer(SlNo, dr("ReceivingCar_ID").ToString, dr("ReceivingCar").ToString)
            Loop

            DGVReceivingCar.Columns(0).Width = 60
            DGVReceivingCar.Columns(1).Visible = False
        Catch ex As Exception
        End Try
    End Sub

    Friend Function Fn_AddBill_ReceivingCar(ByVal Col_Siral As String, ByVal Col_ReceivingCar_ID As String, ByVal Col_ReceivingCar As String) As DataTable
        If Dt_AddBill_ReceivingCar.Columns.Count = 0 Then
            Dt_AddBill_ReceivingCar.Columns.Add("مسلسل", GetType(String))
            Dt_AddBill_ReceivingCar.Columns.Add("كود", GetType(String))
            Dt_AddBill_ReceivingCar.Columns.Add("نوع الصيانة", GetType(String))
        End If

        Dt_AddBill_ReceivingCar.Rows.Add(Col_Siral, Col_ReceivingCar_ID, Col_ReceivingCar)

        Return Dt_AddBill_ReceivingCar

    End Function

    Private Sub MAXRECORD(ByVal Table As String, ByVal Felds As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " + Table + ""
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            AddItemsID = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" + Felds + " As float)) as mb FROM " + Table + " where " + Felds + " <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            AddItemsID = sh + 1
        End If

    End Sub

End Class