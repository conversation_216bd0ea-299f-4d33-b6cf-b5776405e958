﻿Imports System.Windows.Forms
Imports System.Data
Imports System.Data.SqlClient
Imports vb = Microsoft.VisualBasic
Imports CrystalDecisions.CrystalReports.Engine
Imports System.IO
Public Class Frm_IM_Decayed
    Dim WithEvents BS As New BindingSource

    Dim R() As String
    Dim Mnm As String
    Dim _totalItem As Double
    Dim TotalPriceBeforeAverage As Double

    Private Sub sales_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", cmbvendores)
        End If
        Bra.Fil("groups", "g_name", cmbcats)
        Cls.fill_combo_Branch("stores", "store", cmbStores)
        cmbvendores.Items.Add("نقدا")
        cmbvendores.Text = "نقدا"
        MAXRECORD()
        cmbStores.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        GetDateNotBeenActivatedPrograms(DateTimePicker1)
    End Sub

    Private Sub cmbvendores_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbvendores.KeyUp
        If e.KeyCode = 13 Then
            txtbillno.Focus()
        End If
    End Sub
    Private Sub txtbillno_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtbillno.KeyUp
        If e.KeyCode = 13 Then
            DateTimePicker1.Focus()
        End If
    End Sub
    Private Sub DateTimePicker1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles DateTimePicker1.KeyUp
        If e.KeyCode = 13 Then
            txtprc.Focus()
        End If
    End Sub
    Private Sub cmbcats_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then

            cmbname.Focus()

        End If
    End Sub

    Private Sub txtprice_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtprice.KeyUp
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub txtprice_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtprice.TextChanged
        MyVars.CheckNumber(txtprice)

        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If
        TotalBeforeVAT = Val(txtprice.Text) * Val(txtquntUnity.Text)
        If ShowValueVAT = "YES" Then
            TotalValueVAT = Format(Val(TotalBeforeVAT) * Val(ItemsRateVAT) / 100, "Fixed")
        End If
        txtTotalTotal.Text = Math.Round(TotalValueVAT, 2) + Math.Round(TotalBeforeVAT, 2)

        Try
            txtTotalTotal.Text = Math.Round(Convert.ToDouble(txtTotalTotal.Text), 2)
        Catch ex As Exception
        End Try

    End Sub
    Private Sub txtqunt_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtqunt.KeyUp

        If e.KeyCode = 13 Then
            txtprice.Focus()
        End If
    End Sub

    Private Sub txtqunt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtqunt.TextChanged
        MyVars.CheckNumber(txtqunt)
        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If
        TotalBeforeVAT = Val(txtprice.Text) * Val(txtquntUnity.Text)
        If ShowValueVAT = "YES" Then
            TotalValueVAT = Format(Val(TotalBeforeVAT) * Val(ItemsRateVAT) / 100, "Fixed")
        End If
        txtTotalTotal.Text = Math.Round(TotalValueVAT, 2) + Math.Round(TotalBeforeVAT, 2)
        Try
            txtTotalTotal.Text = Math.Round(Convert.ToDouble(txtTotalTotal.Text), 2)
        Catch ex As Exception
        End Try
    End Sub
    Function ValidateTextAdd() As Boolean

        If cmbvendores.Text = "" Then MsgBox("فضلا أختر العميل", MsgBoxStyle.Exclamation) : cmbvendores.Focus() : Return False
        If txtprc.Text = "" Then MsgBox("فضلا ادخل الباركود", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
        If txtbillno.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الفاتورة", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        If cmbcats.Text = "" Then MsgBox("فضلا حدد المجموعة", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbname.Text = "" Then MsgBox("فضلا أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbname.Focus() : Return False
        If cmbUnityItems.Text = "" Then MsgBox("فضلا أدخل وحدة القياس", MsgBoxStyle.Exclamation) : cmbUnityItems.Focus() : Return False
        If cmbStores.Text = "" Then MsgBox("من فضلك اختر المخزن المرتجع اليه", MsgBoxStyle.Exclamation) : cmbStores.Focus() : Return False
        If Val(txtprice.Text.Trim) = 0 Then MsgBox("فضلا أدخل السعر", MsgBoxStyle.Exclamation) : txtprice.Focus() : Return False
        If Val(txtquntUnity.Text.Trim) = 0 Then MsgBox("فضلا أدخل الكمية ", MsgBoxStyle.Exclamation) : txtquntUnity.Focus() : Return False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from Items where itm_id =N'" & txtprc.Text.Trim & "'and group_name =N'" & cmbcats.Text.Trim & "'and sname =N'" & cmbname.Text.Trim & "'and Stores =N'" & cmbStores.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H = 0 Then
            MsgBox("الصنف غير مطابق من فضلك راجع الصنف ", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
        End If


        Dim ReturnInvoice As String = mykey.GetValue("ReturnInvoice", "NO")
        If ReturnInvoice = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from purchase_bill where bill_no =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H = 0 Then
                MsgBox("رقم الفاتورة غير مسجل مسبقا بنفس فاتورة المشتريات", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
            ElseIf H > 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select count(*) from purchase_bill where bill_no =N'" & txtbillno.Text.Trim & "' and Vendorname =N'" & cmbvendores.Text.Trim & "'" : H = cmd.ExecuteScalar
                If H = 0 Then
                    MsgBox("رقم الفاتورة غير مسجل باسم هذا المورد", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
                End If
            End If
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from BilltINData where bill_no =N'" & txtbillno.Text.Trim & "' and itm_id =N'" & txtprc.Text.Trim & "'" : H = cmd.ExecuteScalar
            If H = 0 Then
                MsgBox("هذا الصنف غير مسجل بهذه الفاتورة ", MsgBoxStyle.Exclamation) : txtprc.Focus() : Return False
            End If
        End If

        For i As Integer = 0 To Dt_AddBill.Rows.Count - 1
            If Dgv_Add.Rows(i).Cells(0).Value = txtprc.Text.Trim Then MsgBox("صنف مكرر بنفس الفاتورة", MsgBoxStyle.Exclamation) : txtprc.Focus() : txtprc.SelectAll() : Return False
        Next

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from decayed where bill_no =N'" & txtbillno.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H > 0 Then
            MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False
        End If


        Return True
    End Function
    Function ValidateTextSave() As Boolean
        If cmbvendores.Text = "" Then MsgBox("فضلا أختر المورد", MsgBoxStyle.Exclamation) : cmbvendores.Focus() : Return False
        If txtbillno.Text.Trim = "" Then MsgBox("فضلا أدخل رقم الفاتورة", MsgBoxStyle.Exclamation) : txtbillno.Focus() : Return False

        If Dgv_Add.Rows.Count < 1 Then MsgBox("فضلا أكمل بيانات الفاتورة", MsgBoxStyle.Exclamation) : cmbvendores.Focus() : Return False
        Return True
    End Function
    Friend Sub DTV_Width()
        If Dgv_Add.Rows.Count > 0 Then
            Dgv_Add.Columns(0).Width = 70
            Dgv_Add.Columns(1).Width = 90
            Dgv_Add.Columns(2).Width = 130
            Dgv_Add.Columns(3).Width = 60
            Dgv_Add.Columns(4).Width = 60
            Dgv_Add.Columns(5).Width = 75
            Dgv_Add.Columns(6).Width = 70
            'Dgv_Add.Columns(7).Width = 70
        End If
    End Sub
    Private Sub GetDataByPrc()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If PermtionName = "مدير" Then
            cmd.CommandText = "select group_name , sname ,tinPrice from items where itm_id=N'" & txtprc.Text & "' order by 1"
        Else
            cmd.CommandText = "select group_name , sname ,tinPrice from items where itm_id=N'" & txtprc.Text & "' and Company_Branch_ID =N'" & Company_Branch_ID & "' order by 1"
        End If
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Or dr(1) Is DBNull.Value Then
        Else
            Dim xcmbcats, xcmbname As String
            xcmbcats = dr(0)
            xcmbname = dr(1)
            If dr(2) Is DBNull.Value Then
                txtprice.Text = 0
                cmbcats.Text = xcmbcats
                cmbname.Text = xcmbname
                GoTo 1
            End If

            txtprice.Text = dr(2)
            cmbcats.Text = xcmbcats
            cmbname.Text = xcmbname

        End If
1:
        Dim X As Double = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)
        txtStoreTotal.Text = X

        txtprice.SelectAll()
        txtqunt.Text = 1 : txtqunt.SelectAll() : txtprice.SelectAll() : txtprice.Focus()
    End Sub
    Private Sub BtnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAdd.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateTextAdd() = False Then Exit Sub
        IM.Store(txtprc.Text.Trim, cmbStores.Text)

        If ConnectOnlineStore = "YES" Then
            EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", txtprc.Text)
            StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", txtprc.Text)
            Cos.UpdateProductStock(StockOnline, txtprc.Text, EditItmId)
        End If

        Cls.Get_Value_Count_More("items", "itm_id =N'" & txtprc.Text.Trim & "' and group_name =N'" & cmbcats.Text.Trim & "' and sname =N'" & cmbname.Text.Trim & "'")
        If H = 0 Then
            GetDataByPrc()
        End If

        If cmbcats.Text = "" Or cmbname.Text = "" Or txtprice.Text = "" Or txtqunt.Text = "" Then
            MsgBox("اكمل البيانات")
            Exit Sub
        End If
        Dgv_Add.DataSource = Fn_AddBill(txtprc.Text, cmbcats.Text, cmbname.Text, Val(txtprice.Text), Val(txtqunt.Text), Val(txtprice.Text) * Val(txtqunt.Text), cmbStores.Text)
        ClearAdd() : SumAllPrice() : FocusText()

    End Sub
    Private Sub FocusText()
        Dim FocusText As String = mykey.GetValue("FocusText", "NO")
        If FocusText = "YES" Then
            txtprc.Focus()
        Else
            cmbcats.Focus()
        End If
    End Sub
    Private Sub ClearAdd()
        GrpMain.Enabled = False
        Cls.clear(GroupBox2)
        txtprc.Text = ""
    End Sub
    Dim Dt_AddBill As New DataTable
    Friend Function Fn_AddBill(ByVal Col_Prc As String, ByVal Col_Cats As String, ByVal Col_Name As String _
, ByVal Col_Price As Double, ByVal Col_Quant As Double, ByVal Col_Total As Double, ByVal Col_Stores As String) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("الباركود", GetType(String))
            Dt_AddBill.Columns.Add("المجموعة", GetType(String))
            Dt_AddBill.Columns.Add("الصنف", GetType(String))
            Dt_AddBill.Columns.Add("سعر الشراء", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية", GetType(Double))
            Dt_AddBill.Columns.Add("الإجمالي", GetType(Double))
            Dt_AddBill.Columns.Add("المخزن", GetType(String))
        End If

        DTV_Width()
        Dt_AddBill.Rows.Add(Col_Prc, Col_Cats, Col_Name, Col_Price, Col_Quant, Col_Total, Col_Stores)
        Return Dt_AddBill
    End Function

    Private Sub Dgv_Add_CellContentClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles Dgv_Add.CellContentClick
        On Error Resume Next
        If e.ColumnIndex = 0 Then
            txtprc.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Prc").Value.ToString
            cmbcats.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Cats").Value.ToString
            cmbname.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Name").Value.ToString
            txtprice.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Price").Value
            txtqunt.Text = Dgv_Add.Rows(e.RowIndex).Cells("Col_Quant").Value
            Dgv_Add.Rows.RemoveAt(e.RowIndex)
        End If
        SumAllPrice()
    End Sub


    Private Sub BtnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnSave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        SumAllPrice()
        If ValidateTextSave() = False Then Exit Sub

        Dim x As String = MsgBox("هل تريد بالفعل إتمام عملية التوالف", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        Dim tm As String = Cls.get_time(True)
        Dim stat As String
        If CheckBox1.Checked = True Then
            stat = "تم السداد"
        Else
            stat = "لم يتم السداد"
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into decayed(Company_Branch_ID,bill_No,Vendorname,bill_date,billtime,totalpriceafterdisc,Stat,UserName,Treasury_Code) values ("
        S = S & "N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "' ,N'" & cmbvendores.Text.Trim & "' ,N'" & Cls.C_date(DateTimePicker1.Text) & "' ,N'" & Cls.get_time(True) & "' ," & Val(txttotalpeforedisc.Text.Trim) & ",N'" & stat & "',N'" & UserName & "',N'" & Treasury_Code & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        Dim bill_EndDate As String = "" : Dim bill_no_Expired As String = ""
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Top(100) PERCENT MIN(bill_no_Expired) As Exbill_no_Expired, bill_EndDate, qu_expired, itm_id, Stores From dbo.BilltINData Group By itm_id, Stores, bill_EndDate, qu_expired HAVING(itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "') AND (Stores = N'" & Dgv_Add.Rows(i).Cells(6).Value & "') AND (qu_expired <> 0)  ORDER BY MIN(bill_no_Expired)" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                bill_no_Expired = dr(0).ToString
                bill_EndDate = dr(1).ToString
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into data_decayed (Company_Branch_ID,bill_no,itm_id,itm_cat,itm_name,price,qu,totalprice,Stores,username , bill_date,Treasury_Code,bill_EndDate,bill_no_Expired)  values (N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Dgv_Add.Rows(i).Cells(3).Value & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & UserName & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Treasury_Code & "',N'" & bill_EndDate & "',N'" & bill_no_Expired & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            IM.StoreExpired(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(6).Value, bill_EndDate.ToString(), bill_no_Expired)

        Next

        'UpdatePriceItems()

        '===============================================================================
        Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
        If UseAccounts = "YES" Then
            Daily_Restrictions()
        End If
        '===============================================================================

        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            IM.Store(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(6).Value)

            If ConnectOnlineStore = "YES" Then
                EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
                StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
                Cos.UpdateProductStock(StockOnline, Dgv_Add.Rows(i).Cells(0).Value, EditItmId)
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update data_decayed set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbillno.Text & "' and itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "' and Stores =N'" & Dgv_Add.Rows(i).Cells(6).Value & "'" : cmd.ExecuteNonQuery()

        Next

        IM.CustomerAccountTotal(cmbvendores.Text)

        Get_Movement_In_Out_Money(DateTimePicker1.Text, Treasury_Code)

        MsgBox("تمت عملية مرتجع توالف بنجاح", MsgBoxStyle.Information) : txtprc.Focus()
        If chkprint.Checked = True Then
            PrintReport()
        End If

        ClearSave()
        MAXRECORD()
        Dt_AddBill.Rows.Clear()
    End Sub

    Private Sub UpdatePriceItems()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            cmd.CommandText = "update items set tinprice = " & Val(Dgv_Add.Rows(i).Cells(3).Value) & " where itm_id =N'" & Dgv_Add.Rows(i).Cells(0).Value & "'" : cmd.ExecuteNonQuery()
        Next
    End Sub

    Private Sub PrintReport()

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        Dim BillSerialNumber As Double = 0
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            BillSerialNumber += 1
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases (Company_Branch_ID,itm_id,itm_cat,itm_name,price,qu,totalprice,store,BILL_NO,Vendorname,bill_date,billtime,totalpricebeforedisc,NumberInt1,TotalBeforeDisc)  values (N'" & Company_Branch_ID & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Dgv_Add.Rows(i).Cells(3).Value & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & txtbillno.Text & "',N'" & cmbvendores.Text & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Cls.get_time(True) & "',N'" & txttotalpeforedisc.Text & "',N'" & BillSerialNumber & "',N'" & BillSerialNumber & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New Rpt_SalesPurchases
        Dim txt, txtname, txtNameAr, txtNameEn, txtCmpAddress, txtCmpEmail, txtCmpTel, txtCmpMobile, txtCmpFax As TextObject

        Cls.Select_More_Data_Branch_Print_Orderby("PrintSalesPurchases", "*", "NumberInt1")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txt = rpt.Section1.ReportObjects("txtTitelAddress")
        txt.Text = "تقرير مرتجعات التوالف"
        txtname = rpt.Section1.ReportObjects("txtName")
        txtname.Text = "أسم المورد"
        txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
        txtNameEn.Text = NameEnCompany
        txtCmpAddress = rpt.Section1.ReportObjects("txtAddress")
        txtCmpAddress.Text = CmpAddress
        txtCmpEmail = rpt.Section1.ReportObjects("txtEmail")
        txtCmpEmail.Text = CmpEmail
        txtCmpTel = rpt.Section1.ReportObjects("txtTel")
        txtCmpTel.Text = CmpTel
        txtCmpMobile = rpt.Section1.ReportObjects("txtMobile")
        txtCmpMobile.Text = CmpMobile
        txtCmpFax = rpt.Section1.ReportObjects("txtFax")
        txtCmpFax.Text = CmpFax
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "تقرير مرتجعات مشتريات"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub
    Sub ForPrintAll(ByVal itm_id As String, ByVal itm_cat As String, ByVal itm_name As String, ByVal price As String, ByVal qu As String, ByVal totalprice As String, ByVal stors As String,
                    ByVal BILL_NO As String, ByVal Vendorname As String, ByVal bill_date As String, ByVal billtime As String,
                    ByVal totalpricebeforedisc As String, ByVal disc As String, ByVal totalpriceafterdisc As String, ByVal BEY As String, ByVal STAYING As String)
        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "Sp_PrintSalesPurchases"
        cmd.Parameters.Clear()

        cmd.Parameters.AddWithValue("@itm_id", itm_id)
        cmd.Parameters.AddWithValue("@itm_cat", itm_cat)
        cmd.Parameters.AddWithValue("@itm_name", itm_name)
        cmd.Parameters.AddWithValue("@price", price)
        cmd.Parameters.AddWithValue("@qu", qu)
        cmd.Parameters.AddWithValue("@totalprice", totalprice)
        cmd.Parameters.AddWithValue("@store", stors)
        cmd.Parameters.AddWithValue("@BILL_NO", BILL_NO)
        cmd.Parameters.AddWithValue("@Vendorname", Vendorname)
        cmd.Parameters.AddWithValue("@bill_date", bill_date)
        cmd.Parameters.AddWithValue("@billtime", billtime)
        cmd.Parameters.AddWithValue("@totalpricebeforedisc", totalpricebeforedisc)
        cmd.Parameters.AddWithValue("@disc", disc)
        cmd.Parameters.AddWithValue("@totalpriceafterdisc", totalpriceafterdisc)
        cmd.Parameters.AddWithValue("@BEY", BEY)
        cmd.Parameters.AddWithValue("@STAYING", STAYING)

        cmd.ExecuteNonQuery()

    End Sub
    Private Sub ClearSave()
        GrpMain.Enabled = True
        cmbvendores.SelectedIndex = -1
        txtbillno.Text = ""
        txtprc.Text = ""
        txttotalpeforedisc.Text = ""
    End Sub
    Private Sub SumAllPrice()
        Dim SM As Double
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            SM = SM + Dgv_Add.Rows(i).Cells(5).Value
        Next
        txttotalpeforedisc.Text = SM
    End Sub

    Private Sub txtpaying_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            BtnSave.PerformClick()
        End If
    End Sub

    Private Sub txtbillno_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtbillno.TextChanged
        MyVars.CheckNumber(txtbillno)
    End Sub

    Private Sub txtprc_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtprc.KeyUp
        If e.KeyCode = 13 Then
            If txtprc.Text.Trim = "" Then
                BtnSave.PerformClick()
            Else
                ItemsUnityNumber = 0
                Bol = True
                If txtprc.Text.Trim = "" Then Exit Sub

                'ParcodeMore = txtprc.Text
                'GetBarcodeMore(txtprc.Text) : If ParcodeMore <> "" Then : If ParcodeMore <> 0 Then : txtprc.Text = ParcodeMore : End If : End If
                'If ParcodeMore = "0" Then
                '    Dim PrcUnity As String = Cls.Get_Code_Value("ItemsUnity", "itm_id", "itm_id_Unity", ParcodeMore)
                '    If PrcUnity <> "0" Then
                '        txtprc.Text = PrcUnity
                '    End If
                'End If

                'Try
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select group_name , sname,SalPrice,RateVAT from items where itm_id=N'" & txtprc.Text & "' and Stores=N'" & cmbStores.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    cmbcats.Text = dr(0).ToString
                    cmbname.Text = dr(1).ToString
                    txtprice.Text = Val(dr(2).ToString)
                    ItemsRateVAT = Val(dr(3).ToString)
                End If
                'Catch ex As Exception
                '    ErrorHandling(ex, Me.Text)
                'End Try

                GetItemsUnity(cmbUnityItems, txtprc.Text)

                Dim ItemsUnity As String = Cls.Get_Code_Value("ItemsUnity", "Unity_Name", "itm_id_Unity", ParcodeMore)
                If ItemsUnity <> "0" Then
                    cmbUnityItems.Text = ItemsUnity
                End If

                SetItemsUnity()

                GetItemsUnityTotalCarton()

                txtquntUnity.Focus()
                txtquntUnity.Text = 1
                txtqunt.Text = 1
                txtquntUnity.SelectAll()

                Bol = False
            End If
            Dim X As Double = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)
            txtStoreTotal.Text = X
            ParcodeMore = 0
        End If
    End Sub

    Private Sub SetItemsUnity()
        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 1 : End If
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(txtquntUnity.Text)
        End If

    End Sub

    Private Sub GetItemsUnityTotalCarton()
        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from View_ItemsUnity where itm_id=N'" & txtprc.Text & "' and Unity_Name =N'" & cmbUnityItems.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                NumberPieces = dr(0).ToString
            End If

            Dim Itm_Store As Double = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)
            If NumberPieces = 0 Or NumberPieces = 1 Then
                txtStoreTotal.Text = Itm_Store
            Else
                If Itm_Store = 0 And NumberPieces = 0 Then
                    txtStoreTotal.Text = 0
                Else
                    txtStoreTotal.Text = Val(Itm_Store) / Val(NumberPieces)
                    txtStoreTotal.Text = Math.Round(Val(txtStoreTotal.Text), 2)
                End If
            End If
        Else
            txtqunt.Text = Val(txtquntUnity.Text)
        End If


    End Sub


    Private Sub cmbname_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbname.DropDown
        cmbname.Text = ""
        txtprice.Text = ""
        txtprc.Text = ""
        txtqunt.Text = ""
        txtTotalTotal.Text = ""
        txtquntUnity.Text = ""
        cmbUnityItems.Text = ""
    End Sub

    Private Sub cmbname_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbname.KeyUp
        If e.KeyCode = 13 Then
            cmbStores.Focus()
        End If
    End Sub

    Private Sub cmbname_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbname.SelectedIndexChanged
        If txtprc.Text = "" Then
            GetDataBsal()
        End If
    End Sub

    Private Sub GetDataBsal()
        Bol = True
        ItemsUnityNumber = 0
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id,group_name,SalPrice,RateVAT from items where sname=N'" & cmbname.Text & "' and Stores =N'" & cmbStores.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                txtprc.Text = dr(0).ToString
                cmbcats.Text = dr(1).ToString
                txtprice.Text = dr(2).ToString
                ItemsRateVAT = Val(dr(3).ToString)
            End If

            txtStoreTotal.Text = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)


            txtquntUnity.Focus()
            txtquntUnity.SelectAll()
            txtquntUnity.Text = 1
            txtqunt.Text = 1
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        GetItemsUnity(cmbUnityItems, txtprc.Text)

        SetItemsUnity()

        GetItemsUnityTotalCarton()

        Bol = False
    End Sub


    Private Sub GetDataIM_Decayed()
        If txtprc.Text = "" Then
            Bol = True
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select itm_id,TinPriceAverage from items where sname=N'" & cmbname.Text & "' and Stores =N'" & cmbStores.Text & "'"
            Else
                cmd.CommandText = "select itm_id,TinPriceAverage from items where sname=N'" & cmbname.Text & "' and Stores =N'" & cmbStores.Text & "' and Company_Branch_ID = N'" & Company_Branch_ID & "'  or Company_Branch_ID =N'0'"
            End If
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then Exit Sub
            If dr(0) Is DBNull.Value Then
            Else
                txtprc.Text = dr(0)
            End If
            If dr(1) Is DBNull.Value Then
                txtprice.Text = 0
            Else
                txtprice.Text = dr(1)
            End If

            Dim X As Double = IM.Get_Itm_Store(txtprc.Text.Trim, cmbStores.Text.Trim)
            txtStoreTotal.Text = X

            txtqunt.Focus()
            txtqunt.SelectAll()
            txtqunt.Text = 1

            Bol = False
        End If
    End Sub

    Private Sub cmbcats_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If cmbcats.Text = "" Then GoTo 1
        If Bol = True Then GoTo 1
        Bol = True
        Cls.fill_combo_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbname)
        cmbname.Text = ""
1:
        Bol = False
    End Sub

    Dim RNXD As Integer
    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        For i As Integer = 0 To Dgv_Add.SelectedRows.Count - 1
            RNXD = Dgv_Add.CurrentRow.Index
            Dgv_Add.Rows.RemoveAt(RNXD)
        Next
        SumAllPrice()
    End Sub

    Private Sub BtnAddVendor_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAddVendor.Click
        frmvendors.Show()
    End Sub

    Private Sub BtnClear_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnClear.Click
        Dt_AddBill.Rows.Clear()
        SumAllPrice()
        ClearSave()
        Exit Sub

        Dgv_Add.DataSource = ""
        Dt_AddBill.Columns.Add("الباركود", GetType(String))
        Dt_AddBill.Columns.Add("المجموعة", GetType(String))
        Dt_AddBill.Columns.Add("الاسم", GetType(String))
        Dt_AddBill.Columns.Add("السعر", GetType(Double))
        Dt_AddBill.Columns.Add("الكمية", GetType(Integer))
        Dt_AddBill.Columns.Add("الإجمالي", GetType(Double))
        '  Next
        MAXRECORD()
    End Sub
    Private Sub MAXRECORD()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select * from decayed"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Me.txtbillno.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(bill_No As float)) as mb FROM decayed"

            dr = cmd.ExecuteReader
            dr.Read()
            Dim sh As Integer
            sh = dr("mb")
            Me.txtbillno.Text = sh + 1
        End If
    End Sub

    Private Sub cmbcats_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbcats.DropDown
        cmbname.Text = ""
        cmbcats.Text = ""
        txtqunt.Text = ""
        txtprc.Text = ""
        txtprice.Text = ""
    End Sub

    Private Sub cmbcats_KeyUp1(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbcats.KeyUp
        If e.KeyCode = 13 Then
            cmbname.Focus()
        End If
    End Sub

    Private Sub cmbcats_SelectedIndexChanged_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbcats.SelectedIndexChanged
        If cmbname.Text = "" Then
            If txtprc.Text = "" Then
                If cmbcats.Text.Trim = "" Then Exit Sub
                'Cls.fill_combo_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbname)
                Cls.fill_combo_DataAdapter_Stores_Where("Items", "sname", "group_name", cmbcats.Text, cmbname)

                cmbname.Text = ""
            End If
        End If
    End Sub

    Private Sub cmbStores_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbStores.DropDown
        txtqunt.Text = ""
        txtprc.Text = ""
        txtprice.Text = ""
        cmbStores.Text = ""
    End Sub

    Private Sub cmbStores_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbStores.KeyUp
        If e.KeyCode = 13 Then
            GetDataIM_Decayed()
        End If
    End Sub

    Private Sub cmbStores_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles cmbStores.MouseClick
        GetDataIM_Decayed()
    End Sub

    Private Sub cmbStores_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbStores.SelectedIndexChanged
        GetDataIM_Decayed()
    End Sub

    Private Sub Daily_Restrictions()
        Dim Account As String = ""
        Dim AccountCode As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'توالف او إهلاك'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            Account = dr("Link_AccountsTree")
            AccountCode = dr("ACCNumber")
        End If

        '========================================================================================

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from MOVES where MOVID =N'0'" : H = cmd.ExecuteScalar
        If H > 0 Then
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MOVES(MOVID,MOVRegNumber,MOVStatement,MOVDebtor,MOVCreditor,UserName ) values ("
            S = S & "N'0',N'0',N'0',N'0',N'0',N'0')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If


        Dim AccountTreasury As String = "" : Dim AccountCodeTreasury As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select ACCName,ACCNumber from AccountsTree where ACCName=N'الخزينة'" : dr = cmd.ExecuteReader
        If dr.Read Then
            AccountTreasury = dr("ACCName") : AccountCodeTreasury = dr("ACCNumber")
        End If


        Dim MOVRegNumber As String = Cls.MAXRECORD("MOVES", "MOVRegNumber")
        Dim MOVID As String = Cls.MAXRECORD("MOVES", "MOVID")
        Dim bill_no As String = Cls.MAXRECORD("decayed", "decayedID") - 1


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into MOVES(MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
        S = S & "N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Account & "',N'" & txttotalpeforedisc.Text & "',N'" & txttotalpeforedisc.Text & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        ' من حساب / توالف او إهلاك
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
        S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCode & "',N'" & Account & "',N'" & txttotalpeforedisc.Text & "',N'0',N'" & Account & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        ' الى حساب / الخزينة
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
        S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'0',N'" & txttotalpeforedisc.Text & "',N'" & AccountTreasury & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

    End Sub

    Private Sub GetBarcodeMore()
        If BarcodeMore = "YES" Then
            Dim Parcode As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id from BarcodeMore where itm_id_More=N'" & txtprc.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Parcode = dr("itm_id").ToString
                txtprc.Text = Parcode
            End If
        End If
    End Sub

    Private Sub txtquntUnity_TextChanged(sender As Object, e As EventArgs) Handles txtquntUnity.TextChanged
        cmbUnityItems_SelectedIndexChanged(sender, e)

        MyVars.CheckNumber(txtquntUnity)
        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If
        TotalBeforeVAT = Val(txtprice.Text) * Val(txtquntUnity.Text)
        If ShowValueVAT = "YES" Then
            TotalValueVAT = Format(Val(TotalBeforeVAT) * Val(ItemsRateVAT) / 100, "Fixed")
        End If
        txtTotalTotal.Text = Math.Round(TotalValueVAT, 2) + Math.Round(TotalBeforeVAT, 2)
        Try
            txtTotalTotal.Text = Math.Round(Convert.ToDouble(txtTotalTotal.Text), 2)
        Catch ex As Exception
        End Try
    End Sub

    Private Sub cmbUnityItems_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbUnityItems.SelectedIndexChanged
        Try
            If Bol = False Then
                If NotUnityItemsProgram = "YES" Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 0 : End If
                    txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)

                    If ItemsUnityNumber <> 1 Then
                        If ItemsUnityNumber <> 0 Then
                            Dim PriceNumber As String = Cls.Get_Code_Value_Branch_More("ItemsUnity", "SalPriceUnit", "itm_id=N'" & txtprc.Text & "' and Unity_Name=N'" & cmbUnityItems.Text & "'")
                            If PriceNumber <> "0" Then
                                txtprice.Text = PriceNumber
                            End If
                        End If
                    End If
                Else
                    txtqunt.Text = Val(txtquntUnity.Text)
                End If


                GetItemsUnityTotalCarton()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
End Class
