﻿Imports System.IO
Public Class frmCall
    Inherits System.Windows.Forms.Form
    Dim WithEvents BS As New BindingSource
    Dim ItmID As String


    Private Sub CALLBUTTON_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CALLBUTTON.Click
        On Error Resume Next
        tapiRequestMakeCall(txtPhone.Text, "Dialer", "", "")
    End Sub

    Private Sub INTERNETBUTTON_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles INTERNETBUTTON.Click
        On Error Resume Next
        Dim CON As Integer
        Dim s As String
        s = InputBox("من فضلك ادخل عنوان الموقع على الانترنت", "الاتصال بالانترنت", "http://www.")
        If Len(s) > 0 Then
            CON = ShellExecute(0, vbNullString, s, vbNullString, "C:\", SW_SHOWNORMAL)
        Else
            Exit Sub
        End If

    End Sub

    Private Sub EMAILBUTTON_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles EMAILBUTTON.Click
        On Error Resume Next
        Dim CON As Integer
        Dim s As String
        s = InputBox("من فضلك ادخل عنوان البريد الالكترونى ", "ارسال بريد الكترونى")
        If Len(s) > 0 Then
            CON = ShellExecute(0, vbNullString, "mailto:" & s, vbNullString, "C:\", SW_SHOWNORMAL)
        Else
            Exit Sub
        End If

    End Sub

    Private Sub SAVEBUTTON_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles SAVEBUTTON.Click
        On Error Resume Next
        If Validate_Text() = False Then Exit Sub
        vendorfinance()
        Header()
        CLEAR_ALL()
        txtName.Focus()
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)

    End Sub
    Function Validate_Text() As Boolean
        If Trim(txtName.Text) = "" Then
            MsgBox("فضلاً أدخل الاسم", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtName.Focus() : Return False : Exit Function
        End If
        If Trim(txtPhone.Text) = "" Then
            MsgBox("فضلاً أدخل عنوان العميل", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtPhone.Focus() : Return False : Exit Function
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * From TELEPHONE where TELName =N'" & txtName.Text & "'"
        dr = cmd.ExecuteReader()
        If dr.HasRows = True Then
            MessageBox.Show("تم تسجيل بيانات المستخدم سابقاً", "تكرار بيانات مستخدم", MessageBoxButtons.OK, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading)
            Return False : Exit Function
        End If
        Return True
    End Function

    Sub vendorfinance()
        fil_txt()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into TELEPHONE (TELName,TELAddress,TELPhone,TELJob,TELNotes,UserName) values"
        S = S & " (N'" & txtName.Text & "',N'" & txtAddress.Text & "',N'" & txtPhone.Text & "',N'" & cmbJob.Text & "',N'" & txtNotes.Text & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

    End Sub
    Sub fil_txt()
        If Trim(txtAddress.Text) = "" Then
            txtAddress.Text = "####"
        End If
        If Trim(txtNotes.Text) = "" Then
            txtNotes.Text = "####"
        End If
    End Sub
    Public Sub Header()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select id as [id],TELName as [الاسم],TELAddress as [العنوان],TELPhone as [التليفون],TELJob as [الوظيفة],TELNotes as [ملاحظات] From TELEPHONE order by 1"
        If txtSearch.Text = "" Then
        Else
            Dim X As String = "%" & txtSearch.Text.Trim & "%"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select id as [id],TELName as [الاسم],TELAddress as [العنوان],TELPhone as [التليفون],TELJob as [الوظيفة],TELNotes as [ملاحظات] From TELEPHONE where TELName like N'" & X & "'"
        End If
        If txtTel.Text = "" Then
        Else
            Dim M As String = "%" & txtTel.Text.Trim & "%"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select id as [id],TELName as [الاسم],TELAddress as [العنوان],TELPhone as [التليفون],TELJob as [الوظيفة],TELNotes as [ملاحظات] From TELEPHONE where TELPhone like N'" & M & "'"
        End If

        Dim dt As New DataTable
        dr = cmd.ExecuteReader
        dt.Load(dr)
        DataGridView1.DataSource = dt
        DataGridView1.Columns(0).Visible = False
        DataGridView1.Columns(1).Width = 60
        DataGridView1.Columns(2).Width = 60
        DataGridView1.Columns(3).Width = 40
        DataGridView1.Columns(4).Width = 40
        DataGridView1.Columns(5).Width = 60

    End Sub
    Sub CLEAR_ALL()
        txtName.Text = ""
        txtAddress.Text = ""
        txtPhone.Text = ""
        txtNotes.Text = ""
        cmbJob.Text = ""
    End Sub

    Private Sub EDITBUTTON_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles EDITBUTTON.Click
        UPDATERECORD()
        txtName.Focus()
    End Sub

    Private Sub UPDATERECORD()
        If Trim(txtName.Text) = "" Then
            MsgBox("فضلاً أدخل الاسم", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtName.Focus() : Exit Sub
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update TELEPHONE set TELName=N'" & txtName.Text & "',TELAddress=N'" & txtAddress.Text & "',TELPhone =N'" & txtPhone.Text & "', TELJob=N'" & cmbJob.Text & "', TELNotes=N'" & txtNotes.Text & "', UserName=N'" & UserName & "' where TELName =N'" & txtName.Text & "'"
        cmd.ExecuteNonQuery()
        MsgBox("تم التعديل بنجاح", MsgBoxStyle.Information)
        CLEAR_ALL()

    End Sub

    Private Sub DELETEBUTTON_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DELETEBUTTON.Click
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If

        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1

            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim ItmID As String
            ItmID = DataGridView1.SelectedRows(i).Cells(0).Value

            cmd.CommandText = "delete  from TELEPHONE where id =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
        Next
        txtName.Focus()
        Header()
    End Sub

    Private Sub DataGridView1_CellContentClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridView1.CellContentClick

    End Sub

    Private Sub DataGridView1_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles DataGridView1.DoubleClick
        GetData()
    End Sub

    Private Sub GetData()
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        ItmID = DataGridView1.SelectedRows(0).Cells(0).Value
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from TELEPHONE where id=N'" & ItmID & "'"
        DR = cmd.ExecuteReader
        If DR.Read Then
            txtName.Text = dr("TELName")
            txtAddress.Text = dr("TELAddress")
            txtPhone.Text = dr("TELPhone")
            cmbJob.Text = dr("TELJob")
            txtNotes.Text = dr("TELNotes")
        End If

    End Sub

    Private Sub ADDBUTTON_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ADDBUTTON.Click
        CLEAR_ALL()
        Header()
        txtName.Focus()
    End Sub

    Private Sub txtSearch_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearch.TextChanged
        Header()
    End Sub

    Private Sub txtTel_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTel.TextChanged
        Header()
    End Sub

    Private Sub frmCall_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Header()
    End Sub
End Class