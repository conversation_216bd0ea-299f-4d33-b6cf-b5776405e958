﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:4.0.30319.42000
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On

Imports System

Namespace My.Resources
    
    'This class was auto-generated by the StronglyTypedResourceBuilder
    'class via a tool like ResGen or Visual Studio.
    'To add or remove a member, edit your .ResX file then rerun ResGen
    'with the /str option, or rebuild your VS project.
    '''<summary>
    '''  A strongly-typed resource class, for looking up localized strings, etc.
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0"),  _
     Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.Runtime.CompilerServices.CompilerGeneratedAttribute(),  _
     Global.Microsoft.VisualBasic.HideModuleNameAttribute()>  _
    Friend Module Resources
        
        Private resourceMan As Global.System.Resources.ResourceManager
        
        Private resourceCulture As Global.System.Globalization.CultureInfo
        
        '''<summary>
        '''  Returns the cached ResourceManager instance used by this class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend ReadOnly Property ResourceManager() As Global.System.Resources.ResourceManager
            Get
                If Object.ReferenceEquals(resourceMan, Nothing) Then
                    Dim temp As Global.System.Resources.ResourceManager = New Global.System.Resources.ResourceManager("FIT_SOFT.Resources", GetType(Resources).Assembly)
                    resourceMan = temp
                End If
                Return resourceMan
            End Get
        End Property
        
        '''<summary>
        '''  Overrides the current thread's CurrentUICulture property for all
        '''  resource lookups using this strongly typed resource class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend Property Culture() As Global.System.Globalization.CultureInfo
            Get
                Return resourceCulture
            End Get
            Set
                resourceCulture = value
            End Set
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property _1473532297_file_add() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("1473532297_file_add", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property _1473545928_file_delete() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("1473545928_file_delete", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property _1473545934_file_edit() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("1473545934_file_edit", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property _delegate() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("delegate", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Accept() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Accept", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Add() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Add", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Add_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Add_1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Add_2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Add_2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Add_Bill() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Add_Bill", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Add_Bill_Small_White() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Add_Bill_Small_White", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Add_Bill_White() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Add_Bill_White", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property add_images() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("add_images", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Add_Male_User() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Add-Male-User", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property AdjustmentsStores() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("AdjustmentsStores", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property AdvancedSearch() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("AdvancedSearch", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Aggregation() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Aggregation", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Aggregation48() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Aggregation48", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Asset_Balances() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Asset_Balances", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property background_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("background-1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property background_3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("background-3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property backup_restore() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("backup-restore", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property BackUpDatat() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("BackUpDatat", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property bank() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("bank", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property barcode() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("barcode", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Barcode_562() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Barcode_562", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property barcode_scanner() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("barcode_scanner", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Black_Horse_Software_Logo() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Black Horse Software Logo", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Blue_Modern_Technology_Linktree_Background__5_() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Blue Modern Technology Linktree Background (5)", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property calculator() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("calculator", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property cappuccino_alternative() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("cappuccino-alternative", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property category_add() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("category_add", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Chart_Blue_Area__up() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Chart_Blue_Area  up", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Chevron_Left() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Chevron Left", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Chevron_Right() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Chevron Right", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property circle_Blue() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("circle_Blue", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Circle_Green() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Circle_Green", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Circle_Red() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Circle_Red", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property close() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("close", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Close_Box_Red() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Close_Box_Red", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Close3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Close3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property CloseButtons() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("CloseButtons", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Combany() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Combany", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property ConnectOnlineStore() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ConnectOnlineStore", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Delete() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Delete", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property delete1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("delete1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property delete2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("delete2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Delete3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Delete3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Discount() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Discount", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Discount_Coupon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Discount-Coupon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property discount_pngrepo_com() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("discount-pngrepo-com", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Discount1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Discount1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Edit_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Edit_1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property email() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("email", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property empty() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("empty", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Excel_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Excel-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Exit128() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Exit128", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Exit24() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Exit24", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Exit64() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Exit64", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Expired() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Expired", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Filter() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Filter", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property finance() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("finance", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Finances() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Finances", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Finances48() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Finances48", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Financial_Position() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Financial_Position", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property find_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("find_1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Find_Customers() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Find_Customers", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Find_Items() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Find_Items", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Find_Parcode() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Find_Parcode", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property FIT_SOFT_Logo2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("FIT SOFT Logo2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property FITSOFTLogo() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("FITSOFTLogo", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Form_add_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Form-add-1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Form_back_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Form-back-1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Form_delete_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Form-delete-1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Form_edit_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Form-edit-1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Form_save_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Form-save-1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Form_search_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Form-search-1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property going1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("going1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property gtk_refresh() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("gtk-refresh", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property ItemDiscountRate() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ItemDiscountRate", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Items_add_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Items-add-1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Items_add_2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Items-add-2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property items_add_3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("items_add_3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property items_add_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("items_add_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Items_add_5() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Items-add-5", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property items_edit_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("items-edit-1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property items_edit_2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("items-edit-2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property items_search_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("items_search_1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property link_red() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("link_red", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property link_red_32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("link_red_32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Loading() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Loading", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property login_background_images_2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("login-background-images-2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property login_Icone() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("login_Icone", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property MAR2012_main() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MAR2012-main", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property MaximizeButtons() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MaximizeButtons", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property MedicationDoseSlip() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MedicationDoseSlip", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property MedicationDoseSlip2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MedicationDoseSlip2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property MinimizeButtons() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("MinimizeButtons", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property minus3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("minus3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property next_3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("next_3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Offers_Items() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Offers_Items", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property order_production_pms_min() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("order_production_pms-min", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Other_Income() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Other_Income", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property packground() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("packground", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photos() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photos", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property previous_3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("previous_3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Print_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Print_1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Print_Barcode() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Print_Barcode", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Print_bill() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Print_bill", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property print_parcode() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("print_parcode", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property print_price() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("print_price", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Print_Price2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Print_Price2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Print_Smoll() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Print_Smoll", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property profitsAnd_Losses() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("profitsAnd Losses", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Purchases() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Purchases", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Purchases1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Purchases1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Recyclebin() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Recyclebin", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Recyclebin24() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Recyclebin24", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property refresh() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("refresh", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property RemindAppointment() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("RemindAppointment", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property RemindAppointment2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("RemindAppointment2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property reports() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("reports", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property save_32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("save_32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Save_bill() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Save_bill", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Search3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Search3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property SerialNumber1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("SerialNumber1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property SerialNumber2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("SerialNumber2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Settings() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Settings", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Store_Entry() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Store_Entry", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Store_Out() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Store_Out", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Text_edit() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Text-edit", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property view_hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("view-hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property withdrawal_deposit() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("withdrawal-deposit", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
    End Module
End Namespace
