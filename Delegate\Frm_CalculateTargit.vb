﻿Public Class Frm_CalculateTargit

    Private Sub ChkAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkAll.CheckedChanged
        If ChkAll.Checked = True Then
            cmbRep.Enabled = False
            cmbRep.SelectedIndex = -1
        Else
            cmbRep.Enabled = True
        End If
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub frm_CalculateTargit_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbRep)
    End Sub

    Private Sub GetData()
        UpdateEmployeesAccount(cmbRep.Text)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If ChkAll.Checked = True Then
            S = "SELECT NameEmployee AS [اسم المندوب],Vendorname as [أسم العميل],GeoArea_Name as [المنطقة],itm_name as [أسم الصنف],qu_unity as [الكمية],price as [السعر],totalprice as [المبلغ الصافى],Profits as [الربح] FROM View_Delegate_All where bill_no <> N''"
        Else
            S = "SELECT NameEmployee AS [اسم المندوب],Vendorname as [أسم العميل],GeoArea_Name as [المنطقة],itm_name as [أسم الصنف],qu_unity as [الكمية],price as [السعر],totalprice as [المبلغ الصافى],Profits as [الربح] FROM View_Delegate_All where NameEmployee =N'" & cmbRep.Text.Trim & "'"
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and  bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [أسم العميل]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [اسم المندوب]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [اسم المندوب]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If ChkAll.Checked = True Then
            S = "SELECT NameEmployee AS [اسم المندوب],TotalAmount as [أجمالى الكميات],vintinval as [اجمالى صافى المبالغ] FROM EmployeesAccount where ID <> N''"
        Else
            S = "SELECT NameEmployee AS [اسم المندوب],TotalAmount as [أجمالى الكميات],vintinval as [صافى المبالغ] FROM EmployeesAccount where NameEmployee =N'" & cmbRep.Text.Trim & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [اسم المندوب]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [اسم المندوب]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [اسم المندوب]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView3.DataSource = Cls.PopulateDataView(dr)

        SumDGV()

        GetIncreaseDeficitAmount()

        'Dim RNXD As Integer
        'If DataGridView3.RowCount = 0 Then Beep() : Exit Sub
        'If (DataGridView3.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        'For i As Integer = 0 To DataGridView3.Rows.Count - 1
        '    If DataGridView3.Rows(i).Cells(1).Value = "0" Or DataGridView3.Rows(i).Cells(2).Value = "0" Then
        '        RNXD = DataGridView3.CurrentRow.Index
        '        DataGridView3.Rows.RemoveAt(RNXD)
        '    End If
        'Next
    End Sub

    Private Sub SumDGV()
        Dim SM, SM2, SM3 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM = SM + DataGridView1.Rows(i).Cells(4).Value
            SM2 = SM2 + DataGridView1.Rows(i).Cells(6).Value
        Next
        txtTotalAmount.Text = Math.Round(SM, 2)
        txtTotalNetAmount.Text = Math.Round(SM2, 2)


        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM3 = SM3 + DataGridView1.Rows(i).Cells(7).Value
        Next
        txtProfitsTotal.Text = Math.Round(SM3, 2)

    End Sub

    Private Sub btnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnShow.Click
        GetData()
    End Sub

    Private Sub GetIncreaseDeficitAmount()
        '' خصومات المبيعات

        If ChkAll.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "select sum(amnt) from Vst_disc where id<>''"
            If ChkWithoutDate.Checked = False Then
                S = S & " and  pdate >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and pdate <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
            End If
            cmd.CommandText = S
            dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then txtTotalVst_disc.Text = 0 Else txtTotalVst_disc.Text = dr(0)
        Else
            Dim aray_id As New ArrayList
            Dim aray_Vendorname As New ArrayList
            aray_id.Clear()
            aray_Vendorname.Clear()

            Dim EMPID As String = Cls.Get_Code_Value_Branch("Employees", "EMPID", "NameEmployee", cmbRep.Text)
            Dim Vendorname As String = Cls.Get_Code_Value_Branch("Customers", "Vendorname", "NameEmployee", cmbRep.Text)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select id,Vendorname from Customers where Emp_Code= N'" & EMPID & "'"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_id.Add(dr("id"))
                aray_Vendorname.Add(dr("Vendorname"))
            Loop

            Dim XVendorname As String
            For i As Integer = 0 To aray_id.Count - 1
                XVendorname = aray_Vendorname(i).ToString()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "select sum(amnt) from Vst_disc where Vendorname=N'" & XVendorname & "'"
                If ChkWithoutDate.Checked = False Then
                    S = S & " and  pdate >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and pdate <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
                End If
                cmd.CommandText = S
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    txtTotalVst_disc.Text += dr(0).ToString()
                End If
            Next
        End If
        txtTotalVst_disc.Text = Math.Round(Val(txtTotalVst_disc.Text), 2)

        '==========================================================================================

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If ChkAll.Checked = True Then
            S = "select sum(Amount) from Employee_Deficit_Increase where Deficit_Increase_Code =1"
        Else
            S = "select sum(Amount) from Employee_Deficit_Increase where Deficit_Increase_Code =1 and Employee_Name=N'" & cmbRep.Text & "'"
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and  Emp_Date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and Emp_Date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        cmd.CommandText = S
        dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then txtDeficitAmount.Text = 0 Else txtDeficitAmount.Text = dr(0)


        '==========================================================================================

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If ChkAll.Checked = True Then
            S = "select sum(Amount) from Employee_Deficit_Increase where Deficit_Increase_Code =2"
        Else
            S = "select sum(Amount) from Employee_Deficit_Increase where Deficit_Increase_Code =2 and Employee_Name=N'" & cmbRep.Text & "'"
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and  Emp_Date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and Emp_Date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        cmd.CommandText = S
        dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then txtIncreaseAmount.Text = 0 Else txtIncreaseAmount.Text = dr(0)

        '==========================================================================================

        Dim TypeTargetRateAll As String = mykey.GetValue("TypeTargetRateAll", "RatioAll")
        If TypeTargetRateAll = "RatioAll" Then
            If cmbRep.Text <> "" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "select TargetRatioMinimum from EmployeesTargetDetective"
                cmd.CommandText = S
                dr = cmd.ExecuteReader : dr.Read()
                If dr(0) Is DBNull.Value Then txtRate.Text = 0 Else txtRate.Text = dr(0)
            End If
        End If
        If TypeTargetRateAll = "RatioPerEmployee" Then
            If cmbRep.Text <> "" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "select Emp_Rate_Hour from Employees where NameEmployee =N'" & cmbRep.Text & "'"
                cmd.CommandText = S
                dr = cmd.ExecuteReader : dr.Read()
                If dr(0) Is DBNull.Value Then txtRate.Text = 0 Else txtRate.Text = dr(0)
            End If
        End If
        If TypeTargetRateAll = "TargetRate" Then
            Dim TargetNumber, TargetIncentive, TotalTargetNumber, TotalTargetRate As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TargetNumber,TargetIncentive,TargetRatioMinimum from EmployeesTargetDetective"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                TargetNumber = dr("TargetNumber")
                TargetIncentive = dr("TargetIncentive")
                txtRate.Text = dr("TargetRatioMinimum")
            End If

            TotalTargetNumber = Format(Val(TargetNumber) * Val(txtRate.Text) / 100, "Fixed")
            If TotalTargetNumber <= txtTotalNetAmount.Text Then
                TotalTargetRate = Val(txtTotalNetAmount.Text) / Val(TargetNumber) * 100
                txtNetAmount.Text = Format(Val(TargetIncentive) * Val(TotalTargetRate) / 100, "Fixed")
            End If

            Dim NetafterAmount As Double = Val(txtNetAmount.Text) + Val(txtIncreaseAmount.Text) - Val(txtDeficitAmount.Text)
            txtNetAmount.Text = Val(NetafterAmount)
        End If

        If TypeTargetRateAll <> "TargetRate" Then
            Dim NetafterAmount As Double = Val(txtTotalNetAmount.Text) + Val(txtIncreaseAmount.Text) - Val(txtDeficitAmount.Text)
            txtNetAmount.Text = Format(Val(NetafterAmount) * Val(txtRate.Text) / 100, "Fixed")
        End If


    End Sub

    Private Sub btnprint_Click(sender As Object, e As EventArgs) Handles btnprint.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
    End Sub

    Private Sub UpdateEmployeesAccount(ByVal NameEmployee As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete from EmployeesAccount where NameEmployee =N''" : cmd.ExecuteNonQuery()

        If NameEmployee <> "" Then
            Dim aray_ID As New ArrayList
            Dim aray_NameEmployee As New ArrayList

            aray_ID.Clear()
            aray_NameEmployee.Clear()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ID,NameEmployee from EmployeesAccount where NameEmployee =N'" & NameEmployee & "'"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_ID.Add(dr("ID"))
                aray_NameEmployee.Add(dr("NameEmployee"))
            Loop
            Dim ActionBool As Boolean = False
            For i As Integer = 0 To aray_ID.Count - 1
                If ActionBool = False Then
                    ActionBool = True
                Else
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "delete from EmployeesAccount where ID=N'" & aray_ID(i).ToString & "'" : cmd.ExecuteNonQuery()
                End If
            Next
        End If
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class