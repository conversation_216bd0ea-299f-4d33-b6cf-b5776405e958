﻿Public Class Form5
    Private Sub Form6_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        FillCheckedListBox()
    End Sub
    Private Sub FillCheckedListBox()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT sname FROM Items ORDER BY sname ASC"
        dr = cmd.ExecuteReader()
        Do While dr.Read = True
            CheckedListBox1.Items.Add(dr("sname"))
        Loop
    End Sub

    ' الحدث الذي يتم استدعاؤه عند تغيير حالة تحديد العناصر في CheckedListBox
    Private Sub checkedListBox1_ItemCheck(ByVal sender As Object, ByVal e As ItemCheckEventArgs) Handles CheckedListBox1.ItemCheck
        'Dim selectedItem As String = CheckedListBox1.Items(e.Index).ToString()
        'If e.NewValue = CheckState.Checked Then
        '    MessageBox.Show("تم تحديد العنصر: " & selectedItem)
        'End If
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Dim XX As String = ""
        Dim checkedItem As Object
        For Each checkedItem In CheckedListBox1.CheckedItems
            XX = checkedItem
            'cmd = Cn.CreateCommand
            'cmd.CommandText = "INSERT INTO Mall (Company) VALUES ('" & checkedItem & "')"
            'cmd.ExecuteNonQuery()
        Next

    End Sub
End Class