﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnClose2.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0wAlKG3rvAANJ7k15FYZgZYCgDDjM0sSGiAhFFRJoiSFDEgNFQJFZE
        sRAUVLAHJAgoMRhFVCxvRtaLrqy89/Ly++Osb+2z97n77L3PWhcAkqcvl5cGSwGQyhPwgzyc6RGRUXTs
        AIABHmCAKQBMVka6X7B7CBDJy82FniFyAl8EAfB6WLwCcNPQM4BOB/+fpFnpfIHomAARm7M5GSwRF4g4
        JUuQLrbPipgalyxmGCVmvihBEcuJOWGRDT77LLKjmNmpPLaIxTmns1PZYu4V8bZMIUfEiK+ICzO5nCwR
        3xKxRoowlSviN+LYVA4zAwAUSWwXcFiJIjYRMYkfEuQi4uUA4EgJX3HcVyzgZAvEl3JJS8/hcxMSBXQd
        li7d1NqaQffkZKVwBALDACYrmcln013SUtOZvBwAFu/8WTLi2tJFRbY0tba0NDQzMv2qUP91829K3NtF
        ehn4uWcQrf+L7a/80hoAYMyJarPziy2uCoDOLQDI3fti0zgAgKSobx3Xv7oPTTwviQJBuo2xcVZWlhGX
        wzISF/QP/U+Hv6GvvmckPu6P8tBdOfFMYYqALq4bKy0lTcinZ6QzWRy64Z+H+B8H/nUeBkGceA6fwxNF
        hImmjMtLELWbx+YKuGk8Opf3n5r4D8P+pMW5FonS+BFQY4yA1HUqQH7tBygKESDR+8Vd/6NvvvgwIH55
        4SqTi3P/7zf9Z8Gl4iWDm/A5ziUohM4S8jMX98TPEqABAUgCKpAHykAd6ABDYAasgC1wBG7AG/iDEBAJ
        VgMWSASpgA+yQB7YBApBMdgJ9oBqUAcaQTNoBcdBJzgFzoNL4Bq4AW6D+2AUTIBnYBa8BgsQBGEhMkSB
        5CEVSBPSh8wgBmQPuUG+UBAUCcVCCRAPEkJ50GaoGCqDqqF6qBn6HjoJnYeuQIPQXWgMmoZ+h97BCEyC
        qbASrAUbwwzYCfaBQ+BVcAK8Bs6FC+AdcCXcAB+FO+Dz8DX4NjwKP4PnEIAQERqiihgiDMQF8UeikHiE
        j6xHipAKpAFpRbqRPuQmMorMIG9RGBQFRUcZomxRnqhQFAu1BrUeVYKqRh1GdaB6UTdRY6hZ1Ec0Ga2I
        1kfboL3QEegEdBa6EF2BbkK3oy+ib6Mn0K8xGAwNo42xwnhiIjFJmLWYEsw+TBvmHGYQM46Zw2Kx8lh9
        rB3WH8vECrCF2CrsUexZ7BB2AvsGR8Sp4Mxw7rgoHA+Xj6vAHcGdwQ3hJnELeCm8Jt4G749n43PwpfhG
        fDf+On4Cv0CQJmgT7AghhCTCJkIloZVwkfCA8JJIJKoRrYmBRC5xI7GSeIx4mThGfEuSIemRXEjRJCFp
        B+kQ6RzpLuklmUzWIjuSo8gC8g5yM/kC+RH5jQRFwkjCS4ItsUGiRqJDYkjiuSReUlPSSXK1ZK5kheQJ
        yeuSM1J4KS0pFymm1HqpGqmTUiNSc9IUaVNpf+lU6RLpI9JXpKdksDJaMm4ybJkCmYMyF2TGKQhFneJC
        YVE2UxopFykTVAxVm+pFTaIWU7+jDlBnZWVkl8mGyWbL1sielh2lITQtmhcthVZKO04bpr1borTEaQln
        yfYlrUuGlszLLZVzlOPIFcm1yd2WeydPl3eTT5bfJd8p/1ABpaCnEKiQpbBf4aLCzFLqUtulrKVFS48v
        vacIK+opBimuVTyo2K84p6Ss5KGUrlSldEFpRpmm7KicpFyufEZ5WoWiYq/CVSlXOavylC5Ld6Kn0Cvp
        vfRZVUVVT1Whar3qgOqCmrZaqFq+WpvaQ3WCOkM9Xr1cvUd9VkNFw08jT6NF454mXpOhmai5V7NPc15L
        Wytca6tWp9aUtpy2l3audov2Ax2yjoPOGp0GnVu6GF2GbrLuPt0berCehV6iXo3edX1Y31Kfq79Pf9AA
        bWBtwDNoMBgxJBk6GWYathiOGdGMfI3yjTqNnhtrGEcZ7zLuM/5oYmGSYtJoct9UxtTbNN+02/R3Mz0z
        llmN2S1zsrm7+QbzLvMXy/SXcZbtX3bHgmLhZ7HVosfig6WVJd+y1XLaSsMq1qrWaoRBZQQwShiXrdHW
        ztYbrE9Zv7WxtBHYHLf5zdbQNtn2iO3Ucu3lnOWNy8ft1OyYdvV2o/Z0+1j7A/ajDqoOTIcGh8eO6o5s
        xybHSSddpySno07PnU2c+c7tzvMuNi7rXM65Iq4erkWuA24ybqFu1W6P3NXcE9xb3Gc9LDzWepzzRHv6
        eO7yHPFS8mJ5NXvNelt5r/Pu9SH5BPtU+zz21fPl+3b7wX7efrv9HqzQXMFb0ekP/L38d/s/DNAOWBPw
        YyAmMCCwJvBJkGlQXlBfMCU4JvhI8OsQ55DSkPuhOqHC0J4wybDosOaw+XDX8LLw0QjjiHUR1yIVIrmR
        XVHYqLCopqi5lW4r96yciLaILoweXqW9KnvVldUKq1NWn46RjGHGnIhFx4bHHol9z/RnNjDn4rziauNm
        WS6svaxnbEd2OXuaY8cp40zG28WXxU8l2CXsTphOdEisSJzhunCruS+SPJPqkuaT/ZMPJX9KCU9pS8Wl
        xqae5Mnwknm9acpp2WmD6frphemja2zW7Fkzy/fhN2VAGasyugRU0c9Uv1BHuEU4lmmfWZP5Jiss60S2
        dDYvuz9HL2d7zmSue+63a1FrWWt78lTzNuWNrXNaV78eWh+3vmeD+oaCDRMbPTYe3kTYlLzpp3yT/LL8
        V5vDN3cXKBVsLBjf4rGlpVCikF84stV2a9021DbutoHt5turtn8sYhddLTYprih+X8IqufqN6TeV33za
        Eb9joNSydP9OzE7ezuFdDrsOl0mX5ZaN7/bb3VFOLy8qf7UnZs+VimUVdXsJe4V7Ryt9K7uqNKp2Vr2v
        Tqy+XeNc01arWLu9dn4fe9/Qfsf9rXVKdcV17w5wD9yp96jvaNBqqDiIOZh58EljWGPft4xvm5sUmoqb
        PhziHRo9HHS4t9mqufmI4pHSFrhF2DJ9NProje9cv+tqNWytb6O1FR8Dx4THnn4f+/3wcZ/jPScYJ1p/
        0Pyhtp3SXtQBdeR0zHYmdo52RXYNnvQ+2dNt293+o9GPh06pnqo5LXu69AzhTMGZT2dzz86dSz83cz7h
        /HhPTM/9CxEXbvUG9g5c9Ll4+ZL7pQt9Tn1nL9tdPnXF5srJq4yrndcsr3X0W/S3/2TxU/uA5UDHdavr
        XTesb3QPLh88M+QwdP6m681Lt7xuXbu94vbgcOjwnZHokdE77DtTd1PuvriXeW/h/sYH6AdFD6UeVjxS
        fNTws+7PbaOWo6fHXMf6Hwc/vj/OGn/2S8Yv7ycKnpCfVEyqTDZPmU2dmnafvvF05dOJZ+nPFmYKf5X+
        tfa5zvMffnP8rX82YnbiBf/Fp99LXsq/PPRq2aueuYC5R69TXy/MF72Rf3P4LeNt37vwd5MLWe+x7ys/
        6H7o/ujz8cGn1E+f/gUDmPP8usTo0wAAAAlwSFlzAAALEwAACxMBAJqcGAAABetJREFUSEuVlXtwTFcc
        x88+stl7N7vZyEuGVELViFeM0X8M4vFHR6OptxhKUYkg6KjRovIQ6oaQlnjV6HU9KvtIVjbJJpEHQdZ4
        zhiDP5QQktBImLaKyLe/e3cTS/3jzHz23Pnd8/1+7zn37D1Mzd5qqmDGIokoX7q9Q9A7mL29ibEe/clk
        rk7Henn8WFfArr7d2RgNS1gVYcr6NiJQWNndJKwINwnLw0xCaqhRWBZCBBuFpd2MwpKgACHFHCAsDjQI
        ySaDkGTkhWSjQVjI6TMpYOz7AtQUcBz7c4AzFUAtcaocqHEB1URVCToqi9FR4URHeRFel53Aa5cD7aWF
        aC8uwCunDa+KCPsxNC9ZBArY6hug/YLXzEHeJrSdqUYbGbSWOtBaUoDWYjthw5MiK56csOCJIx9PCo+j
        peAYWuxH0WI7gharhBaLhD/zRaV/6rSgftYUUMAYJYDWtGd237DfXki78ehgHh79+jMe7c9F874daN6b
        g+Y929CUtxVNu7LRtFNA0y8/oTF3Exp3ZKFx+0Y05mTi4bYMPMxOw0PhR6plonVNKiaqWCbZ61gIY9Gb
        +4RKz7ZnKMKHsmBrOpGG++mrUb9uBR5sXouGTT+gIet7NGxcg/sZq3Gf6vWrFyv9vfUrcW/dctxbm4r6
        9SvwePEcTFKzbAowMHmXZESHSA9S5+PS6GFwx/SEe0AP1EYG41rqQtw6sBtlvULh7tcd7v4RCmc/CkZd
        fBzO5uWiLCYS5/qEoq5fOOr6hsE9JAq3x32KSVq1oASEaFRRa8MM0rkwDiU6hlKOwalmOJPwGVpbW/EX
        APeWTBxjdE/PUEy9a0AfVBQWwuoqgyUrA/lmTrlXyjOUkN5tYD4BWlXUqkCtVMapYNd7+J1MrokH8ZzM
        29ra8A/154TNOEB1R0w/nHQWwepwwGqz4URFOfbGfAKLhilaG1HDqzHZzxsQTDNYbtRKdj81DmtUChLN
        YBenxxW7XQmRZ9Lw+DHKhC1w5ed7zO1kXlKM7LjRyKXgLi3h0qkxRecN6EYBSwxayabVkLEXjQYiiXL9
        /HCVQh6Q+cULF1Bx6hQZ22G1WnGi2ImtcXHIkc3V6i7tIcLlp8XUzoAgtSoqmddKJXodrLo32AgLBe1R
        0ZQPHUJ5VRUsFgssVovy5Dn05HlkbveO7dRZiBpOh2n+3oBANYtaxGmlSp5DMecDz8NJBs6YGLjI2Fpg
        V8zttDSVtTVwrPkOR+l+iV6vjO3UOQl3AIdpeo0nwKhiUQv0WumsyYgq4xtO0tNXDh6MSqdTWXOLvCyu
        UlSersalK+fR+OwxLm/PgY1CqgyGLl0lcdVsxHTOGxBAAfP8tdKlIDPqzB7OkeDMsGGKuaVzt9DO2Tdh
        Aqr37MKDtmY8//dvev3A9Z07UUbj6wIDPVriZkggZvDeAJ7+aHP8ddL1yEhc8XIpNBSnSWhxucjcCge9
        UHHiRDjJyBURgdtl9AH0aeXjx+NieLiivUzc7dXj7YCveL10O3YIbsbG4obMoEGoHTECDlFEIS3LkWnT
        UB0djVtDh+LGwIGo6d8f92lHdZB5RXo6Snv3fqONHYrG2AGYaeh8BxSQYjZIjePG4s64cQp3idsjR+I8
        LUkxmbuHD0e9ty7zx6hRuDh2LGpTUlBBpnfoWq579OPRGjcCiQHeADqNoteEmw4/nT4VTWTmS+PkyWhI
        SEDzO3WFKVNQHx////r0GXg+KR6zjFrPx44CIjf2NIvtKcloS5JJ6uKpF9+aL++9n7wYmD8bMwM0myhA
        Lx84fvG8Zj6WfI2XaZl4sXYdXq77MGSNAl23Z2bhZeLn+FjNEijAc2Sq6GDI6x1chJVJwMY0IGMDkE5s
        2IAOQu6R5kWuy8hjOskkjawj2pMSsSxET18a5ucbIDfNKH82b2kIv39BN16cZeLEL3m9GM/pxQTqJxv0
        4vQAvZho5MTZgZw418yJ84N48RsamxzMi0tDDWJKCLd3sIbNJi+dx5KaTwCjALnJvwaCdvAH408BPo2x
        /wCOn1cd2WLtBAAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btnCloseSaveEnd.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0wAlKG3rvAANJ7k15FYZgZYCgDDjM0sSGiAhFFRJoiSFDEgNFQJFZE
        sRAUVLAHJAgoMRhFVCxvRtaLrqy89/Ly++Osb+2z97n77L3PWhcAkqcvl5cGSwGQyhPwgzyc6RGRUXTs
        AIABHmCAKQBMVka6X7B7CBDJy82FniFyAl8EAfB6WLwCcNPQM4BOB/+fpFnpfIHomAARm7M5GSwRF4g4
        JUuQLrbPipgalyxmGCVmvihBEcuJOWGRDT77LLKjmNmpPLaIxTmns1PZYu4V8bZMIUfEiK+ICzO5nCwR
        3xKxRoowlSviN+LYVA4zAwAUSWwXcFiJIjYRMYkfEuQi4uUA4EgJX3HcVyzgZAvEl3JJS8/hcxMSBXQd
        li7d1NqaQffkZKVwBALDACYrmcln013SUtOZvBwAFu/8WTLi2tJFRbY0tba0NDQzMv2qUP91829K3NtF
        ehn4uWcQrf+L7a/80hoAYMyJarPziy2uCoDOLQDI3fti0zgAgKSobx3Xv7oPTTwviQJBuo2xcVZWlhGX
        wzISF/QP/U+Hv6GvvmckPu6P8tBdOfFMYYqALq4bKy0lTcinZ6QzWRy64Z+H+B8H/nUeBkGceA6fwxNF
        hImmjMtLELWbx+YKuGk8Opf3n5r4D8P+pMW5FonS+BFQY4yA1HUqQH7tBygKESDR+8Vd/6NvvvgwIH55
        4SqTi3P/7zf9Z8Gl4iWDm/A5ziUohM4S8jMX98TPEqABAUgCKpAHykAd6ABDYAasgC1wBG7AG/iDEBAJ
        VgMWSASpgA+yQB7YBApBMdgJ9oBqUAcaQTNoBcdBJzgFzoNL4Bq4AW6D+2AUTIBnYBa8BgsQBGEhMkSB
        5CEVSBPSh8wgBmQPuUG+UBAUCcVCCRAPEkJ50GaoGCqDqqF6qBn6HjoJnYeuQIPQXWgMmoZ+h97BCEyC
        qbASrAUbwwzYCfaBQ+BVcAK8Bs6FC+AdcCXcAB+FO+Dz8DX4NjwKP4PnEIAQERqiihgiDMQF8UeikHiE
        j6xHipAKpAFpRbqRPuQmMorMIG9RGBQFRUcZomxRnqhQFAu1BrUeVYKqRh1GdaB6UTdRY6hZ1Ec0Ga2I
        1kfboL3QEegEdBa6EF2BbkK3oy+ib6Mn0K8xGAwNo42xwnhiIjFJmLWYEsw+TBvmHGYQM46Zw2Kx8lh9
        rB3WH8vECrCF2CrsUexZ7BB2AvsGR8Sp4Mxw7rgoHA+Xj6vAHcGdwQ3hJnELeCm8Jt4G749n43PwpfhG
        fDf+On4Cv0CQJmgT7AghhCTCJkIloZVwkfCA8JJIJKoRrYmBRC5xI7GSeIx4mThGfEuSIemRXEjRJCFp
        B+kQ6RzpLuklmUzWIjuSo8gC8g5yM/kC+RH5jQRFwkjCS4ItsUGiRqJDYkjiuSReUlPSSXK1ZK5kheQJ
        yeuSM1J4KS0pFymm1HqpGqmTUiNSc9IUaVNpf+lU6RLpI9JXpKdksDJaMm4ybJkCmYMyF2TGKQhFneJC
        YVE2UxopFykTVAxVm+pFTaIWU7+jDlBnZWVkl8mGyWbL1sielh2lITQtmhcthVZKO04bpr1borTEaQln
        yfYlrUuGlszLLZVzlOPIFcm1yd2WeydPl3eTT5bfJd8p/1ABpaCnEKiQpbBf4aLCzFLqUtulrKVFS48v
        vacIK+opBimuVTyo2K84p6Ss5KGUrlSldEFpRpmm7KicpFyufEZ5WoWiYq/CVSlXOavylC5Ld6Kn0Cvp
        vfRZVUVVT1Whar3qgOqCmrZaqFq+WpvaQ3WCOkM9Xr1cvUd9VkNFw08jT6NF454mXpOhmai5V7NPc15L
        Wytca6tWp9aUtpy2l3audov2Ax2yjoPOGp0GnVu6GF2GbrLuPt0berCehV6iXo3edX1Y31Kfq79Pf9AA
        bWBtwDNoMBgxJBk6GWYathiOGdGMfI3yjTqNnhtrGEcZ7zLuM/5oYmGSYtJoct9UxtTbNN+02/R3Mz0z
        llmN2S1zsrm7+QbzLvMXy/SXcZbtX3bHgmLhZ7HVosfig6WVJd+y1XLaSsMq1qrWaoRBZQQwShiXrdHW
        ztYbrE9Zv7WxtBHYHLf5zdbQNtn2iO3Ucu3lnOWNy8ft1OyYdvV2o/Z0+1j7A/ajDqoOTIcGh8eO6o5s
        xybHSSddpySno07PnU2c+c7tzvMuNi7rXM65Iq4erkWuA24ybqFu1W6P3NXcE9xb3Gc9LDzWepzzRHv6
        eO7yHPFS8mJ5NXvNelt5r/Pu9SH5BPtU+zz21fPl+3b7wX7efrv9HqzQXMFb0ekP/L38d/s/DNAOWBPw
        YyAmMCCwJvBJkGlQXlBfMCU4JvhI8OsQ55DSkPuhOqHC0J4wybDosOaw+XDX8LLw0QjjiHUR1yIVIrmR
        XVHYqLCopqi5lW4r96yciLaILoweXqW9KnvVldUKq1NWn46RjGHGnIhFx4bHHol9z/RnNjDn4rziauNm
        WS6svaxnbEd2OXuaY8cp40zG28WXxU8l2CXsTphOdEisSJzhunCruS+SPJPqkuaT/ZMPJX9KCU9pS8Wl
        xqae5Mnwknm9acpp2WmD6frphemja2zW7Fkzy/fhN2VAGasyugRU0c9Uv1BHuEU4lmmfWZP5Jiss60S2
        dDYvuz9HL2d7zmSue+63a1FrWWt78lTzNuWNrXNaV78eWh+3vmeD+oaCDRMbPTYe3kTYlLzpp3yT/LL8
        V5vDN3cXKBVsLBjf4rGlpVCikF84stV2a9021DbutoHt5turtn8sYhddLTYprih+X8IqufqN6TeV33za
        Eb9joNSydP9OzE7ezuFdDrsOl0mX5ZaN7/bb3VFOLy8qf7UnZs+VimUVdXsJe4V7Ryt9K7uqNKp2Vr2v
        Tqy+XeNc01arWLu9dn4fe9/Qfsf9rXVKdcV17w5wD9yp96jvaNBqqDiIOZh58EljWGPft4xvm5sUmoqb
        PhziHRo9HHS4t9mqufmI4pHSFrhF2DJ9NProje9cv+tqNWytb6O1FR8Dx4THnn4f+/3wcZ/jPScYJ1p/
        0Pyhtp3SXtQBdeR0zHYmdo52RXYNnvQ+2dNt293+o9GPh06pnqo5LXu69AzhTMGZT2dzz86dSz83cz7h
        /HhPTM/9CxEXbvUG9g5c9Ll4+ZL7pQt9Tn1nL9tdPnXF5srJq4yrndcsr3X0W/S3/2TxU/uA5UDHdavr
        XTesb3QPLh88M+QwdP6m681Lt7xuXbu94vbgcOjwnZHokdE77DtTd1PuvriXeW/h/sYH6AdFD6UeVjxS
        fNTws+7PbaOWo6fHXMf6Hwc/vj/OGn/2S8Yv7ycKnpCfVEyqTDZPmU2dmnafvvF05dOJZ+nPFmYKf5X+
        tfa5zvMffnP8rX82YnbiBf/Fp99LXsq/PPRq2aueuYC5R69TXy/MF72Rf3P4LeNt37vwd5MLWe+x7ys/
        6H7o/ujz8cGn1E+f/gUDmPP8usTo0wAAAAlwSFlzAAALEwAACxMBAJqcGAAAE7lJREFUeF7lmgl0VFWa
        x28llaSyVCVhEQSSsK9hExABMSFAAypI04Bgiwvd0Noq2K22MCqLgCEBhOx7QpKqVFJLkspWVamkKpU9
        bEKIDY6OgtM97Zwzi3Omp5U+2nf+302VviIVCNBH+5zcc/7nvfvqvVfv9/+++9336hXjnA9qed04mOR1
        42CS142DSV43DiZ53TiY5HXjYJLXjYNJXjcOJnndeCt9/fXXzGKxMLPZzBoaGlhXVxe7ePEi6+zsYucv
        dis+/eyaz+efX0e/k7W0tDCn08nsdjuzWq2sqqqKlZaWsoyMDJaYmCj67nPV1dWxxsZGIYfDwTo6Olhz
        c7M4vrW93ffqJ58uuHDpkvLs2bPs3LlzjJZnzpxh7e3tYp/6+npmMplYamoqe+edd9iBAwe86maePhtu
        J28GXPjgA9b94e9nftZz3n79+rVN1//1D7c1ICEhgWk0GqbT6cQ2Wqd96NxkQE9Pj4Brx3muXr36dI+p
        +K9XznWeutB9mZ0D/D+MART5K598NulyTeHHxicn8I6UN77+tz99sbED229nQElJCdPr9aysrEysSw24
        fPkya0MW9Fy5utl57PWvU6IZNz4Xyz+6eC7tQneP7Czgf3QDbDYb++DDq2N76jSXS9eN5KdjFULtSb+9
        0d3Ts6Wlrffi7saA7p4PWc/Vj55wHv/dX9Jmy3na/GCeNMOH655azH9/tiP7fHePz49qAI3ZtjPnR1+q
        1Zwve2IUL1oewotXD+VFK8P46RgFb0l86a+Xui9ta2ppZXaM6zsxwA4DPv7s2qqm9/d8mTbXj6ctCObp
        D6p4+gIVPzVdxks2zuOX250F5y9dlre1tf3wBlD0HW2dIy5UFXbp1o92wQ/hxat6VbQijBcsDeCOw7/4
        5sL5c883OpwCbEAGWOtZ1/kLsU0n9vxX+twACbwSWaDkqfOU/OQ0GS96PJpftJs1nec/8PtBDfjqq69Y
        Y3Pb0DPl+c36DZECXi2BF/oJmRAuTLC9/dS3XR1tOyy2xtsYoBHwrZ1di+0n9n6ROU/BMwCfuVDFM1wG
        pJIBDyh5CnRymg8vWDWZn6k1lrZ2nfX/uxhAFb0/0dRD092VT66pOgy5Fv2GCA94NdK/BNKiL4RtJSvD
        eTGZ8Mb6b1vsDTtNNXX9GlCkVjNbU/Nce+Lrf8h7MJDnLgzmeYtCed5DKp4DE7KQ/hnzVTwNGUAGJM8J
        4Sem+PDcuPG8VV+sa2xuDbhnA44fP96vkpOTWW6RJrA+/4TJ8DNEfkUvvJpAAV62ZijXQXrI4JIe23Wr
        wrn2kQBu2f3oDau57lmt3tDHAG2ZjhkqTdG2+Fc/LV4UzAsXhfCiJaG8aHEoP71IxQtgQB6yIAcmZM5T
        8dS5Sp40O4Sfgo5PlvOshyN4Q0G6rsZqC0hNTbt7A1JSUryK4LMLigJqs+K1BF+8QsnVAKTIa1zwhkeH
        ciNU8RiEZaXQEF4Bk8ohfUwAr/31ir8YdWXPpGfnwoCjTKvVsjKdnhVrSyda3v31ldIlIbwE0i4NxTKU
        a2CAGgYUIQtOkwkwIBsZkA4Dkmcr+alZMGFmrwnpD47kNakJusycXMW+ffvYwYMHvUrKS/LoZGVleVdO
        nrz85Ftqw88iePHK7+Ep+lqKNGDLAV4JVUHVbmG7EPYxYd+K2EBufn7hNyXpx3bFHz/JklJSWWZuXmTN
        Pz3XXf6IkuuWKrn+kTCuXxrGyx4O5aUwQQsTNA+F8uKFyAbUgVwYkAkDUsgAwJ+MDuHvQ8cm+cGcYTz3
        wJsJp5AFSUlJXiXlJXl0nn322T565tnn2DPbto3Kejzyj/o1KgFP4O5xT9GnyFc+DlCo5vFhvBYyY90M
        E0h1+LwO+9Vi/5plQbx+63SeE//2i0dOnArV797YUROr4hUxKl4ZG4ZlGC+HCQZkgR4G6GCAFrVADQMK
        kQF5MCALBqTSEIABBP/+9GCegfWd98u7R6hUs8dOmMDGjRvnVVJekmfnFi0ixHdRelz4F0aAiUoPUfrT
        uCcDTAAl+DrIgn2sWNYLDeX1+MyKfazY14LhYEbxtGya8mfjjpVXLMvDYEoor4kL49XLwnlVbDg3wYAK
        ZEE5ssAAA8pggAZDoAgG5KMGZKEApiL9RfRn9MK/Pi7wX4J8ZdNdl9tvk/IKZo/Obdowhc+cQw+p/tkI
        IGEAVLYGY1xiAEW+fu0wboMa0G9EXwifN2C/Bphgg3G2laG8YbmKWzFdWiBzXDivgwG1yIJqZEEVDKh8
        OIwbF4dxHepACYpgEWaBfMwAwgCKPuDTooP5nvGBnw/1k81zXeYtm5RXMHt0BtBC/GRTDi5UXaGoqzHf
        lyGi7gyoBShFX8BDdqw7sHTACAc+d2A/Bwyw4xg7zGvE8Q2YKm0woH55OLcuC+NmGFAHA2pggAkGlCMD
        9KgBWmGAkucj/bOQ/ikzAQ8T3poQ+MdwP9l81+Xdtkl5BbNHZ4AtWC6btH+BqofASwFiBJQJ67WAtADW
        BtnX9sI3QU4Y0UzC507s50TWNMGEJhzrgAH2FTADBtgwDKwwwAwDamFAFQyoQPqTASUogEUY/3kwIBPp
        nw3tmxj074j8ItdlDahJeQWzR+cOGkwYv3++spsibwCMCVC1gLOgbwMsRb8JRjihFqwL4bMW7NMCw1ow
        DJphgBNZ0AQD7DCgEcPAhhpgjcFwQBGsRhEUBqAAkgGFSP/cOcgCDIH9kwL/dJ+/zyOuyxlwk/IKZo/O
        HTaYEHVwgfIDSn2a72vcBkBuA9zwrY8N4234XAgGtGH/VhjQAgOcGAIOGGCHAQ1kwCMwAAWwGulfgejr
        kf4lGP+nEf1CKHFq8JdjAnxWui7jjpqUVzB7dO6iBcllYw4tUJ6jql+NLDADUBgANUnh0W/HZ0LYrx0G
        tAG+FUOgGQY4Ae9AEWyEAfWYBeqQ/tWIfgWqvw7VX4P0L4WOTQv+j+H+sjjX199xk/IKZo/OXTaYMOrw
        AtUZqvY039cDshFqAjSlfasEvgOfdwC+A9Fv/0k4b4MBLTCgGQY0kQEY/1aM/7olqAGo/uUwoAwG6JEB
        p6YH/2dUoM9a19feVZPyCmaPzj00mDAyfoGy04miZ0WEhQFQM9Tqgm8HfCc+65QaAHiSMADRb0T0rYi+
        MAC3wEakfwU9B8wK/r8ohc8619fddZPyCmaPzj02OWMj9kX7t9KUR/M9TXnNAG7FksY9pT7Bdwp4aCWG
        AQpgK8Z/M6LvQPS/MwDjvwrRNwE+Mzr4y0h/9oTra+6pSXkFs7QTFxc3IMXGxrJZs2ax6OhoNmPGDDZ9
        +nS2ePFitvXpZ9iSpTGjUxYGf0qRdwDWiWpPFZ+KHo17iryIPuDbAd+2HAYg+s2Y/hyAb0D6W1H9azH+
        TTCgen4w3z11iOXFN/YGbcOt+caNG9mGDRvYunXr2Jo1a9iyZcvY+vXr2Y4dO9jmzZvZ6tWrWUxMTL+S
        8pL6bBiIbty4IX4Sq6ioYOXl5WJJP5DamtsDLKlvnzYtV/2tAelNNztkQDPAqeK3iciHA57S3hX9OMwE
        uAFqQvTtgLeh+lsQ/RpU/0pEX/9ACC+fF8LPHfhFp83hHFFdZ8Z3GsWvyDk5OezkyZMsPj5ePLLv3buX
        7dy5k23durVf3czSZ8NARAZUVlaKHzLoBw5aVphtcnvq79TOdZgJVoTxeqQ43enRzQ7N9TTdtULuMU9y
        w7uj3wgD6hF9M6Jfg7m/AsXPgMqvmR3MDdFy7vjNprO68or7i9W98PSY/t5774nnfFq+9tprbPv27WzT
        pk396maWPhsGIqkBZWU6VlSql5mT9uTUPz4ET3RKXocI1yPFGyC602vCsplExQ7jvYVEaY9x73TB2zHu
        GwBvBXwdol9NxQ+VX4+bnxLc+BTgvl8z1YebX/zpueIy3aic3Lwf3wCKvtpQycqPv5lseTScG2KUvApw
        dQC1Ir1tgG+EHFh3YJsTnwkB3gn4JknkG5D69Uh9M+BrMParEP1yPPnp5obwEtz7F+K+P3taEM+b5MsL
        XtrWlVWoHpWckvLjGUDjvqjUwLRHXz9auXoY1y5VciPATACshSwQPeA0QHSfT7e6dKdHNzsOTHd2V8Wn
        okfj3uqCr6XCB/hKRN+I6OsQfQ3u+wvx6JuL5/60yQqeNsGPZ+zY0pGcnj7myNGEH94A+mU4p0jD8t/d
        vc+Aca1+OISXYSwbIRMga6A6yALV4wGHZAN4Awn3+TbA22i8I+2tmO+p6NUh9Wsx7gneBHiKvh63vaWI
        vgbRP40hQAZkTA3mSRMVPCnKhxeufbA7If5I5P5Dh2HAkXs34KOPPrqlPv74Y3b92jV29dPrLPfd376h
        RlQLFodwNYDKkM5GAFZC1TCiFkt6vqfHWwtET3n0kGNF1C2AtwDejGd9AU5jHvCU9hT5CsAbAE/R1yL6
        xXjmJwNypsGAKcE8eVIgPzZewU+MlvHsFbMuHzp4IOpIQuK9G/DCCy/0K5pedu3axfYeOMTiX93+khrz
        d+4iRAYwakCVwgQDVAFQE1SNNK9Bvxai5/s67FOHfWuR7jU0zUHVuNV13+xUAp6KnlHAhwh4d/SLYEA+
        op+N6KdPDhIGnJgQyN8bF8gTYUJqzHSYsH/sa2/uYduff94ruFtSXpJH55VXXulXL7/8Mtv9xh721kvb
        txXEDf0mZxEiApgCPLYWA0wLQB1kJBOwrITopy36Zacaomf7KlR5E8ArEfVK3OZS9Ok21zhfKaY7qvhU
        9Moo8hJ4in4eop81JUgYkDQRBowP5EdhwKEoBU8YJeMnFk28/Jtdr4x/fsdOgG7sA+6WlJfk0aEIe9Nu
        6FevvMp2bdu4OTdu+I1cpH02YHKgfJhABpTAiFL6IRPrRiHAAZpUjnFOv+wYAWxAuhsRddzjf7NnYmCH
        9gHlV3rAa5HyWjzrU8WnoqeWwOcDPgfRzwB86qQgfgrRPw4D4scq+BEYcBg6er8PPzw3smf7tp9P2Lj5
        Sa/wJCkvyaNDt5netGHTZvbLDWvWp8YM/+/8JUhFQGcBKBvKhwmFkBrbtFAppEOfRL/q6gFNv+npEPFS
        RLuM5ndo7Qj/JLo3jxnit7cU8CWALwI4iSo+gdPcT5En+EyKPuCTJwbxkzAgcZyCv+cygHQoIoAnjpTx
        PTMj2zZsftJnM0ygW+ObJeUleXTmz5/vVbPmPci2z7qvWBsTItKewMmATMDlQgVYp7c4akizWMVLsKSf
        soUQcfpFVwNoDcY5Pd39dKR/Fth9yQBqS8PlbxL4aUQ9dwZNd71TXjalPcEj8pT6KYCn6J9AAUyQwB+O
        wBIGHB3jz5eGK44uXRYnnglWrVrVR1JekkdnypQpQpMnT/YUtgUGBQ17elyAVYNxTQZkkgCXhcjmIcIF
        gKXXWPQWp1io92VGEYBPA7wQBU4DbRkVUAzmgF7079viUPlruVTpaarDDU/GVEC7xjylPUXeDS+i74aP
        xDrgE6AlSt8TzNeXzcBD2pw5c9js2bP7SMpL8uhERESwsWPHChOmTZvGpk6dKtZpCQPoOsO3jQ8005in
        6Is3t1AWRC8w86B8AH+nBUrxIiOXbmdR6Dbc71+Ec/jRiby1hSr57nQCh5IBTtWeCt4piNKeCh/Bx0vg
        KfKJkQH8YaX8fddpbtmkvCTPDppMJmOBgYFs+PDh4k0KGUGPvEG9BlALfXqcopZeXhK8+919BpSJSGeT
        EOksgGcCnF5jFWCJtC/Bsd+dpL82X+n78ikqdBBNdSQqeMegBIJ3Fz6CBzjBLwuVp+NQWe8Zbt2kvILZ
        o3NTIzNUKhWLioqSGkBN9fRYRXUhhgH9ayMN8PTHBXp1LQRoeoObgsJG0d8yOqASxwT3Hnr7NjfE98Vj
        EwL/BompLh6SFrxeeAU/BvgYlTwNhwwInpqUVzB7dPppZATpphby8yiF6TTGu/ufG/TePgXg9O6eXl9n
        Y/3J0QHV2FfZe8jA25wQ350J4wO/TUTkj0jghQBPkV8TJqch9V0xHUiT8gpmj86dt+CtkQEVNN7d/9xI
        ccNjfXukwu4jY+Gufe+4jVf4bNkXqfgzjXsp/PtYrg6Tq7GLvHfPgTcpr2D26NxdC9oSEWCgd/epuIsj
        +BzAPzUmwIqcGeLa567bGH+fJ/ZFBPyvMEFEXkGR1+KjwN497qxJeQWzR+fumyJ2uF9qOgzIx1D4ZZTC
        KZex4a7P7rmN9pc99s6YgP9JgQlrw+UGbOozjQ60SXkFs0enb6OBT/JxicYbpR3JXyLaxy9mmF/azrGK
        LoWPLBJ9au7j7kWi+ET4y9bGhfpmS4x1fzdNq27RddE1uo9z67sm5RXMHp3eRgfQCehk5DRVbxU0FBoB
        jYKioPHQZGgaFA1NxYEzfWXsAayPg8ZCdLGhEB1PonWqCTQ06HzDXOu0LQxy70dFk+T+3jFQBETGToHo
        ++i/AFOhSRBdC31G13YfROekY2nqIgZiEUZIeQWzR+d7x8gAcpTGGV0InZBOPBoisIkQgc+E5kL0bn6B
        a0n9ORCZQxfkBiQRDJlCRo50idbp3PSZ1Ay36HMCJGg6N70KJ5HR9D20na6FrokCQ9dI56Pz0LUrILcB
        MimvYPboeDa3Ee60J0PITTohGUOZEQK5o+UWbSfnaT9KUTrWm6RpezvR90q/Uyr399Hn9J20r3Q4iMi7
        m5RXMHt07qy5s8WtH7p5++7bXoeUVzDfvGGwyevGwSSvGweTvG4cTPK6cTDJ68bBJK8bB484+3+IhFdX
        +l7d6AAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btnClose.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0wAlKG3rvAANJ7k15FYZgZYCgDDjM0sSGiAhFFRJoiSFDEgNFQJFZE
        sRAUVLAHJAgoMRhFVCxvRtaLrqy89/Ly++Osb+2z97n77L3PWhcAkqcvl5cGSwGQyhPwgzyc6RGRUXTs
        AIABHmCAKQBMVka6X7B7CBDJy82FniFyAl8EAfB6WLwCcNPQM4BOB/+fpFnpfIHomAARm7M5GSwRF4g4
        JUuQLrbPipgalyxmGCVmvihBEcuJOWGRDT77LLKjmNmpPLaIxTmns1PZYu4V8bZMIUfEiK+ICzO5nCwR
        3xKxRoowlSviN+LYVA4zAwAUSWwXcFiJIjYRMYkfEuQi4uUA4EgJX3HcVyzgZAvEl3JJS8/hcxMSBXQd
        li7d1NqaQffkZKVwBALDACYrmcln013SUtOZvBwAFu/8WTLi2tJFRbY0tba0NDQzMv2qUP91829K3NtF
        ehn4uWcQrf+L7a/80hoAYMyJarPziy2uCoDOLQDI3fti0zgAgKSobx3Xv7oPTTwviQJBuo2xcVZWlhGX
        wzISF/QP/U+Hv6GvvmckPu6P8tBdOfFMYYqALq4bKy0lTcinZ6QzWRy64Z+H+B8H/nUeBkGceA6fwxNF
        hImmjMtLELWbx+YKuGk8Opf3n5r4D8P+pMW5FonS+BFQY4yA1HUqQH7tBygKESDR+8Vd/6NvvvgwIH55
        4SqTi3P/7zf9Z8Gl4iWDm/A5ziUohM4S8jMX98TPEqABAUgCKpAHykAd6ABDYAasgC1wBG7AG/iDEBAJ
        VgMWSASpgA+yQB7YBApBMdgJ9oBqUAcaQTNoBcdBJzgFzoNL4Bq4AW6D+2AUTIBnYBa8BgsQBGEhMkSB
        5CEVSBPSh8wgBmQPuUG+UBAUCcVCCRAPEkJ50GaoGCqDqqF6qBn6HjoJnYeuQIPQXWgMmoZ+h97BCEyC
        qbASrAUbwwzYCfaBQ+BVcAK8Bs6FC+AdcCXcAB+FO+Dz8DX4NjwKP4PnEIAQERqiihgiDMQF8UeikHiE
        j6xHipAKpAFpRbqRPuQmMorMIG9RGBQFRUcZomxRnqhQFAu1BrUeVYKqRh1GdaB6UTdRY6hZ1Ec0Ga2I
        1kfboL3QEegEdBa6EF2BbkK3oy+ib6Mn0K8xGAwNo42xwnhiIjFJmLWYEsw+TBvmHGYQM46Zw2Kx8lh9
        rB3WH8vECrCF2CrsUexZ7BB2AvsGR8Sp4Mxw7rgoHA+Xj6vAHcGdwQ3hJnELeCm8Jt4G749n43PwpfhG
        fDf+On4Cv0CQJmgT7AghhCTCJkIloZVwkfCA8JJIJKoRrYmBRC5xI7GSeIx4mThGfEuSIemRXEjRJCFp
        B+kQ6RzpLuklmUzWIjuSo8gC8g5yM/kC+RH5jQRFwkjCS4ItsUGiRqJDYkjiuSReUlPSSXK1ZK5kheQJ
        yeuSM1J4KS0pFymm1HqpGqmTUiNSc9IUaVNpf+lU6RLpI9JXpKdksDJaMm4ybJkCmYMyF2TGKQhFneJC
        YVE2UxopFykTVAxVm+pFTaIWU7+jDlBnZWVkl8mGyWbL1sielh2lITQtmhcthVZKO04bpr1borTEaQln
        yfYlrUuGlszLLZVzlOPIFcm1yd2WeydPl3eTT5bfJd8p/1ABpaCnEKiQpbBf4aLCzFLqUtulrKVFS48v
        vacIK+opBimuVTyo2K84p6Ss5KGUrlSldEFpRpmm7KicpFyufEZ5WoWiYq/CVSlXOavylC5Ld6Kn0Cvp
        vfRZVUVVT1Whar3qgOqCmrZaqFq+WpvaQ3WCOkM9Xr1cvUd9VkNFw08jT6NF454mXpOhmai5V7NPc15L
        Wytca6tWp9aUtpy2l3audov2Ax2yjoPOGp0GnVu6GF2GbrLuPt0berCehV6iXo3edX1Y31Kfq79Pf9AA
        bWBtwDNoMBgxJBk6GWYathiOGdGMfI3yjTqNnhtrGEcZ7zLuM/5oYmGSYtJoct9UxtTbNN+02/R3Mz0z
        llmN2S1zsrm7+QbzLvMXy/SXcZbtX3bHgmLhZ7HVosfig6WVJd+y1XLaSsMq1qrWaoRBZQQwShiXrdHW
        ztYbrE9Zv7WxtBHYHLf5zdbQNtn2iO3Ucu3lnOWNy8ft1OyYdvV2o/Z0+1j7A/ajDqoOTIcGh8eO6o5s
        xybHSSddpySno07PnU2c+c7tzvMuNi7rXM65Iq4erkWuA24ybqFu1W6P3NXcE9xb3Gc9LDzWepzzRHv6
        eO7yHPFS8mJ5NXvNelt5r/Pu9SH5BPtU+zz21fPl+3b7wX7efrv9HqzQXMFb0ekP/L38d/s/DNAOWBPw
        YyAmMCCwJvBJkGlQXlBfMCU4JvhI8OsQ55DSkPuhOqHC0J4wybDosOaw+XDX8LLw0QjjiHUR1yIVIrmR
        XVHYqLCopqi5lW4r96yciLaILoweXqW9KnvVldUKq1NWn46RjGHGnIhFx4bHHol9z/RnNjDn4rziauNm
        WS6svaxnbEd2OXuaY8cp40zG28WXxU8l2CXsTphOdEisSJzhunCruS+SPJPqkuaT/ZMPJX9KCU9pS8Wl
        xqae5Mnwknm9acpp2WmD6frphemja2zW7Fkzy/fhN2VAGasyugRU0c9Uv1BHuEU4lmmfWZP5Jiss60S2
        dDYvuz9HL2d7zmSue+63a1FrWWt78lTzNuWNrXNaV78eWh+3vmeD+oaCDRMbPTYe3kTYlLzpp3yT/LL8
        V5vDN3cXKBVsLBjf4rGlpVCikF84stV2a9021DbutoHt5turtn8sYhddLTYprih+X8IqufqN6TeV33za
        Eb9joNSydP9OzE7ezuFdDrsOl0mX5ZaN7/bb3VFOLy8qf7UnZs+VimUVdXsJe4V7Ryt9K7uqNKp2Vr2v
        Tqy+XeNc01arWLu9dn4fe9/Qfsf9rXVKdcV17w5wD9yp96jvaNBqqDiIOZh58EljWGPft4xvm5sUmoqb
        PhziHRo9HHS4t9mqufmI4pHSFrhF2DJ9NProje9cv+tqNWytb6O1FR8Dx4THnn4f+/3wcZ/jPScYJ1p/
        0Pyhtp3SXtQBdeR0zHYmdo52RXYNnvQ+2dNt293+o9GPh06pnqo5LXu69AzhTMGZT2dzz86dSz83cz7h
        /HhPTM/9CxEXbvUG9g5c9Ll4+ZL7pQt9Tn1nL9tdPnXF5srJq4yrndcsr3X0W/S3/2TxU/uA5UDHdavr
        XTesb3QPLh88M+QwdP6m681Lt7xuXbu94vbgcOjwnZHokdE77DtTd1PuvriXeW/h/sYH6AdFD6UeVjxS
        fNTws+7PbaOWo6fHXMf6Hwc/vj/OGn/2S8Yv7ycKnpCfVEyqTDZPmU2dmnafvvF05dOJZ+nPFmYKf5X+
        tfa5zvMffnP8rX82YnbiBf/Fp99LXsq/PPRq2aueuYC5R69TXy/MF72Rf3P4LeNt37vwd5MLWe+x7ys/
        6H7o/ujz8cGn1E+f/gUDmPP8usTo0wAAAAlwSFlzAAALEwAACxMBAJqcGAAABetJREFUSEuVlXtwTFcc
        x88+stl7N7vZyEuGVELViFeM0X8M4vFHR6OptxhKUYkg6KjRovIQ6oaQlnjV6HU9KvtIVjbJJpEHQdZ4
        zhiDP5QQktBImLaKyLe/e3cTS/3jzHz23Pnd8/1+7zn37D1Mzd5qqmDGIokoX7q9Q9A7mL29ibEe/clk
        rk7Henn8WFfArr7d2RgNS1gVYcr6NiJQWNndJKwINwnLw0xCaqhRWBZCBBuFpd2MwpKgACHFHCAsDjQI
        ySaDkGTkhWSjQVjI6TMpYOz7AtQUcBz7c4AzFUAtcaocqHEB1URVCToqi9FR4URHeRFel53Aa5cD7aWF
        aC8uwCunDa+KCPsxNC9ZBArY6hug/YLXzEHeJrSdqUYbGbSWOtBaUoDWYjthw5MiK56csOCJIx9PCo+j
        peAYWuxH0WI7gharhBaLhD/zRaV/6rSgftYUUMAYJYDWtGd237DfXki78ehgHh79+jMe7c9F874daN6b
        g+Y929CUtxVNu7LRtFNA0y8/oTF3Exp3ZKFx+0Y05mTi4bYMPMxOw0PhR6plonVNKiaqWCbZ61gIY9Gb
        +4RKz7ZnKMKHsmBrOpGG++mrUb9uBR5sXouGTT+gIet7NGxcg/sZq3Gf6vWrFyv9vfUrcW/dctxbm4r6
        9SvwePEcTFKzbAowMHmXZESHSA9S5+PS6GFwx/SEe0AP1EYG41rqQtw6sBtlvULh7tcd7v4RCmc/CkZd
        fBzO5uWiLCYS5/qEoq5fOOr6hsE9JAq3x32KSVq1oASEaFRRa8MM0rkwDiU6hlKOwalmOJPwGVpbW/EX
        APeWTBxjdE/PUEy9a0AfVBQWwuoqgyUrA/lmTrlXyjOUkN5tYD4BWlXUqkCtVMapYNd7+J1MrokH8ZzM
        29ra8A/154TNOEB1R0w/nHQWwepwwGqz4URFOfbGfAKLhilaG1HDqzHZzxsQTDNYbtRKdj81DmtUChLN
        YBenxxW7XQmRZ9Lw+DHKhC1w5ed7zO1kXlKM7LjRyKXgLi3h0qkxRecN6EYBSwxayabVkLEXjQYiiXL9
        /HCVQh6Q+cULF1Bx6hQZ22G1WnGi2ImtcXHIkc3V6i7tIcLlp8XUzoAgtSoqmddKJXodrLo32AgLBe1R
        0ZQPHUJ5VRUsFgssVovy5Dn05HlkbveO7dRZiBpOh2n+3oBANYtaxGmlSp5DMecDz8NJBs6YGLjI2Fpg
        V8zttDSVtTVwrPkOR+l+iV6vjO3UOQl3AIdpeo0nwKhiUQv0WumsyYgq4xtO0tNXDh6MSqdTWXOLvCyu
        UlSersalK+fR+OwxLm/PgY1CqgyGLl0lcdVsxHTOGxBAAfP8tdKlIDPqzB7OkeDMsGGKuaVzt9DO2Tdh
        Aqr37MKDtmY8//dvev3A9Z07UUbj6wIDPVriZkggZvDeAJ7+aHP8ddL1yEhc8XIpNBSnSWhxucjcCge9
        UHHiRDjJyBURgdtl9AH0aeXjx+NieLiivUzc7dXj7YCveL10O3YIbsbG4obMoEGoHTECDlFEIS3LkWnT
        UB0djVtDh+LGwIGo6d8f92lHdZB5RXo6Snv3fqONHYrG2AGYaeh8BxSQYjZIjePG4s64cQp3idsjR+I8
        LUkxmbuHD0e9ty7zx6hRuDh2LGpTUlBBpnfoWq579OPRGjcCiQHeADqNoteEmw4/nT4VTWTmS+PkyWhI
        SEDzO3WFKVNQHx////r0GXg+KR6zjFrPx44CIjf2NIvtKcloS5JJ6uKpF9+aL++9n7wYmD8bMwM0myhA
        Lx84fvG8Zj6WfI2XaZl4sXYdXq77MGSNAl23Z2bhZeLn+FjNEijAc2Sq6GDI6x1chJVJwMY0IGMDkE5s
        2IAOQu6R5kWuy8hjOskkjawj2pMSsSxET18a5ucbIDfNKH82b2kIv39BN16cZeLEL3m9GM/pxQTqJxv0
        4vQAvZho5MTZgZw418yJ84N48RsamxzMi0tDDWJKCLd3sIbNJi+dx5KaTwCjALnJvwaCdvAH408BPo2x
        /wCOn1cd2WLtBAAAAABJRU5ErkJggg==
</value>
  </data>
</root>