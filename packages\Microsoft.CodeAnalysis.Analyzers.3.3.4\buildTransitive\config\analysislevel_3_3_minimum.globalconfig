# NOTE: Requires **VS2019 16.7** or later

# Rules from '3.3.0' release with 'Minimum' analysis mode
# Description: Rules with enabled-by-default state from '3.3.0' release with 'Minimum' analysis mode. Rules that are first released in a version later than '3.3.0' are disabled.

is_global = true

global_level = -100


# RS1009: Only internal implementations of this interface are allowed
dotnet_diagnostic.RS1009.severity = warning

# RS1034: Prefer 'IsKind' for checking syntax kinds
dotnet_diagnostic.RS1034.severity = none

# RS1035: Do not use APIs banned for analyzers
dotnet_diagnostic.RS1035.severity = none

# RS1036: Specify analyzer banned API enforcement setting
dotnet_diagnostic.RS1036.severity = none

# RS1037: Add "CompilationEnd" custom tag to compilation end diagnostic descriptor
dotnet_diagnostic.RS1037.severity = none
