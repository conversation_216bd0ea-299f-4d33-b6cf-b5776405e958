﻿Imports System.Data.SqlClient

Public Class FrmSmartDataProcessor

    Private Sub Form_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' توسيط النافذة
        Me.Location = New Point(CInt((Screen.PrimaryScreen.WorkingArea.Width / 2) - (Me.Width / 2)),
                               CInt((Screen.PrimaryScreen.WorkingArea.Height / 2) - (Me.Height / 2)))

        ' إعداد الكنترولز
        InitializeControls()

        ' إعداد BackgroundWorker
        backgroundWorker1.WorkerReportsProgress = True
        backgroundWorker1.WorkerSupportsCancellation = True

        AddHandler backgroundWorker1.DoWork, AddressOf BackgroundWorker1_DoWork
        AddHandler backgroundWorker1.ProgressChanged, AddressOf BackgroundWorker1_ProgressChanged
        AddHandler backgroundWorker1.RunWorkerCompleted, AddressOf BackgroundWorker1_RunWorkerCompleted

        ' إعداد واجهة المستخدم
        'btnUpdate.Text = "Update"
        'lblProgress.Text = "من فضلك انتظر قليل ......"
        'lblStatus.Text = "0"
        'lblCount.Text = "0"
        'pictureBox1.Visible = False

        Control.CheckForIllegalCrossThreadCalls = False
    End Sub

    Private Sub InitializeControls()
        ' يمكنك إضافة كود إنشاء الكنترولز هنا إذا لم تكن موجودة في Designer
        ' أو ربطها بالكنترولز الموجودة
    End Sub

    Private Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs)
        'Try
        '    Dim CountLoop As Long = 0
        '    Dim totalOperations As Integer = 0
        '    Dim currentOperation As Integer = 0

        '    ' Arrays للبيانات
        '    Dim aray_id As New ArrayList
        '    Dim aray_itm_id As New ArrayList
        '    Dim aray_itm_Unity As New ArrayList
        '    Dim aray_price As New ArrayList
        '    Dim aray_qu As New ArrayList
        '    Dim aray_qu_unity As New ArrayList

        '    ' متغيرات للمعالجة
        '    Dim id, itemId, itemUnity, price, qu, qu_unity As String

        '    ' إعداد الاتصال
        '    Using connection As New SqlConnection(IM.ConnectionString)
        '        connection.Open()
        '        Using cmd As New SqlCommand("", connection)
        '            Using dr As SqlDataReader = Nothing

        '                ' ========== المرحلة 1: معالجة البيانات من BilltINData ==========
        '                BackgroundWorker1.ReportProgress(5, "جاري جلب البيانات التي تحتاج معالجة...")

        '                ' جلب البيانات التي تحتاج معالجة
        '                aray_id.Clear() : aray_itm_id.Clear() : aray_itm_Unity.Clear()
        '                aray_price.Clear() : aray_qu.Clear() : aray_qu_unity.Clear()

        '                cmd.CommandText = "SELECT id, itm_id, itm_Unity, price, qu, qu_unity 
        '                                 FROM BilltINData 
        '                                 WHERE (itm_Unity IS NULL OR itm_Unity = '' OR qu > qu_unity)"

        '                Dim tempDr = cmd.ExecuteReader()
        '                Do While tempDr.Read()
        '                    aray_id.Add(If(IsDBNull(tempDr(0)), "", tempDr(0).ToString()))
        '                    aray_itm_id.Add(If(IsDBNull(tempDr(1)), "", tempDr(1).ToString()))
        '                    aray_itm_Unity.Add(If(IsDBNull(tempDr(2)), "", tempDr(2).ToString()))
        '                    aray_price.Add(If(IsDBNull(tempDr(3)), "0", tempDr(3).ToString()))
        '                    aray_qu.Add(If(IsDBNull(tempDr(4)), "0", tempDr(4).ToString()))
        '                    aray_qu_unity.Add(If(IsDBNull(tempDr(5)), "0", tempDr(5).ToString()))
        '                Loop
        '                tempDr.Close()

        '                totalOperations = aray_id.Count
        '                BackgroundWorker1.ReportProgress(10, $"تم العثور على {totalOperations} سجل يحتاج معالجة")

        '                ' معالجة كل سجل
        '                For i As Integer = 0 To aray_id.Count - 1
        '                    id = aray_id(i).ToString()
        '                    itemId = aray_itm_id(i).ToString()
        '                    itemUnity = aray_itm_Unity(i).ToString()
        '                    price = aray_price(i).ToString()
        '                    qu = aray_qu(i).ToString()
        '                    qu_unity = aray_qu_unity(i).ToString()

        '                    ' التحقق من الوحدة وتحديثها إذا كانت فارغة
        '                    If String.IsNullOrEmpty(itemUnity) Then
        '                        itemUnity = GetUnitByPrice(cmd, itemId, CDec(price))
        '                    End If

        '                    ' تحديث qu_unity إذا كانت qu أكبر
        '                    If CDec(qu) > CDec(qu_unity) Then
        '                        qu_unity = qu
        '                    End If

        '                    ' تحديث السجل
        '                    cmd.CommandText = $"UPDATE BilltINData 
        '                                     SET itm_Unity = N'{itemUnity}', qu_unity = {qu_unity} 
        '                                     WHERE id = {id}"
        '                    cmd.ExecuteNonQuery()

        '                    ' تحديث شريط التقدم
        '                    currentOperation += 1
        '                    Dim progressPercent As Integer = CInt((currentOperation / totalOperations) * 60) + 10
        '                    BackgroundWorker1.ReportProgress(progressPercent, $"معالجة السجل {currentOperation} من {totalOperations}")

        '                    Threading.Thread.Sleep(10) ' للسماح بتحديث الواجهة
        '                Next

        '                ' ========== المرحلة 2: حذف التكرارات ==========
        '                BackgroundWorker1.ReportProgress(70, "جاري حذف التكرارات من جدول الأصناف...")

        '                ' حذف التكرارات من جدول Items
        '                Dim aray_items_id As New ArrayList
        '                Dim aray_sname As New ArrayList
        '                Dim aray_stores As New ArrayList

        '                cmd.CommandText = "SELECT id, sname, Stores FROM Items 
        '                                 WHERE sname IN (SELECT sname FROM Items GROUP BY sname HAVING COUNT(*) > 1) 
        '                                 ORDER BY sname, Stores"

        '                tempDr = cmd.ExecuteReader()
        '                Do While tempDr.Read()
        '                    aray_items_id.Add(tempDr(0))
        '                    aray_sname.Add(tempDr(1))
        '                    aray_stores.Add(tempDr(2))
        '                Loop
        '                tempDr.Close()

        '                ' حذف التكرارات
        '                Dim prevSname As String = ""
        '                Dim prevStores As String = ""

        '                For i As Integer = 0 To aray_items_id.Count - 1
        '                    Dim currentSname = aray_sname(i).ToString()
        '                    Dim currentStores = aray_stores(i).ToString()

        '                    If currentSname = prevSname AndAlso currentStores = prevStores Then
        '                        cmd.CommandText = $"DELETE FROM Items WHERE id = {aray_items_id(i)}"
        '                        cmd.ExecuteNonQuery()
        '                    Else
        '                        prevSname = currentSname
        '                        prevStores = currentStores
        '                    End If
        '                Next

        '                BackgroundWorker1.ReportProgress(80, "جاري حذف التكرارات من بيانات الفواتير...")

        '                ' حذف التكرارات من جدول BilltINData
        '                Dim aray_bill_id As New ArrayList
        '                Dim aray_itm_name As New ArrayList
        '                Dim aray_bill_stores As New ArrayList

        '                cmd.CommandText = "SELECT id, itm_name, Stores FROM BilltINData 
        '                                 WHERE itm_name IN (SELECT itm_name FROM BilltINData GROUP BY itm_name HAVING COUNT(*) > 1) 
        '                                 AND (bill_no = N'جرد') 
        '                                 ORDER BY itm_name, Stores"

        '                tempDr = cmd.ExecuteReader()
        '                Do While tempDr.Read()
        '                    aray_bill_id.Add(tempDr(0))
        '                    aray_itm_name.Add(tempDr(1))
        '                    aray_bill_stores.Add(tempDr(2))
        '                Loop
        '                tempDr.Close()

        '                prevSname = ""
        '                prevStores = ""

        '                For i As Integer = 0 To aray_bill_id.Count - 1
        '                    Dim currentItemName = aray_itm_name(i).ToString()
        '                    Dim currentStores = aray_bill_stores(i).ToString()

        '                    If currentItemName = prevSname AndAlso currentStores = prevStores Then
        '                        cmd.CommandText = $"DELETE FROM BilltINData WHERE id = {aray_bill_id(i)}"
        '                        cmd.ExecuteNonQuery()
        '                    Else
        '                        prevSname = currentItemName
        '                        prevStores = currentStores
        '                    End If
        '                Next

        '                ' ========== المرحلة 3: تحديث الحسابات ==========
        '                BackgroundWorker1.ReportProgress(90, "جاري تحديث حسابات الموردين والعملاء...")

        '                ' حذف البيانات المالية القديمة
        '                cmd.CommandText = "DELETE FROM Date_Finance"
        '                cmd.ExecuteNonQuery()

        '                ' تحديث حسابات الموردين
        '                Dim vendors As New ArrayList
        '                cmd.CommandText = "SELECT DISTINCT Vendorname FROM vendors"
        '                tempDr = cmd.ExecuteReader()
        '                Do While tempDr.Read()
        '                    vendors.Add(tempDr(0))
        '                Loop
        '                tempDr.Close()

        '                For Each vendor In vendors
        '                    IM.VendorAccountTotal(vendor.ToString())
        '                Next

        '                ' تحديث حسابات العملاء
        '                Dim customers As New ArrayList
        '                cmd.CommandText = "SELECT DISTINCT Vendorname FROM customers"
        '                tempDr = cmd.ExecuteReader()
        '                Do While tempDr.Read()
        '                    customers.Add(tempDr(0))
        '                Loop
        '                tempDr.Close()

        '                For Each customer In customers
        '                    IM.CustomerAccountTotal(customer.ToString())
        '                Next

        '                BackgroundWorker1.ReportProgress(100, "تم الانتهاء من جميع العمليات بنجاح")

        '            End Using
        '        End Using
        '    End Using

        'Catch ex As Exception
        '    BackgroundWorker1.ReportProgress(0, $"حدث خطأ: {ex.Message}")
        'End Try
    End Sub

    ''' <summary>
    ''' جلب الوحدة المناسبة بناءً على سعر الشراء
    ''' </summary>
    Private Function GetUnitByPrice(cmd As SqlCommand, itemId As String, price As Decimal) As String
        Try
            Dim originalCommandText = cmd.CommandText
            cmd.CommandText = "SELECT TOP 1 Unity_Name 
                              FROM ItemsUnity 
                              WHERE itm_id = @itemId 
                              AND (@price BETWEEN ISNULL(TinPriceUnit, 0) AND ISNULL(SalPriceUnit, *********))
                              ORDER BY TinPriceUnit ASC"

            cmd.Parameters.Clear()
            cmd.Parameters.AddWithValue("@itemId", itemId)
            cmd.Parameters.AddWithValue("@price", price)

            Dim result = cmd.ExecuteScalar()
            cmd.CommandText = originalCommandText
            cmd.Parameters.Clear()

            If result IsNot Nothing AndAlso Not IsDBNull(result) Then
                Return result.ToString()
            End If

        Catch ex As Exception
            Console.WriteLine($"خطأ في جلب الوحدة للصنف {itemId}: {ex.Message}")
        End Try

        Return "PIECE" ' وحدة افتراضية
    End Function

    Private Sub BackgroundWorker1_ProgressChanged(sender As Object, e As System.ComponentModel.ProgressChangedEventArgs)
        'Try
        '    If progressBar1 IsNot Nothing Then
        '        progressBar1.Value = Math.Min(e.ProgressPercentage, 100)
        '    End If

        '    If lblStatus IsNot Nothing Then
        '        lblStatus.Text = e.ProgressPercentage.ToString() & "%"
        '    End If

        '    If lblProgress IsNot Nothing AndAlso e.UserState IsNot Nothing Then
        '        lblProgress.Text = e.UserState.ToString()
        '    End If

        'Catch ex As Exception
        '    ' تجاهل الأخطاء في تحديث الواجهة
        'End Try
    End Sub

    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As System.ComponentModel.RunWorkerCompletedEventArgs)
        'Try
        '    If PictureBox1 IsNot Nothing Then
        '        PictureBox1.Visible = False
        '    End If

        '    If btnUpdate IsNot Nothing Then
        '        btnUpdate.Enabled = True
        '        btnUpdate.Text = "Update"
        '    End If

        '    If progressBar1 IsNot Nothing Then
        '        progressBar1.Value = 100
        '    End If

        '    If lblStatus IsNot Nothing Then
        '        lblStatus.Text = "100%"
        '    End If

        '    If lblProgress IsNot Nothing Then
        '        lblProgress.Text = "تم الانتهاء من جميع العمليات"
        '    End If

        '    MsgBox("تم تحديث قاعدة البيانات بنجاح", MsgBoxStyle.Information, "نجح")
        '    Me.Close()

        'Catch ex As Exception
        '    MsgBox($"حدث خطأ: {ex.Message}", MsgBoxStyle.Critical, "خطأ")
        'End Try
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs)
        'Try
        '    If btnUpdate IsNot Nothing Then
        '        btnUpdate.Enabled = False
        '        btnUpdate.Text = "جاري المعالجة..."
        '        btnUpdate.Refresh()
        '    End If

        '    If pictureBox1 IsNot Nothing Then
        '        pictureBox1.Visible = True
        '        pictureBox1.Refresh()
        '    End If

        '    If progressBar1 IsNot Nothing Then
        '        progressBar1.Value = 0
        '    End If

        '    If lblStatus IsNot Nothing Then
        '        lblStatus.Text = "0%"
        '    End If

        '    If lblProgress IsNot Nothing Then
        '        lblProgress.Text = "بدء المعالجة الذكية للبيانات..."
        '    End If

        '    backgroundWorker1.RunWorkerAsync()

        'Catch ex As Exception
        '    MsgBox($"خطأ في بدء المعالجة: {ex.Message}", MsgBoxStyle.Critical, "خطأ")
        'End Try
    End Sub

    ''' <summary>
    ''' جلب عدد السجلات التي تحتاج معالجة
    ''' </summary>
    Public Function GetRecordsNeedingProcessingCount() As Integer
        Try
            Using connection As New SqlConnection(constring)
                connection.Open()

                Dim query As String = "SELECT COUNT(*) 
                                     FROM BilltINData 
                                     WHERE (itm_Unity IS NULL OR itm_Unity = '' OR qu > qu_unity)"

                Using command As New SqlCommand(query, connection)
                    Return Convert.ToInt32(command.ExecuteScalar())
                End Using
            End Using

        Catch ex As Exception
            Return 0
        End Try
    End Function

    ''' <summary>
    ''' عرض تقرير بالسجلات التي تحتاج معالجة
    ''' </summary>
    Public Function GetRecordsNeedingProcessing() As DataTable
        Dim dataTable As New DataTable()

        Try
            Using connection As New SqlConnection(constring)
                connection.Open()

                Dim query As String = "SELECT TOP 100 id, itm_id, itm_name, itm_Unity, price, qu, qu_unity,
                                     CASE 
                                         WHEN itm_Unity IS NULL OR itm_Unity = '' THEN 'وحدة فارغة'
                                         ELSE 'وحدة موجودة'
                                     END AS UnitStatus,
                                     CASE 
                                         WHEN qu > qu_unity THEN 'يحتاج تحديث'
                                         ELSE 'لا يحتاج تحديث'
                                     END AS QuantityStatus
                                     FROM BilltINData 
                                     WHERE (itm_Unity IS NULL OR itm_Unity = '' OR qu > qu_unity)
                                     ORDER BY id"

                Using adapter As New SqlDataAdapter(query, connection)
                    adapter.Fill(dataTable)
                End Using
            End Using

        Catch ex As Exception
            MessageBox.Show($"خطأ في جلب التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        Return dataTable
    End Function

End Class
