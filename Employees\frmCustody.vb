﻿Imports System.Data.SqlClient

Public Class frmCustody
    Dim con As New SqlConnection(constring)
    Dim cmd As New SqlCommand
    Dim dr As SqlDataReader
    Dim CodeID As String

    Private Sub frmCustody_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            con.Open()
            cmd.Connection = con
            LoadEmployees()
            LoadCustodyStatus()
            MAXRECORD("Custody", "CustodyID")
            txtCustodyID.Text = CodeID
            Header()
        Catch ex As Exception
            MsgBox("Error: " & ex.Message, MsgBoxStyle.Critical)
        End Try
    End Sub

    Private Sub LoadEmployees()
        cmbEmployee.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT EMPID, NameEmployee FROM Employees ORDER BY NameEmployee"
        dr = cmd.ExecuteReader
        While dr.Read
            cmbEmployee.Items.Add(dr("NameEmployee"))
        End While
        dr.Close()
    End Sub

    Private Sub LoadCustodyStatus()
        cmbStatus.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT CustodyStatusNameAr FROM CustodyStatus ORDER BY CustodyStatusID"
        dr = cmd.ExecuteReader
        While dr.Read
            cmbStatus.Items.Add(dr("CustodyStatusNameAr"))
        End While
        dr.Close()
        cmbStatus.SelectedIndex = 0 ' افتراضياً "غير مسلمة"
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If Validate_Text() = False Then Exit Sub

        Try
            Dim EmployeeID As String = Cls.Get_Code_Value_More("Employees", "EMPID", "NameEmployee =N'" & cmbEmployee.Text & "'")
            Dim StatusID As String = Cls.Get_Code_Value_More("CustodyStatus", "CustodyStatusID", "CustodyStatusNameAr =N'" & cmbStatus.Text & "'")

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Dim S As String = "INSERT INTO Custody (CustodyID, EMPID, Amount, ReceiptNumber, PaymentMethod, Notes, Status, CreatedBy) VALUES ("
            S &= "N'" & txtCustodyID.Text & "', "
            S &= "N'" & EmployeeID & "', "
            S &= "N'" & txtAmount.Text & "', "
            S &= "N'" & txtReceiptNo.Text & "', "
            S &= "N'" & cmbPaymentMethod.Text & "', "
            S &= "N'" & txtNotes.Text & "', "
            S &= "N'" & StatusID & "', "
            S &= "N'" & UserName & "')"

            cmd.CommandText = S
            cmd.ExecuteNonQuery()

            CLEAR_ALL()
            Header()
            MAXRECORD("Custody", "CustodyID")
            txtCustodyID.Text = CodeID
            cmbEmployee.Focus()

            MsgBox("تم حفظ بيانات العهدة بنجاح", MsgBoxStyle.Information, "حفظ")
        Catch ex As Exception
            MsgBox("Error: " & ex.Message, MsgBoxStyle.Critical)
        End Try
    End Sub

    Function Validate_Text() As Boolean
        If Trim(cmbEmployee.Text) = "" Then
            MsgBox("فضلاً أدخل اسم الموظف", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            cmbEmployee.Focus() : Return False
        End If

        If Trim(txtAmount.Text) = "" Then
            MsgBox("فضلاً أدخل المبلغ", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtAmount.Focus() : Return False
        End If

        If Trim(txtReceiptNo.Text) = "" Then
            MsgBox("فضلاً أدخل رقم الإيصال", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtReceiptNo.Focus() : Return False
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT * FROM Custody WHERE CustodyID =N'" & txtCustodyID.Text & "'"
        dr = cmd.ExecuteReader()
        If dr.HasRows Then
            MsgBox("عفواً يوجد كود مسجل مسبقاً بنفس الرقم", MsgBoxStyle.Exclamation)
            dr.Close() : Return False
        End If
        dr.Close()

        Return True
    End Function

    Private Sub Header()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        Dim S As String = "SELECT c.CustodyID AS [كود], e.NameEmployee AS [الموظف], c.Amount AS [المبلغ], " &
                         "cs.CustodyStatusNameAr AS [الحالة], c.ReceiptNumber AS [رقم الإيصال], " &
                         "c.PaymentMethod AS [طريقة الدفع], c.Notes AS [ملاحظات] " &
                         "FROM Custody c " &
                         "INNER JOIN Employees e ON c.EMPID = e.EMPID " &
                         "INNER JOIN CustodyStatus cs ON c.Status = cs.CustodyStatusID " &
                         "ORDER BY c.CustodyID DESC"

        cmd.CommandText = S
        dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
        DataGridView1.Columns(0).Visible = False ' إخفاء العمود الأول (الكود)
    End Sub

    Private Sub CLEAR_ALL()
        txtAmount.Clear()
        txtReceiptNo.Clear()
        txtNotes.Clear()
        cmbPaymentMethod.SelectedIndex = 0
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لا توجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If

        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات المحددة؟", MsgBoxStyle.YesNo + MsgBoxStyle.Exclamation, "تأكيد الحذف")
        If x = vbNo Then Exit Sub

        Try
            For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
                Dim CustodyID As String = DataGridView1.SelectedRows(i).Cells(0).Value.ToString()

                ' حذف تسليم العهدة أولاً إذا وجد
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "DELETE FROM CustodyDelivery WHERE CustodyID =N'" & CustodyID & "'"
                cmd.ExecuteNonQuery()

                ' ثم حذف العهدة
                cmd.CommandText = "DELETE FROM Custody WHERE CustodyID =N'" & CustodyID & "'"
                cmd.ExecuteNonQuery()
            Next

            Header()
            CLEAR_ALL()
            MAXRECORD("Custody", "CustodyID")
            txtCustodyID.Text = CodeID
            cmbEmployee.Focus()

            MsgBox("تم حذف البيانات بنجاح", MsgBoxStyle.Information, "حذف")
        Catch ex As Exception
            MsgBox("Error: " & ex.Message, MsgBoxStyle.Critical)
        End Try
    End Sub

    Private Sub btnDelivery_Click(sender As Object, e As EventArgs) Handles btnDelivery.Click
        If DataGridView1.SelectedRows.Count = 0 Then
            MsgBox("فضلاً حدد عهدة لتسليمها", MsgBoxStyle.Exclamation)
            Exit Sub
        End If

        Dim frm As New frmCustodyDelivery
        frm.CustodyID = DataGridView1.SelectedRows(0).Cells(0).Value.ToString()
        frm.ShowDialog()

        Header() ' تحديث البيانات بعد التسليم
    End Sub

    Private Sub frmCustody_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If con.State = ConnectionState.Open Then con.Close()
    End Sub

    Private Sub MAXRECORD(ByVal Table As String, ByVal Felds As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " + Table + ""
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            CodeID = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" + Felds + " As int)) as mb FROM " + Table + " where " + Felds + " <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            CodeID = sh + 1
        End If

    End Sub

End Class