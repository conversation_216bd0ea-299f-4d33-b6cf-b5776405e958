Imports System.Text

''' <summary>
''' فئة آمنة لبناء الاستعلامات المعقدة مع حماية من SQL Injection
''' </summary>
Public Class SafeQueryBuilder
    Private _baseQuery As String
    Private _whereConditions As New List(Of String)
    Private _parameters As New Dictionary(Of String, Object)
    Private _parameterCounter As Integer = 0

    ''' <summary>
    ''' إنشاء منشئ استعلام جديد
    ''' </summary>
    Public Sub New(baseQuery As String)
        _baseQuery = baseQuery
    End Sub

    ''' <summary>
    ''' إضافة شرط WHERE آمن
    ''' </summary>
    Public Function AddWhereCondition(columnName As String, value As Object, Optional operator As String = "=") As SafeQueryBuilder
        If value IsNot Nothing AndAlso Not String.IsNullOrEmpty(value.ToString().Trim()) Then
            _parameterCounter += 1
            Dim paramName As String = $"@param{_parameterCounter}"

            _whereConditions.Add($"{columnName} {operator} {paramName}")
            _parameters.Add(paramName, value)
        End If

        Return Me
    End Function

    ''' <summary>
    ''' إضافة شرط WHERE للتواريخ
    ''' </summary>
    Public Function AddDateRangeCondition(columnName As String, fromDate As Date?, toDate As Date?) As SafeQueryBuilder
        If fromDate.HasValue AndAlso toDate.HasValue Then
            _parameterCounter += 1
            Dim paramFromName As String = $"@paramFrom{_parameterCounter}"
            Dim paramToName As String = $"@paramTo{_parameterCounter}"

            _whereConditions.Add($"{columnName} >= {paramFromName} AND {columnName} <= {paramToName}")
            _parameters.Add(paramFromName, fromDate.Value)
            _parameters.Add(paramToName, toDate.Value)
        End If

        Return Me
    End Function

    ''' <summary>
    ''' إضافة شرط WHERE للقيم المتعددة (IN)
    ''' </summary>
    Public Function AddInCondition(columnName As String, values As List(Of Object)) As SafeQueryBuilder
        If values IsNot Nothing AndAlso values.Count > 0 Then
            Dim paramNames As New List(Of String)

            For i As Integer = 0 To values.Count - 1
                _parameterCounter += 1
                Dim paramName As String = $"@param{_parameterCounter}"
                paramNames.Add(paramName)
                _parameters.Add(paramName, values(i))
            Next

            _whereConditions.Add($"{columnName} IN ({String.Join(", ", paramNames)})")
        End If

        Return Me
    End Function

    ''' <summary>
    ''' إضافة شرط LIKE آمن
    ''' </summary>
    Public Function AddLikeCondition(columnName As String, value As String) As SafeQueryBuilder
        If Not String.IsNullOrEmpty(value?.Trim()) Then
            _parameterCounter += 1
            Dim paramName As String = $"@param{_parameterCounter}"

            _whereConditions.Add($"{columnName} LIKE {paramName}")
            _parameters.Add(paramName, $"%{value.Trim()}%")
        End If

        Return Me
    End Function

    ''' <summary>
    ''' إضافة شرط مخصص آمن
    ''' </summary>
    Public Function AddCustomCondition(condition As String, parameters As Dictionary(Of String, Object)) As SafeQueryBuilder
        If Not String.IsNullOrEmpty(condition) Then
            _whereConditions.Add(condition)

            If parameters IsNot Nothing Then
                For Each param In parameters
                    _parameters.Add(param.Key, param.Value)
                Next
            End If
        End If

        Return Me
    End Function

    ''' <summary>
    ''' بناء الاستعلام النهائي
    ''' </summary>
    Public Function Build() As String
        Dim query As New StringBuilder(_baseQuery)

        If _whereConditions.Count > 0 Then
            ' التحقق من وجود WHERE في الاستعلام الأساسي
            If _baseQuery.ToUpper().Contains("WHERE") Then
                query.Append(" AND ")
            Else
                query.Append(" WHERE ")
            End If

            query.Append(String.Join(" AND ", _whereConditions))
        End If

        Return query.ToString()
    End Function

    ''' <summary>
    ''' الحصول على المعاملات
    ''' </summary>
    Public ReadOnly Property Parameters As Dictionary(Of String, Object)
        Get
            Return _parameters
        End Get
    End Property

    ''' <summary>
    ''' تنفيذ الاستعلام باستخدام SecureDatabaseManager
    ''' </summary>
    Public Function ExecuteQuery() As DataTable
        Try
            Dim query As String = Build()
            Return SecureDatabaseManager.Instance.ExecuteQuery(query, _parameters)
        Catch ex As Exception
            ErrorHandler.LogError("خطأ في تنفيذ الاستعلام المبني", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' تنفيذ أمر باستخدام SecureDatabaseManager
    ''' </summary>
    Public Function ExecuteNonQuery() As Integer
        Try
            Dim query As String = Build()
            Return SecureDatabaseManager.Instance.ExecuteNonQuery(query, _parameters)
        Catch ex As Exception
            ErrorHandler.LogError("خطأ في تنفيذ الأمر المبني", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' تنفيذ استعلام للحصول على قيمة واحدة
    ''' </summary>
    Public Function ExecuteScalar() As Object
        Try
            Dim query As String = Build()
            Return SecureDatabaseManager.Instance.ExecuteScalar(query, _parameters)
        Catch ex As Exception
            ErrorHandler.LogError("خطأ في تنفيذ ExecuteScalar المبني", ex)
            Throw
        End Try
    End Function
End Class
