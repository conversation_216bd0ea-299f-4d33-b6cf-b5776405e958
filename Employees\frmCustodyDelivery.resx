﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAAAAAAAEAIAAWLgAAFgAAAIlQTkcNChoKAAAADUlIRFIAAAEAAAABAAgGAAAAXHKoZgAAAAFv
        ck5UAc+id5oAAC3QSURBVHja7Z2He1TV1sbvX/NdL0TITDKTycwkBAiB0HtvoYP0DkF6U1RQQJoFBcQu
        RUEBQYo0RYr07qWD0hUr69vvTk44M3POmTOZCTfoe57nfQYmZ/ap67fXXnvtvf/1dHq6UBT1z9S/eBMo
        igCgKIoAoCiKAKAoigCgKIoAoCiKAKAoigCgKIoAoCiKAKAoigCgKIoAoCiKAKAoigCgKIoAoCiKAKAo
        6p8BgLTq6VKVoqgKUVplB0CtbI80zvVSFFUBCvo9KYNAygFQ3ZMuK7sH5MbEsFybEJbrEymKSoVgT1eV
        Rjf1SZXqlRgAa/sE5K+ZOXLp2bAcHx2SExRFlVvHS/XjpLD8MSNHJrao5ABYowCAE53c0idh5a7UCFAU
        law+6BmQP5VdTXhSADCisU+eqlYStKAoqpwqDf4t6/aEAWBkk9SdKEX907WcAKAoAoAAoCgCgACgKAKA
        AKAoAoAAoCgCgACgKAKAAKAoAoAAoCgCgACgKAKAAKAoAoAAoCgCgACgKAKAAKAoAoAAoCgCgAD4pyit
        enVHJft7pzLc/NbteRAABACVoOF7MzMllJtrq5waNSTD73csJxAK2f4+rH6fHQ5LNY8n5tjVvV4J5uQ4
        Ht+pDAKAAKDKqarVqkle7dqyYuVKOXzkiKWOHjsm73/wgdSpV8+2Bn5mwADZ+803csTi9/ju2337ZMiw
        YRHGq8Hj88msF16QQ99/b3t8XcbRo/Ldd9/J8JEjCQACgEqV8derX1++2LBBHj58KHYbDLtl69aWngOM
        ccSoUXL16lXb3//666/y6oIF4s/OLgOIYfyz58yRn3/5ReJtf/zxh7z+xhuSFQyyGUAAUKkw/mYtWsiu
        3bu18VsB4M8//5R169dLQWGh3t/KdX92wgT56aefYn5rlHf/559lziuv6OZDtPHPefll+Vn9Pd72+++/
        y5tLl0YAhCIAqCSMv3PXrvL94cOOtfY7qlmQk5dnafww4BkzZ8rt27dty7h37568OHu23tfK+H9xUfPD
        +JctX67jCzR+AoBKgfo+84ycO3cuoqY2b/fv35f5pS67lfFnqtr8lblz9X52211l/M/PmiWezMwY439Z
        eQTx3H6cF9z+le++qwN/NH4CgEoy0o/2+qgxY+SKQ3sd7vzUadMiam1zGYDC4iVL5MGDB7Zl3LlzR3sH
        6RkZEcaPZsDcefNcuf1ofiDwGMrJiYEQRQBQiXbzKYOerozy5q1btkZ34eJFGTZihAZFtPHDCNEVt3zF
        Cvntt99sy0CTYIoCSLrXG2v88+e7cvth/B99/LHu9qPxEwBUksaPWnv+q6861rzHT5yQnr17W7raMMLc
        mjW1UcI4nYx/8tSpOjgYbfzzYPwOXoPh9v/111/yyapVOu+Axk8AUEkG+1CLxqu1v/n2W2nVtq2lweG7
        /IIC+WzdOm2cdhs8i0mTJ9sa/4M4xo8N5a9es0bDhsZPAFBJGn9tZbhr1q61NVzUuJu+/FIKGzSwNf76
        jRrJV1u3OuYJ3Lx5U8ZPmBDRdDC7/Q9c1vxrP/1UatD4CQAqeeNv1KSJbNu+3dZw0b324Ucf6SxAO+Nv
        2ry57Nm719F4f/zxRyl+9llL45+nmh1uav6HyvjhYdSoVYvGTwBQyRp/m/btddqs3YZAHCL56Fu3M/62
        qowDBw44Gu6NGzdkTHFxjPGjmxDdiK6MXwEKmYi16tSh8RMAVLIBv+49e8qJkydtM/MQqHtu1qyIzLxo
        4+/SrZscP37c0XCvX7+uuxS18ZtzBLKyZMHCha7cfmybN2+W/Lp1afwEAJWM4eMTg20uXLhga2xXrlyR
        0WPHRgTqosvp1aePnD171tF4r127pvP/o3/rU8a/cPFinUXoZtu6bZvUtUgzpggAKgHjR8LN+IkTda1s
        t50+fVpnADqNqR88dKglQMzbZQWRocOHx3Y1BgKyKE6CkHnbsWOH1LMJPlIEAOXS+NHefmn2bLlz965t
        zX/g4EFp36mTpbEZGYKjlTuPmt1pu3TpkgwaMsQ6O/C111zX/Lt27ZIGjRrR+AkAKplgH3LkX3v9ddta
        FwCAm924aVNb44f3gP57qxF95u3ixYt6zL/ZezCMH+fglGdg3vbs2SMNmzSh8RMAVDLGjy4zdONhwIzd
        +Hkk1SAXwM74EQh84aWX5K6F92De/vvf/5Y1H8y/z1LGjzH6v7o0/m+++cYWRhQBQLk0/rr168vGTZts
        E3zgii996y09zZad8SNgh666eKPyflDG37tv31jjDwbljaVLXdf8mBGocbNmNH4CgABIxSQetsNwVW2O
        MfjojrOL9KPpgAk24rXZz50/HzM+wDB+AMat8X+3f79OKqLxEwAEQBLG36lLFz0/nlPfPFJyzcNwrcYG
        vPf++zoT0GlDV2CPXr0ijLY8xn/o0CFp0bo1jZ8AIACSUb/+/R3758+r2nrg4MEReQHRxl+zdm0dFzCa
        DnZpwmfOnJFuPXrEGD8yB996+23Xxg9YtW7XjsZPABAA5e3mQ9KOnsTjyhVHQ+tSVGTbv2/EDTZs3Og4
        qAfbqdOnpcjG+N9etsy18R87flzadexI4ycACICkJ/G4edOyiw/auWuXNG/VytbQ8D263bbv2BHXaE+c
        OKHTgKONHzEDzMsXr9lgLqdj5840fgKAAEhmEg+MprObdw8Tc6z//HPHVFp830LBAd1vbmrsaKOtWmr8
        mE/gd5vuxhgP4tQp6dS1K42fACAAyhvsM6besovSww23m7HXXE575YI7zfxrbFi4o12HDpY1/4p33nFd
        8585e1Y3RWj8BAABkMQkHpgYw27qLXgE8AysZuw1l9O1e3c5qWpjN1H61lGzAcH4g8r433n3XdtEo5he
        g3PnpEgdk8ZPABAA5TT+ho0b69Rdu0Ad0nWnTJ2q1/GzC/jhe6Tsnv/hh7hGi/H+LaO66LTx5+TIu++9
        59r4kS/QrWdPGj8BQACU1/hRCztN4uE0Y6+56w/7OPUYGNv+/ft1fCCiza/+jZo/EeNHmjCGENP4CQAC
        oJzGj9l3Dju01TE5R/devRxrfXQXYmouzNITb9unQNO8ZctY40+w5scAIeQncNEOAoAAKKfxY1gsUmXt
        EnOwMGdrmxl7DePH6jvTZ8yQWw5z/ptz8pu2aBFj/Ag8IkPQrfFjXoABgwbR+AkAAqDcY/mzsuTjTz6x
        nSIbM/bWdxg3b4zowwq7WIfPzWi8JlE5+eUxfqwEjMlDaPwEAAGQRO2PKbys+vlhiHrGXodZco28/IWL
        FrlabWf3nj0xQ3EN48cSXE4LfkSPN0CcgcZHABAASdT+6GPf8fXXMQaGyT0wY2+2zYy95mDdsjgLfhjb
        zp07dQ+DlfF/8OGHro0fU4BHzwVIEQAEQIKq8vTTerBN9EQc8AZeePFFy4U5zYaLBCAYrhuXffv27THN
        CGNUYLylvqK7IDGhKI2OACAAUuABYALNaLd/0eLFEctpWxk/ptBet369K8PFij7RE28axo/Yg9NSX9HG
        P3bcON0FSaMjAAiAJI3fFwjEDM7ZvGWLdsmdjB9LeMVbpsvYEEAsiBonUJ6aHwORxkWt/EMRAARAEgDA
        uHyMmDO2K1evOo6ew/dNmjWTr1Vb3s1KOxs3btQLekYbf05pze/W+G/dvi0TJ0+m8RMABECq5/TDBB6G
        wWJWXTv3Wi/11a6dY65AvGW2jLhBIsZ/584dmTJtmu0iIhQBQACUEwBw5THZpk6lvXAhpm/evC88g6NH
        j7oy/s+/+EJq5efHGH+uMv5PVq1ybfzIKZj53HO2U4tRBAABkEQToLaqobFSD7YVK1da1v7GHIDmpoLT
        6roIDNa0Mv6aNRMyfvREzHrxRcdgJEUAEABJAAB9+Pv27dPj/fsPHKi7BWPGB3ToIMdLjd+p5kckH8OH
        o5f51msHKONfvXq162j/zz//rGcUduqGpAgAAiDZ+6Ha1aiVsQ4f4gHRhotZfvaVjgyMZ/yY5LNGVMag
        sXCInlfApfEjm3DOyy/T+AkAAuBxJAIhuo6oPtJ5zQaH3P6V774bt6sPLj2CenDxrYz/088+c13zI/vw
        lXnzaPwEAAHwuAKBGI6LZbVgdGYwYBUeRODjGT/y99GnH238aAp8tm5dQsaPGYYAHho/AUAAPCZhSi/M
        4w/DM2ID+O7LzZsdDRYZgxizj6ShaONHEBDBQLfGjxjEgkWLaPwEAAHwvxDcd0Tbjdq/e8+ejrU/JufE
        JJ0YSFTVZLAwfvT9J2L8GES05PXXdVYijZ8AIAAqgbDYppPxY2GOQFTMQE8kqowfU4TD+N2kCaOsN996
        S3scRllpKIvPhQAgAP43XYNozx88dMjWYLEeX5aV8RcU6AQgtzU/mhBY5MMMEhh+vZBH+hVmSLqHz4MA
        IAAee1CwZZs2ery9lav+xptvSpaptjYbP1J/3dT6hvFjqm80IaKNf9uAbPlxQlgG1M+gJ0AAEACPu1sQ
        CUGIyEcbP8YJ+C2MH8OCN27a5Nr40XOAab8w8acRPDQbv8zIFZmZK6dGh6RlnpcQIAAIgMcJAIy3N0/w
        gX+/rmr+6CAdjLeOMv4vv/wyIePHBCLmnoMy4x9YavxKDzUEcvR3NQOMCRAABMBjA8DkqVMj2vEY0qtr
        awu3P15XoVXCUNhk/Gnq/tcPe2SHyfjN+mt6jrzTLUsyM0qCg3xGBAABUMEAwJz+xoCdH3/6STp37Rox
        RsAYQ7Bm7VrXNT+AgpTjnOiEIXX/B6q2/r3JOZYAgH6ZmiMzW/mkGp8PAUAAVDwABg0ZUrYY6AZV++vk
        HNM+GDGIgTpup+6G8QMW0anCZSnH3nRZ0skvv0/PtYXAjQlh6c+gIAFAAFR8LwBW58X0W/ACJkycGFH7
        Gyv9YuYgt8aPvICaphGCMOK0iK7HdAn702V934Bu+z+0gsDMXDkxKiQtajAoSAAQABWaB4ABPMeOHdOZ
        gO07dZIqplob3sCq1atdGT+aB/AgomcFaqDa/IHMSAjAqPH9d8OCtl4AVBIU9BACBAABUFGCi48cfwwR
        Ns/ki08su+1m2S8YPwKE6CI0B/x61fXKcVWTv9LeH5PoA6Puku+VC8UhWwA8nJ4jK7tliY9BQQKAAKi4
        OACW9d67d6+eyMPo+gMYlq9Y4ar237p1a8RMwDD+vvUy5IexIe3O356UIyMaZervIzwQpVGNM+XOpLBD
        UDDMoCABQABU6ExBOTkyb/58HbjD//X0YQUFrqYFwwIg9UyTi8CoEcC7MK7E+A1D/m9xWDrWjmzTY194
        BnOVh/DbtByHoGCIQUECgACoSAjUqVevLPMPXkGffv30VF1O2/79+/Uqw2bjH9QgUy6Ns3brvx0a1ElA
        0RDIykyXj3tmWQcES4OCJ5EpyKAgAUAAVBwEzNF/dP059ftj/EDP3r3Leg1wr4cpN//ys9bufIlx58ja
        3gEJ+jwxQcH8bI/sHJStjd0OBEgdZlCQACAAKliYOxApvE4b4gPYz2jz1wqky/cjgo4GDP0+LVcWdPCL
        xxPbM9AqzyOnRtv3DPw1oyQoyExBAoAAqEBl+HyyadMm+5V7bt0qyRg0dfchSDepeabcmxJ2BAB0d3JY
        xjXNjDFiQACjApEIZI4fmPUAmYKtGRQkAAiACmsKIBawc9cuWwDs3r1bAqFQRLMBxuz1pstrnbPkz+k5
        jgCAcV8sDknXOrGBPRj2jFY+Zej2v+fwYQKAAKhAAGACkD1799oCAJOEWP82XXL86bKhX7YzAEp1aHhQ
        GuXEBgXR77+8KEsPDrL7LYKCzBQkAAiAVAMABhgIyLZt22wBgBV8bBcUVfe4sTLqQ6XxgHgQ2NAvoFOD
        zTkCKCMvyyOb+2c7BhS3DggwKEgAEACpFiYMRU6/3fb8rFkxqwpFQ6BHQYZcHheKC4A/VC3/eme/bj5E
        BwWb5HrliA1IAIE/S4cPM1OQACAAUizM32e3YSUfOw/A7EmMb5bpOPTX0P0pOTKxeaYlSLoXeG27Fjl8
        mAAgACpolOCMmTNtJ/zEACGs4BsPAN6yob/xIXB1fEh61bUO7KHHAD0HTkFBZgoSAARACscH9OjVS+7d
        v28JgHPnzklhw4bxvQB1v0Oqff9530Cp257j2DNwdGRQmubGBgWRM7CoowKJQ7owhg83Z1CQACAAUtMT
        gLEAJ0+etJ3ld/KUKXEBYLjxDcMeOTg86KpnYEv/bKmRFRkUBASCvnRZ2yfLESJbBwYkL+CJGXREEQAE
        QIKCi//Rxx/bxgF27d6tJ/t0s7oPIIA+/0tRA4Ss5wXMlWVd/THZfsZkovuGBk09AZxTkAAgACqsGTBi
        1Cg9Rbjd+n6YUdiNF2DoWdWWv+8iKIjA3gyLwB4g0KF2hpwba5cpmFOSKcigIAFAACTfDMhXzYBjx4/b
        jwY8cEAvDOrGC9Bt+dKg4J/T4zcF7OYFxP8xtwDmGLD77XX122cKGRQkAAiApCEwd/5821GBmEfw5blz
        EyivJCi4vk/AOSBoCuy1sFgsBM91dluf5RwCD8vmFAwyKEgAEADJdgc2bd5cTxdmt12+ckU6du4cMTAo
        XjwA8wEeiDMfoNHG/3pQQGpnx/YM+DPT5f0eTunCyBTM1hmFhAABQAAkoUWLFzvODbB9x46y2YTcQqBr
        vlcujgvH9QIwBBiG7rcICtYKlK4wNDPXdqGRFUUMChIABEBSXgBm/Tl9+rTjKkBLXntN9xy4hQAMsrgp
        MgXjQwCBvVltfPp5RoMEA4Lg7mNpMauegQdTrAOKFAFAJRALmDZ9ul4u3G67e++eFI8blxAAkCm4oKPz
        fICGfpoYliENM2L6+AGBvoUZcm18mAuNEAAEQEUBAGv8wdV32i5fvqynCXPbNQhjRoLPZ30CrpKEzo0N
        Sbta1oG9qS198vMUh+HDykvg6sMEAB9eEk2BLkVFcvXaNUcIHD9+XK82VDWBoGBhyCP7h7kYPqz+vmdw
        UOoEY4OCWHJsaRe7oGDJdzooyOHDBABVPmGtgFkvvKBnC0a730oYQHTg4EFp1bZtQkFBLBJysTjsyhNY
        1SsQs9qQnowkK1029gvY9iog/2B5kV8yvQwKEgBUuWcLGjh4sIwpLrYVMgTbtGuXYNnpMqYJRv3Fjwf8
        Oi1HXi5dbSi6ZwAzDB0eEXRcaGR6q0wGBQkAqrwQQJpwPFV1Wfub3XgY9Ksd/PKHi6Dg7UlhGdnYemLR
        ojoZctFh3AEyBfsxU5AAoCrfdGTZGPXXOxB3ZmEYN1Yb6pQfa8gop8SbCNv+9sTooDSrwXgAAUBVrmBj
        aVDwu6HZtqP+zCm/+4YF9f5WcwjML11yzK4MBgUJAKqSQqBTvlfX8PGaAjDutX0CujsxzcKbWO3gTWDo
        8QrOKUgAUJWzOTC6SYbcnRQfApgpaGFHvx5tGB0UrBv0yJ4h9tOU/8JMQQKAqpwAQFBwXvvSqcBmOscD
        MM8AJiG18iaQPHRmjHNQEMOHOZMQAUBVwqDgJz2z5NbEsNycYC/8/ciIbGlXM3ZKMPwfacQ/jA1Z/FZ9
        p367a1BA6oc4nRgBQFU6CGDhkNZ5HmkD1TQpL0rqOyxSavcuIEfAvK/x79alwqpGjAUQAFQlhABceTdK
        ixNcLO9vKQKAoigCgKIoAoCiCAACgKIIAAKAoggAAoCiCAACgKIIAAKAoggAAoCiCAACgKIIAAKAoggA
        AoCiCAACgKIIAAKAoggAAoCiCAACgKIIAALgbyssLgJh/UDI+H9FH88sPgcCIGmlZ2SI1+dLWp7M2Mks
        sQ4fvrfaH3+L3jfT75fMrKyUyqOuL1X3CoaOFYOqe72SHQpJzdq1pbBhQ6nfqJHUrlNHwjVq6GNiX72y
        kMvFRe0MHmXgE/cwmJMjuXl5kluzpv7MDof198Y5lQcIqXr25ueJe5PqZwilp/A5EgClylAG9+qCBbJ5
        yxbZ9OWX5dbmzZtl2fLl+iU1XkS8mPXq15dVq1fLl1H7f/7559K2ffsyA8ESXHXVvp9+9pls275dtm3b
        lpS2lgpljR47Nuna0vg9znH8hAny0ccfyzfffiunTp2SCxcvykWlM2fPyuEjR2TLV1/Jm0uXyvCRI/X+
        MIxEQGB4FTDwHr16ybz582W9ul/79+/XqxafOHlSf+777jv5bN06mfPKK9K1Wze93qHhibg9TvG4cUk/
        e2jd+vXSvFUr+U9amv78YsMGfe+3mp6FWXbPy25ffPbu2zcpoBIAFi+APztb9uzdK6nYzpw5o2tEMwBa
        tWkjt2/fjtn3999/lwGDBumay9i3ecuW8tNPP0mqt4WLFycFAJxbKDdXryh8+vRpvXqwmw3XePbcOXl7
        2TJp36mTrsHinQeOFVCeBRYn3blrl9y7d8/Vse7cuaONZNiIEfqZujEU7LP0rbdSco9/++036dm7t/y7
        ShXp1qOH/PzLLyl/jhMnTy57XwiAFAIAL1oqtpOqNsyLAkBLBQAro37w66/Sf+DACAA0UwC4fv16yl+c
        BQsXlhsAOK/aBQW6poVBl3e7fuOG9rR8ypW1OxcDgqg9f1X3pzwbljxfvWaNNGzSJC4E8Pc3lKeSiu0X
        ZfDwVgwAuAWX2+3hw4cyYdIkAqAyA+CUFQBat04MAMpQKgsAjGXEP1m1Sr+AyW5o3qDJZXcsGA5c+1Rs
        Bw8elPYdOzpCIJUAePDgAQHwpAJglw0A4OrCtXOjP/74Q44ePZocAFq0kKtXr8pff/2ljx0tfG/3cljt
        b/wGNW95AIBzQjseL7fVMaH79+/LzZs35e7du/oe2G1oBsFFrmJhkDhO56Ii3Vxws+G63ADpmIIJmmB2
        EHACAO6b22cPoQnSrWdP+XfVqlLUvbu+3lQ+R9xbxF4IgMcIgB1ffy2Dhw51pSHDhukawFzDJQIAnAsC
        iEOHD9ft32iNGjNGPv/iC8vzRABuytSpOthn9dsW6hzKAwBE2Nd++qltjffOu+/qa27dtq0Owo0dN07e
        WblSgxCuuHnbsHGj+AOBmPPAPapbWCj7DxxwrP1wjWvWrpVZL74oz44fL8+/8IJ29S9euuQIg6937oyA
        slsAnFMwwvUMGjLE1fNHPAe9EygTxxsxalTJ/bd4Jnv27LE85okTJ2T8xIklz9FCDRo1SnnXJwHgAID3
        3n+/rIvJjaJrGicA/BoFAHO3l5WeUjXLi7NnW57nEWVwgAci0G7Oy+29ycnL01F9q23vN9/oKP1/Sss3
        joWIPwKhMAgYLLwDuMP9+ve3rL0QGHzTwQ3Hffrgww91bAD7mo+F/+N79EjYxQxQ274yd669B/Dmm7ZN
        CH19NvfUSoZxOj1HHBPvldW2fccO3Z1od8yKyHsgABwAgBcvuq8+URc6EQDEK2v2yy9bnufRY8e0saby
        BUFZderWlbNnz1oe85tSAFjBxTAAvMxwh6fPmKEj+1a1fyvlPaDZY+dlzH/1VfEpz8HJjcczXPLaa9oV
        t9rO//CDNLIICjoB4NChQzqnIdVGh/fp/Q8+sPY4FQB8Fl4SE4EIgP8JAGrUqqXb0XbGuWjJEu3umjMB
        rc7b7rzwPWpnOxceNbtTr4G5HAAGuQJ2XsD0mTMrPwBUk5MAqEQAwIPCA7NKQXWTlppIDKCyAcBIkkLb
        3W5DYAqu8gsvvaSTmoxa3o27avQw7Nq927Lsy5cvS4tWrVw3X3DMzl272uZRbNy0KaYHwrEJoACA3Adz
        inMiz54A+BsAAN1fQeXm4sXOjiO8zNHewpMOABxz3Pjxtq61UXOjhsU17t27VxYsWiQ9+/TR3oNRhl3Z
        SB++dOmSbZehVWq107OE8SAZyGo7feaMzmcw3yMnAHx/+LAUFBZKQD1XPH87ZZd+uk3TJQCeoDyAa9eu
        6XTXb+MIKamIliMKHP2CPRYAHD1aIQAweiaQ5prIdvfePTn0/fcyf8ECadq8eUQqsbnGRj/97Tt3LMHy
        0pw5CQcvcYzX33jD8pxu3LihU3TNZToBAN2aSD2O9+y/3bdP9zSY07oJgH9gIhACTfl16/6tAGDupkP7
        ujzZeefPn9cJLAgIms8P142uQ6uEGfR7T5g4MeE+b5wrxgVYbTdv3YoxUjsAJJr0hPvSS3k9bs6XAPib
        AgBJLPkWLuaTDgDj2PAE0D+NSPUdVTsmst27f1+PIzC7ybjuLkVFtgCYVI68d5zny3PnWgPg5k1p066d
        aw8g0TRgneREAPxzAYDEkb+jBxA9Qg/t3aIePWTxkiVy4MABnfHmpsbEPYCRlI1+VJ8wSNTMVhu6/8qT
        v4ARmVbbVdWca6KaIzEASEEqsAZAKjwAdgNWLgBg8Asy2uIJL8CxY8csg0xPOgCio9xGlB//RjcZIu8z
        n3tODxay6883B/bQFDDKyXfIM9iyZYseA5/I0F54KYjZWCZLHTliGaOxAgCAhh4ON88e+vHHH6V7z54E
        wN8NABjXjgeLwR3xhICWVTfTkx4EzFL3B7W+U8IP/oaIPa4V6bl2owbRTKpTr56e+wC/BwzsuhlvKc/A
        rVEZTQpkH2JsgtX28Sef6Ik63KYCnzp9Wj8fJDLFe/aIZaDL0M39JwCeIAC8px6UU1pnvHTbvwMApk6f
        rscgoE8+Xp83rgXnsdsm1x2ReIxLMDcDJk+ZYjuICKmx6E50M6wXsxHZHRfdmCNHj46516lMBXZ77wmA
        JwgAFZkJWNkBoF101aQ5fPhwWTR/5vPPlzVznDL87F5wKwAUNmigJxmxy+DDbEo18/MtjcyAM/6OcQd2
        o+yQ1IN9rFKRmQlIAFQsAFxOCFIZAQCDN9fOcO3Rlp49Z442ZCQ/GedmGDWuF6Pa7LpK0aVortH1cZ57
        zrbZ8Neff+p+dkyHhTa+kZmJT9TQCL7h73azFCFleWxxsa2HRgAQAASAzUAgjDK0ywBEwA+Gh+m+kLQD
        rXjnHZ1xZ9crgJl+ouMkOFZIGbbdUOey+QTu3NEjEDGSDj0QK997T7v8VtOtmbcPP/rIchgyAUAA/H0G
        A6UYADgnzK1X3mm5rJJq0N8/cPBg25q4QePG2sBTuSGIi9iA44QgBAABsJMAsJwL4JV583Q6dLIb3PvX
        laFhynOn+QAbN2umc/mTnX4MzQGkLxdENTcSmRCEACAA5P3KBgDlZj/OGACuvVPXrmUTe5Rnw+8wJZnV
        fABW14jJRJDPj7718mxXVNME4DJG8pV3UtCDBMA/BwB23UdoP6YiBnDLItstelrwZPLcj584UaFjAdBu
        R8IP3GUM8rkbJxUY0Xh4DkgOQvafm+nAzc8E+3fp1k3PB4A1B+JNQ45A5bnz5/V0ZO06dCgLFCYzLThG
        A1YUAPBeWW07d+4kAB73wiBIRpk8dapOIUVAyxD+j3ZwMgDAg0S32cJFiyLKhmBMTVu0cJ3uirIw/150
        OThPzJMHkFXki2OcJ0DTsUsXmTZjhrytjg1XG23tr5Rg8G+9/bYeyINrAzjKu5CFkVyE2ACi+Ch3w4YN
        uq8cMRvkCGCAEpoWxgIkSPRJdAESeGFWzx49INoYU3wf8T7hvVq2YkXMMSdNmVKWKUkAPMalwVIx0UN5
        yq9s55nIcl3mJbtg6JBR0ye7JJjVUmQo16uOBaNEdiI+cexkj/e/uKeV4TkSANQTvSBpGu8FAUBRFAFA
        URQBQFEUAUBRFAFAURQBQFEUAUBRFAFAURQBQFEUAUBRBAABUI789Or2ikldtdnfKZXV6jdpSVyzUV4V
        F8c2X6Pb/Y3y3aXy2peXlh57nWlx7rfd/o/z+uKVkei9JwAqMQAyvelSGPJIwzCUXvpZooJguqR7IvfP
        ykyXBqZ9oDpBj2Rm2L9UNbIiy8XvawY8uuyq1RM3/lxVXpuaXumcnyGNczziz7B/EVG+p/QaO+V79e9w
        PkZZVr8J+jxS1+LaY0bCKdVW15GXZf13nGftQMl+xnchX3rEfX50L0u+q5Nd8n4Y+wfU/a6n7q/X63B9
        av966vo6qvvRrpZX3dtHcLL6DcqMPk7ZaFJvSVl4zlbHwt9xnrj3bfWxPBHXRwA8QQDAA22d55ETo4Jy
        dkxQTo1+pDPq/xufyZKw/5Gh4Dz718+U88UhOV26/0kl/H5z/4D0K8ywfKkWd/LLxXGhsrJPjgrJ0ZFB
        ea97ljTJ9biuRWDIxU0z5bth2eqYJWWcUOVtGRDQhm0Fk/bqJV3TO0uf79lSfT8iKHPa+hQIPDFGgjKK
        m/pkz+CA1Ao413AZ6nxW98qSpZ39lkawsINP1qi/Z3gf3T+c/ynTvb6g7svZsSF9H8+MCcln6r2AgaaV
        7j+wvlcOqOsFwKpaeAet8rzycc+AKkuVo35/TpV1RF3fqx386vxjrw9lDm6QoZ9Z//oZEWXi3w1zvPr+
        PFPojXgvUU4j9bdPegX0b3Hvj6vPw+pztrqXqAAIgCcMADhux9oeuTEhJLPa+KWLonpRnUwpKsjQalMr
        Qxudef8RjX3y08SwjG+WKV3qZEhXpQHqRfpUXQ+MvG9hRsyL+mGPLP3C9KqL8kt+M1YZwsHhQfl2aLb2
        IOI1CVDmM6rsy8+GZGFHv7So4ZH6yii6qbLwfxh61agXtne9DGUQQdkxKFuGN8pUxuKRDmq/2e388oMy
        lC/6BaRGlJHgGme08mtQ5Gc7N1Xw0m8fmC2remZZAmBlkV92KDgZAIDB5qtrxb3FPRjcMFMujQvL212z
        9L3E961rZpR5HjiXUY1LrrlRjifm+lAGDH/vkGy9X2sFA9yHWW18GiZbB8TeW5Q5pnGmPJyRq3/bMu/R
        fcNnsxpeua7eh2GNHgEgrdTz2/hMtoJvUPqo+1o/lC7NFbzxHrzUhgB4ogFwbXxIeqiX799Pl3xXxdTG
        i95/hHp5rj4b1gb4VLWS7/5TDW5zuuxWL+I6dV0eTywAdg0KaPfRKBu/wct6RR0bL1G8e4CX+DXlSaB2
        yvaV/N5oixruuHnf/GyPHFKAWd83IDn+R9djfPYo8MolZVjzO/hjrnG6AsAZlwCAkX1iA4B3FAC2KwBk
        eiNrbeP64T6fUUY4uUVm2b2vGmWsI5VhXxoXCQCjGQTD36YABE8l+vo61fbKeQW5pQouZq9MA6BJptyd
        HNZgARzhKRgxCADgqnomwxo+AkBVXft75L/K88Nv/+/pyLhFdQ+bAE80APDAQXV8h9rHUDWL/YcrD+Dq
        +LCuTSNqJKUPlCuKl9IX1SYHAHYrl9qXEbk/mhfHlBu5QBlhFRcewHOtfQpWYVWbZ2g32TgnKzd3lHpR
        r6gXHG1iu7Jf7+JXTYhQhKucKAC2JQiAaEgBAFNtXlY7AJQ0DTJUTR2WHnWtrw/7zmvv102fgmDkb8eq
        ewPjn6DAi2bDB+r5II5SxQYAONfa2SXPCp4emiMej7sAMAHwBAAALj1qbzzcz/qWaE3vgA7yRNdIGgDR
        HoBSjqqRvh0alLXqd+l2HoAn0gPonF/yso1r6sIDUIKhrlbl4+Xdo873ra5+GaLc6LxAZBwB57xAtb/R
        7MjNso4x4HhoulxRQDFfpxkAdSrAA7AEQMvEAFBi3FlyUhlkzax02+t7RkMipJ9xlSgAXFX3EPGXYer+
        AaovtS3xhJpaAMDwsEYq7w9NC8QuEKt4XgEZsRc0E9MIgCcXAD9ODCnDypK57X0yv1SvtPPpCLsVAACM
        Kc0ztBvdW9VAo9UL9bmCBlxOfBfddHhfAQABo4GFJfv3V215vDxHlIHuGpytI+VuugWxD9qiPetmaK8B
        xge39Luh2ao97C0rA8d/VQEANVY8AMCbaVcro1wAyFAA+Eqdw6peNjGAbn7lITyKAaQUAOr60YbPcwKA
        us/XlTF3qGUFgLA0zS2ZHxLPGhAY1CBTQcEaAIYaqvOY0NwnHylvD8FGxDCWdMrSzyWNAHhyYwAwqqeq
        OfftawA0ypRfpuZo1/HYyJAcVUYGIMD4UYaVISDaj98gYn98VEgLRvtGlyypH/a4zglIM7Wh8RvUwG1V
        DbRvWFAH44zuQPx9dKmb69gE6Jylz8muCRAPAHCD1ynwfdk/Nu6BZ7xWPeMN/WL/liwAypoAymgRu7Fr
        Asxt79c1tlUToAQAJfcGzalVvQJyenSJN4b7ZgUAc/4Frg/weaGNT26opghgUxkS2giAJADw72qxQcC0
        9NheALx4fep5JT/oVW1DRIJ9cnZsULoXZFh2xaGNCYNH7YGoNIT2f7X0xPIAEGjM9EYGvP79dHVVA/m1
        u290WRrGhYAhDDRsEQTsps71gqq97IKApzUYHgUbjd9GNzXmtPNrLwTekrEvPuuqa0R8Ad1xVhApaVfH
        B8AomyBgjvJs9g4Jylf9AzFBQKhDba/uEkQzKT0qCGgGgHFteCYo74K6lluTwjEAgJuPHAZzBYEKo27I
        Iz8UuwvkEgCVuBtwZiufqk092l1sX6vkE/83u3ZmALSs8eiFh4u7UtXyiAEUWHTpIQaAfnXsZ2S7Jeou
        AhZomiA2gdoGwSq8wOjTRvNieVFWxIuOY6BLEp4J3PAhDTN0lxd6Hl5Utda5sWHdDZhn0Q0IAMDo0AeP
        /TtoqXui7hUCg5H95h7dlEE0HoHU5uq8ANNNz2RrAOD/VavbewDwpKbFAYBdN2CRavYgDoBm1AjlmeH6
        4PHMUM0r5BrgugssugGLm5S0+w0AGNeC3+K+/DE9R4ZG9QI0U82Fzf2zZaYqGzETNBXwuUzdd3SZtrbJ
        wyAAKnkiUJuantIEkrCcHB0qE9qX+4dl62Cf2X0coNqJqG3xQphfHtR4Owdlq/akP6Zb6E3l6sPYMr3J
        nW9JUk9AH/+YVrb+XF5UmvRi85tP+2Tp60GyDWr2wyPUebb36YxAqx6Ecao2Q3Dw9JhQ5D1RL/pk1f6N
        7v1AbbtF1cRnxjxK6NmqjK+LMlCneAa6AZEHUWwTBDWSdr4fEdJNJbtEIMQgTpYmZeE6cX+QGwEPw+r6
        hikYAlrRUMG/kZuA6+5fPzIPAN4X4jaA/DFTItBuBR/EUipLVyABkKDQNdc4x6tr1Gg1VpSP7rrL9nl0
        tphV4gdeuPphb0wvQC31PVzFaikAFtrTqNUALngoAA+OZ9dWN9JX0XWF38BzyQs4pwKH/R4dDbe6Jwgq
        Wh0D8YcmyqDaqWMguAbPKV6NCLca9yvkt4aX+X47pgJ7S1xxfX15nrL03DSbsRwwZpRpFZzEu1mozgm5
        FmkW4xrQDMD14TpRCaCstOrsBvzHDQayq9Xs3N1UuofxzjHeb9wOlklk0FNagsdwO/AmkcFAVR/DYKDy
        XicBwOHAFEUAEAAURQAQABRFABAAFEUAEAAURQBQFEUAUBRFAFAURQBQFEUAUBRFAFAURQBQFEUAUBRF
        AFAURQBQFAFAAFAUAUAAUBQBQABQFAFAAFAUAUAAUBQBQABQFAFAAFAUAUAAUBQBQABQFAFAAFAUAUAA
        UBQBQABQFAFAAFAUAVAhABjR2CdPVXNeoJOiKHda9qQBYForv17OOT9IUVSy+qjXEwKAtX0D8nBmjlyb
        EJazxSE5R1FU0ro1Oawr1omVHQBoq1weH5aLz4blEkVRKRHs6YLSqCaVGABQjSyPFIa9Ui9EUVSqFfB5
        UmarFQKAtHQGbCiqopSWQlv9F7tXKOqfKwKAoggAiqIIAIqiCACKoggAiqIIAIqiCACKoggAiqIIAIqi
        CACKoggAiqIIAIqiCACKoggAiqIIAIqiCACKoggAiqIIAIqiKoP+H7ydcLRWSyaxAAAAAElFTkSuQmCC
</value>
  </data>
</root>