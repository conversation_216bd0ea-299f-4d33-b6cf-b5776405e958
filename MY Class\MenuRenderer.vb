﻿Imports System.Drawing
Imports System.Drawing.Drawing2D

Public Class MenuRenderer
    Inherits ToolStripRenderer

    Protected Overrides Sub OnRenderToolStripBackground(ByVal e As System.Windows.Forms.ToolStripRenderEventArgs)

        'التدرج المرجاني الدافئ:
        'Dim br As New LinearGradientBrush(e.AffectedBounds, Color.FromArgb(255, 255, 94, 98), Color.FromArgb(255, 143, 40, 93), LinearGradientMode.Vertical)

        'درجات البنفسجي والأزرق:
        'Dim br As New LinearGradientBrush(e.AffectedBounds, Color.FromArgb(255, 102, 51, 153), Color.FromArgb(255, 0, 51, 102), LinearGradientMode.Vertical)

        'درجات الرمادي والأزرق الداكن:
        'Dim br As New LinearGradientBrush(e.AffectedBounds, Color.FromArgb(255, 128, 138, 145), Color.FromArgb(255, 33, 47, 61), LinearGradientMode.Vertical)

        'درجات الأخضر والأزرق:
        Dim br As New LinearGradientBrush(e.AffectedBounds, Color.FromArgb(255, 0, 153, 102), Color.FromArgb(255, 0, 51, 102), LinearGradientMode.Vertical)

        ' تدرج لون أفتح للخلفية الرئيسية
        'Dim br As New LinearGradientBrush(e.AffectedBounds, Color.FromArgb(255, 51, 178, 140), Color.FromArgb(255, 25, 89, 148), LinearGradientMode.Vertical)

        e.Graphics.FillRectangle(br, e.AffectedBounds)
        br.Dispose()


        MyBase.OnRenderToolStripBackground(e)
    End Sub

    Protected Overrides Sub OnRenderItemText(ByVal e As System.Windows.Forms.ToolStripItemTextRenderEventArgs)
        If e.Item.Selected Then
            e.TextColor = Color.Lime
            e.TextFont = New Font(e.TextFont, FontStyle.Bold)
        Else
            e.TextColor = Color.White
            e.TextFont = New Font(e.TextFont, FontStyle.Bold)
        End If
        MyBase.OnRenderItemText(e)
    End Sub

    Protected Overrides Sub OnRenderMenuItemBackground(ByVal e As System.Windows.Forms.ToolStripItemRenderEventArgs)
        If e.Item.Selected Then
            Dim br As New LinearGradientBrush(e.Item.ContentRectangle, Color.FromArgb(255, 128, 128, 128), Color.FromArgb(255, 192, 192, 192), LinearGradientMode.Vertical)
            'Dim br As New LinearGradientBrush(e.Item.ContentRectangle, Color.FromArgb(255, 0, 51, 102), Color.FromArgb(255, 0, 0, 51), LinearGradientMode.Vertical)
            e.Graphics.DrawRectangle(Pens.Transparent, e.Item.ContentRectangle)
            e.Graphics.FillRectangle(br, e.Item.ContentRectangle)
            br.Dispose()
        End If
        MyBase.OnRenderMenuItemBackground(e)
    End Sub

    Protected Overrides Sub OnRenderArrow(ByVal e As System.Windows.Forms.ToolStripArrowRenderEventArgs)
        If e.Item.Selected Then
            e.ArrowColor = Color.Red
        Else
            e.ArrowColor = Color.White
        End If
        MyBase.OnRenderArrow(e)
    End Sub

    Protected Overrides Sub OnRenderSeparator(ByVal e As System.Windows.Forms.ToolStripSeparatorRenderEventArgs)
        ControlPaint.DrawBorder(e.Graphics, e.Item.ContentRectangle, Color.Red, ButtonBorderStyle.Dashed)
    End Sub

End Class
