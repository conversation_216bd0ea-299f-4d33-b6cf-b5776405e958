﻿Imports CrystalDecisions.CrystalReports.Engine

Public Class FrmSales_Cash_Out
    Dim WithEvents BS As New BindingSource
    Dim NameArCompay As String
    Dim NameEnCompany As String
    Dim sign_Indicator As Integer = 0
    Dim Key1 As Integer
    Dim aray_1 As New ArrayList
    Dim aray_2 As New ArrayList
    Dim aray_3 As New ArrayList
    Dim aray_4 As New ArrayList
    Dim totalCash As Double
    Dim totalVisa As Double
    Dim NumberSales As String
    Dim totalprisesal As String
    Dim TotalDiscount As Double
    Dim TotalReturnSales As Double
    Dim TotalExpenses As Double
    Dim NetTotal As Double
    Dim TotalShiftAmount As String
    Dim LanguageSalesInvoice As String = mykey.GetValue("LanguageSalesInvoice", "Arabic")
    Dim SummaryFullReport As String = mykey.GetValue("SummaryFullReport", "Full")


    Private Sub btn9_Click(sender As Object, e As EventArgs)
        If sign_Indicator = 0 Then
            txtPassword.Text = txtPassword.Text & CStr(9)
        ElseIf sign_Indicator = 1 Then
            txtPassword.Text = 9
            sign_Indicator = 0
        End If
        Key1 = 1
    End Sub

    Private Sub btn8_Click(sender As Object, e As EventArgs)
        If sign_Indicator = 0 Then
            txtPassword.Text = txtPassword.Text & CStr(8)
        ElseIf sign_Indicator = 1 Then
            txtPassword.Text = 8
            sign_Indicator = 0
        End If
        Key1 = 1
    End Sub

    Private Sub btn7_Click(sender As Object, e As EventArgs)
        If sign_Indicator = 0 Then
            txtPassword.Text = txtPassword.Text & CStr(7)
        ElseIf sign_Indicator = 1 Then
            txtPassword.Text = 7
            sign_Indicator = 0
        End If
        Key1 = 1
    End Sub

    Private Sub btn6_Click(sender As Object, e As EventArgs)
        If sign_Indicator = 0 Then
            txtPassword.Text = txtPassword.Text & CStr(6)
        ElseIf sign_Indicator = 1 Then
            txtPassword.Text = 6
            sign_Indicator = 0
        End If
        Key1 = 1
    End Sub

    Private Sub btn5_Click(sender As Object, e As EventArgs)
        If sign_Indicator = 0 Then
            txtPassword.Text = txtPassword.Text & CStr(5)
        ElseIf sign_Indicator = 1 Then
            txtPassword.Text = 5
            sign_Indicator = 0
        End If
        Key1 = 1
    End Sub

    Private Sub btn4_Click(sender As Object, e As EventArgs)
        If sign_Indicator = 0 Then
            txtPassword.Text = txtPassword.Text & CStr(4)
        ElseIf sign_Indicator = 1 Then
            txtPassword.Text = 4
            sign_Indicator = 0
        End If
        Key1 = 1
    End Sub

    Private Sub btn3_Click(sender As Object, e As EventArgs)
        If sign_Indicator = 0 Then
            txtPassword.Text = txtPassword.Text & CStr(3)
        ElseIf sign_Indicator = 1 Then
            txtPassword.Text = 3
            sign_Indicator = 0
        End If
        Key1 = 1
    End Sub

    Private Sub btn2_Click(sender As Object, e As EventArgs)
        If sign_Indicator = 0 Then
            txtPassword.Text = txtPassword.Text & CStr(2)
        ElseIf sign_Indicator = 1 Then
            txtPassword.Text = 2
            sign_Indicator = 0
        End If
        Key1 = 1
    End Sub

    Private Sub btn1_Click(sender As Object, e As EventArgs)
        If sign_Indicator = 0 Then
            txtPassword.Text = txtPassword.Text & CStr(1)
        ElseIf sign_Indicator = 1 Then
            txtPassword.Text = 1
            sign_Indicator = 0
        End If
        Key1 = 1
    End Sub

    Private Sub btn0_Click(sender As Object, e As EventArgs)
        If sign_Indicator = 0 Then
            txtPassword.Text = txtPassword.Text & CStr(0)
        ElseIf sign_Indicator = 1 Then
            txtPassword.Text = 0
            sign_Indicator = 0
        End If
        Key1 = 1
    End Sub

    Private Sub btnDecimal_Click(sender As Object, e As EventArgs)
        If sign_Indicator = 0 Then
            txtPassword.Text = txtPassword.Text & CStr(".")
        ElseIf sign_Indicator = 1 Then
            txtPassword.Text = "."
            sign_Indicator = 0
        End If
        Key1 = 1
    End Sub

    Private Sub btnDiscount_Click(sender As Object, e As EventArgs)
        If sign_Indicator = 0 Then
            txtPassword.Text = txtPassword.Text & CStr("/")
        ElseIf sign_Indicator = 1 Then
            txtPassword.Text = "/"
            sign_Indicator = 0
        End If
        Key1 = 1
    End Sub

    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOK.Click
        Dim ActivationEmail As String = mykey.GetValue("ActivationEmail", "NO")
        If Cash_Out_IN = "أنهاء شيفت" Then
            If Cash_Out_IN = "أنهاء شيفت" Then
                If Val(txtShiftAmount.Text.Trim) = 0 Or txtShiftAmount.Text = "" Then MsgBox("فضلا أدخل مبلغ الوردية", MsgBoxStyle.Exclamation) : txtShiftAmount.Focus() : Exit Sub
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select UserName from Users where UserPassword=N'" & txtPassword.Text & "' and UserName=N'" & UserName & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
                MsgBox("أسم المستخدم غير مسجل", MsgBoxStyle.Information)
                Exit Sub
            Else
                Dim x As String = MsgBox("هل تريد بالفعل أنهاء اليومية", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
                If x = vbCancel Then Exit Sub
            End If


            Dim xx As String = MsgBox("هل تريد طباعة المبيعات اليومية", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
            If xx = 1 Then
                SumTotalReport()
                Try
                    FillDataAndPrint()
                Catch ex As Exception
                    FillDataAndPrint()
                End Try
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set CloseSheft = 1 where UserName=N'" & UserName & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Sales_Bill set CloseSheft = 1,StatusOrder=0 where UserName=N'" & UserName & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal_Data set CloseSheft = 1 where UserName=N'" & UserName & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal set CloseSheft = 1 where UserName=N'" & UserName & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Expenses set CloseSheft = 1 where UserName=N'" & UserName & "'" : cmd.ExecuteNonQuery()

            ' إغلاق الشيف
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Users set Sheft_Stat = 1 where UserName=N'" & UserName & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Sheft_Status set Sheft_Stat = 1,Notes = N'" & txtNotes.Text & "' where UserName=N'" & UserName & "' and Sheft_Number=N'" & Sheft_Number & "'" : cmd.ExecuteNonQuery()

            Dim AmountDifference As Double = 0
            Dim Capital_Type_Code As Integer = 0
            If Val(txtDifference.Text) <= 0 Then
                AmountDifference = Math.Abs(Val(txtDifference.Text))
                Capital_Type_Code = 1
            Else
                AmountDifference = txtDifference.Text
                Capital_Type_Code = 2
            End If

            Dim Treasury_Code_TO As String = Cls.Get_Code_Value_Branch_More("Treasury", "Treasury_Code", "Treasury_Name=N'" & cmbTreasuryTo.Text.Trim & "'")
            Dim Treasury_Number As String = MaxRecordTables("Treasury_Deficit_Increase", "Number")
            Cls.insert("Treasury_Deficit_Increase", "Number,Sheft_ID,Capital_Type_Code,Amount,Deficit_Increase_Time,Deficit_Increase_Date,Notes,UserName,Company_Branch_ID,Treasury_Code", "N'" & Treasury_Number & "',N'" & Sheft_Number & "',N'" & Capital_Type_Code & "',N'" & AmountDifference & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & txtNotes.Text & "',N'" & UserName & "',N'" & Company_Branch_ID & "',N'" & Treasury_Code_TO & "'")

            GetTreasuryTransferFromTo()

            Get_Movement_In_Out_Money(DateTimePicker1.Text, Treasury_Code)

            Cash_Out_IN_Button = "Open"

            MAXRECORD_SheftNumber(True)

            If ActivationEmail = "YES" Then
                SendEmail_Sheft_Close("اغلاق الشيفت", NameArCompay, UserName, Sheft_Number, DateTimePicker1.Text, TimeAMBMTotal, totalprisesal)
            End If

            Cash_Out_IN = "فتح شيفت"
        End If

        '========================================================================================================================

        If Cash_Out_IN = "فتح شيفت" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select UserName from Users where UserPassword=N'" & txtPassword.Text & "' and UserName=N'" & UserName & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
                MsgBox("أسم المستخدم غير مسجل", MsgBoxStyle.Information)
                Exit Sub
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Users set Sheft_Stat = 0 where UserName=N'" & UserName & "'" : cmd.ExecuteNonQuery()

                MAXRECORD_SheftNumber(True)
                Cls.insert("Sheft_Status", "Sheft_Number,Sheft_Date,UserName,Sheft_Stat", "N'" & Sheft_Number & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & UserName & "',0")

                Cash_Out_IN_Button = "Close"
                txtPassword.Text = ""
            End If

            If ActivationEmail = "YES" Then
                SendEmail_Sheft_Open("فتح الشيفت", NameArCompay, UserName, Sheft_Number, DateTimePicker1.Text, TimeAMBMTotal)
            End If

            Cash_Out_IN = "أنهاء شيفت"
        End If



        totalCash = 0
        totalVisa = 0
        NetTotal = 0
        TotalReturnSales = 0
        TotalExpenses = 0
        NumberSales = 0
        totalprisesal = 0
        txtShiftAmount.Text = ""
        DataGridViewDelete.DataSource = ""
        DataGridView1.DataSource = ""
        Me.Close()
    End Sub

    Private Sub btnClearTextbox_Click(sender As Object, e As EventArgs)
        txtPassword.Text = ""
    End Sub

    Private Sub FillDataAndPrint()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "Delete from PrintSalesPurchases"
        cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            S = "insert into PrintSalesPurchases (itm_name,price,qu,totalprice)  values("
            S = S & "N'" & DataGridView1.Rows(i).Cells(0).Value & "',N'" & DataGridView1.Rows(i).Cells(1).Value & "',N'" & DataGridView1.Rows(i).Cells(2).Value & "',"
            S = S & "N'" & DataGridView1.Rows(i).Cells(3).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()


        
        If SettingPrinterAuto = "YES" Then
            Dim DefaultPrinterBill As String = mykey.GetValue("DefaultPrinterBill", "DefaultPrinterBill")
            If Not SetDefaultSystemPrinter3(DefaultPrinterBill) Then
                MessageBox.Show("Error occured will trying to set the default printer!")
            End If
        End If

        Dim txtNameAr, txtNameEn, txtTitel, txtNumberSales, txttotalprisesal, txtUserName, txttotalCash, txttotalVisa, txtSheft_Number, txtTotalReturnSales, txtTotalExpenses, txtNetTotal, txtTotalShiftAmount, txtObjectShiftAmount As TextObject
        Dim rpt
        If rdoFullReport.Checked = True Then
            rpt = New Rpt_DaySales_Small_Sheft_AR
        Else
            rpt = New Rpt_DaySales_Small_Summary_AR
        End If
        txtTitel = rpt.Section1.ReportObjects("txtTitel")
        txtTitel.Text = "تقرير الشيفت"

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "Select * from PrintSalesPurchases"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
        txtNameEn.Text = NameEnCompany
        txtNumberSales = rpt.Section1.ReportObjects("NumberSales")
        txtNumberSales.Text = NumberSales
        txttotalprisesal = rpt.Section1.ReportObjects("totalprisesal")
        txttotalprisesal.Text = totalprisesal
        txtUserName = rpt.Section1.ReportObjects("UserName")
        txtUserName.Text = UserName
        txtSheft_Number = rpt.Section1.ReportObjects("txtSheft_Number")
        txtSheft_Number.Text = Sheft_Number
        txttotalCash = rpt.Section1.ReportObjects("totalCash")
        txttotalCash.Text = totalCash
        txttotalVisa = rpt.Section1.ReportObjects("totalVisa")
        txttotalVisa.Text = totalVisa
        txtTotalReturnSales = rpt.Section1.ReportObjects("txtTotalReturnSales")
        txtTotalReturnSales.Text = TotalReturnSales
        txtTotalExpenses = rpt.Section1.ReportObjects("txtTotalExpenses")
        txtTotalExpenses.Text = TotalExpenses
        txtNetTotal = rpt.Section1.ReportObjects("txtNetTotal")
        txtNetTotal.Text = NetTotal
        txtTotalShiftAmount = rpt.Section1.ReportObjects("txtTotalShiftAmount")
        txtTotalShiftAmount.Text = TotalShiftAmount
        txtObjectShiftAmount = rpt.Section1.ReportObjects("txtShiftAmount")
        txtObjectShiftAmount.Text = txtShiftAmount.Text

        If chkPrintDirect.Checked = False Then
            Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
            Frm_PrintReports.Show()
            Frm_PrintReports.Text = "تقرير بمبيعات الشيفت"
        Else
            rpt.PrintToPrinter(1, False, 0, 0)
        End If
    End Sub

    Private Sub AddReportView()
        connectionStringOpen()
        ds.EnforceConstraints = False
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        Dim str As String = "SELECT * FROM Company"
        SqlDataAdapter1 = New SqlClient.SqlDataAdapter(str, Cn)
        ds.Clear()
        SqlDataAdapter1.Fill(ds, "Company")
        BS.DataSource = ds
        BS.DataMember = "Company"
        ds.EnforceConstraints = True
        SqlDataAdapter1.Dispose()
        With Me
            NameArCompay = ds.Tables("COMPANY").Rows(Me.BS.Position)("CMPName").ToString
            NameEnCompany = ds.Tables("COMPANY").Rows(Me.BS.Position)("CMPNameEnglish").ToString
        End With
    End Sub

    Private Declare Function WriteProfileString Lib "kernel32" Alias "WriteProfileStringA" _
  (ByVal lpszSection As String, ByVal lpszKeyName As String,
  ByVal lpszString As String) As Long
    Private Declare Function SendMessage Lib "user32" Alias "SendMessageA" _
         (ByVal hwnd As Long, ByVal wMsg As Long,
         ByVal wParam As Long, ByVal lparam As String) As Long
    Private Const HWND_BROADCAST As Long = &HFFFF&
    Private Const WM_WININICHANGE As Long = &H1A

    Private Function SetDefaultSystemPrinter3(ByVal strPrinterName As String) As Boolean
        'this method does not valid if the change is correct and does not revert to previous printer if wrong
        Dim DeviceLine As String

        'rebuild a valid device line string 
        DeviceLine = strPrinterName & ",,"

        'Store the new printer information in the 
        '[WINDOWS] section of the WIN.INI file for 
        'the DEVICE= item 
        Call WriteProfileString("windows", "Device", DeviceLine)

        'Cause all applications to reload the INI file 
        Call SendMessage(HWND_BROADCAST, WM_WININICHANGE, 0, "windows")

        Return True
    End Function

    Private Sub FrmSales_Cash_Out_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Cls.fill_combo_Branch("Treasury", "Treasury_Name", cmbTreasuryFrom)
        Cls.fill_combo_Branch("Treasury", "Treasury_Name", cmbTreasuryTo)
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployees)

        GetTreasury_Transfer()

        'cmbTreasuryFrom.Text = mykey.GetValue("TreasuryTransferFrom", "")
        'cmbTreasuryTo.Text = mykey.GetValue("TreasuryTransferTo", "")
        If cmbTreasuryFrom.Text = "" Then
            cmbTreasuryFrom.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        End If

        If TreasuryTransferFrom = "" And TreasuryTransferTo = "" Then
            PanelTreasuryTransfer.Top = 300
            PanelTreasuryTransfer.Left = 700
            PanelTreasuryTransfer.Size = New System.Drawing.Size(504, 175)
            PanelTreasuryTransfer.Dock = DockStyle.Fill
        End If
        lblTitel.Text = Cash_Out_IN
        Me.Text = Cash_Out_IN
        Language_Report()
        If Cash_Out_IN = "فتح شيفت" Then
            Panel10.Visible = False
            rdoSummaryReport.Visible = False
            rdoFullReport.Visible = False
            btnOK.Text = "فتح شيفت"
            btnPrintCash_Out.Visible = False
            chkPrintDirect.Visible = False
            lblShiftAmount.Visible = False
            txtShiftAmount.Visible = False
            'txtDifference.Visible = False
            lblNotes.Visible = False
            txtNotes.Visible = False
            cmbEmployees.Visible = False
            Label19.Visible = False
        Else
            Panel10.Visible = True
            rdoSummaryReport.Visible = True
            rdoFullReport.Visible = True
            btnOK.Text = "إغلاق شيفت"
            btnPrintCash_Out.Visible = True
            chkPrintDirect.Visible = True
            lblShiftAmount.Visible = True
            txtShiftAmount.Visible = True
            'txtDifference.Visible = True
            lblNotes.Visible = True
            txtNotes.Visible = True
            cmbEmployees.Visible = True
            Label19.Visible = True
        End If
        txtPassword.Focus()
        SumTotalReport()
        txtCurrentBalance.Text = NetTotal
        PanelTreasuryTransfer.Top = 5000
    End Sub

    Private Sub txtPassword_KeyUp(sender As Object, e As KeyEventArgs) Handles txtPassword.KeyUp
        If e.KeyCode = 13 Then
            btnOK.PerformClick()
        End If
    End Sub

    Private Sub SumTotalReport()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text : cmd.CommandText = "Delete from PrintAllItems" : cmd.ExecuteNonQuery()

        aray_1.Clear() : aray_2.Clear() : aray_3.Clear() : aray_4.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select itm_name,price,qu,totalprice from BillsalData where CloseSheft=0 and UserName= N'" & UserName & "'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr("itm_name")) : aray_2.Add(dr("price")) : aray_3.Add(dr("qu")) : aray_4.Add(dr("totalprice"))
        Loop

        Dim SMSalqu As Double : Dim itm_name As String = "" : Dim SMSalPrice As String = "" : Dim SMSalTotal As Double
        For i As Integer = 0 To aray_1.Count - 1
            itm_name = aray_1(i).ToString : SMSalPrice = aray_2(i).ToString
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sname,qunt from PrintAllItems where sname=N'" & itm_name & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                SMSalqu = Val(dr("qunt").ToString) + Val(aray_3(i).ToString)
                SMSalTotal = Val(SMSalqu) * Val(SMSalPrice)
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update PrintAllItems set qunt =N'" & SMSalqu & "',tinpricetotal =N'" & SMSalTotal & "'  where sname =N'" & itm_name & "'" : cmd.ExecuteNonQuery()
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into PrintAllItems(sname,SalPrice,qunt,tinpricetotal) values ("
                S = S & "N'" & aray_1(i).ToString & "',N'" & aray_2(i).ToString & "',N'" & aray_3(i).ToString & "' ,N'" & aray_4(i).ToString & "')" : cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        Next


        DataGridView1.DataSource = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select sname As [الصنف], SalPrice As [سعر البيع], qunt As [الكمية], tinpricetotal as [الاجمالى] from PrintAllItems"
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)


        Dim SMSalNum As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SMSalNum = SMSalNum + DataGridView1.Rows(i).Cells(2).Value
        Next
        NumberSales = SMSalNum

        '=======================================================================================

        aray_1.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select totalpriceafterdisc from Sales_Bill where CloseSheft=0 and UserName= N'" & UserName & "' and Sheft_Number= N'" & Sheft_Number & "'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr("totalpriceafterdisc"))
        Loop
        Dim SMSal As Double
        For i As Integer = 0 To aray_1.Count - 1
            SMSal += aray_1(i)
        Next
        totalprisesal = SMSal
        totalprisesal = Math.Round(Val(totalprisesal), 2)

        '=======================================================================================

        aray_1.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select totalpriceafterdisc from Sales_Bill where CloseSheft=0 and UserName= N'" & UserName & "' and Stat= N'نقداً' and Sheft_Number= N'" & Sheft_Number & "'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr("totalpriceafterdisc"))
        Loop
        Dim SM1 As Double
        For i As Integer = 0 To aray_1.Count - 1
            SM1 += aray_1(i)
        Next
        totalCash = SM1
        '=======================================================================================
        aray_1.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select totalpriceafterdisc from Sales_Bill where CloseSheft=0 and UserName= N'" & UserName & "' and Stat= N'فيزا' and Sheft_Number= N'" & Sheft_Number & "'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr("totalpriceafterdisc"))
        Loop
        Dim SM2 As Double
        For i As Integer = 0 To aray_1.Count - 1
            SM2 += aray_1(i)
        Next
        totalVisa = SM2
        '=======================================================================================
        aray_1.Clear()
        aray_2.Clear()
        aray_3.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select totalpricebeforedisc,totalpriceafterdisc,disc from Sales_Bill where CloseSheft=0 and UserName= N'" & UserName & "' and Sheft_Number= N'" & Sheft_Number & "'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr("totalpricebeforedisc"))
            aray_2.Add(dr("totalpriceafterdisc"))
            aray_3.Add(dr("disc"))
        Loop
        Dim SM7 As Double
        For i As Integer = 0 To aray_1.Count - 1
            If aray_3(i) <> 0 Then
                If aray_3(i) = 0 Then
                    SM7 += aray_1(i) - aray_2(i)
                Else
                    SM7 += aray_2(i) - Val(aray_3(i))
                End If
            End If
        Next
        TotalDiscount = SM7
        '=======================================================================================

        aray_1.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select totalpriceafterdisc from IM_Bsal where CloseSheft=0 and UserName= N'" & UserName & "'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr("totalpriceafterdisc"))
        Loop
        Dim SMBSal As Double
        For i As Integer = 0 To aray_1.Count - 1
            SMBSal += aray_1(i)
        Next
        TotalReturnSales = SMBSal
        TotalReturnSales = Math.Round(Val(TotalReturnSales), 2)

        '=======================================================================================

        aray_1.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Exp_Value from Expenses where CloseSheft=0 and UserName= N'" & UserName & "'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr("Exp_Value"))
        Loop
        Dim SMExpenses As Double
        For i As Integer = 0 To aray_1.Count - 1
            SMExpenses += aray_1(i)
        Next
        TotalExpenses = SMExpenses
        TotalExpenses = Math.Round(Val(TotalExpenses), 2)

        '=======================================================================================
        NetTotal = Val(totalprisesal) - Val(TotalExpenses) - Val(TotalReturnSales) - Val(TotalDiscount)
        TotalShiftAmount = Val(txtShiftAmount.Text) - Val(NetTotal)
        '=======================================================================================
    End Sub

    Private Sub Language_Report()
        If SummaryFullReport = "Full" Then
            rdoFullReport.Checked = True
        End If
        If SummaryFullReport = "Summary" Then
            rdoSummaryReport.Checked = True
        End If
    End Sub

    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        On Error Resume Next
        SlpitTime()
    End Sub

    Private Sub SlpitTime()
        Dim AMPM As String = ""
        Dim Time As String = Cls.get_time(True)
        Dim split As String() = New String() {":"}
        Dim itemsSplit As String() = Time.Split(split, StringSplitOptions.None)
        Dim Hour As String = itemsSplit(0).ToString()
        Dim Minute As String = itemsSplit(1).ToString()
        Dim Second As String = itemsSplit(2).ToString()

        If Hour.Trim = "00" Then : AMPM = "AM" : Hour = "12" : GoTo TM : End If
        If Hour.Trim = "1" Or Hour.Trim = "01" Then : AMPM = "AM" : Hour = "01" : GoTo TM : End If
        If Hour.Trim = "2" Or Hour.Trim = "02" Then : AMPM = "AM" : Hour = "02" : GoTo TM : End If
        If Hour.Trim = "3" Or Hour.Trim = "03" Then : AMPM = "AM" : Hour = "03" : GoTo TM : End If
        If Hour.Trim = "4" Or Hour.Trim = "04" Then : AMPM = "AM" : Hour = "04" : GoTo TM : End If
        If Hour.Trim = "5" Or Hour.Trim = "05" Then : AMPM = "AM" : Hour = "05" : GoTo TM : End If
        If Hour.Trim = "6" Or Hour.Trim = "06" Then : AMPM = "AM" : Hour = "06" : GoTo TM : End If
        If Hour.Trim = "7" Or Hour.Trim = "07" Then : AMPM = "AM" : Hour = "07" : GoTo TM : End If
        If Hour.Trim = "8" Or Hour.Trim = "08" Then : AMPM = "AM" : Hour = "08" : GoTo TM : End If
        If Hour.Trim = "9" Or Hour.Trim = "09" Then : AMPM = "AM" : Hour = "09" : GoTo TM : End If
        If Hour.Trim = "10" Then : AMPM = "AM" : Hour = "10" : GoTo TM : End If
        If Hour.Trim = "11" Then : AMPM = "AM" : Hour = "11" : GoTo TM : End If
        If Hour.Trim = "12" Then : AMPM = "PM" : Hour = "12" : GoTo TM : End If
        If Hour.Trim = "13" Then : AMPM = "PM" : Hour = "01" : GoTo TM : End If
        If Hour.Trim = "14" Then : AMPM = "PM" : Hour = "02" : GoTo TM : End If
        If Hour.Trim = "15" Then : AMPM = "PM" : Hour = "03" : GoTo TM : End If
        If Hour.Trim = "16" Then : AMPM = "PM" : Hour = "04" : GoTo TM : End If
        If Hour.Trim = "17" Then : AMPM = "PM" : Hour = "05" : GoTo TM : End If
        If Hour.Trim = "18" Then : AMPM = "PM" : Hour = "06" : GoTo TM : End If
        If Hour.Trim = "19" Then : AMPM = "PM" : Hour = "07" : GoTo TM : End If
        If Hour.Trim = "20" Then : AMPM = "PM" : Hour = "08" : GoTo TM : End If
        If Hour.Trim = "21" Then : AMPM = "PM" : Hour = "09" : GoTo TM : End If
        If Hour.Trim = "22" Then : AMPM = "PM" : Hour = "10" : GoTo TM : End If
        If Hour.Trim = "23" Then : AMPM = "PM" : Hour = "11" : GoTo TM : End If
TM:
        TimeAMBMTotal = Hour + ":" + Minute.Trim + ":" + Second.Trim + " " + AMPM

    End Sub



    Private Sub rdoSummaryReport_CheckedChanged(sender As Object, e As EventArgs) Handles rdoSummaryReport.CheckedChanged
        If rdoFullReport.Checked = True Then
            mykey.SetValue("SummaryFullReport", "Full")
        ElseIf rdoSummaryReport.Checked = True Then
            mykey.SetValue("SummaryFullReport", "Summary")
        End If
    End Sub

    Private Sub rdoFullReport_CheckedChanged(sender As Object, e As EventArgs) Handles rdoFullReport.CheckedChanged
        If rdoFullReport.Checked = True Then
            mykey.SetValue("SummaryFullReport", "Full")
        ElseIf rdoSummaryReport.Checked = True Then
            mykey.SetValue("SummaryFullReport", "Summary")
        End If
    End Sub

    Private Sub btnPrintCash_Out_Click(sender As Object, e As EventArgs) Handles btnPrintCash_Out.Click
        SumTotalReport()
        Try
            FillDataAndPrint()
        Catch ex As Exception
            FillDataAndPrint()
        End Try
    End Sub

    Private Sub chkPrintDirect_CheckedChanged(sender As Object, e As EventArgs) Handles chkPrintDirect.CheckedChanged
        If chkPrintDirect.Checked = False Then
            chkPrintDirect.Text = "عرض الطباعة"
        Else
            chkPrintDirect.Text = "طباعة مباشرة"
        End If
    End Sub

    Private Sub txtShiftAmount_TextChanged(sender As Object, e As EventArgs) Handles txtShiftAmount.TextChanged
        txtDifference.Text = Val(txtShiftAmount.Text) - Val(txtCurrentBalance.Text)

        Dim Treasury_Balance As Double = 0
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Treasury_Balance from Treasury where Treasury_Name =N'" & cmbTreasuryFrom.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Treasury_Balance = Val(dr(0).ToString)
        End If
        txtAmountRemainingFrom.Text = Val(Treasury_Balance) - Val(txtShiftAmount.Text)


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Treasury_Balance from Treasury where Treasury_Name =N'" & cmbTreasuryTo.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Treasury_Balance = Val(dr(0).ToString)
        End If
        txtAmountRemainingTo.Text = Val(Treasury_Balance) + Val(txtShiftAmount.Text)
    End Sub

    Private Sub btnCloseTreasuryTransfer_Click(sender As Object, e As EventArgs) Handles btnCloseTreasuryTransfer.Click
        If TreasuryTransferFrom = "" And TreasuryTransferTo = "" Then
            PanelTreasuryTransfer.Dock = DockStyle.Fill
        Else
            PanelTreasuryTransfer.Dock = DockStyle.None
            PanelTreasuryTransfer.Top = 5000
        End If
        mykey.SetValue("TreasuryTransferFrom", cmbTreasuryFrom.Text)
        TreasuryTransferFrom = cmbTreasuryFrom.Text
        mykey.SetValue("TreasuryTransferTo", cmbTreasuryTo.Text)
        TreasuryTransferTo = cmbTreasuryTo.Text

    End Sub

    Private Sub lnkTreasuryTransfer_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles lnkTreasuryTransfer.LinkClicked
        PanelTreasuryTransfer.Top = 300
        PanelTreasuryTransfer.Left = 700
        PanelTreasuryTransfer.Dock = DockStyle.Fill
        PanelTreasuryTransfer.Size = New System.Drawing.Size(504, 175)
        cmbTreasuryFrom.Text = mykey.GetValue("TreasuryTransferFrom", "")
        cmbTreasuryTo.Text = mykey.GetValue("TreasuryTransferTo", "")
    End Sub

    Private Sub cmbTreasuryFrom_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbTreasuryFrom.SelectedIndexChanged
        mykey.SetValue("TreasuryTransferFrom", cmbTreasuryFrom.Text)
        TreasuryTransferFrom = cmbTreasuryFrom.Text
    End Sub

    Private Sub cmbTreasuryTo_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbTreasuryTo.SelectedIndexChanged
        mykey.SetValue("TreasuryTransferTo", cmbTreasuryTo.Text)
        TreasuryTransferTo = cmbTreasuryTo.Text
    End Sub

    Private Sub GetTreasuryTransferFromTo()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Treasury_Balance from Treasury where Treasury_Name =N'" & cmbTreasuryFrom.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            txtAmountRemainingFrom.Text = Val(dr(0).ToString)
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Treasury_Balance from Treasury where Treasury_Name =N'" & cmbTreasuryTo.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            txtAmountRemainingTo.Text = Val(dr(0).ToString)
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Treasury_Balance from Treasury where Treasury_Name =N'" & cmbTreasuryFrom.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            txtAmountRemainingFrom.Text = Val(dr(0).ToString)
        End If
        Dim EMPID As String = Cls.Get_Code_Value_Branch_More("Employees", "EMPID", "NameEmployee=N'" & cmbEmployees.Text.Trim & "'")
        Dim Treasury_Code_TO As String = Cls.Get_Code_Value_Branch_More("Treasury", "Treasury_Code", "Treasury_Name=N'" & cmbTreasuryTo.Text.Trim & "'")
        Dim Treasury_Code_From As String = Cls.Get_Code_Value_Branch_More("Treasury", "Treasury_Code", "Treasury_Name=N'" & cmbTreasuryFrom.Text.Trim & "'")
        Dim ACCNumber As String = Cls.Get_Code_Value_Branch_More("AccountsTreeLinking", "ACCNumber", "Link_Statement=N'الخزينة'")


        Dim TreasuryMovement_ID As String = Cls.MAXRECORD("TreasuryMovement_Deposit", "TreasuryMovement_ID")

        'سحب مبلغ من الخزينة
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into TreasuryMovement_Withdraw (TreasuryMovement_ID,Treasury_Code,Treasury_Amount,ACCNumber,Treasury_Date,Treasury_Time,Notes,EMPID,Company_Branch_ID,UserName)  values("
        S = S & "N'" & TreasuryMovement_ID & "',N'" & Treasury_Code_From & "',N'" & txtShiftAmount.Text.Trim & "',N'" & ACCNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Cls.get_time(True) & "',N'" & txtNotes.Text.Trim & "',N'" & EMPID & "',N'" & Company_Branch_ID & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Treasury set Treasury_Balance = N'" & Val(txtAmountRemainingFrom.Text) & "' where Treasury_Name =N'" & cmbTreasuryFrom.Text & "'" : cmd.ExecuteNonQuery()


        'إيداع مبلغ الى الخزينة
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into TreasuryMovement_Deposit (TreasuryMovement_ID,Treasury_Code,Treasury_Amount,ACCNumber,Treasury_Date,Treasury_Time,Notes,EMPID,Company_Branch_ID,UserName)  values("
        S = S & "N'" & TreasuryMovement_ID & "',N'" & Treasury_Code_TO & "',N'" & txtShiftAmount.Text.Trim & "',N'" & ACCNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Cls.get_time(True) & "',N'" & txtNotes.Text.Trim & "',N'" & EMPID & "',N'" & Company_Branch_ID & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Treasury set Treasury_Balance = N'" & Val(txtAmountRemainingTo.Text) & "' where Treasury_Name =N'" & cmbTreasuryTo.Text & "'" : cmd.ExecuteNonQuery()


        Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
        If UseAccounts = "YES" Then
            Daily_Restrictions()
        End If

    End Sub

    Private Sub Daily_Restrictions()
        'Dim Account As String = "" : Dim AccountCode As String = "" : Dim Paying As String = "" : Dim PayingCode As String = ""
        'Dim Discounts As String = "" : Dim DiscountsCode As String = "" : Dim AccountTreasury As String = "" : Dim AccountCodeTreasury As String = ""

        'Try
        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مبيعات'" : dr = cmd.ExecuteReader
        '    If dr.Read Then
        '        Account = dr("Link_AccountsTree") : AccountCode = dr("ACCNumber")
        '    End If

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مقبوضات عملاء'" : dr = cmd.ExecuteReader
        '    If dr.Read Then
        '        Paying = dr("Link_AccountsTree") : PayingCode = dr("ACCNumber")
        '    End If

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'خصومات عملاء'" : dr = cmd.ExecuteReader
        '    If dr.Read Then
        '        Discounts = dr("Link_AccountsTree") : DiscountsCode = dr("ACCNumber")
        '    End If

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "Select ACCName,ACCNumber from AccountsTree where ACCName=N'الخزينة'" : dr = cmd.ExecuteReader
        '    If dr.Read Then
        '        AccountTreasury = dr("ACCName") : AccountCodeTreasury = dr("ACCNumber")
        '    End If

        '    '========================================================================================

        '    Dim MOVID As String = Cls.MAXRECORD("MOVES", "MOVID")
        '    Dim MOVRegNumber As String = Cls.MAXRECORD("MOVES", "MOVRegNumber")

        '    If ChkCash.Checked = True Or chkVisa.Checked = True Then
        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        S = "insert into MOVES(MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
        '        S = S & "N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Account & "',N'" & txttotalpeforedisc.Text & "',N'" & txttotalpeforedisc.Text & "',N'" & UserName & "')"
        '        cmd.CommandText = S : cmd.ExecuteNonQuery()
        '    End If

        '    If ChkState.Checked = True Then
        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        S = "insert into MOVES(MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
        '        S = S & "N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Account & "',N'" & txttotalpeforedisc.Text & "',N'" & txttotalpeforedisc.Text & "',N'" & UserName & "')"
        '        cmd.CommandText = S : cmd.ExecuteNonQuery()
        '    End If

        '    If Val(txtpaying.Text) > 0 Then
        '        ' من حساب / الخزينة
        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        S = "insert into MOVESDATA (MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
        '        S = S & "N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'" & txtpaying.Text & "',N'0',N'" & AccountTreasury & "',N'" & UserName & "')"
        '        cmd.CommandText = S : cmd.ExecuteNonQuery()

        '        ' الى حساب / مقبوضات عملاء
        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        S = "insert into MOVESDATA (MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
        '        S = S & "N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & PayingCode & "',N'" & Paying & "',N'0',N'" & txtpaying.Text & "',N'" & Paying & "',N'" & UserName & "')"
        '        cmd.CommandText = S : cmd.ExecuteNonQuery()
        '    End If

        '    If Val(txtdisc.Text) > 0 Then
        '        ' من حساب / خصومات المبيعات
        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        S = "insert into MOVESDATA (MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
        '        S = S & "N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & DiscountsCode & "',N'" & Discounts & "',N'" & DiscTotal & "',N'0',N'" & Discounts & "',N'" & UserName & "')"
        '        cmd.CommandText = S : cmd.ExecuteNonQuery()

        '        ' الى حساب / الخزينة
        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        S = "insert into MOVESDATA (MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
        '        S = S & "N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'0',N'" & DiscTotal & "',N'" & AccountTreasury & "',N'" & UserName & "')"
        '        cmd.CommandText = S : cmd.ExecuteNonQuery()

        '    End If
        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub

    Private Sub GetTreasury_Transfer()
        Dim Treasury_Code As String = Cls.Get_Code_Value_Branch("Users", "Treasury_Code", "UserName", UserName)
        cmbTreasuryFrom.Text = Cls.Get_Code_Value_Branch("Treasury", "Treasury_Name", "Treasury_Code", Treasury_Code)

        Dim Treasury_Code_Transfer As String = Cls.Get_Code_Value_Branch("Users", "Treasury_Code_Transfer", "UserName", UserName)
        cmbTreasuryTo.Text = Cls.Get_Code_Value_Branch("Treasury", "Treasury_Name", "Treasury_Code", Treasury_Code_Transfer)

    End Sub

End Class