﻿Imports vb = Microsoft.VisualBasic
Imports System.Data.OleDb
Imports CrystalDecisions.CrystalReports.Engine

Public Class Frm_Product_Order_Filling_Manufacturing
    Dim ListBoxSelectedIndex As Integer
    Dim QuntAll As Integer
    Dim WithEvents BS As New BindingSource
    Dim AlaertParcode As Boolean
    Dim itm_id As String = ""
    Dim itm_cat As String = ""
    Dim itm_name As String = ""
    Dim Unity As String = ""
    Dim price As String = ""
    Dim totalprice As String = ""
    Dim qunt As String = ""
    Dim qunt_unity As String = ""
    Dim quntTotal As String = ""
    Dim StoresFrom As String = ""
    Dim StoresTo As String = ""
    Dim QuntFrom As Double
    Dim TinPrice As String = ""
    Private Sub Headerx()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If PermtionName = "مدير" Then
            S = "select itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الصنف],TinPrice as [سعر الشراء],SalPrice as [سعر البيع],rng as [الحد الأدنى] ,store as [المخزون],Stores as [المخزن] from items order by 1"
        Else
            S = "select itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الصنف],TinPrice as [سعر الشراء],SalPrice as [سعر البيع],rng as [الحد الأدنى] ,store as [المخزون],Stores as [المخزن] from items where Company_Branch_ID =N'" & Company_Branch_ID & "' order by 1"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)
        DTGV.Columns(0).Width = 60
        DTGV.Columns(1).Width = 70
        DTGV.Columns(2).Width = 120
        DTGV.Columns(3).Width = 50
        DTGV.Columns(4).Width = 50
        DTGV.Columns(5).Width = 45
        DTGV.Columns(6).Width = 45
        DTGV.Columns(7).Width = 80

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT SUM(ValStore) FROM ITEMS" : dr = cmd.ExecuteReader : dr.Read()
        'Label3.Text = "سعر المخزون = " & dr(0)
        'dr.Close()

        'Label5.Text = "عدد الأصناف = " & DTGV.RowCount
    End Sub

    Private Sub FrmItemsNew_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Me.KeyUp
    End Sub
    Private Sub FrmItemsNew_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cls.FillComboDataAdapterQuickSearch("Items", "sname", cmbitmnm)
        Cls.fill_combo_Branch("stores", "store", cmbStoresFrom)
        Cls.fill_combo_Branch("stores", "store", cmbStoresTo)
        Cls.fill_combo_Branch("stores", "store", cmbStoreManufacturing)
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployees)
        'txtsearsh.Text = " بحث ...." : txtsearsh.Font = New Font(txtsearsh.Font, FontStyle.Italic)
        'Headerx()
        MAXRECORD()
        cmbEmployees.Focus()
    End Sub

    Private Sub cmbcats_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbcats.DropDown
        TxtPrc.Text = ""
        cmbcats.Text = ""
        cmbitmnm.Text = ""
        txtqunt.Text = ""
        txtquntAll.Text = ""
    End Sub

    Function ValidateSave() As Boolean

        If txtbill_no.Text = "" Then MsgBox("فضلا أدخل رقم أذن التحويل", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbStoresFrom.Text = "" Then MsgBox("من فضلك اختر المخزن المحول منه", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If cmbStoresTo.Text = "" Then MsgBox("من فضلك اختر المخزن المحول البه", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If DTGV.Rows.Count < 1 Then MsgBox("فضلا أكمل بيانات الفاتورة", MsgBoxStyle.Exclamation) : Return False
        If txtitm_id_Manufacturing.Text = "" Then MsgBox("فضلا أدخل الباركود", MsgBoxStyle.Exclamation) : Return False
        If cmbItemsManufacturing.Text = "" Then MsgBox("من فضلك اختر أسم المنتج المصنع", MsgBoxStyle.Exclamation) : cmbItemsManufacturing.Focus() : Return False
        If cmbStoreManufacturing.Text = "" Then MsgBox("من فضلك اختر مخزن المنتج المصنع", MsgBoxStyle.Exclamation) : cmbStoreManufacturing.Focus() : Return False


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from ManufacturingFillOrder where bill_no =N'" & txtbill_no.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H > 0 Then
            MsgBox("رقم أمر الشغل مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbill_no.Focus() : Return False
        End If
        Return True
    End Function

    Sub Clear_All()
        cmbitmnm.Text = ""
        TxtPrc.Text = ""
        txtqunt.Text = ""
        txtquntUnity.Text = ""
        txtquntAll.Text = ""
        cmbStoresFrom.Text = ""
        cmbStoresTo.Text = ""
        txtquntUnity.Text = ""
        cmbitmnm.Focus()
        DTGV.DataSource = ""
        cmbItemsManufacturing.Text = ""
        cmbCatsManufacturing.Text = ""
        cmbStoreManufacturing.Text = ""
        cmbEmployees.Text = ""
        cmbUnity.Text = ""
    End Sub

    Private Sub btnsave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnsave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateTextAdd() = False Then Exit Sub

        DTGV.DataSource = Fn_AddBill(0, TxtPrc.Text, cmbcats.Text, cmbitmnm.Text, cmbUnity.Text, Val(TinPrice), Val(txtqunt.Text), Val(txtquntUnity.Text), Val(txtquntAll.Text), cmbStoresFrom.Text, cmbStoresTo.Text)
        ClearAdd()
        TxtPrc.Focus()
        SumAllPrice()
        DTGV.Columns(0).Visible = False
        DTGV.Columns(6).Visible = False

    End Sub

    Private Sub SumAllPrice()
        Dim SM As Double
        For i As Integer = 0 To DTGV.Rows.Count - 1
            SM = SM + DTGV.Rows(i).Cells(7).Value
        Next
        txtTotal_qu.Text = SM

        Dim SM1 As Double
        For i As Integer = 0 To DTGV.Rows.Count - 1
            SM1 += DTGV.Rows(i).Cells(5).Value * DTGV.Rows(i).Cells(7).Value
        Next
        txtCostPrice.Text = SM1

    End Sub

    Dim Dt_AddBill As New DataTable
    Friend Function Fn_AddBill(ByVal Col_IDTM As String, ByVal Col_Prc As String, ByVal Col_Cats As String, ByVal Col_Name As String, ByVal Col_Unity As String, ByVal Col_TinPrice As String _
, ByVal Col_Quant As Double, ByVal Col_qu_unity As Double, ByVal Col_QuantRemainder As Double, ByVal Col_StoreFrom As String, ByVal Col_StoreTo As String) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("IDTM", GetType(String))
            Dt_AddBill.Columns.Add("الباركود", GetType(String))
            Dt_AddBill.Columns.Add("أسم المجموعة", GetType(String))
            Dt_AddBill.Columns.Add("أسم الصنف", GetType(String))
            Dt_AddBill.Columns.Add("وحدة القياس", GetType(String))
            Dt_AddBill.Columns.Add("سعر الشراء", GetType(Double))
            Dt_AddBill.Columns.Add("1الكمية", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية الباقية", GetType(Double))
            Dt_AddBill.Columns.Add("من مخزن", GetType(String))
            Dt_AddBill.Columns.Add("الى مخزن", GetType(String))
        End If

        'DTV_Width()

        Dt_AddBill.Rows.Add(Col_IDTM, Col_Prc, Col_Cats, Col_Name, Col_Unity, Col_TinPrice, Col_Quant, Col_qu_unity, Col_QuantRemainder, Col_StoreFrom, Col_StoreTo)
        Return Dt_AddBill
    End Function

    Private Sub ClearAdd()
        cmbUnity.Text = ""
        cmbcats.Text = ""
        cmbitmnm.Text = ""
        txtqunt.Text = ""
        txtquntUnity.Text = ""
        txtquntAll.Text = ""
        TxtPrc.Text = ""
        cmbUnity.Text = ""
        TinPrice = ""
    End Sub

    Function ValidateTextAdd() As Boolean
        If txtbill_no.Text = "" Then MsgBox("فضلا أدخل رقم أذن التحويل", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbcats.Text = "" Then MsgBox("فضلا أدخل مجموعة الصنف", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbitmnm.Text = "" Then MsgBox("فضلا أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbitmnm.Focus() : Return False
        If cmbStoresFrom.Text = "" Then MsgBox("من فضلك اختر المخزن المحول منه", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If cmbStoresTo.Text = "" Then MsgBox("من فضلك اختر المخزن المحول البه", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If txtqunt.Text = "" Then MsgBox("فضلا أدخل الكمية ", MsgBoxStyle.Exclamation) : txtqunt.Focus() : Return False
        If TxtPrc.Text = "" Then MsgBox("فضلا أدخل باركود الصنف", MsgBoxStyle.Exclamation) : Return False
        If cmbItemsManufacturing.Text = "" Then MsgBox("فضلا أدخل أسم المنتج المصنع", MsgBoxStyle.Exclamation) : Return False
        If cmbUnity.Text = "" Then MsgBox("فضلا وحدة القياس", MsgBoxStyle.Exclamation) : Return False
        If txtitm_id_Manufacturing.Text = "" Then MsgBox("فضلا أدخل الباركود", MsgBoxStyle.Exclamation) : Return False


        Dim XMSG As String

        Dim Xstore As Double = IM.Get_Itm_Store(TxtPrc.Text, cmbStoresFrom.Text)
        If cmbStoresFrom.Text = cmbStoresTo.Text Then
            MsgBox("لايمكن التحويل من المخزن الى نفسة اختار مخزن اخر", MsgBoxStyle.Critical)
            Return False
        End If
        Dim IncreaseQuantity As String = mykey.GetValue("IncreaseQuantity", "NO")
        If IncreaseQuantity <> "YES" Then
            If Val(txtqunt.Text) > Xstore Then
                MsgBox("الكمية بالمخزن لا تكفي الكمية التى سيتم نقلها من المخزن", MsgBoxStyle.Critical)
                Return False
            End If
        End If

        If Xstore < 1 Then
            XMSG = MsgBox("الكمية بالمخزن قد نفذت أو أنك لم تقم بتسجيل آخر عملية مشتريات " & Environment.NewLine & MsgBoxStyle.MsgBoxRight + MsgBoxStyle.Exclamation)
            Exit Function
        End If

        If txtquntAll.Text < 0 Then
            MsgBox("لايمكن التحويل من المخزن لان الكمية لا تكفى لتحويل القيمة المسموح به", MsgBoxStyle.Critical)
            Return False
        End If

        Return True
    End Function
    Function SumListCombo(ByVal X As ListBox)
        Dim SM As Integer
        For i As Integer = 0 To X.Items.Count - 1
            SM = SM + Val(X.Items(i))
        Next
        Return SM
    End Function


    Function ValidatAdd() As Boolean
        If cmbcats.Text.Trim = "" Then MsgBox("أختر مجموعة الصنف", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbitmnm.Text.Trim = "" Then MsgBox("أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbitmnm.Focus() : Return False
        If Val(txtqunt.Text) > 0 Then
            'If Val(txttinprice.Text) = 0 Then
            '    MsgBox("فضلا أدخل سعر شراء الوحدة", MsgBoxStyle.Exclamation) : txttinprice.Focus() : Return False
            'End If
        End If

        Return True
    End Function

    Private Sub txtsearsh_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles txtsearsh.MouseClick
        If txtsearsh.Text = " بحث ...." Then txtsearsh.SelectAll() : txtsearsh.Font = New Font(txtsearsh.Font, FontStyle.Regular)
    End Sub

    Private Sub TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtsearsh.TextChanged
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
            If PermtionName = "مدير" Then
                S = "select itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الاسم],TinPrice as [سعر الشراء],SalPrice as [سعر البيع],rng as [الحد الأدنى] ,store as [المخزون],Stores as [المخزن] from items"
            Else
                S = "select itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الاسم],TinPrice as [سعر الشراء],SalPrice as [سعر البيع],rng as [الحد الأدنى] ,store as [المخزون],Stores as [المخزن] from items where Company_Branch_ID =N'" & Company_Branch_ID & "'"
            End If
        Else
            Dim X As String = "%" & txtsearsh.Text.Trim & "%"
            If PermtionName = "مدير" Then
                S = "select itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الاسم],TinPrice as [سعر الشراء],SalPrice as [سعر البيع],rng as [الحد الأدنى] ,store as [المخزون],Stores as [المخزن] from items where  sname like N'" & X & "'"
            Else
                S = "select itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الاسم],TinPrice as [سعر الشراء],SalPrice as [سعر البيع],rng as [الحد الأدنى] ,store as [المخزون],Stores as [المخزن] from items where  sname like N'" & X & "' and Company_Branch_ID =N'" & Company_Branch_ID & "'"
            End If
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)
        DTGV.Columns(0).Width = 60
        DTGV.Columns(1).Width = 70
        DTGV.Columns(2).Width = 120
        DTGV.Columns(3).Width = 50
        DTGV.Columns(4).Width = 50
        DTGV.Columns(5).Width = 45
        DTGV.Columns(6).Width = 45
        DTGV.Columns(7).Width = 80
        'Label5.Text = "عدد الأصناف = " & DTGV.RowCount
    End Sub

    Private Sub txtqunt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtqunt.TextChanged
        If Not IsNumeric(txtqunt.Text) Then
            System.Windows.Forms.SendKeys.Send("{backspace}")
        End If
        txtquntAll.Text = Val(QuntAll) - Val(QuntFrom) - Val(txtqunt.Text)
    End Sub

    Private Sub txtrng_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        On Error Resume Next
        If e.KeyCode = 13 Then
            'TxtPrc.Focus()
            '' 
            'Cls.Get_Value_Count_More("items", "group_name =N'" & cmbcats.Text.Trim & "'")
            'Dim X As String
            'Dim XH As String
            'XH = H
            'If Len(XH) = 1 Then
            '    X = "0" & H
            'ElseIf Len(XH) = 2 Then
            '    X = H
            'End If
            'cmd.CommandText = "select id from Groups where G_name =N'" & cmbcats.Text.Trim & "'"
            'dr = cmd.ExecuteReader : dr.Read()

            'If Len(txttinprice.Text) = 1 Then
            '    TxtPrc.Text = dr(0) & "0" & Int(txttinprice.Text.Trim) & X
            'ElseIf Len(txttinprice.Text) = 2 Then
            '    TxtPrc.Text = dr(0) & Int(txttinprice.Text.Trim) & X
            'ElseIf Len(txttinprice.Text) = 3 Then
            '    TxtPrc.Text = dr(0) & "00" & X
            'ElseIf Len(txttinprice.Text) = 4 Then
            '    TxtPrc.Text = dr(0) & "0" & Int(txttinprice.Text.Trim) & X
            'End If
        End If
        TxtPrc.Text = vb.Right(Clss.GenerateItmId_Or_Parcode(), 8)
        TxtPrc.Focus()
        TxtPrc.SelectAll()
    End Sub

    Private Sub cmbcats_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbcats.SelectedIndexChanged
        'If cmbcats.Text.Trim = "" Then Exit Sub
        'cmbitmnm.Items.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select sname from Items where group_name =N'" & cmbcats.Text & "' order by 1"
        'dr = cmd.ExecuteReader
        'Do While dr.Read
        '    cmbitmnm.Items.Add(Trim(dr(0)))
        'Loop
        'cmbitmnm.Text = ""
    End Sub

    Private Sub BtnAddCat_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        FrmCats.Show()
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        TxtPrc.Text = vb.Right(Clss.GenerateItmId_Or_Parcode(), 8)
    End Sub

    Private Sub cmbitmnm_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbitmnm.DropDown
        TxtPrc.Text = ""
        cmbitmnm.Text = ""
        cmbcats.Text = ""
        txtqunt.Text = ""
        txtquntAll.Text = ""
        cmbUnity.Text = ""
        txtquntUnity.Text = ""
    End Sub

    Private Sub cmbitmnm_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbitmnm.KeyUp
        If e.KeyCode = 13 Then
            If AlaertParcode = False Then
                GetDataSales()
            End If
        End If
    End Sub

    Private Sub GetDataSales()
        Bol = True
        QuntFrom = 0
1:
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id,group_name,Unity,store,TinPrice from items where sname=N'" & cmbitmnm.Text & "' and Stores=N'" & cmbStoresFrom.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                TxtPrc.Text = dr("itm_id").ToString()
                cmbcats.Text = dr("group_name").ToString()
                cmbUnity.Text = dr("Unity").ToString()
                QuntAll = dr("store").ToString()
                TinPrice = dr("TinPrice").ToString()
            End If
        Catch ex As Exception
            GoTo 1
        End Try

        '==========================================================

        For i As Integer = 0 To DTGV.Rows.Count - 1
            If DTGV.Rows(i).Cells(9).Value = cmbStoresFrom.Text Then
                If DTGV.Rows(i).Cells(1).Value = TxtPrc.Text Then
                    QuntFrom += DTGV.Rows(i).Cells(7).Value
                End If
            End If
        Next

        txtquntAll.Text = QuntAll - QuntFrom

        GetItemsUnity(cmbUnity, TxtPrc.Text)

        txtqunt.Text = 0
        txtquntUnity.Text = 0
        txtquntUnity.Focus()
        txtquntUnity.SelectAll()
        Bol = False

    End Sub

    Private Sub TxtPrc_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles TxtPrc.KeyDown
        If ((e.KeyCode = Keys.S) AndAlso (e.Modifiers = (Keys.Control))) Then
            btnSaveAll.PerformClick()
        End If
    End Sub

    Private Sub TxtPrc_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles TxtPrc.KeyUp
        If e.KeyCode = 13 Then
            AlaertParcode = True
            Bol = True
            QuntFrom = 0
1:
            Try
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select itm_id,group_name,Unity,store from items where itm_id=N'" & TxtPrc.Text & "' and Stores=N'" & cmbStoresFrom.Text & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    TxtPrc.Text = dr("itm_id").ToString()
                    cmbcats.Text = dr("group_name").ToString()
                    cmbUnity.Text = dr("Unity").ToString()
                    QuntAll = dr("store").ToString()
                End If
            Catch ex As Exception
                GoTo 1
            End Try
            '==========================================================
            For i As Integer = 0 To DTGV.Rows.Count - 1
                If DTGV.Rows(i).Cells(9).Value = cmbStoresFrom.Text Then
                    If DTGV.Rows(i).Cells(1).Value = TxtPrc.Text Then
                        QuntFrom += DTGV.Rows(i).Cells(6).Value
                    End If
                End If
            Next

            txtquntAll.Text = QuntAll - QuntFrom

            GetItemsUnity(cmbUnity, TxtPrc.Text)

            txtqunt.Text = 0
            txtquntUnity.Text = 0
            txtquntUnity.Focus()
            txtquntUnity.SelectAll()
            Bol = False
            AlaertParcode = False
        End If
    End Sub

    Sub AddStores(ByVal Store As String)
        REM للتاكد من عدم التكرار
        Dim TableName, FieldName, StringFind As String
        Dim S As String
        TableName = "stores"
        FieldName = "store"
        StringFind = Store
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select * From " & TableName & " Where " & FieldName & "=N'" & StringFind & "'"

        cmd.CommandType = CommandType.Text
        cmd.CommandText = S
        dr = cmd.ExecuteReader
        'dr.Read()
        If dr.HasRows = False Then
            Cls.Get_Value_Count_More("Stores", "StausMainStore =N'0'")
            Dim StausMainStore As Integer
            If H = 0 Then
                StausMainStore = 0
            Else
                StausMainStore = 1
            End If
            REM لحفظ المخزن
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Stores(Company_Branch_ID,store,UserName,StausMainStore) values (N'" & Company_Branch_ID & "',N'" & StringFind & "',N'" & UserName & "',N'" & StausMainStore & "')"
            cmd.CommandText = S
            cmd.ExecuteNonQuery()
        End If
    End Sub
    Sub AddGroubs(ByVal Cats As String)
        REM للتاكد من عدم التكرار
        Dim TableName, FieldName, StringFind As String
        Dim S As String
        TableName = "groups"
        FieldName = "g_name"
        StringFind = Cats
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select * From " & TableName & " Where " & FieldName & "=N'" & StringFind & "'"

        cmd.CommandType = CommandType.Text
        cmd.CommandText = S
        dr = cmd.ExecuteReader
        'dr.Read()
        If dr.HasRows = False Then
            dr.Close()
            REM لحفظ المجموعه
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into groups(Company_Branch_ID,g_name,UserName) values (N'" & Company_Branch_ID & "',N'" & StringFind & "',N'" & UserName & "')"
            cmd.CommandText = S
            cmd.ExecuteNonQuery()
        End If

    End Sub

    Private Sub cmbUnity_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbUnity.KeyUp
        If e.KeyCode = 13 Then
            cmbStoresFrom.Focus()
        End If
    End Sub

    Private Sub cmbStores_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbStoresFrom.KeyUp
        If e.KeyCode = 13 Then
            cmbStoresTo.Focus()
        End If
    End Sub

    Private Sub cmbStoresTo_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbStoresTo.KeyUp
        If e.KeyCode = 13 Then
            GetDataStores()
            TxtPrc.Focus()
        End If
    End Sub

    Private Sub cmbStoresTo_MouseClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles cmbStoresTo.MouseClick
        GetDataStores()
    End Sub

    Private Sub GetDataStores()
        Bol = True

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id from items where sname=N'" & cmbitmnm.Text.Trim & "' and Stores =N'" & cmbStoresTo.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            TxtPrc.Text = dr(0)
        End If

        TxtPrc.Focus()
        TxtPrc.SelectAll()

        Bol = False
    End Sub

    Private Sub txtNumberTransfer_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtbill_no.TextChanged
        If Not IsNumeric(txtbill_no.Text) Then
            System.Windows.Forms.SendKeys.Send("{backspace}")
        End If
    End Sub

    Private Sub MAXRECORD()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from ManufacturingFillOrder"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Me.txtbill_no.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(bill_no As float)) as mb FROM ManufacturingFillOrder where bill_no <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            Me.txtbill_no.Text = sh + 1
        End If
    End Sub

    Private Sub cmbStores_DropDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbStoresFrom.DropDown
        TxtPrc.Text = ""
        cmbcats.Text = ""
        cmbitmnm.Text = ""
        txtqunt.Text = ""
        txtquntUnity.Text = ""
        txtquntAll.Text = ""
        cmbUnity.Text = ""
    End Sub

    Private Sub cmbStoresTo_DropDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbStoresTo.DropDown
        TxtPrc.Text = ""
        cmbcats.Text = ""
        cmbitmnm.Text = ""
        txtqunt.Text = ""
        txtquntUnity.Text = ""
        txtquntUnity.Text = ""
        txtquntUnity.Text = ""
        txtquntAll.Text = ""
    End Sub

    Private Sub btnSaveAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSaveAll.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateSave() = False Then Exit Sub

        ItemsTransferBill()

        If chkprint.Checked = True Then
            PrintReport()
        End If
        Bra.Fil("groups", "g_name", cmbcats)
        Cls.fill_combo_Branch("stores", "store", cmbStoresFrom)
        Dt_AddBill.Rows.Clear()
        Clear_All()
        MAXRECORD()
        MsgBox("تمت عملية التحويل بنجاح", MsgBoxStyle.Information)

        'Cls.FillComboDataAdapterQuickSearch("Items", "sname", cmbitmnm)

    End Sub

    Private Sub PrintReport()
        Dim CostPrice As String = ""
        Dim Manufacturing_Allowance As String = ""
        Dim Filling_Allowance As String = ""
        Dim TotalCostPrice As String = ""
        Dim itm_id_Manufacturing As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from View_Product_Manufacturing where  SnameManufacturing = N'" & cmbItemsManufacturing.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            itm_id_Manufacturing = dr("itm_id_Manufacturing")
            CostPrice = dr("TinPrice")
            Manufacturing_Allowance = dr("Manufacturing_Allowance")
            Filling_Allowance = dr("Filling_Allowance")
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        For i As Integer = 0 To DTGV.Rows.Count - 1
            TotalCostPrice = Val(DTGV.Rows(i).Cells(5).Value) * Val(DTGV.Rows(i).Cells(6).Value)
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases(Company_Branch_ID,bill_no,bill_date,itm_id,itm_cat,itm_name,Unity,price,qu,totalpriceafterdisc,store,Stat,totalprice) values"
            S = S & " (N'" & Company_Branch_ID & "',N'" & txtbill_no.Text & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & DTGV.Rows(i).Cells(1).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(3).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(5).Value & "',N'" & DTGV.Rows(i).Cells(6).Value & "',N'" & TotalCostPrice & "',N'" & DTGV.Rows(i).Cells(9).Value & "',N'" & DTGV.Rows(i).Cells(10).Value & "',N'" & txtTotal_qu.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New Rpt_Product_Manufacturing
        Dim txtname, txtNameAr, txtNameEn, sname_Object, CostPrice_Object, Weightqunt_Object, Stores_Manufacturing_Object, TotalCostPrice_Object, Manufacturing_Allowance_Object, Filling_Allowance_Object, txtbill_no_Object, txtbill_no_ObjectText, bill_date_Object As TextObject

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "امر شغل تعبئة وتصنيع"
        txtNameAr = rpt.Section1.ReportObjects("txtTitelNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelNameEn")
        txtNameEn.Text = NameEnCompany
        txtbill_no_Object = rpt.Section1.ReportObjects("txtbill_no")
        txtbill_no_Object.Text = txtbill_no.Text
        txtbill_no_ObjectText = rpt.Section1.ReportObjects("txtbill_noText")
        txtbill_no_ObjectText.Text = "رقم أمر الشغل"
        sname_Object = rpt.Section1.ReportObjects("txtsname")
        sname_Object.Text = cmbItemsManufacturing.Text
        CostPrice_Object = rpt.Section1.ReportObjects("txtCostPrice")
        CostPrice_Object.Text = CostPrice
        Weightqunt_Object = rpt.Section1.ReportObjects("txtWeightqunt")
        Weightqunt_Object.Text = txtTotal_qu.Text
        Stores_Manufacturing_Object = rpt.Section1.ReportObjects("txtStores")
        Stores_Manufacturing_Object.Text = cmbStoreManufacturing.Text
        TotalCostPrice_Object = rpt.Section1.ReportObjects("txtTotalCostPrice")
        TotalCostPrice_Object.Text = txtCostPrice.Text
        Manufacturing_Allowance_Object = rpt.Section1.ReportObjects("txtManufacturing_Allowance")
        Manufacturing_Allowance_Object.Text = Manufacturing_Allowance
        Filling_Allowance_Object = rpt.Section1.ReportObjects("txtFilling_Allowance")
        Filling_Allowance_Object.Text = Filling_Allowance
        bill_date_Object = rpt.Section1.ReportObjects("txtbill_date")
        bill_date_Object.Text = dtpDateItem.Text
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "امر شغل تعبئة وتصنيع"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Sub ForPrintAll(ByVal BILL_NO As String, ByVal bill_date As String, ByVal StoresFrom As String, ByVal StoresTO As String, ByVal Total_qu As String,
                    ByVal itm_id As String, ByVal itm_cat As String, ByVal itm_name As String, ByVal itm_Unity As String, ByVal qu As String)


        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "SP_Print_Bill_ItemsTransfer"
        cmd.Parameters.Clear()

        cmd.Parameters.AddWithValue("@bill_no", BILL_NO)
        cmd.Parameters.AddWithValue("@bill_date", bill_date)
        cmd.Parameters.AddWithValue("@Vendorname", StoresFrom)
        cmd.Parameters.AddWithValue("@CustomerName", StoresTO)
        cmd.Parameters.AddWithValue("@totalpriceafterdisc", Total_qu)
        cmd.Parameters.AddWithValue("@itm_id", itm_id)
        cmd.Parameters.AddWithValue("@itm_cat", itm_cat)
        cmd.Parameters.AddWithValue("@itm_name", itm_name)
        cmd.Parameters.AddWithValue("@Unity", itm_Unity)
        cmd.Parameters.AddWithValue("@qu", qu)
        cmd.ExecuteNonQuery()

    End Sub

    Private Sub btnNew_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnNew.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        ClearGrid()
        Clear_All()
        MAXRECORD()
    End Sub

    Sub ClearGrid()
        Dt_AddBill.Rows.Clear() : Clear_All()
        DTGV.DataSource = ""
        Clear_All()
    End Sub

    Private Sub txtqunt_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtqunt.KeyUp
        If e.KeyCode = 13 Then
            btnsave.PerformClick()
        End If
    End Sub
    Dim RNXD As Integer
    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        RNXD = DTGV.CurrentRow.Index
        DTGV.Rows.RemoveAt(RNXD)
    End Sub

    Private Sub ImportExcel()
        DTGV.Columns.Clear()
        Dim MyFileDialog As New OpenFileDialog()
        'Dim Xsheet As String = txtSheetName.Text
        With MyFileDialog
            .Filter = "Excel files|*xlsx;*xls"
            .Title = "Open File"
            .ShowDialog()
        End With

        If MyFileDialog.FileName.ToString <> "" Then
            Dim ExcelFile As String = MyFileDialog.FileName.ToString
            Dim ds As New DataSet
            Dim da As OleDbDataAdapter
            Dim dt As DataTable
            Dim conn As OleDbConnection
            conn = New OleDbConnection(
                       "Provider=Microsoft.Jet.OLEDB.4.0;" &
                       "Data Source= " & ExcelFile & ";" &
                       "Extended Properties=Excel 8.0;")
            Try

                da = New OleDbDataAdapter("select * from [Items$]", conn)
                conn.Open()
                da.Fill(ds, "Items")
                dt = ds.Tables("Items")

            Catch ex As Exception
                MsgBox(ex.Message)
                conn.Close()
            End Try
            Try
                DTGV.DataSource = ds
                DTGV.DataMember = "Items"
            Catch ex As Exception
                MsgBox(ex.Message)
            End Try
        End If
    End Sub

    Private Sub txtNumberTransfer_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtbill_no.KeyUp
        If e.KeyCode = 13 Then
            dtpDateItem.Focus()
        End If
    End Sub

    Private Sub dtpDateItem_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDateItem.KeyUp
        If e.KeyCode = 13 Then
            cmbStoresFrom.Focus()
        End If
    End Sub

    Private Sub cmbUser_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbEmployees.KeyUp
        If e.KeyCode = 13 Then
            cmbStoresFrom.Focus()
            cmbStoresFrom.SelectAll()
        End If
    End Sub

    Private Sub cmbCatsManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbCatsManufacturing.SelectedIndexChanged
        If cmbCatsManufacturing.Text.Trim = "" Then Exit Sub
        cmbItemsManufacturing.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sname from Items where group_name =N'" & cmbCatsManufacturing.Text & "' and Stores =N'" & cmbStoreManufacturing.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbItemsManufacturing.Items.Add(Trim(dr(0)))
        Loop
        cmbItemsManufacturing.Text = ""
    End Sub

    Private Sub cmbStoreManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbStoreManufacturing.SelectedIndexChanged
        If cmbStoreManufacturing.Text.Trim = "" Then Exit Sub
        cmbCatsManufacturing.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select distinct group_name from Items where Stores =N'" & cmbStoreManufacturing.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbCatsManufacturing.Items.Add(Trim(dr(0)))
        Loop
        cmbCatsManufacturing.Text = ""
    End Sub

    Private Sub DTGV_CellValueChanged(sender As Object, e As DataGridViewCellEventArgs) Handles DTGV.CellValueChanged
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.Rows.Count) = 0 Then Beep() : Exit Sub
        Dim sname As String = DTGV.SelectedRows(0).Cells(3).Value
        Dim Stores As String = DTGV.SelectedRows(0).Cells(7).Value
        Dim qunt As String = DTGV.SelectedRows(0).Cells(4).Value

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select store from items where sname=N'" & sname & "' and Stores=N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            QuntAll = dr("store")
        End If

        Dim TotalquntAll As String = Val(QuntAll) - Val(qunt)

    End Sub

    Private Sub cmbUnity_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbUnity.SelectedIndexChanged
        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & TxtPrc.Text & "' and Unity_Name=N'" & cmbUnity.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 0 : End If
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(txtquntUnity.Text)
        End If
    End Sub

    Private Sub txtquntUnity_TextChanged(sender As Object, e As EventArgs) Handles txtquntUnity.TextChanged
        MyVars.CheckNumber(txtquntUnity)
        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If
    End Sub

    Private Sub ItemsTransferBill()

        Dim EMPID As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select EMPID from Employees where  NameEmployee = N'" & cmbEmployees.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then : EMPID = dr("EMPID") : Else EMPID = 0 : End If


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into ManufacturingFillOrder (Company_Branch_ID,bill_no,bill_date,itm_id_Manufacturing,Stores_Manufacturing,Stores_From,Stores_TO,Total_qu,TotalCostPrice,EMPID,UserName)  values("
        S = S & "N'" & Company_Branch_ID & "',N'" & txtbill_no.Text.Trim & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & txtitm_id_Manufacturing.Text & "',N'" & cmbStoreManufacturing.Text & "',N'" & cmbStoresFrom.Text & "',N'" & cmbStoresTo.Text & "',N'" & txtTotal_qu.Text & "',N'" & txtCostPrice.Text & "',N'" & EMPID & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        For i As Integer = 0 To DTGV.Rows.Count - 1
            itm_id = DTGV.Rows(i).Cells(1).Value
            itm_cat = DTGV.Rows(i).Cells(2).Value
            itm_name = DTGV.Rows(i).Cells(3).Value
            Unity = DTGV.Rows(i).Cells(4).Value
            price = DTGV.Rows(i).Cells(5).Value
            qunt = DTGV.Rows(i).Cells(6).Value
            qunt_unity = DTGV.Rows(i).Cells(7).Value
            quntTotal = DTGV.Rows(i).Cells(8).Value
            StoresFrom = DTGV.Rows(i).Cells(9).Value
            StoresTo = DTGV.Rows(i).Cells(10).Value
            totalprice = Val(price) * Val(qunt_unity)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where sname =N'" & itm_name & "' and Stores =N'" & StoresTo & "'" : H = cmd.ExecuteScalar
            If H = 0 Then

                AddGroubs(DTGV.Rows(i).Cells(2).Value)
                AddStores(StoresTo)

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select Stores,itm_id,TinPrice,TinPriceAverage,SalPrice,WholePrice,WholeWholePrice,MinimumSalPrice,rng,group_branch from items where Stores =N'" & StoresFrom & "' and itm_id =N'" & itm_id & "'" : dr = cmd.ExecuteReader : dr.Read()
                Dim store, TinPrice, TinPriceAverage, SalPrice, WholePrice, WholeWholePrice, MinimumSalPrice, rng, group_branch As String
                store = dr(0).ToString() : TinPrice = dr(2).ToString() : TinPriceAverage = dr(3).ToString() : SalPrice = dr(4).ToString() : WholePrice = dr(5).ToString()
                WholeWholePrice = dr(6).ToString() : MinimumSalPrice = dr(7).ToString() : rng = dr(8).ToString() : group_branch = dr(9).ToString()
                '====================================================================================================================

                Dim expired As String = "بدون صلاحية"
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Items (Company_Branch_ID,IDTM,itm_id,group_name,group_branch,sname,Unity,rng,tinprice,salprice,TinPriceAverage,WholePrice,WholeWholePrice,MinimumSalPrice,tin,sal,btin,bsal,decayed,tinpricetotal,salpricetotal,btinpricetotal,bsalpricetotal,decayedpricetotal,store,ValStore,profits,UserName,Stores,QuickSearch) values ("
                S = S & "N'" & Company_Branch_ID & "',N'" & 0 & "',N'" & itm_id & "',N'" & itm_cat & "',N'" & group_branch & "',N'" & itm_name & "',N'" & Unity & "',"
                S = S & "" & Val(rng) & ","
                S = S & "" & Val(TinPrice) & ","
                S = S & "" & Val(SalPrice) & ","
                S = S & "" & Val(TinPrice) & ","
                S = S & "" & Val(WholePrice) & ","
                S = S & "" & Val(WholeWholePrice) & ","
                S = S & "" & Val(MinimumSalPrice) & ","
                S = S & "0,0,0,0,0,0,0,0,0,0,0,0,0,N'" & UserName & "',N'" & StoresTo & "',0)"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
                Dim X As String = "جرد"
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into BilltINData (Company_Branch_ID,bill_no,IDTM,itm_id,itm_cat,group_branch,itm_name,itm_Unity,price,TinPriceAverage,qu,qu_unity,totalprice,UserName,Stores,bill_date,bill_EndDate,Expired,qu_expired)"
                S = S & " values (N'" & Company_Branch_ID & "',N'" & X & "'," & 0 & ",N'" & itm_id & "',N'" & itm_cat & "',N'" & group_branch & "',N'" & itm_name & "',N'" & Unity & "'," & TinPrice & "," & TinPriceAverage & "," & Val(0) & "," & Val(0) & "," & Val(TinPrice) * Val(0) & ",N'" & UserName & "',N'" & StoresTo & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'',N'" & expired & "'," & Val(0) & ")"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If


            'مشتريات تحويل
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Manufacturing_BilltINData (Company_Branch_ID,bill_no,itm_id,itm_Unity,qu,qu_unity,price,totalprice,Stores,bill_date,UserName)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & txtbill_no.Text.Trim & "',N'" & itm_id & "',N'" & Unity & "',N'" & qunt & "',N'" & qunt_unity & "',N'" & price & "',N'" & totalprice & "',N'" & StoresTo & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            'مبيعات تحويل
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Manufacturing_BillsalData (Company_Branch_ID,bill_no,itm_id,itm_Unity,qu,qu_unity,price,totalprice,Stores,bill_date,UserName)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & txtbill_no.Text.Trim & "',N'" & itm_id & "',N'" & Unity & "',N'" & qunt & "',N'" & qunt_unity & "',N'" & price & "',N'" & totalprice & "',N'" & StoresFrom & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            IM.Store(itm_id, StoresFrom)

            If ConnectOnlineStore = "YES" Then
                EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", itm_id)
                StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", itm_id)
                Cos.UpdateProductStock(StockOnline, itm_id, EditItmId)
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Manufacturing_BillsalData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbill_no.Text & "' and itm_id = N'" & itm_id & "' and Stores =N'" & StoresFrom & "'" : cmd.ExecuteNonQuery()

            IM.Store(itm_id, StoresTo)

            If ConnectOnlineStore = "YES" Then
                EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", itm_id)
                StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", itm_id)
                Cos.UpdateProductStock(StockOnline, itm_id, EditItmId)
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Manufacturing_BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbill_no.Text & "' and itm_id = N'" & itm_id & "' and Stores =N'" & StoresTo & "'" : cmd.ExecuteNonQuery()


        Next

    End Sub

    Private Sub cmbItemsManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbItemsManufacturing.SelectedIndexChanged
        Dim itm_id_Manufacturing As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id from Items where  sname = N'" & cmbItemsManufacturing.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then : txtitm_id_Manufacturing.Text = dr("itm_id") : Else txtitm_id_Manufacturing.Text = 0 : End If
    End Sub

    Private Sub txtquntUnity_KeyUp(sender As Object, e As KeyEventArgs) Handles txtquntUnity.KeyUp
        If e.KeyCode = 13 Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub cmbStoresFrom_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbStoresFrom.SelectedIndexChanged
        Cls.fill_combo_DataAdapter_Stores_Where_More("Items", "sname", "Stores=N'" & cmbStoresFrom.Text & "' and QuickSearch=0", cmbitmnm)
        ClearDropDown()
    End Sub

    Private Sub ClearDropDown()
        cmbitmnm.Text = ""
        TxtPrc.Text = ""
        txtqunt.Text = ""
        txtquntAll.Text = ""
        txtquntUnity.Text = ""
        cmbUnity.Text = ""
    End Sub
End Class