﻿Imports System.Windows.Forms
Imports System.Data.SqlClient
Imports vb = Microsoft.VisualBasic
Imports System.IO

Public Class frmCompanyBranch
    Inherits System.Windows.Forms.Form

    Dim Memo As New MemoryStream
    Dim WithEvents BS As New BindingSource
    Dim constring As String
    Dim SqlDataAdapter1 As SqlClient.SqlDataAdapter
    Dim ds As DataSet = New DataSet
    Dim MaxRecoedCode As String
    Dim ActionGrid As Boolean = False

    Private Sub frmCompany_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        connectionStringOpen()

        Bra.Fil("Company", "CMPName", cmbCMPName)
        Bra.Fil("Company_Branch", "Company_Branch_Name", cmbFind)
        Bra.Fil("Company_Branch", "Company_Branch_Name", cmbFind)
        MAXRECORD("Company_Branch", "Company_Branch_ID")
        txtCompany_Branch_ID.Text = MaxRecoedCode
        txtCompany_Branch_Name.Focus()
        cmbCMPName.SelectedIndex = 0
    End Sub

    Private Sub MAXRECORD(ByVal Tables As String, ByVal Code As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " & Tables & ""
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            MaxRecoedCode = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" & Code & " As float)) as mb FROM " & Tables & " where " & Code & " <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            MaxRecoedCode = sh + 1
        End If

    End Sub

    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        ClearAll_Company_Branch()
        Bra.Fil("Company_Branch", "Company_Branch_Name", cmbFind)

        MAXRECORD("Company_Branch", "Company_Branch_ID")
        txtCompany_Branch_ID.Text = MaxRecoedCode
        txtCompany_Branch_Name.Focus()
    End Sub

    Private Sub ClearAll_Company_Branch()
        txtCompany_Branch_ID.Text = ""
        txtCompany_Branch_Name.Text = ""
        txtCMPBAddress.Text = ""
        txtCMPBMobile.Text = ""
        txtCMPBTel.Text = ""
        txtCMPBEmail.Text = ""
        txtCMPBFax.Text = ""
        'cmbCMPName.SelectedIndex = -1
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        If Trim(cmbCMPName.Text) = "" Then MsgBox("من فضلك ادخل أسم الشركة", MsgBoxStyle.Exclamation) : cmbCMPName.Focus() : Exit Sub
        If Trim(txtCompany_Branch_ID.Text) = "" Then MsgBox("من فضلك ادخل الكود", MsgBoxStyle.Exclamation) : txtCompany_Branch_ID.Focus() : Exit Sub
        If Trim(txtCompany_Branch_Name.Text) = "" Then MsgBox("من فضلك ادخل الاسم", MsgBoxStyle.Exclamation) : txtCompany_Branch_Name.Focus() : Exit Sub
        If Cls.Check_Field_Value("Company_Branch", "Company_Branch_Name", txtCompany_Branch_Name.Text) Then MsgBox("عفوا الاسم مسجل من قبل", MsgBoxStyle.Exclamation) : Exit Sub
        If Cls.Check_Field_Value("Company_Branch", "Company_Branch_ID", txtCompany_Branch_ID.Text) Then MsgBox("عفوا الكود مسجل من قبل", MsgBoxStyle.Exclamation) : Exit Sub

        Dim CMPSerial As String = Cls.Get_Code_Value_Branch("Company", "CMPSerial", "CMPName", cmbCMPName.Text)

        Cls.insert("Company_Branch", "Company_Branch_ID,CMPSerial,Company_Branch_Name,CMPBAddress,CMPBTel,CMPBMobile,CMPBFax,CMPBEmail", "N'" & txtCompany_Branch_ID.Text & "',N'" & CMPSerial & "',N'" & txtCompany_Branch_Name.Text & "',N'" & txtCMPBAddress.Text & "',N'" & txtCMPBTel.Text & "',N'" & txtCMPBMobile.Text & "',N'" & txtCMPBFax.Text & "',N'" & txtCMPBEmail.Text & "'")
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)

        ClearAll_Company_Branch()

        Bra.Fil("Company_Branch", "Company_Branch_Name", cmbFind)

        MAXRECORD("Company_Branch", "Company_Branch_ID")
        txtCompany_Branch_ID.Text = MaxRecoedCode
        txtCompany_Branch_Name.Focus()
    End Sub

    Private Sub btnUpdate_Click(sender As Object, e As EventArgs) Handles btnUpdate.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If Trim(cmbCMPName.Text) = "" Then MsgBox("من فضلك ادخل أسم الشركة", MsgBoxStyle.Exclamation) : cmbCMPName.Focus() : Exit Sub
        If Trim(txtCompany_Branch_ID.Text) = "" Then MsgBox("من فضلك ادخل الكود", MsgBoxStyle.Exclamation) : txtCompany_Branch_ID.Focus() : Exit Sub
        If Trim(txtCompany_Branch_Name.Text) = "" Then MsgBox("من فضلك ادخل الاسم", MsgBoxStyle.Exclamation) : txtCompany_Branch_Name.Focus() : Exit Sub

        Dim CMPSerial As String = Cls.Get_Code_Value_Branch("Company", "CMPSerial", "CMPName", cmbCMPName.Text)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Company_Branch set Company_Branch_Name =N'" & txtCompany_Branch_Name.Text & "',CMPSerial =N'" & CMPSerial & "',CMPBAddress =N'" & txtCMPBAddress.Text & "',CMPBTel =N'" & txtCMPBTel.Text & "',CMPBMobile =N'" & txtCMPBMobile.Text & "',CMPBFax =N'" & txtCMPBFax.Text & "',CMPBEmail =N'" & txtCMPBEmail.Text & "' where Company_Branch_Name =N'" & cmbFind.Text & "'" : cmd.ExecuteNonQuery()

        MsgBox("تم التعديل بنجاح", MsgBoxStyle.Information)

        ClearAll_Company_Branch()

        Bra.Fil("Company_Branch", "Company_Branch_Name", cmbFind)

        MAXRECORD("Company_Branch", "Company_Branch_ID")
        txtCompany_Branch_ID.Text = MaxRecoedCode
        txtCompany_Branch_Name.Focus()
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If cmbFind.SelectedIndex = -1 Then MsgBox("من فضلك اختر بيان صحيح", MsgBoxStyle.Exclamation) : Exit Sub

        Dim msg As New MsgBoxResult
        msg = (MsgBox("هل تريد بالفعل الحذف", MsgBoxStyle.Question + MsgBoxStyle.YesNo))
        If msg = MsgBoxResult.Yes Then
            Cls.delete("Company_Branch", "Company_Branch_Name=N'" & cmbFind.Text & "'")
            MsgBox("تم الحذف بنجاح", MsgBoxStyle.Information)
        End If

        ClearAll_Company_Branch()

        Bra.Fil("Company_Branch", "Company_Branch_Name", cmbFind)

        MAXRECORD("Company_Branch", "Company_Branch_ID")
        txtCompany_Branch_ID.Text = MaxRecoedCode
        txtCompany_Branch_Name.Focus()
    End Sub

    Private Sub cmbFind_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbFind.SelectedIndexChanged
        ActionGrid = True
        If cmbFind.Text = "" Then Exit Sub
        Dim Government_Code As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT * from Company_Branch where Company_Branch_Name = N'" & cmbFind.Text & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            Dim Code As String = dr("CMPSerial").ToString()
            txtCompany_Branch_ID.Text = dr("Company_Branch_ID").ToString()
            txtCompany_Branch_Name.Text = dr("Company_Branch_Name").ToString()
            txtCMPBAddress.Text = dr("CMPBAddress").ToString()
            txtCMPBTel.Text = dr("CMPBTel").ToString()
            txtCMPBMobile.Text = dr("CMPBMobile").ToString()
            txtCMPBFax.Text = dr("CMPBFax").ToString()
            txtCMPBEmail.Text = dr("CMPBEmail").ToString()
            cmbCMPName.Text = Cls.Get_Code_Value_Branch("Company", "CMPName", "CMPSerial", Code)
        End If
        ActionGrid = False
    End Sub

End Class