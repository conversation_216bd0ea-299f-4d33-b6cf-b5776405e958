﻿
Imports System.Data.SqlClient
Imports System.IO
Imports SharpCompress.Archives
Imports SharpCompress.Common
Imports SharpCompress.Writer
Imports SharpCompress.Compressor
Imports SharpCompress.Compressor.Rar
Imports System.IO.Compression

Public Class frmBackupRestore
    Dim PathBacupResotr As String
    Dim PathBacupResotrFlashMemory As String

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        sfdBackUp.InitialDirectory = My.Computer.FileSystem.SpecialDirectories.MyDocuments
        sfdBackUp.Filter = "Archivos Bak (*.bak)|Todos los archivos (*.*)"
        If (sfdBackUp.ShowDialog(Me) = System.Windows.Forms.DialogResult.OK) Then
            Dim FileName As String = sfdBackUp.FileName + ".bak"
            txtPathBackUp.Text = FileName
        End If
    End Sub

    Private Sub Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button3.Click
        ofdRestore.InitialDirectory = My.Computer.FileSystem.SpecialDirectories.MyDocuments
        ofdRestore.Filter = "Archivos Bak (*.bak)|*.bak|Todos los archivos (*.*)|*.*"
        If (ofdRestore.ShowDialog(Me) = System.Windows.Forms.DialogResult.OK) Then
            Dim FileName As String = ofdRestore.FileName
            txtPathRestore.Text = FileName

        End If
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        connect()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Company set CMPBackupDatabasePath =N'" & txtPathBackUp.Text & "'" : cmd.ExecuteNonQuery()

        mykey.SetValue("BackupDatabasePath", txtPathBackUp.Text)

        Try
            'RestoreBackup.BackUp(txtPathBackUp.Text, constring)
            SlpitBackupRestorePath(txtPathBackUp.Text)
            connectionStringOpen()
            Cn = New SqlConnection(constring)
            cmd = New SqlCommand("backup database " + DataBaseName + " to disk=N'" & PathBacupResotr & "'", Cn)
            Cn.Open()
            cmd.ExecuteNonQuery()
            Cn.Close()

            MsgBox("تم عمل نسخة إحتياطية بنجاح", MsgBoxStyle.Information)
            connect()
        Catch ex As Exception
            MsgBox(ex, MsgBoxStyle.Information)
        End Try
    End Sub

    Private Sub Button4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button4.Click
        connectionStringClose()
        Dim con As New SqlConnection("server= ./sqlexpress; database = database1; integrated security = true")




        connect()
        mykey.SetValue("RestoreDatabasePath", txtPathRestore.Text)

        Dim x As String = MsgBox("فى حالة اسستعادة البيانات سبتم اغلاقة البرنامج ", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        Try
            'RestoreBackup.Restore(txtPathRestore.Text, constring)
            connectionStringOpen()
            cmd = New SqlCommand("restore database " + DataBaseName + " from disk=N''" & txtPathRestore.Text & "''", Cn)
            Cn.Open()
            cmd.ExecuteNonQuery()
            Cn.Close()

            MsgBox("تم إستعادة البيانات بنجاح", MsgBoxStyle.Information)
            connect()
        Catch ex As Exception
            MsgBox(ex, MsgBoxStyle.Information)
        End Try

        End
    End Sub

    Private Sub TestForm_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        txtPathBackUp.Text = mykey.GetValue("BackupDatabasePath", "NO")
        txtPathBackUpFlashMemory.Text = mykey.GetValue("BackupDatabasePathFlashMemory", "NO")
        txtPathRestore.Text = mykey.GetValue("RestoreDatabasePath", "NO")
        cmbBackUpTimeFlashMemory.Text = mykey.GetValue("BackUpTimeFlashMemory", "NO")
        cmbBackUpTimeFlashMemoryHour.Text = mykey.GetValue("BackUpTimeFlashMemoryHour", "0")

    End Sub
    Private Sub SlpitBackupRestorePath(ByVal Text As String)
        Dim dt As String = Format(Now, "dd-MM-yyyy-hh-mm-tt")
        Dim Slpit As String = Text
        Dim split As String() = New String() {"."}
        Dim itemsSplit As String() = Slpit.Split(split, StringSplitOptions.None)
        Dim Path As String = itemsSplit(0).ToString()
        PathBacupResotr = Path + "-" + dt + ".bak"
    End Sub

    Private Sub SlpitBackupRestorePathFlashMemory(ByVal Text As String)
        Dim dt As String = Format(Now, "dd-MM-yyyy-hh-mm-tt")
        Dim Slpit As String = Text
        Dim split As String() = New String() {"."}
        Dim itemsSplit As String() = Slpit.Split(split, StringSplitOptions.None)
        Dim Path As String = itemsSplit(0).ToString()
        PathBacupResotrFlashMemory = Path + "-" + dt + ".bak"
    End Sub

    Private Sub Button5_Click(sender As Object, e As EventArgs) Handles Button5.Click
        mykey.SetValue("BackUpTimeFlashMemory", cmbBackUpTimeFlashMemory.Text)
        BackUpTimeFlashMemory = cmbBackUpTimeFlashMemory.Text

        mykey.SetValue("BackUpTimeFlashMemoryHour", cmbBackUpTimeFlashMemoryHour.Text)
        BackUpTimeFlashMemoryHour = cmbBackUpTimeFlashMemoryHour.Text

        connect()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Company set CMPBackupDatabasePathFlash =N'" & txtPathBackUpFlashMemory.Text & "'" : cmd.ExecuteNonQuery()

        mykey.SetValue("BackupDatabasePathFlashMemory", txtPathBackUpFlashMemory.Text)
        Try
            'RestoreBackup.BackUp(txtPathBackUp.Text, constring)
            SlpitBackupRestorePathFlashMemory(txtPathBackUpFlashMemory.Text)
            connectionStringOpen()
            Cn = New SqlConnection(constring)
            cmd = New SqlCommand("backup database " + DataBaseName + " to disk=N'" & PathBacupResotrFlashMemory & "'", Cn)
            Cn.Open()
            cmd.ExecuteNonQuery()
            Cn.Close()

            MsgBox("تم عمل نسخة إحتياطية بنجاح", MsgBoxStyle.Information)
            connect()
        Catch ex As Exception
            MsgBox(ex, MsgBoxStyle.Information)
        End Try
    End Sub

    Private Sub Button7_Click(sender As Object, e As EventArgs) Handles Button7.Click
        sfdBackUpFlashMemory.InitialDirectory = My.Computer.FileSystem.SpecialDirectories.MyDocuments
        sfdBackUpFlashMemory.Filter = "Archivos Bak (*.bak)|Todos los archivos (*.*)"
        If (sfdBackUpFlashMemory.ShowDialog(Me) = System.Windows.Forms.DialogResult.OK) Then
            Dim FileName As String = sfdBackUpFlashMemory.FileName + ".bak"
            txtPathBackUpFlashMemory.Text = FileName
        End If
    End Sub

    Private Sub Button6_Click(sender As Object, e As EventArgs) Handles Button6.Click
        'connect()

        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "update Company set CMPBackupDatabasePath =N'" & txtPathBackUp.Text & "'" : cmd.ExecuteNonQuery()

        'mykey.SetValue("BackupDatabasePath", txtPathBackUp.Text)


        '' الكود المعدل للإصدار القديم
        'Try
        '    Dim backupFileName As String = "DB_Backup_" & DateTime.Now.ToString("yyyyMMdd_HHmmss") & ".bak"
        '    Dim compressedFileName As String = "DB_Backup_" & DateTime.Now.ToString("yyyyMMdd_HHmmss") & ".rar"
        '    Dim fullBackupPath As String = Path.Combine(txtPathBackUp.Text, backupFileName)
        '    Dim fullCompressedPath As String = Path.Combine(txtPathBackUp.Text, compressedFileName)

        '    ' إنشاء النسخة الاحتياطية
        '    connectionStringOpen()
        '    Cn = New SqlConnection(constring)
        '    cmd = New SqlCommand("backup database " + DataBaseName + " to disk=N'" & fullBackupPath & "'", Cn)
        '    Cn.Open()
        '    cmd.ExecuteNonQuery()
        '    Cn.Close()

        '    ' ضغط الملف باستخدام الإصدار القديم من SharpCompress
        '    Using archive As IArchive = ArchiveFactory.Create(ArchiveType.Rar)
        '        archive.AddEntry(backupFileName, fullBackupPath)
        '        archive.SaveTo(fullCompressedPath, CompressionType.Rar)
        '    End Using

        '    File.Delete(fullBackupPath)
        '    MsgBox("تم عمل نسخة إحتياطية مضغوطة بنجاح", MsgBoxStyle.Information)
        'Catch ex As Exception
        '    MsgBox(ex.Message, MsgBoxStyle.Critical)
        'End Try
    End Sub
End Class
