﻿Imports System.Data.OleDb

Public Class FrmItemsNewExcel_Bills
    Dim IDTM As String
    Dim Parcode As String

    Private Sub btnImport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnImport.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        ImportExcel()
    End Sub

    Private Sub ImportExcel()
        DTGV.Columns.Clear()
        Dim MyFileDialog As New OpenFileDialog()
        'Dim Xsheet As String = txtSheetName.Text
        With MyFileDialog
            .Filter = "Excel files|*xlsx;*xls"
            .Title = "Open File"
            .ShowDialog()
        End With

        If MyFileDialog.FileName.ToString <> "" Then
            Dim ExcelFile As String = MyFileDialog.FileName.ToString
            Dim ds As New DataSet
            Dim da As OleDbDataAdapter
            Dim dt As DataTable
            Dim conn As OleDbConnection
            conn = New OleDbConnection(
                       "Provider=Microsoft.Jet.OLEDB.4.0;" &
                       "Data Source= " & ExcelFile & ";" &
                       "Extended Properties=Excel 8.0;")
            Try

                da = New OleDbDataAdapter("select * from [Sales_Bill$]", conn)
                conn.Open()
                da.Fill(ds, "Sales_Bill")
                dt = ds.Tables("Sales_Bill")

            Catch ex As Exception
                MsgBox(ex.Message)
                conn.Close()
            End Try
            Try
                DTGV.DataSource = ds
                DTGV.DataMember = "Sales_Bill"
            Catch ex As Exception
                MsgBox(ex.Message)
            End Try

        End If
    End Sub

    Private Sub ImportExcel_Data()
        DTGVData.Columns.Clear()
        Dim MyFileDialog As New OpenFileDialog()
        'Dim Xsheet As String = txtSheetName.Text
        With MyFileDialog
            .Filter = "Excel files|*xlsx;*xls"
            .Title = "Open File"
            .ShowDialog()
        End With

        If MyFileDialog.FileName.ToString <> "" Then
            Dim ExcelFile As String = MyFileDialog.FileName.ToString
            Dim ds As New DataSet
            Dim da As OleDbDataAdapter
            Dim dt As DataTable
            Dim conn As OleDbConnection
            conn = New OleDbConnection(
                       "Provider=Microsoft.Jet.OLEDB.4.0;" &
                       "Data Source= " & ExcelFile & ";" &
                       "Extended Properties=Excel 8.0;")
            Try

                da = New OleDbDataAdapter("select * from [BillsalData$]", conn)
                conn.Open()
                da.Fill(ds, "BillsalData")
                dt = ds.Tables("BillsalData")

            Catch ex As Exception
                MsgBox(ex.Message)
                conn.Close()
            End Try
            Try
                DTGVData.DataSource = ds
                DTGVData.DataMember = "BillsalData"
            Catch ex As Exception
                MsgBox(ex.Message)
            End Try

        End If
    End Sub

    Private Sub btnSaveDataBase_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSaveDataBase.Click
        GODataBase()
    End Sub

    Private Sub GODataBase()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        connectionStringOpen()

        Dim Xbillno As String = ""
        Dim PriceIncludesVAT As String = ""
        Dim RateVAT As String = ""
        Dim XLoopItem As Double = 0
        Dim XLoopPrc As Double = 0


        For i As Integer = 0 To DTGV.Rows.Count - 1
            Xbillno = DTGV.Rows(i).Cells(3).Value.ToString()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Sales_Bill where bill_no =N'" & Xbillno & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
                XLoopItem = 1
            End If
        Next
        If XLoopItem = 1 Then
            MsgBox(" رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation)
            Exit Sub
        End If


        Dim XXbill_No As String = ""
        Dim XXVendorname As String = ""
        Dim XXbill_date As String = ""
        Dim XXtotalpricebeforedisc As String = ""
        Dim XXdisc As String = ""
        Dim XXtotalpriceafterdisc As String = ""
        Dim XXbey As String = ""
        Dim XXSTAYING As String = ""
        Dim XXstat As String = ""
        Dim XXSalesTax As String = ""
        Dim XXExpensesBill As String = ""
        Dim XXSheft_Number As String = ""
        Dim XXEmpName As String = ""
        Dim XXNotes As String = ""

        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'For i As Integer = 0 To DTGV.Rows.Count - 1
        '    XXbill_No = DTGV.Rows(i).Cells(0).Value.ToString()
        '    XXVendorname = DTGV.Rows(i).Cells(1).Value.ToString()
        '    XXbill_date = DTGV.Rows(i).Cells(2).Value.ToString()
        '    XXtotalpricebeforedisc = DTGV.Rows(i).Cells(3).Value.ToString()
        '    XXdisc = DTGV.Rows(i).Cells(4).Value.ToString()
        '    XXtotalpriceafterdisc = DTGV.Rows(i).Cells(5).Value.ToString()
        '    XXbey = DTGV.Rows(i).Cells(6).Value.ToString()
        '    XXSTAYING = DTGV.Rows(i).Cells(7).Value.ToString()
        '    XXstat = DTGV.Rows(i).Cells(8).Value.ToString()
        '    XXSalesTax = DTGV.Rows(i).Cells(9).Value.ToString()
        '    XXExpensesBill = DTGV.Rows(i).Cells(10).Value.ToString()
        '    XXSheft_Number = DTGV.Rows(i).Cells(11).Value.ToString()
        '    XXEmpName = DTGV.Rows(i).Cells(12).Value.ToString()
        '    XXNotes = DTGV.Rows(i).Cells(13).Value.ToString()

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    S = "insert into Sales_Bill(Company_Branch_ID,bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,bey,STAYING,stat,SalesTax,ExpensesBill,Sheft_Number,EmpName,Notes,UserName,DiscountTax,bill_NoTax,DeliveryService,RateValues,CreditPrevious,DebitPrevious,ValueVAT,Treasury_Code,DiscountsValue,RateDriverDelivery,CloseSheft,BillTimeAmBm,CommercialIndustrialProfitsTax,AutoSeriesVATActive) values ("
        '    S = S & "N'" & Company_Branch_ID & "',N'" & XXbill_No & "' ,N'" & XXVendorname & "' ,N'" & Cls.C_date(XXbill_date) & "' ,N'" & Cls.get_time(True) & "' ," & Val(XXtotalpricebeforedisc.Trim) & "," & Val(XXdisc) & " ," & Val(XXtotalpriceafterdisc.Trim) & " ,N'" & Val(XXbey) & "',N'" & Val(XXSTAYING) & "',N'" & XXstat & "',N'" & Val(XXSalesTax.Trim) & "',N'" & Val(XXExpensesBill) & "',N'" & XXSheft_Number & "',N'" & XXEmpName & "',N'" & XXNotes & "',N'" & UserName & "',N'" & Val(0) & "',N'" & 0 & "',N'" & Val(0) & "',N'" & Val(0) & "',N'" & Val(0) & "',N'" & Val(0) & "',N'" & Val(0) & "',N'" & 0 & "',N'" & 0 & "',N'" & Val(0) & "',0,N'01 :45:57 AM',N'" & 0 & "',N'" & 0 & "')"
        '    cmd.CommandText = S : cmd.ExecuteNonQuery()


        '    Dim bill_EndDate As String = "" : Dim bill_no_Expired As String = ""
        '    If NetworkName = "Yes" Then : If UseExternalServer = "Yes" Then : connect() : End If : End If

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    For M As Integer = 0 To DTGVData.Rows.Count - 1

        '        '    DiscountsTin = Val(Cls.Get_Code_Value_More_ALL_Select("Select Top(100) PERCENT Discounts From dbo.BilltINData Where (itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "') Order By id DESC"))

        '        '    AverageTinPrice(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value, Dgv_Add.Rows(i).Cells(3).Value, Dgv_Add.Rows(i).Cells(5).Value, Dgv_Add.Rows(i).Cells(6).Value, Dgv_Add.Rows(i).Cells(9).Value, Dgv_Add.Rows(i).Cells(20).Value)

        '        '    Vendorname = Cls.Get_Code_Value_More("" & BillsalData & "", "Vendorname", "itm_id =N'" & Dgv_Add.Rows(i).Cells(0).Value & "'")

        '        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        '    cmd.CommandText = "Select Top(100) PERCENT bill_no_Expired, bill_EndDate As Ex_bill_EndDate, qu_expired, itm_id, Expired, Stores, id From dbo.BilltINData  Group By itm_id, Expired, qu_expired, Stores, bill_EndDate, id, bill_no_Expired  HAVING(itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "') AND (Stores = N'" & Dgv_Add.Rows(i).Cells(8).Value & "') AND (qu_expired <> 0)    ORDER BY Ex_bill_EndDate"
        '        '    dr = cmd.ExecuteReader : dr.Read()
        '        '    If dr.HasRows = True Then
        '        '        bill_no_Expired = dr(0).ToString
        '        '        bill_EndDate = dr(1).ToString
        '        '    End If

        '        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        '    S = "insert into " & BillsalData & "(Company_Branch_ID, bill_no, itm_id, itm_cat, itm_name, price, qu, qu_unity, itm_Unity, TotalPrice, Stores, Discounts, itm_Notes, bill_date,billtime, UserName, TinPriceAverage, Profits, EmpName, Sheft_Number, ValueVAT, BeforeVAT, RateVAT, TinPrice, Vendorname, STAT, ResourceName, Treasury_Code, bill_EndDate, bill_no_Expired, Price_Unity,DiscountsValue,Discount_Price_After,StateDisc,DiscountsTin,CloseSheft)  values("
        '        '    S = S & "N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Dgv_Add.Rows(i).Cells(3).Value & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "',N'" & Dgv_Add.Rows(i).Cells(9).Value & "',N'" & Dgv_Add.Rows(i).Cells(10).Value & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Cls.get_time(True) & "',N'" & UserName & "',N'" & TinPriceAverage & "',N'" & Profits & "',N'" & EmployeesName & "',N'" & txtSheft_Number.Text & "',N'" & Dgv_Add.Rows(i).Cells(16).Value & "',N'" & Dgv_Add.Rows(i).Cells(17).Value & "',N'" & Dgv_Add.Rows(i).Cells(18).Value & "',N'" & TinPrice & "',N'" & cmbvendores.Text.Trim & "',N'" & STAT & "',N'" & Vendorname & "',N'" & Treasury_Code_ID & "',N'" & bill_EndDate & "',N'" & bill_no_Expired & "',N'" & Price_Unity & "',N'" & Dgv_Add.Rows(i).Cells(19).Value & "',N'" & Dgv_Add.Rows(i).Cells(20).Value & "',N'" & StateDisc & "',N'" & DiscountsTin & "',0)"
        '        '    cmd.CommandText = S : cmd.ExecuteNonQuery()
        '        '    'End If

        '        '    IM.StoreExpired(Dgv_Add.Rows(i).Cells(0).Value.ToString(), Dgv_Add.Rows(i).Cells(8).Value.ToString(), bill_EndDate.ToString(), bill_no_Expired)

        '        'Next

        '        'If NetworkName = "Yes" Then : If UseExternalServer = "Yes" Then : connect() : End If : End If

        '        'If Val(paying) > 0 Then
        '        '    If ItemAddedInvoiceSavedAutomatically = "YES" Then
        '        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        '        cmd.CommandText = "select billno from " & vst & " where billno=N'" & txtbillno.Text.Trim & "'" : dr = cmd.ExecuteReader : dr.Read()
        '        '        If dr.HasRows = True Then
        '        '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        '            cmd.CommandText = "update " & vst & " set Company_Branch_ID =N'" & Company_Branch_ID & "',Vendorname =N'" & cmbvendores.Text & "',VND_XTM =N'" & Cls.get_time(True) & "',VND_dt =N'" & Cls.C_date(DateTimePicker1.Text) & "',VND_amx =N'" & paying & "',VND_ho =N'بفاتورة',VND_rcv =N'بفاتورة',VND_dec =N'بفاتورة',billno =N'" & txtbillno.Text & "',VND_no =N'دفعة نقدية',UserName =N'" & UserName & "',EmpName =N'" & EmployeesName & "',Treasury_Code =N'" & Treasury_Code_ID & "' where billno =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
        '        '        Else
        '        '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        '            S = "insert into " & vst & "(Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,billno,VND_no,UserName,EmpName,Treasury_Code) values"
        '        '            S = S & " (N'" & Company_Branch_ID & "',N'" & cmbvendores.Text & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(DateTimePicker1.Text) & "'," & paying & ",N'بفاتورة',N'بفاتورة',N'بفاتورة',N'" & txtbillno.Text & "',N'دفعة نقدية',N'" & UserName & "',N'" & EmployeesName & "',N'" & Treasury_Code_ID & "')"
        '        '            cmd.CommandText = S : cmd.ExecuteNonQuery()
        '        '        End If
        '        '    Else
        '        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        '        S = "insert into " & vst & "(Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,billno,VND_no,UserName,EmpName,Treasury_Code) values"
        '        '        S = S & " (N'" & Company_Branch_ID & "',N'" & cmbvendores.Text & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(DateTimePicker1.Text) & "'," & paying & ",N'بفاتورة',N'بفاتورة',N'بفاتورة',N'" & txtbillno.Text & "',N'دفعة نقدية',N'" & UserName & "',N'" & EmployeesName & "',N'" & Treasury_Code_ID & "')"
        '        '        cmd.CommandText = S : cmd.ExecuteNonQuery()
        '        '    End If
        '        'End If

        '        'If Val(txtdisc.Text) > 0 Then
        '        '    If ItemAddedInvoiceSavedAutomatically = "YES" Then
        '        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        '        cmd.CommandText = "select TIN_NO from " & Vst_disc & " where TIN_NO=N'" & txtbillno.Text.Trim & "'" : dr = cmd.ExecuteReader : dr.Read()
        '        '        If dr.HasRows = True Then
        '        '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        '            cmd.CommandText = "update " & Vst_disc & " set Company_Branch_ID =N'" & Company_Branch_ID & "',Vendorname =N'" & cmbvendores.Text & "',amnt =N'" & txtdisc.Text & "',pdate =N'" & Cls.C_date(DateTimePicker1.Text) & "',VND_XTM =N'" & Cls.get_time(True) & "',det =N'خصم على فاتورة مباشرة',TIN_NO =N'" & txtbillno.Text & "',UserName =N'" & UserName & "',Treasury_Code =N'" & Treasury_Code_ID & "' where TIN_NO =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
        '        '        Else
        '        '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        '            S = "insert into " & Vst_disc & " (Company_Branch_ID,Vendorname,amnt,pdate,VND_XTM,det,TIN_NO,UserName,Treasury_Code) values (N'" & Company_Branch_ID & "',N'" & cmbvendores.Text.Trim & "'," & Val(DiscTotal) & ",N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Cls.get_time(True) & "',N'خصم على فاتورة مباشرة',N'" & txtbillno.Text.Trim & "',N'" & UserName & "',N'" & Treasury_Code_ID & "')"
        '        '            cmd.CommandText = S : cmd.ExecuteNonQuery()
        '        '        End If
        '        '    Else
        '        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        '        S = "insert into " & Vst_disc & " (Company_Branch_ID,Vendorname,amnt,pdate,VND_XTM,det,TIN_NO,UserName,Treasury_Code) values (N'" & Company_Branch_ID & "',N'" & cmbvendores.Text.Trim & "'," & Val(DiscTotal) & ",N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Cls.get_time(True) & "',N'خصم على فاتورة مباشرة',N'" & txtbillno.Text.Trim & "',N'" & UserName & "',N'" & Treasury_Code_ID & "')"
        '        '        cmd.CommandText = S : cmd.ExecuteNonQuery()
        '        '    End If
        '        'End If






        '        'If DTGV.Rows(i).Cells(3).Value.ToString <> "" Then
        '        '    AddGroubs(DTGV.Rows(i).Cells(1).Value.ToString)
        '        '    AddStores(DTGV.Rows(i).Cells(12).Value.ToString)
        '        '    RateVAT = DTGV.Rows(i).Cells(13).Value.ToString()

        '        '    If RateVAT = 0 Then
        '        '        PriceIncludesVAT = 0
        '        '    Else
        '        '        PriceIncludesVAT = 1
        '        '    End If
        '        '    MAXRECORDIDTM()
        '        '    If DTGV.Rows(i).Cells(0).Value.ToString = "0" Or DTGV.Rows(i).Cells(0).Value.ToString = "" Then
        '        '        MAXRECORDParcode()
        '        '    Else
        '        '        Parcode = DTGV.Rows(i).Cells(0).Value.ToString
        '        '    End If

        '        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        '    S = "insert into Items (Company_Branch_ID,IDTM,itm_id,group_name,group_branch,sname,Unity,TinPrice,TinPriceAverage,salprice,WholePrice,WholeWholePrice,MinimumSalPrice,rng,store,Stores,UserName,QuickSearch,RateVAT,PriceIncludesVAT) values ("
        '        '    S = S & "N'" & Company_Branch_ID & "',N'" & IDTM & "',N'" & Parcode & "',N'" & DTGV.Rows(i).Cells(1).Value.ToString & "',N'" & DTGV.Rows(i).Cells(2).Value.ToString & "',N'" & DTGV.Rows(i).Cells(3).Value.ToString & "',N'" & DTGV.Rows(i).Cells(4).Value.ToString & "',N'" & Val(DTGV.Rows(i).Cells(5).Value.ToString) & "',N'" & Val(DTGV.Rows(i).Cells(5).Value.ToString) & "',N'" & Val(DTGV.Rows(i).Cells(6).Value.ToString) & "',N'" & DTGV.Rows(i).Cells(7).Value.ToString & "',N'" & DTGV.Rows(i).Cells(8).Value.ToString & "',N'" & DTGV.Rows(i).Cells(9).Value.ToString & "',N'" & DTGV.Rows(i).Cells(10).Value.ToString & "',N'" & DTGV.Rows(i).Cells(11).Value.ToString & "',N'" & DTGV.Rows(i).Cells(12).Value.ToString & "',N'" & UserName & "',0," & RateVAT & "," & PriceIncludesVAT & ")"
        '        '    cmd.CommandText = S : cmd.ExecuteNonQuery()

        '        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        '    S = "insert into BilltINData (Company_Branch_ID,IDTM,bill_no,itm_id,itm_cat,group_branch,itm_name,itm_Unity,price,TinPriceAverage,qu,qu_unity,totalprice,Stores,UserName,bill_date,Expired,qu_expired,Treasury_Code,CurrentStock,RateVAT,PriceIncludesVAT)"
        '        '    S = S & " values (N'" & Company_Branch_ID & "',N'" & IDTM & "',N'" & X & "',N'" & Parcode & "',N'" & DTGV.Rows(i).Cells(1).Value.ToString & "',N'" & DTGV.Rows(i).Cells(2).Value.ToString & "',N'" & DTGV.Rows(i).Cells(3).Value.ToString & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & Val(DTGV.Rows(i).Cells(5).Value.ToString) & "',N'" & Val(DTGV.Rows(i).Cells(5).Value.ToString) & "',N'" & DTGV.Rows(i).Cells(11).Value.ToString & "',N'" & DTGV.Rows(i).Cells(11).Value.ToString & "',N'" & Val(DTGV.Rows(i).Cells(5).Value.ToString) * Val(DTGV.Rows(i).Cells(11).Value.ToString) & "',N'" & DTGV.Rows(i).Cells(12).Value.ToString & "',N'" & UserName & "',N'" & Cls.C_date(dtpDate.Text) & "',N'" & bill_EndDate & "',N'" & DTGV.Rows(i).Cells(11).Value.ToString & "',N'" & Treasury_Code & "',N'" & DTGV.Rows(i).Cells(11).Value.ToString & "'," & RateVAT & "," & PriceIncludesVAT & ")"
        '        '    cmd.CommandText = S : cmd.ExecuteNonQuery()

        '        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        '    S = "insert into ItemsUnity(itm_id,Unity_Name,NumberPieces,TinPriceUnit,SalPriceUnit,DefaultTin,DefaultSale,itm_id_Unity,Company_Branch_ID)  values("
        '        '    S = S & "N'" & Parcode & "',N'" & DTGV.Rows(i).Cells(4).Value.ToString & "',N'1',N'1',N'1',N'1',N'1',N'" & Parcode & "',N'" & Company_Branch_ID & "')"
        '        '    cmd.CommandText = S : cmd.ExecuteNonQuery()

        '        '    IM.Store(Parcode, DTGV.Rows(i).Cells(12).Value)

        '        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        '    cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & X & "' and itm_id = N'" & Parcode & "' and Stores =N'" & DTGV.Rows(i).Cells(12).Value & "'" : cmd.ExecuteNonQuery()

        '        'End If
        '    Next

        'MsgBox("تم حفظ فواتير المبيعات من الاكسل الى قاعدة البيانات", MsgBoxStyle.Information)
        'CloseDB()

    End Sub

    Private Sub GODataBaseTin()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        connectionStringOpen()

        Dim Xbillno As String = ""
        Dim PriceIncludesVAT As String = ""
        Dim RateVAT As String = ""
        Dim XLoopItem As Double = 0
        Dim XLoopPrc As Double = 0


        For i As Integer = 0 To DTGV.Rows.Count - 1
            Xbillno = DTGV.Rows(i).Cells(3).Value.ToString()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Sales_Bill where bill_no =N'" & Xbillno & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
                XLoopItem = 1
            End If
        Next
        If XLoopItem = 1 Then
            MsgBox(" رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation)
            Exit Sub
        End If


        Dim XXbill_No As String = ""
        Dim XXVendorname As String = ""
        Dim XXbill_date As String = ""
        Dim XXtotalpricebeforedisc As String = ""
        Dim XXdisc As String = ""
        Dim XXtotalpriceafterdisc As String = ""
        Dim XXbey As String = ""
        Dim XXSTAYING As String = ""
        Dim XXstat As String = ""
        Dim XXSalesTax As String = ""
        Dim XXExpensesBill As String = ""
        Dim XXSheft_Number As String = ""
        Dim XXEmpName As String = ""
        Dim XXNotes As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            XXbill_No = DTGV.Rows(i).Cells(0).Value.ToString()
            XXVendorname = DTGV.Rows(i).Cells(1).Value.ToString()
            XXbill_date = DTGV.Rows(i).Cells(2).Value.ToString()
            XXtotalpricebeforedisc = DTGV.Rows(i).Cells(3).Value.ToString()
            XXdisc = DTGV.Rows(i).Cells(4).Value.ToString()
            XXtotalpriceafterdisc = DTGV.Rows(i).Cells(5).Value.ToString()
            XXbey = DTGV.Rows(i).Cells(6).Value.ToString()
            XXSTAYING = DTGV.Rows(i).Cells(7).Value.ToString()
            XXstat = DTGV.Rows(i).Cells(8).Value.ToString()
            XXSalesTax = DTGV.Rows(i).Cells(9).Value.ToString()
            XXExpensesBill = DTGV.Rows(i).Cells(10).Value.ToString()
            XXSheft_Number = DTGV.Rows(i).Cells(11).Value.ToString()
            XXEmpName = DTGV.Rows(i).Cells(12).Value.ToString()
            XXNotes = DTGV.Rows(i).Cells(13).Value.ToString()


            '            Dim STAT As String
            '            If chkStates.Checked = True Then
            '                STAT = "نقداً"
            '            Else
            '                STAT = "آجل"
            '            End If
            '            Dim Status As String
            '            If ChkVal.Checked = True Then
            '                Status = "قيمة"
            '            Else
            '                Status = "نسبة"
            '            End If

            '            If ChkCent.Checked = True Then
            '                DiscTotal = Val(txttotalpeforedisc.Text) - Val(txttotalafterdisc.Text)
            '                DiscTotal = Math.Round(DiscTotal, 1)
            '            Else
            '                DiscTotal = txtdisc.Text
            '            End If

            '            Dim DeferredCurrentDiscount As Double = 0
            '            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            '                If Val(Dgv_Add.Rows(i).Cells(22).Value) = 1 Then
            '                    DeferredCurrentDiscount = 1
            '                    GoTo DCD
            '                End If
            '            Next
            'DCD:

            '            Dim purchase_bill As String = ""
            '            Dim BilltINData As String = ""
            '            Dim vnd As String = ""
            '            Dim vndr_disc As String = ""

            '            GetDebtorlCreditorPrevious()

            '            If rdoDismissalNotice.Checked = True Then
            '                purchase_bill = "purchase_bill" : BilltINData = "BilltINData" : vnd = "vnd" : vndr_disc = "vndr_disc"
            '            End If
            '            If rdoReceivingPermission.Checked = True Then
            '                purchase_bill = "Receive_Purchase_bill" : BilltINData = "Receive_BilltINData" : vnd = "Receive_Vnd" : vndr_disc = "Receive_vndr_disc"
            '            End If

            '            Dim TotalTax As Double = Format(Val(txttotalpeforedisc.Text) * Val(txtPurchaseTax.Text) / 100, "Fixed")

            '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '            S = "insert into " & purchase_bill & "(Company_Branch_ID,bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,UserName,Status,Currency_Name,PurchaseTax,PurchaseTaxValue,ValueVAT,RateValues,Treasury_Code,DiscountsValue,CreditPrevious,DebitPrevious,DeferredCurrentDiscount,Notes) values ("
            '            S = S & "N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "' ,N'" & cmbvendores.Text.Trim & "' ,N'" & Cls.C_date(DateTimePicker1.Text) & "' ,N'" & Cls.get_time(True) & "' ," & Val(txttotalpeforedisc.Text.Trim) & "," & Val(DiscTotal) & " ," & Val(txttotalafterdisc.Text.Trim) & " ,N'" & STAT & "',N'" & txtpaying.Text.Trim & "',N'" & txtstaying.Text.Trim & "',N'" & UserName & "',N'" & Status & "',N'" & cmbCurrency.Text & "',N'" & txtPurchaseTax.Text & "',N'" & TotalTax & "',N'" & Val(txtTotalValueVAT.Text) & "',N'" & Val(txtPurchaseTax.Text) & "',N'" & Treasury_Code_ID & "',N'" & TotalDiscountsValue & "',N'" & Val(AmntcreditPrevious) & "',N'" & Val(AmntdebitPrevious) & "',N'" & Val(DeferredCurrentDiscount) & "',N'" & txtNotes.Text & "')"
            '            cmd.CommandText = S : cmd.ExecuteNonQuery()

            '            For i As Integer = 0 To Dgv_Add.Rows.Count - 1

            '                PriceTinAverage(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value, Dgv_Add.Rows(i).Cells(6).Value, Dgv_Add.Rows(i).Cells(5).Value, Dgv_Add.Rows(i).Cells(7).Value, Dgv_Add.Rows(i).Cells(3).Value)

            '                'Dgv_Add.DataSource = Fn_AddBill(txtprc.Text, cmbcats.Text, cmbItemName.Text, Val(txtprice.Text), Val(txtqunt.Text) + Val(Indexqunt), Val(txtquntUnity.Text) + Val(Indexquntunity), cmbUnity.Text, Val(XTotalTotal), cmbStores.Text, Val(txt_priseSal.Text), Val(txtWholePrice.Text), Cls.C_date(dtpExpiration.Text), cmb_Expired.Text, Val(txtdiscBill.Text), StateDisc, Val(txtrng.Text), Cls.C_date(dtpProductionDate.Text), UnitySize, TotalValueVAT, ItemsRateVAT, lblDiscount_Price_After.Text, DiscountsValue, DeferredCurrentDiscount, Val(txtWholeWholePrice.Text), Val(txtMinimumSalPrice.Text))

            '                Dim bill_no_Expired As String = MaxRecordTables("BilltINData", "bill_no_Expired")

            '                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '                S = "insert into " & BilltINData & " (Company_Branch_ID,bill_no,itm_id,itm_cat,itm_name,price,qu,qu_unity,itm_Unity,totalprice,Stores,bill_EndDate,Expired,Discounts,StateDisc,username,bill_date,TinPriceAverage,bill_ProductionDate,ValueVAT,Vendorname,qu_expired,Treasury_Code,bill_no_Expired,Price_Unity,RateVAT,Discount_Price_After,DiscountsValue,DeferredCurrentDiscount,totalpriceNet)  values (N'" & Company_Branch_ID & "',N'" & txtbillno.Text.Trim & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Dgv_Add.Rows(i).Cells(3).Value & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Dgv_Add.Rows(i).Cells(5).Value & "',N'" & Dgv_Add.Rows(i).Cells(6).Value & "',N'" & Dgv_Add.Rows(i).Cells(7).Value & "',N'" & Dgv_Add.Rows(i).Cells(8).Value & "',N'" & Dgv_Add.Rows(i).Cells(11).Value & "',N'" & Dgv_Add.Rows(i).Cells(12).Value & "',N'" & Dgv_Add.Rows(i).Cells(13).Value & "',N'" & Dgv_Add.Rows(i).Cells(14).Value & "',N'" & UserName & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & TotalPriceBeforeAverage & "',N'" & Dgv_Add.Rows(i).Cells(16).Value & "',N'" & Dgv_Add.Rows(i).Cells(18).Value & "',N'" & cmbvendores.Text.Trim & "',N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & Treasury_Code_ID & "',N'" & bill_no_Expired & "',N'" & Price_Unity & "',N'" & Dgv_Add.Rows(i).Cells(19).Value & "',N'" & Dgv_Add.Rows(i).Cells(20).Value & "',N'" & Dgv_Add.Rows(i).Cells(21).Value & "',N'" & Dgv_Add.Rows(i).Cells(22).Value & "',N'" & Dgv_Add.Rows(i).Cells(25).Value & "')"
            '                cmd.CommandText = S : cmd.ExecuteNonQuery()

            '                IM.StoreExpired(Dgv_Add.Rows(i).Cells(0).Value.ToString(), Dgv_Add.Rows(i).Cells(8).Value.ToString(), Dgv_Add.Rows(i).Cells(11).Value.ToString(), bill_no_Expired)

            '            Next

            '            If Val(txtpaying.Text) > 0 Then
            '                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '                S = "insert into " & vnd & " (Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,billno,VND_no,UserName,Treasury_Code) values"
            '                S = S & " (N'" & Company_Branch_ID & "',N'" & cmbvendores.Text & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(DateTimePicker1.Text) & "'," & txtpaying.Text.Trim & ",N'بفاتورة',N'بفاتورة',N'بفاتورة',N'" & txtbillno.Text & "',N'دفعة نقدية',N'" & UserName & "',N'" & Treasury_Code_ID & "')"
            '                cmd.CommandText = S : cmd.ExecuteNonQuery()
            '            End If
            '            If Val(txtdisc.Text) > 0 Then
            '                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '                S = "insert into " & vndr_disc & " (Company_Branch_ID,Vendorname,amnt,pdate,det,TIN_NO,UserName,Treasury_Code) values (N'" & Company_Branch_ID & "',N'" & cmbvendores.Text.Trim & "'," & Val(DiscTotal) & ",N'" & Cls.C_date(DateTimePicker1.Text) & "',N'خصم على فاتورة مباشرة',N'" & txtbillno.Text.Trim & "',N'" & UserName & "',N'" & Treasury_Code_ID & "')"
            '                cmd.CommandText = S : cmd.ExecuteNonQuery()
            '            End If

            '            ImageUpdateBill()

            '            UpdatePriceItems()

            '            '===============================================================================
            '            Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
            '            If UseAccounts = "YES" Then
            '                Daily_Restrictions()
            '            End If
            '            '===============================================================================

            '            Get_Movement_In_Out_Money(DateTimePicker1.Text, Val(Treasury_Code))

            '            If ChkCent.Checked = True Then
            '                hintaName = "نسبة"
            '            End If

            '            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            '                IM.Store(Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(8).Value)

            '                If ConnectOnlineStore = "YES" Then
            '                    StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", Dgv_Add.Rows(i).Cells(0).Value)
            '                    Cos.UpdateProductStock(StockOnline, Dgv_Add.Rows(i).Cells(0).Value, Dgv_Add.Rows(i).Cells(2).Value)
            '                End If
            '                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '                cmd.CommandText = "update " & BilltINData & " set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbillno.Text & "' and itm_id = N'" & Dgv_Add.Rows(i).Cells(0).Value & "' and Stores =N'" & Dgv_Add.Rows(i).Cells(8).Value & "'" : cmd.ExecuteNonQuery()
            '            Next

            '            IM.VendorAccountTotal(cmbvendores.Text)

            '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '            cmd.CommandText = "update " & purchase_bill & " set CreditCurrent = N'" & Amntcredit & "',DebitCurrent = N'" & Amntdebit & "' where bill_No =N'" & txtbillno.Text.Trim & "'" : cmd.ExecuteNonQuery()

            '            'Catch ex As Exception
            '            '    'ErrorHandling(ex, Me.Text)
            '            'End Try

            '            GetDebtorlCreditor()

            '            'Amntcredit = Amntcredit - Amntdebit
            '            'AmntcreditPrevious = AmntcreditPrevious - AmntdebitPrevious

            '            Try
            '                Dim CurrentBalanceCustVnd As String = GetCurrentBalanceCustVnd(cmbvendores.Text)

            '                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '                cmd.CommandText = "update purchase_bill set CreditPrevious = " & Val(AmntcreditPrevious) & ",DebitPrevious = " & Val(AmntdebitPrevious) & ",CreditCurrent = " & Val(Amntcredit) & ",DebitCurrent = " & Val(Amntdebit) & ",CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where bill_No =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
            '                If Val(txtpaying.Text) > 0 Then
            '                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '                    cmd.CommandText = "update Vnd set CreditPrevious = " & Val(AmntcreditPrevious) & ",DebitPrevious = " & Val(AmntdebitPrevious) & ",CreditCurrent = " & Val(Amntcredit) & ",DebitCurrent = " & Val(Amntdebit) & ",CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where BillNo =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
            '                End If
            '                If Val(txtdisc.Text) > 0 Then
            '                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '                    cmd.CommandText = "update vndr_disc set CreditPrevious = " & Val(AmntcreditPrevious) & ",DebitPrevious = " & Val(AmntdebitPrevious) & ",CreditCurrent = " & Val(Amntcredit) & ",DebitCurrent = " & Val(Amntdebit) & ",CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where TIN_NO =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
            '                End If

            '            Catch ex As Exception
            '                ErrorHandling(ex, Me.Text)
            '            End Try

        Next

        'MsgBox("تم حفظ فواتير المشتريات من الاكسل الى قاعدة البيانات", MsgBoxStyle.Information)
        CloseDB()

    End Sub

    Sub AddStores(ByVal Name As String)
        REM للتاكد من عدم التكرار
        Dim TableName, FieldName, StringFind As String
        Dim S As String
        TableName = "stores"
        FieldName = "store"
        StringFind = Name
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select * From " & TableName & " Where " & FieldName & "=N'" & StringFind & "'"

        cmd.CommandType = CommandType.Text
        cmd.CommandText = S
        dr = cmd.ExecuteReader
        'dr.Read()
        If dr.HasRows = False Then
            Cls.Get_Value_Count_More("Stores", "StausMainStore =N'0'")
            Dim StausMainStore As Integer
            If H = 0 Then
                StausMainStore = 0
            Else
                StausMainStore = 1
            End If

            REM لحفظ المخزن
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Stores(Company_Branch_ID,store,UserName,StausMainStore) values (N'" & Company_Branch_ID & "',N'" & StringFind & "',N'" & UserName & "',N'" & StausMainStore & "')"
            cmd.CommandText = S
            cmd.ExecuteNonQuery()
        End If
    End Sub

    Sub AddGroubs(ByVal Name As String)
        REM للتاكد من عدم التكرار
        Dim TableName, FieldName, StringFind As String
        Dim S As String
        TableName = "groups"
        FieldName = "g_name"
        StringFind = Name
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select * From " & TableName & " Where " & FieldName & "=N'" & StringFind & "'"

        cmd.CommandType = CommandType.Text
        cmd.CommandText = S
        dr = cmd.ExecuteReader
        'dr.Read()
        If dr.HasRows = False Then
            REM لحفظ المجموعه
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into groups(Company_Branch_ID,g_name,UserName) values (N'" & Company_Branch_ID & "',N'" & StringFind & "',N'" & UserName & "')"
            cmd.CommandText = S
            cmd.ExecuteNonQuery()
        Else
            dr.Close()
        End If

    End Sub

    Sub AddGroubsCategory_ID(ByVal Name As String, ByVal Category_ID As String)
        REM للتاكد من عدم التكرار
        Dim TableName, FieldName, StringFind As String
        Dim S As String
        TableName = "groups"
        FieldName = "g_name"
        StringFind = Name
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select * From " & TableName & " Where " & FieldName & "=N'" & StringFind & "'"

        cmd.CommandType = CommandType.Text
        cmd.CommandText = S
        dr = cmd.ExecuteReader
        'dr.Read()
        If dr.HasRows = False Then
            REM لحفظ المجموعه
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into groups(Company_Branch_ID,CategoryId,g_name,UserName) values (N'" & Company_Branch_ID & "',N'" & Category_ID & "',N'" & StringFind & "',N'" & UserName & "')"
            cmd.CommandText = S
            cmd.ExecuteNonQuery()
        End If

    End Sub

    Sub AddCompanies(ByVal Code As String, ByVal Name As String, ByVal Category_ID As String)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select * From Companies Where Name=N'" & Name & "'"
        cmd.CommandType = CommandType.Text
        cmd.CommandText = S
        dr = cmd.ExecuteReader
        If dr.HasRows = False Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Companies(Company_Branch_ID,id,Name,IsDelete,CategoryId,UserName) values (N'" & Company_Branch_ID & "',N'" & Code & "',N'" & Name & "',N'False',N'" & Category_ID & "',N'" & UserName & "')"
            cmd.CommandText = S
            cmd.ExecuteNonQuery()
        End If
    End Sub

    Private Sub CloseDB()
        If Cn.State = Data.ConnectionState.Closed Then
            Try
                Cn.Close()
            Catch ex As Exception
                MsgBox(ex.Message)
            End Try
        End If
    End Sub

    Private Sub MAXRECORDIDTM()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select * from BilltINData"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            IDTM = 1000
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(IDTM) as mb FROM BilltINData"
            dr = cmd.ExecuteReader
            dr.Read()
            Dim sh As Integer
            sh = dr("mb")
            IDTM = sh + 1
        End If

    End Sub

    Private Sub MAXRECORDParcode()
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Items"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Parcode = 1000
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(itm_id As float)) as mb FROM Items where itm_id <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            Parcode = sh + 1
        End If
    End Sub

    Private Sub MAXRECORDProductParcode()
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Product"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Parcode = 1000
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(Code As float)) as mb FROM Product where Code <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            Parcode = sh + 1
        End If
    End Sub

    'Private Sub MAXRECORDCompanies()
    '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
    '    cmd.CommandType = CommandType.Text
    '    cmd.CommandText = "select * from Companies"
    '    dr = cmd.ExecuteReader
    '    Dim dt As New DataTable
    '    dt.Load(dr)

    '    If dt.Rows.Count = 0 Then
    '        IDTM = 1000
    '    Else
    '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
    '        cmd.CommandType = CommandType.Text
    '        cmd.CommandText = "SELECT MAX(id) as mb FROM Companies"
    '        dr = cmd.ExecuteReader
    '        dr.Read()
    '        Dim sh As Integer
    '        sh = dr("mb")
    '        IDTM = sh + 1
    '    End If

    'End Sub

    Private Sub Headerx()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If PermtionName = "مدير" Then
            S = "select itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الصنف],Unity as [الوحدة],TinPrice as [سعر الشراء],SalPrice as [سعر البيع],WholePrice as [سعر الجملة],rng as [الحد الأدنى] ,store as [الكمية],Stores as [أسم المخزن],StoreBiggest as [الكمية الاكبر],StoreMedium and [الكمية المتوسطة],StoreSmall as [الكمية الاصغر] from items order by 1"
        Else
            S = "select itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الصنف],Unity as [الوحدة],TinPrice as [سعر الشراء],SalPrice as [سعر البيع],WholePrice as [سعر الجملة],rng as [الحد الأدنى] ,store as [الكمية],Stores as [أسم المخزن],StoreBiggest as [الكمية الاكبر],StoreMedium and [الكمية المتوسطة],StoreSmall as [الكمية الاصغر] from items where Company_Branch_ID =N'" & Company_Branch_ID & "' order by 1"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)
        DTGV.Columns(0).Width = 90
        DTGV.Columns(1).Width = 90
        DTGV.Columns(2).Width = 140
        DTGV.Columns(3).Width = 50
        DTGV.Columns(4).Width = 50
        DTGV.Columns(5).Width = 45
        DTGV.Columns(6).Width = 45
        DTGV.Columns(7).Width = 45
        DTGV.Columns(8).Width = 45
        DTGV.Columns(9).Width = 45
        DTGV.Columns(10).Width = 45
        DTGV.Columns(11).Width = 45
        DTGV.Columns(12).Width = 45
    End Sub

    Private Sub FrmItemsNewExcel_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
    End Sub

    Private Sub GetDataSaveOnlineShopToAccountApp()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        connectionStringOpen()

        Dim XPRC As String = ""
        Dim XName As String = ""
        Dim XStore As String = ""

        Dim PriceIncludesVAT As String = ""
        Dim RateVAT As String = ""
        Dim XLoopItem As Double = 0
        Dim XLoopPrc As Double = 0
        Dim Tag As String = ""
        Dim Category_ID As String = ""
        Dim Category_Name As String = ""
        Dim Company_ID As String = ""
        Dim Company_Name As String = ""
        Dim Product_Name As String = ""
        Dim Description As String = ""
        Dim Unit As String = ""
        Dim TinPrice As String = ""
        Dim Price As String = ""
        Dim DiscountedPrice As String = ""
        Dim Price2 As String = ""
        Dim DiscountedPrice2 As String = ""
        Dim Price3 As String = ""
        Dim DiscountedPrice3 As String = ""
        Dim MinimumSalPrice As String = ""
        Dim Rng As String = ""
        Dim Stock As String = ""
        Dim LimitQuantity As String = ""
        Dim Store_Name As String = ""
        Dim VAT As String = ""


        For i As Integer = 0 To DTGV.Rows.Count - 1
            XPRC = DTGV.Rows(i).Cells(0).Value.ToString()
            XName = DTGV.Rows(i).Cells(6).Value.ToString()
            XStore = DTGV.Rows(i).Cells(20).Value.ToString()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where sname =N'" & XName & "' and Stores =N'" & XStore & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
                XLoopItem = 1
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where itm_id =N'" & XPRC & "' and Stores =N'" & XStore & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
                XLoopPrc = 1
            End If
        Next
        If XLoopItem = 1 Then
            MsgBox(" يوجد صنف مسجل مسبقاً بنفس أسم المخزن", MsgBoxStyle.Exclamation)
            Exit Sub
        End If
        If XLoopItem = 1 Then
            MsgBox("الباركود مسجل مسبقاً", MsgBoxStyle.Exclamation)
            Exit Sub
        End If


        Dim bill_EndDate As String = "بدون صلاحية"
        Dim X As String = "جرد"
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            If DTGV.Rows(i).Cells(8).Value.ToString <> "" Then

                XPRC = DTGV.Rows(i).Cells(0).Value.ToString()
                Tag = DTGV.Rows(i).Cells(1).Value.ToString()
                Category_ID = DTGV.Rows(i).Cells(2).Value.ToString()
                Category_Name = DTGV.Rows(i).Cells(3).Value.ToString()
                Company_ID = DTGV.Rows(i).Cells(4).Value.ToString()
                Company_Name = DTGV.Rows(i).Cells(5).Value.ToString()
                Product_Name = DTGV.Rows(i).Cells(6).Value.ToString()
                Description = DTGV.Rows(i).Cells(7).Value.ToString()
                Unit = DTGV.Rows(i).Cells(8).Value.ToString()
                TinPrice = DTGV.Rows(i).Cells(9).Value.ToString()
                Price = DTGV.Rows(i).Cells(10).Value.ToString()
                DiscountedPrice = DTGV.Rows(i).Cells(11).Value.ToString()
                Price2 = DTGV.Rows(i).Cells(12).Value.ToString()
                DiscountedPrice2 = DTGV.Rows(i).Cells(13).Value.ToString()
                Price3 = DTGV.Rows(i).Cells(14).Value.ToString()
                DiscountedPrice3 = DTGV.Rows(i).Cells(15).Value.ToString()
                MinimumSalPrice = DTGV.Rows(i).Cells(16).Value.ToString()
                Rng = DTGV.Rows(i).Cells(17).Value.ToString()
                Stock = DTGV.Rows(i).Cells(18).Value.ToString()
                LimitQuantity = DTGV.Rows(i).Cells(19).Value.ToString()
                Store_Name = DTGV.Rows(i).Cells(20).Value.ToString()
                RateVAT = DTGV.Rows(i).Cells(21).Value.ToString()

                AddGroubsCategory_ID(Category_Name.ToString, Category_ID)
                AddStores(Store_Name.ToString)
                AddCompanies(Company_ID, Company_Name, Category_ID)

                If RateVAT = 0 Then
                    PriceIncludesVAT = 0
                Else
                    PriceIncludesVAT = 1
                End If
                MAXRECORDIDTM()
                If XPRC.ToString = "0" Or XPRC.ToString = "" Then
                    MAXRECORDParcode()
                Else
                    Parcode = XPRC.ToString
                End If
                If Tag = "" Then
                    Tag = Parcode
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Items (Company_Branch_ID,IDTM,itm_id,Tag,group_name,CompaniesID,CompaniesName,sname,Description,Unity,TinPrice,TinPriceAverage,salprice,DiscountedPrice,WholePrice,DiscountedPrice2,WholeWholePrice,DiscountedPrice3,MinimumSalPrice,rng,store,LimitQuantity,Stores,RateVAT,UserName,QuickSearch,PriceIncludesVAT) values ("
                S = S & "N'" & Company_Branch_ID & "',N'" & IDTM & "',N'" & Parcode & "',N'" & Tag & "',N'" & Category_Name.ToString & "',N'" & Company_ID.ToString & "',N'" & Company_Name.ToString & "',N'" & Product_Name.ToString & "',N'" & Description.ToString & "',N'" & Unit.ToString & "',N'" & TinPrice.ToString & "',N'" & TinPrice.ToString & "',N'" & Price.ToString & "',N'" & DiscountedPrice.ToString & "',N'" & Price2.ToString & "',N'" & DiscountedPrice2.ToString & "',N'" & Price3.ToString & "',N'" & DiscountedPrice3.ToString & "',N'" & MinimumSalPrice.ToString & "',N'" & Rng.ToString & "',N'" & Stock.ToString & "',N'" & LimitQuantity.ToString & "',N'" & Store_Name.ToString & "'," & RateVAT & ",N'" & UserName & "',0," & PriceIncludesVAT & ")"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into BilltINData (Company_Branch_ID,IDTM,bill_no,itm_id,itm_cat,itm_name,itm_Unity,price,TinPriceAverage,qu,qu_unity,totalprice,Stores,UserName,bill_date,Expired,qu_expired,Treasury_Code,CurrentStock,RateVAT,PriceIncludesVAT)"
                S = S & " values (N'" & Company_Branch_ID & "',N'" & IDTM & "',N'" & X & "',N'" & Parcode & "',N'" & Category_Name.ToString & "',N'" & Product_Name.ToString & "',N'" & Unit.ToString & "',N'" & TinPrice & "',N'" & TinPrice.ToString & "',N'" & Stock.ToString & "',N'" & Stock.ToString & "',N'" & Val(TinPrice.ToString) * Val(Stock.ToString) & "',N'" & Store_Name.ToString & "',N'" & UserName & "',N'" & Cls.C_date(dtpDate.Text) & "',N'" & bill_EndDate & "',N'" & Stock.ToString & "',N'" & Treasury_Code & "',N'" & Stock.ToString & "'," & RateVAT & "," & PriceIncludesVAT & ")"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into ItemsUnity(itm_id,Unity_Name,NumberPieces,TinPriceUnit,SalPriceUnit,DefaultTin,DefaultSale,itm_id_Unity,Company_Branch_ID)  values("
                S = S & "N'" & Parcode & "',N'" & Unit.ToString & "',N'1',N'1',N'1',N'1',N'1',N'" & Parcode & "',N'" & Company_Branch_ID & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                IM.Store(Parcode, Store_Name)

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & X & "' and itm_id = N'" & Parcode & "' and Stores =N'" & Store_Name & "'" : cmd.ExecuteNonQuery()

            End If
        Next

        MsgBox("تم حفظ البيانات من الاكسل الى قاعدة البيانات", MsgBoxStyle.Information)
        CloseDB()
    End Sub

    Public Function getProductPrice(ByVal productId As Integer, ByVal userID As Integer) As Decimal
        'Try
        Dim slidesPriceId = 0
        Dim slidesPriceNo = 0
        Dim Price As Decimal = 0

        slidesPriceId = Get_Code_Value_More_where("[User]", "slidesPriceId", "Id =N'" & userID & "' and IsDelete = 'False'")

        slidesPriceNo = Get_Code_Value_More_where("[SlidesPrice]", "SlidesPriceNo", "Id =N'" & slidesPriceId & "' and IsSlidesPrice = 'True' and IsDelete = 'False'")

        Dim query3 = Get_Code_Value_More_where("[Product]", "Name", "Id =N'" & productId & "' and IsDelete = 'False'")

        If query3 IsNot Nothing Then
            If slidesPriceNo = 0 Then
                Price = Convert.ToDecimal(Get_Code_Value_More_where("[Product]", "Price", "Id =N'" & productId & "' and IsDelete = 'False'"))
            End If
            If slidesPriceNo = 1 Then
                Price = Convert.ToDecimal(Get_Code_Value_More_where("[Product]", "Price", "Id =N'" & productId & "' and IsDelete = 'False'"))
            End If
            If slidesPriceNo = 2 Then
                Price = Convert.ToDecimal(Get_Code_Value_More_where("[Product]", "Price2", "Id =N'" & productId & "' and IsDelete = 'False'"))
            End If
            If slidesPriceNo = 3 Then
                Price = Convert.ToDecimal(Get_Code_Value_More_where("[Product]", "Price3", "Id =N'" & productId & "' and IsDelete = 'False'"))
            End If
        Else
            Return 0
        End If

        Return Convert.ToDecimal(Price.ToString())
        'Catch ex As Exception
        '    Return 0
        'End Try

    End Function

    Function Get_Code_Value_More_where(ByVal Table_name As String, ByVal Field_Code As String, ByVal crtria As String)
        'Try
        Dim Code As String = "0"
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select " & Field_Code & " from " & Table_name & " where " & crtria
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Code = dr(0).ToString
        End If
        Return Code
        'Catch ex As Exception
        'End Try
    End Function

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        ImportExcel_Data()
    End Sub

    Private Sub btnSaveDataBaseTIN_Click(sender As Object, e As EventArgs) Handles btnSaveDataBaseTIN.Click
        GODataBaseTin()
    End Sub
End Class