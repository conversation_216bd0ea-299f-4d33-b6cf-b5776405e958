﻿Imports System.Data.OleDb

Public Class FrmItemsNewExcel
    Dim IDTM As String
    Dim Parcode As String

    Private Sub btnImport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnImport.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        ImportExcel()
    End Sub

    Private Sub ImportExcel()
        DTGV.Columns.Clear()
        Dim MyFileDialog As New OpenFileDialog()
        'Dim Xsheet As String = txtSheetName.Text
        With MyFileDialog
            .Filter = "Excel files|*xlsx;*xls"
            .Title = "Open File"
            .ShowDialog()
        End With

        If MyFileDialog.FileName.ToString <> "" Then
            Dim ExcelFile As String = MyFileDialog.FileName.ToString
            Dim ds As New DataSet
            Dim da As OleDbDataAdapter
            Dim dt As DataTable
            Dim conn As OleDbConnection
            conn = New OleDbConnection( _
                       "Provider=Microsoft.Jet.OLEDB.4.0;" & _
                       "Data Source= " & ExcelFile & ";" & _
                       "Extended Properties=Excel 8.0;")
            Try

                da = New OleDbDataAdapter("select * from [Items$]", conn)
                conn.Open()
                da.Fill(ds, "Items")
                dt = ds.Tables("Items")

            Catch ex As Exception
                MsgBox(ex.Message)
                conn.Close()
            End Try
            Try
                DTGV.DataSource = ds
                DTGV.DataMember = "Items"
            Catch ex As Exception
                MsgBox(ex.Message)
            End Try

        End If
    End Sub

    Private Sub btnSaveDataBase_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSaveDataBase.Click
        GODataBase()
    End Sub

    Private Sub GODataBase()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        connectionStringOpen()

        Dim XPRC As String = ""
        Dim XName As String = ""
        Dim XStore As String = ""
        Dim PriceIncludesVAT As String = ""
        Dim RateVAT As String = ""
        Dim XLoopItem As Double = 0
        Dim XLoopPrc As Double = 0

        For i As Integer = 0 To DTGV.Rows.Count - 1
            XPRC = DTGV.Rows(i).Cells(0).Value.ToString()
            XName = DTGV.Rows(i).Cells(3).Value.ToString()
            XStore = DTGV.Rows(i).Cells(12).Value.ToString()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where sname =N'" & XName & "' and Stores =N'" & XStore & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
                XLoopItem = 1
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where itm_id =N'" & XPRC & "' and Stores =N'" & XStore & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
                XLoopPrc = 1
            End If
        Next
        If XLoopItem = 1 Then
            MsgBox(" يوجد صنف مسجل مسبقاً بنفس أسم المخزن", MsgBoxStyle.Exclamation)
            Exit Sub
        End If
        If XLoopItem = 1 Then
            MsgBox("الباركود مسجل مسبقاً", MsgBoxStyle.Exclamation)
            Exit Sub
        End If


        Dim BalanceBarcode As String
        Dim bill_EndDate As String = "بدون صلاحية"
        Dim X As String = "جرد"
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            If DTGV.Rows(i).Cells(3).Value.ToString <> "" Then
                AddGroubs(DTGV.Rows(i).Cells(1).Value.ToString)
                AddStores(DTGV.Rows(i).Cells(12).Value.ToString)
                RateVAT = DTGV.Rows(i).Cells(13).Value.ToString()
                BalanceBarcode = DTGV.Rows(i).Cells(14).Value.ToString()

                If BalanceBarcode = "0" Or BalanceBarcode = "" Then
                    BalanceBarcode = "0"
                Else
                    BalanceBarcode = "1"
                End If

                If RateVAT = 0 Then
                    PriceIncludesVAT = 0
                Else
                    PriceIncludesVAT = 1
                End If
                MAXRECORDIDTM()
                If DTGV.Rows(i).Cells(0).Value.ToString = "0" Or DTGV.Rows(i).Cells(0).Value.ToString = "" Then
                    MAXRECORDParcode()
                Else
                    Parcode = DTGV.Rows(i).Cells(0).Value.ToString
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Items (Company_Branch_ID,IDTM,itm_id,group_name,group_branch,sname,Unity,TinPrice,TinPriceAverage,salprice,WholePrice,WholeWholePrice,MinimumSalPrice,rng,store,Stores,UserName,QuickSearch,RateVAT,PriceIncludesVAT,BalanceBarcode) values ("
                S = S & "N'" & Company_Branch_ID & "',N'" & IDTM & "',N'" & Parcode & "',N'" & DTGV.Rows(i).Cells(1).Value.ToString & "',N'" & DTGV.Rows(i).Cells(2).Value.ToString & "',N'" & DTGV.Rows(i).Cells(3).Value.ToString & "',N'" & DTGV.Rows(i).Cells(4).Value.ToString & "',N'" & Val(DTGV.Rows(i).Cells(5).Value.ToString) & "',N'" & Val(DTGV.Rows(i).Cells(5).Value.ToString) & "',N'" & Val(DTGV.Rows(i).Cells(6).Value.ToString) & "',N'" & DTGV.Rows(i).Cells(7).Value.ToString & "',N'" & DTGV.Rows(i).Cells(8).Value.ToString & "',N'" & DTGV.Rows(i).Cells(9).Value.ToString & "',N'" & DTGV.Rows(i).Cells(10).Value.ToString & "',N'" & DTGV.Rows(i).Cells(11).Value.ToString & "',N'" & DTGV.Rows(i).Cells(12).Value.ToString & "',N'" & UserName & "',0," & RateVAT & "," & PriceIncludesVAT & "," & BalanceBarcode & ")"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into BilltINData (Company_Branch_ID,IDTM,bill_no,itm_id,itm_cat,group_branch,itm_name,itm_Unity,price,TinPriceAverage,qu,qu_unity,totalprice,Stores,UserName,bill_date,Expired,qu_expired,Treasury_Code,CurrentStock,RateVAT,PriceIncludesVAT)"
                S = S & " values (N'" & Company_Branch_ID & "',N'" & IDTM & "',N'" & X & "',N'" & Parcode & "',N'" & DTGV.Rows(i).Cells(1).Value.ToString & "',N'" & DTGV.Rows(i).Cells(2).Value.ToString & "',N'" & DTGV.Rows(i).Cells(3).Value.ToString & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & Val(DTGV.Rows(i).Cells(5).Value.ToString) & "',N'" & Val(DTGV.Rows(i).Cells(5).Value.ToString) & "',N'" & DTGV.Rows(i).Cells(11).Value.ToString & "',N'" & DTGV.Rows(i).Cells(11).Value.ToString & "',N'" & Val(DTGV.Rows(i).Cells(5).Value.ToString) * Val(DTGV.Rows(i).Cells(11).Value.ToString) & "',N'" & DTGV.Rows(i).Cells(12).Value.ToString & "',N'" & UserName & "',N'" & Cls.C_date(dtpDate.Text) & "',N'" & bill_EndDate & "',N'" & DTGV.Rows(i).Cells(11).Value.ToString & "',N'" & Treasury_Code & "',N'" & DTGV.Rows(i).Cells(11).Value.ToString & "'," & RateVAT & "," & PriceIncludesVAT & ")"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into ItemsUnity(itm_id,Unity_Name,NumberPieces,TinPriceUnit,SalPriceUnit,DefaultTin,DefaultSale,itm_id_Unity,Company_Branch_ID)  values("
                S = S & "N'" & Parcode & "',N'" & DTGV.Rows(i).Cells(4).Value.ToString & "',N'1',N'1',N'1',N'1',N'1',N'" & Parcode & "',N'" & Company_Branch_ID & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                IM.Store(Parcode, DTGV.Rows(i).Cells(12).Value)

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & X & "' and itm_id = N'" & Parcode & "' and Stores =N'" & DTGV.Rows(i).Cells(12).Value & "'" : cmd.ExecuteNonQuery()

            End If
        Next

        MsgBox("تم حفظ البيانات من الاكسل الى قاعدة البيانات", MsgBoxStyle.Information)
        CloseDB()
    End Sub

    Private Sub GODataBaseUnits()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        connectionStringOpen()

        Dim XPRC As String = ""
        Dim XName As String = ""
        Dim XStore As String = ""
        Dim XUnity As String = ""
        Dim PriceIncludesVAT As String = ""
        Dim RateVAT As String = ""
        Dim NumberPieces As Double = 0
        Dim UnityName As String = ""
        Dim TinPriceUnit As Double = 0
        Dim SalPriceUnit As Double = 0
        Dim XLoopItem As Double = 0
        Dim XLoopPrc As Double = 0
        Dim UnitySize_Name As String = ""
        Dim Itm_id_Unity As String = ""

        For i As Integer = 0 To DTGV.Rows.Count - 1
            XPRC = DTGV.Rows(i).Cells(0).Value.ToString()
            XName = DTGV.Rows(i).Cells(3).Value.ToString()
            XStore = DTGV.Rows(i).Cells(13).Value.ToString()
            XUnity = DTGV.Rows(i).Cells(4).Value.ToString()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where sname =N'" & XName & "' and Stores =N'" & XStore & "' and Unity =N'" & XUnity & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
                XLoopItem = 1
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where itm_id =N'" & XPRC & "' and Stores =N'" & XStore & "' and Unity =N'" & XUnity & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
                XLoopPrc = 1
            End If
        Next
        If XLoopItem = 1 Then
            MsgBox(" يوجد صنف مسجل مسبقاً بنفس أسم المخزن", MsgBoxStyle.Exclamation)
            Exit Sub
        End If
        If XLoopItem = 1 Then
            MsgBox("الباركود مسجل مسبقاً", MsgBoxStyle.Exclamation)
            Exit Sub
        End If

        Dim BalanceBarcode As String = ""
        Dim bill_EndDate As String = "بدون صلاحية"
        Dim X As String = "جرد"
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            If DTGV.Rows(i).Cells(3).Value.ToString <> "" Then
                AddGroubs(DTGV.Rows(i).Cells(1).Value.ToString)
                AddStores(DTGV.Rows(i).Cells(13).Value.ToString)
                RateVAT = DTGV.Rows(i).Cells(14).Value.ToString()
                XPRC = Val(DTGV.Rows(i).Cells(0).Value.ToString())
                XName = DTGV.Rows(i).Cells(3).Value.ToString()
                XStore = DTGV.Rows(i).Cells(13).Value.ToString()
                NumberPieces = Val(DTGV.Rows(i).Cells(5).Value.ToString())
                UnityName = DTGV.Rows(i).Cells(4).Value.ToString()
                TinPriceUnit = Val(DTGV.Rows(i).Cells(6).Value.ToString())
                SalPriceUnit = Val(DTGV.Rows(i).Cells(7).Value.ToString())

                If BalanceBarcode = "0" Or BalanceBarcode = "" Then
                    BalanceBarcode = "0"
                Else
                    BalanceBarcode = "1"
                End If

                If RateVAT = 0 Then
                    PriceIncludesVAT = 0
                Else
                    PriceIncludesVAT = 1
                End If
                MAXRECORDIDTM()
                If DTGV.Rows(i).Cells(0).Value.ToString = "0" Or DTGV.Rows(i).Cells(0).Value.ToString = "" Then
                    MAXRECORDParcode()
                Else
                    Parcode = DTGV.Rows(i).Cells(0).Value.ToString
                End If

                If NumberPieces = 1 Then
                    UnitySize_Name = "الاصغر"
                    Itm_id_Unity = Parcode

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    S = "insert into Items (Company_Branch_ID,IDTM,itm_id,group_name,group_branch,sname,Unity,TinPrice,TinPriceAverage,salprice,WholePrice,WholeWholePrice,MinimumSalPrice,rng,store,Stores,UserName,QuickSearch,RateVAT,PriceIncludesVAT,BalanceBarcode) values ("
                    S = S & "N'" & Company_Branch_ID & "',N'" & IDTM & "',N'" & Parcode & "',N'" & DTGV.Rows(i).Cells(1).Value.ToString & "',N'" & DTGV.Rows(i).Cells(2).Value.ToString & "',N'" & DTGV.Rows(i).Cells(3).Value.ToString & "',N'" & DTGV.Rows(i).Cells(4).Value.ToString & "',N'" & DTGV.Rows(i).Cells(6).Value.ToString & "',N'" & DTGV.Rows(i).Cells(7).Value.ToString & "',N'" & DTGV.Rows(i).Cells(7).Value.ToString & "',N'" & DTGV.Rows(i).Cells(8).Value.ToString & "',N'" & DTGV.Rows(i).Cells(9).Value.ToString & "',N'" & DTGV.Rows(i).Cells(10).Value.ToString & "',N'" & DTGV.Rows(i).Cells(11).Value.ToString & "',N'" & DTGV.Rows(i).Cells(12).Value.ToString & "',N'" & DTGV.Rows(i).Cells(13).Value.ToString & "',N'" & UserName & "',0," & RateVAT & "," & PriceIncludesVAT & "," & BalanceBarcode & ")"
                    cmd.CommandText = S : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    S = "insert into BilltINData (Company_Branch_ID,IDTM,bill_no,itm_id,itm_cat,group_branch,itm_name,itm_Unity,price,TinPriceAverage,qu,qu_unity,totalprice,Stores,UserName,bill_date,Expired,qu_expired,Treasury_Code,CurrentStock,RateVAT,PriceIncludesVAT)"
                    S = S & " values (N'" & Company_Branch_ID & "',N'" & IDTM & "',N'" & X & "',N'" & Parcode & "',N'" & DTGV.Rows(i).Cells(1).Value.ToString & "',N'" & DTGV.Rows(i).Cells(2).Value.ToString & "',N'" & DTGV.Rows(i).Cells(3).Value.ToString & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(6).Value.ToString & "',N'" & DTGV.Rows(i).Cells(6).Value.ToString & "',N'" & DTGV.Rows(i).Cells(12).Value.ToString & "',N'" & DTGV.Rows(i).Cells(12).Value.ToString & "',N'" & Val(DTGV.Rows(i).Cells(6).Value.ToString) * Val(DTGV.Rows(i).Cells(12).Value.ToString) & "',N'" & DTGV.Rows(i).Cells(13).Value.ToString & "',N'" & UserName & "',N'" & Cls.C_date(dtpDate.Text) & "',N'" & bill_EndDate & "',N'" & DTGV.Rows(i).Cells(11).Value.ToString & "',N'" & Treasury_Code & "',N'" & DTGV.Rows(i).Cells(12).Value.ToString & "'," & RateVAT & "," & PriceIncludesVAT & ")"
                    cmd.CommandText = S : cmd.ExecuteNonQuery()

                Else
                    UnitySize_Name = "الاكبر"
                    Itm_id_Unity = Parcode & 1
                End If

                Dim UnitySize_ID As Double
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select UnitySize_ID from ItemsUnitySize where UnitySize_Name=N'" & UnitySize_Name & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    UnitySize_ID = dr("UnitySize_ID").ToString
                End If
                If UnitySize_ID = 1 Then
                    UnityName = UnityName
                End If
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into ItemsUnity(itm_id,Unity_Name,NumberPieces,TinPriceUnit,SalPriceUnit,DefaultTin,DefaultSale,UnitySize_ID,itm_id_Unity,Company_Branch_ID)  values("
                S = S & "N'" & Parcode & "',N'" & UnityName & "',N'" & NumberPieces & "',N'" & TinPriceUnit & "',N'" & SalPriceUnit & "',N'" & 0 & "',N'" & 0 & "',N'" & UnitySize_ID & "',N'" & Itm_id_Unity & "',N'" & Company_Branch_ID & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()


                IM.Store(Parcode, DTGV.Rows(i).Cells(13).Value)

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & X & "' and itm_id = N'" & Parcode & "' and Stores =N'" & DTGV.Rows(i).Cells(13).Value & "'" : cmd.ExecuteNonQuery()

            End If
        Next

        MsgBox("تم حفظ البيانات من الاكسل الى قاعدة البيانات", MsgBoxStyle.Information)
        CloseDB()
    End Sub

    Private Sub GetItemsUnity(ByVal prc As String, ByVal tinprice As String, ByVal priseSal As String, ByVal UnityName As String, ByVal numberPieces As String, ByVal UnitySize_Name As String, ByVal itm_id_Unity As String)
        'Try

        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub


    Private Sub GODataBaseAccountsTree()
        connectionStringOpen()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into AccountsTree (ACCNumber,ACCName,ACCMain,ACCGroub,ACCType,ACCDebtor,ACCCreditor,ACCNotes,UserName) values ("
            S = S & "N'" & DTGV.Rows(i).Cells(0).Value & "',N'" & DTGV.Rows(i).Cells(1).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(3).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(5).Value & "',N'" & DTGV.Rows(i).Cells(6).Value & "',N'" & DTGV.Rows(i).Cells(7).Value & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        MsgBox("تم حفظ البيانات من الاكسل الى قاعدة البيانات", MsgBoxStyle.Information)
        'Headerx()
        CloseDB()
    End Sub

    Sub AddStores(ByVal Name As String)
        REM للتاكد من عدم التكرار
        Dim TableName, FieldName, StringFind As String
        Dim S As String
        TableName = "stores"
        FieldName = "store"
        StringFind = Name
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select * From " & TableName & " Where " & FieldName & "=N'" & StringFind & "'"

        cmd.CommandType = CommandType.Text
        cmd.CommandText = S
        dr = cmd.ExecuteReader
        'dr.Read()
        If dr.HasRows = False Then
            Cls.Get_Value_Count_More("Stores", "StausMainStore =N'0'")
            Dim StausMainStore As Integer
            If H = 0 Then
                StausMainStore = 0
            Else
                StausMainStore = 1
            End If

            REM لحفظ المخزن
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Stores(Company_Branch_ID,store,UserName,StausMainStore) values (N'" & Company_Branch_ID & "',N'" & StringFind & "',N'" & UserName & "',N'" & StausMainStore & "')"
            cmd.CommandText = S
            cmd.ExecuteNonQuery()
        End If
    End Sub

    Sub AddGroubs(ByVal Name As String)
        REM للتاكد من عدم التكرار
        Dim TableName, FieldName, StringFind As String
        Dim S As String
        TableName = "groups"
        FieldName = "g_name"
        StringFind = Name
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select * From " & TableName & " Where " & FieldName & "=N'" & StringFind & "'"

        cmd.CommandType = CommandType.Text
        cmd.CommandText = S
        dr = cmd.ExecuteReader
        'dr.Read()
        If dr.HasRows = False Then
            REM لحفظ المجموعه
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into groups(Company_Branch_ID,g_name,UserName) values (N'" & Company_Branch_ID & "',N'" & StringFind & "',N'" & UserName & "')"
            cmd.CommandText = S
            cmd.ExecuteNonQuery()
        Else
            dr.Close()
        End If

    End Sub

    Sub AddGroubsCategory_ID(ByVal Name As String, ByVal Category_ID As String)
        REM للتاكد من عدم التكرار
        Dim TableName, FieldName, StringFind As String
        Dim S As String
        TableName = "groups"
        FieldName = "g_name"
        StringFind = Name
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select * From " & TableName & " Where " & FieldName & "=N'" & StringFind & "'"

        cmd.CommandType = CommandType.Text
        cmd.CommandText = S
        dr = cmd.ExecuteReader
        'dr.Read()
        If dr.HasRows = False Then
            REM لحفظ المجموعه
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into groups(Company_Branch_ID,CategoryId,g_name,UserName) values (N'" & Company_Branch_ID & "',N'" & Category_ID & "',N'" & StringFind & "',N'" & UserName & "')"
            cmd.CommandText = S
            cmd.ExecuteNonQuery()
        End If

    End Sub

    Sub AddCompanies(ByVal Code As String, ByVal Name As String, ByVal Category_ID As String)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select * From Companies Where Name=N'" & Name & "'"
        cmd.CommandType = CommandType.Text
        cmd.CommandText = S
        dr = cmd.ExecuteReader
        If dr.HasRows = False Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Companies(Company_Branch_ID,id,Name,IsDelete,CategoryId,UserName) values (N'" & Company_Branch_ID & "',N'" & Code & "',N'" & Name & "',N'False',N'" & Category_ID & "',N'" & UserName & "')"
            cmd.CommandText = S
            cmd.ExecuteNonQuery()
        End If
    End Sub

    Private Sub CloseDB()
        If Cn.State = Data.ConnectionState.Closed Then
            Try
                Cn.Close()
            Catch ex As Exception
                MsgBox(ex.Message)
            End Try
        End If
    End Sub

    Private Sub MAXRECORDIDTM()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select * from BilltINData"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            IDTM = 1000
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(IDTM) as mb FROM BilltINData"
            dr = cmd.ExecuteReader
            dr.Read()
            Dim sh As Integer
            sh = dr("mb")
            IDTM = sh + 1
        End If

    End Sub

    Private Sub MAXRECORDParcode()
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Items"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Parcode = 1000
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(itm_id As float)) as mb FROM Items where itm_id <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            Parcode = sh + 1
        End If
    End Sub

    Private Sub MAXRECORDProductParcode()
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Product"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Parcode = 1000
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(Code As float)) as mb FROM Product where Code <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            Parcode = sh + 1
        End If
    End Sub

    'Private Sub MAXRECORDCompanies()
    '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
    '    cmd.CommandType = CommandType.Text
    '    cmd.CommandText = "select * from Companies"
    '    dr = cmd.ExecuteReader
    '    Dim dt As New DataTable
    '    dt.Load(dr)

    '    If dt.Rows.Count = 0 Then
    '        IDTM = 1000
    '    Else
    '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
    '        cmd.CommandType = CommandType.Text
    '        cmd.CommandText = "SELECT MAX(id) as mb FROM Companies"
    '        dr = cmd.ExecuteReader
    '        dr.Read()
    '        Dim sh As Integer
    '        sh = dr("mb")
    '        IDTM = sh + 1
    '    End If

    'End Sub

    Private Sub Headerx()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If PermtionName = "مدير" Then
            S = "select itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الصنف],Unity as [الوحدة],TinPrice as [سعر الشراء],SalPrice as [سعر البيع],WholePrice as [سعر الجملة],rng as [الحد الأدنى] ,store as [الكمية],Stores as [أسم المخزن],StoreBiggest as [الكمية الاكبر],StoreMedium and [الكمية المتوسطة],StoreSmall as [الكمية الاصغر] from items order by 1"
        Else
            S = "select itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الصنف],Unity as [الوحدة],TinPrice as [سعر الشراء],SalPrice as [سعر البيع],WholePrice as [سعر الجملة],rng as [الحد الأدنى] ,store as [الكمية],Stores as [أسم المخزن],StoreBiggest as [الكمية الاكبر],StoreMedium and [الكمية المتوسطة],StoreSmall as [الكمية الاصغر] from items where Company_Branch_ID =N'" & Company_Branch_ID & "' order by 1"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)
        DTGV.Columns(0).Width = 90
        DTGV.Columns(1).Width = 90
        DTGV.Columns(2).Width = 140
        DTGV.Columns(3).Width = 50
        DTGV.Columns(4).Width = 50
        DTGV.Columns(5).Width = 45
        DTGV.Columns(6).Width = 45
        DTGV.Columns(7).Width = 45
        DTGV.Columns(8).Width = 45
        DTGV.Columns(9).Width = 45
        DTGV.Columns(10).Width = 45
        DTGV.Columns(11).Width = 45
        DTGV.Columns(12).Width = 45
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        GODataBaseAccountsTree()
    End Sub

    Private Sub FrmItemsNewExcel_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
    End Sub

    Private Sub GetDataSaveOnlineShop()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        connectionStringOpen()
        ConnectingOnlineStore()

        Dim XPRC As String = ""
        Dim XName As String = ""
        Dim ID As String = ""
        Dim XLoopItem As Double = 0
        Dim XLoopPrc As Double = 0

        For i As Integer = 0 To DTGV.Rows.Count - 1
            XPRC = DTGV.Rows(i).Cells(0).Value.ToString()
            XName = DTGV.Rows(i).Cells(6).Value.ToString()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Product where Name =N'" & XName & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
                XLoopItem = 1
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Product where Code =N'" & XPRC & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
                XLoopPrc = 1
            End If
        Next
        If XLoopItem = 1 Then
            MsgBox(" يوجد صنف مسجل مسبقاً ", MsgBoxStyle.Exclamation)
            Exit Sub
        End If
        If XLoopItem = 1 Then
            MsgBox("الباركود مسجل مسبقاً", MsgBoxStyle.Exclamation)
            Exit Sub
        End If


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            If DTGV.Rows(i).Cells(6).Value.ToString <> "" Then

                If DTGV.Rows(i).Cells(0).Value.ToString = "0" Or DTGV.Rows(i).Cells(0).Value.ToString = "" Then
                    MAXRECORDProductParcode()
                Else
                    Parcode = DTGV.Rows(i).Cells(0).Value.ToString
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Product (Code,Tag,CategoryId,CompanyId,Name,Description,Price,DiscountedPrice,Price2,DiscountedPrice2,Price3,DiscountedPrice3,Stock,LimitQuantity) values ("
                S = S & "N'" & Parcode & "',N'" & Parcode & "',N'" & Val(DTGV.Rows(i).Cells(2).Value.ToString) & "',N'" & Val(DTGV.Rows(i).Cells(4).Value.ToString) & "',N'" & DTGV.Rows(i).Cells(6).Value.ToString & "',N'" & DTGV.Rows(i).Cells(7).Value.ToString & "',N'" & Val(DTGV.Rows(i).Cells(8).Value.ToString) & "',N'" & Val(DTGV.Rows(i).Cells(9).Value.ToString) & "',N'" & Val(DTGV.Rows(i).Cells(10).Value.ToString) & "',N'" & Val(DTGV.Rows(i).Cells(11).Value.ToString) & "',N'" & Val(DTGV.Rows(i).Cells(12).Value.ToString) & "',N'" & Val(DTGV.Rows(i).Cells(13).Value.ToString) & "',N'" & Val(DTGV.Rows(i).Cells(14).Value.ToString) & "',N'" & Val(DTGV.Rows(i).Cells(15).Value.ToString) & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ID = Cls.Get_Code_Value("Product", "id", "Code", Parcode)

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into ProductImages (ProductId,ImageUrl) values ("
                S = S & "N'" & ID & "',N'" & DTGV.Rows(i).Cells(16).Value.ToString & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

            End If
        Next

        MsgBox("تم حفظ البيانات من الاكسل الى قاعدة البيانات", MsgBoxStyle.Information)
        CloseDB()
        connect()
    End Sub

    Private Sub btnSaveApp_Click(sender As Object, e As EventArgs) Handles btnSaveApp.Click
        GetDataSaveOnlineShop()
    End Sub

    Private Sub btnSaveDataBaseUnits_Click(sender As Object, e As EventArgs) Handles btnSaveDataBaseUnits.Click
        GODataBaseUnits()
    End Sub

    Private Sub btnSaveAppToAccount_Click(sender As Object, e As EventArgs) Handles btnSaveAppToAccount.Click
        GetDataSaveOnlineShopToAccountApp()
    End Sub

    Private Sub GetDataSaveOnlineShopToAccountApp()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        connectionStringOpen()

        Dim XPRC As String = ""
        Dim XName As String = ""
        Dim XStore As String = ""

        Dim PriceIncludesVAT As String = ""
        Dim RateVAT As String = ""
        Dim XLoopItem As Double = 0
        Dim XLoopPrc As Double = 0
        Dim Tag As String = ""
        Dim Category_ID As String = ""
        Dim Category_Name As String = ""
        Dim Company_ID As String = ""
        Dim Company_Name As String = ""
        Dim Product_Name As String = ""
        Dim Description As String = ""
        Dim Unit As String = ""
        Dim TinPrice As String = ""
        Dim Price As String = ""
        Dim DiscountedPrice As String = ""
        Dim Price2 As String = ""
        Dim DiscountedPrice2 As String = ""
        Dim Price3 As String = ""
        Dim DiscountedPrice3 As String = ""
        Dim MinimumSalPrice As String = ""
        Dim Rng As String = ""
        Dim Stock As String = ""
        Dim LimitQuantity As String = ""
        Dim Store_Name As String = ""
        Dim VAT As String = ""


        For i As Integer = 0 To DTGV.Rows.Count - 1
            XPRC = DTGV.Rows(i).Cells(0).Value.ToString()
            XName = DTGV.Rows(i).Cells(6).Value.ToString()
            XStore = DTGV.Rows(i).Cells(20).Value.ToString()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where sname =N'" & XName & "' and Stores =N'" & XStore & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
                XLoopItem = 1
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where itm_id =N'" & XPRC & "' and Stores =N'" & XStore & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
                XLoopPrc = 1
            End If
        Next
        If XLoopItem = 1 Then
            MsgBox(" يوجد صنف مسجل مسبقاً بنفس أسم المخزن", MsgBoxStyle.Exclamation)
            Exit Sub
        End If
        If XLoopItem = 1 Then
            MsgBox("الباركود مسجل مسبقاً", MsgBoxStyle.Exclamation)
            Exit Sub
        End If


        Dim bill_EndDate As String = "بدون صلاحية"
        Dim X As String = "جرد"
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            If DTGV.Rows(i).Cells(8).Value.ToString <> "" Then

                XPRC = DTGV.Rows(i).Cells(0).Value.ToString()
                Tag = DTGV.Rows(i).Cells(1).Value.ToString()
                Category_ID = DTGV.Rows(i).Cells(2).Value.ToString()
                Category_Name = DTGV.Rows(i).Cells(3).Value.ToString()
                Company_ID = DTGV.Rows(i).Cells(4).Value.ToString()
                Company_Name = DTGV.Rows(i).Cells(5).Value.ToString()
                Product_Name = DTGV.Rows(i).Cells(6).Value.ToString()
                Description = DTGV.Rows(i).Cells(7).Value.ToString()
                Unit = DTGV.Rows(i).Cells(8).Value.ToString()
                TinPrice = DTGV.Rows(i).Cells(9).Value.ToString()
                Price = DTGV.Rows(i).Cells(10).Value.ToString()
                DiscountedPrice = DTGV.Rows(i).Cells(11).Value.ToString()
                Price2 = DTGV.Rows(i).Cells(12).Value.ToString()
                DiscountedPrice2 = DTGV.Rows(i).Cells(13).Value.ToString()
                Price3 = DTGV.Rows(i).Cells(14).Value.ToString()
                DiscountedPrice3 = DTGV.Rows(i).Cells(15).Value.ToString()
                MinimumSalPrice = DTGV.Rows(i).Cells(16).Value.ToString()
                Rng = DTGV.Rows(i).Cells(17).Value.ToString()
                Stock = DTGV.Rows(i).Cells(18).Value.ToString()
                LimitQuantity = DTGV.Rows(i).Cells(19).Value.ToString()
                Store_Name = DTGV.Rows(i).Cells(20).Value.ToString()
                RateVAT = DTGV.Rows(i).Cells(21).Value.ToString()

                AddGroubsCategory_ID(Category_Name.ToString, Category_ID)
                AddStores(Store_Name.ToString)
                AddCompanies(Company_ID, Company_Name, Category_ID)

                If RateVAT = 0 Then
                    PriceIncludesVAT = 0
                Else
                    PriceIncludesVAT = 1
                End If
                MAXRECORDIDTM()
                If XPRC.ToString = "0" Or XPRC.ToString = "" Then
                    MAXRECORDParcode()
                Else
                    Parcode = XPRC.ToString
                End If
                If Tag = "" Then
                    Tag = Parcode
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Items (Company_Branch_ID,IDTM,itm_id,Tag,group_name,CompaniesID,CompaniesName,sname,Description,Unity,TinPrice,TinPriceAverage,salprice,DiscountedPrice,WholePrice,DiscountedPrice2,WholeWholePrice,DiscountedPrice3,MinimumSalPrice,rng,store,LimitQuantity,Stores,RateVAT,UserName,QuickSearch,PriceIncludesVAT) values ("
                S = S & "N'" & Company_Branch_ID & "',N'" & IDTM & "',N'" & Parcode & "',N'" & Tag & "',N'" & Category_Name.ToString & "',N'" & Company_ID.ToString & "',N'" & Company_Name.ToString & "',N'" & Product_Name.ToString & "',N'" & Description.ToString & "',N'" & Unit.ToString & "',N'" & TinPrice.ToString & "',N'" & TinPrice.ToString & "',N'" & Price.ToString & "',N'" & DiscountedPrice.ToString & "',N'" & Price2.ToString & "',N'" & DiscountedPrice2.ToString & "',N'" & Price3.ToString & "',N'" & DiscountedPrice3.ToString & "',N'" & MinimumSalPrice.ToString & "',N'" & Rng.ToString & "',N'" & Stock.ToString & "',N'" & LimitQuantity.ToString & "',N'" & Store_Name.ToString & "'," & RateVAT & ",N'" & UserName & "',0," & PriceIncludesVAT & ")"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into BilltINData (Company_Branch_ID,IDTM,bill_no,itm_id,itm_cat,itm_name,itm_Unity,price,TinPriceAverage,qu,qu_unity,totalprice,Stores,UserName,bill_date,Expired,qu_expired,Treasury_Code,CurrentStock,RateVAT,PriceIncludesVAT)"
                S = S & " values (N'" & Company_Branch_ID & "',N'" & IDTM & "',N'" & X & "',N'" & Parcode & "',N'" & Category_Name.ToString & "',N'" & Product_Name.ToString & "',N'" & Unit.ToString & "',N'" & TinPrice & "',N'" & TinPrice.ToString & "',N'" & Stock.ToString & "',N'" & Stock.ToString & "',N'" & Val(TinPrice.ToString) * Val(Stock.ToString) & "',N'" & Store_Name.ToString & "',N'" & UserName & "',N'" & Cls.C_date(dtpDate.Text) & "',N'" & bill_EndDate & "',N'" & Stock.ToString & "',N'" & Treasury_Code & "',N'" & Stock.ToString & "'," & RateVAT & "," & PriceIncludesVAT & ")"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into ItemsUnity(itm_id,Unity_Name,NumberPieces,TinPriceUnit,SalPriceUnit,DefaultTin,DefaultSale,itm_id_Unity,Company_Branch_ID)  values("
                S = S & "N'" & Parcode & "',N'" & Unit.ToString & "',N'1',N'1',N'1',N'1',N'1',N'" & Parcode & "',N'" & Company_Branch_ID & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                IM.Store(Parcode, Store_Name)

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & X & "' and itm_id = N'" & Parcode & "' and Stores =N'" & Store_Name & "'" : cmd.ExecuteNonQuery()

            End If
        Next

        MsgBox("تم حفظ البيانات من الاكسل الى قاعدة البيانات", MsgBoxStyle.Information)
        CloseDB()
    End Sub

    Private Sub btnCompanies_Click(sender As Object, e As EventArgs) Handles btnCompanies.Click
        GetCompanies()
    End Sub

    Private Sub GetCompanies()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        connectionStringOpen()

        Dim Code As String = ""
        Dim Name As String = ""
        Dim XLoopName As Double = 0
        Dim XLoopCode As Double = 0

        For i As Integer = 0 To DTGV.Rows.Count - 1
            Code = DTGV.Rows(i).Cells(0).Value.ToString()
            Name = DTGV.Rows(i).Cells(1).Value.ToString()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Companies where id =N'" & Code & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
                XLoopCode = 1
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Companies where Name =N'" & Name & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
                XLoopName = 1
            End If
        Next
        If XLoopCode = 1 Then
            MsgBox("يوجد كود شركة مسبقاً", MsgBoxStyle.Exclamation)
            Exit Sub
        End If
        If XLoopName = 1 Then
            MsgBox(" يوجد بيان شركة مسبقا", MsgBoxStyle.Exclamation)
            Exit Sub
        End If


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            Code = DTGV.Rows(i).Cells(0).Value.ToString()
            Name = DTGV.Rows(i).Cells(1).Value.ToString()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Companies (id,Name,Company_Branch_ID,UserName) values ("
            S = S & "N'" & Code & "',N'" & Name & "',N'" & Company_Branch_ID & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        MsgBox("تم حفظ البيانات من الاكسل الى قاعدة البيانات", MsgBoxStyle.Information)
        CloseDB()
    End Sub

    Private Sub GetCompaniesOnlineStore()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        ConnectingOnlineStore()

        Dim Code As String = ""
        Dim Name As String = ""
        Dim CategoryId As String = ""
        Dim XLoopName As Double = 0
        Dim XLoopCode As Double = 0

        For i As Integer = 0 To DTGV.Rows.Count - 1
            Code = DTGV.Rows(i).Cells(0).Value.ToString()
            Name = DTGV.Rows(i).Cells(1).Value.ToString()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Company where id =N'" & Code & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
                XLoopCode = 1
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Company where Name =N'" & Name & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
                XLoopName = 1
            End If
        Next
        If XLoopCode = 1 Then
            MsgBox("يوجد كود شركة مسبقاً", MsgBoxStyle.Exclamation)
            Exit Sub
        End If
        If XLoopName = 1 Then
            MsgBox(" يوجد بيان شركة مسبقا", MsgBoxStyle.Exclamation)
            Exit Sub
        End If


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            Code = DTGV.Rows(i).Cells(0).Value.ToString()
            Name = DTGV.Rows(i).Cells(1).Value.ToString()
            CategoryId = DTGV.Rows(i).Cells(2).Value.ToString()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Company (id,Name,CategoryId,IsDelete) values ("
            S = S & "N'" & Code & "',N'" & Name & "',N'" & CategoryId & "',N'False')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        MsgBox("تم حفظ البيانات من الاكسل الى قاعدة البيانات", MsgBoxStyle.Information)
        CloseDB()
        connect()
    End Sub

    Private Sub btnCompaniesOnlineStore_Click(sender As Object, e As EventArgs) Handles btnCompaniesOnlineStore.Click
        GetCompaniesOnlineStore()
    End Sub

    Private Sub GetUpdateOnlineApp()

        ConnectingOnlineStore()

        Dim aray_1 As New ArrayList
        Dim aray_2 As New ArrayList
        Dim aray_3 As New ArrayList
        Dim aray_4 As New ArrayList
        Dim Id As String = ""
        Dim ProductId As String = ""
        Dim OrderNo As String
        Dim UserId As String
        Dim Price As Decimal = 0
        Dim Total As Decimal = 0
        Dim DiscountedPrice As String = ""
        Dim Quantity As String = ""

        'aray_1.Clear() : aray_2.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select Id,ProductId from OrderItem"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_1.Add(dr(0))
        '    aray_2.Add(dr(1))
        'Loop

        'For i As Integer = 0 To aray_1.Count - 1
        '    Id = aray_1(i).ToString()
        '    ProductId = aray_2(i).ToString()

        '    DiscountedPrice = Cls.Get_Code_Value("Product", "DiscountedPrice", "Id", ProductId)

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "update OrderItem set DiscountedPriceOrder = " & DiscountedPrice & " where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()
        'Next

        ''===============================================================================

        'Dim TotalDiscountedPrice As String = ""
        'aray_1.Clear() : aray_2.Clear() : aray_3.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select Id,DiscountedPriceOrder,Quantity from OrderItem where TotalPriceOrder=0.00"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_1.Add(dr(0))
        '    aray_2.Add(dr(1))
        '    aray_3.Add(dr(2))
        'Loop

        'For i As Integer = 0 To aray_1.Count - 1
        '    Id = aray_1(i).ToString()
        '    DiscountedPrice = aray_2(i).ToString()
        '    Quantity = aray_3(i).ToString()

        '    TotalDiscountedPrice = Val(DiscountedPrice) * Val(Quantity)

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "update OrderItem set TotalPriceOrder = " & TotalDiscountedPrice & " where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()
        'Next

        ''===============================================================================

        'Dim TotalDiscountedPrice As String = ""
        'aray_1.Clear() : aray_2.Clear() : aray_3.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select Id,Price from Product"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_1.Add(dr(0))
        '    aray_2.Add(dr(1))
        'Loop
        'For i As Integer = 0 To aray_1.Count - 1
        '    Id = aray_1(i).ToString()
        '    Price = aray_2(i).ToString()

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "update Product set Price = " & 0 & " where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()
        'Next

        '===============================================================================
        'connect()
        'Dim CategoryId As String : Dim IsDelete As String
        'aray_1.Clear() : aray_2.Clear() : aray_3.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select Id,CategoryId,IsDelete from Companies"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_1.Add(dr(0))
        '    aray_2.Add(dr(1))
        '    aray_3.Add(dr(2))
        'Loop
        'For i As Integer = 0 To aray_1.Count - 1
        '    Id = aray_1(i).ToString()
        '    CategoryId = aray_2(i).ToString()
        '    IsDelete = aray_3(i).ToString()

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "update Companies set CategoryId = " & 3 & ",IsDelete = 'False' where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()
        'Next
        ''===============================================================================

        'Dim CategoryId As String : Dim IsDelete As String
        'aray_1.Clear() : aray_2.Clear() : aray_3.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select Id,CategoryId,IsDelete from Company"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_1.Add(dr(0))
        '    aray_2.Add(dr(1))
        '    aray_3.Add(dr(2))
        'Loop
        'For i As Integer = 0 To aray_1.Count - 1
        '    Id = aray_1(i).ToString()
        '    CategoryId = aray_2(i).ToString()
        '    IsDelete = aray_3(i).ToString()

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "update Company set CategoryId = " & 3 & " where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()

        '    'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    'cmd.CommandText = "update Company set CategoryId = " & 3 & ",IsDelete = 'False' where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()
        'Next


        ''===============================================================================
        'connect()
        'aray_1.Clear() : aray_2.Clear() : aray_3.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select Id from Items"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_1.Add(dr(0))
        'Loop
        'For i As Integer = 0 To aray_1.Count - 1
        '    Id = aray_1(i).ToString()

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "update Items set CompaniesID = " & 1 & ",CompaniesName =N'الحاوى' where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()
        'Next

        '===============================================================================
        'connect()
        'aray_1.Clear() : aray_2.Clear() : aray_3.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select Id from Area"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_1.Add(dr(0))
        'Loop
        'For i As Integer = 0 To aray_1.Count - 1
        '    Id = aray_1(i).ToString()

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "update Area set OrderLimit = " & 1500 & " where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()
        'Next

        '===============================================================================
        'ConnectingOnlineStore()
        'aray_1.Clear() : aray_2.Clear() : aray_3.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        ''cmd.CommandText = "Select Id,ProductId from OrderItem where UnitPrice=0"
        'cmd.CommandText = "Select Id,ProductId from OrderItem"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_1.Add(dr(0))
        '    aray_2.Add(dr(1))
        'Loop
        'For i As Integer = 0 To aray_1.Count - 1
        '    Id = aray_1(i).ToString()
        '    ProductId = aray_2(i).ToString()

        '    Price = Cls.Get_Code_Value("Product", "Price3", "Id", ProductId)
        '    DiscountedPrice = Cls.Get_Code_Value("Product", "DiscountedPrice3", "Id", ProductId)

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "update OrderItem set UnitPrice = " & Price & ",DiscountedPrice = " & DiscountedPrice & ",DiscountedPriceOrder = " & DiscountedPrice & " where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()
        'Next

        '===============================================================================
        'ConnectingOnlineStore()
        'aray_1.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select Id from [User]"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_1.Add(dr(0))
        'Loop
        'For i As Integer = 0 To aray_1.Count - 1
        '    Id = aray_1(i).ToString()

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "update [User] set SlidesPriceId = " & 3 & " where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()
        'Next
        '===============================================================================
        'OrderItem Online Server
        'aray_1.Clear() : aray_2.Clear() : aray_3.Clear() : aray_3.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select Id,ProductId,Quantity,UserId from OrderItem"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_1.Add(dr(0))
        '    aray_2.Add(dr(1))
        '    aray_3.Add(dr(2))
        '    aray_4.Add(dr(3))
        'Loop

        'For i As Integer = 0 To aray_1.Count - 1
        '    Id = aray_1(i).ToString()
        '    ProductId = aray_2(i).ToString()
        '    Quantity = aray_3(i).ToString()
        '    UserId = aray_4(i).ToString()
        '    Price = getProductPrice(ProductId, UserId)
        '    Total = Val(Price) * Val(Quantity)

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "update OrderItem set UnitPrice = " & Price & ",Total = " & Total & " where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()
        'Next

        '===============================================================================

        'ConnectingOnlineStore()
        'aray_1.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select Id from [User]"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_1.Add(dr(0))
        'Loop

        'For i As Integer = 0 To aray_1.Count - 1
        '    Id = aray_1(i).ToString()

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    'True
        '    cmd.CommandText = "update [User] set IsUpdatePlayStore = '1' where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()
        '    'False
        '    'cmd.CommandText = "update [User] set IsUpdatePlayStore = '0' where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()

        'Next
        'connect()

        '===============================================================================
        'ConnectingOnlineStore()
        'aray_1.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select Id from [Area]"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_1.Add(dr(0))
        'Loop

        'For i As Integer = 0 To aray_1.Count - 1
        '    Id = aray_1(i).ToString()

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "update [Area] set OrderLimit = '2000' where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()
        'Next
        'connect()

        '===============================================================================

        'ConnectingOnlineStore()
        'aray_1.Clear() : aray_2.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select Id,OrderStatusId from [Order] WHERE  (OrderNo = '5770')"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_1.Add(dr(0))
        '    aray_2.Add(dr(1))
        'Loop
        'For i As Integer = 0 To aray_1.Count - 1
        '    Id = aray_1(i).ToString()
        '    ProductId = aray_2(i).ToString()
        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "update [Order] set OrderStatusId =N'2',OrderStatus =N'قيد التحضير' where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()
        'Next

        'aray_1.Clear() : aray_2.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select Id,OrderStatusId from OrderItem WHERE  (OrderNo = '5770')"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_1.Add(dr(0))
        '    aray_2.Add(dr(1))
        'Loop
        'For i As Integer = 0 To aray_1.Count - 1
        '    Id = aray_1(i).ToString()
        '    ProductId = aray_2(i).ToString()

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "update OrderItem set OrderStatusId =N'2',OrderStatus =N'قيد التحضير' where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()
        'Next

        '===============================================================================
        ConnectingOnlineStore()
        aray_1.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Id from [Product] where LimitQuantity IS NULL"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr(0))
        Loop

        For i As Integer = 0 To aray_1.Count - 1
            Id = aray_1(i).ToString()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update [Product] set LimitQuantity = '0' where Id = N'" & Id & "'" : cmd.ExecuteNonQuery()
        Next
        connect()

        '===============================================================================

        MsgBox("تم تحديث البيانات على المتجر الالكترونى", MsgBoxStyle.Information)

        CloseDB()
        connect()
    End Sub

    Private Sub btnUpdateOnlineApp_Click(sender As Object, e As EventArgs) Handles btnUpdateOnlineApp.Click
        GetUpdateOnlineApp()
    End Sub

    Public Function getProductPrice(ByVal productId As Integer, ByVal userID As Integer) As Decimal
        'Try
        Dim slidesPriceId = 0
            Dim slidesPriceNo = 0
            Dim Price As Decimal = 0

        slidesPriceId = Get_Code_Value_More_where("[User]", "slidesPriceId", "Id =N'" & userID & "' and IsDelete = 'False'")

        slidesPriceNo = Get_Code_Value_More_where("[SlidesPrice]", "SlidesPriceNo", "Id =N'" & slidesPriceId & "' and IsSlidesPrice = 'True' and IsDelete = 'False'")

        Dim query3 = Get_Code_Value_More_where("[Product]", "Name", "Id =N'" & productId & "' and IsDelete = 'False'")

        If query3 IsNot Nothing Then
                If slidesPriceNo = 0 Then
                Price = Convert.ToDecimal(Get_Code_Value_More_where("[Product]", "Price", "Id =N'" & productId & "' and IsDelete = 'False'"))
            End If
                If slidesPriceNo = 1 Then
                Price = Convert.ToDecimal(Get_Code_Value_More_where("[Product]", "Price", "Id =N'" & productId & "' and IsDelete = 'False'"))
            End If
                If slidesPriceNo = 2 Then
                Price = Convert.ToDecimal(Get_Code_Value_More_where("[Product]", "Price2", "Id =N'" & productId & "' and IsDelete = 'False'"))
            End If
                If slidesPriceNo = 3 Then
                Price = Convert.ToDecimal(Get_Code_Value_More_where("[Product]", "Price3", "Id =N'" & productId & "' and IsDelete = 'False'"))
            End If
            Else
                Return 0
            End If

            Return Convert.ToDecimal(Price.ToString())
        'Catch ex As Exception
        '    Return 0
        'End Try

    End Function

    Function Get_Code_Value_More_where(ByVal Table_name As String, ByVal Field_Code As String, ByVal crtria As String)
        'Try
        Dim Code As String = "0"
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select " & Field_Code & " from " & Table_name & " where " & crtria
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Code = dr(0).ToString
        End If
        Return Code
        'Catch ex As Exception
        'End Try
    End Function

    Private Sub Button3_Click(sender As Object, e As EventArgs) Handles Button3.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        connectionStringOpen()

        Dim XPRC As String = ""
        Dim XName As String = ""
        Dim XStore As String = ""
        Dim PriceIncludesVAT As String = ""
        Dim RateVAT As String = ""
        Dim XLoopItem As Double = 0
        Dim XLoopPrc As Double = 0


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update items set TinPrice = " & Val(DTGV.Rows(i).Cells(2).Value.ToString()) & ",SalPrice = " & Val(DTGV.Rows(i).Cells(3).Value.ToString()) & ",WholePrice = " & Val(DTGV.Rows(i).Cells(4).Value.ToString()) & ",WholeWholePrice = " & Val(DTGV.Rows(i).Cells(5).Value.ToString()) & ",MinimumSalPrice = " & Val(DTGV.Rows(i).Cells(6).Value.ToString()) & ",TinPriceAverage =N'" & DTGV.Rows(i).Cells(2).Value.ToString() & "' where itm_id =N'" & DTGV.Rows(i).Cells(0).Value.ToString() & "' and Stores =N'" & DTGV.Rows(i).Cells(7).Value.ToString() & "'" : cmd.ExecuteNonQuery()

        Next

        MsgBox("تم تحديث الاسعار من الاكسل الى قاعدة البيانات", MsgBoxStyle.Information)
        CloseDB()
    End Sub

End Class