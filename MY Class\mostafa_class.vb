﻿Imports System.Text
Imports System.Security.Cryptography

Imports VB = Microsoft.VisualBasic
Imports System.IO
Imports System.Data.SqlClient

Public Class mostafa_class
    Dim connection As SqlConnection
    Dim command As SqlCommand
    Dim adapter As New SqlDataAdapter()
    Dim ds As New DataSet()
    Function R_date(ByVal dt As String) As Date
        On Error Resume Next
        Dim dy As String
        Dim mn As String
        Dim yr As String
        dy = Mid(dt, 7, 2)
        If Len(Trim(dy)) = 1 Then dy = "0" + Trim(Str(dy))
        mn = Mid(dt, 5, 2)
        If Len(Trim(mn)) = 1 Then mn = "0" + Trim(Str(mn))
        yr = Mid(dt, 1, 4)
        Return Convert.ToDateTime(yr & "/" & mn & "/" & dy)
    End Function
    Function get_time(ByVal formatted As Boolean) As String
        Try
            Dim tm As String
            Select Case formatted
                Case True
                    Dim h, m, s
                    h = Now.Hour.ToString
                    If Len(h) = 1 Then h = "0" & h
                    m = Now.Minute.ToString
                    If Len(m) = 1 Then m = "0" & m
                    s = Now.Second.ToString
                    If Len(s) = 1 Then s = "0" & s
                    tm = h & " : " & m & " : " & s
                Case False
                    Dim h, m, s
                    h = Now.Hour.ToString
                    If Len(h) = 1 Then h = "0" & h
                    m = Now.Minute.ToString
                    If Len(m) = 1 Then m = "0" & m
                    s = Now.Second.ToString
                    If Len(s) = 1 Then s = "0" & s
                    tm = h & m & s
            End Select
            Return tm
        Catch ex As Exception
        End Try
    End Function

    Function GenerateHash(ByVal SourceText As String) As String
        Try
            Dim Ue As New UnicodeEncoding()
            Dim ByteSourceText() As Byte = Ue.GetBytes(SourceText)
            Dim Md5 As New MD5CryptoServiceProvider()
            Dim ByteHash() As Byte = Md5.ComputeHash(ByteSourceText)
            Return Convert.ToBase64String(ByteHash)
        Catch ex As Exception
        End Try
    End Function

    Sub fill_combo(ByVal Table_Name As String, ByVal Field_Name As String, ByVal combo As ComboBox)
        Try
            combo.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " order by 1"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                combo.Items.Add(Trim(dr(0)))
            Loop
        Catch ex As Exception
        End Try
    End Sub

    Sub fill_combo_Branch(ByVal Table_Name As String, ByVal Field_Name As String, ByVal combo As ComboBox)
        Try
            combo.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " order by 1"
            Else
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & "  where Company_Branch_ID=N'" & Company_Branch_ID & "' order by 1"
            End If
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                combo.Items.Add(Trim(dr(0)))
            Loop
        Catch ex As Exception
        End Try
    End Sub

    Sub fill_combo_Where(ByVal Table_Name As String, ByVal Field_Name As String, ByVal Field_Name_where As String, ByVal Text As String, ByVal combo As ComboBox)
        Try
            combo.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where " & Field_Name_where & " =N'" & Text & "' order by 1"
            Else
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where " & Field_Name_where & " =N'" & Text & "' and Company_Branch_ID = N'" & Company_Branch_ID & "' order by 1"
            End If
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                combo.Items.Add(Trim(dr(0)))
            Loop
        Catch ex As Exception
        End Try
    End Sub

    Sub fill_combo_Stores_Where(ByVal Table_Name As String, ByVal Field_Name As String, ByVal Field_Name_where As String, ByVal Text As String, ByVal combo As ComboBox)
        Try
            combo.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where " & Field_Name_where & " =N'" & Text & "' order by 1"
            Else
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where " & Field_Name_where & " =N'" & Text & "' and Stores = N'" & StoresName & "' order by 1"
            End If
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                combo.Items.Add(Trim(dr(0)))
            Loop
        Catch ex As Exception
        End Try
    End Sub

    Sub fill_combo_Where_More(ByVal Table_Name As String, ByVal Field_Name As String, ByVal crtria As String, ByVal combo As ComboBox)
        Try
            combo.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where " & crtria & " order by 1"
            Else
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where Company_Branch_ID = N'" & Company_Branch_ID & "' and " & crtria & " order by 1"
            End If
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                combo.Items.Add(Trim(dr(0)))
            Loop
        Catch ex As Exception
        End Try
    End Sub

    Sub fill_combo_Stores_Where_More(ByVal Table_Name As String, ByVal Field_Name As String, ByVal crtria As String, ByVal combo As ComboBox)
        Try
            combo.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where " & crtria & " order by 1"
            Else
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where Stores = N'" & StoresName & "' and " & crtria & " order by 1"
            End If
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                combo.Items.Add(Trim(dr(0)))
            Loop
        Catch ex As Exception
        End Try
    End Sub

    Function Select_SUM_Value(ByVal Table_Name As String, ByVal Field_Name As String, ByVal Field_Name_where As String, ByVal Text As String) As Double
        Try
            Dim Return_Value As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "Select sum(" & Field_Name & ") from " & Table_Name & " where " & Field_Name_where & " =N'" & Text & "'" : dr = cmd.ExecuteReader : dr.Read()
            Else
                cmd.CommandText = "Select sum(" & Field_Name & ") from " & Table_Name & " where " & Field_Name_where & " =N'" & Text & "' and Company_Branch_ID =N'" & Company_Branch_ID & "'" : dr = cmd.ExecuteReader : dr.Read()
            End If
            If dr(0) Is DBNull.Value Then Return_Value = 0 Else Return_Value = dr(0).ToString
            Return_Value = Math.Round(Return_Value, 2)
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function

    Function Select_Min_Value(ByVal Table_Name As String, ByVal Field_Name As String) As Double
        Try
            Dim Return_Value As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "Select min(" & Field_Name & ") from " & Table_Name & ""
            Else
                cmd.CommandText = "Select min(" & Field_Name & ") from " & Table_Name & " where Company_Branch_ID =N'" & Company_Branch_ID & "'"
            End If
            dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Return_Value = 0 Else Return_Value = dr(0).ToString
            Return_Value = Math.Round(Return_Value, 2)
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function

    Function Select_Max_Value(ByVal Table_Name As String, ByVal Field_Name As String) As Double
        Try
            Dim Return_Value As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "Select MAX(" & Field_Name & ") from " & Table_Name & ""
            Else
                cmd.CommandText = "Select MAX(" & Field_Name & ") from " & Table_Name & " where Company_Branch_ID =N'" & Company_Branch_ID & "'"
            End If
            dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Return_Value = 0 Else Return_Value = dr(0).ToString
            Return_Value = Math.Round(Return_Value, 2)
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function


    Function Select_SUM_Value_More(ByVal Table_Name As String, ByVal Field_Name As String, ByVal crtria As String) As Double
        Try
            Dim Return_Value As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "Select sum(" & Field_Name & ") from " & Table_Name & " where " & crtria : dr = cmd.ExecuteReader : dr.Read()
            Else
                cmd.CommandText = "Select sum(" & Field_Name & ") from " & Table_Name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' and " & crtria : dr = cmd.ExecuteReader : dr.Read()
            End If
            If dr(0) Is DBNull.Value Then Return_Value = 0 Else Return_Value = dr(0).ToString
            Return_Value = Math.Round(Return_Value, 2)
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function

    Function Get_Select_Grid_S_OrderBy(ByVal Field_Name As String, ByVal Table_Name As String, ByVal crtria As String) As String
        Try
            Dim Return_Value As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where " & crtria & " order by 1"
            Else
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where Stores =N'" & StoresName & "' and " & crtria & " order by 1"
            End If
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function

    Function Get_Select_Grid_S(ByVal Field_Name As String, ByVal Table_Name As String, ByVal crtria As String) As String
        Try
            Dim Return_Value As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where " & crtria
            Else
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where " & crtria & " and Stores =N'" & StoresName & "'"
            End If
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function

    Function Get_Select_Grid_S_OrderBy_Branch(ByVal Field_Name As String, ByVal Table_Name As String, ByVal crtria As String) As String
        Try
            Dim Return_Value As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where " & crtria & " order by 1"
            Else
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' and " & crtria & " order by 1"
            End If
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function

    Function Get_Select_Grid_S_Branch(ByVal Field_Name As String, ByVal Table_Name As String, ByVal crtria As String) As String
        Try
            Dim Return_Value As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where " & crtria
            Else
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where " & crtria & " and Company_Branch_ID =N'" & Company_Branch_ID & "'"
            End If
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function

    Function Get_Value_Count_More(ByVal Table_Name As String, ByVal crtria As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select count(*) from " & Table_Name & " where " & crtria
            Else
                cmd.CommandText = "select count(*) from " & Table_Name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' and " & crtria
            End If
            H = cmd.ExecuteScalar
        Catch ex As Exception
        End Try
    End Function

    Sub fill_combo_QuickSearch(ByVal Table_Name As String, ByVal Field_Name As String, ByVal combo As ComboBox)
        Try
            combo.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select distinct " & Field_Name & " from " & Table_Name & " where QuickSearch=0 And Stores =N'" & StoresName & "' order by 1  "
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                combo.Items.Add(dr(0))
            Loop
        Catch ex As Exception
        End Try
    End Sub

    Sub FillComboDataSet(ByVal Table_Name As String, ByVal ID As String, ByVal Name As String, ByVal combo As ComboBox)
        ds.Clear()
        combo.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "select distinct " & ID & "," & Name & " from " & Table_Name & ""
        connection = New SqlConnection(constring)
        Try
            connection.Open()
            command = New SqlCommand(S, connection)
            adapter.SelectCommand = command
            adapter.Fill(ds)
            adapter.Dispose()
            command.Dispose()
            connection.Close()
            combo.DataSource = ds.Tables(0)
            combo.ValueMember = "" & ID & ""
            combo.DisplayMember = "" & Name & ""
            combo.Text = ""
        Catch ex As Exception
        End Try
    End Sub

    Sub FillComboDataSetQuickSearch(ByVal Table_Name As String, ByVal ID As String, ByVal Name As String, ByVal combo As ComboBox)
        ds.Clear()
        combo.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "select distinct " & ID & "," & Name & " from " & Table_Name & " where Company_Branch_ID=N'" & Company_Branch_ID & "' and QuickSearch=0"
        connection = New SqlConnection(constring)
        Try
            connection.Open()
            command = New SqlCommand(S, connection)
            adapter.SelectCommand = command
            adapter.Fill(ds)
            adapter.Dispose()
            command.Dispose()
            connection.Close()
            combo.DataSource = ds.Tables(0)
            combo.ValueMember = "" & ID & ""
            combo.DisplayMember = "" & Name & ""
            combo.Text = ""
        Catch ex As Exception
        End Try
    End Sub

    Sub Fill_List(ByVal Table_Name As String, ByVal Field_Name As String, ByVal lis As ListBox)
        lis.Items.Clear()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " order by 1"
            dr = cmd.ExecuteReader
1:          If dr.Read Then
                lis.Items.Add(dr(Field_Name))
                GoTo 1
            End If
        Catch ex As Exception
        End Try
    End Sub
    Function Check_Field_Value(ByVal Table_name As String, ByVal crtria As String) As Boolean
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct * from " & Table_name & " where " & crtria
            dr = cmd.ExecuteReader

            If dr.Read Then
                Return True
            Else
                Return False
            End If
        Catch ex As Exception
        End Try
    End Function

    Function Check_Field_Value(ByVal Table_name As String, ByVal Field_Name As String, ByVal Value_toChek As String) As Boolean
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct " & Field_Name & " from " & Table_name & " where " & Field_Name & " =N'" & Value_toChek & "'"
            dr = cmd.ExecuteReader

            If dr.Read Then
                Return True
            Else
                Return False
            End If
        Catch ex As Exception
        End Try
    End Function
    Function Check_Field_Value(ByVal Table_name As String, ByVal Field_Name As String, ByVal Value_toChek As Double) As Boolean
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct " & Field_Name & " from " & Table_name & " where " & Field_Name & " =N'" & Value_toChek & "'"
            dr = cmd.ExecuteReader
            If dr.Read Then
                Return True
            Else
                Return False
            End If
        Catch ex As Exception
        End Try
    End Function

    Function Select_More_Data_Branch(ByVal Table_name As String, ByVal Field As String, ByVal crtria As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select " & Field & " from " & Table_name & " where " & crtria
            Else
                cmd.CommandText = "select " & Field & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' and " & crtria
            End If
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
        End Try
    End Function

    Function Select_More_Data_Branch_All(ByVal Table_name As String, ByVal Field As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select " & Field & " from " & Table_name & ""
            Else
                cmd.CommandText = "select " & Field & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "'"
            End If
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
        End Try
    End Function

    Function Select_More_Data_Stores(ByVal Table_name As String, ByVal Field As String, ByVal crtria As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select " & Field & " from " & Table_name & " where " & crtria
            Else
                cmd.CommandText = "select " & Field & " from " & Table_name & " where Stores =N'" & StoresName & "' and " & crtria
            End If
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
        End Try
    End Function

    Function Select_More_Data_Stores_All(ByVal Table_name As String, ByVal Field As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select " & Field & " from " & Table_name & ""
            Else
                cmd.CommandText = "select " & Field & " from " & Table_name & " where Stores =N'" & StoresName & "'"
            End If
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
        End Try
    End Function

    Function Select_More_Data_Branch_Print(ByVal Table_name As String, ByVal Field As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            If PermtionName = "مدير" Then
                cmd.CommandText = "select " & Field & " from " & Table_name & ""
            Else
                cmd.CommandText = "select " & Field & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "'"
            End If
            dr = cmd.ExecuteReader
        Catch ex As Exception
        End Try
    End Function

    Function Select_More_Data(ByVal Table_name As String, ByVal Field As String, ByVal crtria As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select " & Field & " from " & Table_name & " where " & crtria
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
        End Try
    End Function

    Function Get_Code_Value_Branch(ByVal Table_name As String, ByVal Field_Code As String, ByVal Field_Name As String, ByVal Value_toChek As String)
        Try
            Dim Code As String = "0"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where " & Field_Name & " =N'" & Value_toChek & "'"
            Else
                cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' and " & Field_Name & " =N'" & Value_toChek & "'"
            End If
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Code = dr(0).ToString
            End If
            Return Code
        Catch ex As Exception
        End Try
    End Function

    Function Get_Code_Value_Branch_More(ByVal Table_name As String, ByVal Field_Code As String, ByVal crtria As String)
        Try
            Dim Code As String = "0"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where " & crtria
            Else
                cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' and  " & crtria
            End If
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Code = dr(0).ToString
            End If
            Return Code
        Catch ex As Exception
        End Try
    End Function

    Function Get_Code_Value_Stores(ByVal Table_name As String, ByVal Field_Code As String, ByVal Field_Name As String, ByVal Value_toChek As String)
        Try
            Dim Code As String = "0"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where " & Field_Name & " =N'" & Value_toChek & "'"
            Else
                cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where Stores =N'" & StoresName & "' and " & Field_Name & " =N'" & Value_toChek & "'"
            End If
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Code = dr(0).ToString
            End If
            Return Code
        Catch ex As Exception
        End Try
    End Function

    Function Get_Code_Value_Stores_More(ByVal Table_name As String, ByVal Field_Code As String, ByVal crtria As String)
        Try
            Dim Code As String = "0"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where " & crtria
            Else
                cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where Stores =N'" & StoresName & "' and  " & crtria
            End If
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Code = dr(0).ToString
            End If
            Return Code
        Catch ex As Exception
        End Try
    End Function

    Function Get_Code_Value(ByVal Table_name As String, ByVal Field_Code As String, ByVal Field_Name As String, ByVal Value_toChek As String)
        Try
            Dim Code As String = "0"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where " & Field_Name & " =N'" & Value_toChek & "'"
                dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Code = dr(0).ToString
            End If
            Return Code
        Catch ex As Exception
        End Try
    End Function

    Function Get_Code_Value_More(ByVal Table_name As String, ByVal Field_Code As String, ByVal crtria As String)
        Try
            Dim Code As String = "0"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where " & crtria
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Code = dr(0).ToString
            End If
            Return Code
        Catch ex As Exception
        End Try
    End Function

    Function PopulateDataView(ByVal dataReader As IDataReader) As DataView
        Dim tableName As String
        tableName = "M"
        Dim dataReaderTable As New DataTable(tableName)
        Try
            For count As Integer = 0 To dataReader.FieldCount - 1
                Dim tempCol As New DataColumn(dataReader.GetName(count), dataReader.GetFieldType(count))
                dataReaderTable.Columns.Add(tempCol)
            Next
            While dataReader.Read()
                Dim drr As DataRow = dataReaderTable.NewRow()
                For i As Integer = 0 To dataReader.FieldCount - 1
                    drr(i) = dataReader.GetValue(dataReader.GetOrdinal(dataReader.GetName(i)))
                Next
                dataReaderTable.Rows.Add(drr)
            End While
            Return dataReaderTable.DefaultView
        Catch
            Return Nothing
        End Try
    End Function

    Sub insert(ByVal tbl_name As String, ByVal fields As String, ByVal values As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "insert into " & tbl_name & " (" & fields & ")values(" & values & ")"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try
    End Sub

    Sub Update_Branch(ByVal tbl_name As String, ByVal fields As String, ByVal values As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "update " & tbl_name & " set " & fields & " where " & values & ""
            Else
                cmd.CommandText = "update " & tbl_name & " set " & fields & " where Company_Branch_ID =N'" & Company_Branch_ID & "' and " & values & ""
            End If
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try
    End Sub

    Sub Update(ByVal tbl_name As String, ByVal fields As String, ByVal values As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update " & tbl_name & " set " & fields & " where " & values & ""
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try
    End Sub

    Sub delete_Branch(ByVal tbl_name As String, ByVal crtria As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "delete from " & tbl_name & " where " & crtria
            Else
                cmd.CommandText = "delete from " & tbl_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' and " & crtria
            End If
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try
    End Sub

    Sub delete_Branch_All(ByVal tbl_name As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                cmd.CommandText = "delete from " & tbl_name & ""
            Else
                cmd.CommandText = "delete from " & tbl_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "'"
            End If
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try
    End Sub

    Sub delete(ByVal tbl_name As String, ByVal crtria As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete from " & tbl_name & " where " & crtria
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try
    End Sub

    Sub clear(ByVal cr As Control)
        Dim c, c1 As Control
        For Each c In cr.Controls
            If TypeOf c Is TextBox Or TypeOf c Is ComboBox Then
                c.Text = ""
            ElseIf TypeOf c Is GroupBox Then
                For Each c1 In c.Controls
                    If TypeOf c1 Is TextBox Or TypeOf c1 Is ComboBox Then
                        c1.Text = ""
                    End If
                Next
            End If
        Next
    End Sub

    Sub fill_combo(ByVal Table_Name As String, ByVal Field_Name As String, ByVal crtria As String, ByVal combo As ComboBox)
        Try
            combo.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select " & Field_Name & " from " & Table_Name & " where " & crtria
            dr = cmd.ExecuteReader
1:          If dr.Read Then
                combo.Items.Add(dr(Field_Name))
                GoTo 1
            End If
        Catch ex As Exception
        End Try
    End Sub

    Function return_brcode(ByVal cat As String, ByVal itm As String)
        Try
            Dim br As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select brcode from itms where cat=N'" & cat & "' and itm=N'" & itm & "'"
            dr = cmd.ExecuteReader
            If dr.Read Then
                br = dr("brcode")
                Return br
            Else
            End If
        Catch ex As Exception
        End Try
    End Function

    Function get_random(ByVal lnth As Integer, ByVal rnd_type As String) As String
        Try
            If rnd_type <> "a" And rnd_type <> "n" Then
                MsgBox("Error Creating Rnd", vbCritical)
                Return "Error Creating Rnd"
                Exit Function
            End If
            If IsNumeric(lnth) = False Then
                MsgBox("Error Creating Rnd", vbCritical)
                Return "Error Creating Rnd"
                Exit Function
            End If
            Dim rndx As New Random
            Dim nm As Integer
            nm = Int(rndx.NextDouble * 100) / 4
            Dim rndm As String
            Select Case rnd_type
                Case "n"
                    For i = 1 To lnth
                        nm = Int(rndx.NextDouble * 10)
                        rndm = rndm + Trim(Str(nm))
                    Next
                Case "a"
                    For i = 1 To lnth
                        nm = Int((rndx.NextDouble * 100) / 4)
                        rndm = rndm + Chr(nm + 97)
                    Next
            End Select
            Return rndm
        Catch ex As Exception
        End Try
    End Function

    Function C_date(ByVal dt As Date) As String
        Dim dy
        Dim mn
        Dim yr
        dy = dt.Day
        If Len(Trim(dy)) = 1 Then dy = "0" + Trim(Str(dy))
        mn = Month(dt)
        If Len(Trim(mn)) = 1 Then mn = "0" + Trim(Str(mn))
        yr = Year(dt)
        Return yr & mn & dy
    End Function

    Function C_date2(ByVal dt As Date) As String
        Dim dy
        Dim mn
        Dim yr

        dy = dt.Day
        If Len(Trim(dy)) = 1 Then dy = "0" + Trim(Str(dy))
        mn = Month(dt)
        If Len(Trim(mn)) = 1 Then mn = "0" + Trim(Str(mn))
        yr = Year(dt)
        Return yr & mn & dy
    End Function

    Function sum_any(ByVal Table_Name As String, ByVal Field_Name As String, ByVal crtria As String) As Double
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(" & Field_Name & ") as sms from " & Table_Name & " where " & crtria
            dr = cmd.ExecuteReader

            If dr.Read Then
                If IsDBNull(dr("sms")) Then
                    Return 0
                Else
                    Dim m = dr("sms")
                    Return m
                End If
            Else
                Return 0
            End If
        Catch ex As Exception
        End Try
    End Function

    Function Get_Count_Pre_Zero(ByVal X As String)
        If Len(X) = 1 Then X = "0" & "0" & X
        If Len(X) = 2 Then X = "0" & X
        If Len(X) = 1 Then X = X

        Return X
    End Function
    Function Set_Count_Down(ByVal X As String)
        If Len(X) = 3 Then X = X
        If Len(X) = 2 Then X = "0" & X
        If Len(X) = 1 Then X = "0" & "0" & X
        Dim M As String
        Dim MX As String

        M = VB.Right(Cls.GenerateHash(Now.Ticks), 20) & VB.Left(Cls.GenerateHash(Now.Ticks), 20)
        MX = VB.Left(M, 13) & VB.Left(X, 1) & VB.Mid(M, 13, 7) & VB.Mid(X, 2, 1) & VB.Mid(M, 21, 12) & VB.Right(X, 1) & VB.Right(M, 8)
        X = MX
        Return X
    End Function
    Function Get_Count_Down(ByVal X As String)
        X = Val(VB.Mid(X, 14, 1) & VB.Mid(X, 22, 1) & VB.Mid(X, 35, 1))
        Return X
    End Function

    Function ConfirmVerssion(ByVal x As String) As Boolean
        Try
            Dim M As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ghost from ghost" : dr = cmd.ExecuteReader : dr.Read() : M = dr("ghost")
            If x = M Then
                Return True
            ElseIf M <> x Then
                Return False
            End If
        Catch ex As Exception
        End Try
    End Function

    Function MAXRECORD(ByVal Table As String, ByVal Felds As String)
        Dim Code As String = "0"
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " + Table + ""
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Code = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" + Felds + " As float)) as mb FROM " + Table + ""
            dr = cmd.ExecuteReader
            dr.Read()
            Dim sh As Long
            sh = dr("mb")
            Code = sh + 1
        End If
        Return Code
    End Function


End Class
