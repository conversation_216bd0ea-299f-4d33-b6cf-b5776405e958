﻿Imports System.Data.SqlClient
Imports System.IO
Imports Excel = Microsoft.Office.Interop.Excel

Public Class Frm_Capital_Owner
    Dim con As New SqlConnection(constring)
    Dim cmd As SqlCommand
    Dim da As SqlDataAdapter
    Dim ds As DataSet
    Dim dt As DataTable
    Dim dr As SqlDataReader
    Dim currentID As Integer = 0
    Dim UserName As String = Environment.UserName
    Dim Company_Branch_ID As Integer = 1 ' يمكن تغييرها حسب الفرع

    Private Sub frmCapitalOwners_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadOwners()
        ClearFields()
        SetButtons(True)
    End Sub

    Private Sub LoadOwners()
        Try
            con.Open()
            cmd = New SqlCommand("SELECT * FROM Capital_Owner ORDER BY Owner_Name", con)
            da = New SqlDataAdapter(cmd)
            ds = New DataSet()
            da.Fill(ds, "Capital_Owner")
            dgvOwners.DataSource = ds.Tables("Capital_Owner")

            With dgvOwners
                .Columns("id").Visible = False
                .Columns("Owner_Code").HeaderText = "كود المالك"
                .Columns("Owner_Name").HeaderText = "اسم المالك"
                .Columns("Owner_Phone").HeaderText = "التليفون"
                .Columns("Owner_Mobile").HeaderText = "المحمول"
                .Columns("Owner_Address").HeaderText = "العنوان"
                .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            End With

        Catch ex As Exception
            MessageBox.Show("حدث خطأ أثناء تحميل البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            con.Close()
        End Try
    End Sub

    Private Sub ClearFields()
        txtID.Text = ""
        txtOwnerCode.Text = GenerateOwnerCode()
        txtOwnerName.Text = ""
        txtPhone.Text = ""
        txtMobile.Text = ""
        txtAddress.Text = ""
        txtOpeningBalancePartner.Text = "0"
        txtOpeningBalanceCapital.Text = "0"
        radPartnerWithdraw.Checked = True
        radCapitalDeposit.Checked = True
        currentID = 0
    End Sub

    Private Function GenerateOwnerCode() As String
        Try
            con.Open()
            cmd = New SqlCommand("SELECT ISNULL(MAX(CAST(Owner_Code AS INT)), 0) + 1 FROM Capital_Owner", con)
            Dim newCode As Integer = cmd.ExecuteScalar()
            Return newCode.ToString("D4")
        Catch ex As Exception
            Return "0001"
        Finally
            con.Close()
        End Try
    End Function

    Private Sub SetButtons(ByVal editMode As Boolean)
        btnNew.Enabled = editMode
        btnSave.Enabled = Not editMode
        btnDelete.Enabled = Not editMode AndAlso currentID > 0
        btnCancel.Enabled = Not editMode

        gbOwnerInfo.Enabled = Not editMode
        gbOpeningBalances.Enabled = Not editMode
    End Sub

    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        ClearFields()
        SetButtons(False)
        txtOwnerName.Focus()
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If Not ValidateData() Then Exit Sub

        Try
            con.Open()
            Dim transaction As SqlTransaction = con.BeginTransaction()

            Try
                ' حفظ بيانات المالك
                If currentID = 0 Then
                    cmd = New SqlCommand("INSERT INTO Capital_Owner (Owner_Code, Owner_Name, Owner_Phone, Owner_Mobile, Owner_Address) " &
                                         "VALUES (@Code, @Name, @Phone, @Mobile, @Address); SELECT SCOPE_IDENTITY();", con, transaction)
                Else
                    cmd = New SqlCommand("UPDATE Capital_Owner SET Owner_Code=@Code, Owner_Name=@Name, Owner_Phone=@Phone, " &
                                         "Owner_Mobile=@Mobile, Owner_Address=@Address WHERE id=@ID", con, transaction)
                    cmd.Parameters.AddWithValue("@ID", currentID)
                End If

                cmd.Parameters.AddWithValue("@Code", txtOwnerCode.Text)
                cmd.Parameters.AddWithValue("@Name", txtOwnerName.Text)
                cmd.Parameters.AddWithValue("@Phone", txtPhone.Text)
                cmd.Parameters.AddWithValue("@Mobile", txtMobile.Text)
                cmd.Parameters.AddWithValue("@Address", txtAddress.Text)

                If currentID = 0 Then
                    currentID = cmd.ExecuteScalar()
                Else
                    cmd.ExecuteNonQuery()
                End If

                ' حفظ الرصيد الافتتاحي لجارى الشركاء
                Dim partnerAmount As Decimal = Decimal.Parse(txtOpeningBalancePartner.Text)
                If partnerAmount <> 0 Then
                    Dim partnerType As Integer = If(radPartnerWithdraw.Checked, 1, 2) ' 1 لسحب، 2 لإيداع
                    SaveOpeningTransaction(transaction, "Capital_Partner_Withdrawals", partnerAmount, partnerType, "رصيد افتتاحي")
                End If

                ' حفظ الرصيد الافتتاحي لرأس المال
                Dim capitalAmount As Decimal = Decimal.Parse(txtOpeningBalanceCapital.Text)
                If capitalAmount <> 0 Then
                    Dim capitalType As Integer = If(radCapitalWithdraw.Checked, 1, 2) ' 1 لسحب، 2 لإيداع
                    SaveOpeningTransaction(transaction, "Capital_Reserve_CheckOut_Deposit", capitalAmount, capitalType, "رصيد افتتاحي")
                End If

                transaction.Commit()
                MessageBox.Show("تم حفظ البيانات بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadOwners()
                ClearFields()
                SetButtons(True)

            Catch ex As Exception
                transaction.Rollback()
                Throw ex
            End Try

        Catch ex As Exception
            MessageBox.Show("حدث خطأ أثناء حفظ البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            con.Close()
        End Try
    End Sub

    Private Sub SaveOpeningTransaction(ByVal transaction As SqlTransaction, ByVal tableName As String, ByVal amount As Decimal, ByVal typeCode As Integer, ByVal notes As String)
        ' توليد رقم جديد للمسحوبات/الإيداعات
        cmd = New SqlCommand("SELECT ISNULL(MAX(CAST(Capital_Number AS INT)), 0) + 1 FROM " & tableName, con, transaction)
        Dim newNumber As Integer = cmd.ExecuteScalar()

        ' الحصول على كود الخزينة الافتراضي
        Dim Treasury_Code As String = GetDefaultTreasuryCode(transaction)

        ' إدخال الحركة
        cmd = New SqlCommand("INSERT INTO " & tableName & " (Company_Branch_ID, Capital_Number, Owner_Code, Treasury_Code, " &
                             "Capital_Type_Code, Amount, Capital_Time, Capital_Date, Notes, UserName) " &
                             "VALUES (@Branch, @Number, @OwnerCode, @Treasury, @TypeCode, @Amount, @Time, @Date, @Notes, @User)", con, transaction)

        cmd.Parameters.AddWithValue("@Branch", Company_Branch_ID)
        cmd.Parameters.AddWithValue("@Number", newNumber)
        cmd.Parameters.AddWithValue("@OwnerCode", txtOwnerCode.Text)
        cmd.Parameters.AddWithValue("@Treasury", Treasury_Code)
        cmd.Parameters.AddWithValue("@TypeCode", typeCode)
        cmd.Parameters.AddWithValue("@Amount", amount)
        cmd.Parameters.AddWithValue("@Time", DateTime.Now.ToString("HH:mm:ss"))
        cmd.Parameters.AddWithValue("@Date", DateTime.Now.Date)
        cmd.Parameters.AddWithValue("@Notes", notes)
        cmd.Parameters.AddWithValue("@User", UserName)

        cmd.ExecuteNonQuery()
    End Sub

    Private Function GetDefaultTreasuryCode(ByVal transaction As SqlTransaction) As String
        ' هنا يمكنك استدعاء الدالة الخاصة بك للحصول على كود الخزينة الافتراضي
        ' أو استخدام قيمة افتراضية كما في المثال التالي:
        Return "0" ' كود الخزينة الافتراضي
    End Function

    Private Function ValidateData() As Boolean
        If String.IsNullOrWhiteSpace(txtOwnerName.Text) Then
            MessageBox.Show("يجب إدخال اسم المالك", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtOwnerName.Focus()
            Return False
        End If

        Dim partnerBal As Decimal
        If Not Decimal.TryParse(txtOpeningBalancePartner.Text, partnerBal) Then
            MessageBox.Show("يجب إدخال قيمة رقمية للرصيد الافتتاحي لجارى الشركاء", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtOpeningBalancePartner.Focus()
            Return False
        End If

        Dim capitalBal As Decimal
        If Not Decimal.TryParse(txtOpeningBalanceCapital.Text, capitalBal) Then
            MessageBox.Show("يجب إدخال قيمة رقمية للرصيد الافتتاحي لرأس المال", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtOpeningBalanceCapital.Focus()
            Return False
        End If

        Return True
    End Function

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        ClearFields()
        SetButtons(True)
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If currentID = 0 Then Exit Sub

        If MessageBox.Show("هل أنت متأكد من حذف هذا المالك؟", "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
            Exit Sub
        End If

        Try
            con.Open()

            ' التحقق من وجود حركات للمالك قبل الحذف
            cmd = New SqlCommand("SELECT COUNT(*) FROM Capital_Partner_Withdrawals WHERE Owner_Code=@Code", con)
            cmd.Parameters.AddWithValue("@Code", txtOwnerCode.Text)
            Dim partnerCount As Integer = cmd.ExecuteScalar()

            cmd = New SqlCommand("SELECT COUNT(*) FROM Capital_Reserve_CheckOut_Deposit WHERE Owner_Code=@Code", con)
            cmd.Parameters.AddWithValue("@Code", txtOwnerCode.Text)
            Dim capitalCount As Integer = cmd.ExecuteScalar()

            If partnerCount > 0 Or capitalCount > 0 Then
                MessageBox.Show("لا يمكن حذف المالك لأنه لديه حركات مسحوبات/إيداعات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Exit Sub
            End If

            ' حذف المالك
            cmd = New SqlCommand("DELETE FROM Capital_Owner WHERE id=@ID", con)
            cmd.Parameters.AddWithValue("@ID", currentID)
            cmd.ExecuteNonQuery()

            MessageBox.Show("تم حذف المالك بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            LoadOwners()
            ClearFields()
            SetButtons(True)

        Catch ex As Exception
            MessageBox.Show("حدث خطأ أثناء حذف البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            con.Close()
        End Try
    End Sub

    Private Sub dgvOwners_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgvOwners.CellClick
        If e.RowIndex >= 0 Then
            Dim row As DataGridViewRow = dgvOwners.Rows(e.RowIndex)
            currentID = Integer.Parse(row.Cells("id").Value.ToString())
            txtID.Text = currentID.ToString()
            txtOwnerCode.Text = row.Cells("Owner_Code").Value.ToString()
            txtOwnerName.Text = row.Cells("Owner_Name").Value.ToString()
            txtPhone.Text = row.Cells("Owner_Phone").Value.ToString()
            txtMobile.Text = row.Cells("Owner_Mobile").Value.ToString()
            txtAddress.Text = row.Cells("Owner_Address").Value.ToString()

            LoadOpeningBalances(txtOwnerCode.Text)
            SetButtons(False)
        End If
    End Sub

    Private Sub LoadOpeningBalances(ownerCode As String)
        Try
            con.Open()

            ' جارى الشركاء
            cmd = New SqlCommand("SELECT SUM(CASE WHEN Capital_Type_Code=1 THEN Amount ELSE -Amount END) " &
                                "FROM Capital_Partner_Withdrawals WHERE Owner_Code=@Code AND Notes='رصيد افتتاحي'", con)
            cmd.Parameters.AddWithValue("@Code", ownerCode)
            Dim partnerBalance As Object = cmd.ExecuteScalar()
            txtOpeningBalancePartner.Text = If(partnerBalance Is DBNull.Value, "0", Math.Abs(Decimal.Parse(partnerBalance.ToString())).ToString())

            If Decimal.Parse(If(partnerBalance Is DBNull.Value, "0", partnerBalance.ToString())) >= 0 Then
                radPartnerWithdraw.Checked = True ' سحب (مدين)
            Else
                radPartnerDeposit.Checked = True ' إيداع (دائن)
            End If

            ' رأس المال
            cmd = New SqlCommand("SELECT SUM(CASE WHEN Capital_Type_Code=2 THEN Amount ELSE -Amount END) " &
                                "FROM Capital_Reserve_CheckOut_Deposit WHERE Owner_Code=@Code AND Notes='رصيد افتتاحي'", con)
            cmd.Parameters.AddWithValue("@Code", ownerCode)
            Dim capitalBalance As Object = cmd.ExecuteScalar()
            txtOpeningBalanceCapital.Text = If(capitalBalance Is DBNull.Value, "0", Math.Abs(Decimal.Parse(capitalBalance.ToString())).ToString())

            If Decimal.Parse(If(capitalBalance Is DBNull.Value, "0", capitalBalance.ToString())) >= 0 Then
                radCapitalDeposit.Checked = True ' إيداع (دائن)
            Else
                radCapitalWithdraw.Checked = True ' سحب (مدين)
            End If

        Catch ex As Exception
            MessageBox.Show("حدث خطأ أثناء تحميل الأرصدة الافتتاحية: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            con.Close()
        End Try
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        Try
            con.Open()
            Dim query As String = "SELECT * FROM Capital_Owner WHERE Owner_Name LIKE @Search OR Owner_Code LIKE @Search ORDER BY Owner_Name"
            cmd = New SqlCommand(query, con)
            cmd.Parameters.AddWithValue("@Search", "%" & txtSearch.Text & "%")
            da = New SqlDataAdapter(cmd)
            ds = New DataSet()
            da.Fill(ds, "Capital_Owner")
            dgvOwners.DataSource = ds.Tables("Capital_Owner")
        Catch ex As Exception
            MessageBox.Show("حدث خطأ أثناء البحث: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            con.Close()
        End Try
    End Sub

    Private Sub btnExportExcel_Click(sender As Object, e As EventArgs) Handles btnExportExcel.Click
        If dgvOwners.Rows.Count = 0 Then
            MessageBox.Show("لا توجد بيانات لتصديرها", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Exit Sub
        End If

        Try
            Dim excelApp As New Excel.Application
            Dim excelWorkbook As Excel.Workbook = excelApp.Workbooks.Add()
            Dim excelWorksheet As Excel.Worksheet = CType(excelWorkbook.Sheets(1), Excel.Worksheet)

            For i As Integer = 0 To dgvOwners.Columns.Count - 1
                If dgvOwners.Columns(i).Visible Then
                    excelWorksheet.Cells(1, i + 1) = dgvOwners.Columns(i).HeaderText
                End If
            Next

            For i As Integer = 0 To dgvOwners.Rows.Count - 1
                For j As Integer = 0 To dgvOwners.Columns.Count - 1
                    If dgvOwners.Columns(j).Visible Then
                        excelWorksheet.Cells(i + 2, j + 1) = dgvOwners.Rows(i).Cells(j).Value.ToString()
                    End If
                Next
            Next

            excelWorksheet.Columns.AutoFit()
            CType(excelWorksheet.Rows(1, Type.Missing), Excel.Range).Font.Bold = True

            Dim saveDialog As New SaveFileDialog()
            saveDialog.Filter = "Excel Files|*.xlsx"
            saveDialog.Title = "حفظ ملف Excel"
            saveDialog.FileName = "أسماء الملاك_" & DateTime.Now.ToString("yyyyMMdd")

            If saveDialog.ShowDialog() = DialogResult.OK Then
                excelWorkbook.SaveAs(saveDialog.FileName)
                MessageBox.Show("تم تصدير البيانات بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

            excelWorkbook.Close()
            excelApp.Quit()

            ReleaseObject(excelWorksheet)
            ReleaseObject(excelWorkbook)
            ReleaseObject(excelApp)

        Catch ex As Exception
            MessageBox.Show("حدث خطأ أثناء التصدير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ReleaseObject(ByVal obj As Object)
        Try
            System.Runtime.InteropServices.Marshal.ReleaseComObject(obj)
            obj = Nothing
        Catch ex As Exception
            obj = Nothing
        Finally
            GC.Collect()
        End Try
    End Sub

    Private Sub txtOpeningBalancePartner_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtOpeningBalancePartner.KeyPress
        If Not Char.IsControl(e.KeyChar) AndAlso Not Char.IsDigit(e.KeyChar) AndAlso e.KeyChar <> "." Then
            e.Handled = True
        End If

        If e.KeyChar = "." AndAlso DirectCast(sender, TextBox).Text.IndexOf(".") > -1 Then
            e.Handled = True
        End If
    End Sub

    Private Sub txtOpeningBalanceCapital_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtOpeningBalanceCapital.KeyPress
        If Not Char.IsControl(e.KeyChar) AndAlso Not Char.IsDigit(e.KeyChar) AndAlso e.KeyChar <> "." Then
            e.Handled = True
        End If

        If e.KeyChar = "." AndAlso DirectCast(sender, TextBox).Text.IndexOf(".") > -1 Then
            e.Handled = True
        End If
    End Sub
End Class