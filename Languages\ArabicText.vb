﻿Imports System.Data.SqlClient
Public Class ArabicText

    '=========================================== Manu ====================================================
    Friend Items As String = "الأصناف"
    Friend Purchases As String = "المشتريات"
    Friend Sales As String = "المبيعات"
    Friend Returns As String = "مرتجعات"
    Friend Expenses As String = "المصروفات"
    Friend Employees As String = "الموظفين"
    Friend Delegates As String = "المندوبين"
    Friend Maintenance As String = "الصيانة"
    Friend Manufacturing As String = "التصنيع"
    Friend Banks As String = "البنوك"
    Friend Accounts As String = "الحسابات"
    Friend FinancialReports As String = "تقارير مالية"
    Friend StoresReports As String = "تقارير مخازن"
    Friend ItemsReports As String = "تقارير أصناف"
    Friend ReportsCertainPeriod As String = "تقارير فترة معينة"
    Friend UserReports As String = "تقارير مستخدمين"
    Friend Tools As String = "ادوات"

    '=========================================== Items ====================================================
    Friend RegisterGroupsItems As String = "تسجيل مجموعات الأصناف"
    Friend RegisterBranchesItems As String = "تسجيل فروع الاصناف"
    Friend RegisterItems As String = "تسجيل  أصناف"
    Friend ModifiedItems As String = "تعديل أصناف"
    Friend SearchItems As String = "بحث عن الاصناف"
    Friend ConvertBetweenStores As String = "أذن تحويل بين المخازن"
    Friend MaxAndMinPriceItems As String = "زيادة ونقصان أسعار الاصناف بالنسب"
    Friend OffersDiscounts As String = "عروض الخصومات"
    Friend DelegatesManagement As String = "إدارة المندوبين"
    Friend AmountDisabilityAndIncreaseDelegates As String = "مبلغ العجز والزيادة للمندوبين"
    Friend WithdrawalsAndDepositsCapital As String = "مسحوبات وأيداعات رأس المال"
    Friend ProductManufacturing As String = "تصنيع المنتج"
    Friend Treasury As String = "الخزينة"
    Friend Exits As String = "خروج"

    '=========================================== Purchases ====================================================
    Friend SupplierDataRegistration As String = "تسجيل بيانات موردين"
    Friend PurchasesProcess As String = "إجراء عملية مشتريات"
    Friend ModifyAndDeletePurchases As String = "تعديل وحذف عمليات المشتريات"
    Friend ViewAndPrintPurchases As String = "عرض وطباعة عمليات المشتريات"
    Friend ReportDailyPurchases As String = "تقرير بالمشتريات اليومية"
    Friend SupplierPayments As String = "مدفوعات الموردين"
    Friend DeleteSupplierPayments As String = "حذف مدفوعات الموردين"
    Friend SupplierDiscounts As String = "خصومات الموردين"
    Friend DeleteSupplierDiscounts As String = "حذف خصومات الموردين"

    '=========================================== Sales ====================================================
    Friend SalesScreen As String = "المبيعات"
    Friend FastSales As String = "مبيعات سريعة"
    Friend Sheft As String = "الشفتات"
    Friend DeleteAndModifySales As String = "حذف وتعديل المبيعات"
    Friend ViewAndPrintSales As String = "عرض وطباعة المبيعات"
    Friend DailySalesReport As String = "تقرير بالمبيعات اليومية"
    Friend DetailedSalesReport As String = "تقرير المبيعات مفصل"
    Friend SalesPricesForTheCustomer As String = "أسعار البيع للعميل"
    Friend CustomerData As String = "بيانات العملاء"
    Friend CustomerPayments As String = "مقبوضات العملاء"
    Friend ChecksPayable As String = "شيكات مستحقة الدفع"
    Friend DeleteCustomerPayments As String = "حـذف مقبوضات العملاء"
    Friend CustomerDiscounts As String = "خصومات العملاء"
    Friend DeleteCustomerDiscounts As String = "حـذف خصومات العملاء"
    Friend OtherDiscounts As String = "خصومات أخرى"

    '=============== Returns ==============================
    Friend RecordPurchaseReturns As String = "تسجل مرتجعات مشتربات"
    Friend DeleteAndModifyPurchaseReturns As String = "حذف وتعديل مرتجعات مشتربات"
    Friend DefineAndPrintPurchaseReturns As String = "عرف وطباعة مرتجعات مشتربات"
    Friend DailyPurchaseReturnsReport As String = "تقرير بمرتجعات المشتربات اليومية"
    Friend RecordSalesReturns As String = "تسجل مرتجعات مبيعات"
    Friend DeleteAndModifySalesReturns As String = "حذف وتعديل مرتجعات مبيعات"
    Friend DefineAndPrintSalesReturns As String = "عرف وطباعة مرتجعات مبيعات"
    Friend DailySalesReturnsReport As String = "تقرير بمرتجعات المبيعات اليومية"
    Friend RecordDamages As String = "تسجل توالف"
    Friend DefineAndDeleteDamageReturns As String = "عرض وحذف مرتجعات التوالف"

    '=========================================== Store ====================================================
    Friend RegisterStores As String = "تسجيل المخازن"
    Friend Store As String = "المخزن"
    Friend Stores As String = "المخازن"
    Friend StoreType As String = "نوع المخزن"
    Friend Main As String = "رئيسى"
    Friend Subs As String = "فرعى"
    Friend News As String = "جديد"
    Friend Save As String = "حفظ"
    Friend Edit As String = "تعديل"
    Friend Delete As String = "حذف"
    Friend Added As String = "إضافة"
    Friend PleaseEnterValidData As String = "من فضلك ادخل بيانات صحيحة"
    Friend SorryRecordedStatementBy As String = "عفوا بيان مسجل من قبل"
    Friend SuccessfullySaved As String = "تم الحفظ بنجاح"
    Friend PleaseSelectValidStatement As String = "من فضلك اختر بيان صحيح"

    '=========================================== ADD ====================================================
    Friend HideInSearch As String = "اخفاء فى البحث"
    Friend HideTheGroup As String = "اخفاء المجموعة"
    Friend ChooseGroupPhoto As String = "اختيار صورة المجموعة"
    Friend GroupName As String = "أسم المجموعة"


End Class
