﻿Public Class FrmSales_SerialNumber
    Dim Dt_AddBill As New DataTable

    Dim BillSerialNumber As Double

    Private Sub FrmSales_SerialNumber_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        GetSerial_Number()
        txtSerialNumber.Focus()

        If SN_View = "View" Then
            btnAdd.Visible = False
            BtnDelete.Visible = False
            txtSerialNumber.Enabled = False
            SN_View = ""
        End If
    End Sub


    Friend Function Fn_AddBillSerial(ByVal Col_Serial_Auto As Double, ByVal Col_Serial_Number As String, ByVal Col_Barcode As String, ByVal Col_Store As String) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("م", GetType(String))
            Dt_AddBill.Columns.Add("سيريال", GetType(String))
            Dt_AddBill.Columns.Add("الباركود", GetType(String))
            Dt_AddBill.Columns.Add("المخزن", GetType(String))
        End If

        Dt_AddBill.Rows.Add(Col_Serial_Auto, Col_Serial_Number, Col_Barcode, Col_Store)


        Return Dt_AddBill
    End Function

    Private Sub GetSerial_Number()
        Dim BillSerialNumber As Double = 0
        Dt_AddBill.Rows.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "select Serial_Number_Name,itm_id,Stores from BillsalData_SerialNumber where bill_no =N'" & SN_billno & "' and itm_id =N'" & SN_Barcode & "'" : cmd.CommandText = S : dr = cmd.ExecuteReader
        Do While dr.Read
            BillSerialNumber += 1
            DTGVSerialNumber.DataSource = Fn_AddBillSerial(BillSerialNumber, dr(0).ToString, dr(1).ToString, dr(2).ToString)
            DTGVSerialNumber.Sort(DTGVSerialNumber.Columns(0), System.ComponentModel.ListSortDirection.Ascending)
            DTGVSerialNumber.Columns(2).Visible = False
            DTGVSerialNumber.Columns(3).Visible = False
        Loop
    End Sub

    Private Sub btnAdd_Click(sender As Object, e As EventArgs) Handles btnAdd.Click
        Try
            If SN_Qunt = DTGVSerialNumber.RowCount Then
                MsgBox("السريلات المضافة وصلت لحد الكمية المسموح به  " + SN_Qunt + " ", MsgBoxStyle.Exclamation) : Exit Sub
            End If
            If txtSerialNumber.Text = "" Then MsgBox("فضلا ادخل رقم السيريال", MsgBoxStyle.Exclamation) : txtSerialNumber.Focus() : Exit Sub

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from BillsalData_SerialNumber where itm_id =N'" & SN_Barcode & "' and Serial_Number_Name =N'" & txtSerialNumber.Text & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                MsgBox("رقم السيريال مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtSerialNumber.Focus() : Exit Sub
            End If

            BillSerialNumber += 1
            DTGVSerialNumber.DataSource = Fn_AddBillSerial(BillSerialNumber, txtSerialNumber.Text, SN_Barcode, SN_Store)
            DTGVSerialNumber.Sort(DTGVSerialNumber.Columns(0), System.ComponentModel.ListSortDirection.Ascending)

            txtRowCount.Text = DTGVSerialNumber.RowCount
            DTGVSerialNumber.Columns(2).Visible = False
            DTGVSerialNumber.Columns(3).Visible = False


            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into BillsalData_SerialNumber(Auto_Number,Serial_Number_Name,itm_id,Stores,bill_no,Company_Branch_ID)  values("
            S = S & "N'" & BillSerialNumber & "',N'" & txtSerialNumber.Text & "',N'" & SN_Barcode & "',N'" & SN_Store & "',N'" & SN_billno & "',N'" & Company_Branch_ID & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            txtSerialNumber.Text = ""
            txtSerialNumber.Focus()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub DTGVSerialNumber_CellValueChanged(sender As Object, e As DataGridViewCellEventArgs) Handles DTGVSerialNumber.CellValueChanged

        If DTGVSerialNumber.RowCount = 0 Then Beep() : Exit Sub
        If (DTGVSerialNumber.Rows.Count) = 0 Then Beep() : Exit Sub
        Dim Serial_Number_Name As String = DTGVSerialNumber.SelectedRows(0).Cells(1).Value
        Dim itm_id As String = DTGVSerialNumber.SelectedRows(0).Cells(2).Value
        Dim Stores As String = DTGVSerialNumber.SelectedRows(0).Cells(3).Value
        Dim Auto_Number As String = DTGVSerialNumber.SelectedRows(0).Cells(0).Value

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from BillsalData_SerialNumber where Serial_Number_Name =N'" & Serial_Number_Name & "'" : H = cmd.ExecuteScalar
        If H = 1 Then
            MsgBox("رقم السيريال مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtSerialNumber.Focus() : Exit Sub
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update BillsalData_SerialNumber set Serial_Number_Name =N'" & Serial_Number_Name & "',itm_id =N'" & itm_id & "',Stores =N'" & Stores & "' where bill_No =N'" & SN_billno & "' and itm_id =N'" & itm_id & "' and Auto_Number =N'" & Auto_Number & "'" : cmd.ExecuteNonQuery()


    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs) Handles BtnDelete.Click
        'Try
        Dim RNXD As Integer
        If DTGVSerialNumber.RowCount = 0 Then Beep() : Exit Sub
        If (DTGVSerialNumber.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        For i As Integer = 0 To DTGVSerialNumber.SelectedRows.Count - 1
            Dim Serial_Number_Name As String = DTGVSerialNumber.SelectedRows(0).Cells(1).Value
            Dim itm_id As String = DTGVSerialNumber.SelectedRows(0).Cells(2).Value
            Dim Auto_Number As String = DTGVSerialNumber.SelectedRows(0).Cells(0).Value

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete from BillsalData_SerialNumber where bill_No =N'" & SN_billno & "' and itm_id =N'" & itm_id & "' and Auto_Number =N'" & Auto_Number & "'" : cmd.ExecuteNonQuery()
        Next

        For i As Integer = 0 To DTGVSerialNumber.SelectedRows.Count - 1
            RNXD = DTGVSerialNumber.CurrentRow.Index
            DTGVSerialNumber.Rows.RemoveAt(RNXD)
        Next

        'Catch ex As Exception
        '    'ErrorHandling(ex, Me.Text)
        'End Try
    End Sub

    Private Sub txtSerialNumber_KeyUp(sender As Object, e As KeyEventArgs) Handles txtSerialNumber.KeyUp
        If e.KeyCode = 13 Then
            btnAdd.PerformClick()
        End If
    End Sub

End Class