﻿Public Class FrmFilter
    Private Sub FrmFilter_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        FilterSelect = mykey.GetValue("FilterSelect", "Number")

        If FilterSelect = "Number" Then
            rdoNumberFilter.Checked = True
        End If
        If FilterSelect = "Date" Then
            rdoDateFilter.Checked = True
        End If
        If FilterSelect = "Name" Then
            rdoNameFilter.Checked = True
        End If

    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        If rdoNumberFilter.Checked = True Then
            mykey.SetValue("FilterSelect", "Number")
            FilterSelect = "Number"
        End If
        If rdoDateFilter.Checked = True Then
            mykey.SetValue("FilterSelect", "Date")
            FilterSelect = "Date"
        End If
        If rdoNameFilter.Checked = True Then
            mykey.SetValue("FilterSelect", "Name")
            FilterSelect = "Name"
        End If
        Me.Close()

    End Sub
End Class