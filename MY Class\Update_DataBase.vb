﻿Imports System.Data.SqlClient
Imports vb = Microsoft.VisualBasic
Public Class Update_DataBase
    Dim CodeID As Integer
    Dim TotalPriceBeforeAverage As Double
    Dim TinPriceAverage, TotalPrice, Profits, StoreItems As Double
    Dim MAXID As String = ""

    Public Sub CREATE_TABLE()
        connect()

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from ItemsAlternative"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE ItemsAlternative (" &
            "[Medicament_Name_ID] [int] IDENTITY(1,1) Not NULL, " &
            "itm_id nvarchar(50), " &
            "itm_idTradeName nvarchar(50), " &
            "TradeName   nvarchar(100), " &
            "ScientificName   nvarchar(100), " &
            "DrugConcentration   nvarchar(50), " &
            "UserName   nvarchar(50), " &
            "Company_Branch_ID   int " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from ItemsOffersDiscountsTotal"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE ItemsOffersDiscountsTotal (" &
            "[ID] [int] IDENTITY(1,1) Not NULL, " &
            "bill_No float PRIMARY KEY, " &
            "OffersStatement nvarchar(50), " &
            "bill_date nvarchar(50), " &
            "bill_date_End   nvarchar(50), " &
            "qu_total   float, " &
            "UserName   nvarchar(50), " &
            "Company_Branch_ID   int " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from TreasuryMovement_Deposit"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE TreasuryMovement_Deposit (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "TreasuryMovement_ID float PRIMARY KEY, " &
            "Treasury_Code int, " &
            "Treasury_Amount   float, " &
            "ACCNumber   int, " &
            "Treasury_Date   nvarchar(50), " &
            "Treasury_Time   nvarchar(50), " &
            "Notes   nvarchar(250), " &
            "EMPID   int, " &
            "Company_Branch_ID   int, " &
            "UserName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from TreasuryMovement_Withdraw"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE TreasuryMovement_Withdraw (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "TreasuryMovement_ID float PRIMARY KEY, " &
            "Treasury_Code int, " &
            "Treasury_Amount   float, " &
            "ACCNumber   int, " &
            "Treasury_Date   nvarchar(50), " &
            "Treasury_Time   nvarchar(50), " &
            "Notes   nvarchar(250), " &
            "EMPID   int, " &
            "Company_Branch_ID   int, " &
            "UserName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Capital_Reserve_CheckOut_Deposit"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '"Id int Not NULL PRIMARY KEY, " &
            cmd.CommandText = "CREATE TABLE Capital_Reserve_CheckOut_Deposit (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "Capital_Number int PRIMARY KEY, " &
            "Owner_Code int, " &
            "Capital_Type_Code   int, " &
            "Treasury_Code   int, " &
            "Amount   float, " &
            "Capital_Time   nvarchar(50), " &
            "Capital_Date   nvarchar(50), " &
            "Notes   nvarchar(250), " &
            "UserName   nvarchar(50), " &
            "Company_Branch_ID   int " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Maintenance_ReceivingCarAdd"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Maintenance_ReceivingCarAdd (" &
            "ReceivingCarAddID  int PRIMARY KEY Not NULL, " &
            "OrderID  int, " &
            "ReceivingCar_ID   int, " &
            "UserName   nvarchar(50), " &
            "Company_Branch_ID   int " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from PriceOffer"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE PriceOffer (" &
            "id  int PRIMARY KEY Not NULL, " &
            "bill_no  float, " &
            "Cust_Code   int, " &
            "totalprice   float, " &
            "bill_date   nvarchar(50), " &
            "UserName   nvarchar(50), " &
            "Company_Branch_ID   int " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from PriceOfferBill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE PriceOfferBill (" &
            "id  int PRIMARY KEY Not NULL, " &
            "bill_no  float, " &
            "Cust_Code   int, " &
            "itm_id   nvarchar(50), " &
            "itm_cat   nvarchar(50), " &
            "itm_name   nvarchar(200), " &
            "Unity   nvarchar(50), " &
            "price  float, " &
            "qu  float, " &
            "totalprice  float, " &
            "bill_date   nvarchar(50), " &
            "UserName   nvarchar(50), " &
            "Company_Branch_ID   int " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from MaintenanceOrderRunningAdd"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE MaintenanceOrderRunningAdd (" &
            "OrderAdd_ID  int PRIMARY KEY Not NULL, " &
            "OrderID  int, " &
            "MaintenanceType_ID   int, " &
            "RequiredRepair   nvarchar(MAX), " &
            "Company_Branch_ID   int " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Group_Branch"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Group_Branch (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "branch_ID  int, " &
            "Group_Name_ID   nvarchar(50), " &
            "branch_Name   nvarchar(50), " &
            "UserName   nvarchar(50), " &
            "Company_Branch_ID   int " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Other_Income"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Other_Income (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "Income_ID  float, " &
            "Income_Category_ID int, " &
            "Income_Name   nvarchar(100), " &
            "Income_Date   nvarchar(50), " &
            "Amount   float, " &
            "Note   nvarchar(MAX), " &
            "UserName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Other_Income_Category"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Other_Income_Category (" &
            "Income_Category_ID  int PRIMARY KEY Not NULL, " &
            "Income_Category_Name   nvarchar(100) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Maintenance_ReceivingCar"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Maintenance_ReceivingCar (" &
            "ReceivingCar_ID  int PRIMARY KEY Not NULL, " &
            "ReceivingCar   nvarchar(100) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Maintenance_Recipient"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Maintenance_Recipient (" &
            "Recipient_ID  int PRIMARY KEY Not NULL, " &
            "Recipient   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Maintenance_Supervisor"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Maintenance_Supervisor (" &
            "Supervisor_ID  int PRIMARY KEY Not NULL, " &
            "Supervisor   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Company_Branch"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Company_Branch (" &
            "Company_Branch_ID  int PRIMARY KEY Not NULL, " &
            "CMPSerial int, " &
            "Company_Branch_Name nvarchar(50), " &
            "CMPBAddress   nvarchar(255), " &
            "CMPBTel   nvarchar(255), " &
            "CMPBMobile   nvarchar(255), " &
            "CMPBFax   nvarchar(255), " &
            "CMPBEmail   nvarchar(255) " &
            ") "
            cmd.ExecuteNonQuery()

            Cls.insert("Company_Branch", "Company_Branch_ID,CMPSerial,Company_Branch_Name", "'1',N'1',N'الفرع الرئيسى'")
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from ErrorMessage"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE ErrorMessage (" &
            "Error_ID  int PRIMARY KEY Not NULL, " &
            "TitleForm nvarchar(200), " &
            "LineNumber   nvarchar(50), " &
            "MethodsName   nvarchar(200), " &
            "ErrorMessage   nvarchar(MAX), " &
            "ErrorDate   nvarchar(50), " &
            "ErrorTime   nvarchar(50), " &
            "UserName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from ItemsUnitySize"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE ItemsUnitySize (" &
            "UnitySize_ID  int PRIMARY KEY Not NULL, " &
            "UnitySize_Name   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()

            Cls.insert("ItemsUnitySize", "UnitySize_ID,UnitySize_Name", "'1',N'الاصغر'")
            Cls.insert("ItemsUnitySize", "UnitySize_ID,UnitySize_Name", "'2',N'المتوسط'")
            Cls.insert("ItemsUnitySize", "UnitySize_ID,UnitySize_Name", "'3',N'الاكبر'")
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Vnd_Receipts"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Vnd_Receipts (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "Vendorname  nvarchar(50), " &
            "VND_XTM nvarchar(50), " &
            "VND_dt   nvarchar(50), " &
            "VND_amx   float, " &
            "VND_ho   nvarchar(50), " &
            "VND_rcv   nvarchar(50), " &
            "VND_dec   nvarchar(50), " &
            "VND_no   nvarchar(50), " &
            "BillNo   nvarchar(50), " &
            "Sales_Bill_NO   nvarchar(50), " &
            "UserName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from ItemsDeleted"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE ItemsDeleted (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "delete_date  nvarchar(50), " &
            "itm_id   nvarchar(50), " &
            "group_name nvarchar(50), " &
            "group_branch   nvarchar(50), " &
            "sname   nvarchar(200), " &
            "Unity   nvarchar(50), " &
            "rng   float, " &
            "TinPrice   float, " &
            "TinPriceAverage   float, " &
            "SalPrice   float, " &
            "WholePrice   float, " &
            "WholeWholePrice   float, " &
            "MinimumSalPrice   float, " &
            "RatePriceOffers   float, " &
            "store   float, " &
            "Stores   nvarchar(50), " &
            "QuickSearch   float, " &
            "UserName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from EmployeesSalarySystem"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE EmployeesSalarySystem (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "SalarySystem_ID  int, " &
            "SalarySystemName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()

            Cls.insert("EmployeesSalarySystem", "SalarySystem_ID,SalarySystemName", "'1',N'شهر'")
            Cls.insert("EmployeesSalarySystem", "SalarySystem_ID,SalarySystemName", "'2',N'يوم'")
            Cls.insert("EmployeesSalarySystem", "SalarySystem_ID,SalarySystemName", "'3',N'الاسبوع الاول'")
            Cls.insert("EmployeesSalarySystem", "SalarySystem_ID,SalarySystemName", "'4',N'الاسبوع الثانى'")
            Cls.insert("EmployeesSalarySystem", "SalarySystem_ID,SalarySystemName", "'5',N'الاسبوع الثالث'")
            Cls.insert("EmployeesSalarySystem", "SalarySystem_ID,SalarySystemName", "'6',N'الاسبوع الرابع'")
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from ManufacturingFillOrder"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE ManufacturingFillOrder (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "bill_no  nvarchar(50), " &
            "bill_date   nvarchar(50), " &
            "itm_id_Manufacturing nvarchar(50), " &
            "Stores_Manufacturing   nvarchar(50), " &
            "Stores_From   nvarchar(50), " &
            "Stores_TO   nvarchar(50), " &
            "Total_qu   float, " &
            "TotalCostPrice   float, " &
            "EMPID   int, " &
            "UserName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from ManufacturingDismissalNotice"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE ManufacturingDismissalNotice (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "bill_no  nvarchar(50), " &
            "bill_date   nvarchar(50), " &
            "itm_id_Manufacturing nvarchar(50), " &
            "Stores_From   nvarchar(50), " &
            "Stores_TO   nvarchar(50), " &
            "qunt_Manufacturing   float, " &
            "TotalCostPrice   float, " &
            "TotalWeight   float, " &
            "EMPID   int, " &
            "UserName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Vst_Receipts"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Vst_Receipts (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "Vendorname  nvarchar(50), " &
            "VND_XTM nvarchar(50), " &
            "VND_dt   nvarchar(50), " &
            "VND_Date_Maturity   nvarchar(50), " &
            "VND_amx   float, " &
            "VND_ho   nvarchar(50), " &
            "VND_rcv   nvarchar(50), " &
            "VND_dec   nvarchar(50), " &
            "VND_no   nvarchar(50), " &
            "BillNo   nvarchar(50), " &
            "UserName   nvarchar(50), " &
            "EmpName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from ManufacturingProduct"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE ManufacturingProduct (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "Manufacturing_ID  int, " &
            "itm_id_Manufacturing nvarchar(50), " &
            "Stores_Manufacturing   nvarchar(50), " &
            "UserName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from ManufacturingProductAdd"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE ManufacturingProductAdd (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "Manufacturing_ID  int, " &
            "itm_id nvarchar(50), " &
            "Stores   nvarchar(50), " &
            "qu   float, " &
            "NumberMaterial   int, " &
            "UserName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Manufacturing_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Manufacturing_BillsalData (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "bill_no  nvarchar(50), " &
            "itm_id nvarchar(50), " &
            "itm_Unity   nvarchar(50), " &
            "qu   float, " &
            "qu_unity   float, " &
            "price   float, " &
            "totalprice   float, " &
            "Stores   nvarchar(50), " &
            "bill_date   nvarchar(50), " &
            "UserName   nvarchar(50), " &
            "itm_id_Manufacturing   nvarchar(50), " &
            "EMPID   int, " &
            "bill_noNotice   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Manufacturing_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Manufacturing_BilltINData (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "bill_no  nvarchar(50), " &
            "itm_id nvarchar(50), " &
            "itm_Unity   nvarchar(50), " &
            "qu   float, " &
            "qu_unity   float, " &
            "price   float, " &
            "totalprice   float, " &
            "Stores   nvarchar(50), " &
            "bill_date   nvarchar(50), " &
            "UserName   nvarchar(50), " &
            "TinPriceAverage   float, " &
            "bill_noNotice   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from ItemsTransfer_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE ItemsTransfer_BillsalData (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "bill_no  nvarchar(50), " &
            "itm_id nvarchar(50), " &
            "itm_Unity   nvarchar(50), " &
            "qu   float, " &
            "qu_unity   float, " &
            "Stores   nvarchar(50), " &
            "bill_date   nvarchar(50), " &
            "EMPID   int, " &
            "UserName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from ItemsTransfer_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE ItemsTransfer_BilltINData (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "bill_no  nvarchar(50), " &
            "itm_id nvarchar(50), " &
            "itm_Unity   nvarchar(50), " &
            "qu   float, " &
            "qu_unity   float, " &
            "Stores   nvarchar(50), " &
            "bill_date   nvarchar(50), " &
            "EMPID   int, " &
            "UserName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()

            '==========================================================
            Dim aray_bill_no, aray_itm_id, aray_qu, aray_StoresTO, aray_StoresFrom, aray_bill_date, aray_UserName As New ArrayList

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_no,itm_id,qu,StoresTO,StoresFrom,bill_date,UserName from ItemsTransferData"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_bill_no.Add(dr(0))
                aray_itm_id.Add(dr(1))
                aray_qu.Add(dr(2))
                aray_StoresTO.Add(dr(3))
                aray_StoresFrom.Add(dr(4))
                aray_bill_date.Add(dr(5))
                aray_UserName.Add(dr(6))
            Loop

            Dim StausMainStore As String = ""
            Dim qu As String = ""
            For i As Integer = 0 To aray_bill_no.Count - 1
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select StausMainStore from Stores where store =N'" & aray_StoresTO(i).ToString & "'" : dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    StausMainStore = dr(0).ToString
                End If

                If StausMainStore = 1 Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set qu =0,qu_unity =0 where itm_id=N'" & aray_itm_id(i).ToString & "' and Stores=N'" & aray_StoresTO(i).ToString & "'  and bill_no=N'جرد'" : cmd.ExecuteNonQuery()
                End If

                'مشتريات تحويل
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into ItemsTransfer_BilltINData (Company_Branch_ID,bill_no,itm_id,itm_Unity,qu,qu_unity,Stores,bill_date,EMPID,UserName)  values("
                S = S & "N'1',N'" & aray_bill_no(i).ToString & "',N'" & aray_itm_id(i).ToString & "',N'قطعة',N'" & aray_qu(i).ToString & "',N'" & aray_qu(i).ToString & "',N'" & aray_StoresTO(i).ToString & "',N'" & aray_bill_date(i).ToString & "',0,N'" & aray_UserName(i).ToString & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                'مبيعات تحويل
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into ItemsTransfer_BillsalData (Company_Branch_ID,bill_no,itm_id,itm_Unity,qu,qu_unity,Stores,bill_date,EMPID,UserName)  values("
                S = S & "N'1',N'" & aray_bill_no(i).ToString & "',N'" & aray_itm_id(i).ToString & "',N'قطعة',N'" & aray_qu(i).ToString & "',N'" & aray_qu(i).ToString & "',N'" & aray_StoresFrom(i).ToString & "',N'" & aray_bill_date(i).ToString & "',0,N'" & aray_UserName(i).ToString & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                IM.Store(aray_itm_id(i).ToString, aray_StoresFrom(i).ToString)
                IM.Store(aray_itm_id(i).ToString, aray_StoresTO(i).ToString)

                If ConnectOnlineStore = "YES" Then
                    EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", aray_itm_id(i).ToString)
                    StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", aray_itm_id(i).ToString)
                    Cos.UpdateProductStock(StockOnline, aray_itm_id(i).ToString, EditItmId)
                End If
            Next
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from MaintenanceOrderRunning"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '"Id int Not NULL PRIMARY KEY, " &
            cmd.CommandText = "CREATE TABLE MaintenanceOrderRunning (" &
            "[OrderDayID] [int] IDENTITY(1,1) Not NULL, " &
            "OrderID  int PRIMARY KEY, " &
            "Customer_ID nvarchar(50), " &
            "OrderDate   nvarchar(50), " &
            "Delivery_Date   nvarchar(50), " &
            "Visit_Date   nvarchar(50), " &
            "OrderTime   nvarchar(50), " &
            "TimeAMPM   nvarchar(50), " &
            "KiloMeter   nvarchar(50), " &
            "CarNumber   nvarchar(50), " &
            "TypeProduct_ID   int, " &
            "Supervisor   nvarchar(50), " &
            "Recipient   nvarchar(50), " &
            "MaintenanceStatus_ID   int, " &
            "Driv_ID   int, " &
            "UserName   nvarchar(50), " &
            "DeviceModel_ID   int, " &
            "DeviceBrand_ID   int " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Treasury"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '"Id int Not NULL PRIMARY KEY, " &
            cmd.CommandText = "CREATE TABLE Treasury (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "Treasury_Code int PRIMARY KEY, " &
            "Company_Branch_ID int, " &
            "Treasury_Name nvarchar(50), " &
            "Treasury_Date   nvarchar(50), " &
            "Currency_Code   int, " &
            "Treasury_Amount   float, " &
            "Treasury_Sal   float, " &
            "Treasury_vnd   float, " &
            "Treasury_CustPay   float, " &
            "Treasury_Bsal   float, " &
            "Treasury_Bvnd   float, " &
            "Treasury_Expenses   float, " &
            "Treasury_Salary   float, " &
            "Treasury_Vst_Receipts   float, " &
            "Treasury_Vnd_Receipts   float, " &
            "Treasury_Check_Out   float, " &
            "Treasury_Check_In   float, " &
            "Treasury_Other_Income   float, " &
            "Treasury_Deposit   float, " &
            "Treasury_Withdraw   float, " &
            "Treasury_Capital_Deposit   float, " &
            "Treasury_Capital_Withdraw   float, " &
            "Treasury_Credit   float, " &
            "Treasury_Debit   float, " &
            "Treasury_Balance   float " &
            ") "
            cmd.ExecuteNonQuery()

            Dim DateTimeNow As String = DateTime.Now.ToString("yyyyMMdd")
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Treasury(Company_Branch_ID,Treasury_Code,Treasury_Name,Treasury_Date,Currency_Code,Treasury_Amount,Treasury_Sal,Treasury_Bsal,Treasury_vnd,Treasury_CustPay,Treasury_Expenses,Treasury_Salary,Treasury_Credit,Treasury_Debit) values ("
            S = S & "N'1',N'1',N'الخزينة الرئيسية',N'" & DateTimeNow & "',N'1',N'0',N'0',N'0',N'0',N'0',N'0',N'0',N'0',N'0')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from EmployeesAccount"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '"Id int Not NULL PRIMARY KEY, " &
            cmd.CommandText = "CREATE TABLE EmployeesAccount (" &
            "[ID] [int] IDENTITY(1,1) Not NULL, " &
            "Emp_Code  int, " &
            "NameEmployee nvarchar(50), " &
            "vintinval   float, " &
            "TotalAmount   float, " &
            "UserName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Vst_disc_other"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            connectionStringOpen()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Dim query As String = "IF OBJECT_ID('dbo.Vst_disc_other', 'U') IS NULL "
            query += "BEGIN "
            query += "CREATE TABLE [dbo].[Vst_disc_other]("
            query += "[Id] INT IDENTITY(1,1) NOT NULL CONSTRAINT pkCustomerId PRIMARY KEY,"
            query += "[Vendorname] nvarchar(50) NOT NULL,"
            query += "[DiscStatement] nvarchar(50) NOT NULL,"
            query += "[amnt] float NOT NULL,"
            query += "[pdate] nvarchar(50) NOT NULL,"
            query += "[det] nvarchar(50) NOT NULL,"
            query += "[TIN_NO] nvarchar(50) NOT NULL,"
            query += "[UserName] nvarchar(50) NOT NULL"
            query += ")"
            query += " END"
            cmd.CommandText = query
            cmd.Connection = Cn
            cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================
        ' مبالغ العجز والزيادة للمندوبين
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Employee_Deficit_Increase"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Employee_Deficit_Increase (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "Receipt_Number  int Not NULL PRIMARY KEY, " &
            "Employee_Code int, " &
            "Employee_Name   nvarchar(100), " &
            "Amount   float, " &
            "Deficit_Increase_Code   int, " &
            "Emp_Time   nvarchar(50), " &
            "Emp_Date   nvarchar(50), " &
            "Notes   nvarchar(250), " &
            "UserName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Employee_Type_Deficit_Increase"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Employee_Type_Deficit_Increase (" &
            "Deficit_Increase_Code  int Not NULL PRIMARY KEY, " &
            "Type_Deficit_Increase   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Employee_Type_Deficit_Increase(Deficit_Increase_Code,Type_Deficit_Increase) values ("
            S = S & "'1',N'عجز')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Employee_Type_Deficit_Increase(Deficit_Increase_Code,Type_Deficit_Increase) values ("
            S = S & "'2',N'زيادة')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End Try

        '==================================================================================================================
        ' Accounting

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Account_Statement_Link"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Account_Statement_Link (" &
            "Statement_ID  int Not NULL PRIMARY KEY, " &
            "Statement_link   nvarchar(100) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Accounts_Groub"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Accounts_Groub (" &
            "Groub_Code  int Not NULL PRIMARY KEY, " &
            "Groub_Name   nvarchar(100), " &
            "Main_Code   int " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Accounts_Main"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Accounts_Main (" &
            "Main_Code  int Not NULL PRIMARY KEY, " &
            "Main_Name   nvarchar(100) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from AccountsTreeLinking"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE AccountsTreeLinking (" &
            "Link_ID  int Not NULL PRIMARY KEY, " &
            "Link_Statement   nvarchar(100), " &
            "ACCNumber   nvarchar(50), " &
            "Link_AccountsTree   nvarchar(100) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Employee_Customers"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Employee_Customers (" &
            "[Employee_Customers_ID] [int] IDENTITY(1,1) Not NULL PRIMARY KEY, " &
            "Employee_Code   int, " &
            "Customers_Code   int " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Geographic_Area"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Geographic_Area (" &
            "GeoArea_Code  int Not NULL PRIMARY KEY, " &
            "GeoArea_Name   nvarchar(200), " &
            "Branch_Code   int " &
            ") "
            cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Geographic_Area_Employee"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Geographic_Area_Employee (" &
            "[GeoArea_Employee_ID] [int] IDENTITY(1,1) Not NULL PRIMARY KEY, " &
            "Geographic_Area_Code   int, " &
            "Employee_Code   int " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Government"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Government (" &
            "Gov_Code  int Not NULL PRIMARY KEY, " &
            "Gov_Name   nvarchar(200) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Branch"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Branch (" &
            "Bran_code  int Not NULL PRIMARY KEY, " &
            "Bran_Name   nvarchar(200), " &
            "Government_Code   int, " &
            "Bran_Address   nvarchar(200), " &
            "Telephone   nvarchar(20) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        '==================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Capital_Owner"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '"Id int Not NULL PRIMARY KEY, " &
            cmd.CommandText = "CREATE TABLE Capital_Owner (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "Owner_Code int PRIMARY KEY, " &
            "Owner_Name nvarchar(100), " &
            "Owner_Phone   nvarchar(20), " &
            "Owner_Mobile   nvarchar(20), " &
            "Owner_Address   nvarchar(250) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Capital_Partner_Withdrawals"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '"Id int Not NULL PRIMARY KEY, " &
            cmd.CommandText = "CREATE TABLE Capital_Partner_Withdrawals (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "Capital_Number int PRIMARY KEY, " &
            "Owner_Code int, " &
            "Capital_Type_Code   int, " &
            "Treasury_Code   int, " &
            "Amount   float, " &
            "Capital_Time   nvarchar(50), " &
            "Capital_Date   nvarchar(50), " &
            "Notes   nvarchar(250), " &
            "UserName   nvarchar(50), " &
            "Company_Branch_ID   int " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP TABLE Capital_Type"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Capital_Type"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Capital_Type (" &
            "Capital_Type_Code int PRIMARY KEY, " &
            "Capital_Type_Name   nvarchar(50), " &
            "Decrease_Increase   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()


            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Capital_Type(Capital_Type_Code,Capital_Type_Name,Decrease_Increase) values ("
            S = S & "'1',N'سحب',N'زيادة')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Capital_Type(Capital_Type_Code,Capital_Type_Name,Decrease_Increase) values ("
            S = S & "'2',N'ايداع',N'نقصان')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End Try
        '==================================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Maintenance_Car_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Maintenance_Car_Data (" &
            "Car_Data_ID float PRIMARY KEY Not NULL, " &
            "Paintings_Type_ID   int, " &
            "Paintings_No   nvarchar(50), " &
            "Paintings_Letters   nvarchar(50), " &
            "Cust_Code   int, " &
            "Driv_ID   int, " &
            "TypeProduct_ID   int, " &
            "DeviceBrand_ID   int, " &
            "DeviceModel_ID   int, " &
            "Manu_Year_ID   int, " &
            "CC   nvarchar(50), " &
            "Cylinders_No   nvarchar(50), " &
            "Valves_No   nvarchar(50), " &
            "Chassis_No   nvarchar(50), " &
            "Description   nvarchar(MAX), " &
            "KiloMeter   nvarchar(100), " &
            "Company_Branch_ID   int " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Maintenance_Cities"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Maintenance_Cities (" &
            "Cities_ID int PRIMARY KEY, " &
            "Cities_Name   nvarchar(250) " &
            ") "
            cmd.ExecuteNonQuery()

            Cls.insert("Maintenance_Cities", "Cities_ID,Cities_Name", "'1',N'القاهرة'")
            Cls.insert("Maintenance_Cities", "Cities_ID,Cities_Name", "'2',N'الجيزة'")
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Maintenance_Manu_Year"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Maintenance_Manu_Year (" &
            "Manu_Year_ID int PRIMARY KEY, " &
            "Manu_Year   nvarchar(250) " &
            ") "
            cmd.ExecuteNonQuery()

            Cls.insert("Maintenance_Manu_Year", "Manu_Year_ID,Manu_Year", "'1',N'2000'")
            Cls.insert("Maintenance_Manu_Year", "Manu_Year_ID,Manu_Year", "'2',N'2001'")
            Cls.insert("Maintenance_Manu_Year", "Manu_Year_ID,Manu_Year", "'3',N'2002'")
            Cls.insert("Maintenance_Manu_Year", "Manu_Year_ID,Manu_Year", "'4',N'2003'")
            Cls.insert("Maintenance_Manu_Year", "Manu_Year_ID,Manu_Year", "'5',N'2004'")
            Cls.insert("Maintenance_Manu_Year", "Manu_Year_ID,Manu_Year", "'6',N'2005'")
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Maintenance_Paintings_Type"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE Maintenance_Paintings_Type (" &
            "Paintings_Type_ID int PRIMARY KEY, " &
            "Paintings_Type   nvarchar(250) " &
            ") "
            cmd.ExecuteNonQuery()

            Cls.insert("Maintenance_Paintings_Type", "Paintings_Type_ID,Paintings_Type", "'1',N'لوحات ارقام وحروف'")
            Cls.insert("Maintenance_Paintings_Type", "Paintings_Type_ID,Paintings_Type", "'2',N'لوحات ارقام فقط'")
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from MaintenanceDeviceBrand"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '"Id int Not NULL PRIMARY KEY, " &
            cmd.CommandText = "CREATE TABLE MaintenanceDeviceBrand (" &
            "DeviceBrand_ID int PRIMARY KEY, " &
            "DeviceBrand_Name   nvarchar(250) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from MaintenanceDeviceModel"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '"Id int Not NULL PRIMARY KEY, " &
            cmd.CommandText = "CREATE TABLE MaintenanceDeviceModel (" &
            "DeviceModel_ID int PRIMARY KEY, " &
            "DeviceModel_Name   nvarchar(250) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        '==================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from EmployeesDiscountReward"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '"Id int Not NULL PRIMARY KEY, " &
            cmd.CommandText = "CREATE TABLE EmployeesDiscountReward (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "Move_Number  int, " &
            "EMPID int, " &
            "Month   nvarchar(50), " &
            "Years   nvarchar(50), " &
            "Move_Date   nvarchar(50), " &
            "Amount   float, " &
            "DiscountReward_Type_ID  int, " &
            "Recase   int, " &
            "UserName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from EmployeesDiscountReward_Type"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '"Id int Not NULL PRIMARY KEY, " &
            cmd.CommandText = "CREATE TABLE EmployeesDiscountReward_Type (" &
            "DiscountReward_Type_ID int PRIMARY KEY, " &
            "DiscountReward_Type   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into EmployeesDiscountReward_Type(DiscountReward_Type_ID,DiscountReward_Type) values ("
            S = S & "'1',N'سلفة')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into EmployeesDiscountReward_Type(DiscountReward_Type_ID,DiscountReward_Type) values ("
            S = S & "'2',N'خصومات تأخير')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into EmployeesDiscountReward_Type(DiscountReward_Type_ID,DiscountReward_Type) values ("
            S = S & "'3',N'خصومات غياب')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into EmployeesDiscountReward_Type(DiscountReward_Type_ID,DiscountReward_Type) values ("
            S = S & "'4',N'خصومات جزاءات')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into EmployeesDiscountReward_Type(DiscountReward_Type_ID,DiscountReward_Type) values ("
            S = S & "'5',N'مكأفئة')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into EmployeesDiscountReward_Type(DiscountReward_Type_ID,DiscountReward_Type) values ("
            S = S & "'6',N'علاوة')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into EmployeesDiscountReward_Type(DiscountReward_Type_ID,DiscountReward_Type) values ("
            S = S & "'7',N'بدل نقل')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into EmployeesDiscountReward_Type(DiscountReward_Type_ID,DiscountReward_Type) values ("
            S = S & "'8',N'بدل طبيعة عمل')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into EmployeesDiscountReward_Type(DiscountReward_Type_ID,DiscountReward_Type) values ("
            S = S & "'9',N'بدل سكن')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into EmployeesDiscountReward_Type(DiscountReward_Type_ID,DiscountReward_Type) values ("
            S = S & "'10',N'إضافات أخرى')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from AdjustmentsStores"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE AdjustmentsStores (" &
            "ID int PRIMARY KEY, " &
            "bill_no   nvarchar(50), " &
            "Decrease_Increase   int, " &
            "itm_id   nvarchar(50), " &
            "qu   float, " &
            "bill_date   nvarchar(50), " &
            "Stores   nvarchar(50), " &
            "UserName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from NetWeightEquation"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE NetWeightEquation (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "itm_id nvarchar(50), " &
            "Stores   nvarchar(50), " &
            "GrossWeight   float " &
            ") "
            cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from PriceType"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE PriceType (" &
            "PriceType_ID  int Not NULL PRIMARY KEY, " &
            "PriceTypeName   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PriceType(PriceType_ID,PriceTypeName) values ("
            S = S & "'1',N'سعر البيع')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PriceType(PriceType_ID,PriceTypeName) values ("
            S = S & "'2',N'سعر الجملة')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PriceType(PriceType_ID,PriceTypeName) values ("
            S = S & "'3',N'سعر جملة الجملة')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from EmployeesTargetDetective"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE EmployeesTargetDetective (" &
            "Target_ID int, " &
            "TargetNumber   float, " &
            "TargetIncentive   float, " &
            "TargetRatioMinimum   float " &
            ") "
            cmd.ExecuteNonQuery()

            Dim TargetNumber As String = "20000"
            Dim TargetIncentive As String = "1000"
            Dim TargetRatioMinimum As String = "75"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into EmployeesTargetDetective(Target_ID,TargetNumber,TargetIncentive,TargetRatioMinimum) values ("
            S = S & "'1'," & TargetNumber & "," & TargetIncentive & "," & TargetRatioMinimum & ")"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from BarcodeMore"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE BarcodeMore (" &
            "[Id] [int] IDENTITY(1,1) Not NULL, " &
            "itm_id  nvarchar(50), " &
            "itm_id_More   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from ItemsUnity"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE ItemsUnity (" &
            "[Unity_ID] [int] IDENTITY(1,1) Not NULL PRIMARY KEY, " &
            "itm_id nvarchar(50), " &
            "Unity_Name   nvarchar(50), " &
            "NumberPieces   int, " &
            "TinPriceUnit   float, " &
            "SalPriceUnit   float, " &
            "DefaultTin   int, " &
            "DefaultSale   int " &
            ") "
            cmd.ExecuteNonQuery()

            '=====================================
            Dim itm_id, TinPrice, SalPrice As String
            Dim aray_1, aray_2, aray_3 As New ArrayList
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select itm_id,TinPrice,SalPrice from  Items"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_1.Add(dr("itm_id"))
                aray_2.Add(dr("TinPrice"))
                aray_3.Add(dr("SalPrice"))
            Loop
            For i As Integer = 0 To aray_1.Count - 1
                itm_id = aray_1(i).ToString
                TinPrice = aray_2(i).ToString
                SalPrice = aray_3(i).ToString

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into ItemsUnity(itm_id,Unity_Name,NumberPieces,TinPriceUnit,SalPriceUnit,DefaultTin,DefaultSale,Company_Branch_ID)  values("
                S = S & "N'" & itm_id & "',N'قطعة',N'1',N'" & TinPrice & "',N'" & SalPrice & "',N'1',N'1',N'" & Company_Branch_ID & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next

        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from MaintenanceStatus"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE MaintenanceStatus (" &
            "MaintenanceStatus_ID  int Not NULL PRIMARY KEY, " &
            "MaintenanceStatus_Name   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from MaintenanceTypeProduct"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE TABLE MaintenanceTypeProduct (" &
            "TypeProduct_ID  int Not NULL PRIMARY KEY, " &
            "TypeProduct_Name   nvarchar(100) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Drivers"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '"Id int Not NULL PRIMARY KEY, " &
            cmd.CommandText = "CREATE TABLE Drivers (" &
            "[Driv_ID] [int] IDENTITY(1,1) Not NULL, " &
            "Driv_Name nvarchar(50), " &
            "Driv_Mobile   nvarchar(50), " &
            "Driv_Address   nvarchar(MAX), " &
            "Driv_CarNumber   nvarchar(50), " &
            "Driv_Car_Brand   nvarchar(50), " &
            "Driv_Car_Color   nvarchar(50) " &
            ") "
            cmd.ExecuteNonQuery()
        End Try

        '==================================================================================================================

        'Try
        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "Select * from People"
        '    dr = cmd.ExecuteReader : dr.Read()
        '    dr.Close()
        'Catch ex As Exception
        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    '"Id int Not NULL PRIMARY KEY, " &
        '    cmd.CommandText = "CREATE TABLE People (" &
        '    "[Id] [int] IDENTITY(1,1) Not NULL PRIMARY KEY, " &
        '    "LastName  VARCHAR(30), " &
        '    "FirstName VARCHAR(20), " &
        '    "Address   VARCHAR(50) " &
        '    ") "
        '    cmd.ExecuteNonQuery()

        'End Try

    End Sub

    Public Sub ALTER_TABLE()
        connect()

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CodeEAN13Barre from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD CodeEAN13Barre nvarchar(100)"
            cmd.ExecuteNonQuery()

        End Try
        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_no from MOVES"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE MOVES ADD bill_no float  NULL CONSTRAINT [DF_MOVES_bill_no]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update MOVES set bill_no =0" : cmd.ExecuteNonQuery()

        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_no from MOVESDATA"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE MOVESDATA ADD bill_no float  NULL CONSTRAINT [DF_MOVESDATA_bill_no]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update MOVESDATA set bill_no =0" : cmd.ExecuteNonQuery()

        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TypePlusDiscRateSalPrice,TypePlusDiscRateWholePrice,TypePlusDiscRateWholeWholePrice from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD TypePlusDiscRateSalPrice float  NULL CONSTRAINT [DF_Items_TypePlusDiscRateSalPrice]  DEFAULT ((0)),TypePlusDiscRateWholePrice float  NULL CONSTRAINT [DF_Items_TypePlusDiscRateWholePrice]  DEFAULT ((0)),TypePlusDiscRateWholeWholePrice float  NULL CONSTRAINT [DF_Items_TypePlusDiscRateWholeWholePrice]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Items set TypePlusDiscRateSalPrice =0,TypePlusDiscRateWholePrice =0,TypePlusDiscRateWholeWholePrice =0" : cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TypePlusDiscRateSalPrice,TypePlusDiscRateWholePrice,TypePlusDiscRateWholeWholePrice from Groups"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Groups ADD TypePlusDiscRateSalPrice float  NULL CONSTRAINT [DF_Groups_TypePlusDiscRateSalPrice]  DEFAULT ((0)),TypePlusDiscRateWholePrice float  NULL CONSTRAINT [DF_Groups_TypePlusDiscRateWholePrice]  DEFAULT ((0)),TypePlusDiscRateWholeWholePrice float  NULL CONSTRAINT [DF_Groups_TypePlusDiscRateWholeWholePrice]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Groups set TypePlusDiscRateSalPrice =0,TypePlusDiscRateWholePrice =0,TypePlusDiscRateWholeWholePrice =0" : cmd.ExecuteNonQuery()
        End Try


        '============================================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Driv_Name,Driv_CarNumber,KiloMeter,Supervisor_Reform,Recipient,Received_Date,Delivery_Date from PrintSalesPurchases"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE PrintSalesPurchases ADD Driv_Name nvarchar(50),Driv_CarNumber nvarchar(50),KiloMeter nvarchar(50),Supervisor_Reform nvarchar(50),Recipient nvarchar(50),Received_Date nvarchar(50),Delivery_Date nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_unity from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD qu_unity float  NULL CONSTRAINT [DF_Receive_BillsalData_qu_unity]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BillsalData set qu_unity =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select OrderID from Expenses"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Expenses ADD  OrderID int  NULL CONSTRAINT [DF_Expenses_OrderID]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateDiscTinPriceAfter,RateDiscSalPriceAfter,RateDiscWholePriceAfter,RateDiscWholeWholePriceAfter from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD  RateDiscTinPriceAfter float  NULL CONSTRAINT [DF_Items_RateDiscTinPriceAfter]  DEFAULT ((0)),RateDiscSalPriceAfter float  NULL CONSTRAINT [DF_Items_RateDiscSalPriceAfter]  DEFAULT ((0)),RateDiscWholePriceAfter float  NULL CONSTRAINT [DF_Items_RateDiscWholePriceAfter]  DEFAULT ((0)),RateDiscWholeWholePriceAfter float  NULL CONSTRAINT [DF_Items_RateDiscWholeWholePriceAfter]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Items set RateDiscTinPriceAfter =0,RateDiscSalPriceAfter =0,RateDiscWholePriceAfter =0,RateDiscWholeWholePriceAfter =0" : cmd.ExecuteNonQuery()

        End Try

        '============================================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TotalNetAssets from Treasury"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Treasury ADD  TotalNetAssets float  NULL CONSTRAINT [DF_Treasury_TotalNetAssets]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TotalNetAssets from Date_Move_Money"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Date_Move_Money ADD  TotalNetAssets float  NULL CONSTRAINT [DF_Date_Move_Money_TotalNetAssets]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Stores from PriceOfferBill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE PriceOfferBill ADD Stores nvarchar(50)"
            cmd.ExecuteNonQuery()

            Dim Stores As String = Cls.Get_Code_Value_Stores_More("Stores", "store", "StausMainStore =N'0'")

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update PriceOfferBill set Stores =N'" & Stores & "'" : cmd.ExecuteNonQuery()

        End Try
        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStockTotal from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD CurrentStockTotal float  NULL CONSTRAINT [DF_Receive_BillsalData_CurrentStockTotal]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BillsalData set CurrentStockTotal =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStockTotal from Receive_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BilltINData ADD CurrentStockTotal float  NULL CONSTRAINT [DF_Receive_BilltINData_CurrentStockTotal]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BilltINData set CurrentStockTotal =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStockTotal from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD CurrentStockTotal float  NULL CONSTRAINT [DF_BillsalData_CurrentStockTotal]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set CurrentStockTotal =0" : cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStockTotal from BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ADD CurrentStockTotal float  NULL CONSTRAINT [DF_BilltINData_CurrentStockTotal]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BilltINData set CurrentStockTotal =0" : cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStockTotal from IM_Bsal_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal_Data ADD  CurrentStockTotal float  NULL CONSTRAINT [DF_IM_Bsal_Data_CurrentStockTotal]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal_Data set CurrentStockTotal =0" : cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStockTotal from IM_Btin_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin_Data ADD CurrentStockTotal float  NULL CONSTRAINT [DF_IM_Btin_Data_CurrentStockTotal]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Btin_Data set CurrentStockTotal =0" : cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStockTotal from data_decayed"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE data_decayed ADD  CurrentStockTotal float  NULL CONSTRAINT [DF_data_decayed_CurrentStockTotal]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update data_decayed set CurrentStockTotal =0" : cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStock,CurrentStockTotal,itm_cat from ItemsTransfer_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsTransfer_BilltINData ADD CurrentStock float  NULL CONSTRAINT [DF_ItemsTransfer_BilltINData_CurrentStock]  DEFAULT ((0)),CurrentStockTotal float  NULL CONSTRAINT [DF_ItemsTransfer_BilltINData_CurrentStockTotal]  DEFAULT ((0)),itm_cat nvarchar(50)"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsTransfer_BilltINData set CurrentStock =0,CurrentStockTotal =0" : cmd.ExecuteNonQuery()

            Dim aray_id As New ArrayList : Dim aray_itm_id As New ArrayList : Dim aray_itm_cat As New ArrayList
            Dim itm_id As String = "" : Dim itm_cat As String = ""
            aray_id.Clear() : aray_itm_id.Clear() : aray_itm_cat.Clear()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select id,itm_id,itm_cat from  ItemsTransfer_BilltINData" : dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_id.Add(dr("id")) : aray_itm_id.Add(dr("itm_id")) : aray_itm_cat.Add(dr("itm_cat"))
            Loop

            For i As Integer = 0 To aray_id.Count - 1
                itm_id = aray_itm_id(i).ToString
                itm_cat = Get_Code_Value("Items", "group_name", "itm_id", itm_id)
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update ItemsTransfer_BilltINData set itm_cat =N'" & itm_cat & "' where itm_id =N'" & itm_id & "'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStock,CurrentStockTotal,itm_cat from ItemsTransfer_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsTransfer_BillsalData ADD CurrentStock float  NULL CONSTRAINT [DF_ItemsTransfer_BillsalData_CurrentStock]  DEFAULT ((0)),CurrentStockTotal float  NULL CONSTRAINT [DF_ItemsTransfer_BillsalData_CurrentStockTotal]  DEFAULT ((0)),itm_cat nvarchar(50)"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsTransfer_BillsalData set CurrentStock =0,CurrentStockTotal =0" : cmd.ExecuteNonQuery()

            Dim aray_id As New ArrayList : Dim aray_itm_id As New ArrayList : Dim aray_itm_cat As New ArrayList
            Dim itm_id As String = "" : Dim itm_cat As String = ""
            aray_id.Clear() : aray_itm_id.Clear() : aray_itm_cat.Clear()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select id,itm_id,itm_cat from  ItemsTransfer_BillsalData" : dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_id.Add(dr("id")) : aray_itm_id.Add(dr("itm_id")) : aray_itm_cat.Add(dr("itm_cat"))
            Loop

            For i As Integer = 0 To aray_id.Count - 1
                itm_id = aray_itm_id(i).ToString
                itm_cat = Get_Code_Value("Items", "group_name", "itm_id", itm_id)
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update ItemsTransfer_BillsalData set itm_cat =N'" & itm_cat & "' where itm_id =N'" & itm_id & "'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStock,CurrentStockTotal,itm_cat from Manufacturing_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Manufacturing_BillsalData ADD CurrentStock float  NULL CONSTRAINT [DF_Manufacturing_BillsalData_CurrentStock]  DEFAULT ((0)),CurrentStockTotal float  NULL CONSTRAINT [DF_Manufacturing_BillsalData_CurrentStockTotal]  DEFAULT ((0)),itm_cat nvarchar(50)"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Manufacturing_BillsalData set CurrentStock =0,CurrentStockTotal =0" : cmd.ExecuteNonQuery()

            Dim aray_id As New ArrayList : Dim aray_itm_id As New ArrayList : Dim aray_itm_cat As New ArrayList
            Dim itm_id As String = "" : Dim itm_cat As String = ""
            aray_id.Clear() : aray_itm_id.Clear() : aray_itm_cat.Clear()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select id,itm_id,itm_cat from  Manufacturing_BillsalData" : dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_id.Add(dr("id")) : aray_itm_id.Add(dr("itm_id")) : aray_itm_cat.Add(dr("itm_cat"))
            Loop

            For i As Integer = 0 To aray_id.Count - 1
                itm_id = aray_itm_id(i).ToString
                itm_cat = Get_Code_Value("Items", "group_name", "itm_id", itm_id)
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update Manufacturing_BillsalData set itm_cat =N'" & itm_cat & "' where itm_id =N'" & itm_id & "'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
        End Try

        '=============================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStock,CurrentStockTotal,itm_cat from Manufacturing_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Manufacturing_BilltINData ADD CurrentStock float  NULL CONSTRAINT [DF_Manufacturing_BilltINData_CurrentStock]  DEFAULT ((0)),CurrentStockTotal float  NULL CONSTRAINT [DF_Manufacturing_BilltINData_CurrentStockTotal]  DEFAULT ((0)),itm_cat nvarchar(50)"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Manufacturing_BilltINData set CurrentStock =0,CurrentStockTotal =0" : cmd.ExecuteNonQuery()

            Dim aray_id As New ArrayList : Dim aray_itm_id As New ArrayList : Dim aray_itm_cat As New ArrayList
            Dim itm_id As String = "" : Dim itm_cat As String = ""
            aray_id.Clear() : aray_itm_id.Clear() : aray_itm_cat.Clear()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select id,itm_id,itm_cat from  Manufacturing_BilltINData" : dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_id.Add(dr("id")) : aray_itm_id.Add(dr("itm_id")) : aray_itm_cat.Add(dr("itm_cat"))
            Loop

            For i As Integer = 0 To aray_id.Count - 1
                itm_id = aray_itm_id(i).ToString
                itm_cat = Get_Code_Value("Items", "group_name", "itm_id", itm_id)
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update Manufacturing_BilltINData set itm_cat =N'" & itm_cat & "' where itm_id =N'" & itm_id & "'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateDiscWholeWholePrice from Groups"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Groups ADD RateDiscWholeWholePrice float  NULL CONSTRAINT [DF_Groups_RateDiscWholeWholePrice]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Groups set RateDiscWholeWholePrice =0" : cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateDiscWholePrice from Groups"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Groups ADD RateDiscWholePrice float  NULL CONSTRAINT [DF_Groups_RateDiscWholePrice]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Groups set RateDiscWholePrice =0" : cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateDiscSalPrice from Groups"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Groups ADD RateDiscSalPrice float  NULL CONSTRAINT [DF_Groups_RateDiscSalPrice]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Groups set RateDiscSalPrice =0" : cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateDiscTinPrice from Groups"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Groups ADD RateDiscTinPrice float  NULL CONSTRAINT [DF_Groups_RateDiscTinPrice]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Groups set RateDiscTinPrice =0" : cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PriceIncludesVAT from tmpBillTinData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE tmpBillTinData ADD PriceIncludesVAT int  NULL CONSTRAINT [DF_tmpBillTinData_PriceIncludesVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PriceIncludesVAT from TmpBillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE TmpBillsalData ADD PriceIncludesVAT int  NULL CONSTRAINT [DF_TmpBillsalData_PriceIncludesVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PriceIncludesVAT from Receive_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BilltINData ADD PriceIncludesVAT int  NULL CONSTRAINT [DF_Receive_BilltINData_PriceIncludesVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BilltINData set PriceIncludesVAT =0" : cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PriceIncludesVAT from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD PriceIncludesVAT int  NULL CONSTRAINT [DF_Receive_BillsalData_PriceIncludesVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BillsalData set PriceIncludesVAT =0" : cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PriceIncludesVAT from IM_Btin_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin_Data ADD PriceIncludesVAT int  NULL CONSTRAINT [DF_IM_Btin_Data_PriceIncludesVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Btin_Data set PriceIncludesVAT =0" : cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PriceIncludesVAT from IM_Bsal_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal_Data ADD PriceIncludesVAT int  NULL CONSTRAINT [DF_IM_Bsal_Data_PriceIncludesVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal_Data set PriceIncludesVAT =0" : cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PriceIncludesVAT  from BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ADD PriceIncludesVAT int  NULL CONSTRAINT [DF_BilltINData_PriceIncludesVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BilltINData set PriceIncludesVAT =0" : cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PriceIncludesVAT from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD PriceIncludesVAT int  NULL CONSTRAINT [DF_BillsalData_PriceIncludesVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set PriceIncludesVAT =0" : cmd.ExecuteNonQuery()
        End Try

        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PriceIncludesVAT from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD PriceIncludesVAT int  NULL CONSTRAINT [DF_Items_PriceIncludesVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Items set PriceIncludesVAT =0" : cmd.ExecuteNonQuery()
        End Try


        '=============================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select id from A_C"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE A_C ADD id int IDENTITY(1,1) NOT NULL"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select QuickSearch from Groups"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Groups ADD QuickSearch int  NULL CONSTRAINT [DF_Groups_QuickSearch]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Groups set QuickSearch =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CloseSheft from Expenses"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Expenses ADD CloseSheft int  NULL CONSTRAINT [DF_Expenses_CloseSheft]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Expenses set CloseSheft =1" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CloseSheft from IM_Bsal_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal_Data ADD CloseSheft int  NULL CONSTRAINT [DF_IM_Bsal_Data_CloseSheft]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal_Data set CloseSheft =1" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CloseSheft from IM_Bsal"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal ADD CloseSheft int  NULL CONSTRAINT [DF_IM_Bsal_CloseSheft]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal set CloseSheft =1" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CloseSheft,BillTimeAmBm from Receive_Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Sales_Bill ADD CloseSheft int  NULL CONSTRAINT [DF_Receive_Sales_Bill_CloseSheft]  DEFAULT ((0)),BillTimeAmBm nvarchar(50)"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_Sales_Bill set CloseSheft =1" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select BillTimeAmBm from Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sales_Bill ADD BillTimeAmBm nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CloseSheft from Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sales_Bill ADD CloseSheft int  NULL CONSTRAINT [DF_Sales_Bill_CloseSheft]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Sales_Bill set CloseSheft =1" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CloseSheft,BillTimeAmBm from Receive_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BilltINData ADD CloseSheft int  NULL CONSTRAINT [DF_Receive_BilltINData_CloseSheft]  DEFAULT ((0)),BillTimeAmBm nvarchar(50)"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BilltINData set CloseSheft =1" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CloseSheft from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD CloseSheft int  NULL CONSTRAINT [DF_BillsalData_CloseSheft]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set CloseSheft =1" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select BillTimeAmBm from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD BillTimeAmBm nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Sheft_Stat from Users"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Users ADD Sheft_Stat int  NULL CONSTRAINT [DF_Users_Sheft_Stat]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Users set Sheft_Stat =1" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Sheft_Stat from Sheft_Status"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sheft_Status ADD Sheft_Stat nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateDriverDelivery from Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sales_Bill ADD RateDriverDelivery float  NULL CONSTRAINT [DF_Sales_Bill_RateDriverDelivery]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Sales_Bill set RateDriverDelivery =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateDriverDelivery from Receive_Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Sales_Bill ADD RateDriverDelivery float  NULL CONSTRAINT [DF_Receive_Sales_Bill_RateDriverDelivery]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu from ItemsOffersDiscounts"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsOffersDiscounts ADD qu float  NULL CONSTRAINT [DF_ItemsOffersDiscounts_qu]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsOffersDiscounts set qu =1" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ActionOffers from ItemsOffersDiscounts"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsOffersDiscounts ADD ActionOffers int  NULL CONSTRAINT [DF_ItemsOffersDiscounts_ActionOffers]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsOffersDiscounts set ActionOffers =1" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ActionOffers from ItemsOffersDiscountsTotal"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsOffersDiscountsTotal ADD ActionOffers int  NULL CONSTRAINT [DF_ItemsOffersDiscountsTotal_ActionOffers]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsOffersDiscountsTotal set ActionOffers =1" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select OffersStatement from ItemsOffersDiscountsTotal"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsOffersDiscountsTotal ADD OffersStatement nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_unity from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD qu_unity float  NULL CONSTRAINT [DF_BillsalData_qu_unity]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set qu_unity =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_date_End,PriceOffersBefore,StateDisc from ItemsOffersDiscounts"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsOffersDiscounts ADD bill_date_End nvarchar(50),PriceOffersBefore float  NULL CONSTRAINT [DF_ItemsOffersDiscounts_PriceOffersBefore]  DEFAULT ((0)),StateDisc nvarchar(50)"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsOffersDiscounts set PriceOffersBefore =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Discount_Price_After from IM_Bsal_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal_Data ADD Discount_Price_After float  NULL CONSTRAINT [DF_IM_Bsal_Data_Discount_Price_After]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal_Data set Discount_Price_After =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Discount_Price_After from IM_Btin_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin_Data ADD Discount_Price_After float  NULL CONSTRAINT [DF_IM_Btin_Data_Discount_Price_After]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Discounts from IM_Btin_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin_Data ADD  Discounts float  NULL CONSTRAINT [DF_IM_Btin_Data_Discounts]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select DiscountsValue from IM_Btin_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin_Data ADD DiscountsValue float  NULL CONSTRAINT [DF_IM_Btin_Data_DiscountsValue]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateDiscTinPrice,RateDiscSalPrice,RateDiscWholePrice,RateDiscWholeWholePrice from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD RateDiscTinPrice float  NULL CONSTRAINT [DF_Items_RateDiscTinPrice]  DEFAULT ((0)),RateDiscSalPrice float  NULL CONSTRAINT [DF_Items_RateDiscSalPrice]  DEFAULT ((0)),RateDiscWholePrice float  NULL CONSTRAINT [DF_Items_RateDiscWholePrice]  DEFAULT ((0)),RateDiscWholeWholePrice float  NULL CONSTRAINT [DF_Items_RateDiscWholeWholePrice]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CashBank from Vst"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vst ADD CashBank float  NULL CONSTRAINT [DF_Vst_CashBank]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vst set CashBank =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Name1,Name2,Name3,Name4,Name5,Name6,Name7 from PrintSalesPurchases"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE PrintSalesPurchases ADD Name1 nvarchar(50),Name2 nvarchar(50),Name3 nvarchar(50),Name4 nvarchar(50),Name5 nvarchar(50),Name6 nvarchar(50),Name7 nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CreditPrevious,DebitPrevious,CreditCurrent,DebitCurrent from Receive_Purchase_bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Purchase_bill ADD CreditPrevious float  NULL CONSTRAINT [DF_Receive_Purchase_bill_CreditPrevious]  DEFAULT ((0)),DebitPrevious float  NULL CONSTRAINT [DF_Receive_Purchase_bill_DebitPrevious]  DEFAULT ((0)),CreditCurrent float  NULL CONSTRAINT [DF_Receive_Purchase_bill_CreditCurrent]  DEFAULT ((0)),DebitCurrent float  NULL CONSTRAINT [DF_Receive_Purchase_bill_DebitCurrent]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CreditPrevious,DebitPrevious,CreditCurrent,DebitCurrent from purchase_bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE purchase_bill ADD CreditPrevious float  NULL CONSTRAINT [DF_purchase_bill_CreditPrevious]  DEFAULT ((0)),DebitPrevious float  NULL CONSTRAINT [DF_purchase_bill_DebitPrevious]  DEFAULT ((0)),CreditCurrent float  NULL CONSTRAINT [DF_purchase_bill_CreditCurrent]  DEFAULT ((0)),DebitCurrent float  NULL CONSTRAINT [DF_purchase_bill_DebitCurrent]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update purchase_bill set CreditPrevious =0,DebitPrevious =0,CreditCurrent =0,DebitCurrent =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TinPrice from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD TinPrice float  NULL CONSTRAINT [DF_BillsalData_TinPrice]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set TinPrice =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Price_Unity from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD Price_Unity float  NULL CONSTRAINT [DF_BillsalData_Price_Unity]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set Price_Unity =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select DiscountsValue from IM_Btin"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin ADD DiscountsValue float  NULL CONSTRAINT [DF_IM_Btin_DiscountsValue]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Btin set DiscountsValue =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select DiscountsValue from IM_Btin"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin ADD DiscountsValue float  NULL CONSTRAINT [DF_IM_Btin_DiscountsValue]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Btin set DiscountsValue =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select DiscountsValue from IM_Bsal"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal ADD DiscountsValue float  NULL CONSTRAINT [DF_IM_Bsal_DiscountsValue]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal set DiscountsValue =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select DiscountsValue from Receive_Purchase_bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Purchase_bill ADD DiscountsValue float  NULL CONSTRAINT [DF_Receive_Purchase_bill_DiscountsValue]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_Purchase_bill set DiscountsValue =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select DiscountsValue from Receive_Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Sales_Bill ADD DiscountsValue float  NULL CONSTRAINT [DF_Receive_Sales_Bill_DiscountsValue]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_Sales_Bill set DiscountsValue =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select DiscountsValue from Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sales_Bill ADD DiscountsValue float  NULL CONSTRAINT [DF_Sales_Bill_DiscountsValue]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Sales_Bill set DiscountsValue =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select DiscountsValue from purchase_bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE purchase_bill ADD DiscountsValue float  NULL CONSTRAINT [DF_purchase_bill_DiscountsValue]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update purchase_bill set DiscountsValue =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select StateDisc,DiscountsTin from TmpBillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE TmpBillsalData ADD StateDisc nvarchar(50),DiscountsTin float  NULL CONSTRAINT [DF_TmpBillsalData_DiscountsTin]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select StateDisc,DiscountsTin from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD StateDisc nvarchar(50),DiscountsTin float  NULL CONSTRAINT [DF_Receive_BillsalData_DiscountsTin]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select StateDisc,DiscountsTin from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD StateDisc nvarchar(50),DiscountsTin float  NULL CONSTRAINT [DF_BillsalData_DiscountsTin]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Expired from tmpBillTinData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE tmpBillTinData ADD Expired nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Discounts,DiscountsValue,Discount_Price_After from tmpBillTinData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE tmpBillTinData ADD Discounts float  NULL CONSTRAINT [DF_tmpBillTinData_Discounts]  DEFAULT ((0)),DiscountsValue float  NULL CONSTRAINT [DF_tmpBillTinData_DiscountsValue]  DEFAULT ((0)),Discount_Price_After float  NULL CONSTRAINT [DF_tmpBillTinData_Discount_Price_After]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Discount_Price_After from TmpBillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE TmpBillsalData ADD Discount_Price_After float  NULL CONSTRAINT [DF_TmpBillsalData_Discount_Price_After]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Discount_Price_After from Receive_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BilltINData ADD Discount_Price_After float  NULL CONSTRAINT [DF_Receive_BilltINData_Discount_Price_After]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BilltINData set Discount_Price_After =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Discount_Price_After from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD Discount_Price_After float  NULL CONSTRAINT [DF_Receive_BillsalData_Discount_Price_After]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BillsalData set Discount_Price_After =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Discount_Price_After from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD Discount_Price_After float  NULL CONSTRAINT [DF_BillsalData_Discount_Price_After]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set Discount_Price_After =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Discount_Price_After from BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ADD Discount_Price_After float  NULL CONSTRAINT [DF_BilltINData_Discount_Price_After]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BilltINData set Discount_Price_After =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Discounts,DiscountsValue from TmpBillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE TmpBillsalData ADD Discounts float  NULL CONSTRAINT [DF_TmpBillsalData_Discounts]  DEFAULT ((0)),DiscountsValue float  NULL CONSTRAINT [DF_TmpBillsalData_DiscountsValue]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select DiscountsValue from IM_Bsal_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal_Data ADD DiscountsValue float  NULL CONSTRAINT [DF_IM_Bsal_Data_DiscountsValue]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal_Data set DiscountsValue =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Discounts from IM_Bsal_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal_Data ADD Discounts float  NULL CONSTRAINT [DF_IM_Bsal_Data_Discounts]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal_Data set Discounts =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select DiscountsValue from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD DiscountsValue float  NULL CONSTRAINT [DF_Receive_BillsalData_DiscountsValue]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BillsalData set DiscountsValue =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select DiscountsValue from BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ADD DiscountsValue float  NULL CONSTRAINT [DF_BilltINData_DiscountsValue]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BilltINData set DiscountsValue =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select DiscountsValue from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD DiscountsValue float  NULL CONSTRAINT [DF_BillsalData_DiscountsValue]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set DiscountsValue =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStock from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD CurrentStock float  NULL CONSTRAINT [DF_BillsalData_CurrentStock]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set CurrentStock =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStock from data_decayed"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE data_decayed ADD CurrentStock float  NULL CONSTRAINT [DF_data_decayed_CurrentStock]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update data_decayed set CurrentStock =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStock from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD CurrentStock float  NULL CONSTRAINT [DF_Receive_BillsalData_CurrentStock]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BillsalData set CurrentStock =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStock from Receive_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BilltINData ADD CurrentStock float  NULL CONSTRAINT [DF_Receive_BilltINData_CurrentStock]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BilltINData set CurrentStock =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStock from IM_Btin_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin_Data ADD CurrentStock float  NULL CONSTRAINT [DF_IM_Btin_Data_CurrentStock]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Btin_Data set CurrentStock =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStock from IM_Bsal_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal_Data ADD CurrentStock float  NULL CONSTRAINT [DF_IM_Bsal_Data_CurrentStock]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal_Data set CurrentStock =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStock from BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ADD CurrentStock float  NULL CONSTRAINT [DF_BilltINData_CurrentStock]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BilltINData set CurrentStock =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CurrentStock from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD CurrentStock float  NULL CONSTRAINT [DF_BillsalData_CurrentStock]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set CurrentStock =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ValueVAT from Customers"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Customers ADD ValueVAT float  NULL CONSTRAINT [DF_Customers_ValueVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Customers set ValueVAT =0" : cmd.ExecuteNonQuery()
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select saltinpricetotal from Date_Finance"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Date_Finance ADD saltinpricetotal float"
            cmd.ExecuteNonQuery()
        End Try
        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select saltinpricetotal from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD saltinpricetotal float"
            cmd.ExecuteNonQuery()
        End Try
        '============================================================================================================================


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select SalTinTotalprice from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD SalTinTotalprice float"
            cmd.ExecuteNonQuery()

            Dim aray_id As New ArrayList
            Dim aray_TinPrice As New ArrayList
            Dim aray_qu_unity As New ArrayList
            Dim SalTinTotalprice As String = ""

            aray_id.Clear()
            aray_TinPrice.Clear()
            aray_qu_unity.Clear()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select id,TinPrice,qu_unity from  BillsalData"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_id.Add(dr("id"))
                aray_TinPrice.Add(dr("TinPrice"))
                aray_qu_unity.Add(dr("qu_unity"))
            Loop

            For i As Integer = 0 To aray_id.Count - 1

                SalTinTotalprice = Val(aray_TinPrice(i).ToString) * Val(aray_qu_unity(i).ToString)

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update BillsalData set SalTinTotalprice =N'" & SalTinTotalprice & "' where id =N'" & aray_id(i).ToString & "'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
        End Try

        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select billtime from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD billtime nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try
        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select billtime from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD billtime nvarchar(50)"
            cmd.ExecuteNonQuery()

            Dim aray_id As New ArrayList
            Dim aray_bill_no As New ArrayList
            Dim Vendorname As String = "" : Dim billtime As String = ""

            aray_id.Clear()
            aray_bill_no.Clear()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select id,bill_no from  BillsalData"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_id.Add(dr("id"))
                aray_bill_no.Add(dr("bill_no"))
            Loop

            For i As Integer = 0 To aray_id.Count - 1

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select billtime from Sales_Bill where bill_no=N'" & aray_bill_no(i).ToString & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    billtime = dr(0).ToString
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update BillsalData set billtime =N'" & billtime & "' where bill_no =N'" & aray_bill_no(i).ToString & "'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
        End Try

        '============================================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_no_Expired from TmpBillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE TmpBillsalData ADD  bill_no_Expired float  NULL CONSTRAINT [DF_TmpBillsalData_bill_no_Expired]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update TmpBillsalData set bill_no_Expired =0" : cmd.ExecuteNonQuery()
        End Try
        '============================================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_no_Expired from tmpBillTinData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE tmpBillTinData ADD  bill_no_Expired float  NULL CONSTRAINT [DF_tmpBillTinData_bill_no_Expired]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update tmpBillTinData set bill_no_Expired =0" : cmd.ExecuteNonQuery()
        End Try
        '============================================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_no_Expired from data_decayed"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE data_decayed ADD  bill_no_Expired float  NULL CONSTRAINT [DF_data_decayed_bill_no_Expired]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update data_decayed set bill_no_Expired =0" : cmd.ExecuteNonQuery()
        End Try
        '============================================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_no_Expired from ItemsTransfer_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsTransfer_BillsalData ADD  bill_no_Expired float  NULL CONSTRAINT [DF_ItemsTransfer_BillsalData_bill_no_Expired]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsTransfer_BillsalData set bill_no_Expired =0" : cmd.ExecuteNonQuery()
        End Try
        '============================================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_no_Expired from ItemsTransfer_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsTransfer_BilltINData ADD  bill_no_Expired float  NULL CONSTRAINT [DF_ItemsTransfer_BilltINData_bill_no_Expired]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsTransfer_BilltINData set bill_no_Expired =0" : cmd.ExecuteNonQuery()
        End Try
        '============================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_no_Expired from Manufacturing_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Manufacturing_BillsalData ADD  bill_no_Expired float  NULL CONSTRAINT [DF_Manufacturing_BillsalData_bill_no_Expired]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Manufacturing_BillsalData set bill_no_Expired =0" : cmd.ExecuteNonQuery()
        End Try
        '============================================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_no_Expired from Manufacturing_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Manufacturing_BilltINData ADD  bill_no_Expired float  NULL CONSTRAINT [DF_Manufacturing_BilltINData_bill_no_Expired]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Manufacturing_BilltINData set bill_no_Expired =0" : cmd.ExecuteNonQuery()
        End Try
        '============================================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_no_Expired from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD  bill_no_Expired float  NULL CONSTRAINT [DF_Receive_BillsalData_bill_no_Expired]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BillsalData set bill_no_Expired =0" : cmd.ExecuteNonQuery()
        End Try
        '============================================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_no_Expired from Receive_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BilltINData ADD  bill_no_Expired float  NULL CONSTRAINT [DF_Receive_BilltINData_bill_no_Expired]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BilltINData set bill_no_Expired =0" : cmd.ExecuteNonQuery()
        End Try
        '============================================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_no_Expired from IM_Btin_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin_Data ADD  bill_no_Expired float  NULL CONSTRAINT [DF_IM_Btin_Data_bill_no_Expired]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Btin_Data set bill_no_Expired =0" : cmd.ExecuteNonQuery()
        End Try
        '============================================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_no_Expired from IM_Bsal_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal_Data ADD  bill_no_Expired float  NULL CONSTRAINT [DF_IM_Bsal_Data_bill_no_Expired]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal_Data set bill_no_Expired =0" : cmd.ExecuteNonQuery()
        End Try
        '============================================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_no_Expired from BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ADD  bill_no_Expired float  NULL CONSTRAINT [DF_BilltINData_bill_no_Expired]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BilltINData set bill_no_Expired =0" : cmd.ExecuteNonQuery()
        End Try
        '============================================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_no_Expired from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD  bill_no_Expired float  NULL CONSTRAINT [DF_BillsalData_bill_no_Expired]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set bill_no_Expired =0" : cmd.ExecuteNonQuery()
        End Try
        '============================================================================================================================
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_EndDate from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD bill_EndDate float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_EndDate from TmpBillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE TmpBillsalData ADD bill_EndDate float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_EndDate from IM_Bsal_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal_Data ADD bill_EndDate float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_EndDate from IM_Btin_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin_Data ADD bill_EndDate float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_EndDate from data_decayed"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE data_decayed ADD bill_EndDate float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_EndDate from Manufacturing_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Manufacturing_BillsalData ADD bill_EndDate float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_EndDate from Manufacturing_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Manufacturing_BilltINData ADD bill_EndDate float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_EndDate from ItemsTransfer_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsTransfer_BillsalData ADD bill_EndDate float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_EndDate from ItemsTransfer_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsTransfer_BilltINData ADD bill_EndDate float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_EndDate from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD bill_EndDate float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_expired_carton from BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ADD qu_expired_carton float  NULL CONSTRAINT [DF_BilltINData_qu_expired_carton]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "update BilltINData set qu_expired_carton =N'0'"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Bvnd,Treasury_Vst_Receipts,Treasury_Vnd_Receipts,Treasury_Check_Out,Treasury_Check_In,Treasury_Other_Income,Treasury_Deposit,Treasury_Withdraw,Treasury_Capital_Deposit,Treasury_Capital_Withdraw,Treasury_Balance from Treasury"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Treasury ADD Treasury_Bvnd float,Treasury_Vst_Receipts float,Treasury_Vnd_Receipts float,Treasury_Check_Out float,Treasury_Check_In float,Treasury_Other_Income float,Treasury_Deposit float,Treasury_Withdraw float,Treasury_Capital_Deposit float,Treasury_Capital_Withdraw float,Treasury_Balance float"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Treasury set Treasury_Bvnd =0,Treasury_Vst_Receipts =0,Treasury_Vnd_Receipts =0,Treasury_Check_Out =0,Treasury_Check_In =0,Treasury_Other_Income =0,Treasury_Deposit =0,Treasury_Withdraw =0,Treasury_Capital_Deposit =0,Treasury_Capital_Withdraw =0,Treasury_Balance =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Vendorname from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD Vendorname nvarchar(50)"
            cmd.ExecuteNonQuery()

            Dim aray_itm_id As New ArrayList
            Dim Vendorname As String = "" : Dim bill_no As String = ""

            aray_itm_id.Clear()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select id,itm_id from  BilltINData where bill_no<> N'جرد' and bill_no<> N'تسوية'"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_itm_id.Add(dr("itm_id"))
            Loop

            For i As Integer = 0 To aray_itm_id.Count - 1

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select bill_no from BilltINData where itm_id=N'" & aray_itm_id(i).ToString & "' and bill_no<> N'جرد' and bill_no<> N'تسوية'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    bill_no = dr(0).ToString
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select dbo.purchase_bill.Vendorname, dbo.BilltINData.itm_id, dbo.BilltINData.bill_no From dbo.BilltINData LEFT OUTER Join dbo.purchase_bill ON dbo.BilltINData.bill_no = dbo.purchase_bill.bill_No Where (dbo.BilltINData.itm_id = N'" & aray_itm_id(i).ToString & "') AND (dbo.BilltINData.bill_no = N'" & bill_no & "')"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    Vendorname = dr(0).ToString
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update Items set Vendorname =N'" & Vendorname & "' where itm_id =N'" & aray_itm_id(i).ToString & "'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Deposit,Treasury_Withdraw,Capital_Deposit,Capital_Withdraw from Date_Move_Money"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Date_Move_Money ADD Treasury_Deposit float,Treasury_Withdraw float,Capital_Deposit float,Capital_Withdraw float"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Date_Move_Money set Treasury_Deposit =0,Treasury_Withdraw =0,Capital_Deposit =0,Capital_Withdraw =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Assets"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Assets ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Assets set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Employee_Deficit_Increase"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Employee_Deficit_Increase ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Employee_Deficit_Increase set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from MOVES"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE MOVES ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update MOVES set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from MOVESDATA"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE MOVESDATA ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update MOVESDATA set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BilltINData set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from data_decayed"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE data_decayed ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update data_decayed set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from decayed"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE decayed ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update decayed set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from EmployeesDiscountReward"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE EmployeesDiscountReward ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update EmployeesDiscountReward set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Expenses"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Expenses ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Expenses set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from IM_Bsal"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from IM_Bsal_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal_Data ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal_Data set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from IM_Btin"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Btin set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from IM_Btin_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin_Data ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Btin_Data set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from IM_Vnd"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Vnd ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Vnd set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from IM_vndr_disc"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_vndr_disc ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_vndr_disc set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from IM_Vst"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Vst ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Vst set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from IM_Vst_disc"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Vst_disc ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Vst_disc set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Other_Income"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Other_Income ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Other_Income set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from purchase_bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE purchase_bill ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update purchase_bill set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BillsalData set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Receive_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BilltINData ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BilltINData set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Receive_Purchase_bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Purchase_bill ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_Purchase_bill set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Receive_Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Sales_Bill ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_Sales_Bill set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Receive_Vnd"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Vnd ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_Vnd set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Receive_vndr_disc"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_vndr_disc ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_vndr_disc set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Receive_Vst"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Vst ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_Vst set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Receive_Vst_disc"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Vst_disc ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_Vst_disc set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Salary"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Salary ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Salary set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sales_Bill ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Sales_Bill set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Vnd"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vnd ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vnd set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Vnd_Receipts"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vnd_Receipts ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vnd_Receipts set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from vndr_disc"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE vndr_disc ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update vndr_disc set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Vst"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vst ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vst set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Vst_Check_Type"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vst_Check_Type ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vst_Check_Type set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Vst_disc"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vst_disc ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vst_disc set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Vst_disc_other"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vst_disc_other ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vst_disc_other set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Treasury_Code from Vst_Receipts"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vst_Receipts ADD Treasury_Code int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vst_Receipts set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_expired from BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ADD qu_expired float  NULL CONSTRAINT [DF_BilltINData_qu_expired]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_expired from Receive_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BilltINData ADD qu_expired float  NULL CONSTRAINT [DF_Receive_BilltINData_qu_expired]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Error_ID,UserName from ErrorMessage"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ErrorMessage ADD Error_ID int ,UserName nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Vendorname from Receive_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BilltINData ADD Vendorname nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Vendorname from BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ADD Vendorname nvarchar(50)"
            cmd.ExecuteNonQuery()

            Dim aray_itm_id As New ArrayList
            Dim Vendorname As String = "" : Dim bill_no As String = ""

            aray_itm_id.Clear()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select id,itm_id from  BilltINData where bill_no<> N'جرد' and bill_no<> N'تسوية'"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_itm_id.Add(dr("itm_id"))
            Loop

            For i As Integer = 0 To aray_itm_id.Count - 1

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select bill_no from BilltINData where itm_id=N'" & aray_itm_id(i).ToString & "' and bill_no<> N'جرد' and bill_no<> N'تسوية'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    bill_no = dr(0).ToString
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select dbo.purchase_bill.Vendorname, dbo.BilltINData.itm_id, dbo.BilltINData.bill_no From dbo.BilltINData LEFT OUTER Join dbo.purchase_bill ON dbo.BilltINData.bill_no = dbo.purchase_bill.bill_No Where (dbo.BilltINData.itm_id = N'" & aray_itm_id(i).ToString & "') AND (dbo.BilltINData.bill_no = N'" & bill_no & "')"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    Vendorname = dr(0).ToString
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update BilltINData set Vendorname =N'" & Vendorname & "' where itm_id =N'" & aray_itm_id(i).ToString & "' and bill_no<> N'جرد' and bill_no<> N'تسوية'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ResourceName from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD ResourceName nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ResourceName from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD ResourceName nvarchar(50)"
            cmd.ExecuteNonQuery()

            Dim aray_itm_id As New ArrayList
            Dim Vendorname As String = "" : Dim bill_no As String = ""

            aray_itm_id.Clear()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select id,itm_id from  BilltINData where bill_no<> N'جرد' and bill_no<> N'تسوية'"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_itm_id.Add(dr("itm_id"))
            Loop

            For i As Integer = 0 To aray_itm_id.Count - 1

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select bill_no from BilltINData where itm_id=N'" & aray_itm_id(i).ToString & "' and bill_no<> N'جرد' and bill_no<> N'تسوية'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    bill_no = dr(0).ToString
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select dbo.purchase_bill.Vendorname, dbo.BilltINData.itm_id, dbo.BilltINData.bill_no From dbo.BilltINData LEFT OUTER Join dbo.purchase_bill ON dbo.BilltINData.bill_no = dbo.purchase_bill.bill_No Where (dbo.BilltINData.itm_id = N'" & aray_itm_id(i).ToString & "') AND (dbo.BilltINData.bill_no = N'" & bill_no & "')"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    Vendorname = dr(0).ToString
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update BillsalData set ResourceName =N'" & Vendorname & "' where itm_id =N'" & aray_itm_id(i).ToString & "' and bill_no<> N'جرد' and bill_no<> N'تسوية'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Assets"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Assets ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Assets_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PaymentTotal from Vst_Check_Type"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vst_Check_Type ADD PaymentTotal float  NULL CONSTRAINT [DF_Vst_Check_Type_PaymentTotal]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Unity from PriceOfferBill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE PriceOfferBill ADD  Unity nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Vendorname,Stat from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD Vendorname nvarchar(50),Stat nvarchar(50)"
            cmd.ExecuteNonQuery()

            Dim aray_id, aray_bill_no, aray_qu_unity, aray_price As New ArrayList
            Dim Vendorname As String = "" : Dim Stat As String = ""
            Dim Total As Double = 0

            aray_id.Clear() : aray_bill_no.Clear() : aray_qu_unity.Clear() : aray_price.Clear()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select id,bill_no,qu_unity,price from  Receive_BillsalData"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_id.Add(dr("id")) : aray_bill_no.Add(dr("bill_no")) : aray_qu_unity.Add(dr("qu_unity")) : aray_price.Add(dr("price"))
            Loop

            For i As Integer = 0 To aray_id.Count - 1
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select Vendorname,Stat from Sales_Bill where bill_No=N'" & aray_bill_no(i).ToString & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    Vendorname = dr(0).ToString : Stat = dr(1).ToString
                End If
                Total = Val(aray_qu_unity(i).ToString()) * Val(aray_price(i).ToString())
                Total = Math.Round(Total, 2)

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update Receive_BillsalData set Vendorname =N'" & Vendorname & "',Stat = N'" & Stat & "',totalprice =N'" & Total & "' where id =N'" & aray_id(i).ToString & "'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Vendorname,Stat from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD Vendorname nvarchar(50),Stat nvarchar(50)"
            cmd.ExecuteNonQuery()

            Dim aray_id, aray_bill_no, aray_qu_unity, aray_price As New ArrayList
            Dim Vendorname As String = "" : Dim Stat As String = ""
            Dim Total As Double = 0

            aray_id.Clear() : aray_bill_no.Clear() : aray_qu_unity.Clear() : aray_price.Clear()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select id,bill_no,qu_unity,price from  BillsalData"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_id.Add(dr("id")) : aray_bill_no.Add(dr("bill_no")) : aray_qu_unity.Add(dr("qu_unity")) : aray_price.Add(dr("price"))
            Loop

            For i As Integer = 0 To aray_id.Count - 1
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select Vendorname,Stat from Sales_Bill where bill_No=N'" & aray_bill_no(i).ToString & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    Vendorname = dr(0).ToString : Stat = dr(1).ToString
                End If
                Total = Val(aray_qu_unity(i).ToString()) * Val(aray_price(i).ToString())
                Total = Math.Round(Total, 2)

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update BillsalData set Vendorname =N'" & Vendorname & "',Stat = N'" & Stat & "',totalprice =N'" & Total & "' where id =N'" & aray_id(i).ToString & "'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Other_Income"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Other_Income ADD  Company_Branch_ID int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Other_Income set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Other_Income from Date_Move_Money"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Date_Move_Money ADD  Other_Income float  NULL CONSTRAINT [DF_Date_Move_Money_Other_Income]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Details from PrintAllItems"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE PrintAllItems ADD  Details nvarchar(MAX)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Details from PrintSalesPurchases"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE PrintSalesPurchases ADD  Details nvarchar(MAX)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Price_Unity from tmpBillTinData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE tmpBillTinData ADD  Price_Unity float  NULL CONSTRAINT [DF_tmpBillTinData_Price_Unity]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update tmpBillTinData set Price_Unity =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Price_Unity from TmpBillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE TmpBillsalData ADD  Price_Unity float  NULL CONSTRAINT [DF_TmpBillsalData_Price_Unity]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update TmpBillsalData set Price_Unity =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Price_Unity from Manufacturing_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Manufacturing_BilltINData ADD  Price_Unity float  NULL CONSTRAINT [DF_Manufacturing_BilltINData_Price_Unity]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Manufacturing_BilltINData set Price_Unity =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Price_Unity from Manufacturing_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Manufacturing_BillsalData ADD  Price_Unity float  NULL CONSTRAINT [DF_Manufacturing_BillsalData_Price_Unity]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Manufacturing_BillsalData set Price_Unity =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Price_Unity from Receive_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BilltINData ADD  Price_Unity float  NULL CONSTRAINT [DF_Receive_BilltINData_Price_Unity]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BilltINData set Price_Unity =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TinPrice,Price_Unity from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD TinPrice float  NULL CONSTRAINT [DF_Receive_BillsalData_TinPrice]  DEFAULT ((0)),Price_Unity float  NULL CONSTRAINT [DF_Receive_BillsalData_Price_Unity]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BillsalData set TinPrice =0,Price_Unity =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Price_Unity from IM_Btin_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin_Data ADD Price_Unity float  NULL CONSTRAINT [DF_IM_Btin_Data_Price_Unity]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Btin_Data set Price_Unity =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TinPrice,Price_Unity from IM_Bsal_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal_Data ADD TinPrice float  NULL CONSTRAINT [DF_IM_Bsal_Data_TinPrice]  DEFAULT ((0)),Price_Unity float  NULL CONSTRAINT [DF_IM_Bsal_Data_Price_Unity]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal_Data set TinPrice =0,Price_Unity =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Price_Unity from BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ADD Price_Unity float  NULL CONSTRAINT [DF_BilltINData_Price_Unity]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BilltINData set Price_Unity =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateVAT,BeforeVAT from tmpBillTinData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE tmpBillTinData ADD RateVAT float  NULL CONSTRAINT [DF_tmpBillTinData_RateVAT]  DEFAULT ((0)),BeforeVAT float  NULL CONSTRAINT [DF_tmpBillTinData_BeforeVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateVAT,BeforeVAT from TmpBillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE TmpBillsalData ADD RateVAT float  NULL CONSTRAINT [DF_TmpBillsalData_RateVAT]  DEFAULT ((0)),BeforeVAT float  NULL CONSTRAINT [DF_TmpBillsalData_BeforeVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateVAT,BeforeVAT from Receive_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BilltINData ADD RateVAT float  NULL CONSTRAINT [DF_Receive_BilltINData_RateVAT]  DEFAULT ((0)),BeforeVAT float  NULL CONSTRAINT [DF_Receive_BilltINData_BeforeVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BilltINData set RateVAT =0,BeforeVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateVAT,BeforeVAT from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD RateVAT float  NULL CONSTRAINT [DF_Receive_BillsalData_RateVAT]  DEFAULT ((0)),BeforeVAT float  NULL CONSTRAINT [DF_Receive_BillsalData_BeforeVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BillsalData set RateVAT =0,BeforeVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateVAT,BeforeVAT from IM_Btin_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin_Data ADD RateVAT float  NULL CONSTRAINT [DF_IM_Btin_Data_RateVAT]  DEFAULT ((0)),BeforeVAT float  NULL CONSTRAINT [DF_IM_Btin_Data_BeforeVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Btin_Data set RateVAT =0,BeforeVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateVAT,BeforeVAT from IM_Bsal_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal_Data ADD RateVAT float  NULL CONSTRAINT [DF_IM_Bsal_Data_RateVAT]  DEFAULT ((0)),BeforeVAT float  NULL CONSTRAINT [DF_IM_Bsal_Data_BeforeVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal_Data set RateVAT =0,BeforeVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateVAT,BeforeVAT from BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ADD RateVAT float  NULL CONSTRAINT [DF_BilltINData_RateVAT]  DEFAULT ((0)),BeforeVAT float  NULL CONSTRAINT [DF_BilltINData_BeforeVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BilltINData set RateVAT =0,BeforeVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateVAT,BeforeVAT from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD RateVAT float  NULL CONSTRAINT [DF_BillsalData_RateVAT]  DEFAULT ((0)),BeforeVAT float  NULL CONSTRAINT [DF_BillsalData_BeforeVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set RateVAT =0,BeforeVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ValueVAT from IM_Btin"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin ADD ValueVAT float  NULL CONSTRAINT [DF_IM_Btin_ValueVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Btin set ValueVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ValueVAT from IM_Bsal"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal ADD ValueVAT float  NULL CONSTRAINT [DF_IM_Bsal_ValueVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal set ValueVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ValueVAT from Receive_Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Sales_Bill ADD ValueVAT float  NULL CONSTRAINT [DF_Receive_Sales_Bill_ValueVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_Sales_Bill set ValueVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ValueVAT from Receive_Purchase_bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Purchase_bill ADD ValueVAT float  NULL CONSTRAINT [DF_Receive_Purchase_bill_ValueVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_Purchase_bill set ValueVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ValueVAT from purchase_bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE purchase_bill ADD ValueVAT float  NULL CONSTRAINT [DF_purchase_bill_ValueVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update purchase_bill set ValueVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ValueVAT from Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sales_Bill ADD ValueVAT float  NULL CONSTRAINT [DF_Sales_Bill_ValueVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Sales_Bill set ValueVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select AfterValueVAT,RateVAT,ValueVAT from Expenses"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Expenses ADD AfterValueVAT float  NULL CONSTRAINT [DF_Expenses_AfterValueVAT]  DEFAULT ((0)),RateVAT float  NULL CONSTRAINT [DF_Expenses_RateVAT]  DEFAULT ((0)),ValueVAT float  NULL CONSTRAINT [DF_Expenses_ValueVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Expenses set AfterValueVAT =0,RateVAT =0,ValueVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateVAT from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD RateVAT float  NULL CONSTRAINT [DF_Items_RateVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Items set RateVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ValueVAT from tmpBillTinData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE tmpBillTinData ADD ValueVAT float  NULL CONSTRAINT [DF_tmpBillTinData_ValueVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ValueVAT from TmpBillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE TmpBillsalData ADD ValueVAT float  NULL CONSTRAINT [DF_TmpBillsalData_ValueVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ValueVAT from Receive_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BilltINData ADD ValueVAT float  NULL CONSTRAINT [DF_Receive_BilltINData_ValueVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BilltINData set ValueVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ValueVAT from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD ValueVAT float  NULL CONSTRAINT [DF_Receive_BillsalData_ValueVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BillsalData set ValueVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ValueVAT from IM_Btin_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin_Data ADD ValueVAT float  NULL CONSTRAINT [DF_IM_Btin_Data_ValueVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Btin_Data set ValueVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ValueVAT from IM_Bsal_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal_Data ADD ValueVAT float  NULL CONSTRAINT [DF_IM_Bsal_Data_ValueVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal_Data set ValueVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ValueVAT from BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ADD ValueVAT float  NULL CONSTRAINT [DF_BilltINData_ValueVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BilltINData set ValueVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ValueVAT from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD ValueVAT float  NULL CONSTRAINT [DF_BillsalData_ValueVAT]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set ValueVAT =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select OrderID from Sales_Bill" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sales_Bill ADD OrderID int" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select SalaryDate from Salary"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Salary ADD SalaryDate nvarchar(50)"
            cmd.ExecuteNonQuery()

        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PurchaseTax from purchase_bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE purchase_bill ADD PurchaseTax float"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update purchase_bill set PurchaseTax =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PurchaseTax from Receive_Purchase_bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Purchase_bill ADD PurchaseTax float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateWholePrice,RateWholeWholePrice,RateMinimumSalPrice from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD RateWholePrice float  NULL CONSTRAINT [DF_Items_RateWholePrice]  DEFAULT ((0)),RateWholeWholePrice float  NULL CONSTRAINT [DF_Items_RateWholeWholePrice]  DEFAULT ((0)),RateMinimumSalPrice float  NULL CONSTRAINT [DF_Items_RateMinimumSalPrice]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Items set RateWholePrice =0,RateWholeWholePrice =0,RateMinimumSalPrice =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select BalanceBarcode from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD BalanceBarcode int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Items set BalanceBarcode =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id_Unity from ItemsUnity"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsUnity ADD itm_id_Unity nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select disc_type from Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sales_Bill ADD disc_type nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Car_Data_ID from MaintenanceOrderRunning"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE MaintenanceOrderRunning ADD Car_Data_ID int  NULL CONSTRAINT [DF_MaintenanceOrderRunning_Car_Data_ID]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Supervisor_ID,Recipient_ID from MaintenanceOrderRunning"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE MaintenanceOrderRunning ADD Supervisor_ID int  NULL CONSTRAINT [DF_MaintenanceOrderRunning_Supervisor_ID]  DEFAULT ((0)),Recipient_ID int  NULL CONSTRAINT [DF_MaintenanceOrderRunning_Recipient_ID]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select KiloMeter from Maintenance_Car_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Maintenance_Car_Data ADD KiloMeter int  NULL CONSTRAINT [DF_Maintenance_Car_Data_KiloMeter]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Car_Number from Maintenance_Car_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Maintenance_Car_Data ADD Car_Number int  NULL CONSTRAINT [DF_Maintenance_Car_Data_Car_Number]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Sheft_Status"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sheft_Status ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Sheft_Status_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Sheft_Status set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Maintenance_Drivers"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Maintenance_Drivers ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Maintenance_Drivers_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Maintenance_Drivers set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD Company_Branch_ID int  NULL CONSTRAINT [DF_BillsalData_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD Company_Branch_ID int  NULL CONSTRAINT [DF_BillsalData_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ADD Company_Branch_ID int  NULL CONSTRAINT [DF_BilltINData_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BilltINData set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Capital_Partner_Withdrawals"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Capital_Partner_Withdrawals ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Capital_Partner_Withdrawals_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Capital_Partner_Withdrawals set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Customers"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Customers ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Customers_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Customers set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from data_decayed"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE data_decayed ADD Company_Branch_ID int  NULL CONSTRAINT [DF_data_decayed_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update data_decayed set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Date_Move_Money"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Date_Move_Money ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Date_Move_Money_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Date_Move_Money set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from decayed"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE decayed ADD Company_Branch_ID int  NULL CONSTRAINT [DF_decayed_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update decayed set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Employee_Deficit_Increase"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Employee_Deficit_Increase ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Employee_Deficit_Increase_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Employee_Deficit_Increase set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Employees"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Employees ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Employees_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Employees set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from EmployeesDiscountReward"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE EmployeesDiscountReward ADD Company_Branch_ID int  NULL CONSTRAINT [DF_EmployeesDiscountReward_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update EmployeesDiscountReward set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from ErrorMessage"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ErrorMessage ADD Company_Branch_ID int  NULL CONSTRAINT [DF_ErrorMessage_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ErrorMessage set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Expenses"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Expenses ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Expenses_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Expenses set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Group_Branch"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Group_Branch ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Group_Branch_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Group_Branch set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Groups"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Groups ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Groups_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Groups set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from IM_Btin"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin ADD Company_Branch_ID int  NULL CONSTRAINT [DF_IM_Btin_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Btin set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from IM_Bsal"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal ADD Company_Branch_ID int  NULL CONSTRAINT [DF_IM_Bsal_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from IM_Bsal_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal_Data ADD Company_Branch_ID int  NULL CONSTRAINT [DF_IM_Bsal_Data_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal_Data set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from IM_Btin_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin_Data ADD Company_Branch_ID int  NULL CONSTRAINT [DF_IM_Btin_Data_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Btin_Data set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from IM_Vnd"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Vnd ADD Company_Branch_ID int  NULL CONSTRAINT [DF_IM_Vnd_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Vnd set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from IM_vndr_disc"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_vndr_disc ADD Company_Branch_ID int  NULL CONSTRAINT [DF_IM_vndr_disc_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_vndr_disc set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from IM_Vst"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Vst ADD Company_Branch_ID int  NULL CONSTRAINT [DF_IM_Vst_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Vst set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from IM_Vst_disc"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Vst_disc ADD Company_Branch_ID int  NULL CONSTRAINT [DF_IM_Vst_disc_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Vst_disc set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Items_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Items set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from ItemsDeleted"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsDeleted ADD Company_Branch_ID int  NULL CONSTRAINT [DF_ItemsDeleted_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsDeleted set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from ItemsOffersDiscounts"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsOffersDiscounts ADD Company_Branch_ID int  NULL CONSTRAINT [DF_ItemsOffersDiscounts_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsOffersDiscounts set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from ItemsTransfer_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsTransfer_BillsalData ADD Company_Branch_ID int  NULL CONSTRAINT [DF_ItemsTransfer_BillsalData_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsTransfer_BillsalData set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from ItemsTransfer_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsTransfer_BilltINData ADD Company_Branch_ID int  NULL CONSTRAINT [DF_ItemsTransfer_BilltINData_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsTransfer_BilltINData set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from ItemsTransferData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsTransferData ADD Company_Branch_ID int  NULL CONSTRAINT [DF_ItemsTransferData_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsTransferData set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from MaintenanceOrderRunning"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE MaintenanceOrderRunning ADD Company_Branch_ID int  NULL CONSTRAINT [DF_MaintenanceOrderRunning_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update MaintenanceOrderRunning set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from MaintenanceOrderRunningAdd"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE MaintenanceOrderRunningAdd ADD Company_Branch_ID int  NULL CONSTRAINT [DF_MaintenanceOrderRunningAdd_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update MaintenanceOrderRunningAdd set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Manufacturing_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Manufacturing_BillsalData ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Manufacturing_BillsalData_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Manufacturing_BillsalData set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Manufacturing_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Manufacturing_BilltINData ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Manufacturing_BilltINData_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Manufacturing_BilltINData set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from ManufacturingDismissalNotice"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ManufacturingDismissalNotice ADD Company_Branch_ID int  NULL CONSTRAINT [DF_ManufacturingDismissalNotice_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ManufacturingDismissalNotice set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from ManufacturingFillOrder"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ManufacturingFillOrder ADD Company_Branch_ID int  NULL CONSTRAINT [DF_ManufacturingFillOrder_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ManufacturingFillOrder set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from ManufacturingProduct"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ManufacturingProduct ADD Company_Branch_ID int  NULL CONSTRAINT [DF_ManufacturingProduct_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ManufacturingProduct set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from ManufacturingProductAdd"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ManufacturingProductAdd ADD Company_Branch_ID int  NULL CONSTRAINT [DF_ManufacturingProductAdd_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ManufacturingProductAdd set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Permtions"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Permtions ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Permtions_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Permtions set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from PrintAllItems"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE PrintAllItems ADD Company_Branch_ID int  NULL CONSTRAINT [DF_PrintAllItems_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update PrintAllItems set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from PrintSalesPurchases"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE PrintSalesPurchases ADD Company_Branch_ID int  NULL CONSTRAINT [DF_PrintSalesPurchases_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update PrintSalesPurchases set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from purchase_bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE purchase_bill ADD Company_Branch_ID int  NULL CONSTRAINT [DF_purchase_bill_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update purchase_bill set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Receive_BillsalData_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BillsalData set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Receive_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BilltINData ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Receive_BilltINData_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BilltINData set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Receive_Purchase_bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Purchase_bill ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Receive_Purchase_bill_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_Purchase_bill set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Receive_Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Sales_Bill ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Receive_Sales_Bill_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_Sales_Bill set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Receive_Vnd"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Vnd ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Receive_Vnd_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_Vnd set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Receive_vndr_disc"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_vndr_disc ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Receive_vndr_disc_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_vndr_disc set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Receive_Vst"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Vst ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Receive_Vst_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_Vst set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Receive_Vst_disc"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Vst_disc ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Receive_Vst_disc_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_Vst_disc set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Salary"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Salary ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Salary_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Salary set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sales_Bill ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Sales_Bill_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Sales_Bill set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Stores"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Stores ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Stores_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Stores set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Treasury"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Treasury ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Treasury_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Treasury set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Users"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Users ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Users_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Users set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from vendors"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE vendors ADD Company_Branch_ID int  NULL CONSTRAINT [DF_vendors_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update vendors set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Vnd"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vnd ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Vnd_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vnd set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Vnd_Receipts"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vnd_Receipts ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Vnd_Receipts_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vnd_Receipts set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from vndr_disc"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE vndr_disc ADD Company_Branch_ID int  NULL CONSTRAINT [DF_vndr_disc_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update vndr_disc set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Vst"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vst ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Vst_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vst set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Vst_Check_Type"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vst_Check_Type ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Vst_Check_Type_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vst_Check_Type set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Vst_disc"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vst_disc ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Vst_disc_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vst_disc set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Vst_disc_other"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vst_disc_other ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Vst_disc_other_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vst_disc_other set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Company_Branch_ID from Vst_Receipts"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vst_Receipts ADD Company_Branch_ID int  NULL CONSTRAINT [DF_Vst_Receipts_Company_Branch_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vst_Receipts set Company_Branch_ID =1" : cmd.ExecuteNonQuery()
        End Try

        '=================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PriceMedium, PriceSmall from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD PriceMedium float  NULL CONSTRAINT [DF_Items_PriceMedium]  DEFAULT ((0)),PriceSmall float  NULL CONSTRAINT [DF_Items_PriceSmall]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select StatusOrder from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD StatusOrder int  NULL CONSTRAINT [DF_BillsalData_StatusOrder]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set StatusOrder =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select StatusOrder from Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sales_Bill ADD StatusOrder int  NULL CONSTRAINT [DF_Sales_Bill_StatusOrder]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Sales_Bill set StatusOrder =0" : cmd.ExecuteNonQuery()
        End Try


        'Dim aray_1, aray_2, aray_3 As New ArrayList
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select itm_id,TinPrice,SalPrice from Items"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_1.Add(dr(0).ToString)
        '    aray_2.Add(dr(1).ToString)
        '    aray_3.Add(dr(2).ToString)
        'Loop
        'For i As Integer = 0 To aray_1.Count - 1
        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "update ItemsUnity set TinPriceUnit =N'" + aray_2(i).ToString + "',SalPriceUnit =N'" + aray_3(i).ToString + "' where itm_id=N'" & aray_1(i).ToString & "'" : cmd.ExecuteNonQuery()
        'Next


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select purchase,Bvnd,Vst_disc,vndr_disc,Vst_disc_other,Vst_Receipts,Vnd_Receipts,Check_Out,Check_In,Treasury_Code from Date_Move_Money"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Date_Move_Money ADD purchase float  NULL CONSTRAINT [DF_Date_Move_Money_purchase]  DEFAULT ((0)),Bvnd float  NULL CONSTRAINT [DF_Date_Move_Money_Bvnd]  DEFAULT ((0)),Vst_disc float  NULL CONSTRAINT [DF_Date_Move_Money_Vst_disc]  DEFAULT ((0)),vndr_disc float  NULL CONSTRAINT [DF_Date_Move_Money_vndr_disc]  DEFAULT ((0)),Vst_disc_other float  NULL CONSTRAINT [DF_Date_Move_Money_Vst_disc_other]  DEFAULT ((0)),Vst_Receipts float  NULL CONSTRAINT [DF_Date_Move_Money_Vst_Receipts]  DEFAULT ((0)),Vnd_Receipts float  NULL CONSTRAINT [DF_Date_Move_Money_Vnd_Receipts]  DEFAULT ((0)),Check_Out float  NULL CONSTRAINT [DF_Date_Move_Money_Check_Out]  DEFAULT ((0)),Check_In float  NULL CONSTRAINT [DF_Date_Move_Money_Check_In]  DEFAULT ((0)),Treasury_Code float  NULL CONSTRAINT [DF_Date_Move_Money_Treasury_Code]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Date_Move_Money set Treasury_Code =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select UnitySize from tmpBillTinData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE tmpBillTinData ADD UnitySize nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsUnity set UnitySize_ID =1 where UnitySize_ID =0" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select UnitySize_ID from ItemsUnity"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsUnity ADD UnitySize_ID float  NULL CONSTRAINT [DF_ItemsUnity_UnitySize_ID]  DEFAULT ((1))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsUnity set UnitySize_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CMPLogoPath from Company"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Company ADD CMPLogoPath nvarchar(255)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateBankExpensesVisa,ValueBankExpensesVisa from Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sales_Bill ADD RateBankExpensesVisa float  NULL CONSTRAINT [DF_Sales_Bill_RateBankExpensesVisa]  DEFAULT ((0)),ValueBankExpensesVisa float  NULL CONSTRAINT [DF_Sales_Bill_ValueBankExpensesVisa]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Sales_Bill set RateBankExpensesVisa =0,ValueBankExpensesVisa =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select VnReceipts from vendors"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE vendors ADD VnReceipts float  NULL CONSTRAINT [DF_vendors_VnReceipts]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update vendors set VnReceipts =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CreditPrevious,DebitPrevious,CreditCurrent,DebitCurrent from Receive_Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Sales_Bill ADD CreditPrevious float  NULL CONSTRAINT [DF_Receive_Sales_Bill_CreditPrevious]  DEFAULT ((0)),DebitPrevious float  NULL CONSTRAINT [DF_Receive_Sales_Bill_DebitPrevious]  DEFAULT ((0)),CreditCurrent float  NULL CONSTRAINT [DF_Receive_Sales_Bill_CreditCurrent]  DEFAULT ((0)),DebitCurrent float  NULL CONSTRAINT [DF_Receive_Sales_Bill_DebitCurrent]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CreditPrevious,DebitPrevious,CreditCurrent,DebitCurrent from Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sales_Bill ADD CreditPrevious float  NULL CONSTRAINT [DF_Sales_Bill_CreditPrevious]  DEFAULT ((0)),DebitPrevious float  NULL CONSTRAINT [DF_Sales_Bill_DebitPrevious]  DEFAULT ((0)),CreditCurrent float  NULL CONSTRAINT [DF_Sales_Bill_CreditCurrent]  DEFAULT ((0)),DebitCurrent float  NULL CONSTRAINT [DF_Sales_Bill_DebitCurrent]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Sales_Bill set CreditPrevious =0,DebitPrevious =0,CreditCurrent =0,DebitCurrent =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TaxValue from vendors"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE vendors ADD TaxValue float  NULL CONSTRAINT [DF_vendors_TaxValue]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PurchaseTaxValue from purchase_bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE purchase_bill ADD PurchaseTaxValue float  NULL CONSTRAINT [DF_purchase_bill_PurchaseTaxValue]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update purchase_bill set PurchaseTaxValue =0" : cmd.ExecuteNonQuery()


            Dim aray_bill_no, aray_totalpricebeforedisc, aray_PurchaseTax As New ArrayList
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_No,totalpricebeforedisc,PurchaseTax from purchase_bill"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_bill_no.Add(dr(0))
                aray_totalpricebeforedisc.Add(dr(1))
                aray_PurchaseTax.Add(dr(2))
            Loop
            Dim Total As Double = 0
            For i As Integer = 0 To aray_bill_no.Count - 1
                Total = Format(Val(aray_totalpricebeforedisc(i).ToString) * Val(aray_PurchaseTax(i).ToString) / 100, "Fixed")
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update purchase_bill set PurchaseTaxValue =N'" & Total & "' where bill_No=N'" & aray_bill_no(i).ToString & "'" : cmd.ExecuteNonQuery()
            Next
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PurchaseTaxValue from Receive_Purchase_bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Purchase_bill ADD PurchaseTaxValue float  NULL CONSTRAINT [DF_Receive_Purchase_bill_PurchaseTaxValue]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Height,Width,Altitude,Density from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD Height float  NULL CONSTRAINT [DF_Items_Height]  DEFAULT ((0)),Width float NULL CONSTRAINT [DF_Items_Width]  DEFAULT ((0)),Altitude float NULL CONSTRAINT [DF_Items_Altitude]  DEFAULT ((0)),Density float NULL CONSTRAINT [DF_Items_Density]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Items set Height =0,Width =0,Altitude =0,Density =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select SalarySystem_ID from EmployeesDiscountReward"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE EmployeesDiscountReward ADD SalarySystem_ID int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update EmployeesDiscountReward set SalarySystem_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select SalarySystem_ID from Salary"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Salary ADD SalarySystem_ID int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Salary set SalarySystem_ID =1" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CMPEndorsement from Company"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Company ADD CMPEndorsement nvarchar(255)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateValues from Receive_Purchase_bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Purchase_bill ADD RateValues nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateValues from Receive_Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Sales_Bill ADD RateValues nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateValues from IM_Bsal"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal ADD RateValues nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateValues from IM_Btin"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin ADD RateValues nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateValues from purchase_bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE purchase_bill ADD RateValues nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select RateValues from Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sales_Bill ADD RateValues nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TotalCostPrice from ManufacturingFillOrder"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ManufacturingFillOrder ADD TotalCostPrice float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_Unity from Manufacturing_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Manufacturing_BillsalData ADD itm_Unity nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_noNotice from Manufacturing_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Manufacturing_BillsalData ADD bill_noNotice nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_noNotice from Manufacturing_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Manufacturing_BilltINData ADD bill_noNotice nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select group_branch from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD group_branch nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select group_branch from BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ADD group_branch nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Cust_Code from Customers"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Customers ADD Cust_Code int"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select GeoArea_Code from Customers"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Customers ADD GeoArea_Code int"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Emp_Code from Customers"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Customers ADD Emp_Code int"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Bran_code from Employees"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Employees ADD Bran_code int,Emp_Type_Code int,Emp_Status_Code int"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Emp_Salary,Emp_Rate_Hour,Emp_Count_Hour from Employees"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Employees ADD Emp_Salary float,Emp_Rate_Hour float,Emp_Count_Hour float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select QuickSearch from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD QuickSearch int NULL CONSTRAINT [DF_Items_QuickSearch]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Items set QuickSearch =0" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Groups_Image from Groups"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Groups ADD Groups_Image image"
            cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Items_Images from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD Items_Images image"
            cmd.ExecuteNonQuery()
        End Try

        connect()

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Weightqunt from ManufacturingProduct"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ManufacturingProduct ADD Weightqunt float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Manufacturing_Allowance from ManufacturingProduct"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ManufacturingProduct ADD Manufacturing_Allowance float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Filling_Allowance from ManufacturingProduct"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ManufacturingProduct ADD Filling_Allowance float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select totalprice from ManufacturingProductAdd"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ManufacturingProductAdd ADD totalprice float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_Unity,qu_unity from ManufacturingProductAdd"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            Try
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "ALTER TABLE ManufacturingProductAdd ADD itm_Unity nvarchar(50),qu_unity float"
                cmd.ExecuteNonQuery()
            Catch ex1 As Exception
            End Try
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CostPrice from ManufacturingProduct"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ManufacturingProduct ADD CostPrice float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberMaterial from ManufacturingProduct"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ManufacturingProduct ADD NumberMaterial float"
            cmd.ExecuteNonQuery()
        End Try

        '==================================================================================================================
        'Customers
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Mobile,Apartment,Role,Region,Mark from Customers"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Customers ADD Mobile nvarchar(50),Apartment nvarchar(50),Role nvarchar(50),Region nvarchar(100),Mark nvarchar(100)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Items_Images from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD Items_Images image"
            cmd.ExecuteNonQuery()
        End Try


        Try
            connect()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Deficit_Increase_Code from Employee_Deficit_Increase"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Employee_Deficit_Increase ADD Deficit_Increase_Code int"
            cmd.ExecuteNonQuery()
        End Try

        '==================================================================================================================

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Deficit_Increase_Code from Employee_Deficit_Increase"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Employee_Deficit_Increase ADD Deficit_Increase_Code int"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ACCMain_Code,ACCGroub_Code from AccountsTree"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE AccountsTree ADD ACCMain_Code int,ACCGroub_Code int"
            cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CMPWebsite from Company"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Company ADD CMPWebsite nvarchar(255)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CMPNameDown from Company"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Company ADD CMPNameDown nvarchar(255)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select DeviceModel_ID,DeviceBrand_ID,Visit_Date from MaintenanceOrderRunning"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE MaintenanceOrderRunning ADD DeviceModel_ID int,DeviceBrand_ID int,Visit_Date nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select StausMainStore from Stores"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Stores ADD StausMainStore int"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_Notes from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD itm_Notes nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Discounts from BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD Discounts float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select StoreCarton from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD StoreCarton float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PriceType_ID from Customers"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Customers ADD PriceType_ID int"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CMPCommercialRecord,CMPTaxCard from Company"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Company ADD CMPCommercialRecord nvarchar(255),CMPTaxCard nvarchar(255)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select DiscountTax,bill_NoTax from Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sales_Bill ADD DiscountTax float,bill_NoTax float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select CashWithdrawal,NetSalary from Date_Move_Money"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Date_Move_Money ADD CashWithdrawal float,NetSalary float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select DiscountTax,bill_NoTax from Receive_Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Sales_Bill ADD DiscountTax float,bill_NoTax float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Sheft_Number,itm_Notes,Discounts from Receive_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD Sheft_Number nvarchar(50),itm_Notes nvarchar(50),Discounts float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Check_Type,PaymentTotal,BillNo_Check,EmpName from Receive_Vst"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Vst ADD Check_Type nvarchar(50),PaymentTotal float,BillNo_Check float,EmpName nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_unity from BillsalData" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ADD qu_unity float" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_unity from BilltINData" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ADD qu_unity float" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_unity from IM_Bsal_Data" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal_Data ADD qu_unity float" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_unity from IM_Btin_Data" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin_Data ADD qu_unity float" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_unity from IM_Btin_Data" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin_Data ADD qu_unity float" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_unity from Receive_BillsalData" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ADD qu_unity float" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_unity from Receive_BilltINData" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BilltINData ADD qu_unity float" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_unity from TmpBillsalData" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE TmpBillsalData ADD qu_unity float" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_unity from tmpBillTinData" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE tmpBillTinData ADD qu_unity float" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_unity from Manufacturing_BillsalData" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Manufacturing_BillsalData ADD qu_unity float" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_unity from ManufacturingProductAdd" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ManufacturingProductAdd ADD qu_unity float" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select VstDiscOther,BVstPay,BVstDiscount from Customers"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Customers ADD VstDiscOther float,BVstPay float,BVstDiscount float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select BVndPay,BVndDiscount from vendors"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE vendors ADD BVndPay float,BVndDiscount float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_ProductionDate from BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ADD bill_ProductionDate nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select DeliveryService from Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sales_Bill ADD DeliveryService float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select id from PrintSalesPurchases"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE PrintSalesPurchases ADD id int IDENTITY(1,1) NOT NULL"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select id from PrintAllItems"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE PrintAllItems ADD id int IDENTITY(1,1) NOT NULL"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ExpensesBill from Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Sales_Bill ADD ExpensesBill float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select DeliveryService,ExpensesBill from Receive_Sales_Bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_Sales_Bill ADD DeliveryService float,ExpensesBill float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select EmpName from vst" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE vst ADD EmpName nvarchar(50)" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select VND_Date_Maturity from vst" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE vst ADD VND_Date_Maturity nvarchar(50)" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Check_Type from vst" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE vst ADD Check_Type nvarchar(50)" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select PaymentTotal from vst" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE vst ADD PaymentTotal float" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select BillNo_Check from vst" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE vst ADD BillNo_Check float" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select OrderID from vst" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE vst ADD OrderID int" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select bill_EndDate from tmpBillTinData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE tmpBillTinData ADD bill_EndDate nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select SDate from Additional"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Additional ADD SDate nvarchar(50)"
            cmd.ExecuteNonQuery()
        End Try


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Permtions set PermtionName =N'المبيعات' where PermtionName=N'الكاشير البائع'" : cmd.ExecuteNonQuery()

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ItemsManufacturing from ItemsTransferData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsTransferData ADD ItemsManufacturing nvarchar(200)"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NotViewItems from Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ADD NotViewItems int"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Items set NotViewItems =N'0'" : cmd.ExecuteNonQuery()

        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id_Manufacturing,EMPID from Manufacturing_BillsalData" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Manufacturing_BillsalData ADD itm_id_Manufacturing nvarchar(50),EMPID int" : cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select EMPID from Manufacturing_BilltINData" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Manufacturing_BilltINData ADD EMPID int" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id_Manufacturing from Manufacturing_BilltINData" : dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Manufacturing_BilltINData ADD itm_id_Manufacturing nvarchar(50)" : cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select VnReceipts from PrintSalesPurchases"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE PrintSalesPurchases ADD VnReceipts float"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select VnReceipts from Customers"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Customers ADD VnReceipts float"
            cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Total_Qunt from Customers"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Customers ADD Total_Qunt float  NULL CONSTRAINT [DF_Customers_Total_Qunt]  DEFAULT ((0))"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Customers set Total_Qunt =0" : cmd.ExecuteNonQuery()

            Dim Total_Qunt As Double
            Dim Vendorname As String = ""
            Dim aray_id As New ArrayList
            Dim aray_Vendorname As New ArrayList

            aray_id.Clear()
            aray_Vendorname.Clear()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select id,Vendorname from  Customers"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_id.Add(dr("id"))
                aray_Vendorname.Add(dr("Vendorname"))
            Loop

            For i As Integer = 0 To aray_id.Count - 1

                Vendorname = aray_Vendorname(i).ToString
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select sum(qu_unity) from BillsalData where Vendorname = N'" & Vendorname & "'" : dr = cmd.ExecuteReader : dr.Read()
                If dr(0) Is DBNull.Value Then Total_Qunt = 0 Else Total_Qunt = dr(0)
                Total_Qunt = Math.Round(Total_Qunt, 2)


                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update Customers set Total_Qunt =N'" & Total_Qunt & "' where Vendorname =N'" & aray_Vendorname(i).ToString & "'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next

        End Try

        '============================================================================================================================

        '=========================================================================================
        'Try
        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "select MaintenanceStatus_ID from MaintenanceStatus where MaintenanceStatus_ID = 4"
        '    dr = cmd.ExecuteReader : dr.Read()
        '    If dr.HasRows = False Then
        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        S = "insert into MaintenanceStatus(MaintenanceStatus_ID,MaintenanceStatus_Name) values ("
        '        S = S & "'4' ,N'مرتجع صيانة')"
        '        cmd.CommandText = S : cmd.ExecuteNonQuery()
        '    End If
        'Catch ex As Exception
        'End Try


    End Sub

    Public Sub ALTER_COLUMN()
        '********************************************************
        '******** ALTER TABLE - DROP COLUMN ********
        'cmd.CommandText = "ALTER TABLE Items DROP COLUMN rng"
        '********************************************************
        '******** ALTER TABLE - ALTER/MODIFY COLUMN ********
        'cmd.CommandText = "ALTER TABLE table_name ALTER COLUMN column_name datatype"
        '********************************************************
        '******** ALTER TABLE - ALTER/MODIFY COLUMN ********
        'cmd.CommandText = "ALTER TABLE table_name ALTER COLUMN column_name datatype"
        '********************************************************

        connect()

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsOffersDiscounts ALTER COLUMN bill_No float"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE ItemsOffersDiscounts ALTER COLUMN StateDisc nvarchar(50)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BillsalData ALTER COLUMN CurrentStock float"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Receive_BilltINData ALTER COLUMN CurrentStock float"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Btin_Data ALTER COLUMN CurrentStock float"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE IM_Bsal_Data ALTER COLUMN CurrentStock float"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BilltINData ALTER COLUMN CurrentStock float"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE BillsalData ALTER COLUMN CurrentStock float"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Customers ALTER COLUMN ValueVAT float"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE PrintAllItems ALTER COLUMN Notes nvarchar(MAX)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE PrintAllItems ALTER COLUMN sname nvarchar(MAX)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE PrintSalesPurchases ALTER COLUMN itm_name nvarchar(MAX)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE PrintSalesPurchases ALTER COLUMN CustomerName nvarchar(MAX)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE PrintSalesPurchases ALTER COLUMN Vendorname nvarchar(MAX)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Additional ALTER COLUMN TimeLeave nvarchar(50)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Additional ALTER COLUMN TimeAttendance nvarchar(50)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE PrintAllItems ALTER COLUMN bill_date nvarchar(50)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Customers ALTER COLUMN notes nvarchar(MAX)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE vendors ALTER COLUMN notes nvarchar(MAX)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE vndr_disc ALTER COLUMN det nvarchar(MAX)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vnd ALTER COLUMN VND_dec nvarchar(MAX)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vst_Receipts ALTER COLUMN VND_dec nvarchar(MAX)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vst_disc_other ALTER COLUMN det nvarchar(MAX)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vst_disc ALTER COLUMN det nvarchar(MAX)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vst_Check_Type ALTER COLUMN VND_dec nvarchar(MAX)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Vst ALTER COLUMN VND_dec nvarchar(MAX)"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "ALTER TABLE Items ALTER COLUMN rng float"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

    End Sub

    Public Sub Rename_Tabels()

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "sp_rename 'Drivers', 'Maintenance_Drivers'"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "sp_rename 'Capital_CheckOut_Deposit', 'Capital_Partner_Withdrawals'"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try

    End Sub

    Public Sub CREATE_VIEW()
        connect()


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_ItemsOffersDiscounts"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_ItemsOffersDiscounts"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_ItemsOffersDiscounts As   Select dbo.ItemsOffersDiscounts.ID, dbo.ItemsOffersDiscounts.bill_No, dbo.ItemsOffersDiscounts.bill_date, dbo.ItemsOffersDiscounts.itm_id, dbo.Items.group_name, dbo.ItemsOffersDiscounts.sname, dbo.ItemsOffersDiscounts.RatePriceOffers,    dbo.ItemsOffersDiscounts.PriceOffers, dbo.ItemsOffersDiscounts.UserName, dbo.ItemsOffersDiscounts.bill_date_End, dbo.ItemsOffersDiscounts.PriceOffersBefore, dbo.ItemsOffersDiscounts.StateDisc,  dbo.ItemsOffersDiscounts.Company_Branch_ID  From dbo.ItemsOffersDiscounts LEFT OUTER Join    dbo.Items ON dbo.ItemsOffersDiscounts.itm_id = dbo.Items.itm_id  Group By dbo.ItemsOffersDiscounts.ID, dbo.ItemsOffersDiscounts.bill_No, dbo.ItemsOffersDiscounts.bill_date, dbo.ItemsOffersDiscounts.itm_id, dbo.Items.group_name, dbo.ItemsOffersDiscounts.sname,   dbo.ItemsOffersDiscounts.RatePriceOffers, dbo.ItemsOffersDiscounts.PriceOffers, dbo.ItemsOffersDiscounts.UserName, dbo.ItemsOffersDiscounts.bill_date_End, dbo.ItemsOffersDiscounts.PriceOffersBefore, dbo.ItemsOffersDiscounts.StateDisc, dbo.ItemsOffersDiscounts.Company_Branch_ID"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_DebitCustomersDelegate"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_DebitCustomersDelegate"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_DebitCustomersDelegate As  Select TOP(100) PERCENT dbo.BillsalData.bill_no, dbo.Sales_Bill.Vendorname, dbo.BillsalData.UserName As StateCust, dbo.BillsalData.qu, dbo.BillsalData.price, dbo.BillsalData.totalprice, dbo.BillsalData.bill_date, dbo.Sales_Bill.Stat As State,    dbo.BillsalData.totalprice AS vnamntcredit, dbo.BillsalData.itm_name, dbo.Employees.NameEmployee, dbo.BillsalData.Company_Branch_ID  From dbo.Customers INNER Join   dbo.Sales_Bill ON dbo.Customers.Vendorname = dbo.Sales_Bill.Vendorname LEFT OUTER Join   dbo.Employees ON dbo.Customers.Emp_Code = dbo.Employees.EMPID RIGHT OUTER Join  dbo.BillsalData ON dbo.Sales_Bill.bill_No = dbo.BillsalData.bill_no Group By dbo.BillsalData.bill_no, dbo.Sales_Bill.Vendorname, dbo.BillsalData.UserName, dbo.BillsalData.qu, dbo.BillsalData.price, dbo.BillsalData.totalprice, dbo.BillsalData.bill_date, dbo.Sales_Bill.Stat, dbo.BillsalData.itm_name,   dbo.Employees.NameEmployee, dbo.BillsalData.Company_Branch_ID HAVING(dbo.BillsalData.bill_no <> N'جرد')"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_TreasuryMovement_Deposit"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_TreasuryMovement_Deposit"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_TreasuryMovement_Deposit As  Select dbo.TreasuryMovement_Deposit.id, dbo.TreasuryMovement_Deposit.TreasuryMovement_ID, dbo.Treasury.Treasury_Code, dbo.Treasury.Treasury_Name,   dbo.TreasuryMovement_Deposit.Treasury_Amount, dbo.TreasuryMovement_Deposit.ACCNumber, dbo.TreasuryMovement_Deposit.Treasury_Date, dbo.TreasuryMovement_Deposit.Treasury_Time,    dbo.TreasuryMovement_Deposit.Notes, dbo.Employees.EMPID, dbo.Employees.NameEmployee, dbo.TreasuryMovement_Deposit.Company_Branch_ID,  dbo.TreasuryMovement_Deposit.UserName From dbo.TreasuryMovement_Deposit LEFT OUTER Join  dbo.Employees ON dbo.TreasuryMovement_Deposit.EMPID = dbo.Employees.EMPID LEFT OUTER Join dbo.Treasury ON dbo.TreasuryMovement_Deposit.Treasury_Code = dbo.Treasury.Treasury_Code  Group By dbo.TreasuryMovement_Deposit.TreasuryMovement_ID, dbo.Treasury.Treasury_Code, dbo.Treasury.Treasury_Name, dbo.TreasuryMovement_Deposit.Treasury_Amount, dbo.TreasuryMovement_Deposit.ACCNumber, dbo.TreasuryMovement_Deposit.Treasury_Date, dbo.TreasuryMovement_Deposit.Treasury_Time, dbo.TreasuryMovement_Deposit.Notes,  dbo.Employees.EMPID, dbo.Employees.NameEmployee, dbo.TreasuryMovement_Deposit.Company_Branch_ID, dbo.TreasuryMovement_Deposit.UserName, dbo.TreasuryMovement_Deposit.id"
            cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_TreasuryMovement_Withdraw"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_TreasuryMovement_Withdraw"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_TreasuryMovement_Withdraw As  Select dbo.TreasuryMovement_Withdraw.id, dbo.TreasuryMovement_Withdraw.TreasuryMovement_ID, dbo.Treasury.Treasury_Code, dbo.Treasury.Treasury_Name,  dbo.TreasuryMovement_Withdraw.Treasury_Amount, dbo.TreasuryMovement_Withdraw.ACCNumber, dbo.TreasuryMovement_Withdraw.Treasury_Date, dbo.TreasuryMovement_Withdraw.Treasury_Time, dbo.TreasuryMovement_Withdraw.Notes, dbo.Employees.EMPID, dbo.Employees.NameEmployee,  dbo.TreasuryMovement_Withdraw.Company_Branch_ID  From dbo.TreasuryMovement_Withdraw LEFT OUTER Join   dbo.Employees ON dbo.TreasuryMovement_Withdraw.EMPID = dbo.Employees.EMPID LEFT OUTER Join  dbo.Treasury ON dbo.TreasuryMovement_Withdraw.Treasury_Code = dbo.Treasury.Treasury_Code Group By dbo.TreasuryMovement_Withdraw.TreasuryMovement_ID, dbo.Treasury.Treasury_Code, dbo.Treasury.Treasury_Name, dbo.TreasuryMovement_Withdraw.ACCNumber,  dbo.TreasuryMovement_Withdraw.Treasury_Date, dbo.TreasuryMovement_Withdraw.Treasury_Time, dbo.TreasuryMovement_Withdraw.Notes, dbo.Employees.EMPID,  dbo.Employees.NameEmployee, dbo.TreasuryMovement_Withdraw.Company_Branch_ID, dbo.TreasuryMovement_Withdraw.id, dbo.TreasuryMovement_Withdraw.Treasury_Amount"
            cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_purchase_bill"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_purchase_bill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_purchase_bill As  Select dbo.purchase_bill.Vendorname, dbo.purchase_bill.Stat, dbo.BilltINData.bill_no, dbo.BilltINData.itm_name, dbo.BilltINData.price, dbo.BilltINData.qu, dbo.BilltINData.qu_unity, dbo.BilltINData.itm_Unity, dbo.BilltINData.totalprice,   dbo.BilltINData.bill_date, dbo.BilltINData.totalprice As vnamntcredit, dbo.BilltINData.UserName, dbo.BilltINData.Stores, dbo.BilltINData.itm_id, dbo.BilltINData.itm_cat, dbo.BilltINData.group_branch, dbo.BilltINData.CurrentStock  From dbo.purchase_bill RIGHT OUTER Join  dbo.BilltINData ON dbo.purchase_bill.bill_No = dbo.BilltINData.bill_no  Group By dbo.purchase_bill.Vendorname, dbo.purchase_bill.Stat, dbo.BilltINData.bill_no, dbo.BilltINData.itm_name, dbo.BilltINData.price, dbo.BilltINData.qu, dbo.BilltINData.qu_unity, dbo.BilltINData.itm_Unity, dbo.BilltINData.totalprice,   dbo.BilltINData.bill_date, dbo.BilltINData.UserName, dbo.BilltINData.Stores, dbo.BilltINData.itm_id, dbo.BilltINData.itm_cat, dbo.BilltINData.group_branch, dbo.BilltINData.CurrentStock"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_Capital_Reserve_CheckOut_Deposit"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_Capital_Reserve_CheckOut_Deposit"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_Capital_Reserve_CheckOut_Deposit As Select dbo.Capital_Reserve_CheckOut_Deposit.Capital_Number, dbo.Capital_Owner.Owner_Name, dbo.Treasury.Treasury_Name, dbo.Capital_Type.Capital_Type_Name, dbo.Capital_Reserve_CheckOut_Deposit.Amount,   dbo.Capital_Reserve_CheckOut_Deposit.Capital_Date, dbo.Capital_Reserve_CheckOut_Deposit.Capital_Time, dbo.Capital_Reserve_CheckOut_Deposit.Notes, dbo.Capital_Reserve_CheckOut_Deposit.UserName,  dbo.Capital_Reserve_CheckOut_Deposit.Owner_Code, dbo.Capital_Reserve_CheckOut_Deposit.Capital_Type_Code, dbo.Capital_Reserve_CheckOut_Deposit.Treasury_Code,  dbo.Capital_Reserve_CheckOut_Deposit.Company_Branch_ID  From dbo.Capital_Reserve_CheckOut_Deposit INNER Join  dbo.Capital_Owner ON dbo.Capital_Reserve_CheckOut_Deposit.Owner_Code = dbo.Capital_Owner.Owner_Code INNER Join   dbo.Capital_Type ON dbo.Capital_Reserve_CheckOut_Deposit.Capital_Type_Code = dbo.Capital_Type.Capital_Type_Code INNER Join   dbo.Treasury ON dbo.Capital_Reserve_CheckOut_Deposit.Treasury_Code = dbo.Treasury.Treasury_Code  Where (dbo.Capital_Reserve_CheckOut_Deposit.Capital_Number <> N'')"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_ItemsUnity"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_ItemsUnity"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_ItemsUnity As Select dbo.Items.itm_id, dbo.Items.group_name, dbo.Items.sname, dbo.Items.store, dbo.ItemsUnity.Unity_Name, dbo.ItemsUnity.NumberPieces, dbo.ItemsUnity.UnitySize_ID, dbo.Items.Stores  From dbo.ItemsUnity RIGHT OUTER Join  dbo.Items ON dbo.ItemsUnity.itm_id = dbo.Items.itm_id  Group By dbo.Items.itm_id, dbo.Items.group_name, dbo.Items.sname, dbo.Items.store, dbo.ItemsUnity.Unity_Name, dbo.ItemsUnity.NumberPieces, dbo.ItemsUnity.UnitySize_ID, dbo.Items.Stores"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_Maintenance_ReceivingCar"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_Maintenance_ReceivingCar"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_Maintenance_ReceivingCar As Select dbo.Maintenance_ReceivingCarAdd.ReceivingCarAddID, dbo.Maintenance_ReceivingCarAdd.OrderID, dbo.Maintenance_ReceivingCar.ReceivingCar_ID, dbo.Maintenance_ReceivingCar.ReceivingCar,  dbo.Maintenance_ReceivingCarAdd.UserName, dbo.Maintenance_ReceivingCarAdd.Company_Branch_ID From dbo.Maintenance_ReceivingCarAdd LEFT OUTER Join  dbo.Maintenance_ReceivingCar ON dbo.Maintenance_ReceivingCarAdd.ReceivingCar_ID = dbo.Maintenance_ReceivingCar.ReceivingCar_ID"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_PriceOffer"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_PriceOffer"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_PriceOffer As  Select dbo.PriceOffer.id, dbo.PriceOffer.bill_no, dbo.Customers.Cust_Code, dbo.Customers.Vendorname, dbo.PriceOffer.totalprice, dbo.PriceOffer.bill_date, dbo.PriceOffer.UserName, dbo.PriceOffer.Company_Branch_ID  From dbo.PriceOffer LEFT OUTER Join  dbo.Customers ON dbo.PriceOffer.Cust_Code = dbo.Customers.Cust_Code  Group By dbo.PriceOffer.id, dbo.PriceOffer.bill_no, dbo.Customers.Cust_Code, dbo.Customers.Vendorname, dbo.PriceOffer.totalprice, dbo.PriceOffer.bill_date, dbo.PriceOffer.UserName, dbo.PriceOffer.Company_Branch_ID"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_PriceOfferBill"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_PriceOfferBill"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_PriceOfferBill As  Select dbo.PriceOfferBill.id, dbo.PriceOfferBill.bill_no, dbo.Customers.Cust_Code, dbo.Customers.Vendorname, dbo.PriceOfferBill.itm_id, dbo.PriceOfferBill.itm_cat, dbo.PriceOfferBill.itm_name, dbo.PriceOfferBill.Unity, dbo.PriceOfferBill.price,   dbo.PriceOfferBill.qu, dbo.PriceOfferBill.totalprice, dbo.PriceOfferBill.bill_date, dbo.PriceOfferBill.UserName, dbo.PriceOfferBill.Company_Branch_ID  From dbo.PriceOfferBill LEFT OUTER Join  dbo.Customers ON dbo.PriceOfferBill.Cust_Code = dbo.Customers.Cust_Code Group By dbo.PriceOfferBill.id, dbo.PriceOfferBill.bill_no, dbo.Customers.Cust_Code, dbo.Customers.Vendorname, dbo.PriceOfferBill.itm_id, dbo.PriceOfferBill.itm_cat, dbo.PriceOfferBill.itm_name, dbo.PriceOfferBill.Unity, dbo.PriceOfferBill.price,   dbo.PriceOfferBill.qu, dbo.PriceOfferBill.totalprice, dbo.PriceOfferBill.bill_date, dbo.PriceOfferBill.UserName, dbo.PriceOfferBill.Company_Branch_ID"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_Maintenance_Car_Data"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_Maintenance_Car_Data"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_Maintenance_Car_Data As Select dbo.Maintenance_Car_Data.Car_Data_ID, dbo.Maintenance_Car_Data.Car_Number, dbo.Maintenance_Paintings_Type.Paintings_Type, dbo.Maintenance_Car_Data.Paintings_No, dbo.Maintenance_Car_Data.Paintings_Letters,  dbo.Customers.Vendorname, dbo.Maintenance_Drivers.Driv_Name, dbo.MaintenanceTypeProduct.TypeProduct_Name, dbo.MaintenanceDeviceBrand.DeviceBrand_Name, dbo.MaintenanceDeviceModel.DeviceModel_Name, dbo.Maintenance_Manu_Year.Manu_Year, dbo.Maintenance_Car_Data.CC, dbo.Maintenance_Car_Data.Cylinders_No, dbo.Maintenance_Car_Data.Valves_No, dbo.Maintenance_Car_Data.Chassis_No,  dbo.Maintenance_Car_Data.Description, dbo.Company_Branch.Company_Branch_Name, dbo.Maintenance_Car_Data.Cust_Code, dbo.Maintenance_Car_Data.Driv_ID, dbo.Maintenance_Car_Data.TypeProduct_ID, dbo.Maintenance_Car_Data.DeviceBrand_ID, dbo.Maintenance_Car_Data.DeviceModel_ID, dbo.Maintenance_Car_Data.Manu_Year_ID, dbo.Maintenance_Car_Data.Company_Branch_ID, dbo.Customers.tel1, dbo.Maintenance_Car_Data.KiloMeter  From dbo.Maintenance_Car_Data LEFT OUTER Join  dbo.Company_Branch ON dbo.Maintenance_Car_Data.Company_Branch_ID = dbo.Company_Branch.Company_Branch_ID LEFT OUTER Join dbo.MaintenanceTypeProduct ON dbo.Maintenance_Car_Data.TypeProduct_ID = dbo.MaintenanceTypeProduct.TypeProduct_ID LEFT OUTER Join  dbo.Maintenance_Paintings_Type ON dbo.Maintenance_Car_Data.Paintings_Type_ID = dbo.Maintenance_Paintings_Type.Paintings_Type_ID LEFT OUTER Join  dbo.MaintenanceDeviceBrand ON dbo.Maintenance_Car_Data.DeviceBrand_ID = dbo.MaintenanceDeviceBrand.DeviceBrand_ID LEFT OUTER Join  dbo.Maintenance_Manu_Year ON dbo.Maintenance_Car_Data.Manu_Year_ID = dbo.Maintenance_Manu_Year.Manu_Year_ID LEFT OUTER Join  dbo.MaintenanceDeviceModel ON dbo.Maintenance_Car_Data.DeviceModel_ID = dbo.MaintenanceDeviceModel.DeviceModel_ID LEFT OUTER Join  dbo.Maintenance_Drivers ON dbo.Maintenance_Car_Data.Driv_ID = dbo.Maintenance_Drivers.Driv_ID LEFT OUTER Join dbo.Customers ON dbo.Maintenance_Car_Data.Cust_Code = dbo.Customers.Cust_Code"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_Vst_disc"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_Vst_disc"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_Vst_disc As Select dbo.Vst_disc.TIN_NO, dbo.Vst_disc.Vendorname, dbo.Vst_disc_State.Vst_disc As State, dbo.Vst_disc.amnt, dbo.Vst_disc.pdate, dbo.Vst_disc.amnt As vnamntdebit, dbo.Vst_disc.Company_Branch_ID From dbo.Vst_disc CROSS Join dbo.Vst_disc_State"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_Vst"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_Vst"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_Vst As Select dbo.Vst.BillNo, dbo.Vst.Vendorname, dbo.Vst_State.Vst_State, dbo.Vst.VND_amx, dbo.Vst.VND_dt, dbo.Vst.VND_amx As vnamntcredit, dbo.Vst.Company_Branch_ID From dbo.Vst CROSS Join dbo.Vst_State"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_Vnd_disc"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_Vnd_disc"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_Vnd_disc As  Select dbo.vndr_disc.Vendorname, dbo.vndr_disc.amnt, dbo.vndr_disc.pdate, dbo.vndr_disc.TIN_NO, dbo.vndr_disc_State.vndr_disc_State As State, dbo.vndr_disc.amnt As vnamntdebit, dbo.vndr_disc.Company_Branch_ID From dbo.vndr_disc CROSS Join dbo.vndr_disc_State"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_Vnd"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_Vnd"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_Vnd As  Select dbo.Vnd.Vendorname, dbo.Vnd.VND_dt, dbo.Vnd.VND_amx, dbo.Vnd.BillNo, dbo.Vnd_State.Vnd_State, dbo.Vnd.VND_amx As vnamntcredit, dbo.Vnd.Company_Branch_ID From dbo.Vnd CROSS Join dbo.Vnd_State"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_Expenses"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_Expenses"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_Expenses As Select dbo.Expenses.id, dbo.Expenses.Exp_Name, dbo.Expenses.Exp_Value, dbo.Expenses.Exp_Date, dbo.Expenses.Cats, dbo.ExpensesState.Expenses, dbo.Expenses.Exp_Value As vnamntcredit, dbo.Expenses.Company_Branch_ID,  dbo.Expenses.OrderID, dbo.Expenses.AfterValueVAT, dbo.Expenses.RateVAT, dbo.Expenses.ValueVAT, dbo.Expenses.Treasury_Code From dbo.Expenses CROSS Join dbo.ExpensesState"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_Items"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_Items"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_Items As Select sname, SUM(store) As Quan, Stores, group_name, TinPrice, SalPrice, Company_Branch_ID From dbo.Items Group By sname, Stores, group_name, TinPrice, SalPrice, Company_Branch_ID"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_Capital_Partner_Withdrawals"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_Capital_Partner_Withdrawals"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_Capital_Partner_Withdrawals As   Select dbo.Capital_Partner_Withdrawals.Capital_Number, dbo.Capital_Owner.Owner_Name, dbo.Treasury.Treasury_Name, dbo.Capital_Type.Capital_Type_Name, dbo.Capital_Partner_Withdrawals.Amount,    dbo.Capital_Partner_Withdrawals.Capital_Date, dbo.Capital_Partner_Withdrawals.Capital_Time, dbo.Capital_Partner_Withdrawals.Notes, dbo.Capital_Partner_Withdrawals.UserName, dbo.Capital_Partner_Withdrawals.Owner_Code,   dbo.Capital_Partner_Withdrawals.Capital_Type_Code, dbo.Capital_Partner_Withdrawals.Treasury_Code, dbo.Capital_Partner_Withdrawals.Company_Branch_ID  From dbo.Capital_Partner_Withdrawals INNER Join dbo.Capital_Owner ON dbo.Capital_Partner_Withdrawals.Owner_Code = dbo.Capital_Owner.Owner_Code INNER Join  dbo.Capital_Type ON dbo.Capital_Partner_Withdrawals.Capital_Type_Code = dbo.Capital_Type.Capital_Type_Code INNER Join   dbo.Treasury ON dbo.Capital_Partner_Withdrawals.Treasury_Code = dbo.Treasury.Treasury_Code  Where (dbo.Capital_Partner_Withdrawals.Capital_Number <> N'')"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_ItemsUnitySize"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_ItemsUnitySize"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_ItemsUnitySize As  Select dbo.ItemsUnity.Unity_ID, dbo.ItemsUnity.itm_id, dbo.ItemsUnity.Unity_Name, dbo.ItemsUnity.TinPriceUnit, dbo.ItemsUnity.SalPriceUnit, dbo.ItemsUnity.DefaultTin, dbo.ItemsUnity.DefaultSale, dbo.ItemsUnitySize.UnitySize_ID,  dbo.ItemsUnitySize.UnitySize_Name, dbo.ItemsUnity.NumberPieces, dbo.ItemsUnity.itm_id_Unity From dbo.ItemsUnity LEFT OUTER Join dbo.ItemsUnitySize ON dbo.ItemsUnity.UnitySize_ID = dbo.ItemsUnitySize.UnitySize_ID"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_Salary"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_Salary"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_Salary As Select dbo.Salary.SLYID, dbo.Salary.NameEmployee, dbo.Salary.Month, dbo.EmployeesSalarySystem.SalarySystem_ID, dbo.EmployeesSalarySystem.SalarySystemName, dbo.Salary.SalaryDate, dbo.Salary.BasicSalary,   dbo.Salary.ValueAdditional, dbo.Salary.TotalValueAdditional, dbo.Salary.ValuePremium, dbo.Salary.TotalValuePremium, dbo.Salary.ValueIncentive, dbo.Salary.TotalValueIncentive, dbo.Salary.Reward, dbo.Salary.RatherNatureWork, dbo.Salary.TransferAllowance, dbo.Salary.HousingAllowance, dbo.Salary.OtherAddition, dbo.Salary.ChnagedSalary, dbo.Salary.Insurances, dbo.Salary.WorkTax, dbo.Salary.Advance, dbo.Salary.DiscountsDelay, dbo.Salary.DiscountsAbsence, dbo.Salary.DiscountsSanctions, dbo.Salary.OtherDiscount, dbo.Salary.NetSalary, dbo.Salary.UserName, dbo.Salary.Company_Branch_ID From dbo.Salary LEFT OUTER Join  dbo.EmployeesSalarySystem ON dbo.Salary.SalarySystem_ID = dbo.EmployeesSalarySystem.SalarySystem_ID Group By dbo.Salary.SLYID, dbo.Salary.Month, dbo.EmployeesSalarySystem.SalarySystem_ID, dbo.EmployeesSalarySystem.SalarySystemName, dbo.Salary.SalaryDate, dbo.Salary.BasicSalary, dbo.Salary.ValueAdditional, dbo.Salary.TotalValueAdditional, dbo.Salary.ValuePremium, dbo.Salary.TotalValuePremium, dbo.Salary.ValueIncentive, dbo.Salary.TotalValueIncentive, dbo.Salary.Reward, dbo.Salary.RatherNatureWork, dbo.Salary.TransferAllowance, dbo.Salary.HousingAllowance, dbo.Salary.OtherAddition, dbo.Salary.ChnagedSalary, dbo.Salary.Insurances, dbo.Salary.WorkTax, dbo.Salary.Advance, dbo.Salary.DiscountsDelay, dbo.Salary.DiscountsAbsence, dbo.Salary.DiscountsSanctions, dbo.Salary.OtherDiscount, dbo.Salary.NetSalary, dbo.Salary.UserName, dbo.Salary.NameEmployee, dbo.Salary.Company_Branch_ID"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_ManufacturingDismissalNotice"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_ManufacturingDismissalNotice"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_ManufacturingDismissalNotice As Select dbo.Items.group_name, dbo.Items.sname, dbo.Employees.NameEmployee, dbo.Employees.EMPID, dbo.ManufacturingDismissalNotice.id, dbo.ManufacturingDismissalNotice.bill_no, dbo.ManufacturingDismissalNotice.bill_date, dbo.ManufacturingDismissalNotice.itm_id_Manufacturing, dbo.ManufacturingDismissalNotice.Stores_From, dbo.ManufacturingDismissalNotice.Stores_TO,  dbo.ManufacturingDismissalNotice.qunt_Manufacturing, dbo.ManufacturingDismissalNotice.TotalCostPrice, dbo.ManufacturingDismissalNotice.UserName From dbo.Items RIGHT OUTER Join  dbo.ManufacturingDismissalNotice On dbo.Items.itm_id = dbo.ManufacturingDismissalNotice.itm_id_Manufacturing LEFT OUTER Join  dbo.Employees On dbo.ManufacturingDismissalNotice.EMPID = dbo.Employees.EMPID Group By dbo.Items.sname, dbo.Employees.NameEmployee, dbo.Employees.EMPID, dbo.Items.group_name, dbo.ManufacturingDismissalNotice.id, dbo.ManufacturingDismissalNotice.bill_no,  dbo.ManufacturingDismissalNotice.bill_date, dbo.ManufacturingDismissalNotice.itm_id_Manufacturing, dbo.ManufacturingDismissalNotice.Stores_From, dbo.ManufacturingDismissalNotice.Stores_TO,dbo.ManufacturingDismissalNotice.qunt_Manufacturing, dbo.ManufacturingDismissalNotice.TotalCostPrice, dbo.ManufacturingDismissalNotice.UserName"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_ManufacturingDismissalNoticeData"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_ManufacturingDismissalNoticeData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_ManufacturingDismissalNoticeData As Select dbo.Manufacturing_BillsalData.id, dbo.Manufacturing_BillsalData.bill_no, dbo.Items.itm_id, dbo.Items.group_name, dbo.Items.sname, dbo.Manufacturing_BillsalData.itm_Unity, dbo.Items.TinPrice,   dbo.Manufacturing_BillsalData.qu, dbo.Manufacturing_BillsalData.qu_unity, dbo.Manufacturing_BillsalData.totalprice, dbo.Manufacturing_BillsalData.Stores, dbo.Manufacturing_BillsalData.bill_date,  dbo.Manufacturing_BillsalData.UserName, dbo.ManufacturingDismissalNotice.Stores_From, dbo.ManufacturingDismissalNotice.Stores_TO, dbo.Manufacturing_BillsalData.bill_noNotice  From dbo.Manufacturing_BillsalData LEFT OUTER Join   dbo.ManufacturingDismissalNotice On dbo.Manufacturing_BillsalData.bill_noNotice = dbo.ManufacturingDismissalNotice.bill_no LEFT OUTER Join   dbo.Items On dbo.Manufacturing_BillsalData.itm_id = dbo.Items.itm_id Group By dbo.Manufacturing_BillsalData.bill_no, dbo.Items.itm_id, dbo.Items.group_name, dbo.Items.sname, dbo.Manufacturing_BillsalData.qu, dbo.Manufacturing_BillsalData.qu_unity, dbo.Manufacturing_BillsalData.totalprice,  dbo.Manufacturing_BillsalData.Stores, dbo.Manufacturing_BillsalData.bill_date, dbo.Manufacturing_BillsalData.UserName, dbo.Items.TinPrice, dbo.Manufacturing_BillsalData.id, dbo.Manufacturing_BillsalData.itm_Unity,  dbo.ManufacturingDismissalNotice.Stores_From, dbo.ManufacturingDismissalNotice.Stores_TO, dbo.Manufacturing_BillsalData.bill_noNotice HAVING(dbo.Manufacturing_BillsalData.bill_noNotice <> N'')"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_ManufacturingFillOrderData"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from View_ManufacturingFillOrderData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_ManufacturingFillOrderData AS Select dbo.Manufacturing_BillsalData.id, dbo.Manufacturing_BillsalData.bill_no, dbo.Items.itm_id, dbo.Items.group_name, dbo.Items.sname, dbo.Employees.NameEmployee, dbo.Employees.EMPID,  dbo.Items.TinPrice, dbo.Manufacturing_BillsalData.itm_Unity, dbo.Manufacturing_BillsalData.qu, dbo.Manufacturing_BillsalData.qu_unity, dbo.Manufacturing_BillsalData.price, dbo.Manufacturing_BillsalData.totalprice, dbo.Manufacturing_BillsalData.Stores, dbo.ManufacturingFillOrder.Stores_From, dbo.ManufacturingFillOrder.Stores_TO,  dbo.Manufacturing_BillsalData.bill_noNotice From dbo.ManufacturingFillOrder RIGHT OUTER Join   dbo.Manufacturing_BillsalData ON dbo.ManufacturingFillOrder.bill_no = dbo.Manufacturing_BillsalData.bill_no LEFT OUTER Join   dbo.Employees ON dbo.Manufacturing_BillsalData.EMPID = dbo.Employees.EMPID LEFT OUTER Join  dbo.Items ON dbo.Manufacturing_BillsalData.itm_id = dbo.Items.itm_id Group By dbo.Items.sname, dbo.Employees.NameEmployee, dbo.Employees.EMPID, dbo.Items.group_name, dbo.Items.itm_id, dbo.Items.TinPrice, dbo.Manufacturing_BillsalData.itm_Unity,  dbo.Manufacturing_BillsalData.qu, dbo.Manufacturing_BillsalData.qu_unity, dbo.Manufacturing_BillsalData.price, dbo.Manufacturing_BillsalData.totalprice, dbo.Manufacturing_BillsalData.Stores, dbo.Manufacturing_BillsalData.bill_no, dbo.Manufacturing_BillsalData.id, dbo.ManufacturingFillOrder.Stores_From, dbo.ManufacturingFillOrder.Stores_TO, dbo.Manufacturing_BillsalData.bill_noNotice HAVING(dbo.Manufacturing_BillsalData.bill_no <> N'')"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_ManufacturingFillOrder"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from View_ManufacturingFillOrder"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_ManufacturingFillOrder AS Select dbo.ManufacturingFillOrder.Id, dbo.ManufacturingFillOrder.bill_no, dbo.ManufacturingFillOrder.bill_date, dbo.ManufacturingFillOrder.itm_id_Manufacturing, dbo.Items.group_name, dbo.Items.sname,  dbo.ManufacturingFillOrder.Stores_Manufacturing, dbo.ManufacturingFillOrder.Stores_From, dbo.ManufacturingFillOrder.Stores_TO, dbo.ManufacturingFillOrder.Total_qu, dbo.ManufacturingFillOrder.TotalCostPrice, dbo.Employees.NameEmployee, dbo.ManufacturingFillOrder.UserName, dbo.Employees.EMPID From dbo.ManufacturingFillOrder LEFT OUTER Join   dbo.Items ON dbo.ManufacturingFillOrder.itm_id_Manufacturing = dbo.Items.itm_id LEFT OUTER Join  dbo.Employees ON dbo.ManufacturingFillOrder.EMPID = dbo.Employees.EMPID Group By dbo.ManufacturingFillOrder.Id, dbo.ManufacturingFillOrder.bill_no, dbo.ManufacturingFillOrder.bill_date, dbo.Items.sname, dbo.ManufacturingFillOrder.Stores_Manufacturing, dbo.ManufacturingFillOrder.Stores_From, dbo.ManufacturingFillOrder.Stores_TO, dbo.ManufacturingFillOrder.Total_qu, dbo.Employees.NameEmployee, dbo.ManufacturingFillOrder.UserName, dbo.ManufacturingFillOrder.TotalCostPrice, dbo.Employees.EMPID, dbo.Items.group_name, dbo.ManufacturingFillOrder.itm_id_Manufacturing"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_SalesPricesCustomer"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * From View_SalesPricesCustomer"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()

            cmd.CommandText = "CREATE VIEW View_SalesPricesCustomer As Select TOP(100) PERCENT dbo.BillsalData.id, dbo.BillsalData.bill_no, dbo.Sales_Bill.bill_date, dbo.Sales_Bill.Vendorname, dbo.BillsalData.itm_id, dbo.BillsalData.itm_cat, dbo.BillsalData.itm_name, dbo.BillsalData.price,  dbo.Sales_Bill.Company_Branch_ID From dbo.Sales_Bill INNER Join dbo.BillsalData ON dbo.Sales_Bill.bill_No = dbo.BillsalData.bill_no Group By dbo.BillsalData.price, dbo.BillsalData.bill_no, dbo.BillsalData.itm_name, dbo.BillsalData.id, dbo.BillsalData.itm_cat, dbo.BillsalData.itm_id, dbo.Sales_Bill.Vendorname, dbo.Sales_Bill.bill_date, dbo.Sales_Bill.Company_Branch_ID Order By dbo.BillsalData.bill_no DESC"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_More_Customer_Sales"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_More_Customer_Sales"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_More_Customer_Sales As Select TOP(100) PERCENT Vendorname, vintinval As Exvintinval From dbo.Customers Order By Exvintinval DESC"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_More_Customer_Sales_Date"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_More_Customer_Sales_Date As Select TOP (100) PERCENT dbo.Sales_Bill.Vendorname, SUM(dbo.BillsalData.totalprice) As Extotalprice, dbo.BillsalData.bill_date From dbo.BillsalData INNER Join dbo.Sales_Bill On dbo.BillsalData.bill_no = dbo.Sales_Bill.bill_No Group By dbo.Sales_Bill.Vendorname, dbo.BillsalData.bill_date Order By Extotalprice DESC"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_Product_Manufacturing"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_Product_Manufacturing"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            Try
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "CREATE VIEW View_Product_Manufacturing As Select dbo.ManufacturingProduct.Manufacturing_ID, dbo.ManufacturingProduct.itm_id_Manufacturing, dbo.ManufacturingProduct.Stores_Manufacturing, dbo.ManufacturingProductAdd.itm_id, dbo.ManufacturingProductAdd.Stores,  dbo.ManufacturingProductAdd.itm_Unity, dbo.ManufacturingProductAdd.qu, dbo.ManufacturingProductAdd.qu_unity, dbo.ManufacturingProductAdd.totalprice, dbo.ManufacturingProductAdd.UserName, dbo.Items.group_name, dbo.Items.sname, dbo.Items.TinPrice, Items_1.sname As SnameManufacturing, dbo.ManufacturingProduct.Manufacturing_Allowance, dbo.ManufacturingProduct.Filling_Allowance, Items_1.itm_id As CostPrice,  Items_1.group_name AS QU_Manufacturing, Items_1.group_branch As QU_Rest, dbo.ManufacturingProduct.UserName AS store From dbo.ManufacturingProductAdd RIGHT OUTER Join  dbo.ManufacturingProduct LEFT OUTER Join  dbo.Items AS Items_1 ON dbo.ManufacturingProduct.itm_id_Manufacturing = Items_1.itm_id ON dbo.ManufacturingProductAdd.Manufacturing_ID = dbo.ManufacturingProduct.Manufacturing_ID LEFT OUTER Join  dbo.Items ON dbo.ManufacturingProductAdd.itm_id = dbo.Items.itm_id Group By dbo.ManufacturingProduct.Manufacturing_ID, dbo.ManufacturingProduct.itm_id_Manufacturing, dbo.ManufacturingProduct.Stores_Manufacturing, dbo.ManufacturingProductAdd.itm_id, dbo.ManufacturingProductAdd.Stores,  dbo.ManufacturingProductAdd.itm_Unity, dbo.ManufacturingProductAdd.qu, dbo.ManufacturingProductAdd.qu_unity, dbo.ManufacturingProductAdd.totalprice, dbo.ManufacturingProductAdd.UserName, dbo.Items.group_name, dbo.Items.sname, dbo.Items.TinPrice, Items_1.sname, dbo.ManufacturingProduct.Manufacturing_Allowance, dbo.ManufacturingProduct.Filling_Allowance, Items_1.itm_id, Items_1.group_name, Items_1.group_branch, dbo.ManufacturingProduct.UserName"
                cmd.ExecuteNonQuery()
            Catch ex1 As Exception
            End Try
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_Product_Manufacturing_Show"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_Product_Manufacturing_Show"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_Product_Manufacturing_Show As Select dbo.ManufacturingProduct.Manufacturing_ID, dbo.ManufacturingProduct.itm_id_Manufacturing, dbo.Items.group_name, dbo.Items.sname, dbo.Items.TinPrice, dbo.ManufacturingProduct.Stores_Manufacturing, dbo.ManufacturingProduct.CostPrice, dbo.ManufacturingProduct.Weightqunt, dbo.ManufacturingProduct.NumberMaterial, dbo.ManufacturingProduct.Manufacturing_Allowance, dbo.ManufacturingProduct.Filling_Allowance, dbo.ManufacturingProduct.UserName From dbo.Items RIGHT OUTER Join  dbo.ManufacturingProduct ON dbo.Items.itm_id = dbo.ManufacturingProduct.itm_id_Manufacturing Group By dbo.ManufacturingProduct.Manufacturing_ID, dbo.ManufacturingProduct.itm_id_Manufacturing, dbo.Items.group_name, dbo.Items.sname, dbo.Items.TinPrice, dbo.ManufacturingProduct.Stores_Manufacturing, dbo.ManufacturingProduct.CostPrice, dbo.ManufacturingProduct.Weightqunt, dbo.ManufacturingProduct.NumberMaterial, dbo.ManufacturingProduct.Manufacturing_Allowance, dbo.ManufacturingProduct.Filling_Allowance, dbo.ManufacturingProduct.UserName"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_Product_ManufacturingMaterials"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_Product_ManufacturingMaterials"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_Product_ManufacturingMaterials As Select dbo.Manufacturing_BillsalData.id, dbo.Manufacturing_BillsalData.bill_no, dbo.Manufacturing_BillsalData.itm_id, dbo.Items.group_name, dbo.Items.sname, dbo.Manufacturing_BillsalData.price, dbo.Manufacturing_BillsalData.qu, dbo.Manufacturing_BillsalData.totalprice, dbo.Manufacturing_BillsalData.Stores, dbo.Manufacturing_BillsalData.bill_date, dbo.Manufacturing_BillsalData.UserName From dbo.Manufacturing_BillsalData LEFT OUTER Join  dbo.Items On dbo.Manufacturing_BillsalData.itm_id = dbo.Items.itm_id Group By dbo.Manufacturing_BillsalData.id, dbo.Manufacturing_BillsalData.bill_no, dbo.Manufacturing_BillsalData.itm_id, dbo.Items.group_name, dbo.Items.sname, dbo.Manufacturing_BillsalData.price,dbo.Manufacturing_BillsalData.qu, dbo.Manufacturing_BillsalData.totalprice, dbo.Manufacturing_BillsalData.Stores, dbo.Manufacturing_BillsalData.bill_date, dbo.Manufacturing_BillsalData.UserName"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_Delegate_All"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_Delegate_All"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_Delegate_All As  Select dbo.BillsalData.bill_no, dbo.Employees.NameEmployee, dbo.Sales_Bill.Vendorname, dbo.Geographic_Area.GeoArea_Name, dbo.BillsalData.itm_name, dbo.BillsalData.qu, dbo.BillsalData.price,   dbo.BillsalData.totalprice, dbo.BillsalData.bill_date, dbo.BillsalData.Profits, dbo.BillsalData.qu_unity  From dbo.Employees INNER Join  dbo.Branch INNER Join  dbo.Customers INNER Join  dbo.Geographic_Area ON dbo.Customers.GeoArea_Code = dbo.Geographic_Area.GeoArea_Code INNER Join  dbo.Sales_Bill INNER Join  dbo.BillsalData ON dbo.Sales_Bill.bill_No = dbo.BillsalData.bill_no ON dbo.Customers.Vendorname = dbo.Sales_Bill.Vendorname ON  dbo.Branch.Bran_code = dbo.Geographic_Area.Branch_Code On dbo.Employees.NameEmployee = dbo.BillsalData.EmpName And dbo.Employees.Bran_code = dbo.Branch.Bran_code WHERE(dbo.Employees.NameEmployee <> N'')"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_daySales"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from View_daySales"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_daySales As  Select TOP(100) PERCENT dbo.BillsalData.bill_no, dbo.BillsalData.Sheft_Number, dbo.BillsalData.itm_id, dbo.BillsalData.itm_cat, dbo.BillsalData.itm_name, dbo.BillsalData.price As SalPrice, dbo.BillsalData.qu,   dbo.BillsalData.totalprice, dbo.BillsalData.UserName, dbo.BillsalData.bill_date, dbo.BillsalData.Stores, dbo.Sales_Bill.Stat, dbo.BillsalData.EmpName, dbo.Sales_Bill.Vendorname, dbo.BillsalData.itm_Unity, dbo.BillsalData.itm_Notes, dbo.BillsalData.Company_Branch_ID, dbo.BillsalData.qu_unity, dbo.BillsalData.RateVAT, dbo.BillsalData.BeforeVAT, dbo.BillsalData.ValueVAT From dbo.BillsalData INNER Join dbo.Sales_Bill ON dbo.BillsalData.bill_no = dbo.Sales_Bill.bill_No Order By dbo.BillsalData.bill_no DESC"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_BillsalData"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_BillsalData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_BillsalData As Select TOP(100) PERCENT dbo.BillsalData.bill_no, dbo.BillsalData.itm_id, dbo.BillsalData.itm_cat, dbo.BillsalData.itm_name, dbo.BillsalData.price, dbo.BillsalData.qu, dbo.BillsalData.TinPriceAverage As TinPrice,  dbo.BillsalData.totalprice, dbo.BillsalData.UserName, dbo.BillsalData.Stores, dbo.BillsalData.bill_date, dbo.Sales_Bill.Stat, dbo.Sales_Bill.Vendorname, dbo.BillsalData.itm_Unity, dbo.BillsalData.itm_Notes, dbo.BillsalData.Company_Branch_ID, dbo.BillsalData.qu_unity From dbo.BillsalData INNER Join dbo.Sales_Bill On dbo.BillsalData.bill_no = dbo.Sales_Bill.bill_No Order By dbo.BillsalData.bill_no DESC"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_BilltINData"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_BilltINData"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_BilltINData As  Select dbo.BilltINData.bill_no, dbo.BilltINData.itm_id, dbo.BilltINData.itm_name, dbo.BilltINData.itm_cat, dbo.BilltINData.price, SUM(dbo.BilltINData.qu) As ExQunt, SUM(dbo.BilltINData.totalprice) As Extotalprice,  dbo.BilltINData.Stores, dbo.BilltINData.UserName, dbo.BilltINData.Company_Branch_ID, dbo.purchase_bill.Vendorname, dbo.purchase_bill.Stat From dbo.BilltINData INNER Join dbo.purchase_bill On dbo.BilltINData.bill_no = dbo.purchase_bill.bill_No Group By dbo.BilltINData.itm_id, dbo.BilltINData.itm_name, dbo.BilltINData.price, dbo.BilltINData.Stores, dbo.BilltINData.UserName, dbo.BilltINData.bill_no, dbo.BilltINData.itm_cat, dbo.BilltINData.Company_Branch_ID, dbo.purchase_bill.Vendorname, dbo.purchase_bill.Stat"
            cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_AccountsTree"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_AccountsTree As Select  dbo.Accounts_Main.Main_Code, dbo.Accounts_Main.Main_Name, dbo.Accounts_Groub.Groub_Code, dbo.Accounts_Groub.Groub_Name, dbo.AccountsTree.ACCNumber, dbo.AccountsTree.ACCName, dbo.AccountsTree.ACCDebtor,   dbo.AccountsTree.ACCCreditor, dbo.AccountsTree.ACCNotes From dbo.Accounts_Groub INNER Join dbo.Accounts_Main On dbo.Accounts_Groub.Main_Code = dbo.Accounts_Main.Main_Code INNER Join dbo.AccountsTree On dbo.Accounts_Groub.Groub_Code = dbo.AccountsTree.ACCGroub_Code"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW FINALBALANCE"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from FINALBALANCE"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW FINALBALANCE As Select dbo.AccountsTree.ACCNumber, dbo.Accounts_Main.Main_Name, dbo.Accounts_Groub.Groub_Name, dbo.AccountsTree.ACCName, dbo.AccountsTree.ACCDebtor, dbo.AccountsTree.ACCCreditor, dbo.BALANCE.SUMDEBIT,  dbo.BALANCE.SUMCREDIT, dbo.BALANCE.SUMDEBIT As EXSUMDEBIT, dbo.BALANCE.SUMCREDIT As EXSUMCREDIT From dbo.Accounts_Groub INNER Join  dbo.AccountsTree INNER Join  dbo.BALANCE On dbo.AccountsTree.ACCName = dbo.BALANCE.MOVDNameAccount On dbo.Accounts_Groub.Groub_Code = dbo.AccountsTree.ACCGroub_Code INNER Join  dbo.Accounts_Main On dbo.AccountsTree.ACCMain_Code = dbo.Accounts_Main.Main_Code"
            cmd.ExecuteNonQuery()
        End Try


        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_MaintenanceOrderRunning"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_MaintenanceOrderRunning"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_MaintenanceOrderRunning As  Select dbo.MaintenanceOrderRunning.OrderDayID, dbo.MaintenanceOrderRunning.OrderID, dbo.Sales_Bill.bill_No, dbo.Customers.Vendorname, dbo.Maintenance_Drivers.Driv_Name, dbo.MaintenanceOrderRunning.CarNumber,  dbo.MaintenanceOrderRunning.KiloMeter, dbo.MaintenanceOrderRunning.OrderDate, dbo.MaintenanceOrderRunning.TimeAMPM, dbo.MaintenanceTypeProduct.TypeProduct_Name, dbo.MaintenanceStatus.MaintenanceStatus_Name, dbo.Maintenance_Supervisor.Supervisor, dbo.Maintenance_Recipient.Recipient, dbo.MaintenanceOrderRunning.Delivery_Date, dbo.MaintenanceStatus.MaintenanceStatus_ID, dbo.MaintenanceDeviceBrand.DeviceBrand_Name,  dbo.MaintenanceDeviceModel.DeviceModel_Name, dbo.Maintenance_Manu_Year.Manu_Year, dbo.Maintenance_Car_Data.CC, dbo.Maintenance_Car_Data.Cylinders_No, dbo.Maintenance_Car_Data.Valves_No,  dbo.Maintenance_Car_Data.Chassis_No, dbo.Maintenance_Car_Data.Description, dbo.MaintenanceOrderRunning.UserName, dbo.MaintenanceOrderRunning.Company_Branch_ID, dbo.MaintenanceOrderRunning.Visit_Date, dbo.MaintenanceOrderRunning.OrderTime  From dbo.Maintenance_Supervisor INNER Join dbo.MaintenanceOrderRunning INNER Join dbo.MaintenanceStatus ON dbo.MaintenanceOrderRunning.MaintenanceStatus_ID = dbo.MaintenanceStatus.MaintenanceStatus_ID ON      dbo.Maintenance_Supervisor.Supervisor_ID = dbo.MaintenanceOrderRunning.Supervisor_ID INNER JOIN   dbo.Maintenance_Recipient ON dbo.MaintenanceOrderRunning.Recipient_ID = dbo.Maintenance_Recipient.Recipient_ID LEFT OUTER JOIN   dbo.Maintenance_Manu_Year INNER JOIN    dbo.Maintenance_Car_Data ON dbo.Maintenance_Manu_Year.Manu_Year_ID = dbo.Maintenance_Car_Data.Manu_Year_ID LEFT OUTER JOIN  dbo.Maintenance_Drivers ON dbo.Maintenance_Car_Data.Driv_ID = dbo.Maintenance_Drivers.Driv_ID LEFT OUTER JOIN  dbo.MaintenanceDeviceModel ON dbo.Maintenance_Car_Data.DeviceModel_ID = dbo.MaintenanceDeviceModel.DeviceModel_ID LEFT OUTER JOIN dbo.MaintenanceDeviceBrand ON dbo.Maintenance_Car_Data.DeviceBrand_ID = dbo.MaintenanceDeviceBrand.DeviceBrand_ID LEFT OUTER JOIN   dbo.Customers ON dbo.Maintenance_Car_Data.Cust_Code = dbo.Customers.Cust_Code LEFT OUTER JOIN dbo.MaintenanceTypeProduct ON dbo.Maintenance_Car_Data.TypeProduct_ID = dbo.MaintenanceTypeProduct.TypeProduct_ID ON   dbo.MaintenanceOrderRunning.Car_Data_ID = dbo.Maintenance_Car_Data.Car_Data_ID LEFT OUTER JOIN dbo.Sales_Bill ON dbo.MaintenanceOrderRunning.OrderID = dbo.Sales_Bill.OrderID"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * From View_ItemsTransfer"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_ItemsTransfer As Select dbo.ItemsTransfer_BillsalData.id, dbo.ItemsTransfer_BillsalData.bill_no, dbo.ItemsTransfer_BillsalData.itm_id, dbo.Items.group_name, dbo.Items.sname, dbo.ItemsTransfer_BillsalData.itm_Unity, dbo.ItemsTransfer_BillsalData.qu, dbo.ItemsTransfer_BillsalData.qu_unity, dbo.ItemsTransfer_BillsalData.bill_date, dbo.ItemsTransfer_BillsalData.Stores As StoresFrom, dbo.ItemsTransfer_BilltINData.Stores As StoresTO,  dbo.Employees.NameEmployee, dbo.Items.Company_Branch_ID From dbo.ItemsTransfer_BillsalData INNER Join dbo.ItemsTransfer_BilltINData On dbo.ItemsTransfer_BillsalData.bill_no = dbo.ItemsTransfer_BilltINData.bill_no INNER Join dbo.Items On dbo.ItemsTransfer_BillsalData.itm_id = dbo.Items.itm_id LEFT OUTER Join dbo.Employees On dbo.ItemsTransfer_BillsalData.EMPID = dbo.Employees.EMPID Group By dbo.ItemsTransfer_BillsalData.itm_id, dbo.ItemsTransfer_BillsalData.itm_Unity, dbo.ItemsTransfer_BillsalData.qu, dbo.ItemsTransfer_BillsalData.Stores, dbo.ItemsTransfer_BillsalData.bill_date, dbo.ItemsTransfer_BilltINData.Stores, dbo.ItemsTransfer_BillsalData.bill_no, dbo.ItemsTransfer_BillsalData.qu_unity, dbo.Employees.NameEmployee, dbo.Items.group_name, dbo.Items.sname, dbo.ItemsTransfer_BillsalData.id, dbo.Items.Company_Branch_ID"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_IM_Bsal"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_IM_Bsal"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_IM_Bsal As  Select dbo.IM_Bsal.Vendorname, dbo.IM_Bsal_Data.bill_no, dbo.IM_Bsal_Data.price, dbo.IM_Bsal_Data.qu, dbo.IM_Bsal_Data.totalprice, dbo.IM_Bsal_Data.bill_date, dbo.IM_Bsal_Data.EmpName, dbo.IM_Bsal_Data.totalprice As vnamntdebit,    dbo.IM_Bsal_Data.itm_name, dbo.Items.TinPrice, dbo.IM_Bsal_Data.qu * dbo.Items.TinPrice As TotalPriceTin, dbo.IM_Bsal_Data.itm_id, dbo.IM_Bsal_Data.itm_cat, dbo.IM_Bsal_Data.Stores, dbo.IM_Bsal_Data.UserName, dbo.IM_Bsal.Stat, dbo.IM_Bsal_Data.Company_Branch_ID, dbo.IM_Bsal_Data.qu_unity, dbo.IM_Bsal_Data.itm_Unity, dbo.IM_Bsal_Data.CurrentStock  From dbo.IM_Bsal_Data LEFT OUTER Join  dbo.Items ON dbo.IM_Bsal_Data.itm_id = dbo.Items.itm_id RIGHT OUTER Join  dbo.IM_Bsal ON dbo.IM_Bsal_Data.bill_no = dbo.IM_Bsal.bill_No Group By dbo.IM_Bsal.Vendorname, dbo.IM_Bsal_Data.bill_no, dbo.IM_Bsal_Data.price, dbo.IM_Bsal_Data.qu, dbo.IM_Bsal_Data.totalprice, dbo.IM_Bsal_Data.bill_date, dbo.IM_Bsal_Data.itm_name, dbo.Items.TinPrice, dbo.IM_Bsal_Data.qu * dbo.Items.TinPrice, dbo.IM_Bsal_Data.itm_id, dbo.IM_Bsal_Data.itm_cat, dbo.IM_Bsal_Data.Stores, dbo.IM_Bsal_Data.UserName, dbo.IM_Bsal.Stat, dbo.IM_Bsal_Data.Company_Branch_ID,  dbo.IM_Bsal_Data.qu_unity, dbo.IM_Bsal_Data.itm_Unity, dbo.IM_Bsal_Data.EmpName, dbo.IM_Bsal_Data.CurrentStock"
            cmd.ExecuteNonQuery()
        End Try

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "DROP VIEW View_IM_BTin"
            cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_IM_BTin"
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "CREATE VIEW View_IM_BTin As Select dbo.IM_Btin.Vendorname, dbo.IM_Btin_Data.bill_no, dbo.IM_Btin_Data.itm_name, dbo.IM_Btin_Data.price, dbo.IM_Btin_Data.qu, dbo.IM_Btin_Data.qu_unity, dbo.IM_Btin_Data.itm_Unity, dbo.IM_Btin_Data.totalprice,  dbo.IM_Btin_Data.bill_date, dbo.IM_Btin_Data.totalprice As vnamntdebit, dbo.IM_Btin.Company_Branch_ID, dbo.IM_Btin_Data.UserName, dbo.IM_Btin_Data.Stores, dbo.IM_Btin.Stat, dbo.IM_Btin_Data.itm_id, dbo.IM_Btin_Data.itm_cat,   dbo.IM_Btin_Data.group_branch, dbo.IM_Btin_Data.CurrentStock From dbo.IM_Btin LEFT OUTER Join  dbo.IM_Btin_Data ON dbo.IM_Btin.bill_No = dbo.IM_Btin_Data.bill_no   Group By dbo.IM_Btin.Vendorname, dbo.IM_Btin_Data.bill_no, dbo.IM_Btin_Data.itm_name, dbo.IM_Btin_Data.price, dbo.IM_Btin_Data.qu, dbo.IM_Btin_Data.totalprice, dbo.IM_Btin_Data.bill_date, dbo.IM_Btin.Company_Branch_ID,   dbo.IM_Btin_Data.itm_Unity, dbo.IM_Btin_Data.qu_unity, dbo.IM_Btin_Data.UserName, dbo.IM_Btin_Data.Stores, dbo.IM_Btin.Stat, dbo.IM_Btin_Data.itm_id, dbo.IM_Btin_Data.itm_cat, dbo.IM_Btin_Data.group_branch,  dbo.IM_Btin_Data.CurrentStock"
            cmd.ExecuteNonQuery()
        End Try

    End Sub

    Public Sub ADD_EDIT_Tabels()
        connect()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Statement_link from Account_Statement_Link where Statement_link=N'الخزينة'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            MAXRECORDAuto("Account_Statement_Link", "Statement_ID")
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Account_Statement_Link(Statement_ID,Statement_link)  values("
            S = S & "N'" & MAXID & "',N'الخزينة')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Link_Statement from AccountsTreeLinking where Link_Statement=N'الخزينة'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            MAXRECORDAuto("AccountsTreeLinking", "Link_ID")
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into AccountsTreeLinking(Link_ID,Link_Statement,ACCNumber,Link_AccountsTree)  values("
            S = S & "N'" & MAXID & "',N'الخزينة',N'125',N'الخزينة')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select ACCID from AccountsTree where ACCName=N'مدفوعات عملاء'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Dim ACCID As String = dr(0).ToString

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "update AccountsTree set ACCName =N'مقبوضات عملاء' where ACCID =N'" & ACCID & "'"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Link_ID from AccountsTreeLinking where Link_Statement=N'مدفوعات عملاء'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Dim Link_ID As String = dr(0).ToString

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "update AccountsTreeLinking set Link_Statement =N'مقبوضات عملاء',Link_AccountsTree =N'مقبوضات عملاء' where Link_ID =N'" & Link_ID & "'"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Statement_ID from Account_Statement_Link where Statement_link=N'مدفوعات عملاء'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Dim Statement_ID As String = dr(0).ToString

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "update Account_Statement_Link set Statement_link =N'مقبوضات عملاء' where Statement_ID =N'" & Statement_ID & "'"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Statement_link from Account_Statement_Link where Statement_link=N'مسحوبات جارى الشركاء'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            MAXRECORDAuto("Account_Statement_Link", "Statement_ID")
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Account_Statement_Link(Statement_ID,Statement_link)  values("
            S = S & "N'" & MAXID & "',N'مسحوبات جارى الشركاء')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Statement_link from Account_Statement_Link where Statement_link=N'ايداعات جارى الشركاء'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            MAXRECORDAuto("Account_Statement_Link", "Statement_ID")
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Account_Statement_Link(Statement_ID,Statement_link)  values("
            S = S & "N'" & MAXID & "',N'ايداعات جارى الشركاء')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Statement_link from Account_Statement_Link where Statement_link=N'مسحوبات رأس المال'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            MAXRECORDAuto("Account_Statement_Link", "Statement_ID")
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Account_Statement_Link(Statement_ID,Statement_link)  values("
            S = S & "N'" & MAXID & "',N'مسحوبات رأس المال')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Statement_link from Account_Statement_Link where Statement_link=N'ايداعات رأس المال'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            MAXRECORDAuto("Account_Statement_Link", "Statement_ID")
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Account_Statement_Link(Statement_ID,Statement_link)  values("
            S = S & "N'" & MAXID & "',N'ايداعات رأس المال')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If


    End Sub

    Public Sub UpdateTransferCash()
        connect()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Sales_Bill Set Vendorname =N'نقدا' where Vendorname =N'كاش'" : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Vst set Vendorname =N'نقدا' where Vendorname =N'كاش'" : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Vst_disc set Vendorname =N'نقدا' where Vendorname =N'كاش'" : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update IM_Bsal set Vendorname =N'نقدا' where Vendorname =N'كاش'" : cmd.ExecuteNonQuery()

    End Sub

    Public Sub UpdatePurchase_bill()
        connect()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update purchase_bill set bill_No =N'جرد1' where bill_No =N'جرد'" : cmd.ExecuteNonQuery()

        Dim aray_1 As New ArrayList
        Dim aray_2 As New ArrayList
        aray_1.Clear()
        aray_2.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select id,Vendorname from  Customers"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr("id"))
            aray_2.Add(dr("Vendorname"))
        Loop
        For i As Integer = 0 To aray_1.Count - 1
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "update Customers set Cust_Code = " & aray_1(i) & " where Vendorname =N'" & aray_2(i) & "'"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
    End Sub

    Public Sub UpdateStore()
        connect()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Items set ValStore =0,profits=0" : cmd.ExecuteNonQuery()
    End Sub

    Public Sub UpdateItemsQuickSearch()
        connect()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Items set QuickSearch =0" : cmd.ExecuteNonQuery()
    End Sub

    Public Sub UpdatePayment()
        connect()
        Dim aray_id As New ArrayList
        Dim aray_VND_no As New ArrayList
        Dim aray_BillNo As New ArrayList
        Dim aray_VND_ho As New ArrayList

        aray_id.Clear()
        aray_VND_no.Clear()
        aray_BillNo.Clear()
        aray_VND_ho.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select id,VND_no,BillNo,VND_ho from Vst"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_id.Add(dr(0))
            aray_VND_no.Add(dr(1))
            aray_BillNo.Add(dr(2))
            aray_VND_ho.Add(dr(3))
        Loop

        For i As Integer = 0 To aray_id.Count - 1
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If aray_VND_ho(i) = "نقدي" Then
                cmd.CommandText = "update Vst set BillNo =N'" & aray_VND_no(i) & "',VND_no =N'" & aray_BillNo(i) & "' where id =N'" & aray_id(i) & "' and VND_ho =N'نقدي'" : cmd.ExecuteNonQuery()
            End If
        Next


        'Dim aray_id As New ArrayList
        'Dim aray_VND_no As New ArrayList
        'Dim aray_BillNo As New ArrayList

        'aray_id.Clear()
        'aray_VND_no.Clear()
        'aray_BillNo.Clear()

        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select id,VND_no,BillNo from Vst"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_id.Add(dr(0))
        '    aray_VND_no.Add(dr(1))
        '    aray_BillNo.Add(dr(2))
        'Loop


        'For i As Integer = 0 To aray_id.Count - 1
        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "update Vst set BillNo =N'" & aray_VND_no(i) & "',VND_no =N'" & aray_BillNo(i) & "' where id =N'" & aray_id(i) & "'" : cmd.ExecuteNonQuery()
        'Next

        ''==========================================================================

        aray_id.Clear()
        aray_VND_no.Clear()
        aray_BillNo.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select id,VND_no,BillNo from Vnd"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_id.Add(dr(0))
            aray_VND_no.Add(dr(1))
            aray_BillNo.Add(dr(2))
        Loop


        For i As Integer = 0 To aray_id.Count - 1
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vnd set BillNo =N'" & aray_VND_no(i) & "',VND_no =N'" & aray_BillNo(i) & "' where id =N'" & aray_id(i) & "'" : cmd.ExecuteNonQuery()
        Next



    End Sub

    Public Sub ChackUpdateParcodeSales()
        connect()
        Dim aray_itm_id As New ArrayList
        Dim aray_sname As New ArrayList
        Dim aray_Stores As New ArrayList
        aray_itm_id.Clear()
        aray_Stores.Clear()
        aray_sname.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select itm_id,sname,Stores,TinPrice from Items"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_itm_id.Add(dr("itm_id"))
            aray_Stores.Add(dr("Stores"))
            aray_sname.Add(dr("sname"))
        Loop

        For i As Integer = 0 To aray_itm_id.Count - 1
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "update BillsalData set itm_id = " & aray_itm_id(i) & " where itm_name =N'" & aray_sname(i) & "' and Stores =N'" & aray_Stores(i) & "'"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
    End Sub

    Public Sub UpdateTinPriceAverageNull()
        connect()
        Dim aray_1 As New ArrayList
        Dim aray_2 As New ArrayList
        Dim aray_3 As New ArrayList
        aray_1.Clear()
        aray_2.Clear()
        aray_3.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select id,TinPrice,TinPriceAverage from  Items"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr("id"))
            aray_2.Add(dr("TinPrice"))
            aray_3.Add(dr("TinPriceAverage"))
        Loop
        For i As Integer = 0 To aray_1.Count - 1
            If aray_3(i).ToString = "" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update Items set TinPriceAverage = " & aray_2(i) & " where id =N'" & aray_1(i) & "'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        Next
    End Sub

    Public Sub UpdateCustomersCustCode()
        connect()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(Cust_Code As float)) as mb FROM Customers where Cust_Code <> ''"
            dr = cmd.ExecuteReader
            dr.Read()
            Dim sh As Long
            sh = dr("mb")
        Catch ex As Exception
            Dim aray_1 As New ArrayList
            Dim aray_2 As New ArrayList
            aray_1.Clear()
            aray_2.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select id,Cust_Code from  Customers"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_1.Add(dr("id"))
                aray_2.Add(dr("Cust_Code"))
            Loop
            For i As Integer = 0 To aray_1.Count - 1
                If aray_2(i).ToString = "" Then
                    CodeID += 1
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    S = "update Customers set Cust_Code = " & CodeID & " where id =N'" & aray_1(i) & "'"
                    cmd.CommandText = S : cmd.ExecuteNonQuery()
                End If
            Next
        End Try

    End Sub

    Public Sub AddItemsUnity()
        Dim aray_itm_id As New ArrayList

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id from Items"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_itm_id.Add(dr(0))
        Loop

        Dim StausMainStore As String = ""
        Dim qu As String = ""
        For i As Integer = 0 To aray_itm_id.Count - 1
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from ItemsUnity where itm_id =N'" & aray_itm_id(i).ToString & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into ItemsUnity(itm_id,Unity_Name,NumberPieces,TinPriceUnit,SalPriceUnit,DefaultTin,DefaultSale,Company_Branch_ID)  values("
                S = S & "N'" & aray_itm_id(i).ToString & "',N'قطعة',N'1',N'1',N'1',N'1',N'1',N'" & Company_Branch_ID & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        Next

    End Sub

    Public Sub EditItemsUnityParcode()
        Dim aray_itm_id As New ArrayList
        Dim aray_Unity_ID As New ArrayList

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id,Unity_ID from ItemsUnity"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_itm_id.Add(dr(0))
            aray_Unity_ID.Add(dr(1))
        Loop

        Dim StausMainStore As String = ""
        Dim qu As String = ""
        For i As Integer = 0 To aray_Unity_ID.Count - 1
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "update ItemsUnity set itm_id_Unity =N'" & aray_itm_id(i).ToString & "' where Unity_ID=N'" & aray_Unity_ID(i).ToString & "'"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

    End Sub

    Public Sub UpdateItemsUnitySmall()
        Dim aray_itm_id As New ArrayList
        Dim aray_Unity_ID As New ArrayList

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id,Unity_ID from ItemsUnity"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_itm_id.Add(dr(0))
            aray_Unity_ID.Add(dr(1))
        Loop

        Dim StausMainStore As String = ""
        Dim qu As String = ""
        For i As Integer = 0 To aray_Unity_ID.Count - 1

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from ItemsUnity where itm_id =N'" & aray_itm_id(i).ToString & "' and Unity_Name =N'المتوسط'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update ItemsUnity set Unity_Name =N'الاصغر' where Unity_ID=N'" & aray_Unity_ID(i).ToString & "'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        Next

    End Sub

    Public Sub UpdateUnitFromCartoon()
        connect()

        GetUpdateUnitFromCartoon("BillsalData")
        GetUpdateUnitFromCartoon("BilltINData")
        GetUpdateUnitFromCartoon("IM_Bsal_Data")
        GetUpdateUnitFromCartoon("IM_Btin_Data")
        GetUpdateUnitFromCartoon("ItemsTransfer_BillsalData")
        GetUpdateUnitFromCartoon("ItemsTransfer_BilltINData")
        GetUpdateUnitFromCartoon("Manufacturing_BillsalData")
        GetUpdateUnitFromCartoon("Manufacturing_BilltINData")
        GetUpdateUnitFromCartoon("ManufacturingProductAdd")
        'IM.UpdateDataBase()
    End Sub

    Private Sub GetUpdateUnitFromCartoon(ByVal TabelName As String)
        Dim aray_id As New ArrayList
        Dim aray_itm_id As New ArrayList
        Dim aray_qu As New ArrayList
        Dim aray_qu_unity As New ArrayList
        Dim Unity_Name, NumberPieces, TotalNumberPieces As String

        aray_id.Clear()
        aray_itm_id.Clear()
        aray_qu.Clear()
        aray_qu_unity.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select id,itm_id,qu,qu_unity from " & TabelName & ""
        cmd.CommandText = "select id,itm_id,qu,qu_unity from " & TabelName & " where itm_id=N'91'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_id.Add(dr("id"))
            aray_itm_id.Add(dr("itm_id"))
            aray_qu.Add(dr("qu"))
            aray_qu_unity.Add(dr("qu_unity"))
        Loop

        For i As Integer = 0 To aray_id.Count - 1
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Unity_Name,NumberPieces from ItemsUnity where itm_id=N'" & aray_itm_id(i).ToString & "' and NumberPieces <> 1"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                'If aray_itm_id(i).ToString = "1" Then
                '    aray_itm_id(i) = "1"
                'End If
                Unity_Name = dr("Unity_Name").ToString
                NumberPieces = dr("NumberPieces").ToString

                TotalNumberPieces = Val(NumberPieces) * Val(aray_qu(i).ToString)

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update " & TabelName & " set qu = " & TotalNumberPieces & " where id=N'" & aray_id(i).ToString & "'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If

        Next
    End Sub

    Public Sub GetUpdateTinPriceAverageProfits()
        Dim aray_id As New ArrayList
        Dim aray_itm_id As New ArrayList
        Dim aray_Stores As New ArrayList
        Dim aray_price As New ArrayList
        Dim aray_TinPriceAverage As New ArrayList
        Dim aray_qu As New ArrayList
        Dim aray_itm_Unity As New ArrayList
        Dim aray_Profits As New ArrayList

        Dim Xprice, XTinPriceAverage As Double

        aray_id.Clear()
        aray_itm_id.Clear()
        aray_Stores.Clear()
        aray_price.Clear()
        aray_TinPriceAverage.Clear()
        aray_qu.Clear()
        aray_itm_Unity.Clear()
        aray_Profits.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select id,itm_id,Stores,price,TinPriceAverage,qu_unity,itm_Unity,Profits from BillsalData"
        'cmd.CommandText = "select id,itm_id,Stores,price,TinPriceAverage,qu_unity,Profits from BillsalData"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_id.Add(dr("id"))
            aray_itm_id.Add(dr("itm_id"))
            aray_Stores.Add(dr("Stores"))
            aray_price.Add(dr("price"))
            aray_TinPriceAverage.Add(dr("TinPriceAverage"))
            aray_qu.Add(dr("qu_unity"))
            aray_itm_Unity.Add(dr("itm_Unity"))
            aray_Profits.Add(dr("Profits"))
        Loop

        For i As Integer = 0 To aray_id.Count - 1
            If aray_price(i).ToString() <> "" Then
                Xprice = aray_price(i).ToString()
            Else
                Xprice = 0
            End If
            If aray_TinPriceAverage(i).ToString() <> "" Then
                XTinPriceAverage = aray_TinPriceAverage(i).ToString()
            Else
                XTinPriceAverage = 0
            End If

            If Xprice < XTinPriceAverage Then
                AverageTinPrice(aray_itm_id(i).ToString, aray_Stores(i).ToString, aray_price(i).ToString(), aray_qu(i).ToString(), aray_itm_Unity(i).ToString())

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update BillsalData set TinPriceAverage = " & TinPriceAverage & ",Profits = " & Profits & " where id=N'" & aray_id(i).ToString & "'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        Next
    End Sub

    Public Sub AverageTinPrice(ByVal itm_id As String, ByVal Stores As String, ByVal Price As Double, ByVal Qunt As Double, ByVal Unity As String)
        'Dim PriceAverage As String = 0
        'TinPriceAverage = 0
        'Dim Xqunt As Double = Qunt

        'If NotUnityItemsProgram = "YES" Then
        '    PriceAverage = Cls.Get_Code_Value_Branch_More("ItemsUnity", "TinPriceUnit", "itm_id=N'" & itm_id & "' and Unity_Name=N'" & Unity & "'")
        '    TinPrice = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)

        '    Price = Cls.Get_Code_Value_Branch_More("ItemsUnity", "SalPriceUnit", "itm_id=N'" & itm_id & "' and Unity_Name=N'" & Unity & "'")
        '    Price = Math.Round(Convert.ToDouble(Val(Price)), 2)
        'Else
        '    PriceAverage = Cls.Get_Code_Value_Stores_More("items", "TinPriceAverage", "itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'")
        '    TinPriceAverage = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)

        '    PriceAverage = Cls.Get_Code_Value_Stores_More("items", "TinPrice", "itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'")
        '    TinPrice = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)
        'End If

        ''If NotUnityItemsProgram = "NO" Then
        ''    NumberPieces = Cls.Get_Code_Value_Stores_More("ItemsUnity", "NumberPieces", "itm_id =N'" & itm_id & "' and Unity_Name=N'" & Unity & "'")
        ''    If NumberPieces <> 1 Then
        ''        Price = Cls.Get_Code_Value_Stores_More("Items", "SalPrice", "itm_id =N'" & itm_id & "' and Stores=N'" & Stores & "'")
        ''        Xqunt = Val(NumberPieces) * Val(Qunt)
        ''    End If
        ''End If

        'Price_Unity = Price

        'If PriceAverage = "" Then : PriceAverage = 0 : End If

        'If LastTinPriceItems = "NO" Then : PriceAverage = TinPriceAverage : Else PriceAverage = TinPrice : End If
        'If TinPriceAverage = 0 Then
        '    PriceAverage = TinPrice
        '    TinPriceAverage = TinPrice
        'End If
        'TotalPrice = Price - PriceAverage
        'Profits = TotalPrice * Xqunt
        'Profits = Math.Round(Profits, 2)
    End Sub

    Public Sub AverageTinPriceAverage(ByVal itm_id As String, ByVal Stores As String, ByVal Price As Double, ByVal Qunt As Double, ByVal Unity As String)

        Dim BalanceBeforeBuying, TotalBalanceBeforeBuying As Double
        Dim NumberPieces As String = "0"

        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id =N'" & itm_id & "' and Unity_Name =N'" & Unity & "'" : dr = cmd.ExecuteReader : dr.Read()
        'If dr.HasRows = True Then
        '    NumberPieces = dr(0).ToString
        'End If

        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'If NumberPieces = 1 Then
        '    cmd.CommandText = "select TinPrice from Items where itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
        'Else
        '    cmd.CommandText = "select TinPriceUnit from ItemsUnity where itm_id =N'" & itm_id & "' and Unity_Name =N'" & Unity & "'" : dr = cmd.ExecuteReader : dr.Read()
        'End If
        'If dr.HasRows = True Then
        '    TinPriceAverage = dr(0)
        '    TinPriceAverage = Math.Round(Val(TinPriceAverage), 2)
        'Else
        '    TinPriceAverage = 0
        'End If
        TinPriceAverage = Price
        '================================================ المخزون الحالى =====================================================
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select store from Items where itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'"
        'dr = cmd.ExecuteReader : dr.Read()
        'If dr.HasRows = True Then
        '    StoreItems = dr("store").ToString()
        'End If
        '================================================ متوسط سعر الشراء الجديد =====================================================



        BalanceBeforeBuying = Val(TinPriceAverage) * Val(StoreItems)

        TotalBalanceBeforeBuying = Val(TotalPrice) + Val(BalanceBeforeBuying)
        TotalBalanceBeforeBuying = Math.Round(TotalBalanceBeforeBuying, 2)

        Dim TotalTotal As Double = Val(StoreItems) + Val(Qunt)
        If TotalBalanceBeforeBuying = 0 And TotalTotal = 0 Then
            TotalPriceBeforeAverage = 0
        Else
            TotalPriceBeforeAverage = Val(TotalBalanceBeforeBuying) / Val(TotalTotal)
            TotalPriceBeforeAverage = Math.Round(TotalPriceBeforeAverage, 2)
        End If
        If Qunt <> 0 Then
            StoreItems += Qunt
        End If
        If TotalPriceBeforeAverage = 0 Then
            TotalPriceBeforeAverage = Price
        End If
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update items set TinPriceAverage = " & Val(TotalPriceBeforeAverage) & " where itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'" : cmd.ExecuteNonQuery()
    End Sub

    Public Sub Convert_qu_qu_unity()
        connect()

        Dim aray_1 As New ArrayList
        Dim aray_2 As New ArrayList
        Dim aray_3 As New ArrayList
        aray_1.Clear()
        aray_2.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select itm_id,qu,qu_unity from  BillsalData"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr("itm_id"))
            aray_2.Add(dr("qu"))
            aray_3.Add(dr("qu_unity"))
        Loop
        For i As Integer = 0 To aray_1.Count - 1
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "update BillsalData set qu = " & Val(aray_3(i).ToString) & ",qu_unity = " & Val(aray_2(i).ToString) & " where itm_id =N'" & aray_1(i) & "'"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
    End Sub

    Public Sub Sum_Total_qu_Price()
        connect()

        Dim aray_id, aray_bill_no, aray_qu_unity, aray_price As New ArrayList
        Dim Total As Double = 0

        aray_id.Clear() : aray_bill_no.Clear() : aray_qu_unity.Clear() : aray_price.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select id,bill_no,qu_unity,price from  BillsalData"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_id.Add(dr("id")) : aray_bill_no.Add(dr("bill_no")) : aray_qu_unity.Add(dr("qu_unity")) : aray_price.Add(dr("price"))
        Loop

        For i As Integer = 0 To aray_id.Count - 1
            Total = Val(aray_qu_unity(i).ToString()) * Val(aray_price(i).ToString())
            Total = Math.Round(Total, 2)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "update BillsalData set totalprice =N'" & Total & "' where id =N'" & aray_id(i).ToString & "'"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
    End Sub

    Public Sub GetBillsalDataEditCustNameAndStat()
        Dim aray_id, aray_bill_no, aray_qu_unity, aray_price, aray_PriceAverage As New ArrayList
        Dim Vendorname As String = "" : Dim Stat As String = ""
        Dim Total, Profits As Double

        aray_id.Clear() : aray_bill_no.Clear() : aray_qu_unity.Clear() : aray_price.Clear() : aray_PriceAverage.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select id,bill_no,qu_unity,price,TinPriceAverage from  BillsalData"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_id.Add(dr("id")) : aray_bill_no.Add(dr("bill_no")) : aray_qu_unity.Add(dr("qu_unity")) : aray_price.Add(dr("price")) : aray_PriceAverage.Add(dr("TinPriceAverage"))
        Loop

        For i As Integer = 0 To aray_id.Count - 1

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Vendorname,Stat from Sales_Bill where bill_No=N'" & aray_bill_no(i).ToString & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Vendorname = dr(0).ToString : Stat = dr(1).ToString
            End If
            Total = Val(aray_qu_unity(i).ToString()) * Val(aray_price(i).ToString())
            Total = Math.Round(Total, 2)

            TotalPrice = Val(aray_price(i).ToString()) - Val(aray_PriceAverage(i).ToString())
            Profits = TotalPrice * Val(aray_qu_unity(i).ToString())
            Profits = Math.Round(Profits, 2)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "update BillsalData set Vendorname =N'" & Vendorname & "',Stat = N'" & Stat & "',totalprice =N'" & Total & "',Profits =N'" & Profits & "' where id =N'" & aray_id(i).ToString & "'"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
    End Sub

    Private Sub MAXRECORDAuto(ByVal Tabel As String, ByVal Feild As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from " + Tabel + ""
            dr = cmd.ExecuteReader

            Dim dt As New DataTable
            dt.Load(dr)

            If dt.Rows.Count = 0 Then
                MAXID = 1
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(CAST(" + Feild + " As float)) as mb FROM " + Tabel + ""
                dr = cmd.ExecuteReader
                dr.Read()

                Dim sh As Long
                sh = dr("mb")
                MAXID = sh + 1
            End If
        Catch ex As Exception
        End Try
    End Sub

    Public Sub Controlling_qu_qu_unity_BillsalData()
        connect()
        Dim Total_qu, qu, qu_unity As Double


        Dim aray_1 As New ArrayList
        Dim aray_2 As New ArrayList
        Dim aray_3 As New ArrayList

        aray_1.Clear() : aray_2.Clear() : aray_3.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select id,qu,qu_unity from  BillsalData"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr("id"))
            aray_2.Add(dr("qu"))
            aray_3.Add(dr("qu_unity"))
        Loop

        For i As Integer = 0 To aray_1.Count - 1
            qu = Val(aray_2(i).ToString)
            qu_unity = Val(aray_3(i).ToString)

            If qu < qu_unity Then
                Total_qu = qu_unity
            Else
                Total_qu = qu
            End If
            If qu <> qu_unity Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update BillsalData set qu = " & Total_qu & ",qu_unity = " & Total_qu & " where id =N'" & aray_1(i) & "'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        Next
    End Sub

    Public Sub Controlling_qu_qu_unity_BilltINData()
        connect()
        Dim Total_qu, qu, qu_unity As Double


        Dim aray_1 As New ArrayList
        Dim aray_2 As New ArrayList
        Dim aray_3 As New ArrayList

        aray_1.Clear() : aray_2.Clear() : aray_3.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select id,qu,qu_unity from  BilltINData"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr("id"))
            aray_2.Add(dr("qu"))
            aray_3.Add(dr("qu_unity"))
        Loop

        For i As Integer = 0 To aray_1.Count - 1
            qu = Val(aray_2(i).ToString)
            qu_unity = Val(aray_3(i).ToString)

            If qu < qu_unity Then
                Total_qu = qu_unity
            Else
                Total_qu = qu
            End If
            If qu <> qu_unity Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "update BilltINData set qu = " & Total_qu & ",qu_unity = " & Total_qu & " where id =N'" & aray_1(i) & "'"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        Next
    End Sub

    Public Sub Edit_TinPriceUnit_SalPriceUnit()
        Dim itm_id, TinPrice, SalPrice As String
        Dim aray_1, aray_2, aray_3 As New ArrayList
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select itm_id,TinPrice,SalPrice from  Items"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr("itm_id"))
            aray_2.Add(dr("TinPrice"))
            aray_3.Add(dr("SalPrice"))
        Loop
        For i As Integer = 0 To aray_1.Count - 1
            itm_id = aray_1(i).ToString
            TinPrice = aray_2(i).ToString
            SalPrice = aray_3(i).ToString

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsUnity set TinPriceUnit =N'" & TinPrice & "',SalPriceUnit =N'" & SalPrice & "' where itm_id=N'" & itm_id & "'" : cmd.ExecuteNonQuery()
        Next
    End Sub

    Public Sub GetUpdate_TinPriceAverage_Items()
        Dim aray_id As New ArrayList
        Dim aray_itm_id As New ArrayList
        Dim aray_Stores As New ArrayList
        Dim aray_price As New ArrayList
        Dim aray_TinPriceAverage As New ArrayList
        Dim aray_qu As New ArrayList
        Dim aray_itm_Unity As New ArrayList
        Dim aray_id_BilltINData As New ArrayList

        aray_id.Clear()
        aray_id_BilltINData.Clear()
        aray_itm_id.Clear()
        aray_Stores.Clear()
        aray_price.Clear()
        aray_TinPriceAverage.Clear()
        aray_qu.Clear()
        aray_itm_Unity.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select id,itm_id,Stores,Unity from Items"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_id.Add(dr("id"))
            aray_itm_id.Add(dr("itm_id"))
            aray_Stores.Add(dr("Stores"))
            aray_itm_Unity.Add(dr("Unity"))
        Loop
        Dim bill_date As String = ""
        For i As Integer = 0 To aray_id.Count - 1
            'If aray_itm_id(i).ToString = "144" Then
            '    NumberPieces = 0
            'End If
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select itm_id, Stores, MAX(bill_date) As Expr1 From dbo.BilltINData Group By itm_id, Stores HAVING(itm_id =N'" & aray_itm_id(i).ToString & "') AND (Stores =N'" & aray_Stores(i).ToString & "')"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                bill_date = dr(2).ToString
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select itm_id, Stores, bill_date As Expr1, TinPriceAverage, price  From dbo.BilltINData  Group By itm_id, Stores, bill_date, TinPriceAverage, price  HAVING(itm_id = N'" & aray_itm_id(i).ToString & "') AND (Stores = N'" & aray_Stores(i).ToString & "') AND (bill_date = N'" & bill_date & "')"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                TotalPriceBeforeAverage = dr(3).ToString
                TotalPrice = dr(4).ToString
            End If

            If TotalPriceBeforeAverage = 0 Or TotalPrice = 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select TinPrice,TinPriceAverage  From Items  Where itm_id = N'" & aray_itm_id(i).ToString & "' AND Stores = N'" & aray_Stores(i).ToString & "'"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    TotalPrice = dr(0).ToString
                    TotalPriceBeforeAverage = dr(1).ToString
                End If
            End If
            If TotalPriceBeforeAverage = 0 Then
                TotalPriceBeforeAverage = TotalPrice
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update items set TinPriceAverage = " & Val(TotalPriceBeforeAverage) & " where itm_id =N'" & aray_itm_id(i).ToString & "' and Stores =N'" & aray_Stores(i).ToString & "'" : cmd.ExecuteNonQuery()


            'aray_id_BilltINData.Clear()
            'aray_price.Clear()
            'aray_TinPriceAverage.Clear()
            'aray_qu.Clear()

            'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            'cmd.CommandText = "Select id,price,TinPriceAverage,qu from  BilltINData where itm_id =N'" & aray_itm_id(i).ToString & "' and Stores =N'" & aray_Stores(i).ToString & "'"
            'dr = cmd.ExecuteReader
            'Do While dr.Read = True
            '    aray_id_BilltINData.Add(dr("id"))
            '    aray_price.Add(dr("price"))
            '    aray_TinPriceAverage.Add(dr("TinPriceAverage"))
            '    aray_qu.Add(dr("qu"))
            'Loop

            'StoreItems = 0
            'For M As Integer = 0 To aray_id_BilltINData.Count - 1
            '    If aray_itm_id(i).ToString = "5" Then
            '        NumberPieces = 0
            '    End If
            '    If aray_qu(M).ToString() <> 0 Then
            'AverageTinPriceAverage(aray_itm_id(i).ToString, aray_Stores(i).ToString, aray_price(M).ToString(), aray_qu(M).ToString(), aray_itm_Unity(i).ToString())
            '    End If
            'Next
        Next
    End Sub

    Public Sub GetUpdate_TinPriceAverage_BilltINData_Zero()
        Dim aray_id As New ArrayList
        Dim aray_itm_id As New ArrayList
        Dim aray_Stores As New ArrayList
        Dim aray_TinPriceAverage As New ArrayList

        aray_id.Clear()
        aray_itm_id.Clear()
        aray_Stores.Clear()
        aray_TinPriceAverage.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select id,itm_id,Stores,TinPriceAverage from BillsalData where bill_no <>N'تسوية' and TinPriceAverage=0"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_id.Add(dr("id"))
            aray_itm_id.Add(dr("itm_id"))
            aray_Stores.Add(dr("Stores"))
            aray_TinPriceAverage.Add(dr("TinPriceAverage"))
        Loop
        Dim TinPriceAverage As String = ""
        For i As Integer = 0 To aray_id.Count - 1
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TinPriceAverage from Items where itm_id =N'" & aray_itm_id(i).ToString & "' and Stores =N'" & aray_Stores(i).ToString & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                TinPriceAverage = dr(0).ToString
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set TinPriceAverage = " & Val(TinPriceAverage) & " where id =N'" & aray_id(i).ToString & "'" : cmd.ExecuteNonQuery()
        Next
    End Sub

    Public Sub Get_TinPriceAverage_Items_First_ALL()
        Dim aray_id As New ArrayList
        Dim aray_itm_id As New ArrayList
        Dim aray_Stores As New ArrayList

        Dim aray_id_Tin As New ArrayList
        Dim aray_itm_id_Tin As New ArrayList
        Dim aray_Stores_Tin As New ArrayList
        Dim aray_price As New ArrayList
        Dim aray_TinPriceAverage As New ArrayList
        Dim aray_qu As New ArrayList
        Dim aray_itm_Unity As New ArrayList
        Dim aray_TotalPrice As New ArrayList


        aray_id.Clear()
        aray_id_Tin.Clear()
        aray_itm_id.Clear()
        aray_itm_id_Tin.Clear()
        aray_Stores.Clear()
        aray_Stores_Tin.Clear()
        aray_price.Clear()
        aray_TinPriceAverage.Clear()
        aray_qu.Clear()
        aray_itm_Unity.Clear()
        aray_TotalPrice.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select id,itm_id,Stores from Items Where itm_id = N'5023716666662'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_id.Add(dr("id"))
            aray_itm_id.Add(dr("itm_id"))
            aray_Stores.Add(dr("Stores"))
        Loop

        For i As Integer = 0 To aray_id.Count - 1

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select id,bill_no,itm_id,Stores,itm_Unity,qu,totalprice,price,bill_date from BilltINData  Where itm_id = N'" & aray_itm_id(i).ToString & "' ORDER BY bill_date"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_id_Tin.Add(dr("id"))
                aray_itm_id_Tin.Add(dr("itm_id"))
                aray_Stores_Tin.Add(dr("Stores"))
                aray_itm_Unity.Add(dr("itm_Unity"))
                aray_qu.Add(dr("qu"))
                aray_TotalPrice.Add(dr("totalprice"))
                aray_price.Add(dr("price"))
            Loop

            For M As Integer = 0 To aray_id_Tin.Count - 1

                PriceTinAverage(aray_itm_id_Tin(i).ToString, aray_Stores_Tin(i).ToString, aray_itm_Unity(i).ToString, aray_qu(i).ToString, aray_TotalPrice(i).ToString, aray_price(i).ToString)

            Next

        Next
    End Sub

    Private Sub PriceTinAverage(ByVal Parcode As String, ByVal Stores As String, ByVal Unity As String, ByVal qunt As Double, ByVal TotalPrice As Double, ByVal Price As Double)
        Dim StoreItems, TotalPriceTinAverage, BalanceBeforeBuying, TotalBalanceBeforeBuying, Price_Unity As Double
        '================================================ المخزون الحالى =====================================================
        Dim Xqunt As Double = qunt

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select store,TinPriceAverage from Items where itm_id =N'" & Parcode & "' and Stores =N'" & Stores & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            StoreItems = dr("store").ToString()
            Dim xx As String = dr("TinPriceAverage").ToString()
            If StoreItems = 0 Then
                TotalPriceTinAverage = Price
            Else
                If xx = "" Then
                    TotalPriceTinAverage = 0
                Else
                    TotalPriceTinAverage = xx
                End If
            End If
        End If
        If NotUnityItemsProgram = "YES" Then
            NumberPieces = Cls.Get_Code_Value_Branch_More("ItemsUnity", "NumberPieces", "itm_id=N'" & Parcode & "' and Unity_Name=N'" & Unity & "'")
            If NumberPieces <> 1 Then
                Price_Unity = Cls.Get_Code_Value_Stores_More("Items", "SalPrice", "itm_id =N'" & Parcode & "' and Stores=N'" & Stores & "'")
                Xqunt = Val(NumberPieces) * Val(qunt)
            End If
        End If

        '================================================ متوسط سعر الشراء الجديد =====================================================

        'Try
        BalanceBeforeBuying = Val(TotalPriceTinAverage) * Val(StoreItems)
        If BalanceBeforeBuying > 0 Then
            TotalBalanceBeforeBuying = Val(TotalPrice) + Val(BalanceBeforeBuying)
            TotalBalanceBeforeBuying = Math.Round(TotalBalanceBeforeBuying, 2)

            Dim TotalTotal As Double = Val(StoreItems) + Val(Xqunt)
            If TotalBalanceBeforeBuying = 0 And TotalTotal = 0 Then
                TotalPriceBeforeAverage = 0
            Else
                TotalPriceBeforeAverage = Val(TotalBalanceBeforeBuying) / Val(TotalTotal)
                If TinPriceAverageThreeDigits = "NO" Then
                    TotalPriceBeforeAverage = Math.Round(TotalPriceBeforeAverage, 2)
                Else
                    TotalPriceBeforeAverage = Math.Round(TotalPriceBeforeAverage, 3)
                End If
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update items set TinPriceAverage = " & Val(TotalPriceBeforeAverage) & " where itm_id =N'" & Parcode & "' and Stores =N'" & Stores & "'" : cmd.ExecuteNonQuery()

        Else
            TotalPriceBeforeAverage = TotalPriceTinAverage
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update items set TinPriceAverage = " & Val(TotalPriceBeforeAverage) & " where itm_id =N'" & Parcode & "' and Stores =N'" & Stores & "'" : cmd.ExecuteNonQuery()
        End If


        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub

    Public Sub Get_FrmEditsaleAd_Profits()
        connect()

        Dim XSalPrice2 As String
        Dim XSalPrice1 As String

        Dim aray_ID_ID As New ArrayList
        Dim aray_ITM_ID As New ArrayList
        Dim aray_price As New ArrayList
        Dim aray_qu As New ArrayList
        Dim aray_qu_unity As New ArrayList
        Dim aray_itm_Unity As New ArrayList
        Dim aray_Stores As New ArrayList
        Dim aray_DiscountRate As New ArrayList
        Dim aray_DiscountsTin As New ArrayList
        Dim aray_id As New ArrayList
        Dim aray_BILL_NO As New ArrayList
        Dim aray_TinPrice As New ArrayList
        Dim aray_TinPriceAverage As New ArrayList

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select id,BILL_NO from  Sales_Bill where BILL_NO <> N'جرد'"
        'cmd.CommandText = "Select id,BILL_NO from  Sales_Bill where BILL_NO= N'85'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_id.Add(dr("id"))
            aray_BILL_NO.Add(dr("BILL_NO"))
        Loop

        Dim arr_BILL_NO As String = ""

        For i As Integer = 0 To aray_id.Count - 1
            arr_BILL_NO = aray_BILL_NO(i).ToString

            aray_ID_ID.Clear() : aray_ITM_ID.Clear() : aray_price.Clear() : aray_qu.Clear() : aray_qu_unity.Clear() : aray_itm_Unity.Clear() : aray_Stores.Clear() : aray_DiscountRate.Clear() : aray_DiscountsTin.Clear() : aray_TinPrice.Clear() : aray_TinPriceAverage.Clear()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select id,itm_id,price,qu,qu_unity,itm_Unity,Stores,username,Discounts,DiscountsTin,TinPrice,TinPriceAverage from  BillsalData  where bill_no =N'" & arr_BILL_NO & "'"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_ID_ID.Add(dr("id"))
                aray_ITM_ID.Add(dr("itm_id"))
                aray_price.Add(dr("price"))
                aray_qu.Add(dr("qu"))
                aray_qu_unity.Add(dr("qu_unity"))
                aray_itm_Unity.Add(dr("itm_Unity"))
                aray_Stores.Add(dr("Stores"))
                aray_DiscountRate.Add(dr("Discounts"))
                aray_DiscountsTin.Add(dr("DiscountsTin"))
                aray_TinPrice.Add(dr("TinPrice"))
                aray_TinPriceAverage.Add(dr("TinPriceAverage"))
            Loop

            '========================================================================================================
            Dim ID_ID As String = ""
            Dim BILL_NO As String = ""
            Dim itm_id As String = ""
            Dim Stores As String = ""
            Dim Qunt As String = ""
            Dim Unity As String = ""
            Dim price As String = ""
            Dim TinPrice As Double = 0
            Dim Price_Unity As Double = 0
            Dim DiscountRate As Double = 0
            Dim DiscountsTin As Double = 0
            Dim TinPriceAverage As Double = 0

            Dim LastTinPriceItems As String = "NO"
            For M As Integer = 0 To aray_ID_ID.Count - 1
                ID_ID = aray_ID_ID(M).ToString
                itm_id = aray_ITM_ID(M).ToString
                Stores = aray_Stores(M).ToString
                Qunt = aray_qu_unity(M).ToString
                price = aray_price(M).ToString
                Unity = aray_itm_Unity(M).ToString
                DiscountRate = Val(aray_DiscountRate(M).ToString)
                DiscountsTin = Val(aray_DiscountsTin(M).ToString)
                TinPrice = Val(aray_TinPrice(M).ToString)
                TinPriceAverage = Val(aray_TinPriceAverage(M).ToString)


                If TinPrice > price Then
                    XSalPrice1 = Val(TinPrice) - Val(price)
                Else
                    XSalPrice1 = Val(price) - Val(TinPrice)
                End If

                If TinPriceAverage > price Then
                    XSalPrice2 = Val(TinPriceAverage) - Val(price)
                Else
                    XSalPrice2 = Val(price) - Val(TinPriceAverage)
                End If

                If XSalPrice2 > XSalPrice1 Then
                    TinPriceAverage = TinPrice
                End If

                If DiscountsTin = 0 Then
                    DiscountsTin = Get_Code_Value_More_ALL_Select("Select Top(100) PERCENT Discounts From dbo.BilltINData Where (itm_id = N'" & itm_id & "') Order By id DESC")
                End If
                If DiscountsTin = 0 Then
                    DiscountsTin = Get_Code_Value_More_Double("Items", "RateDiscTinPrice", "itm_id =N'" & itm_id & "'")
                End If
                If DiscountsTin = 0 Then
                    DiscountsTin = Get_Code_Value_More_Double("Items", "RateDiscWholePrice", "itm_id =N'" & itm_id & "'")
                End If
                If DiscountsTin = 0 Then
                    DiscountsTin = Get_Code_Value_More_Double("Items", "RateDiscWholeWholePrice", "itm_id =N'" & itm_id & "'")
                End If
                If DiscountsTin = 0 Then
                    DiscountsTin = Get_Code_Value_More_Double("BilltINData", "Discounts", "itm_id =N'" & itm_id & "' and Discounts <> 0")
                End If

                If DiscountRate = 0 Then
                    DiscountRate = Get_Code_Value_More_Double("Items", "RateDiscSalPrice", "itm_id =N'" & itm_id & "'")
                End If
                If DiscountRate = 0 Then
                    DiscountRate = Get_Code_Value_More_Double("BillsalData", "Discounts", "itm_id =N'" & itm_id & "' and bill_no =N'" & BILL_NO & "'")
                End If
                If DiscountRate = 0 Then
                    DiscountRate = Get_Code_Value_More_Double("BillsalData", "Discounts", "itm_id =N'" & itm_id & "' and Discounts <> 0")
                End If

                '==================================================

                Dim PriceAverage As String = ""
                Dim NumberPieces As String = ""
                Dim Xqunt As Double = Qunt

                If TinPriceAverage = 0 Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "Select TinPriceAverage  From items where itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then
                        PriceAverage = dr(0).ToString
                        TinPriceAverage = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)
                    End If
                End If

                If TinPrice = 0 Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "Select TinPrice  From items where itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then
                        PriceAverage = dr(0).ToString
                        TinPrice = Math.Round(Convert.ToDouble(Val(PriceAverage)), 2)
                    End If
                End If

                'If NotUnityItemsProgram = "YES" Then
                '    NumberPieces = Cls.Get_Code_Value_Stores_More("ItemsUnity", "NumberPieces", "itm_id =N'" & itm_id & "' and Unity_Name=N'" & Unity & "'")
                '    If NumberPieces <> 1 Then
                '        price = Cls.Get_Code_Value_Stores_More("Items", "SalPrice", "itm_id =N'" & itm_id & "' and Stores=N'" & Stores & "'")
                '        Xqunt = Val(NumberPieces) * Val(Qunt)
                '    End If
                'End If

                Price_Unity = price

                If PriceAverage = "" Then : PriceAverage = 0 : End If

                If LastTinPriceItems = "NO" Then : PriceAverage = TinPriceAverage : Else PriceAverage = TinPrice : End If
                If TinPriceAverage = 0 Then
                    PriceAverage = TinPrice
                    TinPriceAverage = TinPrice
                End If
                TotalPrice = price - PriceAverage
                Profits = TotalPrice * Xqunt
                Profits = Math.Round(Profits, 2)
                If DiscountRate <> 0 Then
                    If DiscountsTin <> 0 Then
                        Dim TotalDiscountRate As Double
                        If DiscountsTin > DiscountRate Then
                            TotalDiscountRate = DiscountsTin - DiscountRate
                        Else
                            TotalDiscountRate = DiscountRate - DiscountsTin
                        End If
                        Dim TotalRate As Double = Format(Val(PriceAverage) * Val(TotalDiscountRate) / 100, "Fixed")
                        Profits = Format(Val(Xqunt) * Val(TotalRate))
                        Profits = Math.Round(Profits, 2)
                    End If
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BillsalData set Profits = " & Val(Profits) & " where id =N'" & ID_ID & "'" : cmd.ExecuteNonQuery()
            Next
        Next

    End Sub

    Public Sub Get_Chack_Delete_BilltINData_From_Purchase_bill()
        connect()

        Dim aray_id As New ArrayList
        Dim aray_BILL_NO As New ArrayList
        Dim arr_BILL_NO As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select id,bill_no from  BilltINData where BILL_NO <> N'جرد' and BILL_NO <> N'تسوية'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_id.Add(dr("id"))
            aray_BILL_NO.Add(dr("bill_no"))
        Loop

        For i As Integer = 0 To aray_id.Count - 1
            arr_BILL_NO = aray_BILL_NO(i).ToString
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select bill_no  From purchase_bill where BILL_NO =N'" & arr_BILL_NO & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  BilltINData where bill_no =N'" & arr_BILL_NO & "'" : cmd.ExecuteNonQuery()
            End If
        Next

        '======================================================================================================================
        aray_BILL_NO.Clear() : aray_id.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select id,bill_no from  BillsalData where BILL_NO <> N'جرد' and BILL_NO <> N'تسوية'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_id.Add(dr("id"))
            aray_BILL_NO.Add(dr("bill_no"))
        Loop

        For i As Integer = 0 To aray_id.Count - 1
            arr_BILL_NO = aray_BILL_NO(i).ToString
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select bill_no  From Sales_Bill where BILL_NO =N'" & arr_BILL_NO & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  BillsalData where bill_no =N'" & arr_BILL_NO & "'" : cmd.ExecuteNonQuery()
            End If
        Next

        '======================================================================================================================
        aray_BILL_NO.Clear() : aray_id.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select id,bill_no from  IM_Bsal_Data where BILL_NO <> N'جرد' and BILL_NO <> N'تسوية'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_id.Add(dr("id"))
            aray_BILL_NO.Add(dr("bill_no"))
        Loop

        For i As Integer = 0 To aray_id.Count - 1
            arr_BILL_NO = aray_BILL_NO(i).ToString
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select bill_no  From IM_Bsal where BILL_NO =N'" & arr_BILL_NO & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  IM_Bsal_Data where bill_no =N'" & arr_BILL_NO & "'" : cmd.ExecuteNonQuery()
            End If
        Next

        '======================================================================================================================
        aray_BILL_NO.Clear() : aray_id.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select id,bill_no from  IM_Btin_Data where BILL_NO <> N'جرد' and BILL_NO <> N'تسوية'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_id.Add(dr("id"))
            aray_BILL_NO.Add(dr("bill_no"))
        Loop

        For i As Integer = 0 To aray_id.Count - 1
            arr_BILL_NO = aray_BILL_NO(i).ToString
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select bill_no  From IM_Btin where BILL_NO =N'" & arr_BILL_NO & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  IM_Btin_Data where bill_no =N'" & arr_BILL_NO & "'" : cmd.ExecuteNonQuery()
            End If
        Next

    End Sub

    Public Sub Get_Chack_Double_Vendorname_Stocktaking_Delete_Sales_Bill()
        connect()

        Dim aray_id As New ArrayList
        Dim aray_BILL_NO As New ArrayList
        Dim aray_Vendorname As New ArrayList
        Dim arr_id As String = ""
        Dim arr_BILL_NO As String = ""
        Dim arr_Vendorname As String = ""
        Dim ActiveLoop As Double = 0
        '======================================================================================================================
        aray_BILL_NO.Clear() : aray_id.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select id,bill_no,Vendorname from  Sales_Bill where BILL_NO = N'جرد'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_id.Add(dr("id"))
            aray_BILL_NO.Add(dr("bill_no"))
            aray_Vendorname.Add(dr("Vendorname"))
        Loop

        For i As Integer = 0 To aray_id.Count - 1
            arr_id = aray_id(i).ToString
            arr_BILL_NO = aray_BILL_NO(i).ToString
            arr_Vendorname = aray_Vendorname(i).ToString

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from  Sales_Bill where Vendorname =N'" & arr_Vendorname & "' and bill_no =N'" & arr_BILL_NO & "'"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                ActiveLoop += 1
            Loop

            If ActiveLoop > 1 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  Sales_Bill where id =N'" & arr_id & "'" : cmd.ExecuteNonQuery()
            End If
            ActiveLoop = 0
        Next

    End Sub

    Public Sub Delete_Duplicate_Items_Transfer_Between_Stores()
        connect()

        'Dim aray_BILL_NO_From As New ArrayList
        'Dim aray_itm_id_From As New ArrayList
        'Dim aray_Stores_From As New ArrayList
        Dim aray_BILL_NO_To As New ArrayList
        Dim aray_itm_id_To As New ArrayList
        Dim aray_Stores_To As New ArrayList

        'Dim BILL_NO_From As String = ""
        'Dim itm_id_From As String = ""
        'Dim Stores_From As String = ""
        Dim BILL_NO_To As String = ""
        Dim itm_id_To As String = ""
        Dim Stores_To As String = ""

        '======================================================================================================================
        'aray_BILL_NO_From.Clear() : aray_itm_id_From.Clear() : aray_Stores_From.Clear()
        aray_BILL_NO_To.Clear() : aray_itm_id_To.Clear() : aray_Stores_To.Clear()

        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select bill_no,itm_id,Stores from  ItemsTransfer_BillsalData"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_BILL_NO_From.Add(dr("bill_no"))
        '    aray_itm_id_From.Add(dr("itm_id"))
        '    aray_Stores_From.Add(dr("Stores"))
        'Loop

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select bill_no,itm_id,Stores from  ItemsTransfer_BilltINData"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_BILL_NO_To.Add(dr("bill_no"))
            aray_itm_id_To.Add(dr("itm_id"))
            aray_Stores_To.Add(dr("Stores"))
        Loop

        For i As Integer = 0 To aray_BILL_NO_To.Count - 1
            BILL_NO_To = aray_BILL_NO_To(i).ToString
            itm_id_To = aray_itm_id_To(i).ToString
            Stores_To = aray_Stores_To(i).ToString

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from  ItemsTransfer_BillsalData where bill_no =N'" & BILL_NO_To & "' and itm_id =N'" & itm_id_To & "' and Stores =N'" & Stores_To & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  ItemsTransfer_BillsalData where bill_no =N'" & BILL_NO_To & "' and itm_id =N'" & itm_id_To & "' and Stores =N'" & Stores_To & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  ItemsTransfer_BilltINData where bill_no =N'" & BILL_NO_To & "' and itm_id =N'" & itm_id_To & "' and Stores =N'" & Stores_To & "'" : cmd.ExecuteNonQuery()
            End If
        Next

    End Sub

    Function Get_Code_Value_More_String(ByVal Table_name As String, ByVal Field_Code As String, ByVal crtria As String)
        Try
            Dim Code As String = "0"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where " & crtria
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Code = dr(0).ToString
            End If
            Return Code
        Catch ex As Exception
        End Try
    End Function

    Function Get_Code_Value_More_Double(ByVal Table_name As String, ByVal Field_Code As String, ByVal crtria As String)
        Try
            Dim Code As Double = 0
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where " & crtria
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Code = dr(0).ToString
            End If
            Return Code
        Catch ex As Exception
        End Try
    End Function

    Function Get_Code_Value(ByVal Table_name As String, ByVal Field_Code As String, ByVal Field_Name As String, ByVal Value_toChek As String)
        Try
            Dim Code As String = "0"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where " & Field_Name & " =N'" & Value_toChek & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Code = dr(0).ToString
            End If
            Return Code
        Catch ex As Exception
        End Try
    End Function

    Function Get_Code_Value_More_ALL_Select(ByVal crtria As String)
        Try
            Dim Code As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "" & crtria & ""
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Code = dr(0).ToString
            Else
                Code = 0
            End If
            Return Code
        Catch ex As Exception
        End Try
    End Function

    Public Sub GetUpdate_Company_Branch_ID_0()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Expenses set Company_Branch_ID =0" : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Vnd set Company_Branch_ID =0" : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Other_Income set Company_Branch_ID =0" : cmd.ExecuteNonQuery()
    End Sub

    Public Sub GetUpdate_MOVESDATA_AccountsTree()
        Dim aray_ID As New ArrayList
        Dim aray_MOVDNameAccount As New ArrayList
        Dim aray_MOVNumberAccount As New ArrayList
        Dim aray_MOVDNAMStatement As New ArrayList
        Dim MOVDNameAccount As String = ""
        Dim MOVNumberAccount As Double = 0
        Dim MOVDNAMStatement As String = ""

        aray_ID.Clear()
        aray_MOVDNameAccount.Clear()
        aray_MOVNumberAccount.Clear()
        aray_MOVDNAMStatement.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select ID,MOVDNameAccount,MOVNumberAccount,MOVDNAMStatement from MOVESDATA"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_ID.Add(dr("ID"))
            aray_MOVDNameAccount.Add(dr("MOVDNameAccount"))
            aray_MOVNumberAccount.Add(dr("MOVNumberAccount"))
            aray_MOVDNAMStatement.Add(dr("MOVDNAMStatement"))
        Loop
        Dim TinPriceAverage As String = ""
        For i As Integer = 0 To aray_ID.Count - 1
            MOVDNameAccount = aray_MOVDNameAccount(i).ToString()
            MOVNumberAccount = Val(aray_MOVNumberAccount(i).ToString())
            MOVDNAMStatement = aray_MOVDNAMStatement(i).ToString()

            If MOVDNameAccount = "مشتريات بضاعة" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update MOVESDATA set MOVDNameAccount =N'مشتريات',MOVDNAMStatement =N'مشتريات' where ID =N'" & aray_ID(i).ToString & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update AccountsTreeLinking set Link_AccountsTree =N'مشتريات' where Link_Statement =N'مشتريات'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update AccountsTree set ACCName =N'مشتريات' where ACCNumber =N'2213'" : cmd.ExecuteNonQuery()
            End If


            If MOVDNameAccount <> MOVDNAMStatement Then

                If MOVDNameAccount = "" Then
                    If MOVDNAMStatement = "مرتجعات مبيعات" Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update MOVESDATA set MOVDNameAccount =N'مرتجعات مبيعات' where ID =N'" & aray_ID(i).ToString & "'" : cmd.ExecuteNonQuery()
                    End If
                    GoTo 1
                End If

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update MOVESDATA set MOVDNAMStatement =N'" & MOVDNameAccount & "' where ID =N'" & aray_ID(i).ToString & "'" : cmd.ExecuteNonQuery()

            End If
1:
        Next
    End Sub

    Public Sub GetDeltet_MOVESDATA_AccountsTree()
        Dim aray_ID As New ArrayList
        Dim aray_MOVDNameAccount As New ArrayList
        Dim aray_MOVNumberAccount As New ArrayList
        Dim aray_MOVDNAMStatement As New ArrayList
        Dim aray_MOVDDebtor As New ArrayList
        Dim aray_MOVDCreditor As New ArrayList

        Dim MOVDNameAccount As String = ""
        Dim MOVNumberAccount As Double = 0
        Dim MOVDNAMStatement As String = ""

        aray_ID.Clear()
        aray_MOVDNameAccount.Clear()
        aray_MOVNumberAccount.Clear()
        aray_MOVDNAMStatement.Clear()
        aray_MOVDDebtor.Clear()
        aray_MOVDCreditor.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select ID,MOVDNameAccount,MOVNumberAccount,MOVDNAMStatement from MOVESDATA"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_ID.Add(dr("ID"))
            aray_MOVDNameAccount.Add(dr("MOVDNameAccount"))
            aray_MOVNumberAccount.Add(dr("MOVNumberAccount"))
            aray_MOVDNAMStatement.Add(dr("MOVDNAMStatement"))
            aray_MOVDDebtor.Add(dr("MOVDDebtor"))
            aray_MOVDCreditor.Add(dr("MOVDCreditor"))
        Loop
        Dim TinPriceAverage As String = ""
        For i As Integer = 0 To aray_ID.Count - 1
            MOVDNameAccount = aray_MOVDNameAccount(i).ToString()
            MOVNumberAccount = Val(aray_MOVNumberAccount(i).ToString())
            MOVDNAMStatement = aray_MOVDNAMStatement(i).ToString()

            If MOVDNameAccount = "مصروفات" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  MOVESDATA where ID =N'" & aray_ID(i).ToString & "'" : cmd.ExecuteNonQuery()
            End If

            If MOVDNameAccount = "مسحوبات جارى الشركاء" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  MOVESDATA where ID =N'" & aray_ID(i).ToString & "'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  MOVESDATA where MOVDDebtor =N'" & aray_MOVDDebtor(i).ToString & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
            End If

            If MOVDNameAccount = "ايداعات جارى الشركاء" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  MOVESDATA where ID =N'" & aray_ID(i).ToString & "'" : cmd.ExecuteNonQuery()
            End If

            If MOVDNameAccount = "الخزينة" Then

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  MOVESDATA where MOVDDebtor =N'" & aray_MOVDDebtor(i).ToString & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
            End If

        Next
    End Sub

    Public Sub GetUpdateParcodeEdit13Number()
        Try
            System.Diagnostics.Process.Start("EAN13.EXE")
        Catch ex As Exception
        End Try

        Dim aray_ID As New ArrayList
        Dim aray_itm_id As New ArrayList
        Dim aray_sname As New ArrayList

        Dim itm_id As String = ""
        Dim itm_idE2 As String = ""
        Dim sname As String = ""
        Dim itm_idEAN13Prc As String = ""
        Dim ReturnCodeEAN13Reg As String = ""
        Dim CodeEAN13Reg As String = ""

        aray_ID.Clear()
        aray_itm_id.Clear()
        aray_sname.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select id,itm_id,sname from Items"
        'cmd.CommandText = "select id,itm_id,sname from Items where group_name=N'شركة CRAWN' and TestAction IS NULL"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_ID.Add(dr("id"))
            aray_itm_id.Add(dr("itm_id"))
            aray_sname.Add(dr("sname"))
        Loop
        Dim TinPriceAverage As String = ""
        For i As Integer = 0 To aray_ID.Count - 1
            itm_id = aray_itm_id(i).ToString()
            'itm_idE2 = Val(aray_itm_id(i).ToString())

            itm_idE2 = 5 & vb.Right(Cls.GenerateItmId_Or_Parcode(), 6) & vb.Right(Cls.GenerateItmId_Or_Parcode(), 6)
            sname = Val(aray_sname(i).ToString())

            mykey.SetValue("CodeEAN13Prc", itm_idE2)
            ReturnCodeEAN13Reg = mykey.GetValue("ReturnCodeEAN13Reg", "ReturnCodeEAN13Reg")
            itm_idEAN13Prc = ReturnCodeEAN13Reg
            CodeEAN13Reg = mykey.GetValue("CodeEAN13Reg", "CodeEAN13Reg")


            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from Items where itm_id =N'" & itm_idEAN13Prc & "'" : H = cmd.ExecuteScalar
            If H > 0 Then
                MsgBox(" الباركود مسجل مسبقاً بنفس أسم المخزن", MsgBoxStyle.Exclamation)
                GoTo XX
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Items set itm_id =N'" & itm_idEAN13Prc & "',CodeEAN13Barre =N'" & CodeEAN13Reg & "',TestAction =N'1' where id =N'" & aray_ID(i).ToString & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BilltINData set itm_id = N'" & itm_idEAN13Prc & "' where itm_name =N'" & sname & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BillsalData set itm_id = N'" & itm_idEAN13Prc & "' where itm_name =N'" & sname & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update data_decayed set itm_id = N'" & itm_idEAN13Prc & "' where itm_name =N'" & sname & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal_Data set itm_id = N'" & itm_idEAN13Prc & "' where itm_name =N'" & sname & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Btin_Data set itm_id = N'" & itm_idEAN13Prc & "' where itm_name =N'" & sname & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsOffersDiscounts set itm_id = N'" & itm_idEAN13Prc & "' where sname =N'" & sname & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsTransfer set itm_id = N'" & itm_idEAN13Prc & "' where itm_name =N'" & sname & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsTransferData set itm_id = N'" & itm_idEAN13Prc & "' where itm_name =N'" & sname & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BillsalData set itm_id = N'" & itm_idEAN13Prc & "' where itm_name =N'" & sname & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Receive_BilltINData set itm_id = N'" & itm_idEAN13Prc & "' where itm_name =N'" & sname & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsTransfer_BillsalData set itm_id = N'" & itm_idEAN13Prc & "' where itm_id =N'" & itm_id & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsTransfer_BilltINData set itm_id = N'" & itm_idEAN13Prc & "' where itm_id =N'" & itm_id & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Manufacturing_BillsalData set itm_id = N'" & itm_idEAN13Prc & "' where itm_id =N'" & itm_id & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Manufacturing_BilltINData set itm_id = N'" & itm_idEAN13Prc & "' where itm_id =N'" & itm_id & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ManufacturingProductAdd set itm_id = N'" & itm_idEAN13Prc & "' where itm_id =N'" & itm_id & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into BarcodeMore(itm_id,itm_id_More)  values("
            S = S & "N'" & itm_idEAN13Prc & "',N'" & itm_idEAN13Prc & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into BarcodeMore(itm_id,itm_id_More)  values("
            S = S & "N'" & itm_idEAN13Prc & "',N'" & itm_id & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
XX:
        Next
    End Sub


End Class
