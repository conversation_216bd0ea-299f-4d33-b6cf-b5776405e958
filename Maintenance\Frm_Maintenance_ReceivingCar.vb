﻿Public Class Frm_Maintenance_ReceivingCar
    Dim CodeID As String

    Sub CLEAR_ALL()
        txtCode.Text = ""
        txtName.Text = ""
    End Sub

    Private Sub txtDriv_Address_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            txtCode.Focus()
        End If
    End Sub

    Private Sub txtDriv_Car_Color_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub BtnNew_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnNew.Click
        CLEAR_ALL()
        MAXRECORD("Maintenance_ReceivingCar", "ReceivingCar_ID")
        txtCode.Text = CodeID
    End Sub

    Private Sub BtnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAdd.Click
        If Validate_Text() = False Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Maintenance_ReceivingCar(ReceivingCar_ID,ReceivingCar) values ("
        S = S & "N'" & txtCode.Text & "',N'" & txtName.Text & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        CLEAR_ALL()
        MAXRECORD("Maintenance_ReceivingCar", "ReceivingCar_ID")
        txtCode.Text = CodeID
        Header()
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)
        txtName.Focus()
    End Sub

    Function Validate_Text() As Boolean
        If Trim(txtName.Text) = "" Then
            MsgBox("فضلاً أدخل الاسم", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtName.Focus() : Return False : Exit Function
        End If
        If Trim(txtCode.Text) = "" Then
            MsgBox("فضلاً أدخل الكود", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtCode.Focus() : Return False : Exit Function
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * From Maintenance_ReceivingCar where ReceivingCar =N'" & txtName.Text & "'"
        dr = cmd.ExecuteReader()
        If dr.HasRows = True Then
            MsgBox("عفواً يوجد نوع مسجل مسبقا بنفس الاسم", MsgBoxStyle.Exclamation)
            Return False : Exit Function
        End If
        Return True
    End Function

    Private Sub Header()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT  ReceivingCar_ID as [الكود], ReceivingCar as [الاسم] FROM  Maintenance_ReceivingCar"

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

    End Sub

    Private Sub BtnEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnEdit.Click
        If ValedateEdit() = False Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update Maintenance_ReceivingCar set "
        S = S & "ReceivingCar =N'" & txtName.Text & "',"
        S = S & "ReceivingCar_ID =N'" & txtCode.Text & "' where ReceivingCar_ID =N'" & txtCode.Text & "'"
        cmd.CommandText = S : H = cmd.ExecuteNonQuery()

        MsgBox("تم حفظ التعديل بنجاح", MsgBoxStyle.Information)
        Header()
        CLEAR_ALL()
        MAXRECORD("Maintenance_ReceivingCar", "ReceivingCar_ID")
        txtCode.Text = CodeID
    End Sub

    Function ValedateEdit() As Boolean
        If Trim(txtName.Text) = "" Then
            MsgBox("فضلاً أدخل الاسم", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtName.Focus() : Return False : Exit Function
        End If
        If Trim(txtCode.Text) = "" Then
            MsgBox("فضلاً أدخل الكود", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtCode.Focus() : Return False : Exit Function
        End If

        Return True
    End Function

    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If Trim(txtName.Text) = "" Then
            MsgBox("فضلاً ادخل الاسم", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtName.Focus()
            Exit Sub
        End If

        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete  from Maintenance_ReceivingCar where ReceivingCar =N'" & txtName.Text & "'" : cmd.ExecuteNonQuery()
        Header()
        CLEAR_ALL()
        MAXRECORD("Maintenance_ReceivingCar", "ReceivingCar_ID")
        txtCode.Text = CodeID
    End Sub

    Private Sub BtnFind_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnFind.Click
        Panel_Search.Top = 20
        Panel_Search.Dock = DockStyle.Fill
    End Sub

    Private Sub Frm_Drivers_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        MAXRECORD("Maintenance_ReceivingCar", "ReceivingCar_ID")
        txtCode.Text = CodeID
        Panel_Search.Top = 1000
        Header()
        txtName.Focus()
        txtName.SelectAll()
        If SelectTypeMaintenanceDeviceCar = "مركز صيانة السيارات" Then
            Me.Text = "ملحقات السيارة"
            lblTitel.Text = "ملحقات السيارة"
        End If
        If SelectTypeMaintenanceDeviceCar = "مركز صيانة الاجهزة" Then
            Me.Text = "ملحقات الجهاز"
            lblTitel.Text = "ملحقات الجهاز"
        End If
    End Sub

    Private Sub DataGridView1_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DataGridView1.DoubleClick
        Dim Code As String
        Code = DataGridView1.SelectedRows(0).Cells(0).Value

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT ReceivingCar_ID,ReceivingCar FROM  Maintenance_ReceivingCar WHERE ReceivingCar_ID =N'" & Code & "'" : dr = cmd.ExecuteReader : dr.Read()

        txtCode.Text = dr(0) : txtName.Text = dr(1).ToString()

        Panel_Search.Dock = DockStyle.None
        Panel_Search.Top = 1000
    End Sub

    Private Sub btnBack_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBack.Click
        Panel_Search.Dock = DockStyle.None
        Panel_Search.Top = 1000
    End Sub

    Private Sub txtSearch_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearch.TextChanged
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If txtSearch.Text = "" Then
            S = "SELECT  ReceivingCar_ID as [الكود], ReceivingCar as [الاسم] FROM  Maintenance_ReceivingCar"
        Else
            S = "SELECT  ReceivingCar_ID as [الكود], ReceivingCar as [الاسم] FROM  Maintenance_ReceivingCar where ReceivingCar Like N'%" & txtSearch.Text & "%' or ReceivingCar_ID Like N'%" & txtSearch.Text & "%'"
        End If
        S = S & " order by 1"
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

    End Sub

    Private Sub MAXRECORD(ByVal Table As String, ByVal Felds As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " + Table + ""
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            CodeID = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" + Felds + " As float)) as mb FROM " + Table + " where " + Felds + " <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            CodeID = sh + 1
        End If

    End Sub

    Private Sub txtCode_TextChanged(sender As Object, e As EventArgs) Handles txtCode.TextChanged
        MyVars.CheckNumber(txtCode)
    End Sub
End Class