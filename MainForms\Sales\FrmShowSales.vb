﻿Imports vb = Microsoft.VisualBasic
Imports CrystalDecisions.CrystalReports.Engine
Imports System.IO
Imports System.Data.SqlClient
Imports ThoughtWorks.QRCode.Codec

Public Class FrmShowSales
    Dim WithEvents BS As New BindingSource
    Dim Cls_Altfiqith As New Class_Altfiqith
    Dim ItmID As String
    Dim Vendor As String
    Dim ColorWithItems As String = mykey.GetValue("UseColorWithItems", "")
    Dim ShowTax As String = mykey.GetValue("SalesTax", "0")
    Dim SalesPricePublic As String = mykey.GetValue("SalesPricePublic", "NO")
    Dim PriceMedium As String = ""
    Dim PriceSmall As String = ""
    Dim StoreCarton As Double = 0
    Dim billno As String = ""
    Dim bill_NoTax As String = ""
    Dim Result_Code As String
    Dim totalpricebeforedisc, disc, totalpriceafterdisc, BEY, STAYING, VstPay, Vst_discPay As Double
    Dim SalesTax As String = ""
    Dim DiscountTax As String = ""
    Dim Stat As String = ""
    Dim BillNumber As String
    Dim CustomerAddress As String = ""
    Dim CustomerTel As String = ""
    Dim PhoneEmployee As String = ""
    Dim AmountDebitCreditPrevious, AmountDebitCreditAfter As String
    Dim txtCustAddress, txtCusttel1, txtCustMobile, txtCustApartment, txtCustRole, txtCustRegion, txtCustMark As String
    Dim CustPayAllBill As Boolean = False
    Private Sub GetData()
        Try
            GetReturnProfit()

            ' تحديد نوع الاستعلام حسب النوع المحدد
            Dim baseQuery As String = ""
            Dim salesBillPrefix As String = ""

            If rdoDismissalNotice.Checked = True Then
                salesBillPrefix = "dbo.Sales_Bill."
                baseQuery = "SELECT CAST(dbo.Sales_Bill.bill_no AS float) AS [رقم الفاتورة], dbo.Sales_Bill.Vendorname As [اسم العميل], dbo.Sales_Bill.bill_date As [تاريخ الفاتورة], dbo.Sales_Bill.billtime As [وقت الفاتورة], dbo.Sales_Bill.totalpricebeforedisc As [القيمة قبل الخصم], dbo.Sales_Bill.disc AS الخصم, dbo.Sales_Bill.totalpriceafterdisc As [القيمة بعد الخصم], dbo.Sales_Bill.BEY As المدفوع, dbo.Sales_Bill.STAYING As الباقى, dbo.Sales_Bill.Stat As الحالة, dbo.Sales_Bill.Notes As ملاحظات, dbo.Sales_Bill.Sheft_Number AS الشيفت, dbo.Sales_Bill.EmpName As المندوب, SUM(dbo.BillsalData.Profits) As الربح, dbo.Customers.addr As العنوان, dbo.Customers.tel1 As التليفون, dbo.Sales_Bill.id, dbo.Sales_Bill.DeliveryService As [خدمة التوصيل], dbo.Sales_Bill.ExpensesBill As المصروفات, dbo.Sales_Bill.ValueVAT As VAT, dbo.Sales_Bill.bill_NoTax, dbo.Sales_Bill.disc_type As [معدل أعمار الديون], SUM(dbo.BillsalData.DiscountsValue) As [خصم الصنف], dbo.Sales_Bill.CommercialIndustrialProfitsTax As [ضريبة ارباح ت ص], dbo.Sales_Bill.PendingBill As [فاتورة معلقة] FROM dbo.Sales_Bill INNER JOIN dbo.BillsalData ON dbo.Sales_Bill.bill_No = dbo.BillsalData.bill_no LEFT OUTER JOIN dbo.Customers ON dbo.Sales_Bill.Vendorname = dbo.Customers.Vendorname GROUP BY dbo.Sales_Bill.bill_No, dbo.Sales_Bill.Vendorname, dbo.Sales_Bill.bill_date, dbo.Sales_Bill.billtime, dbo.Sales_Bill.totalpricebeforedisc, dbo.Sales_Bill.disc, dbo.Sales_Bill.totalpriceafterdisc, dbo.Sales_Bill.BEY, dbo.Sales_Bill.STAYING, dbo.Sales_Bill.Stat, dbo.Sales_Bill.Notes, dbo.Sales_Bill.Sheft_Number, dbo.Sales_Bill.EmpName, dbo.Customers.addr, dbo.Customers.tel1, dbo.Sales_Bill.id, dbo.Sales_Bill.DeliveryService, dbo.Sales_Bill.ExpensesBill, dbo.Sales_Bill.ValueVAT, dbo.Sales_Bill.bill_NoTax, dbo.Sales_Bill.disc_type, dbo.Sales_Bill.CommercialIndustrialProfitsTax, dbo.Sales_Bill.PendingBill HAVING (dbo.Sales_Bill.bill_No <> @excludeValue)"
            ElseIf rdoReceivingPermission.Checked = True Then
                salesBillPrefix = "dbo.Receive_Sales_Bill."
                baseQuery = "SELECT CAST(dbo.Receive_Sales_Bill.bill_no As float) As [رقم الفاتورة], dbo.Receive_Sales_Bill.Vendorname As [اسم العميل], dbo.Receive_Sales_Bill.bill_date As [تاريخ الفاتورة], dbo.Receive_Sales_Bill.billtime As [وقت الفاتورة], dbo.Receive_Sales_Bill.totalpricebeforedisc AS [القيمة قبل الخصم], dbo.Receive_Sales_Bill.disc As الخصم, dbo.Receive_Sales_Bill.totalpriceafterdisc As [القيمة بعد الخصم], dbo.Receive_Sales_Bill.BEY As المدفوع, dbo.Receive_Sales_Bill.STAYING AS الباقى, dbo.Receive_Sales_Bill.Stat As الحالة, dbo.Receive_Sales_Bill.Notes As ملاحظات, dbo.Receive_Sales_Bill.Sheft_Number As الشيفت, dbo.Receive_Sales_Bill.EmpName As المندوب, SUM(dbo.Receive_BillsalData.Profits) As الربح, dbo.Customers.addr As العنوان, dbo.Customers.tel1 As التليفون, dbo.Receive_Sales_Bill.id, dbo.Receive_Sales_Bill.DeliveryService As [خدمة التوصيل], dbo.Receive_Sales_Bill.ExpensesBill AS المصروفات, dbo.Receive_Sales_Bill.ValueVAT As VAT, dbo.Receive_Sales_Bill.bill_NoTax, dbo.Receive_Sales_Bill.disc_type As [معدل أعمار الديون], dbo.Receive_BillsalData.DiscountsValue AS [خصم الصنف], dbo.Receive_Sales_Bill.CommercialIndustrialProfitsTax As [ضريبة ارباح ت ص], dbo.Receive_Sales_Bill.PendingBill AS [فاتورة معلقة] FROM dbo.Receive_Sales_Bill LEFT OUTER JOIN dbo.Receive_BillsalData ON dbo.Receive_Sales_Bill.bill_No = dbo.Receive_BillsalData.bill_no LEFT OUTER JOIN dbo.Customers ON dbo.Receive_Sales_Bill.Vendorname = dbo.Customers.Vendorname GROUP BY dbo.Receive_Sales_Bill.bill_No, dbo.Receive_Sales_Bill.Vendorname, dbo.Receive_Sales_Bill.bill_date, dbo.Receive_Sales_Bill.billtime, dbo.Receive_Sales_Bill.totalpricebeforedisc, dbo.Receive_Sales_Bill.disc, dbo.Receive_Sales_Bill.totalpriceafterdisc, dbo.Receive_Sales_Bill.BEY, dbo.Receive_Sales_Bill.STAYING, dbo.Receive_Sales_Bill.Stat, dbo.Receive_Sales_Bill.Notes, dbo.Receive_Sales_Bill.Sheft_Number, dbo.Receive_Sales_Bill.EmpName, dbo.Receive_Sales_Bill.id, dbo.Customers.addr, dbo.Customers.tel1, dbo.Receive_Sales_Bill.DeliveryService, dbo.Receive_Sales_Bill.ExpensesBill, dbo.Receive_Sales_Bill.ValueVAT, dbo.Receive_Sales_Bill.bill_NoTax, dbo.Receive_BillsalData.DiscountsValue, dbo.Receive_Sales_Bill.CommercialIndustrialProfitsTax, dbo.Receive_Sales_Bill.disc_type, dbo.Receive_Sales_Bill.PendingBill HAVING (dbo.Receive_Sales_Bill.bill_No <> @excludeValue)"
            End If

            ' بناء الاستعلام الآمن باستخدام SafeQueryBuilder
            Dim queryBuilder As New SafeQueryBuilder(baseQuery)

            ' إضافة قيمة الاستبعاد الأساسية
            queryBuilder.AddCustomCondition("", New Dictionary(Of String, Object) From {{"@excludeValue", "جرد"}})

            ' إضافة شروط البحث الآمنة
            If ChbAll.Checked = False Then
                queryBuilder.AddWhereCondition($"{salesBillPrefix}Vendorname", Cmbvendorname.Text.Trim())
                queryBuilder.AddWhereCondition($"{salesBillPrefix}BILL_NO", txtbillnoSearch.Text.Trim())
                queryBuilder.AddWhereCondition($"{salesBillPrefix}Notes", txtNotesView.Text.Trim())
            End If

            ' شروط الموظفين
            If chkEmployees.Checked = False Then
                queryBuilder.AddWhereCondition($"{salesBillPrefix}EmpName", cmbEmployees.Text.Trim())
            End If

            If Not String.IsNullOrEmpty(cmbEmployees2.Text.Trim()) Then
                queryBuilder.AddWhereCondition($"{salesBillPrefix}EmpName", cmbEmployees2.Text.Trim())
            End If

            ' شروط حالة الدفع
            If chkAll.Checked = False Then
                Dim paymentStatuses As New List(Of Object)
                If ChkCash.Checked Then paymentStatuses.Add("نقداً")
                If ChkState.Checked Then paymentStatuses.Add("آجل")
                If chkVisa.Checked Then paymentStatuses.Add("فيزا")

                If paymentStatuses.Count > 0 Then
                    queryBuilder.AddInCondition($"{salesBillPrefix}Stat", paymentStatuses)
                End If
            End If

            ' شرط الضريبة
            If chkSalesTaxView.Checked Then
                queryBuilder.AddWhereCondition($"{salesBillPrefix}bill_NoTax", "0")
            End If

            ' شروط التاريخ
            If ChkWithoutDate.Checked = False Then
                Dim fromDate As Date? = Cls.C_date(DateTimePicker1.Text)
                Dim toDate As Date? = Cls.C_date(DateTimePicker2.Text)
                queryBuilder.AddDateRangeCondition($"{salesBillPrefix}bill_date", fromDate, toDate)
            End If

            ' شروط الوقت
            If chkTime.Checked = False Then
                Dim timeFrom As String = Cls.SlpitTime24Hour(dtpTime1.Text)
                Dim timeTo As String = Cls.SlpitTime24Hour(dtpTime2.Text)
                queryBuilder.AddCustomCondition($"{salesBillPrefix}billtime BETWEEN @timeFrom AND @timeTo",
                    New Dictionary(Of String, Object) From {
                        {"@timeFrom", timeFrom},
                        {"@timeTo", timeTo}
                    })
            End If

            ' شرط الفواتير المعلقة
            If chkPendingBill.Checked Then
                queryBuilder.AddWhereCondition($"{salesBillPrefix}PendingBill", "1")
            Else
                queryBuilder.AddWhereCondition($"{salesBillPrefix}PendingBill", "0")
            End If

            ' إضافة ترتيب النتائج
            Dim finalQuery As String = queryBuilder.Build()
            Select Case FilterSelect
                Case "Number"
                    finalQuery += " ORDER BY [رقم الفاتورة]"
                Case "Date"
                    finalQuery += " ORDER BY [تاريخ الفاتورة]"
                Case "Name"
                    finalQuery += " ORDER BY [اسم العميل]"
            End Select

            ' تنفيذ الاستعلام الآمن
            Dim result As DataTable = SecureDatabaseManager.Instance.ExecuteQuery(finalQuery, queryBuilder.Parameters)
            DataGridView1.DataSource = result

            SumDGV()

            ' تنسيق التواريخ
            For i As Integer = 0 To DataGridView1.RowCount - 1
                If DataGridView1.Rows(i).Cells(2).Value IsNot Nothing Then
                    Dim dateValue As String = Val(DataGridView1.Rows(i).Cells(2).Value.ToString())
                    DataGridView1.Rows(i).Cells(2).Value = Cls.R_date(dateValue)
                End If
            Next

            ' تنسيق عرض الأعمدة
            SetColumnWidths()

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في تحميل بيانات المبيعات", ex)
            MessageBox.Show("حدث خطأ في تحميل البيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تنسيق عرض الأعمدة
    ''' </summary>
    Private Sub SetColumnWidths()
        Try
            If DataGridView1.Columns.Count > 0 Then
                ' تحديد عرض الأعمدة
                DataGridView1.Columns(0).Width = 75   ' رقم الفاتورة
                DataGridView1.Columns(1).Width = 140  ' اسم العميل
                DataGridView1.Columns(2).Width = 85   ' تاريخ الفاتورة
                DataGridView1.Columns(3).Width = 85   ' وقت الفاتورة
                DataGridView1.Columns(4).Width = 75   ' القيمة قبل الخصم
                DataGridView1.Columns(5).Width = 75   ' الخصم
                DataGridView1.Columns(6).Width = 75   ' القيمة بعد الخصم
                DataGridView1.Columns(7).Width = 75   ' المدفوع
                DataGridView1.Columns(8).Width = 75   ' الباقي
                DataGridView1.Columns(9).Width = 75   ' الحالة
                DataGridView1.Columns(13).Width = 75  ' الربح
                DataGridView1.Columns(19).Width = 75  ' VAT

                ' إظهار/إخفاء عمود الضريبة
                DataGridView1.Columns(19).Visible = (ShowValueVAT = "YES")

                ' إخفاء الأعمدة غير المطلوبة
                DataGridView1.Columns(11).Visible = False
                DataGridView1.Columns(12).Visible = False
                DataGridView1.Columns(14).Visible = False
                DataGridView1.Columns(15).Visible = False
                DataGridView1.Columns(16).Visible = False
                DataGridView1.Columns(17).Visible = False
                DataGridView1.Columns(20).Visible = False

                ' إعدادات الصلاحيات
                If PermtionName = "مدير" Then
                    DataGridView1.Columns(13).Visible = True  ' عمود الربح
                    DataGridView1.Columns(12).Visible = False
                Else
                    DataGridView1.Columns(13).Visible = False
                End If

                ' إعدادات عرض التوصيل
                If chkOrderDelivery.Checked Then
                    ' إخفاء أعمدة غير مطلوبة في وضع التوصيل
                    DataGridView1.Columns(3).Visible = False
                    DataGridView1.Columns(4).Visible = False
                    DataGridView1.Columns(5).Visible = False
                    DataGridView1.Columns(7).Visible = False
                    DataGridView1.Columns(8).Visible = False
                    DataGridView1.Columns(9).Visible = False
                    DataGridView1.Columns(13).Visible = False
                    DataGridView1.Columns(16).Visible = False
                    DataGridView1.Columns(18).Visible = False

                    ' إظهار أعمدة التوصيل
                    DataGridView1.Columns(14).Visible = True
                    DataGridView1.Columns(15).Visible = True

                    ' تحديد عرض أعمدة التوصيل
                    DataGridView1.Columns(10).Width = 90
                    DataGridView1.Columns(14).Width = 120
                    DataGridView1.Columns(15).Width = 90
                    DataGridView1.Columns(18).Width = 90
                Else
                    ' الوضع العادي - إظهار جميع الأعمدة
                    DataGridView1.Columns(3).Visible = True
                    DataGridView1.Columns(4).Visible = True
                    DataGridView1.Columns(5).Visible = True
                    DataGridView1.Columns(7).Visible = True
                    DataGridView1.Columns(8).Visible = True
                    DataGridView1.Columns(9).Visible = True
                    DataGridView1.Columns(10).Visible = True
                End If

                ' حساب معدل أعمار الديون للعملاء (للمبيعات فقط)
                If rdoDismissalNotice.Checked Then
                    CalculateDebtAge()
                End If
            End If

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في تنسيق عرض الأعمدة", ex)
        End Try
    End Sub

    ''' <summary>
    ''' حساب معدل أعمار الديون للعملاء
    ''' </summary>
    Private Sub CalculateDebtAge()
        Try
            For i As Integer = 0 To DataGridView1.Rows.Count - 1
                If DataGridView1.Rows(i).Cells(2).Value IsNot Nothing Then
                    Dim billDate As DateTime = Convert.ToDateTime(DataGridView1.Rows(i).Cells(2).Value)
                    Dim currentDate As DateTime = DateTime.Now
                    Dim daysDifference As Integer = (currentDate - billDate).Days

                    If daysDifference >= 0 Then
                        DataGridView1.Rows(i).Cells(21).Value = daysDifference
                    End If
                End If
            Next

            ' حساب إجمالي معدل أعمار الديون
            Dim totalDebtAge As Double = 0
            For i As Integer = 0 To DataGridView1.Rows.Count - 1
                If DataGridView1.Rows(i).Cells(21).Value IsNot Nothing Then
                    totalDebtAge += Val(DataGridView1.Rows(i).Cells(21).Value.ToString())
                End If
            Next

            txtTotalDebitAverageRate.Text = totalDebtAge.ToString()

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في حساب معدل أعمار الديون", ex)
            txtTotalDebitAverageRate.Text = "0"
        End Try
    End Sub

    Private Sub GetDetails()
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim ItmID As String = ""

        ItmID = DataGridView1.SelectedRows(0).Cells(0).Value

        Panel2.Top = 80
        Panel2.Dock = DockStyle.Fill

        Dim BillsalData As String = ""
        If rdoDismissalNotice.Checked = True Then
            BillsalData = "BillsalData"
            btnEnterStore.Visible = False
        End If
        If rdoReceivingPermission.Checked = True Then
            BillsalData = "Receive_BillsalData"
            btnEnterStore.Visible = True
        End If

        Try
            ' استخدام SecureDatabaseManager للاستعلام الآمن
            Dim query As String
            If ColorWithItems = "" Then
                query = "SELECT itm_id as [الباركود],itm_cat as [المجموعة] ,itm_name as [اسم الصنف],price as [السعر] ,qu_unity as [الكمية],itm_Unity as [الوحدة] ,totalprice as [الأجمالي],Stores as [المخزن],itm_Notes as [itm_Notes],Discounts as [خصم البيع],DiscountsValue as [قيمة الخصم],StateDisc as [نوع الخصم],Discount_Price_After as [السعر بعد الخصم],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],Profits as [الربح],DiscountsTin as [خصم الشراء] FROM " & BillsalData & " WHERE bill_no = @billNo ORDER BY itm_name"
            Else
                query = "SELECT itm_id as [الباركود],itm_cat as [المجموعة] ,itm_name as [اسم الصنف],price as [السعر] ,qu_unity as [الكمية],itm_Unity as [الوحدة] ,totalprice as [الأجمالي],Stores as [المخزن],itm_Notes as [" & ColorWithItems & "],Discounts as [خصم البيع],DiscountsValue as [قيمة الخصم],StateDisc as [نوع الخصم],Discount_Price_After as [السعر بعد الخصم],TinPrice as [سعر الشراء],TinPriceAverage as [متوسط سعر الشراء],Profits as [الربح],DiscountsTin as [خصم الشراء] FROM " & BillsalData & " WHERE bill_no = @billNo ORDER BY itm_name"
            End If

            Dim parameters As New Dictionary(Of String, Object) From {
                {"@billNo", ItmID}
            }

            Dim result As DataTable = SecureDatabaseManager.Instance.ExecuteQuery(query, parameters)
            DataGridView2.DataSource = result

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في تحميل بيانات الفاتورة", ex, $"BillNo: {ItmID}")
            MessageBox.Show("حدث خطأ في تحميل بيانات الفاتورة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try

        DataGridView2.Columns(0).Width = 75
        DataGridView2.Columns(1).Width = 75
        DataGridView2.Columns(2).Width = 100
        DataGridView2.Columns(3).Width = 65
        DataGridView2.Columns(4).Width = 65
        DataGridView2.Columns(5).Width = 65
        DataGridView2.Columns(6).Width = 65
        DataGridView2.Columns(7).Width = 65
        DataGridView2.Columns(8).Visible = False
        'DataGridView2.Columns(9).Visible = False
        DataGridView2.Columns(12).Visible = False
        If ColorWithItems <> "" Then
            DataGridView2.Columns(9).Visible = False
            DataGridView2.Columns(10).Visible = False
        End If
        If PermtionName = "مدير" Then
            DataGridView2.Columns(13).Visible = True
            DataGridView2.Columns(14).Visible = True
            DataGridView2.Columns(15).Visible = True
            DataGridView2.Columns(16).Visible = True
        Else
            DataGridView2.Columns(13).Visible = False
            DataGridView2.Columns(14).Visible = False
            DataGridView2.Columns(15).Visible = False
            DataGridView2.Columns(16).Visible = False
        End If
        TxtTotal.Text = DataGridView1.SelectedRows(0).Cells(6).Value
        TxtDisc.Text = DataGridView1.SelectedRows(0).Cells(5).Value
        TxtTotalAfterdisc.Text = DataGridView1.SelectedRows(0).Cells(6).Value

        Dim ItemDiscount As Double
        Dim TotalCountItems As Integer
        For i As Integer = 0 To DataGridView2.RowCount - 1
            If ColorWithItems <> "" Then
                ItemDiscount = ItemDiscount + Val(DataGridView2.Rows(i).Cells(10).Value.ToString)
            Else
                ItemDiscount = ItemDiscount + Val(DataGridView2.Rows(i).Cells(10).Value.ToString)
            End If
            TotalCountItems += DataGridView2.Rows(i).Cells(4).Value
        Next
        lblTotaIItemDiscount.Text = ItemDiscount
        lblTotalCountItems.Text = TotalCountItems

        insertxt()

        ShowPhotoBill()

        txtNumberItems.Text = DataGridView2.RowCount
    End Sub

    Private Sub SumDGV()
        Dim SM As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM = SM + Val(DataGridView1.Rows(i).Cells(4).Value.ToString)
        Next
        txttotalpricebefor.Text = SM

        Dim SM2 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM2 = SM2 + Val(DataGridView1.Rows(i).Cells(5).Value.ToString)
        Next
        txttotaldisc.Text = SM2

        Dim SM3 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM3 = SM3 + Val(DataGridView1.Rows(i).Cells(6).Value.ToString)
        Next
        txttotalpriceafter.Text = SM3

        Dim SM4 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM4 = SM4 + Val(DataGridView1.Rows(i).Cells(7).Value.ToString)
        Next
        txtpaying.Text = SM4

        Dim SM5 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM5 = SM5 + Val(DataGridView1.Rows(i).Cells(8).Value.ToString)
        Next
        txtstaying.Text = SM5

        Dim SM6 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM6 = SM6 + Val(DataGridView1.Rows(i).Cells(13).Value.ToString)
        Next
        txtProfitsBill.Text = Math.Round(SM6, 2)

        txtProfitsBill.Text = Val(txtProfitsBill.Text) - Val(txtReturnProfitsBill.Text)

        Dim SM7 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM7 = SM7 + Val(DataGridView1.Rows(i).Cells(19).Value.ToString)
        Next
        txtValueVAT.Text = Math.Round(SM7, 2)

        Dim SM8 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM8 = SM8 + Val(DataGridView1.Rows(i).Cells(22).Value.ToString)
        Next
        txtTotalDiscountsValue.Text = Math.Round(SM8, 2)

        Dim SM9 As Double
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM9 = Val(DataGridView1.Rows(i).Cells(13).Value.ToString)
            DataGridView1.Rows(i).Cells(13).Value = Math.Round(SM9, 2)
        Next

        Dim SM10 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM10 = SM10 + Val(DataGridView1.Rows(i).Cells(23).Value.ToString)
        Next
        txtCommercialAndIndustrialProfitsTax.Text = Math.Round(SM10, 2)
    End Sub

    Private Sub DateTimePicker1_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        GetData()
    End Sub

    Private Sub DateTimePicker2_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        GetData()
    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        Panel2.Top = 1000
        Panel2.Dock = DockStyle.None
        Pic_Bill.Image = Nothing
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False

        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub BtnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnShow.Click
        GetSerialNumber()
        GetData()
    End Sub

    Private Sub FrmShowImport_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", Cmbvendorname)
        End If
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployees)
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployees2)
        Cmbvendorname.Items.Add("نقداً")

        Panel2.Top = 10000
        PanelEmployees.Location = New System.Drawing.Point(622, 196)
        PanelEmployees.Size = New System.Drawing.Size(445, 149)
        PanelEmployees.Top = 10000

        PanelCust_Pay.Location = New System.Drawing.Point(158, 129)
        PanelCust_Pay.Size = New System.Drawing.Size(479, 337)
        PanelCust_Pay.Top = 5000
        btnDubbileClickGrid.Top = 5000
        GetData()
        If ShowValueVAT = "YES" Then
            txtValueVAT.Visible = True
            lblValueVAT.Visible = True
        Else
            txtValueVAT.Visible = False
            lblValueVAT.Visible = False
        End If
        If PermtionName <> "مدير" Then
            Label30.Visible = False
            txtProfitsBill.Visible = False
        End If

        PanelViewImage.Dock = DockStyle.None
        PanelViewImage.Top = 5000

        If DealingWithSerialItems = "YES" Then
            lblSerialNumber.Visible = True
            txtSerialNumber.Visible = True
        End If
    End Sub

    Private Sub DataGridView1_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles DataGridView1.DoubleClick
        GetDetails()
    End Sub

    Sub ForPrintAll(ByVal itm_id As String, ByVal itm_cat As String, ByVal itm_name As String, ByVal price As String, ByVal qu As String, ByVal totalprice As String, ByVal stors As String,
                    ByVal BILL_NO As String, ByVal Vendorname As String, ByVal bill_date As String, ByVal billtime As String,
                    ByVal totalpricebeforedisc As String, ByVal disc As String, ByVal totalpriceafterdisc As String, ByVal BEY As String, ByVal STAYING As String)


        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "Sp_PrintSalesPurchases"
        cmd.Parameters.Clear()


        cmd.Parameters.AddWithValue("@itm_id", itm_id)
        cmd.Parameters.AddWithValue("@itm_cat", itm_cat)
        cmd.Parameters.AddWithValue("@itm_name", itm_name)
        cmd.Parameters.AddWithValue("@price", price)
        cmd.Parameters.AddWithValue("@qu", qu)
        cmd.Parameters.AddWithValue("@totalprice", totalprice)
        cmd.Parameters.AddWithValue("@store", stors)
        cmd.Parameters.AddWithValue("@BILL_NO", BILL_NO)
        cmd.Parameters.AddWithValue("@Vendorname", Vendorname)
        cmd.Parameters.AddWithValue("@bill_date", bill_date)
        cmd.Parameters.AddWithValue("@billtime", billtime)
        cmd.Parameters.AddWithValue("@totalpricebeforedisc", totalpricebeforedisc)
        cmd.Parameters.AddWithValue("@disc", disc)
        cmd.Parameters.AddWithValue("@totalpriceafterdisc", totalpriceafterdisc)
        cmd.Parameters.AddWithValue("@BEY", BEY)
        cmd.Parameters.AddWithValue("@STAYING", STAYING)

        cmd.ExecuteNonQuery()

    End Sub

    Private Sub Cmbvendorname_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Cmbvendorname.SelectedIndexChanged
        GetData()
    End Sub

    Private Sub ChbAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChbAll.CheckedChanged
        If ChbAll.Checked = True Then
            Cmbvendorname.Enabled = False
            txtbillnoSearch.Enabled = False
            txtNotesView.Enabled = False
            Cmbvendorname.SelectedIndex = -1
        ElseIf ChbAll.Checked = False Then
            Cmbvendorname.Enabled = True
            txtbillnoSearch.Enabled = True
            txtNotesView.Enabled = True
        End If
        GetData()
    End Sub

    Sub InsertForPrint(ByVal BILL_NO As String, ByVal Vendorname As String, ByVal bill_date As String, ByVal billtime As String,
                       ByVal totalpricebeforedisc As String, ByVal disc As String, ByVal totalpriceafterdisc As String, ByVal BEY As String,
                       ByVal STAYING As String, ByVal Stat As String, ByVal TotalBeforeDisc As String, ByVal TotalDisc As String,
                       ByVal TotalafterDisc As String, ByVal TotalBay As String, ByVal Totalstaying As String)

        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "SP_PrintDaySalesAll"
        cmd.Parameters.Clear()

        cmd.Parameters.AddWithValue("@BILL_NO", BILL_NO)
        cmd.Parameters.AddWithValue("@Vendorname", Vendorname)
        cmd.Parameters.AddWithValue("@bill_date", bill_date)
        cmd.Parameters.AddWithValue("@billtime", billtime)
        cmd.Parameters.AddWithValue("@totalpricebeforedisc", totalpricebeforedisc)
        cmd.Parameters.AddWithValue("@disc", disc)
        cmd.Parameters.AddWithValue("@totalpriceafterdisc", totalpriceafterdisc)
        cmd.Parameters.AddWithValue("@BEY", BEY)
        cmd.Parameters.AddWithValue("@STAYING", STAYING)
        cmd.Parameters.AddWithValue("@Stat", Stat)
        cmd.Parameters.AddWithValue("@TotalBeforeDisc", TotalBeforeDisc)
        cmd.Parameters.AddWithValue("@TotalDisc", TotalDisc)
        cmd.Parameters.AddWithValue("@TotalafterDisc", TotalafterDisc)
        cmd.Parameters.AddWithValue("@TotalBay", TotalBay)
        cmd.Parameters.AddWithValue("@Totalstaying", Totalstaying)

        cmd.ExecuteNonQuery()

    End Sub

    Sub insertxt()

        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        ItmID = DataGridView1.SelectedRows(0).Cells(0).Value
        Vendor = DataGridView1.SelectedRows(0).Cells(1).Value

        Dim Sales_Bill As String = ""
        If rdoDismissalNotice.Checked = True Then
            Sales_Bill = "Sales_Bill"
        End If
        If rdoReceivingPermission.Checked = True Then
            Sales_Bill = "Receive_Sales_Bill"
        End If

        Try
            ' استعلام آمن للحصول على تفاصيل الفاتورة
            Dim query As String = $"SELECT * FROM {Sales_Bill} WHERE BILL_NO = @billNo"
            Dim parameters As New Dictionary(Of String, Object) From {
                {"@billNo", ItmID}
            }

            Dim result As DataTable = SecureDatabaseManager.Instance.ExecuteQuery(query, parameters)
            If result.Rows.Count > 0 Then
                Dim row As DataRow = result.Rows(0)

                ' تعبئة الحقول من البيانات المسترجعة
                txtbillno.Text = row("BILL_NO").ToString()
                txtvendores.Text = row("Vendorname").ToString()
                txtbilldate.Text = Cls.R_date(row("bill_date"))
                txtbilltime.Text = row("billtime").ToString()
                txtTimeAMBM.Text = If(row("BillTimeAmBm") IsNot DBNull.Value, row("BillTimeAmBm").ToString(), "")
                txttotalpeforedisc.Text = row("totalpricebeforedisc").ToString()
                txtdisc1.Text = row("disc").ToString()
                txttotalafterdisc1.Text = row("totalpriceafterdisc").ToString()
                txtpaying1.Text = row("BEY").ToString()
                txt_staying1.Text = row("STAYING").ToString()
                txt_staying2.Text = row("STAYING").ToString()
                txtSalesTax.Text = If(row("SalesTax") IsNot DBNull.Value, row("SalesTax").ToString(), "")
                txtNotes.Text = If(row("Notes") IsNot DBNull.Value, row("Notes").ToString(), "")
                txtDiscountTax.Text = If(row("DiscountTax") IsNot DBNull.Value, row("DiscountTax").ToString(), "")
                txtbill_NoTax.Text = If(row("bill_NoTax") IsNot DBNull.Value, row("bill_NoTax").ToString(), "")
                txtCreditPrevious.Text = If(row("CreditPrevious") IsNot DBNull.Value, row("CreditPrevious").ToString(), "")
                txtDebitPrevious.Text = If(row("DebitPrevious") IsNot DBNull.Value, row("DebitPrevious").ToString(), "")
                lblCreditCurrent.Text = If(row("CreditCurrent") IsNot DBNull.Value, row("CreditCurrent").ToString(), "")
                lblDebitCurrent.Text = If(row("DebitCurrent") IsNot DBNull.Value, row("DebitCurrent").ToString(), "")
                lblTotalValueVAT.Text = If(row("ValueVAT") IsNot DBNull.Value, row("ValueVAT").ToString(), "")
                lblExpenses.Text = If(row("ExpensesBill") IsNot DBNull.Value, row("ExpensesBill").ToString(), "")
                lblEmployeesName.Text = If(row("EmpName") IsNot DBNull.Value, row("EmpName").ToString(), "")
                txtStateCash.Text = If(row("Stat") IsNot DBNull.Value, row("Stat").ToString(), "")
                lblCommercialAndIndustrialProfitsTax.Text = If(row("CommercialIndustrialProfitsTax") IsNot DBNull.Value, row("CommercialIndustrialProfitsTax").ToString(), "")
                lblDeliveryService.Text = If(row("DeliveryService") IsNot DBNull.Value, row("DeliveryService").ToString(), "")
                lblCurrentBalanceCustVnd.Text = If(row("CurrentBalanceCustVnd") IsNot DBNull.Value, row("CurrentBalanceCustVnd").ToString(), "")
                lblCurrentBalanceCustVndPrevious.Text = (Val(lblCurrentBalanceCustVnd.Text) + Val(txt_staying1.Text)).ToString()

                ' تحديد إظهار زر الدفع
                btnCust_Pay.Visible = (txt_staying1.Text <> "0")

                ' حساب الأرصدة
                AmountDebitCreditPrevious = Val(txtCreditPrevious.Text) - Val(txtDebitPrevious.Text)
                AmountDebitCreditAfter = Val(lblCreditCurrent.Text) - Val(lblDebitCurrent.Text)

                Dim CustomerBalance As String = (Val(txtCreditPrevious.Text) - Val(txtDebitPrevious.Text)).ToString()
            Else
                ' لا توجد بيانات للفاتورة
                ErrorHandler.LogWarning($"لم يتم العثور على بيانات للفاتورة رقم: {ItmID}")
                MessageBox.Show("لم يتم العثور على بيانات للفاتورة المحددة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في تحميل تفاصيل الفاتورة", ex, $"BillID: {ItmID}")
            MessageBox.Show("حدث خطأ في تحميل تفاصيل الفاتورة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
        lblTotalAccountAfterInvoice.Text = Val(CustomerBalance) + Val(TxtTotalAfterdisc.Text)

    End Sub

    Private Sub btnPrint_Click(sender As Object, e As EventArgs) Handles btnPrint.Click

        If SettingPrinterAuto = "YES" Then
            Dim DefaultPrinterBill As String = mykey.GetValue("DefaultPrinterA4", "DefaultPrinterA4")
            If Not Cls.SetDefaultSystemPrinter4(DefaultPrinterBill) Then
                MessageBox.Show("Error occured will trying to set the default printer!")
            End If
        End If

        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            S = "insert into PrintSalesPurchases(Company_Branch_ID,BILL_NO,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,BEY,STAYING,Stat,det,Unity,TotalBeforeDisc,TotalDisc,TotalafterDisc,TotalBay,Totalstaying,CustomerName,priceSal,TotalCreditor,vintinval)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView1.Rows(i).Cells(0).Value & "',N'" & DataGridView1.Rows(i).Cells(1).Value & "',N'" & DataGridView1.Rows(i).Cells(2).Value & "',N'" & DataGridView1.Rows(i).Cells(3).Value & "',N'" & DataGridView1.Rows(i).Cells(4).Value & "',N'" & DataGridView1.Rows(i).Cells(5).Value & "',N'" & DataGridView1.Rows(i).Cells(6).Value & "',N'" & DataGridView1.Rows(i).Cells(7).Value & "',N'" & DataGridView1.Rows(i).Cells(8).Value & "',N'" & DataGridView1.Rows(i).Cells(9).Value & "',N'" & DataGridView1.Rows(i).Cells(14).Value & "',N'" & DataGridView1.Rows(i).Cells(15).Value & "',N'" & txttotalpricebefor.Text & "',N'" & txttotaldisc.Text & "',N'" & txttotalpriceafter.Text & "',N'" & txtpaying.Text & "',N'" & txtstaying.Text & "',N'" & cmbEmployees.Text & "',N'" & DataGridView1.Rows(i).Cells(10).Value & "',N'" & txtValueVAT.Text & "',N'" & DataGridView1.Rows(i).Cells(19).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt
        If chkOrderDelivery.Checked = True Then
            rpt = New Rpt_DaySald_Delivery
        Else
            If ShowValueVAT = "YES" Then
                rpt = New Rpt_DaySald_VAT
            Else
                rpt = New Rpt_DaySald
            End If
        End If
        Dim txt, txtNameAr, txtNameEn As TextObject

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txt = rpt.Section1.ReportObjects("Text16")
        txt.Text = "تقرير بالمبيعات اليومية"
        txtNameAr = rpt.Section1.ReportObjects("Text2")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("Text18")
        txtNameEn.Text = NameEnCompany
        'If ShowValueVAT = "YES" Then
        '    txtValueVAT = rpt.Section1.ReportObjects("txtVAT")
        '    txtValueVAT.Text = ""
        'End If
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "تقرير بالمبيعات اليومية"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub ShowPhotoBill()
        On Error Resume Next
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim bill_Nomber As String
        bill_Nomber = DataGridView1.SelectedRows(0).Cells(0).Value

        connectionStringOpen()
        Dim sql As String
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        sql = "SELECT Image_Bill FROM Sales_Bill WHERE bill_No =N'" & bill_Nomber & "'"
        Dim cmd As SqlClient.SqlCommand = New SqlClient.SqlCommand(sql, Cn)
        Dim by() As Byte
        by = cmd.ExecuteScalar()
        If (by.Length > 0) Then
            Dim stream As New MemoryStream(by, True)
            stream.Write(by, 0, by.Length)
            Pic_Bill.Image = New Bitmap(stream)
            picBillShowImage.Image = New Bitmap(stream)
            stream.Close()
        Else
            Pic_Bill.Image = Nothing
            picBillShowImage.Image = Nothing
        End If

    End Sub

    Private Sub Button1_Click_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        'On Error Resume Next

        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView2.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        GetPrintSerialNumber()

        'Try
        Dim TotalQunt As Double
        For i As Integer = 0 To DataGridView2.RowCount - 1
            TotalQunt += Val(DataGridView2.Rows(i).Cells(4).Value)
        Next

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        Dim XTotalBill As String
        If txtpaying.Text = 0 Then
            XTotalBill = txtstaying.Text
        Else
            XTotalBill = TxtTotalAfterdisc.Text
        End If

        If ShowCustomerAddressSales = "YES" Then
            GetCustomerAddress()
        End If

        Dim STAT As String = ""
        If txtStateCash.Text = "نقداً" Then : STAT = "نقداً" : End If
        If txtStateCash.Text = "آجل" Then : STAT = "آجل" : End If
        If txtStateCash.Text = "فيزا" Then : STAT = "فيزا" : End If

        Dim taxn As String = Cls.Get_Code_Value_Branch_More("Customers", "taxn", "Vendorname =N'" & txtvendores.Text & "'")
        Dim Cust_Code As String = Cls.Get_Code_Value_Branch_More("Customers", "Cust_Code", "Vendorname =N'" & txtvendores.Text & "'")
        Vendor_Customer = Cls.Get_Code_Value_Branch_More("Vendor_Customer", "Vendorname", "Vendorname=N'" & txtvendores.Text & "'")


        Dim Xbill_No As String = ""
        If ShowTax = "0" Then
            Xbill_No = txtbillno.Text
        Else
            Xbill_No = txtbill_NoTax.Text
        End If
        Dim BillSerialNumber As Double
        Dim Xqunt As String = ""
        Dim RateDiscount As String
        Dim TotalDiscountsValue As Double
        BillSerialNumber = 0
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView2.Rows.Count - 1
            'If SalesQuantBiggestMediumSmall = "YES" Then
            '    GetMediumSmall(DataGridView2.Rows(i).Cells(0).Value, DataGridView2.Rows(i).Cells(6).Value, DataGridView2.Rows(i).Cells(4).Value)
            '    Xqunt = StoreCarton
            'Else
            '    Xqunt = DataGridView2.Rows(i).Cells(4).Value
            'End If

            If DataGridView2.Rows(i).Cells(11).Value.ToString = "نسبة" Then
                RateDiscount = "%"
            Else
                RateDiscount = "$"
            End If

            Dim TotalAfterDisc As Double
            TotalDiscountsValue += Val(DataGridView2.Rows(i).Cells(0).Value.ToString)
            If TotalDiscountsValue <> 0 Then
                TotalAfterDisc = Val(txttotalpeforedisc.Text) + lblTotaIItemDiscount.Text
            Else
                TotalAfterDisc = Val(txttotalpeforedisc.Text)
            End If

            If Vendor_Customer = txtvendores.Text Then
                txtCreditPrevious.Text = lblCurrentBalanceCustVndPrevious.Text
                lblCreditCurrent.Text = lblCurrentBalanceCustVnd.Text
            End If

            BillSerialNumber += 1
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If ColorWithItems = "" Then
                S = "insert into PrintSalesPurchases(Company_Branch_ID,itm_id,itm_cat,itm_name,price,qu,Unity,totalprice,store,det,BILL_NO,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,BEY,STAYING,UserName,Stat,TotalDisc,vnamntcredit,vnamntdebit,TotalCreditor,TotalDebtor,Recipient,Delivery_Date,KiloMeter,Supervisor_Reform,Received_Date,VnReceipts,Driv_CarNumber,vndiscount,Totalreturns,Name1,Name2,Name6,Name7,Number1,Name5,Name8,Name9,NumberInt1,TotalBeforeDisc,Name10)  values("
                S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView2.Rows(i).Cells(0).Value & "',N'" & DataGridView2.Rows(i).Cells(1).Value & "',N'" & DataGridView2.Rows(i).Cells(2).Value & "',N'" & DataGridView2.Rows(i).Cells(3).Value & "',N'" & DataGridView2.Rows(i).Cells(4).Value & "',N'" & DataGridView2.Rows(i).Cells(5).Value & "',N'" & DataGridView2.Rows(i).Cells(6).Value & "',N'" & DataGridView2.Rows(i).Cells(7).Value & "',N'" & DataGridView2.Rows(i).Cells(9).Value & "',N'" & Xbill_No & "',N'" & txtvendores.Text & "',N'" & txtbilldate.Text & "',N'" & Cls.Get_Time_AM_PM(txtbilltime.Text.ToString) & "',N'" & TotalAfterDisc & "',N'" & txtdisc1.Text & "',N'" & txttotalafterdisc1.Text & "',N'" & txtpaying1.Text & "',N'" & txt_staying1.Text & "',N'" & XTotalBill & "',N'" & txtNotes.Text & "',N'" & DataGridView2.Rows.Count & "',N'" & lblCreditCurrent.Text & "',N'" & lblDebitCurrent.Text & "',N'" & txtCreditPrevious.Text & "',N'" & txtDebitPrevious.Text & "',N'" & UserName & "',N'" & DefaultCurrencyProgram & "',N'" & lblTotalValueVAT.Text & "',N'" & lblTotaIItemDiscount.Text & "',N'" & RateDiscount & "',N'" & DataGridView2.Rows(i).Cells(12).Value & "',N'" & TotalQunt & "',N'" & lblExpenses.Text & "',N'" & lblTotalAccountAfterInvoice.Text & "',N'" & CustomerTel & "',N'" & CustomerAddress & "',N'" & STAT & "',N'" & taxn & "',N'" & Cust_Code & "',N'" & lblCommercialAndIndustrialProfitsTax.Text & "',N'" & AmountDebitCreditPrevious & "',N'" & AmountDebitCreditAfter & "',N'" & BillSerialNumber & "',N'" & BillSerialNumber & "',N'" & lblTotalCountItems.Text & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Else
                S = "insert into PrintSalesPurchases(Company_Branch_ID,itm_id,itm_cat,itm_name,price,qu,Unity,totalprice,store,VnPay,det,BILL_NO,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,BEY,STAYING,UserName,Stat,TotalDisc,vnamntcredit,vnamntdebit,TotalCreditor,TotalDebtor,Recipient,Delivery_Date,KiloMeter,Supervisor_Reform,Received_Date,VnReceipts,Driv_CarNumber,vndiscount,Totalreturns,Name1,Name2,Name6,Name7,Number1,Name5,Name8,Name9,NumberInt1,TotalBeforeDisc,Name10)  values("
                S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView2.Rows(i).Cells(0).Value & "',N'" & DataGridView2.Rows(i).Cells(1).Value & "',N'" & DataGridView2.Rows(i).Cells(2).Value & "',N'" & DataGridView2.Rows(i).Cells(3).Value & "',N'" & DataGridView2.Rows(i).Cells(4).Value & "',N'" & DataGridView2.Rows(i).Cells(5).Value & "',N'" & DataGridView2.Rows(i).Cells(6).Value & "',N'" & DataGridView2.Rows(i).Cells(7).Value & "',N'" & DataGridView2.Rows(i).Cells(8).Value & "',N'" & DataGridView2.Rows(i).Cells(9).Value & "',N'" & Xbill_No & "',N'" & txtvendores.Text & "',N'" & txtbilldate.Text & "',N'" & Cls.Get_Time_AM_PM(txtbilltime.Text.ToString) & "',N'" & TotalAfterDisc & "',N'" & txtdisc1.Text & "',N'" & txttotalafterdisc1.Text & "',N'" & txtpaying1.Text & "',N'" & txt_staying1.Text & "',N'" & XTotalBill & "',N'" & txtNotes.Text & "',N'" & DataGridView2.Rows.Count & "',N'" & lblCreditCurrent.Text & "',N'" & lblDebitCurrent.Text & "',N'" & txtCreditPrevious.Text & "',N'" & txtDebitPrevious.Text & "',N'" & UserName & "',N'" & DefaultCurrencyProgram & "',N'" & lblTotalValueVAT.Text & "',N'" & lblTotaIItemDiscount.Text & "',N'" & RateDiscount & "',N'" & DataGridView2.Rows(i).Cells(12).Value & "',N'" & TotalQunt & "',N'" & lblExpenses.Text & "',N'" & lblTotalAccountAfterInvoice.Text & "',N'" & CustomerTel & "',N'" & CustomerAddress & "',N'" & STAT & "',N'" & taxn & "',N'" & Cust_Code & "',N'" & lblCommercialAndIndustrialProfitsTax.Text & "',N'" & AmountDebitCreditPrevious & "',N'" & AmountDebitCreditAfter & "',N'" & BillSerialNumber & "',N'" & BillSerialNumber & "',N'" & lblTotalCountItems.Text & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        Next
        AddReportView()
        If ActivateElectronicBill = "YES" Then
            GetQRCode()
            ImageUpdateQRCode("PrintSalesPurchases", "Image1", "BILL_NO", txtbillno.Text)
        End If


        Dim txtNameAr, txtNameEn, txtCmpAddress, txtCmpEmail, txtCmpTel, txtCmpMobile, txtCmpFax, txtCmpUnderBILL, txtCmpAddressBill, txtCommercialRecord, txtTaxCard, txtSaleTax, txtDiscountTaxs, txtSaleTaxText, txtDiscountTaxText, txtAltfiqith, txtCMPNameDown, txtCustomerAddressSales, txtCustomerTel, txtDelegateName, txtEndorsement, txtPhoneEmployee, txtProgramNameBill, txtObjectUserName, txtObjectCommercialAndIndustrialProfitsTax, txtObjectVATRate As TextObject
        Dim txtObjectMobile, txtObjectTel, txtObjectAddress, txtObjectApartment, txtObjectRole, txtObjectRegion, txtObjectMark, txtObjectDeliveryService As TextObject

        If SettingPrinterAuto = "YES" Then
            If PrintSmall = "YES" Then

            End If
            Dim DefaultPrinterBill As String = mykey.GetValue("DefaultPrinterBill", "DefaultPrinterBill")
            If Not Cls.SetDefaultSystemPrinter4(DefaultPrinterBill) Then
                MessageBox.Show("Error occured will trying to set the default printer!")
            End If
        End If

        Cls.GetDefaultPrinterA4()

        Dim rpt
        If PrintSmall = "YES" Then
            Cls.GetDefaultPrinterBill()
            If chkOrderDelivery.Checked = True Then
                GetDataCustomerDelivery()
                rpt = New Rpt_SoldSmall_OrderDelivery
                txtObjectMobile = rpt.Section1.ReportObjects("Mobile")
                txtObjectMobile.Text = txtCustMobile
                txtObjectTel = rpt.Section1.ReportObjects("tel")
                txtObjectTel.Text = txtCusttel1
                txtObjectAddress = rpt.Section1.ReportObjects("address")
                txtObjectAddress.Text = txtCustAddress
                txtObjectApartment = rpt.Section1.ReportObjects("Apartment")
                txtObjectApartment.Text = txtCustApartment
                txtObjectRole = rpt.Section1.ReportObjects("Role")
                txtObjectRole.Text = txtCustRole
                txtObjectRegion = rpt.Section1.ReportObjects("Region")
                txtObjectRegion.Text = txtCustRegion
                txtObjectMark = rpt.Section1.ReportObjects("Mark")
                txtObjectMark.Text = txtCustMark
                txtObjectUserName = rpt.Section1.ReportObjects("UserName")
                txtObjectUserName.Text = UserName
                txtObjectDeliveryService = rpt.Section1.ReportObjects("DeliveryService")
                txtObjectDeliveryService.Text = lblDeliveryService.Text
            Else

                If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                    rpt = New Rpt_SoldSmall
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                    rpt = New Rpt_SoldSmall_2
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                    rpt = New Rpt_SoldSmall_3
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                    rpt = New Rpt_SoldSmall_4
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                    rpt = New Rpt_SoldSmall_5
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                    rpt = New Rpt_SoldSmall_6
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                    rpt = New Rpt_SoldSmall_7
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                    rpt = New Rpt_SoldSmall_8
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                    rpt = New Rpt_SoldSmall_9
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                    rpt = New Rpt_SoldSmall_10
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                    rpt = New Rpt_SoldSmall_11
                    If ShowCustomerAddressSales = "YES" Then
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                        End If
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = lblEmployeesName.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                    rpt = New Rpt_SoldSmall_12
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                    rpt = New Rpt_SoldSmall_13
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = ""
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                    rpt = New Rpt_SoldSmall_14
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                    rpt = New Rpt_SoldSmall_15
                    txtObjectUserName = rpt.Section1.ReportObjects("UserName")
                    txtObjectUserName.Text = UserName
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                    rpt = New Rpt_SoldSmall_16
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                    rpt = New Rpt_SoldSmall_17
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                    rpt = New Rpt_SoldSmall_18
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                    rpt = New Rpt_SoldSmall_19
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                    rpt = New Rpt_SoldSmall_20
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                    rpt = New Rpt_SoldSmall_10
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                    rpt = New Rpt_SoldSmall_10
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                    If ShowCustomerAddressSales = "YES" Then
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                        End If
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                    rpt = New Rpt_SoldSmall_24
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                    rpt = New Rpt_SoldSmall_10
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                    rpt = New Rpt_SoldSmall_24
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                    rpt = New Rpt_SoldSmall_24
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                    rpt = New Rpt_SoldSmall_24
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                    rpt = New Rpt_SoldSmall_24
                End If
                If SalesInvoicePrintingLanguage = "English" Then
                    rpt = New Rpt_SoldSmall_EN
                End If
                'txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
                'txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(TxtTotalAfterdisc.Text)
                'txtCMPNameDown = rpt.Section1.ReportObjects("txtCMPNameDown")
                'txtCMPNameDown.Text = CMPNameDown
                If SalesInvoicePrintingLanguage = "English" Then
                    rpt = New Rpt_SoldSmall_EN
                End If
            End If
        End If

        If PrintSmall = "NO" Then
            If ColorWithItems = "YES" Then
                rpt = New Rpt_SalesBill_4
            Else
                If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                    rpt = New Rpt_SalesBill
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                    rpt = New Rpt_SalesBill_2
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                    rpt = New Rpt_SalesBill_3
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                    rpt = New Rpt_SalesBill_4
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                    rpt = New Rpt_SalesBill_5
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                    rpt = New Rpt_SalesBill_6
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                    rpt = New Rpt_SalesBill_7
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                    rpt = New Rpt_SalesBill_8
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                    rpt = New Rpt_SalesBill_9
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                    rpt = New Rpt_SalesBill_10
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                    rpt = New Rpt_SalesBill_11
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                    rpt = New Rpt_SalesBill_Delegate
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = lblEmployeesName.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                    rpt = New Rpt_SalesBill_Delegate_2
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = lblEmployeesName.Text
                    txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                    txtCustomerAddressSales.Text = CustomerAddress
                    txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                    txtCustomerTel.Text = CustomerTel
                    txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                    txtPhoneEmployee.Text = PhoneEmployee
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                    rpt = New Rpt_SalesBill_14
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                    rpt = New Rpt_SalesBill_15
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                    rpt = New Rpt_SalesBill_16
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                    rpt = New Rpt_SalesBill_17
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                    rpt = New Rpt_SalesBill_18
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = lblEmployeesName.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                    rpt = New Rpt_SalesBill_19
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = lblEmployeesName.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                    rpt = New Rpt_SalesBill_20
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = lblEmployeesName.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                    rpt = New Rpt_SalesBill_21
                    If CommercialAndIndustrialProfitsTax = 0 Then
                        txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                        txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                    End If
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                    rpt = New Rpt_SalesBill_22
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                    rpt = New Rpt_SalesBill_23
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                    rpt = New Rpt_SalesBill_24
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                    rpt = New Rpt_SalesBill_25
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                    rpt = New Rpt_SalesBill_26
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                    rpt = New Rpt_SalesBill_27
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                    rpt = New Rpt_SalesBill_28
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                    rpt = New Rpt_SalesBill_29
                End If
            End If
            If ColorWithItems <> "" Then
                If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                    rpt = New Rpt_SalesBill
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                    rpt = New Rpt_SalesBill_2
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                    rpt = New Rpt_SalesBill_3
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                    rpt = New Rpt_SalesBill_4
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                    rpt = New Rpt_SalesBill_5
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                    rpt = New Rpt_SalesBill_6
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                    rpt = New Rpt_SalesBill_7
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                    rpt = New Rpt_SalesBill_8
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                    rpt = New Rpt_SalesBill_9
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                    rpt = New Rpt_SalesBill_10
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                    rpt = New Rpt_SalesBill_11
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                    rpt = New Rpt_SalesBill_Delegate
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                    txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                    txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                    txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                    txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                    rpt = New Rpt_SalesBill_Delegate_2
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                    txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                    txtCustomerAddressSales.Text = CustomerAddress
                    txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                    txtCustomerTel.Text = CustomerTel
                    txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                    txtPhoneEmployee.Text = PhoneEmployee
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                    rpt = New Rpt_SalesBill_14
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                    rpt = New Rpt_SalesBill_15
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                    rpt = New Rpt_SalesBill_16
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                    rpt = New Rpt_SalesBill_17
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                    rpt = New Rpt_SalesBill_18
                    If ShowCustomerAddressSales = "YES" Then
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                        End If
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                    rpt = New Rpt_SalesBill_19
                    If ShowCustomerAddressSales = "YES" Then
                        If CustomerAddress <> "" Then
                            txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                            txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                            txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                            txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                        End If
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                    rpt = New Rpt_SalesBill_20
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                    End If
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                    rpt = New Rpt_SalesBill_21
                    If CommercialAndIndustrialProfitsTax = 0 Then
                        txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                        txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                    End If
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                    rpt = New Rpt_SalesBill_22
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                    rpt = New Rpt_SalesBill_23
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                    rpt = New Rpt_SalesBill_24
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                    rpt = New Rpt_SalesBill_25
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                    rpt = New Rpt_SalesBill_26
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                    rpt = New Rpt_SalesBill_27
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                    rpt = New Rpt_SalesBill_28
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                    rpt = New Rpt_SalesBill_29
                End If
            End If

            txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
            txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(TxtTotalAfterdisc.Text)
            txtCMPNameDown = rpt.Section1.ReportObjects("txtCMPNameDown")
            txtCMPNameDown.Text = CMPNameDown
        End If

        If PrintSmall = "A5" Then
            If SalesPricePublic = "YES" Then
                rpt = New Rpt_SalesBill_A5_PricePublic
            Else
                If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                    rpt = New Rpt_SalesBill_A5_1
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                    rpt = New Rpt_SalesBill_BalanceCust_A5_2
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                    rpt = New Rpt_SalesBill_A5_3
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                    rpt = New Rpt_SalesBill_A5_4
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                    rpt = New Rpt_SalesBill_A5_5
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                    rpt = New Rpt_SalesBill_A5_6
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                    rpt = New Rpt_SalesBill_A5_7
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                    rpt = New Rpt_SalesBill_A5_5
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                    rpt = New Rpt_SalesBill_A5_9
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                    rpt = New Rpt_SalesBill_A5_5
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                    rpt = New Rpt_SalesBill_A5_5
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                    rpt = New Rpt_SalesBill_A5_6_Delegate_2
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = lblEmployeesName.Text
                    txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                    txtCustomerAddressSales.Text = CustomerAddress
                    txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                    txtCustomerTel.Text = CustomerTel
                    txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                    txtPhoneEmployee.Text = PhoneEmployee
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                    rpt = New Rpt_SalesBill_A5_6_Delegate_2
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = lblEmployeesName.Text
                    txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                    txtCustomerAddressSales.Text = CustomerAddress
                    txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                    txtCustomerTel.Text = CustomerTel
                    txtPhoneEmployee = rpt.Section1.ReportObjects("txtPhoneEmployee")
                    txtPhoneEmployee.Text = PhoneEmployee
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                    rpt = New Rpt_SalesBill_BalanceCust_A5_14
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                    rpt = New Rpt_SalesBill_A5_15
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                    rpt = New Rpt_SalesBill_A5_16
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                    rpt = New Rpt_SalesBill_A5_17
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                    rpt = New Rpt_SalesBill_A5_5
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                    rpt = New Rpt_SalesBill_A5_5
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                    rpt = New Rpt_SalesBill_A5_5
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                    rpt = New Rpt_SalesBill_A5_5
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                    rpt = New Rpt_SalesBill_A5_22
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                    rpt = New Rpt_SalesBill_A5_22
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                    rpt = New Rpt_SalesBill_A5_22
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                    rpt = New Rpt_SalesBill_A5_22
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                    rpt = New Rpt_SalesBill_A5_22
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                    rpt = New Rpt_SalesBill_A5_22
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                    rpt = New Rpt_SalesBill_A5_22
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                    rpt = New Rpt_SalesBill_A5_22
                End If
                txtCMPNameDown = rpt.Section1.ReportObjects("txtCMPNameDown")
                txtCMPNameDown.Text = CMPNameDown
            End If
            If ShowTax <> "0" Then
                rpt = New Rpt_SalesBill_A5_Nubaria_Tax
                If chkSalesTax.Checked = True Then
                    txtSaleTax = rpt.Section1.ReportObjects("txtSaleTax")
                    txtSaleTax.Text = txtSalesTax.Text
                Else
                    txtSaleTax = rpt.Section1.ReportObjects("txtSaleTax")
                    txtSaleTax.Text = ""
                    txtSaleTaxText = rpt.Section1.ReportObjects("txtSaleTaxText")
                    txtSaleTaxText.Text = ""
                End If
                If chkDiscountTax.Checked = True Then
                    txtDiscountTaxs = rpt.Section1.ReportObjects("txtDiscountTax")
                    txtDiscountTaxs.Text = txtDiscountTax.Text
                Else
                    txtDiscountTaxs = rpt.Section1.ReportObjects("txtDiscountTax")
                    txtDiscountTaxs.Text = ""
                    txtDiscountTaxText = rpt.Section1.ReportObjects("txtDiscountTaxText")
                    txtDiscountTaxText.Text = ""
                End If
            End If
            txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
            txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(TxtTotalAfterdisc.Text)
        End If

        If ShowValueVAT = "YES" Then
            If PrintSmall = "YES" Then
                rpt = New Rpt_SoldSmall_VAT
            End If
            If PrintSmall = "NO" Then
                'If CommercialAndIndustrialProfitsTaxYESNO = "YES" Then
                '    If txtCommercialAndIndustrialProfitsTax.Text = "0" And CommercialAndIndustrialProfitsTaxYESNO = "NO" Then
                '        rpt = New Rpt_SalesBill_Cash_VAT
                '    Else
                '        rpt = New Rpt_SalesBill_Cash_VAT_1
                '    End If
                '    If txtCommercialAndIndustrialProfitsTax.Text = "0" And CommercialAndIndustrialProfitsTaxYESNO = "YES" Then
                '        rpt = New Rpt_SalesBill_Cash_VAT_1
                '    End If
                If CommercialAndIndustrialProfitsTax <> 0 Then
                    If txtCommercialAndIndustrialProfitsTax.Text = "0" Then
                        rpt = New Rpt_SalesBill_Cash_VAT
                    Else
                        rpt = New Rpt_SalesBill_Cash_VAT_1
                    End If
                End If

                txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
                txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(TxtTotalAfterdisc.Text)
            End If
            If PrintSmall = "A5" Then
                rpt = New Rpt_SalesBill_BalanceCust_A5_VAT
            End If
        End If

        If ActivateElectronicBill = "YES" Then
            If PrintSmall = "YES" Then
                rpt = New Rpt_SoldSmall_VAT_QRCode
            End If
            If PrintSmall = "NO" Then
                rpt = New Rpt_SalesBill_Cash_VAT_QRCode2
                txtObjectVATRate = rpt.Section1.ReportObjects("txtVATRate")
                txtObjectVATRate.Text = "(% " & txtSalesTax.Text & " )"
                txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
                txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(TxtTotalAfterdisc.Text)
            End If
            If PrintSmall = "A5" Then
                rpt = New Rpt_SalesBill_BalanceCust_A5_VAT_QRCode
            End If
        End If

        Cls.Select_More_Data_Branch_Print_Orderby("PrintSalesPurchases", "*", "NumberInt1")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txtCmpAddressBill = rpt.Section1.ReportObjects("txtTitelAddress")
        txtCmpAddressBill.Text = CMPAddressBill

        'txtname = rpt.Section1.ReportObjects("txtName")
        'txtname.Text = "أسم العميل"
        txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
        txtNameEn.Text = NameEnCompany
        txtCmpAddress = rpt.Section1.ReportObjects("txtAddress")
        txtCmpAddress.Text = CmpAddress
        txtCmpEmail = rpt.Section1.ReportObjects("txtEmail")
        txtCmpEmail.Text = CmpEmail
        txtCmpTel = rpt.Section1.ReportObjects("txtTel")
        txtCmpTel.Text = CmpTel
        txtCmpMobile = rpt.Section1.ReportObjects("txtMobile")
        txtCmpMobile.Text = CmpMobile
        txtCmpUnderBILL = rpt.Section1.ReportObjects("txtUnderBILL")
        txtCmpUnderBILL.Text = CMPUnderBILL
        If PrintSmall <> "YES" Then
            txtCmpFax = rpt.Section1.ReportObjects("txtFax")
            txtCmpFax.Text = CmpFax
        End If
        txtCommercialRecord = rpt.Section1.ReportObjects("txtCommercialRecord")
        txtCommercialRecord.Text = CMPCommercialRecord
        txtTaxCard = rpt.Section1.ReportObjects("txtTaxCard")
        txtTaxCard.Text = CMPTaxCard

        If HideProgramNameBill = "YES" Then
            txtProgramNameBill = rpt.Section1.ReportObjects("txtProgramNameBill")
            txtProgramNameBill.Text = ""
        Else
            txtProgramNameBill = rpt.Section1.ReportObjects("txtProgramNameBill")
            txtProgramNameBill.Text = ProgramNameBill
        End If

        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "فاتـــــورة مبــيــعات"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub

    Private Sub btnEnterStore_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnEnterStore.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Dim x As String = MsgBox("هل تريد بالفعل إتمام عملية البيع", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        ReceivingEditBillno = txtbillno.Text
        Dim newForm As New List(Of FrmSales)
        newForm.Add(New FrmSales)
        newForm(0).Show()

        'Dim bill_No, Vendorname, bill_date, billtime, totalpricebeforedisc, disc, totalpriceafterdisc, stat, bey, STAYING, SalesTax, Notes, UserName, DiscountTax, bill_NoTax As String
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select * from Receive_Sales_Bill where bill_No=N'" & txtbillno.Text & "'"
        'dr = cmd.ExecuteReader : dr.Read()
        'If dr.HasRows = True Then
        '    bill_No = dr("bill_No") : Vendorname = dr("Vendorname") : bill_date = dr("bill_date") : billtime = dr("billtime") : totalpricebeforedisc = dr("totalpricebeforedisc")
        '    disc = dr("disc") : totalpriceafterdisc = dr("totalpriceafterdisc") : stat = dr("stat") : bey = dr("bey") : STAYING = dr("STAYING") : SalesTax = dr("SalesTax") : Notes = dr("Notes") : UserName = dr("UserName") : DiscountTax = dr("DiscountTax") : bill_NoTax = dr("bill_NoTax")
        'End If
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'S = "insert into Sales_Bill(bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,SalesTax,Notes,UserName,DiscountTax,bill_NoTax) values ("
        'S = S & "N'" & txtbillno.Text.Trim & "' ,N'" & Vendorname & "' ,N'" & Cls.C_date(DateTimePicker1.Text) & "' ,N'" & Cls.get_time(True) & "' ," & Val(txttotalpeforedisc.Text.Trim) & "," & Val(TxtDisc.Text.Trim) & " ," & Val(TxtTotalAfterdisc.Text.Trim) & " ,N'" & stat & "',N'" & txtpaying.Text.Trim & "',N'" & txtstaying.Text.Trim & "',N'" & txtSalesTax.Text.Trim & "',N'" & txtNotes.Text & "',N'" & UserName & "'," & DiscountTax & "," & bill_NoTax & ")"
        'cmd.CommandText = S : cmd.ExecuteNonQuery()

        ''==========================================================

        'Dim aray_bill_no As New ArrayList
        'Dim aray_itm_id As New ArrayList
        'Dim aray_itm_cat As New ArrayList
        'Dim aray_itm_name As New ArrayList
        'Dim aray_price As New ArrayList
        'Dim aray_TinPriceAverage As New ArrayList
        'Dim aray_qu As New ArrayList
        'Dim aray_totalprice As New ArrayList
        'Dim aray_Stores As New ArrayList
        'Dim aray_bill_date As New ArrayList
        'Dim aray_username As New ArrayList
        'Dim aray_Discounts As New ArrayList
        'Dim aray_Profits As New ArrayList
        'Dim aray_EmpName As New ArrayList
        'Dim aray_Vendorname As New ArrayList
        'Dim aray_Stat As New ArrayList

        'If stat = "نقدا" Then
        '    hintaName = "نسبة"
        'End If

        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select * from Receive_BillsalData where bill_No=N'" & txtbillno.Text & "'"
        'dr = cmd.ExecuteReader
        'Do While dr.Read = True
        '    aray_bill_no.Add(dr("bill_no").ToString()) : aray_itm_id.Add(dr("itm_id")) : aray_itm_cat.Add(dr("itm_cat")) : aray_itm_name.Add(dr("itm_name")) : aray_price.Add(dr("price")) : aray_TinPriceAverage.Add(dr("TinPriceAverage")) : aray_qu.Add(dr("qu")) : aray_totalprice.Add(dr("totalprice")) : aray_Stores.Add(dr("Stores")) : aray_username.Add(dr("username")) : aray_bill_date.Add(dr("bill_date")) : aray_Discounts.Add(dr("Discounts")) : aray_Profits.Add(dr("Profits")) : aray_EmpName.Add(dr("EmpName")) : aray_Vendorname.Add(dr("Vendorname")) : aray_Stat.Add(dr("Stat"))
        'Loop

        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'For i As Integer = 0 To aray_bill_no.Count - 1
        '    S = "insert into BillsalData(bill_no,itm_id,itm_cat,itm_name,price,TinPriceAverage,qu,totalprice,Stores,bill_date,UserName,Discounts,Profits,EmpName,Vendorname,Stat)  values (N'" & aray_bill_no(i) & "',N'" & aray_itm_id(i) & "',N'" & aray_itm_cat(i) & "',N'" & aray_itm_name(i) & "'," & aray_price(i) & "," & aray_TinPriceAverage(i) & "," & aray_qu(i) & "," & aray_totalprice(i) & ",N'" & aray_Stores(i) & "',N'" & aray_bill_date(i) & "',N'" & aray_username(i) & "'," & aray_Discounts(i) & "," & aray_Profits(i) & ",N'" & aray_EmpName(i) & "',N'" & aray_Vendorname(i) & "',N'" & aray_Stat(i) & "')"
        '    cmd.CommandText = S : cmd.ExecuteNonQuery()

        '    IM.Store(aray_itm_id(i), aray_Stores(i))
        'Next

        ''==========================================================

        'If Val(txtpaying.Text) > 0 Then
        '    Dim VND_XTM, VND_dt, VND_amx, VND_ho, VND_rcv, VND_dec, VND_no, EmpName As String
        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "select * from Receive_vst where BillNo=N'" & txtbillno.Text & "'"
        '    dr = cmd.ExecuteReader : dr.Read()
        '    If dr.HasRows = True Then
        '        Vendorname = dr("Vendorname") : VND_XTM = dr("VND_XTM") : VND_dt = dr("VND_dt") : VND_amx = dr("VND_amx") : VND_ho = dr("VND_ho") : VND_rcv = dr("VND_rcv") : VND_dec = dr("VND_dec") : VND_no = dr("VND_no") : bill_No = dr("BillNo") : UserName = dr("UserName") : EmpName = dr("EmpName")
        '    End If
        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    S = "insert into vst (Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,billno,VND_no,UserName,EmpName) values"
        '    S = S & " (N'" & Vendorname & "',N'" & VND_XTM & "',N'" & VND_dt & "'," & VND_amx & ",N'" & VND_ho & "',N'" & VND_rcv & "',N'" & VND_dec & "',N'" & bill_No & "',N'" & VND_no & "',N'" & UserName & "',N'" & EmpName & "')"
        '    cmd.CommandText = S : cmd.ExecuteNonQuery()
        'End If

        ''==========================================================

        'If Val(TxtDisc.Text) > 0 Then
        '    Dim amnt, pdate, det, TIN_NO As String
        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "select * from Receive_Vst_disc where TIN_NO=N'" & txtbillno.Text & "'"
        '    dr = cmd.ExecuteReader : dr.Read()
        '    If dr.HasRows = True Then
        '        Vendorname = dr("Vendorname") : amnt = dr("amnt") : pdate = dr("pdate") : det = dr("det") : TIN_NO = dr("TIN_NO") : UserName = dr("UserName")
        '    End If
        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    S = "insert into Vst_disc (Vendorname,amnt,pdate,VND_XTM,det,TIN_NO,UserName) values (N'" & Vendorname & "'," & amnt & ",N'" & pdate & "',N'" & Cls.get_time(True) & "',N'" & det & "',N'" & TIN_NO & "',N'" & UserName & "')"
        '    cmd.CommandText = S : cmd.ExecuteNonQuery()
        'End If

        'ImageUpdateBill()

        'IM.VendorAccountTotal(Vendorname)

        ''==========================================================
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "delete From  Receive_Sales_Bill where bill_no =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
        'cmd.CommandText = "delete From  Receive_BillsalData where bill_no =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
        'cmd.CommandText = "delete From  Receive_Vst_disc where TIN_NO =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
        'cmd.CommandText = "delete From  Receive_Vst where BillNo =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()

        'MsgBox("تمت عملية المبيعات بنجاح", MsgBoxStyle.Information)
        'Panel2.Dock = DockStyle.None
        'Panel2.Top = 5000
        'GetData()
    End Sub

    Private Sub ImageUpdateBill()
        If OpenFileDialog1.FileName <> "" Then
            connectionStringOpen()
            Dim cmd2 As SqlClient.SqlCommand = New SqlClient.SqlCommand
            S = " Update purchase_bill SET  Image_Bill = @Image_Bill WHERE bill_No =N'" & txtbillno.Text & "'"
            cmd2.CommandType = CommandType.Text
            cmd2.Connection = Cn
            Dim fs As FileStream = New FileStream(OpenFileDialog1.FileName, FileMode.Open, FileAccess.Read)
            Dim r As BinaryReader = New BinaryReader(fs)
            Dim FileByteArray(fs.Length - 1) As Byte
            r.Read(FileByteArray, 0, CInt(fs.Length))
            With cmd2
                .CommandType = CommandType.Text
                .Connection = Cn
                .Parameters.Add("@Image_Bill", SqlDbType.Image).Value = FileByteArray
                .CommandText = S
            End With
            cmd2.ExecuteNonQuery()
        End If
    End Sub

    Private Sub btnClose_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClose.Click
        PanelEmployees.Top = 10000
    End Sub

    Private Sub btnEmployees_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnEmployees.Click
        PanelEmployees.Top = 61
    End Sub


    Private Sub chkAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            ChkCash.Enabled = False
            ChkState.Enabled = False
            chkVisa.Enabled = False
        ElseIf chkAll.Checked = False Then
            ChkCash.Enabled = True
            ChkState.Enabled = True
            chkVisa.Enabled = True
        End If
    End Sub

    Private Sub GetMediumSmall(ByVal IdItems As String, ByVal Stores As String, ByVal Store As Double)
        'الكمية الاكبر والمتوسط ةالاصغر 
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select PriceMedium,PriceSmall from Items where itm_id = N'" & IdItems & "'  and Stores =N'" & Stores & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then PriceMedium = 0 : PriceSmall = 0 Else PriceMedium = dr(0) : PriceSmall = dr(1)

        If PriceMedium = 0 And PriceSmall = 0 Then
            StoreCarton = 0
        Else
            Try
                StoreCarton = Store / PriceMedium
                If PriceSmall <= 1 Then
                    Dim STO As String = StoreCarton : Dim split As String() = New String() {"."} : Dim itemsSplit As String() = STO.Split(split, StringSplitOptions.None)
                    StoreCarton = itemsSplit(0).ToString()
                    Dim XFirst As String = StoreCarton * PriceMedium
                    Dim XSecond As String = Store - XFirst
                    StoreCarton = StoreCarton & "." & XSecond
                Else
                    StoreCarton = StoreCarton / PriceSmall
                    Dim STO As String = StoreCarton : Dim split As String() = New String() {"."} : Dim itemsSplit As String() = STO.Split(split, StringSplitOptions.None)
                    StoreCarton = itemsSplit(0).ToString()
                    Dim XFirst As String = StoreCarton * PriceSmall
                    Dim XSecond As String = Store - XFirst
                    StoreCarton = StoreCarton & "." & XFirst
                End If
            Catch ex As Exception
                StoreCarton = 0
            End Try
        End If
    End Sub

    Private Sub MAXRECORD()
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Sales_Bill"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            billno = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(bill_No As float)) as mb FROM Sales_Bill where bill_No <> N'جرد'"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            billno = sh + 1
        End If

    End Sub

    Private Sub MAXRECORD_TAX()
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Sales_Bill"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            bill_NoTax = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(bill_NoTax As float)) as mb FROM Sales_Bill"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            bill_NoTax = sh + 1
        End If

    End Sub

    Private Sub Chack_Code(ByVal Table As String, ByVal Code As String, ByVal Name As String, ByVal TextBox As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select " & Code & " from " & Table & " where " & Name & "=N'" & TextBox & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Result_Code = dr(0)
        End If
    End Sub

    Private Sub chkSalesTaxView_CheckedChanged(sender As Object, e As EventArgs) Handles chkSalesTaxView.CheckedChanged
        If chkSalesTaxView.Checked = True Then
            chkDiscountTax.Visible = True
            chkSalesTax.Visible = True
            Label29.Visible = True
            txtbill_NoTax.Visible = True
        Else
            chkDiscountTax.Visible = False
            chkSalesTax.Visible = False
            Label29.Visible = False
            txtbill_NoTax.Visible = False
        End If
    End Sub

    Private Sub txtammnt_TextChanged(sender As Object, e As EventArgs) Handles txtammnt.TextChanged
        txtStay.Text = Val(txtAmountBack.Text) - Val(txtammnt.Text)
    End Sub

    Private Sub btnAddPay_Click(sender As Object, e As EventArgs) Handles btnAddPay.Click
        If txtStay.Text < 0 Then
            MsgBox("غير مسموح بسداد اكثر من القيمة المتبقية من الفاتورة", MsgBoxStyle.Exclamation)
            Exit Sub
        End If

        Dim MTHODX As String = "نقدي"
        Dim DateTimeNow As DateTime = DateTime.Now

        If CustPayAllBill = False Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Try
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into vst (Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,billno,VND_no,UserName,Treasury_Code,CashBank) values"
                S = S & " (N'" & txtCustName.Text & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(DateTimeNow) & "'," & txtammnt.Text.Trim & ",N'" & MTHODX & "',N'" & txtrecipient.Text & "',N'" & txtdet.Text.Trim & "',N'" & txtbillno.Text & "',N'" & txtno.Text & "',N'" & UserName & "',N'" & Treasury_Code & "',0)"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Catch ex As Exception

            End Try

            If txtStay.Text = 0 Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Sales_Bill set Stat =N'نقداً'  where bill_No =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()
            End If

            Dim xbill As String = Cls.Select_SUM_Value_More("Sales_Bill", "BEY", "bill_No =N'" & txtbillno.Text & "'")
            Dim xBEY As String = Val(xbill) + Val(txtammnt.Text)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Sales_Bill set STAYING =N'" & txtStay.Text & "',BEY =N'" & xBEY & "'  where bill_No =N'" & txtbillno.Text & "'" : cmd.ExecuteNonQuery()

            IM.CustomerAccountTotal(txtCustName.Text.Trim)

            Get_Movement_In_Out_Money(dtpvndrecipient.Text, Treasury_Code)
        End If

        '=====================================================================================

        If CustPayAllBill = True Then
            Dim CustName, State As String
            Dim xBEY, AmoutBillNet, xAmout, Billno, totalpriceafterdisc, STAYING As Double
            xAmout = txtammnt.Text
            For i As Integer = 0 To DataGridView1.RowCount - 1
                Billno = DataGridView1.Rows(i).Cells(0).Value.ToString()
                CustName = DataGridView1.Rows(i).Cells(1).Value.ToString()
                totalpriceafterdisc = DataGridView1.Rows(i).Cells(6).Value.ToString()
                xBEY = DataGridView1.Rows(i).Cells(8).Value.ToString()
                State = DataGridView1.Rows(i).Cells(9).Value.ToString()
                If State <> "نقداً" Then

                    If xAmout <= xBEY Then
                        AmoutBillNet = xBEY - xAmout
                        STAYING = AmoutBillNet
                        xBEY = xAmout
                    Else
                        AmoutBillNet = xAmout - xBEY
                        STAYING = totalpriceafterdisc - xBEY
                        xAmout = AmoutBillNet
                    End If

                    MAXRECORD2("Vst", "VND_no")
                    txtno.Text = BillNumber

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    Try
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        S = "insert into vst (Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,billno,VND_no,UserName,Treasury_Code,CashBank) values"
                        S = S & " (N'" & CustName & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(DateTimeNow) & "'," & xBEY & ",N'" & MTHODX & "',N'" & txtrecipient.Text & "',N'" & txtdet.Text.Trim & "',N'" & txtbillno.Text & "',N'" & BillNumber & "',N'" & UserName & "',N'" & Treasury_Code & "',0)"
                        cmd.CommandText = S : cmd.ExecuteNonQuery()
                    Catch ex As Exception

                    End Try

                    If STAYING = 0 Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "update Sales_Bill set Stat =N'نقداً'  where bill_No =N'" & Billno & "'" : cmd.ExecuteNonQuery()
                    End If

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update Sales_Bill set STAYING =N'" & STAYING & "',BEY =N'" & xBEY & "'  where bill_No =N'" & Billno & "'" : cmd.ExecuteNonQuery()

                    IM.CustomerAccountTotal(CustName.Trim)

                    Get_Movement_In_Out_Money(dtpvndrecipient.Text, Treasury_Code)

                    Dim CurrentBalanceCustVnd As String = GetCurrentBalanceCustVnd(CustName)
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update vst set CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where bill_No =N'" & Billno & "' and Vendorname =N'" & CustName & "'" : cmd.ExecuteNonQuery()

                End If
            Next
        End If
        MsgBox("تم تسديد الدفعة بنجاح", MsgBoxStyle.Information)
        GetData()
        UpdatePayAndStay()
        Panel6.Top = 5000
        PanelCust_Pay.Top = 5000
        txtno.Enabled = True
        CustPayAllBill = False
        Label37.Visible = False
        txtCustName.Visible = False
    End Sub

    Private Sub UpdatePayAndStay()
        Dim VND_amx As Double = 0
        Dim Total_amx As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(VND_amx) from Vst where BillNo =N'" & txtbillno.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then VND_amx = 0 Else VND_amx = dr(0)

        Dim staying As Double = Val(txt_staying2.Text)

        txt_staying1.Text = Val(staying) - Val(VND_amx)
        txtpaying1.Text = Val(VND_amx)
    End Sub


    Private Sub btnCust_Pay_Click(sender As Object, e As EventArgs) Handles btnCust_Pay.Click
        If txt_staying2.Text = "0" Then
            MsgBox("لا يمكن تسديد الفاتورة لانها نقداا", MsgBoxStyle.Information)
            Exit Sub
        End If

        Dim VND_amx As Double = 0
        Dim Total_amx As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sum(VND_amx) from Vst where BillNo =N'" & txtbillno.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr(0) Is DBNull.Value Then VND_amx = 0 Else VND_amx = dr(0)

        Total_amx = Val(txttotalafterdisc1.Text) - Val(VND_amx)


        If Total_amx = 0 Then
            MsgBox("تم سداد الفاتورة كاملة", MsgBoxStyle.Exclamation)
            Exit Sub
        End If

        PanelCust_Pay.Top = 80
        PanelCust_Pay.Location = New System.Drawing.Point(283, 29)
        PanelCust_Pay.Size = New System.Drawing.Size(479, 337)
        MAXRECORD2("Vst", "VND_no")
        txtno.Text = BillNumber

        txtCustName.Text = txtvendores.Text
        'txtammnt.Text = txt_staying1.Text

        txtAmountBack.Text = Total_amx
        txtammnt.Text = Total_amx
    End Sub

    Private Sub MAXRECORD2(ByVal Tabel As String, ByVal Fields As String)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " & Tabel & " where VND_no <> N'جرد' and VND_no <> N'دفعة نقدية'"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)
        If dt.Rows.Count = 0 Then
            BillNumber = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" & Fields & " As float)) as mb FROM " & Tabel & " where VND_no <> N'جرد' and VND_no <> N'دفعة نقدية'"
            dr = cmd.ExecuteReader
            dr.Read()
            Dim sh As Long
            sh = dr("mb")
            BillNumber = sh + 1
        End If

    End Sub

    Private Sub btnClosePay_Click(sender As Object, e As EventArgs) Handles btnClosePay.Click
        PanelCust_Pay.Top = 5000
        txtno.Enabled = True
        CustPayAllBill = False
        Label37.Visible = False
        txtCustName.Visible = False
    End Sub

    Private Sub btnCloseViewImage_Click(sender As Object, e As EventArgs) Handles btnCloseViewImage.Click
        PanelViewImage.Dock = DockStyle.None
        PanelViewImage.Top = 5000
    End Sub

    Private Sub Pic_Bill_Click(sender As Object, e As EventArgs) Handles Pic_Bill.Click
        PanelViewImage.Top = 20
        PanelViewImage.Dock = DockStyle.Fill
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub

    Private Sub btnCust_PayAllBill_Click(sender As Object, e As EventArgs) Handles btnCust_PayAllBill.Click
        If txtstaying.Text = "0" Then
            MsgBox("لا يمكن تسديد الفاتورة لانها نقداا", MsgBoxStyle.Information)
            Exit Sub
        End If

        Dim VND_amx As Double = 0
        Dim Total_amx As String = ""

        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select sum(VND_amx) from Vst where BillNo =N'" & txtbillno.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        'If dr(0) Is DBNull.Value Then VND_amx = 0 Else VND_amx = dr(0)

        Total_amx = txtstaying.Text


        If Total_amx = 0 Then
            MsgBox("تم سداد الفاتورة كاملة", MsgBoxStyle.Exclamation)
            Exit Sub
        End If

        PanelCust_Pay.Top = 80
        PanelCust_Pay.Location = New System.Drawing.Point(283, 29)
        PanelCust_Pay.Size = New System.Drawing.Size(479, 337)
        MAXRECORD2("Vst", "VND_no")
        txtno.Text = BillNumber

        txtAmountBack.Text = Total_amx
        txtammnt.Text = Total_amx
        txtno.Enabled = False
        CustPayAllBill = True
        Label37.Visible = False
        txtCustName.Visible = False
    End Sub

    Private Sub btnDubbileClickGrid_Click(sender As Object, e As EventArgs) Handles btnDubbileClickGrid.Click
        GetDetails()
    End Sub

    Private Sub chkTime_CheckedChanged(sender As Object, e As EventArgs) Handles chkTime.CheckedChanged
        If chkTime.Checked = True Then
            dtpTime1.Enabled = False
            dtpTime2.Enabled = False
        Else
            dtpTime1.Enabled = True
            dtpTime2.Enabled = True
        End If
    End Sub

    Private Sub btnViewBill_Click(sender As Object, e As EventArgs) Handles btnViewBill.Click
        Panel2.Dock = DockStyle.None
        Panel2.Top = 5000
    End Sub

    Private Sub DataGridView2_DoubleClick(sender As Object, e As EventArgs) Handles DataGridView2.DoubleClick
        If DealingWithSerialItems = "YES" Then
            If DataGridView2.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView2.Rows.Count) = 0 Then Beep() : Exit Sub
            SN_billno = txtbillno.Text
            SN_Barcode = DataGridView2.SelectedRows(0).Cells(0).Value
            SN_View = "View"

            FrmSales_SerialNumber.Close()
            FrmSales_SerialNumber.Show()
        End If
    End Sub

    Private Sub btnSelectBillOneView_Click(sender As Object, e As EventArgs) Handles btnSelectBillOneView.Click
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات لجمع الفواتير المحددة فى فاتورة واحدة", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل جمع الفواتير المحددة فى فاتورة واحدة", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub


        MAXRECORD()

        If ShowTax = "0" Then
            bill_NoTax = "0"
        Else
            MAXRECORD_TAX()
        End If

        Dim CustName As String = ""
        Dim Notes As String = ""
        Dim Sheft_Number As String = ""
        Dim EmpName As String = ""


        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            ItmID = DataGridView1.Rows(i).Cells(0).Value
            CustName = DataGridView1.Rows(i).Cells(1).Value
            Stat = DataGridView1.Rows(i).Cells(9).Value
            Notes = DataGridView1.Rows(i).Cells(10).Value
            Sheft_Number = DataGridView1.Rows(i).Cells(11).Value
            EmpName = DataGridView1.Rows(i).Cells(12).Value

            totalpricebeforedisc += DataGridView1.Rows(i).Cells(4).Value
            disc += DataGridView1.Rows(i).Cells(5).Value
            totalpriceafterdisc += DataGridView1.Rows(i).Cells(6).Value
            BEY += DataGridView1.Rows(i).Cells(7).Value
            STAYING += DataGridView1.Rows(i).Cells(8).Value

            Chack_Code("vst", "VND_amx", "billno", ItmID)
            VstPay += Val(Result_Code) : Result_Code = 0

            Chack_Code("Vst_disc", "amnt", "TIN_NO", ItmID)
            Vst_discPay += Val(Result_Code) : Result_Code = 0

            Try
                ' حذف آمن للفاتورة والبيانات المرتبطة بها
                Dim deleteQueries As New List(Of String) From {
                    "DELETE FROM Sales_Bill WHERE bill_No = @billNo",
                    "DELETE FROM vst WHERE billno = @billNo",
                    "DELETE FROM Vst_disc WHERE TIN_NO = @billNo"
                }

                Dim parameters As New Dictionary(Of String, Object) From {
                    {"@billNo", ItmID}
                }

                ' تنفيذ عمليات الحذف
                For Each deleteQuery In deleteQueries
                    SecureDatabaseManager.Instance.ExecuteNonQuery(deleteQuery, parameters)
                Next

                ' تحديث بيانات الفاتورة
                Dim updateQuery As String = "UPDATE BillsalData SET bill_no = @newBillNo WHERE bill_no = @oldBillNo"
                Dim updateParameters As New Dictionary(Of String, Object) From {
                    {"@newBillNo", billno},
                    {"@oldBillNo", ItmID}
                }

                SecureDatabaseManager.Instance.ExecuteNonQuery(updateQuery, updateParameters)

            Catch ex As Exception
                ErrorHandler.LogError("خطأ في حذف الفاتورة", ex, $"BillID: {ItmID}")
                MessageBox.Show("حدث خطأ في حذف الفاتورة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try

        Next

        SalesTax = mykey.GetValue("SalesTax", "0")
        DiscountTax = mykey.GetValue("ValueDiscountTax", "0")

        sumdisc()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Sales_Bill(bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,SalesTax,Notes,UserName,Sheft_Number,EmpName,DiscountTax,bill_NoTax) values ("
        S = S & "N'" & billno & "' ,N'" & CustName & "' ,N'" & Cls.C_date(DateTimePicker3.Text) & "' ,N'" & Cls.get_time(True) & "' ," & Val(totalpricebeforedisc) & "," & Val(disc) & " ," & Val(totalpriceafterdisc) & " ,N'" & Stat & "',N'" & BEY & "',N'" & STAYING & "',N'" & SalesTax & "',N'" & Notes & "',N'" & UserName & "',N'" & Sheft_Number & "',N'" & EmpName & "',N'" & DiscountTax & "',N'" & bill_NoTax & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Val(VstPay) > 0 Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into vst(Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,billno,VND_no,UserName,EmpName,CashBank) values"
            S = S & " (N'" & CustName & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(DateTimePicker3.Text) & "'," & VstPay & ",N'بفاتورة',N'بفاتورة',N'بفاتورة',N'" & billno & "',N'دفعة نقدية',N'" & UserName & "',N'" & EmpName & "',0)"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If

        If Val(Vst_discPay) > 0 Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Vst_disc(Vendorname,amnt,pdate,det,TIN_NO,UserName ) values (N'" & CustName & "'," & Val(Vst_discPay) & ",N'" & Cls.C_date(DateTimePicker2.Text) & "',N'خصم على فاتورة مباشرة',N'" & billno & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If

        IM.CustomerAccountTotal(CustName)
        IM.EmployeesAccountTotal(cmbEmployees.Text)

        GetData()
        MsgBox("تم جمع الفواتير المحددة فى فاتورة واحدة", MsgBoxStyle.Information)
    End Sub

    Private Sub sumdisc()

        Dim DiscVal, TaxVal, TotalVal, TotalDiscountTax As Double
        TaxVal = Format(Val(txttotalpeforedisc.Text) * Val(txtSalesTax.Text) / 100, "Fixed")

        TotalDiscountTax = Val((Val(totalpricebeforedisc) * (100 - Val(DiscountTax))) / 100)
        TotalDiscountTax = Val(totalpricebeforedisc) - Val(TotalDiscountTax)
        TotalDiscountTax = Math.Round(TotalDiscountTax, 2)


        DiscVal = Val(totalpricebeforedisc) - Val(disc)

        TotalVal = TaxVal + DiscVal - TotalDiscountTax
        totalpriceafterdisc = TotalVal

        If Stat <> "نقداً" Then
            STAYING = Val(totalpriceafterdisc) - Val(BEY)
        Else
            STAYING = "0"
        End If
        If txtstaying.Text = "0" Then
            BEY = Val(totalpriceafterdisc) - Val(STAYING)
        End If
    End Sub

    Private Sub GetPrintBillOne()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView2.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        Dim XTotalBill As String
        If txtpaying.Text = 0 Then
            XTotalBill = txtstaying.Text
        Else
            XTotalBill = TxtTotalAfterdisc.Text
        End If

        Dim Xbill_No As String = ""
        If ShowTax = "0" Then
            Xbill_No = txtbillno.Text
        Else
            Xbill_No = txtbill_NoTax.Text
        End If

        Dim Xqunt As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView2.Rows.Count - 1
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases(Company_Branch_ID,itm_id,itm_cat,itm_name,price,qu,totalprice,store,VnPay,Unity,det,BILL_NO,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,BEY,STAYING,UserName,Stat)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView2.Rows(i).Cells(0).Value & "',N'" & DataGridView2.Rows(i).Cells(1).Value & "',N'" & DataGridView2.Rows(i).Cells(2).Value & "',N'" & DataGridView2.Rows(i).Cells(3).Value & "',N'" & DataGridView2.Rows(i).Cells(4).Value & "',N'" & DataGridView2.Rows(i).Cells(5).Value & "',N'" & DataGridView2.Rows(i).Cells(6).Value & "',N'" & DataGridView2.Rows(i).Cells(7).Value & "',N'" & DataGridView2.Rows(i).Cells(8).Value & "',N'" & DataGridView2.Rows(i).Cells(9).Value & "',N'" & Xbill_No & "',N'" & txtvendores.Text & "',N'" & txtbilldate.Text & "',N'" & txtbilltime.Text & "',N'" & txttotalpeforedisc.Text & "',N'" & txtdisc1.Text & "',N'" & txttotalafterdisc1.Text & "',N'" & txtpaying1.Text & "',N'" & txt_staying1.Text & "',N'" & XTotalBill & "',N'" & txtNotes.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim txt, txtname, txtNameAr, txtNameEn, txtCmpAddress, txtCmpEmail, txtCmpTel, txtCmpMobile, txtCmpFax, txtCmpUnderBILL, txtCmpAddressBill, txtCommercialRecord, txtTaxCard, txtSaleTax, txtDiscountTaxs, txtSaleTaxText, txtDiscountTaxText As TextObject


        Dim rpt
        If PrintSmall = "YES" Then
            rpt = New Rpt_SoldSmall
        Else
            If ColorWithItems = "YES" Then
                rpt = New Rpt_SalesBill_4
            Else
                rpt = New Rpt_SalesBill
            End If
        End If
        If PrintSmall = "A5" Then
            rpt = New Rpt_SalesBill_A5_4
            txtCommercialRecord = rpt.Section1.ReportObjects("txtCommercialRecord")
            txtCommercialRecord.Text = CMPCommercialRecord
            txtTaxCard = rpt.Section1.ReportObjects("txtTaxCard")
            txtTaxCard.Text = CMPTaxCard
            If ShowTax <> "0" Then
                rpt = New Rpt_SalesBill_A5_Nubaria_Tax
                If chkSalesTax.Checked = True Then
                    txtSaleTax = rpt.Section1.ReportObjects("txtSaleTax")
                    txtSaleTax.Text = txtSalesTax.Text
                Else
                    txtSaleTax = rpt.Section1.ReportObjects("txtSaleTax")
                    txtSaleTax.Text = txtSalesTax.Text
                    txtSaleTaxText = rpt.Section1.ReportObjects("txtSaleTaxText")
                    txtSaleTaxText.Text = ""
                End If
                If chkDiscountTax.Checked = True Then
                    txtDiscountTaxs = rpt.Section1.ReportObjects("txtDiscountTax")
                    txtDiscountTaxs.Text = txtDiscountTax.Text
                Else
                    txtDiscountTaxs = rpt.Section1.ReportObjects("txtDiscountTax")
                    txtDiscountTaxs.Text = txtDiscountTax.Text
                    txtDiscountTaxText = rpt.Section1.ReportObjects("txtDiscountTaxText")
                    txtDiscountTaxText.Text = ""
                End If
            End If
        End If

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txtCmpAddressBill = rpt.Section1.ReportObjects("txtTitelAddress")
        txtCmpAddressBill.Text = CMPAddressBill

        txtname = rpt.Section1.ReportObjects("txtName")
        txtname.Text = "أسم العميل"
        txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
        txtNameEn.Text = NameEnCompany
        txtCmpAddress = rpt.Section1.ReportObjects("txtAddress")
        txtCmpAddress.Text = CmpAddress
        txtCmpEmail = rpt.Section1.ReportObjects("txtEmail")
        txtCmpEmail.Text = CmpEmail
        txtCmpTel = rpt.Section1.ReportObjects("txtTel")
        txtCmpTel.Text = CmpTel
        txtCmpMobile = rpt.Section1.ReportObjects("txtMobile")
        txtCmpMobile.Text = CmpMobile
        txtCmpUnderBILL = rpt.Section1.ReportObjects("txtUnderBILL")
        txtCmpUnderBILL.Text = CMPUnderBILL
        If PrintSmall <> "YES" Then
            txtCmpFax = rpt.Section1.ReportObjects("txtFax")
            txtCmpFax.Text = CmpFax
        End If
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "فاتـــــورة مبــيــعات"
        Frm_PrintReports.Show()
    End Sub

    Private Sub GetCustomerAddress()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select addr,tel1 from Customers where Vendorname=N'" & txtvendores.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                CustomerAddress = dr(0).ToString
                CustomerTel = dr(1).ToString
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Phone from Employees where NameEmployee=N'" & lblEmployeesName.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                PhoneEmployee = dr(0).ToString
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetReturnProfit()
        Dim aray_1 As New ArrayList

        aray_1.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select Profits from IM_Bsal_Data where id <> N''"
        If ChbAll.Checked = False Then
            If txtbillnoSearch.Text <> "" Then
                S = S & " and bill_no = N'" & txtbillnoSearch.Text.Trim & "'"
            End If
            If txtNotesView.Text <> "" Then
                S = S & " and Notes =N'" & txtNotesView.Text.Trim & "'"
            End If
        End If
        If chkEmployees.Checked = False Then
            S = S & " and EmpName =N'" & cmbEmployees.Text.Trim & "'"
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and  bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        'S = S & " ORDER BY  bill_No"
        cmd.CommandText = S
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr(0))
        Loop

        Dim SM10 As Double
        For i As Integer = 0 To aray_1.Count - 1
            SM10 += Val(aray_1(i).ToString)
        Next
        txtReturnProfitsBill.Text = Math.Round(SM10, 2)
    End Sub

    Private Sub GetSerialNumber()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select bill_no from BillsalData_SerialNumber where Serial_Number_Name=N'" & txtSerialNumber.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            txtbillnoSearch.Text = dr(0).ToString
        End If
    End Sub

    Private Sub GetPrintSerialNumber()
        If DealingWithSerialItems = "YES" Then

            If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

            Dim aray_1 As New ArrayList : Dim aray_2 As New ArrayList : Dim aray_3 As New ArrayList
            aray_1.Clear() : aray_2.Clear() : aray_3.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select dbo.BillsalData_SerialNumber.Auto_Number As auto, dbo.BillsalData_SerialNumber.Serial_Number_Name As Serial_Number, dbo.Items.sname As Items, dbo.BillsalData_SerialNumber.bill_no  From dbo.BillsalData_SerialNumber LEFT OUTER Join dbo.Items ON dbo.BillsalData_SerialNumber.itm_id = dbo.Items.itm_id  Where (dbo.BillsalData_SerialNumber.bill_no =N'" & txtbillno.Text & "')"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_1.Add(dr(0)) : aray_2.Add(dr(1)) : aray_3.Add(dr(2))
            Loop
            If aray_1.Count = 0 Then
                Exit Sub
            End If
            Cls.delete_Branch_All("PrintSalesPurchases")

            For i As Integer = 0 To aray_1.Count - 1
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into PrintSalesPurchases(disc,itm_id,itm_name,Vendorname,bill_date,billtime,BILL_NO) values"
                S = S & " (N'" & aray_1(i) & "',N'" & aray_2(i) & "',N'" & aray_3(i) & "',N'" & txtvendores.Text & "',N'" & txtbilldate.Text & "',N'" & txtTimeAMBM.Text & "',N'" & txtbillno.Text & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next

            AddReportView()

            Dim rpt
            If PrintSmall = "YES" Then
                rpt = New Rpt_SerialNumberItems_Small
            End If
            If PrintSmall = "NO" Then
                rpt = New Rpt_SerialNumberItems_A4
            End If
            If PrintSmall = "A5" Then
                rpt = New Rpt_SerialNumberItems_A5
            End If
            Dim txt, txtNameAr, txtProgramNameBill As TextObject

            Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
            Dim dt As New DataTable
            dt.Load(dr)
            rpt.SetDataSource(dt)
            txt = rpt.Section1.ReportObjects("txtReportTitel")
            txt.Text = "سيريال للاصناف"
            txtNameAr = rpt.Section1.ReportObjects("txtTitelNameAr")
            txtNameAr.Text = NameArCompay

            If HideProgramNameBill = "YES" Then
                txtProgramNameBill = rpt.Section1.ReportObjects("txtProgramNameBill")
                txtProgramNameBill.Text = ""
            Else
                txtProgramNameBill = rpt.Section1.ReportObjects("txtProgramNameBill")
                txtProgramNameBill.Text = ProgramNameBill
            End If

            If SelectLogoPathOther = "YES" Then
                rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
            Else
                rpt.SetParameterValue("ImageURL", CMPLogoPath)
            End If
            Frm_PrintReports_2.CrystalReportViewer1.ReportSource = rpt
            Frm_PrintReports_2.Text = "سيريال للاصناف"
            Frm_PrintReports_2.Show()

            If RunDatabaseInternet = "YES" Then : connect() : End If

        End If
    End Sub

    Private Sub ImageUpdateQRCode(ByVal Table As String, ByVal Field As String, ByVal Where As String, ByVal Text As String)
        'Try
        connectionStringOpen()
        Cn = New SqlConnection(constring)
        Cn.Open()
        S = "Update " + Table + " SET  " + Field + " = @" + Field + " WHERE " + Where + " =N'" & Text & "'"
        cmd = New SqlCommand(S)
        Dim stream As MemoryStream = New MemoryStream
        picQRCode.Image.Save(stream, System.Drawing.Imaging.ImageFormat.Jpeg)
        Dim Pic() As Byte = stream.ToArray
        cmd.Parameters.AddWithValue("@" + Field + "", Pic)
        cmd.Connection = Cn
        cmd.ExecuteReader()
        Cn.Close()
        connectionStringOpen()
        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub

    Private Sub GetQRCode()
        If ActivateElectronicBill = "YES" Then
            Try
                Dim QRHotelName As String = ""
                Dim QRBillNo As String = ""
                Dim QRVATAmount As String = ""
                Dim QRTotalAmount As String = ""
                Dim QRTIN As String = ""
                Dim QRBillDate As String = ""


                QRBillNo = "BillNo:" + txtbillno.Text
                QRVATAmount = "VAT:" + lblTotalValueVAT.Text
                QRTotalAmount = "Total:" + txttotalpeforedisc.Text
                QRHotelName = "Name:" + NameEnCompany
                QRTIN = "TIN:" + CMPTaxCard
                QRBillDate = "Date:" + DateTimePicker1.Text
                Dim NewDate = DateTimePicker1.Text

                Dim qr As QRCode = New QRCode(NameArCompay, CMPTaxCard, DateTimePicker1.Text, txttotalpeforedisc.Text, lblTotalValueVAT.Text)

                Dim hexValue = qr.ToString()
                Dim Base64Value = qr.ToBase64()

                Dim objqrcode As QRCodeEncoder = New QRCodeEncoder
                Dim img As Image
                Dim btm As Bitmap
                Dim yourcode As String = QRHotelName + "," + QRTIN + "," + QRBillNo + "," + QRVATAmount + "," + QRTotalAmount + "," + QRBillDate

                'Dim EncoderHex As String = StringToHex(yourcode)
                'Dim HexBase64 As String = HexToBase64(EncoderHex)

                objqrcode.QRCodeScale = 2
                img = objqrcode.Encode(Base64Value)
                'img = objqrcode.Encode(yourcode)
                btm = New Bitmap(img)
                btm.Save("qrimage.jpg")
                picQRCode.ImageLocation = "qrimage.jpg"
                picQRCode.Image = CType(btm, Image)
            Catch ex As Exception
                ErrorHandling(ex, "GetQRCode")
            End Try

        End If

    End Sub

    Private Sub GetDataCustomerDelivery()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from Customers where Vendorname=N'" & txtvendores.Text.Trim & "'"
            dr = cmd.ExecuteReader
            If dr.Read Then
                txtCustAddress = dr("addr").ToString
                txtCusttel1 = dr("tel1").ToString
                txtCustMobile = dr("Mobile").ToString
                txtCustApartment = dr("Apartment").ToString
                txtCustRole = dr("Role").ToString
                txtCustRegion = dr("Region").ToString
                txtCustMark = dr("Mark").ToString
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub


End Class