﻿Imports vb = Microsoft.VisualBasic
Imports System.Data.OleDb
Imports CrystalDecisions.CrystalReports.Engine

Public Class FrmRemindAppointment
    Dim ListBoxSelectedIndex As Integer
    Dim WithEvents BS As New BindingSource
    Dim ActivUpdate As Boolean = False
    Dim CustomersCode As String
    Dim ActivEdit As Boolean = False
    Dim Remind_Number As String = ""

    Private Sub FrmItemsNew_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyBase.KeyUp
    End Sub
    Private Sub FrmItemsNew_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        cmbRemind_Confirmation.SelectedIndex = 0
        MAXRECORDAutoALL("RemindAppointment", "Remind_Number")

        Cls.fill_combo("Customers", "Vendorname", cmbCustomers)
        Cls.fill_combo("Customers", "Vendorname", cmbCustomersView)
        PanelAddNew.Dock = DockStyle.None
        PanelAddNew.Top = 5000
    End Sub

    Private Sub btnShow_Click(sender As Object, e As EventArgs) Handles btnShow.Click
        connect()
        DataGridView1.DataSource = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT id as [id],Remind_Number as [رقم العملية],Remind_Name as [أسم التذكير],Customers_Name as [العميل],Remind_Date as [التاريخ],Remind_Time as [الوقت],Remind_Notes as [البيان],Remind_Confirmation as [حالة التذكير] FROM RemindAppointment where id <> N''"
        If chkAll.Checked = False Then
            If txtRemind_Number_View.Text <> "" Then
                S = S & " And Remind_Number = N'" & txtRemind_Number_View.Text.Trim & "'"
            End If
            If cmbCustomersView.Text <> "" Then
                S = S & " And Customers_Name =N'" & cmbCustomersView.Text.Trim & "'"
            End If
        End If
        If chkRemind_Confirm_Active_ALL.Checked = False Then
            If rdoRemind_Confirm_Active_View.Checked = True Then
                S = S & " And Remind_Confirmation = N'1'"
            End If
            If rdoRemind_Confirm_Inactive_View.Checked = True Then
                S = S & " And Remind_Confirmation = N'2'"
            End If
            If rdoRemind_Confirm_Hanging_View.Checked = True Then
                S = S & " And Remind_Confirmation = N'3'"
            End If
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and Remind_Date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and Remind_Date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        S = S & " order by Remind_Number"

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
        DataGridView1.Columns(2).Width = 90
        DataGridView1.Columns(6).Width = 250
        DataGridView1.Columns(0).Visible = False
        DataGridView1.Columns(7).Visible = False

        For i As Integer = 0 To DataGridView1.RowCount - 1
            If DataGridView1.Rows(i).Cells(7).Value = 1 Then
                DataGridView1.Rows(i).DefaultCellStyle.BackColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(191, Byte), Integer), CType(CType(46, Byte), Integer))
                DataGridView1.Rows(i).DefaultCellStyle.ForeColor = Color.White
            End If
            If DataGridView1.Rows(i).Cells(7).Value = 2 Then
                DataGridView1.Rows(i).DefaultCellStyle.BackColor = System.Drawing.Color.FromArgb(CType(CType(239, Byte), Integer), CType(CType(53, Byte), Integer), CType(CType(58, Byte), Integer))
                DataGridView1.Rows(i).DefaultCellStyle.ForeColor = Color.White
            End If
            If DataGridView1.Rows(i).Cells(7).Value = 3 Then
                DataGridView1.Rows(i).DefaultCellStyle.BackColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(104, Byte), Integer), CType(CType(236, Byte), Integer))
                DataGridView1.Rows(i).DefaultCellStyle.ForeColor = Color.White
            End If
        Next

        'DataGridView1.Columns(1).HeaderText = "رقم الطلب"
        'DataGridView1.Columns(2).HeaderText = "اسم المحل"
        'DataGridView1.Columns(3).HeaderText = "اجمالى الخصومات"
        'DataGridView1.Columns(4).HeaderText = "اجمالى الطلب"
        'DataGridView1.Columns(5).HeaderText = "عنوان الشحن"
        'DataGridView1.Columns(6).HeaderText = "ملاحظات الطلب"
        'DataGridView1.Columns(7).HeaderText = "الموبايل"
        'DataGridView1.Columns(8).HeaderText = "التاريخ"



        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(4).Value.ToString)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(4).Value = SM
        Next
        ActivUpdate = True
    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbCustomersView.Enabled = False
            txtRemind_Number_View.Enabled = False
        Else
            cmbCustomersView.Enabled = True
            txtRemind_Number_View.Enabled = True
        End If
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(sender As Object, e As EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub MAXRECORDAutoALL(ByVal Tabel As String, ByVal Feild As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from " + Tabel + ""
            dr = cmd.ExecuteReader

            Dim dt As New DataTable
            dt.Load(dr)

            If dt.Rows.Count = 0 Then
                CustomersCode = 1
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(CAST(" + Feild + " As float)) as mb FROM " + Tabel + " where " + Feild + " <> 0"
                dr = cmd.ExecuteReader
                dr.Read()

                Dim sh As Long
                sh = dr("mb")
                CustomersCode = sh + 1
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        PanelAddNew.Dock = DockStyle.None
        PanelAddNew.Top = 5000
        ActivEdit = False
    End Sub

    Private Sub btnAdd_Click(sender As Object, e As EventArgs) Handles btnAdd.Click
        PanelAddNew.Top = 20
        PanelAddNew.Dock = DockStyle.Fill
        MAXRECORDAutoALL("RemindAppointment", "Remind_Number")
        cmbCustomers.Focus()
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs) Handles btnEdit.Click
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        Dim Remind_Number1 As String = DataGridView1.SelectedRows(0).Cells(0).Value
        Remind_Number = Remind_Number1
        txtRemind_Number.Text = Remind_Number1

        Dim Owner_Code As String = "" : Dim Treasury_Code As String = "" : Dim Capital_Type_Code As String = "" : Dim Xbill_date As String = ""
        Cls.Select_More_Data_Stores("RemindAppointment", "Cust_Code,Customers_Name,Remind_Date,Remind_Time,Remind_Notes,Remind_Again_Number,Remind_Confirmation", "Remind_Number=N'" & Remind_Number1 & "'")
        If dr.HasRows = True Then
            Owner_Code = dr(0).ToString
            Treasury_Code = dr(1).ToString
            Capital_Type_Code = dr(2).ToString
            'txtAmount.Text = dr(3).ToString
            Xbill_date = dr(4).ToString
            txtNotes.Text = dr(5).ToString
            dtpCapital_Date.Text = Cls.R_date(Xbill_date)
        End If

        'cmbCapital_Owner.Text = Cls.Get_Code_Value_Branch_More("Capital_Owner", "Owner_Name", "Owner_Code=N'" & Owner_Code & "'")
        'cmbRemind_Confirmation.Text = Cls.Get_Code_Value_Branch_More("Treasury", "Treasury_Name", "Treasury_Code=N'" & Treasury_Code & "'")

        'If Capital_Type_Code = "1" Then
        '    rdoCheckOut.Checked = True
        'End If
        'If Capital_Type_Code = "2" Then
        '    rdoDeposit.Checked = True
        'End If
        ActivEdit = True

        PanelAddNew.Top = 20
        PanelAddNew.Dock = DockStyle.Fill
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        Dim Capital_Number, XDate As String
        Dim Capital_Type As String

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub

            Capital_Number = DataGridView1.SelectedRows(i).Cells(0).Value
            Capital_Type = DataGridView1.SelectedRows(i).Cells(3).Value

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete from RemindAppointment where Capital_Number =N'" & Capital_Number & "'" : cmd.ExecuteNonQuery()

            If Capital_Type = "سحب" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  MOVES where bill_no =N'" & Capital_Number & "' and MOVStatement =N'مسحوبات جارى الشركاء'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & Capital_Number & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & Capital_Number & "' and MOVDNameAccount =N'مسحوبات جارى الشركاء'" : cmd.ExecuteNonQuery()
            End If

            If Capital_Type = "ايداع" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  MOVES where bill_no =N'" & Capital_Number & "' and MOVStatement =N'ايداعات جارى الشركاء'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & Capital_Number & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & Capital_Number & "' and MOVDNameAccount =N'ايداعات جارى الشركاء'" : cmd.ExecuteNonQuery()
            End If


            XDate = DataGridView1.SelectedRows(i).Cells(5).Value
            Get_Movement_In_Out_Money(XDate, Treasury_Code)
        Next

        'GetData()

    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If ValidateTextAdd() = False Then Exit Sub

        'Custmersfinance()

        Get_Movement_In_Out_Money(dtpCapital_Date.Text, Treasury_Code)

        'cmbCapital_Owner.SelectedIndex = -1
        'cmbRemind_Confirmation.SelectedIndex = -1
        'txtRemind_Number.Text = ""
        'txtAmount.Text = ""
        'cmbCapital_Owner.Text = ""
        'MAXRECORD()
        'MsgBox("تم إجراء العملية بنجاح", MsgBoxStyle.Information)
        'cmbCapital_Owner.Focus()
        'cmbCapital_Owner.SelectAll()

        If ActivEdit = True Then
            PanelAddNew.Dock = DockStyle.None
            PanelAddNew.Top = 5000
        End If
        ActivEdit = False

    End Sub

    Function ValidateTextAdd() As Boolean

        If Trim(txtRemind_Number.Text) = "" Then
            MsgBox("فضلاً أدخل رقم العملية", MsgBoxStyle.Exclamation)
            txtRemind_Number.Focus()
            Return False
        End If

        If Trim(cmbCustomers.Text) = "" Then
            MsgBox("فضلاً أختر اسم العميل", MsgBoxStyle.Exclamation)
            cmbCustomers.Focus()
            Return False
        End If

        If Trim(cmbRemind_Confirmation.Text) = "" Then
            MsgBox("فضلاً أختر التكرار", MsgBoxStyle.Exclamation)
            cmbRemind_Confirmation.Focus()
            Return False
        End If

        If Trim(txtNotes.Text) = "" Then
            MsgBox("فضلاً أختر البيان", MsgBoxStyle.Exclamation)
            txtNotes.Focus()
            Return False
        End If


        If ActivEdit = False Then
            Cls.Select_More_Data_Branch("RemindAppointment", "*", "Capital_Number=N'" & txtRemind_Number.Text & "'")
            If dr.HasRows = True Then
                MsgBox("عفواً يوجد رقم ايصال مسجل مسبقا بنفس الرقم", MsgBoxStyle.Exclamation)
                Return False : Exit Function
            Else
            End If
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * From Capital_Owner where Owner_Name =N'" & cmbCustomers.Text & "'"
        dr = cmd.ExecuteReader()
        If dr.HasRows = False Then
            MsgBox("عفواً  لا يوجد أسم المالك مسجل مسبقا بنفس الرقم", MsgBoxStyle.Exclamation)
            Return False : Exit Function
        Else
        End If

        Return True
    End Function

    Private Sub rdoRemind_Confirm_Active_CheckedChanged(sender As Object, e As EventArgs) Handles rdoRemind_Confirm_Active.CheckedChanged
        PicRemind_Confirm.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Circle_Green
    End Sub

    Private Sub rdoRemind_Confirm_Inactive_CheckedChanged(sender As Object, e As EventArgs) Handles rdoRemind_Confirm_Inactive.CheckedChanged
        PicRemind_Confirm.BackgroundImage = My.Resources.Circle_Red
    End Sub

    Private Sub rdoRemind_Confirm_Hanging_CheckedChanged(sender As Object, e As EventArgs) Handles rdoRemind_Confirm_Hanging.CheckedChanged
        PicRemind_Confirm.BackgroundImage = My.Resources.circle_Blue
    End Sub

    Private Sub chkRemind_Confirm_Active_ALL_CheckedChanged(sender As Object, e As EventArgs) Handles chkRemind_Confirm_Active_ALL.CheckedChanged
        If chkAll.Checked = True Then
            PanelRemind_Confirm_Active_ALL.Enabled = False
        Else
            PanelRemind_Confirm_Active_ALL.Enabled = True
        End If
    End Sub
End Class