﻿
Imports VB = Microsoft.VisualBasic
Imports System.Windows.Forms
Imports System.Data
Imports System.Data.SqlClient

Public Class Supper_Market_Class

    Function insertintofunds(ByVal funds_item_id As String, ByVal funds_item_cat As String, ByVal funds_item_name As String, ByVal x As String, ByVal xx As String, ByVal funds_range As String, ByVal funds_purchases As String, ByVal funds_sales As String, ByVal funds_back_purchases As String, ByVal funds_back_sales As String, ByVal funds As String, ByVal Funds_purchase As String, ByVal Funs_Sales As String)
        Try
            ' إدراج آمن للصندوق
            Dim insertQuery As String = "INSERT INTO funds (funds_item_id, funds_item_cat, funds_item_name, x, xx, funds_range, funds_purchases, funds_sales, funds_back_purchases, funds_back_sales, funds, Funds_purchase, Funs_Sales) VALUES (@fundsItemId, @fundsItemCat, @fundsItemName, @x, @xx, @fundsRange, @fundsPurchases, @fundsSales, @fundsBackPurchases, @fundsBackSales, @funds, @fundsPurchase, @funsSales)"

            Dim parameters As New Dictionary(Of String, Object) From {
                {"@fundsItemId", funds_item_id},
                {"@fundsItemCat", funds_item_cat},
                {"@fundsItemName", funds_item_name},
                {"@x", Val(x)},
                {"@xx", Val(xx)},
                {"@fundsRange", Val(funds_range)},
                {"@fundsPurchases", Val(funds_purchases)},
                {"@fundsSales", Val(funds_sales)},
                {"@fundsBackPurchases", Val(funds_back_purchases)},
                {"@fundsBackSales", Val(funds_back_sales)},
                {"@funds", Val(funds)},
                {"@fundsPurchase", Val(Funds_purchase)},
                {"@funsSales", Val(Funs_Sales)}
            }

            SecureDatabaseManager.Instance.ExecuteNonQuery(insertQuery, parameters)

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في إدراج بيانات الصندوق", ex, $"ItemID: {funds_item_id}")
            MessageBox.Show("حدث خطأ في حفظ بيانات الصندوق", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Function

    Friend Sub SP_FillCombo(ByVal fieldsname As String, ByVal tablename As String, ByVal wherename As String, ByVal valuename As String, ByVal cmb As ComboBox)
        Try
            Bol = True

            ' بناء استعلام آمن
            Dim queryBuilder As New SafeQueryBuilder($"SELECT DISTINCT {fieldsname} FROM {tablename} WHERE {wherename} = @value ORDER BY 1")
            queryBuilder.AddCustomCondition("", New Dictionary(Of String, Object) From {{"@value", valuename}})

            Dim result As DataTable = SecureDatabaseManager.Instance.ExecuteQuery(queryBuilder.Build(), queryBuilder.Parameters)

            cmb.DataSource = result
            If result.Columns.Count > 0 Then
                cmb.ValueMember = result.Columns(0).ColumnName
                cmb.DisplayMember = result.Columns(0).ColumnName
            End If

            Bol = False

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في تعبئة ComboBox", ex, $"Table: {tablename}, Field: {fieldsname}")
            Bol = False
        End Try
    End Sub

    Function GenerateItmId_Or_Parcode() As String
        Dim DDTT As String
        DDTT = Now.Year.ToString
        DDTT = (DDTT & (Now.Month.ToString))
        DDTT = (DDTT & (Now.Day.ToString))
        DDTT = (DDTT & (Now.Hour.ToString))
        DDTT = (DDTT & (Now.Minute.ToString))
        DDTT = (DDTT & (Now.Second.ToString))
        DDTT = (DDTT & (Now.Millisecond.ToString))

        DDTT = VB.Mid(DDTT, 3, 12)
        If Len(DDTT) = 6 Then
            DDTT = DDTT & "0" & "0" & "0" & "0" & "0" & "0"
        End If
        If Len(DDTT) = 7 Then
            DDTT = DDTT & "0" & "0" & "0" & "0" & "0"
        End If
        If Len(DDTT) = 8 Then
            DDTT = DDTT & "0" & "0" & "0" & "0"
        End If
        If Len(DDTT) = 9 Then
            DDTT = DDTT & "0" & "0" & "0"
        End If
        If Len(DDTT) = 10 Then
            DDTT = DDTT & "0" & "0"
        End If
        If Len(DDTT) = 11 Then
            DDTT = DDTT & "0"
        End If
        Return DDTT
    End Function

    Function ConfirmItemIdFunds(ByVal itm_det As String, ByVal itm_nm As String, ByVal itm_qtr As String) As Boolean
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select distinct Itm_ from funds where itm_det =N'" & itm_det & "' and itm_nm =N'" & itm_nm & "' " : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Return True
        ElseIf dr.HasRows = False Then
            Return False
        End If
    End Function

    Function RETURNitm_id(ByVal itm_det As String, ByVal itm_nm As String) As String
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select distinct Itm_id from itm where itm_det =N'" & itm_det & "' and itm_nm =N'" & itm_nm & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Dim M As String = dr("itm_id")
            Return M
        ElseIf dr.HasRows = False Then
            Return Nothing
        End If
    End Function

    Function RETURNItm_Parcode(ByVal itm_det As String, ByVal itm_nm As String, ByVal itm_cat As String) As String
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select distinct Itm_Prc from itm where itm_det =N'" & itm_det & "' and itm_nm =N'" & itm_nm & "' and itm_cat =N'" & itm_cat & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Dim M As String = dr("itm_id")
            Return M
        ElseIf dr.HasRows = False Then
            Return Nothing
        End If
    End Function

    Function RETURNItm_rng_x_xx(ByVal Itm_Prc As String) As String
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select distinct itm_rng , itm_det,itm_nm , itm_cat , itm_rng , x,xx from itm where Itm_Prc=N'" & Itm_Prc & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Dim M As String = dr("itm_id")
            Return M
        ElseIf dr.HasRows = False Then
            Return Nothing
        End If
    End Function

    Function return_out_Range(ByVal X As Integer, ByVal Y As Integer) As Integer
        Dim M As Integer
        M = Int(X - Y * Int(X / Y))
        Return M
    End Function

    Public Sub Sp_cats_insert(ByVal CAtname As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "Sp_cats_insert"
        cmd.Parameters.AddWithValue("@CAtName", CAtname)

        cmd.ExecuteNonQuery()

    End Sub

    

End Class
