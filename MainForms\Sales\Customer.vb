﻿Imports CrystalDecisions.CrystalReports.Engine

Public Class Customer
    Dim archiveManager As New Cls_ArchiveManager
    Dim XEdit As String
    Dim MTHODX As String
    Dim ItmID As String
    Dim Vendor As String
    Dim billno As String
    Dim CustName As String
    Dim Cust_Code As String
    Dim Result_Code As String


    Private Sub Customer_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Header()
        PanelCustDataMore.Top = 2000
        PanelDeliverydriver.Top = 2000
        PanelPrint.Top = 2000
        MAXRECORD()
        Bra.Fil("PriceType", "PriceTypeName", cmbPriceType)
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployeeNameFind)
        Bra.Fil("Geographic_Area", "GeoArea_Name", cmbGeoArea_Code)
        Bra.Fil("Geographic_Area", "GeoArea_Name", cmbGeoArea_Code)
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployeeName)

    End Sub
    Public Sub Header()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If PermtionName = "مدير" Then
                S = "Select Top(100) PERCENT dbo.Customers.id As رقم, dbo.Customers.Cust_Code As [كود العميل], dbo.Customers.Vendorname As الاسم, dbo.Customers.addr As العنوان, dbo.Customers.tel1 As تليفون, dbo.Customers.vnamntcredit As مدين,     dbo.Customers.vnamntdebit AS دائن, dbo.Customers.notes As ملاحظات, dbo.Customers.Mobile As موبايل, dbo.Customers.Region As المنطقة, dbo.Customers.Apartment As الشقة, dbo.Customers.Mark As علامة, dbo.Customers.Role As الدور,     dbo.PriceType.PriceTypeName AS [نوع السعر], dbo.Customers.PriceType_ID AS [كود نوع السعر]  From dbo.Customers LEFT OUTER Join    dbo.PriceType ON dbo.Customers.PriceType_ID = dbo.PriceType.PriceType_ID Group By dbo.Customers.id, dbo.Customers.Cust_Code, dbo.Customers.Vendorname, dbo.Customers.addr, dbo.Customers.tel1, dbo.Customers.vnamntcredit, dbo.Customers.vnamntdebit, dbo.Customers.notes, dbo.Customers.Mobile,    dbo.Customers.Region, dbo.Customers.Apartment, dbo.Customers.Mark, dbo.Customers.Role, dbo.Customers.PriceType_ID, dbo.PriceType.PriceTypeName  Order By رقم DESC"
            Else
                S = "Select Top(100) PERCENT dbo.Customers.id As رقم, dbo.Customers.Cust_Code As [كود العميل], dbo.Customers.Vendorname As الاسم, dbo.Customers.addr As العنوان, dbo.Customers.tel1 As تليفون, dbo.Customers.vnamntcredit As مدين,     dbo.Customers.vnamntdebit AS دائن, dbo.Customers.notes As ملاحظات, dbo.Customers.Mobile As موبايل, dbo.Customers.Region As المنطقة, dbo.Customers.Apartment As الشقة, dbo.Customers.Mark As علامة, dbo.Customers.Role As الدور,     dbo.PriceType.PriceTypeName AS [نوع السعر], dbo.Customers.PriceType_ID As [كود نوع السعر], dbo.Customers.Company_Branch_ID  From dbo.Customers LEFT OUTER Join    dbo.PriceType ON dbo.Customers.PriceType_ID = dbo.PriceType.PriceType_ID  Group By dbo.Customers.id, dbo.Customers.Cust_Code, dbo.Customers.Vendorname, dbo.Customers.addr, dbo.Customers.tel1, dbo.Customers.vnamntcredit, dbo.Customers.vnamntdebit, dbo.Customers.notes, dbo.Customers.Mobile,    dbo.Customers.Region, dbo.Customers.Apartment, dbo.Customers.Mark, dbo.Customers.Role, dbo.Customers.PriceType_ID, dbo.PriceType.PriceTypeName, dbo.Customers.Company_Branch_ID  HAVING(dbo.Customers.Company_Branch_ID = N'" & Company_Branch_ID & "')    ORDER BY رقم DESC"
            End If
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DataGridView1.DataSource = Cls.PopulateDataView(dr)
            DataGridView1.Columns(0).Visible = False
            DataGridView1.Columns(8).Visible = False
            DataGridView1.Columns(9).Visible = False
            DataGridView1.Columns(10).Visible = False
            DataGridView1.Columns(11).Visible = False
            DataGridView1.Columns(12).Visible = False
            DataGridView1.Columns(13).Visible = False
            DataGridView1.Columns(14).Visible = False

            txtNumberCust.Text = DataGridView1.RowCount

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
    Sub RadioButtonState()
        If RadioButton1.Checked = True Then
            txtammnt.Enabled = True
        ElseIf RadioButton2.Checked = True Then
            txtammnt.Enabled = True
        ElseIf RadioButton3.Checked = True Then
            txtammnt.Text = 0
            txtammnt.Enabled = False
        End If
    End Sub

    Private Sub RadioButton3_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RadioButton3.CheckedChanged
        RadioButtonState()
    End Sub

    Private Sub RadioButton2_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RadioButton2.CheckedChanged
        RadioButtonState()
    End Sub

    Private Sub RadioButton1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RadioButton1.CheckedChanged
        RadioButtonState()
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        If Button1.Text = "حفظ التعديل" Then edt() : CLEAR_ALL() : Exit Sub
        If Validate_Text() = False Then Exit Sub

        vendorfinance()

        If RadioButton2.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Sales_Bill set CreditPrevious = " & Val(0) & ",DebitPrevious = " & Val(0) & ",CreditCurrent = " & Val(0) & ",DebitCurrent = " & Val(txtammnt.Text) & ",CurrentBalanceCustVnd = " & Val(txtammnt.Text) & " where Vendorname =N'" & CustName & "' and bill_No=N'جرد'" : cmd.ExecuteNonQuery()
        End If
        If RadioButton1.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vst set CreditPrevious = " & Val(0) & ",DebitPrevious = " & Val(0) & ",CreditCurrent = " & Val(txtammnt.Text) & ",DebitCurrent = " & Val(0) & ",CurrentBalanceCustVnd = " & Val(txtammnt.Text) & " where Vendorname =N'" & CustName & "' and BillNo=N'جرد'" : cmd.ExecuteNonQuery()
        End If

        '===============================================================================
        Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
        If UseAccounts = "YES" Then
            Daily_Restrictions()
        End If
        '===============================================================================


        Get_Movement_In_Out_Money(DateTimePicker1.Text, Treasury_Code)

        'Header()
        CLEAR_ALL()
        MAXRECORD()
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)
    End Sub
    Sub CLEAR_ALL()
        vndr.Text = ""
        addr.Text = ""
        tel1.Text = ""
        notes.Text = ""
        txtammnt.Text = ""
        txtMark.Text = ""
        txtMobile.Text = ""
        txtApartment.Text = ""
        txtRegion.Text = ""
        txtRole.Text = ""
        cmbPriceType.Text = ""
        GroupBox7.Enabled = True
    End Sub
    Sub fil_txt()


        If Trim(tel1.Text) = "" Then
            tel1.Text = "####"
        End If

        If Trim(notes.Text) = "" Then
            notes.Text = "####"
        End If
    End Sub
    Sub vendorfinance()
        'MAXRECORD()

        fil_txt()

        Chack_Code("PriceType", "PriceType_ID", "PriceTypeName", cmbPriceType.Text)

        Cls.insert("customers", "Company_Branch_ID,Cust_Code,Vendorname,addr,tel1,notes,vintinval,vndiscount,VnPay,vnamntdebit,vnamntcredit,UserName,GeoArea_Code,Mobile,Apartment,Role,Region,Mark,PriceType_ID,Limit_DrawDowns_Price,taxn,tradn", "N'" & Company_Branch_ID & "',N'" & txtCust_Code.Text & "',N'" & vndr.Text.Trim & "',N'" & addr.Text & "',N'" & tel1.Text & "',N'" & notes.Text & "',0,0,0,0,0,N'" & UserName & "',0,N'" & txtMobile.Text & "',N'" & txtApartment.Text & "',N'" & txtRole.Text & "',N'" & txtRegion.Text & "',N'" & txtMark.Text & "',N'" & Result_Code & "',N'" & txtLimit_DrawDowns_Price.Text & "',N'" & txttaxn.Text & "',N'" & txttradn.Text & "'")

        Try
            Dim inventory As String = "جرد"
            If RadioButton1.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into vst (Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,VND_no,billno,Treasury_Code,CashBank) values"
                S = S & " (N'" & Company_Branch_ID & "',N'" & vndr.Text & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(DateTimePicker1.Text) & "'," & Val(txtammnt.Text) & ",N'" & inventory & "',N'" & inventory & "',N'" & inventory & "',N'" & inventory & "',N'" & inventory & "',N'" & Treasury_Code & "',0)"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            ElseIf RadioButton2.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Sales_bill(Company_Branch_ID,bill_No,Vendorname,bill_date,billtime,disc,totalpriceafterdisc,totalpricebeforedisc,stat,Treasury_Code) values ("
                S = S & "N'" & Company_Branch_ID & "',N'" & inventory & "',N'" & vndr.Text.Trim & "' ,N'" & Cls.C_date(DateTimePicker1.Text) & "' ,N'" & Cls.get_time(True) & "' ,0 ,0," & Val(txtammnt.Text) & " ,N'" & inventory & "',N'" & Treasury_Code & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        IM.CustomerAccountTotal(vndr.Text.Trim)

        Dim CurrentBalanceCustVnd As String = GetCurrentBalanceCustVnd(vndr.Text)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Sales_bill set CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where bill_No =N'جرد' and Vendorname =N'" & vndr.Text & "'" : cmd.ExecuteNonQuery()

        Dim GeoArea_Code As String = Cls.Get_Code_Value_More("Geographic_Area", "GeoArea_Code", "GeoArea_Name =N'" & cmbGeoArea_Code.Text & "'")
        Dim EMPID As String = Cls.Get_Code_Value_More("Employees", "EMPID", "NameEmployee =N'" & cmbEmployeeName.Text & "'")
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Customers set GeoArea_Code = " & Val(GeoArea_Code) & ",Emp_Code = " & Val(EMPID) & " where id =N'" & ItmID & "'" : cmd.ExecuteNonQuery()

    End Sub

    Function Validate_Text() As Boolean
        If Trim(vndr.Text) = "" Then
            MsgBox("فضلاً أدخل اسم العميل", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            vndr.Focus() : Return False : Exit Function
        End If
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * From customers where Vendorname =N'" & vndr.Text & "'"
        dr = cmd.ExecuteReader()
        If dr.HasRows = True Then
            MsgBox("عفواً يوجد عميل مسجل مسبقا بنفس الاسم", MsgBoxStyle.Exclamation)
            Return False : Exit Function
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * From customers where Cust_Code =N'" & txtCust_Code.Text & "'"
        dr = cmd.ExecuteReader()
        If dr.HasRows = True Then
            MsgBox("عفواً يوجد كود عميل مسجل مسبقا بنفس الرقم", MsgBoxStyle.Exclamation)
            Return False : Exit Function
        End If
        If RadioButton1.Checked = True Or RadioButton2.Checked = True Then
            If Val(txtammnt.Text) = 0 Then MsgBox("فضلا أدخل المبلغ", MsgBoxStyle.Exclamation) : txtammnt.Focus() : Return False
        End If
        Return True
    End Function
    Function Validate_TextEdit() As Boolean
        If Trim(vndr.Text) = "" Then
            MsgBox("فضلاً أدخل اسم العميل", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            vndr.Focus() : Return False : Exit Function
        End If
        'If Trim(addr.Text) = "" Then
        '    MsgBox("فضلاً أدخل عنوان العميل", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
        '    addr.Focus() : Return False : Exit Function
        'End If

        If Cust_Code <> txtCust_Code.Text.Trim Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * From customers where Cust_Code =N'" & txtCust_Code.Text & "'"
            dr = cmd.ExecuteReader()
            If dr.HasRows = True Then
                MsgBox("عفواً يوجد كود عميل مسجل مسبقا بنفس الاسم", MsgBoxStyle.Exclamation)
                Return False : Exit Function
            End If
        End If

        If Vendor <> vndr.Text.Trim Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * From customers where Vendorname =N'" & vndr.Text & "'"
            dr = cmd.ExecuteReader()
            If dr.HasRows = True Then
                MsgBox("عفواً يوجد عميل مسجل مسبقا بنفس الاسم", MsgBoxStyle.Exclamation)
                Return False : Exit Function
            End If
        End If
        Return True
    End Function
    Function GetVendorState(ByVal vn As String) As Boolean
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from sales_bill where Vendorname =N'" & vn & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Return False
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from vst where Vendorname =N'" & vn & "'  and  billno <> N'جرد'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Return False
        End If
        Return True
    End Function

    Private Sub edt()
        If Validate_TextEdit() = False Then Exit Sub
        'If GetVendorState(Vendor) = False Then
        GroupBox7.Enabled = False
        txtammnt.Enabled = False

        Chack_Code("PriceType", "PriceType_ID", "PriceTypeName", cmbPriceType.Text)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update Customers set "
        S = S & "Company_Branch_ID =N'" & Company_Branch_ID & "',"
        S = S & "Cust_Code =N'" & txtCust_Code.Text & "',"
        S = S & "Vendorname =N'" & vndr.Text.Trim & "',"
        S = S & "addr =N'" & addr.Text & "',"
        S = S & "tel1 =N'" & tel1.Text & "',"
        S = S & "notes =N'" & notes.Text & "',"
        S = S & "Mobile =N'" & txtMobile.Text & "',"
        S = S & "Apartment =N'" & txtApartment.Text & "',"
        S = S & "Role =N'" & txtRole.Text & "',"
        S = S & "Region =N'" & txtRegion.Text & "',"
        S = S & "Mark =N'" & txtMark.Text & "',"
        S = S & "Limit_DrawDowns_Price =N'" & txtLimit_DrawDowns_Price.Text & "',"
        S = S & "taxn =N'" & txttaxn.Text & "',"
        S = S & "tradn =N'" & txttradn.Text & "',"
        S = S & "PriceType_ID =" & Result_Code & ""
        S = S & "where id = " & ItmID & ""
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If RadioButton1.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update vst set Company_Branch_ID =N'" & Company_Branch_ID & "',VND_amx =N'" & txtammnt.Text & "',VND_dt =N'" & Cls.C_date(DateTimePicker1.Text) & "',Treasury_Code =N'" & Treasury_Code & "' where Vendorname =N'" & CustName & "' and billno = N'جرد'" : cmd.ExecuteNonQuery()
        ElseIf RadioButton2.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Sales_Bill set Company_Branch_ID =N'" & Company_Branch_ID & "',totalpricebeforedisc =N'" & txtammnt.Text & "',bill_date =N'" & Cls.C_date(DateTimePicker1.Text) & "',Treasury_Code =N'" & Treasury_Code & "' where Vendorname =N'" & CustName & "' and bill_No = N'جرد'" : cmd.ExecuteNonQuery()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Sales_Bill set Company_Branch_ID =N'" & Company_Branch_ID & "',Vendorname =N'" & vndr.Text.Trim & "' where Vendorname =N'" & CustName & "'" : cmd.ExecuteNonQuery()
        cmd.CommandText = "update IM_Bsal set Company_Branch_ID =N'" & Company_Branch_ID & "',Vendorname =N'" & vndr.Text.Trim & "' where Vendorname =N'" & CustName & "'" : cmd.ExecuteNonQuery()
        cmd.CommandText = "update Vst_disc set Company_Branch_ID =N'" & Company_Branch_ID & "',Vendorname =N'" & vndr.Text.Trim & "' where Vendorname =N'" & CustName & "'" : cmd.ExecuteNonQuery()
        cmd.CommandText = "update Vst_disc_other set Company_Branch_ID =N'" & Company_Branch_ID & "',Vendorname =N'" & vndr.Text.Trim & "' where Vendorname =N'" & CustName & "'" : cmd.ExecuteNonQuery()
        cmd.CommandText = "update IM_Vst_disc set Company_Branch_ID =N'" & Company_Branch_ID & "',Vendorname =N'" & vndr.Text.Trim & "' where Vendorname =N'" & CustName & "'" : cmd.ExecuteNonQuery()
        cmd.CommandText = "update vst set Company_Branch_ID =N'" & Company_Branch_ID & "',Vendorname =N'" & vndr.Text.Trim & "' where Vendorname =N'" & CustName & "'" : cmd.ExecuteNonQuery()
        cmd.CommandText = "update IM_Vst set Company_Branch_ID =N'" & Company_Branch_ID & "',Vendorname =N'" & vndr.Text.Trim & "' where Vendorname =N'" & CustName & "'" : cmd.ExecuteNonQuery()
        cmd.CommandText = "update Vst_Check_Type set Company_Branch_ID =N'" & Company_Branch_ID & "',Vendorname =N'" & vndr.Text.Trim & "' where Vendorname =N'" & CustName & "'" : cmd.ExecuteNonQuery()
        cmd.CommandText = "update Vst_Receipts set Company_Branch_ID =N'" & Company_Branch_ID & "',Vendorname =N'" & vndr.Text.Trim & "' where Vendorname =N'" & CustName & "'" : cmd.ExecuteNonQuery()

        If RadioButton2.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from Sales_Bill where Vendorname =N'" & CustName & "' and bill_No=N'جرد'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  vst where Vendorname =N'" & CustName & "' and billNo=N'جرد'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Sales_Bill (Company_Branch_ID,bill_No,Vendorname,bill_date,totalpricebeforedisc,disc,totalpriceafterdisc,Stat,UserName,Treasury_Code) values"
                S = S & " (N'" & Company_Branch_ID & "',N'جرد',N'" & vndr.Text.Trim & "',N'" & Cls.C_date(DateTimePicker1.Text) & "'," & txtammnt.Text.Trim & ",0,0,N'جرد',N'" & UserName & "',N'" & Treasury_Code & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Sales_Bill set Company_Branch_ID =N'" & Company_Branch_ID & "',totalpricebeforedisc =N'" & txtammnt.Text & "',Treasury_Code =N'" & Treasury_Code & "' where Vendorname =N'" & CustName & "' and bill_No=N'جرد'" : cmd.ExecuteNonQuery()
            End If

        End If


        If RadioButton1.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from vst where Vendorname =N'" & CustName & "' and BillNo=N'جرد'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  Sales_Bill where Vendorname =N'" & CustName & "' and bill_No=N'جرد'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Vst (Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,VND_no,billno,UserName,Treasury_Code,CashBank) values"
                S = S & " (N'" & Company_Branch_ID & "',N'" & vndr.Text.Trim & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(DateTimePicker1.Text) & "'," & Val(txtammnt.Text) & ",N'جرد',N'جرد',N'جرد',N'جرد',N'جرد',N'" & UserName & "',N'" & Treasury_Code & "',0)"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update vst set Company_Branch_ID =N'" & Company_Branch_ID & "',VND_amx =N'" & txtammnt.Text & "',Treasury_Code =N'" & Treasury_Code & "' where Vendorname =N'" & CustName & "' and BillNo=N'جرد'" : cmd.ExecuteNonQuery()
            End If
        End If


        If RadioButton3.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete From  Sales_Bill where Vendorname =N'" & CustName & "' and bill_No=N'جرد'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete From  vst where Vendorname =N'" & CustName & "' and billNo=N'جرد'" : cmd.ExecuteNonQuery()

        End If

        IM.CustomerAccountTotal(CustName)

        Get_Movement_In_Out_Money(DateTimePicker1.Text, Treasury_Code)

        If RadioButton2.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Sales_Bill set CreditPrevious = " & Val(0) & ",DebitPrevious = " & Val(0) & ",CreditCurrent = " & Val(txtammnt.Text) & ",DebitCurrent = " & Val(0) & ",CurrentBalanceCustVnd = " & Val(txtammnt.Text) & " where Vendorname =N'" & CustName & "' and bill_No=N'جرد'" : cmd.ExecuteNonQuery()
        End If
        If RadioButton1.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vst set CreditPrevious = " & Val(0) & ",DebitPrevious = " & Val(0) & ",CreditCurrent = " & Val(0) & ",DebitCurrent = " & Val(txtammnt.Text) & ",CurrentBalanceCustVnd = " & Val(txtammnt.Text) & " where Vendorname =N'" & CustName & "' and BillNo=N'جرد'" : cmd.ExecuteNonQuery()
        End If

        Dim GeoArea_Code As String = Cls.Get_Code_Value_More("Geographic_Area", "GeoArea_Code", "GeoArea_Name =N'" & cmbGeoArea_Code.Text & "'")
        Dim EMPID As String = Cls.Get_Code_Value_More("Employees", "EMPID", "NameEmployee =N'" & cmbEmployeeName.Text & "'")
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Customers set GeoArea_Code = " & Val(GeoArea_Code) & ",Emp_Code = " & Val(EMPID) & " where id =N'" & ItmID & "'" : cmd.ExecuteNonQuery()


        ItmID = "" : Vendor = ""
        Button4.Enabled = True
        txtammnt.Enabled = True
        Button1.Text = "حفظ"
        'Header()
        MsgBox("تم حفظ التعديل بنجاح", MsgBoxStyle.Information)
    End Sub
    Private Sub GetData()
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim PriceType_ID As String = ""
        Dim GeoArea_Code As String = ""
        Dim EMPID As String = ""

        ItmID = DataGridView1.SelectedRows(0).Cells(0).Value.ToString
        Cust_Code = DataGridView1.SelectedRows(0).Cells(1).Value.ToString
        Vendor = DataGridView1.SelectedRows(0).Cells(2).Value.ToString
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Customers where id=N'" & ItmID & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            txtCust_Code.Text = dr("Cust_Code").ToString
            vndr.Text = dr("Vendorname").ToString
            CustName = dr("Vendorname").ToString
            addr.Text = dr("addr").ToString
            tel1.Text = dr("tel1").ToString
            notes.Text = dr("notes").ToString
            XEdit = dr("Vendorname").ToString
            txtMobile.Text = dr("Mobile").ToString
            txtApartment.Text = dr("Apartment").ToString
            txtRole.Text = dr("Role").ToString
            txtRegion.Text = dr("Region").ToString
            txtMark.Text = dr("Mark").ToString
            txtLimit_DrawDowns_Price.Text = dr("Limit_DrawDowns_Price").ToString
            txttaxn.Text = dr("taxn").ToString
            txttradn.Text = dr("tradn").ToString
            PriceType_ID = dr("PriceType_ID").ToString
            GeoArea_Code = dr("GeoArea_Code").ToString
            EMPID = dr("Emp_Code").ToString
        End If

        Chack_Code("PriceType", "PriceTypeName", "PriceType_ID", PriceType_ID)
        cmbPriceType.Text = Result_Code : If Result_Code = "0" Then : cmbPriceType.Text = "" : End If
        Chack_Code("Geographic_Area", "GeoArea_Name", "GeoArea_Code", GeoArea_Code)
        cmbGeoArea_Code.Text = Result_Code : If Result_Code = "0" Then : cmbPriceType.Text = "" : End If
        Chack_Code("Employees", "NameEmployee", "EMPID", EMPID)
        cmbEmployeeName.Text = Result_Code : If Result_Code = "0" Then : cmbPriceType.Text = "" : End If


        Dim ActionX As Boolean = False
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select totalpricebeforedisc from Sales_Bill where Vendorname=N'" & Vendor & "' and bill_No=N'جرد'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            RadioButton2.Checked = True
            txtammnt.Text = dr("totalpricebeforedisc")
            ActionX = True
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select VND_amx from Vst where Vendorname=N'" & Vendor & "' and BillNo=N'جرد'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            RadioButton1.Checked = True
            txtammnt.Text = dr("VND_amx")
            ActionX = True
        End If

        If ActionX = False Then
            RadioButton3.Checked = True
            txtammnt.Text = 0
        End If


    End Sub

    Private Sub GetDataCode(ByVal Code As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Customers where Cust_Code=N'" & Code & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            vndr.Text = dr("Vendorname")
            CustName = dr("Vendorname")
            addr.Text = dr("addr").ToString
            tel1.Text = dr("tel1").ToString
            notes.Text = dr("notes")
            XEdit = dr("Vendorname")
            txtMobile.Text = dr("Mobile").ToString
            txtApartment.Text = dr("Apartment").ToString
            txtRole.Text = dr("Role").ToString
            txtRegion.Text = dr("Region").ToString
            txtMark.Text = dr("Mark").ToString

            Dim ActionX As Boolean = False
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select totalpricebeforedisc from Sales_Bill where Vendorname=N'" & CustName & "' and bill_No=N'جرد'"
            dr = cmd.ExecuteReader
            If dr.Read Then
                RadioButton2.Checked = True
                txtammnt.Text = dr("totalpricebeforedisc")
                ActionX = True
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select VND_amx from Vst where Vendorname=N'" & CustName & "' and BillNo=N'جرد'"
            dr = cmd.ExecuteReader
            If dr.Read Then
                RadioButton1.Checked = True
                txtammnt.Text = dr("VND_amx")
                ActionX = True
            End If

            If ActionX = False Then
                RadioButton3.Checked = True
                txtammnt.Text = 0
            End If

        Else
            CLEAR_ALL()
        End If




    End Sub

    Private Sub DataGridView1_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles DataGridView1.DoubleClick
        GetData()
        Button1.Text = "حفظ التعديل"

    End Sub

    Private Sub Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button3.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            ItmID = DataGridView1.SelectedRows(i).Cells(0).Value
            Vendor = DataGridView1.SelectedRows(i).Cells(2).Value



            If GetVendorState(Vendor) = False Then
                MsgBox("هذا العميل تم تسجيل فواتير بأسمه وحذفه من قاعدة البيانات سيخل بأداءها", MsgBoxStyle.Exclamation)
                'Exit Sub
            End If


            Dim Cust_Code As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Cust_Code from Customers where Vendorname= N'" & Vendor & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Cust_Code = dr("Cust_Code")
            End If

            '=========================================== Archive Manager ============================================================
            archiveManager.ArchiveAndDeleteCustomer("Delete", Vendor, UserName, "تم حذف العميل لعدم النشاط")
            archiveManager.ArchiveAndDeleteVst("Delete", "جرد", Vendor, UserName, "تم حذف العميل لعدم النشاط")
            archiveManager.ArchiveAndDeleteSalesBill("Delete", "جرد", Vendor, UserName, "تم حذف العميل لعدم النشاط")
            '=========================================== Archive Manager ============================================================

            Dim XDate As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select VND_dt from vst where Vendorname =N'" & Vendor & "' and BillNo =N'جرد'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                XDate = dr("VND_dt")
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete  from customers where id =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete  from sales_bill where Vendorname =N'" & Vendor & "' and bill_No =N'جرد'" : cmd.ExecuteNonQuery()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete  from vst where Vendorname =N'" & Vendor & "' and BillNo =N'جرد'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete  from Employee_Customers where Customers_Code =N'" & Cust_Code & "'" : cmd.ExecuteNonQuery()


            cmd.CommandText = "delete From  MOVES where bill_no =N'" & ItmID & "' and MOVStatement =N'مقبوضات عملاء'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'مقبوضات عملاء'" : cmd.ExecuteNonQuery()

            Get_Movement_In_Out_Money(XDate, Treasury_Code)
        Next
        Header()

        ItmID = "" : Vendor = ""
    End Sub

    Private Sub Button4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button4.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        GetData()
        Button1.Text = "حفظ التعديل"
    End Sub
    Private Sub vndr_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles vndr.KeyUp
        If e.KeyCode = 13 Then
            addr.Focus()

        End If
    End Sub

    Private Sub vndr_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles vndr.TextChanged

    End Sub

    Private Sub addr_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles addr.KeyUp
        If e.KeyCode = 13 Then
            tel1.Focus()
        End If
    End Sub

    Private Sub tel1_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles tel1.KeyUp
        If e.KeyCode = 13 Then
            notes.Focus()
        End If
    End Sub

    Private Sub tel1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tel1.TextChanged
        MyVars.CheckNumber(tel1)
    End Sub

    Private Sub notes_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles notes.KeyUp
        If e.KeyCode = 13 Then
            txtammnt.Focus()
        End If
    End Sub

    Private Sub txtammnt_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtammnt.KeyUp
        If e.KeyCode = 13 Then
            Button1.PerformClick()
        End If
    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        Button1.Text = "حفظ"
        Header()
        CLEAR_ALL()
        MAXRECORD()
    End Sub

    Private Sub txtammnt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtammnt.TextChanged
        MyVars.CheckNumber(txtammnt)
    End Sub

    Private Sub MAXRECORD()
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Customers"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            txtCust_Code.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(Cust_Code As float)) as mb FROM Customers where Cust_Code <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            txtCust_Code.Text = sh + 1
        End If

    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        PanelCustDataMore.Top = 2000
    End Sub

    Private Sub LinkLabeCustDataMore_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles LinkLabeCustDataMore.LinkClicked
        PanelCustDataMore.Top = 58
        PanelCustDataMore.Left = 5
        txtMobile.Focus()
    End Sub

    Private Sub txtMob_KeyUp(sender As Object, e As KeyEventArgs) Handles txtMobile.KeyUp
        If e.KeyCode = 13 Then
            txtRegion.Focus()
        End If
    End Sub

    Private Sub txtRegion_KeyUp(sender As Object, e As KeyEventArgs) Handles txtRegion.KeyUp
        If e.KeyCode = 13 Then
            txtApartment.Focus()
        End If
    End Sub

    Private Sub txtApartment_KeyUp(sender As Object, e As KeyEventArgs) Handles txtApartment.KeyUp
        If e.KeyCode = 13 Then
            txtMark.Focus()
        End If
    End Sub

    Private Sub txtMark_KeyUp(sender As Object, e As KeyEventArgs) Handles txtMark.KeyUp
        If e.KeyCode = 13 Then
            txtRole.Focus()
        End If
    End Sub

    Private Sub txtRole_KeyUp(sender As Object, e As KeyEventArgs) Handles txtRole.KeyUp
        If e.KeyCode = 13 Then
            Button1.PerformClick()
        End If
    End Sub

    Private Sub txtCust_Code_KeyUp(sender As Object, e As KeyEventArgs) Handles txtCust_Code.KeyUp
        If e.KeyCode = 13 Then
            GetDataCode(txtCust_Code.Text)
            vndr.Focus()
        End If
    End Sub

    Private Sub txtsearsh_TextChanged(sender As Object, e As EventArgs) Handles txtsearsh.TextChanged
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
            S = "Select id as [رقم],Cust_Code as [كود العميل],Vendorname as [اسم العميل],addr  as [العنوان],tel1  as [تليفون],vnamntcredit  as [مدين],vnamntdebit  as [دائن],notes  as [ملاحظات] From customers"
        Else
            S = "Select id as [رقم],Cust_Code as [كود العميل],Vendorname as [اسم العميل],addr  as [العنوان],tel1  as [تليفون],vnamntcredit  as [مدين],vnamntdebit  as [دائن],notes  as [ملاحظات] From customers where  Vendorname Like N'%" & txtsearsh.Text & "%' or  Cust_Code Like N'%" & txtsearsh.Text & "%' or  addr Like N'%" & txtsearsh.Text & "%' or  tel1 Like N'%" & txtsearsh.Text & "%'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [كود العميل]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [اسم العميل]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [اسم العميل]"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

        DataGridView1.Columns(0).Visible = False
        txtNumberCust.Text = DataGridView1.RowCount
    End Sub

    Private Sub Chack_Code(ByVal Table As String, ByVal Code As String, ByVal Name As String, ByVal TextBox As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select " & Code & " from " & Table & " where " & Name & "=N'" & TextBox & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then
            Result_Code = 0
        Else
            Result_Code = dr(0)
        End If
    End Sub

    Private Sub btnCustBarcode_Click(sender As Object, e As EventArgs) Handles btnCustBarcode.Click
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If
        AddReportView()

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintAllItems")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            S = "insert into PrintAllItems(Company_Branch_ID,itm_id,group_name,sname,Unity)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView1.SelectedRows(i).Cells(1).Value & "',N'" & DataGridView1.SelectedRows(i).Cells(2).Value & "',N'" & DataGridView1.SelectedRows(i).Cells(3).Value & "',N'" & DataGridView1.SelectedRows(i).Cells(4).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        Dim rpt
        rpt = New rpt_Parcode_Customer2

        Cls.Select_More_Data_Branch_Print("PrintAllItems", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)

        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub btnAddBill_Click(sender As Object, e As EventArgs) Handles btnAddBill.Click
        CustomersName = vndr.Text
        Dim newForm As New List(Of FrmSales)
        newForm.Add(New FrmSales)
        newForm(0).Show()
    End Sub

    Private Sub btnPrintCustomer_Click(sender As Object, e As EventArgs) Handles btnPrintCustomer.Click

        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases (Company_Branch_ID,BILL_NO,CustomerName,itm_cat,itm_id,itm_name)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView1.SelectedRows(i).Cells(1).Value & "',N'" & DataGridView1.SelectedRows(i).Cells(2).Value & "',N'" & DataGridView1.SelectedRows(i).Cells(3).Value & "',N'" & DataGridView1.SelectedRows(i).Cells(4).Value & "',N'" & DataGridView1.SelectedRows(i).Cells(7).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        AddReportView()
        Dim rpt = New Rpt_DataCustomer

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        Dim txtNameAr, txtNameEn, txtTitel As TextObject
        txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
        txtNameEn.Text = NameEnCompany
        txtTitel = rpt.Section1.ReportObjects("txtTitel")
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()
        Frm_PrintReports.Text = "تقرير بيانات العملاء"

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub btnAddVendors_Click(sender As Object, e As EventArgs) Handles btnAddVendors.Click
        If Trim(vndr.Text) = "" Then
            MsgBox("فضلاً أدخل اسم المورد", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            vndr.Focus() : Exit Sub
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * From vendors where Vendorname =N'" & vndr.Text & "'"
        dr = cmd.ExecuteReader()
        If dr.HasRows = True Then
            MsgBox("عفواً يوجد مورد مسجل مسبقا بنفس الاسم", MsgBoxStyle.Exclamation)
            Exit Sub
        End If

        Cls.insert("vendors", "Company_Branch_ID,Vendorname,addr,tel1,notes,valuereturns,vintinval,vndiscount,VnPay,vnamntcredit,vnamntdebit,UserName", "N'" & Company_Branch_ID & "',N'" & vndr.Text & "',N'" & addr.Text & "',N'" & tel1.Text & "',N'" & notes.Text & "',0,0,0,0,0,0,N'" & UserName & "'")

        CLEAR_ALL()
        MAXRECORD()
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)
        IM.VendorAccountTotal(vndr.Text.Trim)
    End Sub

    Private Sub btnAddBill_Import_Click(sender As Object, e As EventArgs) Handles btnAddBill_Import.Click
        CustomersName = vndr.Text
        Dim newForm As New List(Of Frmimport)
        newForm.Add(New Frmimport)
        newForm(0).Show()
    End Sub

    Private Sub txtCust_Code_TextChanged(sender As Object, e As EventArgs) Handles txtCust_Code.TextChanged
        MyVars.CheckNumber(txtCust_Code)
    End Sub

    Private Sub btnDeliverydriver_Click(sender As Object, e As EventArgs) Handles btnDeliverydriver.Click
        PanelDeliverydriver.Top = 58
        PanelDeliverydriver.Left = 5
        txtEmployeeName.Focus()
    End Sub

    Private Sub btnCloseDeliverydriver_Click(sender As Object, e As EventArgs) Handles btnCloseDeliverydriver.Click
        PanelDeliverydriver.Top = 2000
    End Sub

    Private Sub btnAddDeliverydriver_Click(sender As Object, e As EventArgs) Handles btnAddDeliverydriver.Click

        If txtEmployeeName.Text = "" Then MsgBox("فضلا ادخل إسم الموظف", MsgBoxStyle.Exclamation) : txtEmployeeName.Focus() : Exit Sub

        Dim EMPID As String = MaxRecordTables("Employees", "EMPID")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Employees(Company_Branch_ID,EMPID,NameEmployee,Address,Phone) values ("
        S = S & "N'" & Company_Branch_ID & "',N'" & EMPID & "',N'" & txtEmployeeName.Text.Trim & "',N'" & txtAddressEmp.Text.Trim & "',N'" & txtPhoneEmp.Text.Trim & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        txtEmployeeName.Text = ""
        txtAddressEmp.Text = ""
        txtPhoneEmp.Text = ""
        txtEmployeeName.Focus()
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployeeNameFind)

    End Sub

    Private Sub btnCustomerNewExcel_Click(sender As Object, e As EventArgs) Handles btnCustomerNewExcel.Click
        CustomerNewExcel.Tag = "Customer"
        CustomerNewExcel.ShowDialog()
    End Sub

    Private Sub btnPrintView_Click(sender As Object, e As EventArgs) Handles btnPrintView.Click

        Cls.GetDefaultPrinterBill()

        PanelPrint.Top = 177
    End Sub

    Private Sub btnClosePrint_Click(sender As Object, e As EventArgs) Handles btnClosePrint.Click
        PanelPrint.Top = 10000
    End Sub

    Private Sub btnPrintCustomerAll_Click(sender As Object, e As EventArgs) Handles btnPrintCustomerAll.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases (Company_Branch_ID,BILL_NO,CustomerName,itm_cat,itm_id,itm_name,totalpricebeforedisc,disc,totalpriceafterdisc,BEY,STAYING,Unity)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView1.SelectedRows(i).Cells(1).Value & "',N'" & DataGridView1.SelectedRows(i).Cells(2).Value & "',N'" & DataGridView1.SelectedRows(i).Cells(3).Value & "',N'" & DataGridView1.SelectedRows(i).Cells(4).Value & "',N'" & DataGridView1.SelectedRows(i).Cells(7).Value & "',N'" & DataGridView1.SelectedRows(i).Cells(8).Value & "',N'" & DataGridView1.SelectedRows(i).Cells(9).Value & "',N'" & DataGridView1.SelectedRows(i).Cells(10).Value & "',N'" & DataGridView1.SelectedRows(i).Cells(11).Value & "',N'" & DataGridView1.SelectedRows(i).Cells(12).Value & "',N'" & DataGridView1.SelectedRows(i).Cells(13).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        AddReportView()
        Dim rpt = New Rpt_DataCustomerALL

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        Dim txtNameAr, txtNameEn, txtTitel As TextObject
        txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
        txtNameEn.Text = NameEnCompany
        txtTitel = rpt.Section1.ReportObjects("txtTitel")
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()
        Frm_PrintReports.Text = "تقرير كل بيانات العملاء"

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub txtEmployeeName_KeyUp(sender As Object, e As KeyEventArgs) Handles txtEmployeeName.KeyUp
        If e.KeyCode = "13" Then
            txtAddressEmp.Focus()
        End If
    End Sub

    Private Sub txtAddressEmp_KeyUp(sender As Object, e As KeyEventArgs) Handles txtAddressEmp.KeyUp
        If e.KeyCode = "13" Then
            txtPhoneEmp.Focus()
        End If
    End Sub

    Private Sub txtPhoneEmp_KeyUp(sender As Object, e As KeyEventArgs) Handles txtPhoneEmp.KeyUp
        If e.KeyCode = "13" Then
            btnAddDeliverydriver.PerformClick()
        End If
    End Sub

    Private Sub cmbEmployeeNameFind_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbEmployeeNameFind.SelectedIndexChanged
        If cmbEmployeeNameFind.Text = "" Then Exit Sub
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Address,Phone from Employees where NameEmployee=N'" & cmbEmployeeNameFind.Text & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            txtAddressEmp.Text = dr("Address").ToString
            txtPhoneEmp.Text = dr("Phone").ToString
            txtEmployeeName.Text = cmbEmployeeNameFind.Text
        End If
    End Sub

    Private Sub btnDeleteEmp_Click(sender As Object, e As EventArgs) Handles btnDeleteEmp.Click
        If cmbEmployeeNameFind.SelectedIndex = -1 Then MsgBox("من فضلك اختر بيان صحيح", MsgBoxStyle.Exclamation) : Exit Sub

        Dim msg As New MsgBoxResult
        msg = (MsgBox("هل تريد بالفعل الحذف", MsgBoxStyle.Question + MsgBoxStyle.YesNo))
        If msg = MsgBoxResult.Yes Then
            Cls.delete("Employees", "NameEmployee=N'" & cmbEmployeeNameFind.Text & "'")
            MsgBox("تم الحذف بنجاح", MsgBoxStyle.Information)
        End If
        txtEmployeeName.Text = ""
        txtAddressEmp.Text = ""
        txtPhoneEmp.Text = ""
        txtEmployeeName.Focus()
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployeeNameFind)

    End Sub

    Private Sub Daily_Restrictions()
        Try
            Dim Account As String = ""
            Dim AccountCode As String = ""

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مقبوضات عملاء'"
            dr = cmd.ExecuteReader
            If dr.Read Then
                Account = dr("Link_AccountsTree")
                AccountCode = dr("ACCNumber")
            End If

            '========================================================================================

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from MOVES where MOVID =N'0'" : H = cmd.ExecuteScalar
            If H > 0 Then
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVES(MOVID,MOVRegNumber,MOVStatement,MOVDebtor,MOVCreditor,UserName ) values ("
                S = S & "N'0',N'0',N'0',N'0',N'0',N'0')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If


            Dim AccountTreasury As String = "" : Dim AccountCodeTreasury As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select ACCName,ACCNumber from AccountsTree where ACCName=N'الخزينة'" : dr = cmd.ExecuteReader
            If dr.Read Then
                AccountTreasury = dr("ACCName") : AccountCodeTreasury = dr("ACCNumber")
            End If

            Dim MOVRegNumber As String = Cls.MAXRECORD("MOVES", "MOVRegNumber")
            Dim MOVID As String = Cls.MAXRECORD("MOVES", "MOVID")
            Dim bill_no As String = Cls.MAXRECORD("Vst", "id") - 1

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MOVES(MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
            S = S & "N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Account & "',N'" & txtammnt.Text & "',N'" & txtammnt.Text & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If RadioButton1.Checked = True Then
                ' من حساب / الخزينة
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'" & txtammnt.Text & "',N'0',N'" & AccountTreasury & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' الى حساب / مقبوضات عملاء
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCode & "',N'" & Account & "',N'0',N'" & txtammnt.Text & "',N'" & Account & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

            End If

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub txtLimit_DrawDowns_Price_TextChanged(sender As Object, e As EventArgs) Handles txtLimit_DrawDowns_Price.TextChanged
        MyVars.CheckNumber(txtLimit_DrawDowns_Price)
    End Sub

    Private Sub txttaxn_TextChanged(sender As Object, e As EventArgs) Handles txttaxn.TextChanged
        MyVars.CheckNumber(txttaxn)
    End Sub

    Private Sub txttradn_TextChanged(sender As Object, e As EventArgs) Handles txttradn.TextChanged
        MyVars.CheckNumber(txttradn)
    End Sub

    Private Sub GetArch_Customers()
        '============== ArchiveManager ===============

        Dim TransactionType As String = "Arch_Customers"
        Dim OriginalID As String = ""

        OriginalID = Cls.MAXRECORD("Arch_Customers", "OriginalID")
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "insert into Arch_Customers (Cust_Code, Vendorname, addr, tel1, notes, VstDiscOther, BVstPay, BVstDiscount, valuereturns, vintinval, vndiscount, VnPay,  VnReceipts, vnamntcredit, vnamntdebit, addvintinval, ValueVAT, Limit_DrawDowns_Price, UserName, GeoArea_Code, Emp_Code, Mobile, Apartment, Role, Region, Mark, PriceType_ID, Company_Branch_ID, Total_Qunt, taxn, tradn, Bran_code) select Cust_Code, Vendorname, addr, tel1, notes, VstDiscOther, BVstPay, BVstDiscount, valuereturns, vintinval, vndiscount, VnPay,  VnReceipts, vnamntcredit, vnamntdebit, addvintinval, ValueVAT, Limit_DrawDowns_Price, UserName, GeoArea_Code, Emp_Code, Mobile, Apartment, Role, Region, Mark, PriceType_ID, Company_Branch_ID, Total_Qunt, taxn, tradn, Bran_code from Customers where Cust_Code =N'" & txtCust_Code.Text & "'" : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Arch_Customers set TransactionType=N'" & TransactionType & "',OriginalID=N'" & OriginalID & "',DeletedDate =N'" & Cls.C_date(DateTimePicker1.Text) & "',DeletedTime =N'" & Cls.get_time(True) & "',DeletedBy =N'" & UserName & "',DeleteReason =N'" & TransactionType & "' where Cust_Code =N'" & txtCust_Code.Text & "'" : cmd.ExecuteNonQuery()


        OriginalID = Cls.MAXRECORD("Arch_Sales_Bill", "OriginalID")
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "insert into Arch_Sales_Bill (bill_No, OrderID, Sheft_Number, Vendorname, bill_date, billtime, totalpricebeforedisc, disc, totalpriceafterdisc,  Stat, STAYING, BEY, SalesTax, notes, UserName, Image_Bill, EmpName, Driv_Name, Driv_CarNumber, KiloMeter, Supervisor_Reform, Recipient, Received_Date, Delivery_Date, DiscountTax, bill_NoTax, DeliveryService, ExpensesBill, RateValues, CreditPrevious, DebitPrevious, CreditCurrent, DebitCurrent, RateBankExpensesVisa, ValueBankExpensesVisa, StatusOrder, Company_Branch_ID, disc_type, ValueVAT, Treasury_Code, DiscountsValue, RateDriverDelivery, CloseSheft, BillTimeAmBm, QRCodeBill, CommercialIndustrialProfitsTax, PendingBill, CurrentBalanceCustVnd, PriceBeforeDiscValue, AutoSeriesVATActive) select bill_No, OrderID, Sheft_Number, Vendorname, bill_date, billtime, totalpricebeforedisc, disc, totalpriceafterdisc,  Stat, STAYING, BEY, SalesTax, notes, UserName, Image_Bill, EmpName, Driv_Name, Driv_CarNumber, KiloMeter, Supervisor_Reform, Recipient, Received_Date, Delivery_Date, DiscountTax, bill_NoTax, DeliveryService, ExpensesBill, RateValues, CreditPrevious, DebitPrevious, CreditCurrent, DebitCurrent, RateBankExpensesVisa, ValueBankExpensesVisa, StatusOrder, Company_Branch_ID, disc_type, ValueVAT, Treasury_Code, DiscountsValue, RateDriverDelivery, CloseSheft, BillTimeAmBm, QRCodeBill, CommercialIndustrialProfitsTax, PendingBill, CurrentBalanceCustVnd, PriceBeforeDiscValue, AutoSeriesVATActive from Sales_Bill where Vendorname =N'" & Vendor & "' and bill_No =N'جرد'" : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Arch_Sales_Bill set TransactionType=N'" & TransactionType & "',OriginalID=N'" & OriginalID & "',DeletedDate =N'" & Cls.C_date(DateTimePicker1.Text) & "',DeletedTime =N'" & Cls.get_time(True) & "',DeletedBy =N'" & UserName & "',DeleteReason =N'" & TransactionType & "' where Vendorname =N'" & Vendor & "' and bill_No =N'جرد'" : cmd.ExecuteNonQuery()


        '============== ArchiveManager ===============

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete  from vst where Vendorname =N'" & Vendor & "' and BillNo =N'جرد'" : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete  from Employee_Customers where Customers_Code =N'" & Cust_Code & "'" : cmd.ExecuteNonQuery()


        cmd.CommandText = "delete From  MOVES where bill_no =N'" & ItmID & "' and MOVStatement =N'مقبوضات عملاء'" : cmd.ExecuteNonQuery()
        cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()

    End Sub
End Class