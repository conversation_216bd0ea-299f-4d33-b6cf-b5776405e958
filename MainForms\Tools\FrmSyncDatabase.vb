﻿Imports System.Web.UI.WebControls
Imports SriLibs.DataSync.MSSQLSERVER
Imports Microsoft.Synchronization
Imports Microsoft.Synchronization.Data.SqlServer

Public Class FrmSyncDatabase

    Private Sub FrmSyncDatabase_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub

    Private Sub GetSyncDatabase()

        'Dim sourceConnString As String = "Data Source=SourceServer;Initial Catalog=SourceDatabase;Integrated Security=True"
        'Dim destConnString As String = "Data Source=DestinationServer;Initial Catalog=DestinationDatabase;Integrated Security=True"

        'Dim syncOrchestrator As New SyncOrchestrator()

        'Dim sourceProvider As New SqlSyncProvider("SourceScope", sourceConnString)
        'Dim destProvider As New SqlSyncProvider("DestinationScope", destConnString)

        'Dim table1 As New SyncTable("Table1")
        'table1.CreationOption = TableCreationOption.DropExistingOrCreateNewTable
        'table1.SyncDirection = SyncDirection.Bidirectional
        'table1.SyncGroup = "MySyncGroup"
        'table1.SyncMethod = SyncMethod.Snapshot

        'Dim table2 As New SyncTable("Table2")
        'table2.CreationOption = TableCreationOption.DropExistingOrCreateNewTable
        'table2.SyncDirection = SyncDirection.Bidirectional
        'table2.SyncGroup = "MySyncGroup"
        'table2.SyncMethod = SyncMethod.Snapshot

        'sourceProvider.Tables.Add(table1)
        'sourceProvider.Tables.Add(table2)

        'destProvider.Tables.Add(table1)
        'destProvider.Tables.Add(table2)

        'syncOrchestrator.LocalProvider = sourceProvider
        'syncOrchestrator.RemoteProvider = destProvider
        'syncOrchestrator.Direction = SyncDirectionOrder.UploadAndDownload

        'Dim syncStats As SyncOperationStatistics = syncOrchestrator.Synchronize()



        '        ' Set up the source and destination data sources
        '        Dim sourceDataSource = New SqlDataSource("Data Source=sourceServer;Initial Catalog=sourceDatabase;Integrated Security=True;")
        '        Dim destinationDataSource = New SqlDataSource("Data Source=destinationServer;Initial Catalog=destinationDatabase;Integrated Security=True;")

        '        ' Create a SyncConfiguration object
        '        Dim config As Object = New SyncConfiguration() With {
        '            .Source = New DataSourceInfo() With {
        '        .DataSource = sourceDataSource,
        '        .Tables = {"Customers", "Orders"}
        '    },
        '            .Destination = New DataSourceInfo() With {
        '        .DataSource = destinationDataSource,
        '        .Tables = {"Customers", "Orders"}
        '    },
        '    .SyncDirection = SyncDirection.TwoWay
        '}

        '        ' Create a SyncAgent object
        '        Dim agent = New SyncAgent(config)

        '        ' Register event handlers
        '        AddHandler agent.SyncProgress, Sub(sender, args)
        '                                           Console.WriteLine($"Sync progress: {args.ProgressPercentage}%")
        '                                       End Sub
        '        AddHandler agent.ApplyChangeFailed, Sub(sender, args)
        '                                                Console.WriteLine($"Failed to apply change: {args.ErrorMessage}")
        '                                            End Sub

        '        ' Start the sync operation
        '        Dim stats = agent.Sync()
        '        Console.WriteLine($"Sync completed: {stats.TotalChangesApplied} changes applied.")

    End Sub

    Private Sub btnSyncToServer_Click(sender As Object, e As EventArgs) Handles btnSyncToServer.Click
        Dim initializer As New Initializer()
        Dim Synchronizer As Synchronizer = initializer.Initiate(txtLocalConnectionString.Text, txtServerConnectionString.Text)
        Dim syncInfo As SyncInfo = Synchronizer.Sync(SyncDirections.SyncToServer)

        Dim inserted As String = syncInfo.Inserted
        Dim updated As String = syncInfo.Updated
        Dim deleted As String = syncInfo.Deleted
        Dim status As String = syncInfo.Status
        Dim duration As String = syncInfo.Duration.ToString()


        MessageBox.Show("Inserted : " + syncInfo.Inserted.ToString() + Environment.NewLine + "Updated : " + syncInfo.Updated.ToString() + Environment.NewLine + "Deleted : " + syncInfo.Deleted.ToString() + Environment.NewLine + "Status : " + syncInfo.Status.ToString() + Environment.NewLine + "\n" + syncInfo.Duration.ToString())
    End Sub

    Private Sub btnSyncFromServer_Click(sender As Object, e As EventArgs) Handles btnSyncFromServer.Click
        Dim initializer As New Initializer()
        Dim Synchronizer As Synchronizer = initializer.Initiate(txtServerConnectionString.Text, txtLocalConnectionString.Text)
        Dim syncInfo As SyncInfo = Synchronizer.Sync(SyncDirections.SyncFromServer)

        Dim inserted As String = syncInfo.Inserted
        Dim updated As String = syncInfo.Updated
        Dim deleted As String = syncInfo.Deleted
        Dim status As String = syncInfo.Status
        Dim duration As String = syncInfo.Duration.ToString()


        MessageBox.Show("Inserted : " + syncInfo.Inserted.ToString() + Environment.NewLine + "Updated : " + syncInfo.Updated.ToString() + Environment.NewLine + "Deleted : " + syncInfo.Deleted.ToString() + Environment.NewLine + "Status : " + syncInfo.Status.ToString() + Environment.NewLine + "\n" + syncInfo.Duration.ToString())

    End Sub

    Private Sub btnFlashSyncToServer_Click(sender As Object, e As EventArgs) Handles btnFlashSyncToServer.Click
        Dim initializer As New Initializer()
        Dim Synchronizer As Synchronizer = initializer.Initiate(txtLocalConnectionString.Text, txtServerConnectionString.Text)
        Dim syncInfo As SyncInfo = Synchronizer.Sync(SyncDirections.SyncToServer)

        MessageBox.Show("Inserted : " + syncInfo.Inserted.ToString() + Environment.NewLine + "Updated : " + syncInfo.Updated.ToString() + Environment.NewLine + "Deleted : " + syncInfo.Deleted.ToString() + Environment.NewLine + "Status : " + syncInfo.Status.ToString() + Environment.NewLine + "\n" + syncInfo.Duration.ToString())


        Dim initializer2 As New Initializer()
        Dim Synchronizer2 As Synchronizer = initializer2.Initiate(txtServerConnectionString.Text, txtLocalConnectionString.Text)
        Dim syncInfo2 As SyncInfo = Synchronizer2.Sync(SyncDirections.SyncFromServer)

        Dim inserted As String = syncInfo2.Inserted
        Dim updated As String = syncInfo2.Updated
        Dim deleted As String = syncInfo2.Deleted
        Dim status As String = syncInfo2.Status
        Dim duration As String = syncInfo2.Duration.ToString()


        MessageBox.Show("Inserted : " + syncInfo2.Inserted.ToString() + Environment.NewLine + "Updated : " + syncInfo2.Updated.ToString() + Environment.NewLine + "Deleted : " + syncInfo2.Deleted.ToString() + Environment.NewLine + "Status : " + syncInfo2.Status.ToString() + Environment.NewLine + "\n" + syncInfo2.Duration.ToString())

    End Sub
End Class