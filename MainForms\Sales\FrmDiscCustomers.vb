﻿Public Class FrmDiscCustomers

    Private Sub FrmDiscCustomers_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Me.MdiParent = MdiParent
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        DataGridView1.DataSource = ""
        DataGridView1.DataSource = Nothing
        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", ComboBox1)

        End If
        ComboBox1.Items.Add("نقداً")

        GetData()
        GetDateNotBeenActivatedPrograms(DateTimePicker1)
    End Sub

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT id, Vendorname AS [العميل],amnt AS [المبلغ], pdate AS [التاريخ],det as الملاحظات FROM Vst_disc  order by 1"
        dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(3).Value)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(3).Value = SM
        Next
        DataGridView1.Columns(0).Width = 0
        DataGridView1.Columns(4).Width = 150

        Dim SM2 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM2 = SM2 + DataGridView1.Rows(i).Cells(2).Value
        Next
        Label6.Text = "جملة الخصومات السابقة لكل العملاء = " & SM2
        Label6.Left = GroupBox4.Width - Label6.Width - 10

    End Sub

    Private Sub TextBox6_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TextBox6.TextChanged
        If Not IsNumeric(TextBox6.Text) Then
            System.Windows.Forms.SendKeys.Send("{backspace}")
        End If
    End Sub

    Private Sub CheckBox1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox1.CheckedChanged
        If CheckBox1.Checked = True Then
            TextBox6.Enabled = False
        Else
            TextBox6.Enabled = True
        End If
    End Sub

    Private Sub ComboBox1_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ComboBox1.SelectedIndexChanged
        Label5.Text = "جملة الخصومات لهذا العميل = " & IM.VnDiscF(ComboBox1.Text.Trim)
        Label5.Left = GroupBox2.Width - Label5.Width - 10

    End Sub

    Private Sub amnt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles amnt.TextChanged
        If Not IsNumeric(amnt.Text) Then
            System.Windows.Forms.SendKeys.Send("{backspace}")
        End If
    End Sub

    Private Sub th1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles th1.TextChanged

        If Not IsNumeric(th1.Text) Then
            System.Windows.Forms.SendKeys.Send("{backspace}")
        End If
        th3.Text = (Val(th2.Text) / 100) * Val(th1.Text)
    End Sub

    Private Sub th2_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles th2.TextChanged
        If Not IsNumeric(th2.Text) Then
            System.Windows.Forms.SendKeys.Send("{backspace}")
        End If
        th3.Text = (Val(th2.Text) / 100) * Val(th1.Text)
    End Sub

    Private Sub th3_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles th3.TextChanged
        If Not IsNumeric(th3.Text) Then
            System.Windows.Forms.SendKeys.Send("{backspace}")
        End If
        th3.Text = (Val(th2.Text) / 100) * Val(th1.Text)
    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        amnt.Text = th3.Text

    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If amnt.Text = "" Then
            MsgBox("من فضلك ادخل المبلغ", MsgBoxStyle.Critical)
            Exit Sub
        End If
        If ComboBox1.Text = "" Then
            MsgBox("من فضلك اختر اسم العميل", MsgBoxStyle.Critical)
            Exit Sub
        End If
        Dim XNO As String
        If CheckBox1.Checked = True Then
            XNO = "خصم بدون فاتورة"
        Else

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "SELECT COUNT(*) FROM Sales_Bill WHERE Vendorname =N'" & ComboBox1.Text & "' AND bill_No =N'" & TextBox6.Text & "'"
            H = cmd.ExecuteScalar
            If H = 0 Then
                MsgBox("لا يوجد فاتورة لهذا العميل بهذا الرقم", MsgBoxStyle.Exclamation)
                TextBox6.Focus()
                Exit Sub
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "SELECT SUM(totalpricebeforedisc) AS SS FROM Sales_Bill WHERE bill_No =N'" & TextBox6.Text & "' AND Vendorname =N'" & ComboBox1.Text & "' GROUP BY bill_No"
            dr = cmd.ExecuteReader
            dr.Read()
            Dim XVAL As Integer
            XVAL = dr("SS")
            If XVAL < Val(amnt.Text) Then
                Dim RSLT As String
                RSLT = MsgBox("قيمة الخصم أكبر من قيمة الفاتورة هل تريد الحفظ على هذه الطريقة", MsgBoxStyle.Question + MsgBoxStyle.YesNo)
                If RSLT = MsgBoxResult.No Then Exit Sub
            End If
            XNO = TextBox6.Text
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Vst_disc (Company_Branch_ID,Vendorname,amnt,pdate,VND_XTM,det,TIN_NO,UserName,Treasury_Code) values (N'" & Company_Branch_ID & "',N'" & ComboBox1.Text.Trim & "'," & Val(amnt.Text) & ",N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Cls.get_time(True) & "',N'" & det.Text & "',N'" & XNO & "',N'" & UserName & "',N'" & Treasury_Code & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()
        IM.CustomerAccountTotal(ComboBox1.Text)

        '===============================================================================
        Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
        If UseAccounts = "YES" Then
            Daily_Restrictions()
        End If
        '===============================================================================

        Dim CurrentBalanceCustVnd As String = GetCurrentBalanceCustVnd(ComboBox1.Text)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Vst_disc set CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where TIN_NO =N'" & TextBox6.Text & "' and Vendorname =N'" & ComboBox1.Text & "' and pdate =N'" & Cls.C_date(DateTimePicker1.Text) & "'" : cmd.ExecuteNonQuery()

        amnt.Text = ""
        ComboBox1.SelectedIndex = -1
        det.Text = ""
        GetData()
        MsgBox("تم تسجيل الخصم بنجاح", MsgBoxStyle.Information)
    End Sub

    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If

        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim ItmID As String
        ItmID = DataGridView1.SelectedRows(0).Cells(0).Value

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete From  Vst_disc where id =N'" & ItmID & "'" : cmd.ExecuteNonQuery()

        cmd.CommandText = "delete From  MOVES where bill_no =N'" & ItmID & "' and MOVStatement =N'خصومات عملاء'" : cmd.ExecuteNonQuery()
        cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
        cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'خصومات عملاء'" : cmd.ExecuteNonQuery()

        GetData()
    End Sub

    Private Sub Daily_Restrictions()
        Dim Account As String = ""
        Dim AccountCode As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'خصومات عملاء'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            Account = dr("Link_AccountsTree")
            AccountCode = dr("ACCNumber")
        End If


        '========================================================================================


        Dim AccountTreasury As String = "" : Dim AccountCodeTreasury As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select ACCName,ACCNumber from AccountsTree where ACCName=N'الخزينة'" : dr = cmd.ExecuteReader
        If dr.Read Then
            AccountTreasury = dr("ACCName") : AccountCodeTreasury = dr("ACCNumber")
        End If


        Dim MOVRegNumber As String = Cls.MAXRECORD("MOVES", "MOVRegNumber")
        Dim MOVID As String = Cls.MAXRECORD("MOVES", "MOVID")
        Dim bill_no As String = Cls.MAXRECORD("Vst_disc", "id") - 1


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
        S = S & "N'" & bill_no & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Account & "',N'" & amnt.Text & "',N'" & amnt.Text & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        ' من حساب / خصومات العملاء
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
        S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCode & "',N'" & Account & "',N'" & amnt.Text & "',N'0',N'" & Account & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        ' الى حساب / الخزينة
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
        S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'0',N'" & amnt.Text & "',N'" & AccountTreasury & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()
    End Sub

End Class