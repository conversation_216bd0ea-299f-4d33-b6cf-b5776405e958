﻿Public Class Frm_Other_Income_Category
    Dim MaxRecoedCode As String
    Dim ActionGrid As Boolean = False

    Private Sub btnNew_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnNew.Click

        ClearAll()
        Cls.fill_combo("Other_Income_Category", "Income_Category_Name", cmbFind)

        MAXRECORD("Other_Income_Category", "Income_Category_ID")
        txtCode.Text = MaxRecoedCode
        txtName.Focus()
    End Sub

    Private Sub ClearAll()
        txtCode.Text = ""
        txtName.Text = ""
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        If Trim(txtCode.Text) = "" Then MsgBox("من فضلك ادخل الكود", MsgBoxStyle.Exclamation) : Exit Sub
        If Trim(txtName.Text) = "" Then MsgBox("من فضلك ادخل الاسم", MsgBoxStyle.Exclamation) : Exit Sub
        If Cls.Check_Field_Value("Other_Income_Category", "Income_Category_Name", txtName.Text) Then MsgBox("عفوا الاسم مسجل من قبل", MsgBoxStyle.Exclamation) : Exit Sub
        If Cls.Check_Field_Value("Other_Income_Category", "Income_Category_ID", txtCode.Text) Then MsgBox("عفوا الكود مسجل من قبل", MsgBoxStyle.Exclamation) : Exit Sub

        Cls.insert("Other_Income_Category", "Income_Category_ID,Income_Category_Name", "'" & txtCode.Text & "',N'" & txtName.Text & "'")
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)

        ClearAll()

        Cls.fill_combo("Other_Income_Category", "Income_Category_Name", cmbFind)

        MAXRECORD("Other_Income_Category", "Income_Category_ID")
        txtCode.Text = MaxRecoedCode
        txtName.Focus()
    End Sub

    Private Sub frmAccountsMain_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Cls.fill_combo("Other_Income_Category", "Income_Category_Name", cmbFind)

        MAXRECORD("Other_Income_Category", "Income_Category_ID")
        txtCode.Text = MaxRecoedCode
        txtName.Focus()
    End Sub

    Private Sub MAXRECORD(ByVal Tables As String, ByVal Code As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " & Tables & ""
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            MaxRecoedCode = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" & Code & " As int)) as mb FROM " & Tables & " where " & Code & " <> ''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            MaxRecoedCode = sh + 1
        End If

    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If Trim(txtCode.Text) = "" Then MsgBox("من فضلك ادخل الكود", MsgBoxStyle.Exclamation) : Exit Sub
        If Trim(txtName.Text) = "" Then MsgBox("من فضلك ادخل الاسم", MsgBoxStyle.Exclamation) : Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Other_Income_Category set Income_Category_Name =N'" & txtName.Text & "' where Income_Category_ID =N'" & txtCode.Text & "'" : cmd.ExecuteNonQuery()

        MsgBox("تم التعديل بنجاح", MsgBoxStyle.Information)

        ClearAll()

        Cls.fill_combo("Other_Income_Category", "Income_Category_Name", cmbFind)

        MAXRECORD("Other_Income_Category", "Income_Category_ID")
        txtCode.Text = MaxRecoedCode
        txtName.Focus()
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If cmbFind.SelectedIndex = -1 Then MsgBox("من فضلك اختر بيان صحيح", MsgBoxStyle.Exclamation) : Exit Sub

        Dim msg As New MsgBoxResult
        msg = (MsgBox("هل تريد بالفعل الحذف", MsgBoxStyle.Question + MsgBoxStyle.YesNo))
        If msg = MsgBoxResult.Yes Then
            Cls.delete("Other_Income_Category", "Income_Category_Name=N'" & cmbFind.Text & "'")
            MsgBox("تم الحذف بنجاح", MsgBoxStyle.Information)
        End If

        ClearAll()

        Cls.fill_combo("Other_Income_Category", "Income_Category_Name", cmbFind)

        MAXRECORD("Other_Income_Category", "Income_Category_ID")
        txtCode.Text = MaxRecoedCode
        txtName.Focus()
    End Sub

    Private Sub cmbFind_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbFind.SelectedIndexChanged
        ActionGrid = True
        If cmbFind.Text = "" Then Exit Sub
        Dim Government_Code As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT * from Other_Income_Category where Income_Category_Name = N'" & cmbFind.Text & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            txtCode.Text = dr("Income_Category_ID")
            txtName.Text = dr("Income_Category_Name").ToString()
        End If
        ActionGrid = False
    End Sub

End Class