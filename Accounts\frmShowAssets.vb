﻿Imports CrystalDecisions.CrystalReports.Engine
Public Class frmShowAssets
    Dim WithEvents BS As New BindingSource

    Private Sub frmShowAssets_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Bra.Fil("Assets", "Original", cmbOriginal)
        GetData()
    End Sub

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT AssID AS [مسلسل],Original AS [الاصل], TypeAssets AS [نوع الاصل], DatePurchase AS [التاريخ], OriginalValue AS [القيمة الاصلية],RateDepreciation as [نسبة الاهلاك],NetValue as [صافى القيمة],Notes as [ملاحظات] FROM Assets where AssID <> N''"

        If ChbAll.Checked = False Then
            S = S & "and Original =N'" & cmbOriginal.Text.Trim & "'"
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & "and DatePurchase >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and DatePurchase <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

        'DataGridView1.Columns(0).Width = 40
        'DataGridView1.Columns(1).Width = 40
        'DataGridView1.Columns(2).Width = 60
        'DataGridView1.Columns(3).Width = 60
        'DataGridView1.Columns(4).Width = 60
    End Sub

    Private Sub ChbAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChbAll.CheckedChanged
        If ChbAll.Checked = True Then
            cmbOriginal.Enabled = False
            cmbOriginal.SelectedIndex = -1
        ElseIf ChbAll.Checked = False Then
            cmbOriginal.Enabled = True
        End If
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub btnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnShow.Click
        GetData()
    End Sub

    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click
        If ChbAll.Checked = False Then
            If cmbOriginal.Text = "" Then MsgBox("فضلا أدخل رقم القيد", MsgBoxStyle.Exclamation) : cmbOriginal.Focus() : Exit Sub
        End If
        Dim txtFROMDate As String
        Dim txtToDate As String
        Dim f As New Frm_PrintReports
        Dim rpt As New rptAssets
        Dim txt As TextObject
        txtFROMDate = Format(Me.DateTimePicker1.Value, "yyy, MM, dd, 00, 00, 000")
        txtToDate = Format(Me.DateTimePicker2.Value, "yyy, MM, dd, 00, 00, 00")
        'On Error Resume Next
        GETSERVERNAMEANDDATABASENAME(rpt, DataBaseName, PasswordServer, UserNameServer)
        If ChkWithoutDate.Checked = False Or ChbAll.Checked = False Then
            If ChbAll.Checked = False Then
                rpt.RecordSelectionFormula = "{Assets.DatePurchase} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")AND{Assets.Original} =N'" & cmbOriginal.Text & "'"
                txt = rpt.Section1.ReportObjects("Text9")
                txt.Text = "خلال الفترة من" & Format(Me.DateTimePicker1.Value, "dd - MM - yyyy") & " الى " & Format(Me.DateTimePicker2.Value, "dd - MM - yyyy")
            Else
                rpt.RecordSelectionFormula = "{Assets.DatePurchase} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")"
                txt = rpt.Section1.ReportObjects("Text9")
                txt.Text = "خلال الفترة من" & Format(Me.DateTimePicker1.Value, "dd - MM - yyyy") & " الى " & Format(Me.DateTimePicker2.Value, "dd - MM - yyyy")
            End If
        End If
        AddReportView()
        Dim txtname, txtNameAr, txtNameEn As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير بالاصول"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        f.CrystalReportViewer1.ReportSource = rpt
        f.CrystalReportViewer1.Zoom(95%)
        f.CrystalReportViewer1.RefreshReport()
        f.Text = "تقرير بالاصول"
        f.Show()
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim NumberID As String
            NumberID = DataGridView1.SelectedRows(0).Cells(0).Value


            cmd.CommandText = "delete from Assets where AssID =N'" & NumberID & "'" : cmd.ExecuteNonQuery()

            cmd.CommandText = "delete From  MOVES where bill_no =N'" & NumberID & "' and MOVStatement =N'مجمع اهلاك الاصول الثابتة'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & NumberID & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & NumberID & "' and MOVDNameAccount =N'مجمع اهلاك الاصول الثابتة'" : cmd.ExecuteNonQuery()


        Next
        GetData()
    End Sub

    Private Sub btnEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnEdit.Click
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        EditItmId = DataGridView1.SelectedRows(0).Cells(0).Value
        Action = "Edit"
        frmAssets.Show()
    End Sub
End Class