﻿Imports Microsoft.SqlServer.Management.Smo
Imports Microsoft.SqlServer.Management.Common
Imports System.Data.SqlClient

Public Class RestoreBackup

    Public Shared Sub BackUp(path As String, connectionString As String)
        Dim connection As ServerConnection
        Dim builder As New SqlConnectionStringBuilder(connectionString)
        If NetworkName = "No" Then
            connection = New ServerConnection(builder.DataSource)
        End If
        If NetworkName = "Yes" Then
            connection = New ServerConnection(builder.DataSource, builder.UserID, builder.Password)
        End If
        If NetworkName = "Source" Then
            connection = New ServerConnection(builder.DataSource)
        End If
        If NetworkName = "LocalDB" Then
            connection = New ServerConnection(builder.DataSource)
        End If

        Dim sqlServer As New Server(connection)

        Dim bk As New Backup
        bk.Database = builder.InitialCatalog
        bk.Action = BackupActionType.Database
        bk.BackupSetDescription = "Full backup of " & bk.Database
        bk.BackupSetName = bk.Database
        bk.Devices.AddDevice(path, DeviceType.File)
        bk.Incremental = False
        bk.LogTruncation = BackupTruncateLogType.Truncate
        bk.SqlBackup(sqlServer)

    End Sub

    Public Shared Sub Restore(path As String, connectionString As String)
        Dim connection As ServerConnection
        Dim builder As New SqlConnectionStringBuilder(connectionString)
        If NetworkName = "No" Then
            connection = New ServerConnection(builder.DataSource)
        End If
        If NetworkName = "Yes" Then
            connection = New ServerConnection(builder.DataSource, builder.UserID, builder.Password)
        End If
        If NetworkName = "Source" Then
            connection = New ServerConnection(builder.DataSource)
        End If
        If NetworkName = "LocalDB" Then
            connection = New ServerConnection(builder.DataSource)
        End If
        Dim sqlServer As New Server(connection)

        Dim rs As New Restore
        rs.Database = builder.InitialCatalog
        rs.NoRecovery = False
        rs.Action = BackupActionType.Database
        rs.ReplaceDatabase = True
        rs.Devices.AddDevice(path, DeviceType.File)
        sqlServer.KillAllProcesses(builder.InitialCatalog)
        rs.Wait()
        rs.SqlRestore(sqlServer)

    End Sub
End Class
