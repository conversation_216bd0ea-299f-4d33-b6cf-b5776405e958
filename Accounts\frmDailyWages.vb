﻿Imports System.Data.Common
Imports CrystalDecisions.CrystalReports.Engine
Public Class frmDailyWages
    Dim WithEvents BS As New BindingSource
    Dim ds1 As DataSet = New DataSet

    Private Sub frmDailyWages_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        On Error Resume Next
        If Action = "Edit" Then
            FillData()
        Else
            MAXRECORD()
            txtRegistrationNo.Focus()
        End If
        PanelSearch.Top = 5000
    End Sub
    Private Sub DataGridView1_DataError(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewDataErrorEventArgs)
        On Error Resume Next
        Exit Sub
    End Sub
    Private Sub MAXRECORD()
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select * from MOVES"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Me.txtSeries.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(MOVID As float)) as mb FROM MOVES"
            dr = cmd.ExecuteReader
            dr.Read()
            Dim sh As Long
            sh = dr("mb")
            Me.txtSeries.Text = sh + 1
        End If
    End Sub

    Private Sub txtDebtor_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtDebtor.KeyUp
        If e.KeyCode = 13 Then
            txtCreditor.Focus()
        End If
    End Sub

    Private Sub txtDebtor_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtDebtor.TextChanged
        MyVars.CheckNumber(txtDebtor)
    End Sub

    Private Sub txtCreditor_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtCreditor.KeyUp
        If e.KeyCode = 13 Then
            txtNAMStatement.Focus()
        End If
    End Sub

    Private Sub txtCreditor_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtCreditor.TextChanged
        MyVars.CheckNumber(txtCreditor)
    End Sub

    Private Sub txtNumberAccount_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtNumberAccount.KeyUp
        If e.KeyCode = 13 Then
            GetDataNumberAccount()
        End If
    End Sub

    Private Sub txtNumberAccount_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtNumberAccount.TextChanged
        MyVars.CheckNumber(txtNumberAccount)
    End Sub

    Private Sub txtRegistrationNo_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtRegistrationNo.KeyUp
        If e.KeyCode = 13 Then
            txtRegStatement.Focus()
        End If
    End Sub

    Private Sub txtRegistrationNo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtRegistrationNo.TextChanged
        MyVars.CheckNumber(txtRegistrationNo)
    End Sub
    Private Sub GetDataNumberAccount()
        Bol = True
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select ACCName from AccountsTree where ACCNumber=N'" & txtNumberAccount.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            txtNameAccount.Text = dr(0)
        End If
        txtDebtor.Focus()
        txtDebtor.SelectAll()
        Bol = False
    End Sub

    Private Sub txtNAMStatement_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtNAMStatement.KeyUp
        If e.KeyCode = 13 Then
            btnAdd.PerformClick()
        End If
    End Sub

    Private Sub txtSeries_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtSeries.KeyUp
        If e.KeyCode = 13 Then
            dtpDate.Focus()
        End If
    End Sub

    Private Sub txtSeries_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSeries.TextChanged
        MyVars.CheckNumber(txtSeries)
    End Sub

    Private Sub txtRegStatement_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtRegStatement.KeyUp
        If e.KeyCode = 13 Then
            txtNumberAccount.Focus()
        End If
    End Sub

    Private Sub dtpDate_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDate.KeyUp
        If e.KeyCode = 13 Then
            txtRegistrationNo.Focus()
        End If
    End Sub

    Private Sub btnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAdd.Click
        If ValidateTextAdd() = False Then Exit Sub
        If Action = "Edit" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete from TmpBillsalData where itm_id =N'" & txtNumberAccount.Text.Trim & "'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into TmpBillsalData (bill_no,itm_id,itm_cat,price,totalprice,itm_Unity)"
            S = S & " values (N'" & txtRegistrationNo.Text & "',N'" & txtNumberAccount.Text & "',N'" & txtNameAccount.Text & "',N'" & txtDebtor.Text.Trim & "',"
            S = S & "N'" & txtCreditor.Text.Trim & "',N'" & txtNAMStatement.Text.Trim & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select  itm_id as [رقم الحساب] ,itm_cat AS [أسم الحساب],price as [مدين],totalprice as [دائن],itm_Unity as [بيان الحركة] from TmpBillsalData where bill_no =N'" & EditItmId & "'"
            dr = cmd.ExecuteReader
            Dgv_Add.DataSource = Cls.PopulateDataView(dr)
        Else
            Dgv_Add.DataSource = Fn_AddBill(txtNumberAccount.Text, txtNameAccount.Text, txtDebtor.Text, txtCreditor.Text, txtNAMStatement.Text)
        End If
        DTV_Grid()
        ClearAdd()
        SumAllPrice()
    End Sub
    Function ValidateTextAdd() As Boolean
        If txtCreditor.Text = "" Then txtCreditor.Text = "0"
        If txtDebtor.Text = "" Then txtDebtor.Text = "0"
        If txtRegistrationNo.Text = "" Then MsgBox("فضلا أدخل رقم القيد", MsgBoxStyle.Exclamation) : txtRegistrationNo.Focus() : Return False
        If txtNumberAccount.Text = "" Then MsgBox("فضلا ادخل رقم الحساب", MsgBoxStyle.Exclamation) : txtNumberAccount.Focus() : Return False
        If txtNameAccount.Text = "" Then MsgBox("فضلا ادخل أسم الحساب", MsgBoxStyle.Exclamation) : txtNameAccount.Focus() : Return False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from MOVES where MOVRegNumber =N'" & txtRegistrationNo.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H > 0 Then
            If Action = "Edit" Then
                If txtRegistrationNo.Text.Trim <> EditItmId Then
                    MsgBox("رقم القيد مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtRegistrationNo.Focus() : Return False
                End If
            Else
                MsgBox("رقم القيد مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtRegistrationNo.Focus() : Return False
            End If
        End If
        If Action = "Edit" Then
        Else
            For i As Integer = 0 To Dgv_Add.Rows.Count - 1
                If Dgv_Add.Rows(i).Cells(1).Value = txtNumberAccount.Text.Trim Then MsgBox("حساب مكرر بنفس الفاتورة", MsgBoxStyle.Exclamation) : txtNumberAccount.Focus() : Return False
            Next
        End If
        If Val(Me.txtTotalDebtor.Text) <> Val(Me.txtTotalCreditor.Text) Then
            MessageBox.Show("اجمالى الطرف المدين لايساوى اجمالى الطرف الدائن", "حفظ سجل", MessageBoxButtons.OK, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading)
        End If
        Return True
    End Function

    Dim Dt_AddBill As New DataTable
    Friend Function Fn_AddBill(ByVal Col_Number As String, ByVal Col_Name As String, ByVal Col_Debtor As Double, ByVal Col_Creditor As Double _
, ByVal Col_Stat As String) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("رقم الحساب", GetType(String))
            Dt_AddBill.Columns.Add("أسم الحساب", GetType(String))
            Dt_AddBill.Columns.Add("مدين", GetType(Double))
            Dt_AddBill.Columns.Add("دائن", GetType(Double))
            Dt_AddBill.Columns.Add("بيان الحركة", GetType(String))
        End If

        DTV_Width()
        Dt_AddBill.Rows.Add(Col_Number, Col_Name, Col_Debtor, Col_Creditor, Col_Stat)
        Return Dt_AddBill
    End Function

    Friend Sub DTV_Width()
        If Dgv_Add.Rows.Count > 0 Then
            DTV_Grid()
        End If
    End Sub
    Private Sub DTV_Grid()
        Dgv_Add.Columns(0).Width = 80
        Dgv_Add.Columns(1).Width = 100
        Dgv_Add.Columns(2).Width = 65
        Dgv_Add.Columns(3).Width = 65
        Dgv_Add.Columns(4).Width = 130
    End Sub
    Private Sub ClearAdd()
        txtNameAccount.Text = ""
        txtNumberAccount.Text = ""
        txtDebtor.Text = ""
        txtCreditor.Text = ""
        txtNAMStatement.Text = ""
        txtNumberAccount.Focus()
    End Sub
    Private Sub SumAllPrice()
        Dim SM As Double
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            SM = SM + Dgv_Add.Rows(i).Cells(2).Value
        Next
        txtTotalDebtor.Text = SM

        Dim SM2 As Double
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            SM2 = SM2 + Dgv_Add.Rows(i).Cells(3).Value
        Next
        txtTotalCreditor.Text = SM2
    End Sub

    Private Sub SAVEBUTTON_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles SAVEBUTTON.Click
        If ValidateTextSave() = False Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If Action = "Edit" Then
            cmd.CommandText = "delete From  MOVES where MOVRegNumber =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  MOVESDATA where MOVRegNumber =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
        Else
            Dim x As String = MsgBox("هل تريد بالفعل إتمام عملية القيود اليومية", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
            If x = vbCancel Then Exit Sub
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into MOVES(MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName,Treasury_Code) values ("
        S = S & "N'" & txtSeries.Text.Trim & "' ,N'" & txtRegistrationNo.Text.Trim & "' ,N'" & Cls.C_date(dtpDate.Text) & "',"
        S = S & "N'" & txtRegStatement.Text.Trim & "',N'" & txtTotalDebtor.Text.Trim & "',N'" & txtTotalCreditor.Text.Trim & "',N'" & UserName & "',N'" & Treasury_Code & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To Dgv_Add.Rows.Count - 1
            S = "insert into MOVESDATA (MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName,Treasury_Code)  values ("
            S = S & "N'" & txtRegistrationNo.Text.Trim & "',N'" & Cls.C_date(dtpDate.Text) & "',N'" & Dgv_Add.Rows(i).Cells(0).Value & "',"
            S = S & "N'" & Dgv_Add.Rows(i).Cells(1).Value & "',N'" & Dgv_Add.Rows(i).Cells(2).Value & "',N'" & Dgv_Add.Rows(i).Cells(3).Value & "',"
            S = S & "N'" & Dgv_Add.Rows(i).Cells(4).Value & "',N'" & UserName & "',N'" & Treasury_Code & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        If Action = "Edit" Then
            Action = "Add"
            Me.Close()
            frmShowDailyWages.btnShow.PerformClick()
        Else
            MsgBox("تمت عملية الحفظ بنجاح", MsgBoxStyle.Information)
            ClearSave()
            MAXRECORD()
        End If
        If chkprint.Checked = True Then
            PrintReport()
        End If
    End Sub
    Function ValidateTextSave() As Boolean
        If txtRegistrationNo.Text = "" Then MsgBox("فضلا أدخل رقم القيد", MsgBoxStyle.Exclamation) : txtRegistrationNo.Focus() : Return False
        If Dgv_Add.Rows.Count < 1 Then MsgBox("فضلا أكمل بيانات القيد", MsgBoxStyle.Exclamation) : Return False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from MOVES where MOVRegNumber =N'" & txtRegistrationNo.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H > 0 Then
            If Action = "Edit" Then
                If txtRegistrationNo.Text.Trim <> EditItmId Then
                    MsgBox("رقم الفاتورة مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtRegistrationNo.Focus() : Return False
                End If
            Else
                MsgBox("رقم القيد مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtRegistrationNo.Focus() : Return False
            End If
        End If

        If Val(Me.txtTotalDebtor.Text) <> Val(Me.txtTotalCreditor.Text) Then
            MessageBox.Show("اجمالى الطرف المدين لايساوى اجمالى الطرف الدائن", "حفظ سجل", MessageBoxButtons.OK, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading)
            Return False
        End If

        Return True
    End Function
    Private Sub PrintReport()

    End Sub
    Private Sub ClearSave()
        Cls.clear(Me)
        Dt_AddBill.Rows.Clear()
    End Sub
    Private Sub FillData()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select MOVDNameAccount AS [أسم الحساب], MOVNumberAccount as [رقم الحساب] ,MOVDDebtor as [مدين],MOVDCreditor as [دائن],MOVDNAMStatement as [بيان الحركة] from MOVESDATA where MOVRegNumber =N'" & EditItmId & "'"
        dr = cmd.ExecuteReader
        Dgv_Add.DataSource = Cls.PopulateDataView(dr)
        DTV_Grid()


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,MOVID from MOVES where MOVRegNumber =N'" & EditItmId & "'"
        dr = cmd.ExecuteReader : dr.Read()
        Dim XRegNumber, XVDate, XStatement, XDebtor, XCreditor, XID As String
        XRegNumber = dr("MOVRegNumber")
        XVDate = dr("MOVDate")
        XStatement = dr("MOVStatement")
        XDebtor = dr("MOVDebtor")
        XCreditor = dr("MOVCreditor")
        XID = dr("MOVID")


        txtRegistrationNo.Text = XRegNumber
        dtpDate.Text = XVDate
        txtRegStatement.Text = XStatement
        txtTotalDebtor.Text = XDebtor
        txtTotalCreditor.Text = XCreditor
        txtSeries.Text = XID
    End Sub
    Dim RNXD As Integer
    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim ItmID As String
        ItmID = Dgv_Add.SelectedRows(0).Cells(0).Value
        RNXD = Dgv_Add.CurrentRow.Index
        Dgv_Add.Rows.RemoveAt(RNXD) : SumAllPrice()
        cmd.CommandText = "delete from TmpBillsalData where itm_id =N'" & ItmID & "'" : cmd.ExecuteNonQuery()

    End Sub

    Private Sub btnCloseNameAccount_Click(sender As Object, e As EventArgs) Handles btnCloseNameAccount.Click
        PanelSearch.Top = 5000
    End Sub

    Private Sub txtsearsh_TextChanged(sender As Object, e As EventArgs) Handles txtsearsh.TextChanged
        Try

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
                S = Cls.Get_Select_Grid_S("ACCNumber as [رقم الحساب],ACCName as [أسم الحساب]", "AccountsTree", "ACCName <> ''")
            Else
                S = Cls.Get_Select_Grid_S("ACCNumber as [رقم الحساب],ACCName as [أسم الحساب]", "AccountsTree", "ACCName Like N'%" & txtsearsh.Text & "%'")
            End If
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub DTGV_DoubleClick(sender As Object, e As EventArgs) Handles DTGV.DoubleClick
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        txtNumberAccount.Text = DTGV.SelectedRows(0).Cells(0).Value
        txtNameAccount.Text = DTGV.SelectedRows(0).Cells(1).Value
        PanelSearch.Top = 5000
    End Sub

    Private Sub btnSearchItems_Click(sender As Object, e As EventArgs) Handles btnSearchItems.Click
        PanelSearch.Location = New System.Drawing.Point(357, 213)
        PanelSearch.Size = New System.Drawing.Point(409, 281)
        txtsearsh_TextChanged(sender, e)
    End Sub
End Class