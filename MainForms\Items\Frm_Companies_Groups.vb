﻿Public Class Frm_Companies_Groups
    Dim branch_ID As String

    Private Sub Frm_Group_Branch_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Bra.Fil("groups", "g_name", cmbcats)
        Headerx()
    End Sub

    Private Sub BtnAddCat_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAddCat.Click
        FrmCats.Show()
    End Sub

    Private Sub btnsave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnsave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateSave() = False Then Exit Sub


        Dim Groups_ID As String = Cls.Get_Code_Value_Branch("Groups", "id", "G_name", cmbcats.Text)

        MAXRECORD()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Group_Branch (Company_Branch_ID,branch_ID,Group_Name_ID,branch_Name,UserName)"
        S = S & " values (N'" & Company_Branch_ID & "',N'" & branch_ID & "',N'" & Groups_ID & "',N'" & cmbbranch_Name.Text.Trim & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        Headerx()
        cmbbranch_Name.Text = ""
        cmbcats.Text = ""

    End Sub

    Private Sub MAXRECORD()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Group_Branch"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            branch_ID = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(branch_ID As float)) as mb FROM Group_Branch where branch_ID <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            branch_ID = sh + 1
        End If

    End Sub

    Function ValidateSave() As Boolean

        If cmbcats.Text = "" Then MsgBox("فضلا أدخل مجموعة الصنف", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbbranch_Name.Text = "" Then MsgBox("فضلا أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbbranch_Name.Focus() : Return False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from Group_Branch where branch_Name =N'" & cmbbranch_Name.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H > 0 Then
            MsgBox(" مجموعة الفرعية للصنف مسجل مسبقاً بنفس الاسم", MsgBoxStyle.Exclamation) : cmbbranch_Name.Focus() : Return False
        End If

        Return True
    End Function

    Private Sub Headerx()
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If PermtionName = "مدير" Then
            S = "SELECT     dbo.Group_Branch.branch_ID AS [رقم الفرع], dbo.Groups.G_name AS [اسم المجموعة], dbo.Group_Branch.branch_Name AS [مجموعة الفرع], dbo.Group_Branch.Company_Branch_ID AS Branch_ID  FROM  dbo.Groups INNER JOIN   dbo.Group_Branch ON dbo.Groups.id = dbo.Group_Branch.Group_Name_ID"
        Else
            S = "SELECT     dbo.Group_Branch.branch_ID AS [رقم الفرع], dbo.Groups.G_name AS [اسم المجموعة], dbo.Group_Branch.branch_Name AS [مجموعة الفرع], dbo.Group_Branch.Company_Branch_ID AS Branch_ID  FROM  dbo.Groups INNER JOIN   dbo.Group_Branch ON dbo.Groups.id = dbo.Group_Branch.Group_Name_ID WHERE   (dbo.Group_Branch.Company_Branch_ID =N'" & Company_Branch_ID & "')"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)
        DTGV.Columns(0).Width = 90
        DTGV.Columns(1).Width = 100
        DTGV.Columns(2).Width = 100
        DTGV.Columns(3).Visible = False
        txtNumber.Text = DTGV.RowCount
    End Sub

    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DTGV.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.SelectedRows.Count - 1
            If DTGV.RowCount = 0 Then Beep() : Exit Sub
            If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim ItmID As String
            ItmID = DTGV.SelectedRows(i).Cells(0).Value

            cmd.CommandText = "delete from Group_Branch where branch_ID =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
        Next
        Headerx()
    End Sub

    Private Sub cmbcats_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbcats.KeyUp
        If e.KeyCode = 13 Then
            cmbbranch_Name.Focus()
        End If
    End Sub

    Private Sub cmbbranch_Name_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbbranch_Name.KeyUp
        If e.KeyCode = 13 Then
            btnsave.PerformClick()
        End If
    End Sub
End Class