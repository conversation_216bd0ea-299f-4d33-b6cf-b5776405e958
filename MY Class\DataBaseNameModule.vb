﻿Imports System.IO

Public Module DataBaseNameModule
    ' مسار الملف الافتراضي
    Private ReadOnly FilePath As String = Path.Combine(Application.StartupPath, "Database.txt")

    ''' <summary>
    ''' قراءة أسماء الخوادم من الملف
    ''' </summary>
    ''' <returns>مصفوفة تحتوي على أسماء الخوادم</returns>
    Public Function ReadDataBaseNames() As String()
        Try
            ' التأكد من وجود الملف
            If File.Exists(FilePath) Then
                ' قراءة جميع السطور من الملف
                Return File.ReadAllLines(FilePath)
            Else
                ' إرجاع مصفوفة فارغة إذا لم يكن الملف موجودًا
                Return New String() {}
            End If
        Catch ex As Exception
            ' تسجيل الخطأ وإرجاع مصفوفة فارغة
            MessageBox.Show("خطأ في قراءة الملف: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return New String() {}
        End Try
    End Function

    ''' <summary>
    ''' إضافة اسم خادم جديد
    ''' </summary>
    ''' <param name="dataBaseName">اسم قاعدة البيانات للإضافة</param>
    ''' <returns>true إذا تمت الإضافة بنجاح, false خلاف ذلك</returns>
    Public Function AddDataBaseName(dataBaseName As String) As Boolean
        Try
            ' التأكد من عدم وجود اسم قاعدة البيانات مسبقًا
            Dim existingServers = ReadDataBaseNames()

            If Not existingServers.Contains(dataBaseName) Then
                ' إنشاء قائمة جديدة وإضافة الاسم الجديد
                Dim updatedServers As New List(Of String)(existingServers)
                updatedServers.Add(dataBaseName)

                ' حفظ القائمة المحدثة في الملف
                File.WriteAllLines(FilePath, updatedServers)
                Return True
            Else
                MessageBox.Show("اسم قاعدة البيانات موجود بالفعل", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return False
            End If
        Catch ex As Exception
            MessageBox.Show("خطأ في إضافة اسم قاعدة البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ''' <summary>
    ''' حذف اسم خادم
    ''' </summary>
    ''' <param name="dataBaseName">اسم قاعدة البيانات المراد حذفه</param>
    ''' <returns>true إذا تم الحذف بنجاح, false خلاف ذلك</returns>
    Public Function RemoveDataBaseName(dataBaseName As String) As Boolean
        Try
            ' قراءة الخوادم الحالية
            Dim existingServers = ReadDataBaseNames().ToList()

            ' محاولة حذف اسم قاعدة البيانات
            If existingServers.Remove(dataBaseName) Then
                ' حفظ القائمة المحدثة في الملف
                File.WriteAllLines(FilePath, existingServers)
                Return True
            Else
                MessageBox.Show("اسم قاعدة البيانات غير موجود", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return False
            End If
        Catch ex As Exception
            MessageBox.Show("خطأ في حذف اسم قاعدة البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function
End Module