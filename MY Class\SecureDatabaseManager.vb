Imports System.Data.SqlClient
Imports System.Configuration
Imports System.Security.Cryptography
Imports System.Text
Imports System.IO

''' <summary>
''' فئة مركزية آمنة لإدارة قاعدة البيانات
''' تحل محل الطرق غير الآمنة في MyVars.vb
''' </summary>
Public Class SecureDatabaseManager
    Private Shared _instance As SecureDatabaseManager
    Private _connectionString As String
    Private _isInitialized As Boolean = False

    ' Singleton Pattern للتأكد من وجود نسخة واحدة فقط
    Public Shared ReadOnly Property Instance As SecureDatabaseManager
        Get
            If _instance Is Nothing Then
                _instance = New SecureDatabaseManager()
            End If
            Return _instance
        End Get
    End Property

    Private Sub New()
        ' منع إنشاء نسخ متعددة
    End Sub

    ''' <summary>
    ''' تهيئة مدير قاعدة البيانات مع سلسلة الاتصال المشفرة
    ''' </summary>
    Public Sub Initialize()
        Try
            _connectionString = GetSecureConnectionString()
            _isInitialized = True
            LogInfo("تم تهيئة مدير قاعدة البيانات بنجاح")
        Catch ex As Exception
            LogError("خطأ في تهيئة مدير قاعدة البيانات", ex)
            Throw New InvalidOperationException("فشل في تهيئة قاعدة البيانات", ex)
        End Try
    End Sub

    ''' <summary>
    ''' الحصول على سلسلة اتصال آمنة ومشفرة
    ''' </summary>
    Private Function GetSecureConnectionString() As String
        Try
            Dim networkType As String = GetRegistryValue("NetworkName", "LocalDB")
            Dim connectionString As String = ""

            Select Case networkType.ToUpper()
                Case "NO"
                    ' Windows Authentication
                    connectionString = String.Format("Data Source={0};Initial Catalog={1};Integrated Security=True;Connection Timeout=30;",
                                                    GetRegistryValue("ServerName", Environment.MachineName),
                                                    GetRegistryValue("DatabaseElectric", "DatabaseElectric"))

                Case "YES"
                    ' SQL Server Authentication مع كلمة مرور مشفرة
                    Dim encryptedPassword As String = GetRegistryValue("Password", "")
                    Dim decryptedPassword As String = DecryptPassword(encryptedPassword)

                    connectionString = String.Format("Data Source={0};Initial Catalog={1};User ID={2};Password=***;Connection Timeout=30;Pooling=true;Max Pool Size=100;",
                                                    GetRegistryValue("ServerName", Environment.MachineName),
                                                    GetRegistryValue("DatabaseElectric", "DatabaseElectric"),
                                                    GetRegistryValue("UserName", "sa"),
                                                    decryptedPassword)

                Case "LOCALDB"
                    ' قراءة من ملف مشفر
                    connectionString = ReadEncryptedConnectionFile()

                Case Else
                    ' LocalDB افتراضي
                    connectionString = "Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\DatabaseElectric.mdf;Integrated Security=True;"
            End Select

            Return connectionString

        Catch ex As Exception
            LogError("خطأ في بناء سلسلة الاتصال", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' إنشاء اتصال آمن بقاعدة البيانات
    ''' </summary>
    Public Function CreateConnection() As SqlConnection
        If Not _isInitialized Then
            Throw New InvalidOperationException("يجب تهيئة مدير قاعدة البيانات أولاً")
        End If

        Try
            Dim connection As New SqlConnection(_connectionString)
            Return connection
        Catch ex As Exception
            LogError("خطأ في إنشاء اتصال قاعدة البيانات", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' تنفيذ استعلام آمن مع معاملات
    ''' </summary>
    Public Function ExecuteQuery(query As String, parameters As Dictionary(Of String, Object)) As DataTable
        Dim dataTable As New DataTable()

        Try
            Using connection As SqlConnection = CreateConnection()
                Using command As New SqlCommand(query, connection)
                    ' إضافة المعاملات بشكل آمن
                    If parameters IsNot Nothing Then
                        For Each param In parameters
                            command.Parameters.AddWithValue(param.Key, If(param.Value, DBNull.Value))
                        Next
                    End If

                    connection.Open()
                    Using adapter As New SqlDataAdapter(command)
                        adapter.Fill(dataTable)
                    End Using
                End Using
            End Using

            LogInfo($"تم تنفيذ الاستعلام بنجاح: {query.Substring(0, Math.Min(50, query.Length))}...")
            Return dataTable

        Catch ex As Exception
            LogError($"خطأ في تنفيذ الاستعلام: {query}", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' تنفيذ أمر آمن (INSERT, UPDATE, DELETE)
    ''' </summary>
    Public Function ExecuteNonQuery(query As String, parameters As Dictionary(Of String, Object)) As Integer
        Try
            Using connection As SqlConnection = CreateConnection()
                Using command As New SqlCommand(query, connection)
                    ' إضافة المعاملات بشكل آمن
                    If parameters IsNot Nothing Then
                        For Each param In parameters
                            command.Parameters.AddWithValue(param.Key, If(param.Value, DBNull.Value))
                        Next
                    End If

                    connection.Open()
                    Dim rowsAffected As Integer = command.ExecuteNonQuery()

                    LogInfo($"تم تنفيذ الأمر بنجاح، عدد الصفوف المتأثرة: {rowsAffected}")
                    Return rowsAffected
                End Using
            End Using

        Catch ex As Exception
            LogError($"خطأ في تنفيذ الأمر: {query}", ex)
            Throw
        End Try
    End Function
    ''' <summary>
    ''' تنفيذ استعلام للحصول على قيمة واحدة
    ''' </summary>
    Public Function ExecuteScalar(query As String, parameters As Dictionary(Of String, Object)) As Object
        Try
            Using connection As SqlConnection = CreateConnection()
                Using command As New SqlCommand(query, connection)
                    ' إضافة المعاملات بشكل آمن
                    If parameters IsNot Nothing Then
                        For Each param In parameters
                            command.Parameters.AddWithValue(param.Key, If(param.Value, DBNull.Value))
                        Next
                    End If

                    connection.Open()
                    Dim result As Object = command.ExecuteScalar()

                    LogInfo($"تم تنفيذ ExecuteScalar بنجاح")
                    Return result
                End Using
            End Using

        Catch ex As Exception
            LogError($"خطأ في تنفيذ ExecuteScalar: {query}", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' التحقق من صحة اسم المستخدم وكلمة المرور بشكل آمن
    ''' </summary>
    Public Function ValidateUser(username As String, password As String) As Boolean
        Try
            ' تشفير كلمة المرور قبل المقارنة
            Dim hashedPassword As String = HashPassword(password)

            Dim query As String = "SELECT COUNT(*) FROM users WHERE username = @username AND userpassword = @password"
            Dim parameters As New Dictionary(Of String, Object) From {
                {"@username", username},
                {"@password", hashedPassword}
            }

            Dim result As Object = ExecuteScalar(query, parameters)
            Return Convert.ToInt32(result) > 0

        Catch ex As Exception
            LogError("خطأ في التحقق من المستخدم", ex)
            Return False
        End Try
    End Function

    ''' <summary>
    ''' تشفير كلمة المرور
    ''' </summary>
    Private Function HashPassword(password As String) As String
        Try
            Using sha256 As SHA256 = SHA256.Create()
                Dim hashedBytes As Byte() = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "FIT_SOFT_SALT"))
                Return Convert.ToBase64String(hashedBytes)
            End Using
        Catch ex As Exception
            LogError("خطأ في تشفير كلمة المرور", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' فك تشفير كلمة المرور
    ''' </summary>
    Private Function DecryptPassword(encryptedPassword As String) As String
        If String.IsNullOrEmpty(encryptedPassword) Then
            Return ""
        End If

        Try
            ' هنا يجب استخدام خوارزمية فك تشفير مناسبة
            ' هذا مثال بسيط - يجب استخدام تشفير أقوى في الإنتاج
            Dim bytes As Byte() = Convert.FromBase64String(encryptedPassword)
            Return Encoding.UTF8.GetString(bytes)
        Catch ex As Exception
            LogError("خطأ في فك تشفير كلمة المرور", ex)
            Return ""
        End Try
    End Function

    ''' <summary>
    ''' قراءة ملف سلسلة الاتصال المشفر
    ''' </summary>
    Private Function ReadEncryptedConnectionFile() As String
        Try
            Dim filePath As String = Path.Combine(Application.StartupPath, "ConnectionString.txt")
            If File.Exists(filePath) Then
                Dim encryptedContent As String = File.ReadAllText(filePath)
                ' فك تشفير المحتوى هنا
                Return encryptedContent
            Else
                Throw New FileNotFoundException("ملف سلسلة الاتصال غير موجود")
            End If
        Catch ex As Exception
            LogError("خطأ في قراءة ملف سلسلة الاتصال", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' قراءة قيمة من الريجستري بشكل آمن
    ''' </summary>
    Private Function GetRegistryValue(keyName As String, defaultValue As String) As String
        Try
            Using key As Microsoft.Win32.RegistryKey = Microsoft.Win32.Registry.CurrentUser.OpenSubKey("Software\CUSTOMS\CUSTOMERS")
                If key IsNot Nothing Then
                    Return key.GetValue(keyName, defaultValue).ToString()
                Else
                    Return defaultValue
                End If
            End Using
        Catch ex As Exception
            LogError($"خطأ في قراءة الريجستري: {keyName}", ex)
            Return defaultValue
        End Try
    End Function

    ''' <summary>
    ''' تسجيل المعلومات
    ''' </summary>
    Private Sub LogInfo(message As String)
        ' يمكن تحسين هذا لاستخدام نظام تسجيل أكثر تطوراً
        Console.WriteLine($"[INFO] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}")
    End Sub

    ''' <summary>
    ''' تسجيل الأخطاء
    ''' </summary>
    Private Sub LogError(message As String, ex As Exception)
        ' يمكن تحسين هذا لاستخدام نظام تسجيل أكثر تطوراً
        Console.WriteLine($"[ERROR] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}")
        Console.WriteLine($"[ERROR] Exception: {ex.Message}")
        Console.WriteLine($"[ERROR] StackTrace: {ex.StackTrace}")
    End Sub

    ''' <summary>
    ''' تنظيف الموارد
    ''' </summary>
    Public Sub Dispose()
        _isInitialized = False
        LogInfo("تم تنظيف موارد مدير قاعدة البيانات")
    End Sub
End Class
