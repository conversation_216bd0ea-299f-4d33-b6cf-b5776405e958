﻿Imports CrystalDecisions.CrystalReports.Engine
Public Class frmShowAppropriations
    Dim WithEvents BS As New BindingSource

    Private Sub frmShowAppropriations_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Cls.fill_combo("vendors", "Vendorname", cmbSupplier)
        Bra.Fil("LetterCredits", "ApprovalNumber", cmbApprovalNumber)
        FillComboBoxBank()
        GroupBox2.Top = 600
        GetData()
    End Sub

    Private Sub FillComboBoxBank()
        cmbBank.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select distinct Bank from Banks"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            cmbBank.Items.Add(dr("Bank"))
        Loop
    End Sub

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT ApprovalNumber AS [رقم الاعتماد],LOCDate AS [تاريخ], Currency AS [العملة], Supplier AS [المورد], Bank AS [البنك],BankAddress as [عنوان البنك],InvoiceNumber as [رقم الفاتورة],PolicyNumber as [رقم البوليصة],TotalItems as [اجمالى الاصناف],ValueEgyptian as [القيمة بالمصرى],BankCharges as [مصاريف البنك],ChargeCustoms as [مصاريف الجمارك],ChargeSendDocument as [مصاريف ارسال المستندات],ChargeTransport as [مصاريف النقل],ExpenseEnvironment as [مصاريف البيئة],OtherCharge as [مصاريف اخرى],TotalAppearTax as [الاجمالى بدون ض م],SalesTax as [ضريبة المبيعات],TotalAllSum as [الاجمالى شامل ضريبة المبيعات] FROM LetterCredits where ApprovalNumber <> N''"

        If cmbApprovalNumber.Text <> Nothing Then
            S = S & "and ApprovalNumber =N'" & cmbApprovalNumber.Text.Trim & "'"
        End If
        If cmbSupplier.Text <> Nothing Then
            S = S & "and Supplier =N'" & cmbSupplier.Text.Trim & "'"
        End If
        If cmbBank.Text <> Nothing Then
            S = S & "and Bank =N'" & cmbBank.Text.Trim & "'"
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & "and LOCDate >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and LOCDate <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
    End Sub

    Private Sub btnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnShow.Click
        GetData()
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub GetDetails()
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim NumberID As String
        NumberID = DataGridView1.SelectedRows(0).Cells(0).Value

        GroupBox1.Enabled = False
        GroupBox2.Top = 140

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select ApprovalNumber as [رقم الاعتماد],itm_id as [الباركود],itm_cat as [المجموعة] ,itm_name as [الصنف],itm_Unity as [الوحدة],price as [السعر],qu as [الكمية],totalprice as [الاجمالى] from LetterCreditsItems where ApprovalNumber =N'" & NumberID & "'"
        dr = cmd.ExecuteReader
        DataGridView2.DataSource = Cls.PopulateDataView(dr)
        DataGridView2.Columns(0).Visible = False
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim NumberID As String
            NumberID = DataGridView1.SelectedRows(0).Cells(0).Value


            cmd.CommandText = "delete from LetterCredits where ApprovalNumber =N'" & NumberID & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete from LetterCreditsItems where ApprovalNumber =N'" & NumberID & "'" : cmd.ExecuteNonQuery()
        Next
        GetData()
    End Sub

    Private Sub btnEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnEdit.Click
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        EditItmId = DataGridView1.SelectedRows(0).Cells(0).Value

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete from TmpBillsalData" : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "insert into TmpBillsalData(bill_no,itm_id,itm_cat,itm_name,itm_Unity,price,qu,totalprice,Stores,username,bill_date) select ApprovalNumber,itm_id,itm_cat,itm_name,itm_Unity,price,qu,totalprice,Stores,username,bill_date from LetterCreditsItems where ApprovalNumber =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
        Action = "Edit"
        frmAppropriations.Show()
    End Sub

    Private Sub DataGridView1_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DataGridView1.DoubleClick
        GetDetails()
    End Sub

    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click
        Dim txtFROMDate As String
        Dim txtToDate As String
        Dim f As New Frm_PrintReports
        Dim rpt As New rptLetterCredits
        Dim txt As TextObject
        txtFROMDate = Format(Me.DateTimePicker1.Value, "yyy, MM, dd, 00, 00, 000")
        txtToDate = Format(Me.DateTimePicker2.Value, "yyy, MM, dd, 00, 00, 00")
        'On Error Resume Next
        GETSERVERNAMEANDDATABASENAME(rpt, DataBaseName, PasswordServer, UserNameServer)
        If ChkWithoutDate.Checked = False Then
            If cmbApprovalNumber.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{LetterCredits.LOCDate} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")AND{LetterCredits.ApprovalNumber} =N'" & cmbApprovalNumber.Text & "'"
            End If
            If cmbBank.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{LetterCredits.LOCDate} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")AND{LetterCredits.Bank} =N'" & cmbBank.Text & "'"
            End If
            If cmbSupplier.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{LetterCredits.LOCDate} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")AND{LetterCredits.Supplier} =N'" & cmbSupplier.Text & "'"
            End If
            If cmbApprovalNumber.Text = Nothing And cmbBank.Text = Nothing And cmbSupplier.Text = Nothing Then
                rpt.RecordSelectionFormula = "{LetterCredits.LOCDate} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")"
            End If
            txt = rpt.Section1.ReportObjects("txtDate")
            txt.Text = "خلال الفترة من" & Format(Me.DateTimePicker1.Value, "dd - MM - yyyy") & " الى " & Format(Me.DateTimePicker2.Value, "dd - MM - yyyy")
        Else
            If cmbApprovalNumber.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{LetterCredits.ApprovalNumber} =N'" & cmbApprovalNumber.Text & "'"
            End If
            If cmbBank.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{LetterCredits.Bank} =N'" & cmbBank.Text & "'"
            End If
            If cmbSupplier.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{LetterCredits.Supplier} =N'" & cmbSupplier.Text & "'"
            End If
        End If
        AddReportView()
        Dim txtname, txtNameAr, txtNameEn As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير بحركة الاعتمادات"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        f.CrystalReportViewer1.ReportSource = rpt
        f.CrystalReportViewer1.Zoom(95%)
        f.CrystalReportViewer1.RefreshReport()
        f.Text = "تقرير بحركة الاعتمادات"
        f.Show()
    End Sub

    Private Sub btnBack_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBack.Click
        GroupBox2.Top = 600
        GroupBox2.Enabled = True
        GroupBox1.Enabled = True
    End Sub

    Private Sub btnPrintStat_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrintStat.Click
        Dim txtFROMDate As String
        Dim txtToDate As String
        Dim f As New Frm_PrintReports
        Dim rpt As New rptAppropriations
        Dim txt As TextObject
        'On Error Resume Next
        GETSERVERNAMEANDDATABASENAME(rpt, DataBaseName, PasswordServer, UserNameServer)
        rpt.RecordSelectionFormula = "{LetterCredits.ApprovalNumber} =" & DataGridView1.Rows(0).Cells(0).Value & " AND{LetterCreditsItems.ApprovalNumber} =" & DataGridView1.Rows(0).Cells(0).Value & ""
        AddReportView()
        Dim txtname, txtNameAr, txtNameEn As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير بحركة الاعتمادات"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        f.CrystalReportViewer1.ReportSource = rpt
        f.CrystalReportViewer1.Zoom(95%)
        f.CrystalReportViewer1.RefreshReport()
        f.Text = "تقرير بحركة الاعتمادات"
        f.Show()
    End Sub
End Class