﻿Public Class Frm_Capital_Owner2
    Dim MaxRecoedCode As String
    Dim ActionGrid As Boolean = False

    Private Sub btnNew_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnNew.Click

        ClearAll()
        Bra.Fil("Capital_Owner", "Owner_Name", cmbFind)

        MAXRECORD("Capital_Owner", "Owner_Code")
        txtCode.Text = MaxRecoedCode
        txtName.Focus()
    End Sub

    Private Sub ClearAll()
        txtCode.Text = ""
        txtName.Text = ""
        txtAddress.Text = ""
        txtMobile.Text = ""
        txtPhone.Text = ""
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If Trim(txtCode.Text) = "" Then MsgBox("من فضلك ادخل الكود", MsgBoxStyle.Exclamation) : Exit Sub
        If Trim(txtName.Text) = "" Then MsgBox("من فضلك ادخل الاسم", MsgBoxStyle.Exclamation) : Exit Sub
        If Cls.Check_Field_Value("Capital_Owner", "Owner_Name", txtName.Text) Then MsgBox("عفوا الاسم مسجل من قبل", MsgBoxStyle.Exclamation) : Exit Sub
        If Cls.Check_Field_Value("Capital_Owner", "Owner_Code", txtCode.Text) Then MsgBox("عفوا الكود مسجل من قبل", MsgBoxStyle.Exclamation) : Exit Sub

        Cls.insert("Capital_Owner", "Owner_Code,Owner_Name,Owner_Phone,Owner_Mobile,Owner_Address", "N'" & txtCode.Text & "',N'" & txtName.Text & "',N'" & txtPhone.Text & "',N'" & txtMobile.Text & "',N'" & txtAddress.Text & "'")
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)

        ClearAll()

        Bra.Fil("Capital_Owner", "Owner_Name", cmbFind)
        Bra.Fil("Capital_Owner", "Owner_Name", Frm_Capital_Partner_Withdrawals.cmbCapital_Owner)

        MAXRECORD("Capital_Owner", "Owner_Code")
        txtCode.Text = MaxRecoedCode
        txtName.Focus()
    End Sub

    Private Sub frmAccountsMain_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Bra.Fil("Capital_Owner", "Owner_Name", cmbFind)

        MAXRECORD("Capital_Owner", "Owner_Code")
        txtCode.Text = MaxRecoedCode
        txtName.Focus()
    End Sub

    Private Sub MAXRECORD(ByVal Tables As String, ByVal Code As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " & Tables & ""
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            MaxRecoedCode = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" & Code & " As float)) as mb FROM " & Tables & " where " & Code & " <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            MaxRecoedCode = sh + 1
        End If

    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If Trim(txtCode.Text) = "" Then MsgBox("من فضلك ادخل الكود", MsgBoxStyle.Exclamation) : Exit Sub
        If Trim(txtName.Text) = "" Then MsgBox("من فضلك ادخل الاسم", MsgBoxStyle.Exclamation) : Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Capital_Owner set Owner_Name =N'" & txtName.Text & "',Owner_Phone =N'" & txtPhone.Text & "',Owner_Mobile =N'" & txtMobile.Text & "',Owner_Address =N'" & txtAddress.Text & "' where Owner_Code =N'" & txtCode.Text & "'" : cmd.ExecuteNonQuery()

        MsgBox("تم التعديل بنجاح", MsgBoxStyle.Information)

        ClearAll()

        Bra.Fil("Capital_Owner", "Owner_Name", cmbFind)

        MAXRECORD("Capital_Owner", "Owner_Code")
        txtCode.Text = MaxRecoedCode
        txtName.Focus()
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If cmbFind.SelectedIndex = -1 Then MsgBox("من فضلك اختر بيان صحيح", MsgBoxStyle.Exclamation) : Exit Sub

        Dim msg As New MsgBoxResult
        msg = (MsgBox("هل تريد بالفعل الحذف", MsgBoxStyle.Question + MsgBoxStyle.YesNo))
        If msg = MsgBoxResult.Yes Then
            Cls.delete("Capital_Owner", "Owner_Name=N'" & cmbFind.Text & "'")
            MsgBox("تم الحذف بنجاح", MsgBoxStyle.Information)
        End If

        ClearAll()

        Bra.Fil("Capital_Owner", "Owner_Name", cmbFind)

        MAXRECORD("Capital_Owner", "Owner_Code")
        txtCode.Text = MaxRecoedCode
        txtName.Focus()
    End Sub

    Private Sub cmbFind_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbFind.SelectedIndexChanged
        ActionGrid = True
        If cmbFind.Text = "" Then Exit Sub
        Dim Government_Code As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT * from Capital_Owner where Owner_Name = N'" & cmbFind.Text & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            txtCode.Text = dr("Owner_Code")
            txtName.Text = dr("Owner_Name").ToString()
            txtPhone.Text = dr("Owner_Phone").ToString()
            txtMobile.Text = dr("Owner_Mobile").ToString()
            txtAddress.Text = dr("Owner_Address").ToString()
        End If
        ActionGrid = False
    End Sub

    Private Sub txtCode_TextChanged(sender As Object, e As EventArgs) Handles txtCode.TextChanged
        MyVars.CheckNumber(txtCode)
    End Sub
End Class