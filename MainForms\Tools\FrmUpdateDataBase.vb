﻿Imports System.Data.SqlClient

Public Class FrmUpdateDataBase
    Private Sub Form2_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Location = New Point(CInt((Screen.PrimaryScreen.WorkingArea.Width / 2) - (Me.Width / 2)), CInt((Screen.PrimaryScreen.WorkingArea.Height / 2) - (Me.Height / 2)))
        Button1.Left = CInt((Me.ClientRectangle.Width / 2) - (Button1.Width / 2))
        PictureBox1.Size = My.Resources.Loading.Size
        PictureBox1.Image = My.Resources.Loading
        PictureBox1.Left = CInt((Me.ClientRectangle.Width / 2) - (PictureBox1.Width / 2))
        PictureBox1.Visible = False
        BackgroundWorker1.WorkerSupportsCancellation = True
        Button1_Click(sender, e)
    End Sub

    Private Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork

        'Dim sw As New Stopwatch
        'sw.Start()
        ''------------------------------
        Dim <PERSON>oop As Long

        Dim aray_1 As New ArrayList
        Dim aray_2 As New ArrayList
        Dim aray_3 As New ArrayList
        Dim aray_4 As New ArrayList
        Dim aray_5 As New ArrayList
        Dim aray_6 As New ArrayList
        Dim aray_7 As New ArrayList
        Dim aray_8 As New ArrayList
        Dim aray_9 As New ArrayList
        Dim aray_10 As New ArrayList
        Dim aray_11 As New ArrayList

        Dim sname2 As String = ""
        Dim Stores2 As String = ""
        Dim id, sname, Stores, qu, qu_unity As String




        '============================================================================

        aray_6.Clear() : aray_7.Clear() : aray_8.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select id,sname,Stores from Items where sname In (Select sname from Items Group by sname having (Count(*)>1)) order by sname,Stores"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_6.Add(dr(0))
            aray_7.Add(dr(1))
            aray_8.Add(dr(2))
        Loop


        aray_9.Clear() : aray_10.Clear() : aray_11.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select id,itm_name,Stores from BilltINData where itm_name In (Select itm_name from BilltINData Group by itm_name having (Count(*)>1))  AND (bill_no = N'جرد') order by itm_name,Stores"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_9.Add(dr(0))
            aray_10.Add(dr(1))
            aray_11.Add(dr(2))
        Loop

        '============================================================================

        aray_1.Clear() : aray_2.Clear() : aray_3.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select id, bill_no, itm_id, itm_name, qu, qu_unity, bill_date From dbo.IM_Bsal_Data Where (qu = 1) And (qu_unity >= 2)"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr(0))
            aray_2.Add(dr(4))
            aray_3.Add(dr(5))
        Loop
        For i As Integer = 0 To aray_1.Count - 1
            id = aray_1(i).ToString()
            qu = aray_2(i).ToString()
            qu_unity = aray_3(i).ToString()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update IM_Bsal_Data set qu = N'" & qu_unity & "' where id =N'" & id & "'" : cmd.ExecuteNonQuery()
        Next


        '============================================================================

        For i As Integer = 0 To aray_6.Count - 1
            id = aray_6(i).ToString
            sname = aray_7(i).ToString
            Stores = aray_8(i).ToString
            If sname <> sname2 Or Stores <> Stores2 Then
                sname2 = sname
                Stores2 = Stores
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete from Items where id =N'" & id & "'" : cmd.ExecuteNonQuery()
            End If
        Next

        sname2 = "" : Stores2 = ""
        For i As Integer = 0 To aray_9.Count - 1
            id = aray_9(i).ToString
            sname = aray_10(i).ToString
            Stores = aray_11(i).ToString
            If sname <> sname2 Or Stores <> Stores2 Then
                sname2 = sname
                Stores2 = Stores
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete from BilltINData where id =N'" & id & "'" : cmd.ExecuteNonQuery()
            End If
        Next

        '============================================================================


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete from Date_Finance" : cmd.ExecuteNonQuery()

        aray_1.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select distinct Vendorname from vendors"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr(0))
        Loop

        aray_2.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select distinct Vendorname from customers"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_2.Add(dr(0))
        Loop


        aray_3.Clear()
        aray_4.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select itm_id,Stores from Items"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_3.Add(dr(0))
            aray_4.Add(dr(1))
        Loop

        aray_5.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select distinct NameEmployee from Employees"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_5.Add(dr(0))
        Loop


        '========================================================================================================
        'chkVendor.Checked = False
        'chkCustomer.Checked = False
        'chkStore.Checked = False
        'chkEmployees.Checked = False

        'lblValueTo.Text = aray_1.Count
        For i As Integer = 0 To aray_1.Count - 1
            IM.VendorAccountTotal(aray_1(i))

            'lblValueFrom.Text = i

            'lblValueText.Text = aray_1(i).ToString()
            'lblValueNumber.Text = CountLoop
            'CountLoop += 1
            'If CountLoop = 100 Then
            '    CountLoop = 0
            'End If
            'BackgroundWorker1.ReportProgress(CountLoop)
            'Threading.Thread.Sleep(300)
        Next
        'chkVendor.Checked = True

        'lblValueTo.Text = aray_2.Count
        For i As Integer = 0 To aray_2.Count - 1
            IM.CustomerAccountTotal(aray_2(i))

            'lblValueFrom.Text = i

            'lblValueText.Text = aray_2(i)
            'lblValueNumber.Text = CountLoop
            'CountLoop += 1
            'If CountLoop = 100 Then
            '    CountLoop = 0
            'End If
            'BackgroundWorker1.ReportProgress(CountLoop)
            'Threading.Thread.Sleep(300)
        Next
        'chkCustomer.Checked = True


        'lblValueTo.Text = aray_3.Count
        For i As Integer = 0 To aray_3.Count - 1
            IM.Store(aray_3(i), aray_4(i))

            'lblValueFrom.Text = i

            'lblValueText.Text = aray_3(i) + " - " + aray_4(i)
            'lblValueNumber.Text = CountLoop
            'CountLoop += 1
            'If CountLoop = 100 Then
            '    CountLoop = 0
            'End If
            'BackgroundWorker1.ReportProgress(CountLoop)
            'Threading.Thread.Sleep(300)
        Next
        'chkStore.Checked = True
        'ProgressBar1.Value = 100

        'lblValueTo.Text = aray_5.Count
        For i As Integer = 0 To aray_5.Count - 1
            IM.EmployeesAccountTotal(aray_5(i))

            'lblValueFrom.Text = i

            'lblValueText.Text = aray_5(i)
            'lblValueNumber.Text = CountLoop
            'CountLoop += 1
            'If CountLoop = 100 Then
            '    CountLoop = 0
            'End If
            'BackgroundWorker1.ReportProgress(CountLoop)
            'Threading.Thread.Sleep(300)
        Next
        'chkEmployees.Checked = True

        'ProgressBar1.Value = 100

        MsgBox("تم تحديث قاعدة البيانات بنجاح", MsgBoxStyle.Information)

        ''------------------------------
        'sw.Stop()
        'MsgBox(sw.Elapsed.ToString)

        Me.Close()
    End Sub

    Private Sub BackgroundWorker1_ProgressChanged(sender As Object, e As System.ComponentModel.ProgressChangedEventArgs) Handles BackgroundWorker1.ProgressChanged
        'ProgressBar1.Value = e.ProgressPercentage
    End Sub

    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        'MsgBox("You Don App ......", MsgBoxStyle.Information)
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Button1.Refresh()
        PictureBox1.Visible = True
        PictureBox1.Refresh()
        BackgroundWorker1.RunWorkerAsync()
        Control.CheckForIllegalCrossThreadCalls = False
    End Sub

End Class