<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SriLibs.DataSync.MSSQLSERVER</name>
    </assembly>
    <members>
        <member name="T:SriLibs.DataSync.MSSQLSERVER.Initializer">
            <summary>
            It is a class to initialize and verify essential things like connections and pre installs.
            </summary>
        </member>
        <member name="M:SriLibs.DataSync.MSSQLSERVER.Initializer.Initiate(System.String,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Method to initiate the Sync Process.
            </summary>
            <param name="localConnectionString">Connection string of local Machine</param>
            <param name="serverConnectionString">Connection string of server DB </param>
            <param name="tables"> Specify list of table names if you did not want sync all tables</param>
            <returns>Synchronizer object which has sync methods.</returns>
        </member>
        <member name="T:SriLibs.DataSync.MSSQLSERVER.SyncDirections">
            <summary>
            enum to choose the directions
            </summary>
        </member>
        <member name="F:SriLibs.DataSync.MSSQLSERVER.SyncDirections.SyncToServer">
            <summary>
            option to sync data to server.
            </summary>
        </member>
        <member name="F:SriLibs.DataSync.MSSQLSERVER.SyncDirections.SyncFromServer">
            <summary>
            option to sync data from server.
            </summary>
        </member>
        <member name="T:SriLibs.DataSync.MSSQLSERVER.Synchronizer">
            <summary>
            It is a main class to do synchronization.
            </summary>
        </member>
        <member name="M:SriLibs.DataSync.MSSQLSERVER.Synchronizer.Sync(SriLibs.DataSync.MSSQLSERVER.SyncDirections)">
            <summary>
            It is method to sync data which is cached in syncfeed table. But it is possible only if the sync direction is local to server.
            </summary>
            <param name="direction">Specify the direction of synchronization.</param>
            <returns></returns>
        </member>
        <member name="M:SriLibs.DataSync.MSSQLSERVER.Synchronizer.FlashSync(SriLibs.DataSync.MSSQLSERVER.SyncDirections)">
            <summary>
            It is method to sync all the data which is located in the source. It deletes all the data from destination before flash sync executed. 
            </summary>
            <param name="direction">Specify the direction of synchronization.</param>
            <returns></returns>    
        </member>
        <member name="T:SriLibs.DataSync.MSSQLSERVER.SyncInfo">
            <summary>
            It is a class which holds the Sync Information after sync Or FlashSync method executed.
            </summary>
        </member>
        <member name="P:SriLibs.DataSync.MSSQLSERVER.SyncInfo.Inserted">
            <summary>
            Count of Inserted records.
            </summary>
        </member>
        <member name="P:SriLibs.DataSync.MSSQLSERVER.SyncInfo.Updated">
            <summary>
            Count of Updated records.
            </summary>
        </member>
        <member name="P:SriLibs.DataSync.MSSQLSERVER.SyncInfo.Deleted">
            <summary>
            Count of Deleted records.
            </summary>
        </member>
        <member name="P:SriLibs.DataSync.MSSQLSERVER.SyncInfo.Duration">
            <summary>
            Duration of the sync process.
            </summary>
        </member>
        <member name="P:SriLibs.DataSync.MSSQLSERVER.SyncInfo.Status">
            <summary>
            status of the complete synchronization.
            </summary>
        </member>
    </members>
</doc>
