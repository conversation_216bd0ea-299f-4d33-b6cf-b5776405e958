﻿Public Class FrmOnlineStoreCompany
    Dim Area_ID As String
    Dim ActivEdit As Boolean = False

    Private Sub Frm_Group_Branch_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Cos.Fill_ComboBox_OnlineStore("[Category] Where (IsDelete = 0)", "Name", cmbCategory)
        Cos.Fill_ComboBox_OnlineStore("[Category] Where (IsDelete = 0)", "Name", cmbCategoryView)
        Headerx()
        PanelAddNew.Dock = DockStyle.None
        PanelAddNew.Top = 5000
    End Sub

    Private Sub btnsave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnsave.Click
        If ValidateSave() = False Then Exit Sub

        Dim CategoryId As String = Cos.GetCodeValueOnlineStore("Category", "Id", "Name", cmbCategory.Text)

        If Not ConnectingOnlineStore() Is Nothing Then
            If ActivEdit = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update Company set Name =N'" & cmbCompany.Text & "',CategoryId =N'" & CategoryId & "' where Id= '" & txtID.Text & "'"
                cmd.ExecuteNonQuery()
                Cn.Close()
                connect()
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Company (Name,CategoryId,IsDelete)"
                S = S & " values (N'" & cmbCompany.Text.Trim & "',N'" & CategoryId & "',N'0')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
                Cn.Close()
                connect()
            End If
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If

        cmbCompany.Text = ""
        cmbCategory.Text = ""
        ActivEdit = False
        btnsave.Text = "إضافة"
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.save_32

        PanelAddNew.Dock = DockStyle.None
        PanelAddNew.Top = 5000
        Headerx()
    End Sub

    Private Sub MAXRECORD()
        If Not ConnectingOnlineStore() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from Company"
            dr = cmd.ExecuteReader

            Dim dt As New DataTable
            dt.Load(dr)

            If dt.Rows.Count = 0 Then
                txtID.Text = 1
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(CAST(Id As float)) as mb FROM Company where Id <> N''"
                dr = cmd.ExecuteReader
                dr.Read()

                Dim sh As Long
                sh = dr("mb")
                txtID.Text = sh + 1
            End If
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Sub

    Function ValidateSave() As Boolean

        If cmbCategory.Text = "" Then MsgBox("فضلا أدخل الفئة", MsgBoxStyle.Exclamation) : cmbCategory.Focus() : Return False
        If cmbCompany.Text = "" Then MsgBox("فضلا أدخل الشركة", MsgBoxStyle.Exclamation) : cmbCompany.Focus() : Return False

        If Not ConnectingOnlineStore() Is Nothing Then
            If ActivEdit = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select count(*) from Company where Name =N'" & cmbCompany.Text.Trim & "' and (IsDelete = 0)" : H = cmd.ExecuteScalar
                If H > 0 Then
                    MsgBox(" الشركة مسجلة مسبقاً بنفس الاسم", MsgBoxStyle.Exclamation) : cmbCompany.Focus() : Return False
                End If
            End If
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If

        Return True
    End Function

    Private Sub Headerx()
        If Not ConnectingOnlineStore() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "Select dbo.Company.Id As [كود الشركة], dbo.Category.Name As [الفئات], dbo.Company.Name As [الشركات], dbo.Company.IsDelete  From dbo.Category RIGHT OUTER Join     dbo.Company ON dbo.Category.Id = dbo.Company.CategoryId  Where (dbo.Company.IsDelete = 0)"
            If chkAll.Checked = False Then
                If cmbCategoryView.Text <> "" Then
                    S = S & " And dbo.Category.Name =N'" & cmbCategoryView.Text.Trim & "'"
                End If
                If cmbCompanyView.Text <> "" Then
                    S = S & " And dbo.Company.Name = N'" & cmbCompanyView.Text.Trim & "'"
                End If
            End If

            If chkIsDelete.Checked = True Then
                S = S & " and dbo.Company.IsDelete =N'True'"
            Else
                S = S & " and dbo.Company.IsDelete =N'False'"
            End If
            S = S & " order by dbo.Company.Id"
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DTGV.DataSource = Cls.PopulateDataView(dr)
            'DTGV.Columns(0).Width = 80
            'DTGV.Columns(1).Width = 100
            'DTGV.Columns(2).Width = 100
            'DTGV.Columns(3).Width = 100
            DTGV.Columns(3).Visible = False
            txtNumber.Text = DTGV.RowCount
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Sub

    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If Not ConnectingOnlineStore() Is Nothing Then
            If DTGV.Rows.Count = 0 Then
                MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
                Exit Sub
            End If
            Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
            If x = vbCancel Then Exit Sub
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To DTGV.SelectedRows.Count - 1
                If DTGV.RowCount = 0 Then Beep() : Exit Sub
                If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
                Dim ItmID As String
                ItmID = DTGV.SelectedRows(i).Cells(0).Value

                cmd.CommandText = "update Company set IsDelete =N'1' where Id= '" & ItmID & "'"
                cmd.ExecuteNonQuery()
                'cmd.CommandText = "delete from Company where Id =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
            Next
            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If

        Headerx()
        ActivEdit = False
        btnsave.Text = "إضافة"
    End Sub

    Private Sub cmbcats_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCategory.KeyUp
        If e.KeyCode = 13 Then
            cmbCompany.Focus()
        End If
    End Sub

    Private Sub cmbbranch_Name_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbCompany.KeyUp
        If e.KeyCode = 13 Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub chkIsDelete_CheckedChanged(sender As Object, e As EventArgs) Handles chkIsDelete.CheckedChanged
        Headerx()
    End Sub

    Private Sub DTGV_DoubleClick(sender As Object, e As EventArgs) Handles DTGV.DoubleClick
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        ActivEdit = True
        txtID.Text = DTGV.SelectedRows(0).Cells(0).Value.ToString()
        cmbCategory.Text = DTGV.SelectedRows(0).Cells(1).Value.ToString()
        cmbCompany.Text = DTGV.SelectedRows(0).Cells(2).Value.ToString()
        btnsave.Text = "تعديل"
        ActivEdit = True
        PanelAddNew.Top = 20
        PanelAddNew.Dock = DockStyle.Fill
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.Edit_1

    End Sub

    Private Sub cmbArea_DropDown(sender As Object, e As EventArgs) Handles cmbCompany.DropDown
        ActivEdit = False
        btnsave.Text = "إضافة"
    End Sub

    Private Sub cmbCategory_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbCategory.SelectedIndexChanged
        If ActivEdit = False Then
            Dim CategoryId As String = Cos.GetProductValue("Category", "Id", "Name", cmbCategory.Text)


            If Not ConnectingOnlineStore() Is Nothing Then
                cmbCompany.Items.Clear()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "SELECT distinct Name FROM Company  WHERE CategoryId =N'" & CategoryId & "' and (IsDelete = 0) order by 1"
                dr = cmd.ExecuteReader
                Do While dr.Read
                    cmbCompany.Items.Add(Trim(dr(0)))
                Loop

                Cn.Close()
                connect()
            Else
                Cn.Close()
                connect()
                MsgBox(Cls_Constant.ErrMsg)
            End If

            Headerx()
        End If

    End Sub

    Private Sub DTGV_CellValueChanged(sender As Object, e As DataGridViewCellEventArgs) Handles DTGV.CellValueChanged
        'If DTGV.RowCount = 0 Then Beep() : Exit Sub
        'If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub


        'Dim ID As String = DTGV.SelectedRows(0).Cells(0).Value
        'Dim OrderLimit As String = DTGV.SelectedRows(0).Cells(3).Value

        'If DTGV.Columns(e.ColumnIndex).Name = "check" Then
        '    For i As Integer = 0 To DTGV.RowCount() - 1
        '        If DTGV.Rows(i).Cells("check").Value Then

        '        End If
        '    Next
        'End If
        'Cos.UpdateTabelOnlineStore("[Area]", "OrderLimit", OrderLimit, ID)

    End Sub

    Private Sub cmbArea_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbCompany.SelectedIndexChanged
        Headerx()
    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbCategoryView.Enabled = False
            cmbCompanyView.Enabled = False
        Else
            cmbCategoryView.Enabled = True
            cmbCompanyView.Enabled = True
        End If
    End Sub

    Private Sub cmbCategoryView_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbCategoryView.SelectedIndexChanged
        Dim CategoryId As String = Cos.GetProductValue("Category", "Id", "Name", cmbCategoryView.Text)

        If Not ConnectingOnlineStore() Is Nothing Then
            cmbCompanyView.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "SELECT distinct Name FROM Company  WHERE CategoryId =N'" & CategoryId & "' and (IsDelete = 0) order by 1"
            dr = cmd.ExecuteReader
            Do While dr.Read
                cmbCompanyView.Items.Add(Trim(dr(0)))
            Loop

            Cn.Close()
            connect()
        Else
            Cn.Close()
            connect()
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        PanelAddNew.Dock = DockStyle.None
        PanelAddNew.Top = 5000
        ActivEdit = False
        btnsave.Text = "إضافة"
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.save_32
    End Sub

    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        PanelAddNew.Top = 20
        PanelAddNew.Dock = DockStyle.Fill
        cmbCategory.Focus()
        ActivEdit = False
        btnsave.Text = "إضافة"
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.save_32
        cmbCompany.Text = ""
        cmbCategory.Text = ""
        MAXRECORD()
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs) Handles btnEdit.Click
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        ActivEdit = True
        txtID.Text = DTGV.SelectedRows(0).Cells(0).Value
        cmbCategory.Text = DTGV.SelectedRows(0).Cells(1).Value
        cmbCompany.Text = DTGV.SelectedRows(0).Cells(2).Value
        btnsave.Text = "تعديل"
        ActivEdit = True
        PanelAddNew.Top = 20
        PanelAddNew.Dock = DockStyle.Fill
        btnsave.Image = Global.FIT_SOFT.My.Resources.Resources.Edit_1
    End Sub

    Private Sub btnShow_Click(sender As Object, e As EventArgs) Handles btnShow.Click
        Headerx()
    End Sub

End Class