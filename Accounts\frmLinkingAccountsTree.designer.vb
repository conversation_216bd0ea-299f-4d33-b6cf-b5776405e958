﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmLinkingAccountsTree
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmLinkingAccountsTree))
        Me.Panel10 = New System.Windows.Forms.Panel()
        Me.cmbLink_Statement = New System.Windows.Forms.ComboBox()
        Me.Label20 = New System.Windows.Forms.Label()
        Me.bgHeader = New System.Windows.Forms.Panel()
        Me.Label26 = New System.Windows.Forms.Label()
        Me.Image1 = New System.Windows.Forms.PictureBox()
        Me.cmbAccountName = New System.Windows.Forms.ComboBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Dgv_Add = New System.Windows.Forms.DataGridView()
        Me.btnAdd = New System.Windows.Forms.Button()
        Me.txtAccountCode = New System.Windows.Forms.TextBox()
        Me.txtStatement_link = New System.Windows.Forms.TextBox()
        Me.btnDelete = New System.Windows.Forms.Button()
        Me.bgHeader.SuspendLayout()
        CType(Me.Image1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Dgv_Add, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Panel10
        '
        Me.Panel10.BackColor = System.Drawing.Color.White
        Me.Panel10.Location = New System.Drawing.Point(1, 165)
        Me.Panel10.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel10.Name = "Panel10"
        Me.Panel10.Size = New System.Drawing.Size(863, 6)
        Me.Panel10.TabIndex = 88
        '
        'cmbLink_Statement
        '
        Me.cmbLink_Statement.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.cmbLink_Statement.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbLink_Statement.FormattingEnabled = True
        Me.cmbLink_Statement.Items.AddRange(New Object() {"مشتريات", "مرتجعات مشتريات", "مدفوعات موردين", "خصومات موردين", "مبيعات", "مرتجعات مبيعات", "مقبوضات عملاء", "خصومات عملاء", "توالف او إهلاك", "مصروفات", "مرتبات", "خصومات اخرى", "مخصص إهلاك اصول", "مسحوبات جارى الشركاء", "ايداعات جارى الشركاء", "مسحوبات رأس المال", "ايداعات رأس المال"})
        Me.cmbLink_Statement.Location = New System.Drawing.Point(232, 97)
        Me.cmbLink_Statement.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbLink_Statement.Name = "cmbLink_Statement"
        Me.cmbLink_Statement.Size = New System.Drawing.Size(478, 36)
        Me.cmbLink_Statement.TabIndex = 85
        '
        'Label20
        '
        Me.Label20.AutoSize = True
        Me.Label20.BackColor = System.Drawing.Color.Transparent
        Me.Label20.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label20.ForeColor = System.Drawing.Color.White
        Me.Label20.Location = New System.Drawing.Point(717, 102)
        Me.Label20.Name = "Label20"
        Me.Label20.Size = New System.Drawing.Size(87, 30)
        Me.Label20.TabIndex = 83
        Me.Label20.Text = "بيان الربط"
        '
        'bgHeader
        '
        Me.bgHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(162, Byte), Integer), CType(CType(45, Byte), Integer), CType(CType(100, Byte), Integer))
        Me.bgHeader.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.bgHeader.Controls.Add(Me.Label26)
        Me.bgHeader.Controls.Add(Me.Image1)
        Me.bgHeader.Cursor = System.Windows.Forms.Cursors.Default
        Me.bgHeader.Dock = System.Windows.Forms.DockStyle.Top
        Me.bgHeader.Font = New System.Drawing.Font("Arial", 8.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bgHeader.ForeColor = System.Drawing.SystemColors.WindowText
        Me.bgHeader.Location = New System.Drawing.Point(0, 0)
        Me.bgHeader.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.bgHeader.Name = "bgHeader"
        Me.bgHeader.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.bgHeader.Size = New System.Drawing.Size(866, 67)
        Me.bgHeader.TabIndex = 97
        Me.bgHeader.TabStop = True
        '
        'Label26
        '
        Me.Label26.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label26.AutoSize = True
        Me.Label26.BackColor = System.Drawing.Color.Transparent
        Me.Label26.Cursor = System.Windows.Forms.Cursors.Default
        Me.Label26.Font = New System.Drawing.Font("JF Flat", 15.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label26.ForeColor = System.Drawing.Color.White
        Me.Label26.Location = New System.Drawing.Point(354, 10)
        Me.Label26.Name = "Label26"
        Me.Label26.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.Label26.Size = New System.Drawing.Size(178, 47)
        Me.Label26.TabIndex = 10
        Me.Label26.Text = "ربط الحسابات"
        '
        'Image1
        '
        Me.Image1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Image1.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.link_red
        Me.Image1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.Image1.Cursor = System.Windows.Forms.Cursors.Default
        Me.Image1.Location = New System.Drawing.Point(767, 6)
        Me.Image1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Image1.Name = "Image1"
        Me.Image1.Size = New System.Drawing.Size(79, 59)
        Me.Image1.TabIndex = 12
        Me.Image1.TabStop = False
        '
        'cmbAccountName
        '
        Me.cmbAccountName.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.cmbAccountName.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cmbAccountName.FormattingEnabled = True
        Me.cmbAccountName.Location = New System.Drawing.Point(232, 198)
        Me.cmbAccountName.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.cmbAccountName.Name = "cmbAccountName"
        Me.cmbAccountName.Size = New System.Drawing.Size(478, 36)
        Me.cmbAccountName.TabIndex = 99
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.BackColor = System.Drawing.Color.Transparent
        Me.Label1.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.White
        Me.Label1.Location = New System.Drawing.Point(717, 203)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(113, 30)
        Me.Label1.TabIndex = 98
        Me.Label1.Text = "أسم الحساب"
        '
        'Dgv_Add
        '
        Me.Dgv_Add.AllowUserToAddRows = False
        Me.Dgv_Add.AllowUserToDeleteRows = False
        Me.Dgv_Add.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Dgv_Add.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill
        Me.Dgv_Add.BackgroundColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle1.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle1.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.Dgv_Add.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle1
        Me.Dgv_Add.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window
        DataGridViewCellStyle2.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ActiveCaptionText
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.Dgv_Add.DefaultCellStyle = DataGridViewCellStyle2
        Me.Dgv_Add.Location = New System.Drawing.Point(3, 256)
        Me.Dgv_Add.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Dgv_Add.Name = "Dgv_Add"
        Me.Dgv_Add.ReadOnly = True
        Me.Dgv_Add.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        DataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle3.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle3.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle3.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle3.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.Dgv_Add.RowHeadersDefaultCellStyle = DataGridViewCellStyle3
        Me.Dgv_Add.RowTemplate.Height = 26
        Me.Dgv_Add.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.Dgv_Add.Size = New System.Drawing.Size(861, 302)
        Me.Dgv_Add.TabIndex = 100
        '
        'btnAdd
        '
        Me.btnAdd.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Add_Bill
        Me.btnAdd.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnAdd.Location = New System.Drawing.Point(14, 183)
        Me.btnAdd.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnAdd.Name = "btnAdd"
        Me.btnAdd.Size = New System.Drawing.Size(132, 68)
        Me.btnAdd.TabIndex = 101
        Me.btnAdd.UseVisualStyleBackColor = True
        '
        'txtAccountCode
        '
        Me.txtAccountCode.Enabled = False
        Me.txtAccountCode.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtAccountCode.Location = New System.Drawing.Point(162, 198)
        Me.txtAccountCode.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtAccountCode.Name = "txtAccountCode"
        Me.txtAccountCode.Size = New System.Drawing.Size(69, 36)
        Me.txtAccountCode.TabIndex = 126
        Me.txtAccountCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'txtStatement_link
        '
        Me.txtStatement_link.Enabled = False
        Me.txtStatement_link.Font = New System.Drawing.Font("JF Flat", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtStatement_link.Location = New System.Drawing.Point(162, 97)
        Me.txtStatement_link.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.txtStatement_link.Name = "txtStatement_link"
        Me.txtStatement_link.Size = New System.Drawing.Size(69, 36)
        Me.txtStatement_link.TabIndex = 130
        Me.txtStatement_link.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'btnDelete
        '
        Me.btnDelete.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Delete
        Me.btnDelete.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.btnDelete.Location = New System.Drawing.Point(14, 562)
        Me.btnDelete.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnDelete.Name = "btnDelete"
        Me.btnDelete.Size = New System.Drawing.Size(132, 39)
        Me.btnDelete.TabIndex = 132
        Me.btnDelete.UseVisualStyleBackColor = True
        '
        'frmLinkingAccountsTree
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.SystemColors.ButtonFace
        Me.ClientSize = New System.Drawing.Size(866, 609)
        Me.Controls.Add(Me.btnDelete)
        Me.Controls.Add(Me.txtStatement_link)
        Me.Controls.Add(Me.txtAccountCode)
        Me.Controls.Add(Me.btnAdd)
        Me.Controls.Add(Me.Dgv_Add)
        Me.Controls.Add(Me.cmbAccountName)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.bgHeader)
        Me.Controls.Add(Me.Panel10)
        Me.Controls.Add(Me.cmbLink_Statement)
        Me.Controls.Add(Me.Label20)
        Me.ForeColor = System.Drawing.SystemColors.ActiveCaptionText
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.IsMdiContainer = True
        Me.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Name = "frmLinkingAccountsTree"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "ربط حسابات الشجرة"
        Me.bgHeader.ResumeLayout(False)
        Me.bgHeader.PerformLayout()
        CType(Me.Image1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Dgv_Add, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents Panel10 As System.Windows.Forms.Panel
    Friend WithEvents cmbLink_Statement As System.Windows.Forms.ComboBox
    Friend WithEvents Label20 As System.Windows.Forms.Label
    Public WithEvents bgHeader As System.Windows.Forms.Panel
    Public WithEvents Label26 As System.Windows.Forms.Label
    Public WithEvents Image1 As System.Windows.Forms.PictureBox
    Friend WithEvents cmbAccountName As System.Windows.Forms.ComboBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Dgv_Add As System.Windows.Forms.DataGridView
    Friend WithEvents btnAdd As System.Windows.Forms.Button
    Friend WithEvents txtAccountCode As System.Windows.Forms.TextBox
    Friend WithEvents txtStatement_link As System.Windows.Forms.TextBox
    Friend WithEvents btnDelete As Button
End Class
