﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class FrmItemsNewExcel
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FrmItemsNewExcel))
        Me.bgHeader = New System.Windows.Forms.Panel()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.Label26 = New System.Windows.Forms.Label()
        Me.dtpDate = New System.Windows.Forms.DateTimePicker()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.Panel5 = New System.Windows.Forms.Panel()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.DTGV = New System.Windows.Forms.DataGridView()
        Me.Panel4 = New System.Windows.Forms.Panel()
        Me.btnImport = New System.Windows.Forms.Button()
        Me.btnSaveDataBase = New System.Windows.Forms.Button()
        Me.Button1 = New System.Windows.Forms.Button()
        Me.Button2 = New System.Windows.Forms.Button()
        Me.btnSaveApp = New System.Windows.Forms.Button()
        Me.btnSaveDataBaseUnits = New System.Windows.Forms.Button()
        Me.btnSaveAppToAccount = New System.Windows.Forms.Button()
        Me.btnCompanies = New System.Windows.Forms.Button()
        Me.btnCompaniesOnlineStore = New System.Windows.Forms.Button()
        Me.btnUpdateOnlineApp = New System.Windows.Forms.Button()
        Me.Button3 = New System.Windows.Forms.Button()
        Me.bgHeader.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DTGV, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'bgHeader
        '
        Me.bgHeader.BackColor = System.Drawing.SystemColors.HotTrack
        Me.bgHeader.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.bgHeader.Controls.Add(Me.PictureBox1)
        Me.bgHeader.Controls.Add(Me.Label26)
        Me.bgHeader.Controls.Add(Me.dtpDate)
        Me.bgHeader.Controls.Add(Me.Label1)
        Me.bgHeader.Cursor = System.Windows.Forms.Cursors.Default
        Me.bgHeader.Dock = System.Windows.Forms.DockStyle.Top
        Me.bgHeader.Font = New System.Drawing.Font("Arial", 8.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.bgHeader.ForeColor = System.Drawing.SystemColors.WindowText
        Me.bgHeader.Location = New System.Drawing.Point(0, 0)
        Me.bgHeader.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.bgHeader.Name = "bgHeader"
        Me.bgHeader.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.bgHeader.Size = New System.Drawing.Size(1251, 67)
        Me.bgHeader.TabIndex = 98
        Me.bgHeader.TabStop = True
        '
        'PictureBox1
        '
        Me.PictureBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureBox1.BackgroundImage = Global.FIT_SOFT.My.Resources.Resources.Text_edit
        Me.PictureBox1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.PictureBox1.Location = New System.Drawing.Point(1127, 5)
        Me.PictureBox1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(91, 58)
        Me.PictureBox1.TabIndex = 11
        Me.PictureBox1.TabStop = False
        '
        'Label26
        '
        Me.Label26.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label26.AutoSize = True
        Me.Label26.BackColor = System.Drawing.Color.Transparent
        Me.Label26.Cursor = System.Windows.Forms.Cursors.Default
        Me.Label26.Font = New System.Drawing.Font("JF Flat", 14.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label26.ForeColor = System.Drawing.Color.White
        Me.Label26.Location = New System.Drawing.Point(485, 14)
        Me.Label26.Name = "Label26"
        Me.Label26.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.Label26.Size = New System.Drawing.Size(314, 41)
        Me.Label26.TabIndex = 10
        Me.Label26.Text = "أستيراد الاصناف من الاكسل"
        '
        'dtpDate
        '
        Me.dtpDate.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.dtpDate.Format = System.Windows.Forms.DateTimePickerFormat.[Short]
        Me.dtpDate.Location = New System.Drawing.Point(11, 14)
        Me.dtpDate.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.dtpDate.Name = "dtpDate"
        Me.dtpDate.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.dtpDate.Size = New System.Drawing.Size(141, 34)
        Me.dtpDate.TabIndex = 211
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        Me.Label1.Location = New System.Drawing.Point(158, 20)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(57, 26)
        Me.Label1.TabIndex = 212
        Me.Label1.Text = "التاريخ"
        '
        'Panel2
        '
        Me.Panel2.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel2.Location = New System.Drawing.Point(0, 67)
        Me.Panel2.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(1251, 6)
        Me.Panel2.TabIndex = 202
        '
        'Panel5
        '
        Me.Panel5.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel5.Dock = System.Windows.Forms.DockStyle.Right
        Me.Panel5.Location = New System.Drawing.Point(1245, 73)
        Me.Panel5.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel5.Name = "Panel5"
        Me.Panel5.Size = New System.Drawing.Size(6, 693)
        Me.Panel5.TabIndex = 204
        '
        'Panel1
        '
        Me.Panel1.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Left
        Me.Panel1.Location = New System.Drawing.Point(0, 73)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(6, 693)
        Me.Panel1.TabIndex = 205
        '
        'Panel3
        '
        Me.Panel3.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel3.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel3.Location = New System.Drawing.Point(6, 760)
        Me.Panel3.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(1239, 6)
        Me.Panel3.TabIndex = 206
        '
        'DTGV
        '
        Me.DTGV.AllowUserToAddRows = False
        Me.DTGV.AllowUserToDeleteRows = False
        Me.DTGV.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DTGV.BackgroundColor = System.Drawing.Color.WhiteSmoke
        DataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Control
        DataGridViewCellStyle1.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle1.ForeColor = System.Drawing.SystemColors.WindowText
        DataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DTGV.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle1
        Me.DTGV.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window
        DataGridViewCellStyle2.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold)
        DataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.DTGV.DefaultCellStyle = DataGridViewCellStyle2
        Me.DTGV.Location = New System.Drawing.Point(6, 228)
        Me.DTGV.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.DTGV.Name = "DTGV"
        Me.DTGV.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.DTGV.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.CellSelect
        Me.DTGV.Size = New System.Drawing.Size(1239, 531)
        Me.DTGV.TabIndex = 207
        '
        'Panel4
        '
        Me.Panel4.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel4.BackColor = System.Drawing.Color.FromArgb(CType(CType(67, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(84, Byte), Integer))
        Me.Panel4.Location = New System.Drawing.Point(6, 220)
        Me.Panel4.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Panel4.Name = "Panel4"
        Me.Panel4.Size = New System.Drawing.Size(1245, 6)
        Me.Panel4.TabIndex = 208
        '
        'btnImport
        '
        Me.btnImport.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnImport.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnImport.Location = New System.Drawing.Point(1079, 75)
        Me.btnImport.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnImport.Name = "btnImport"
        Me.btnImport.Size = New System.Drawing.Size(160, 62)
        Me.btnImport.TabIndex = 209
        Me.btnImport.Text = "فتح  ملف الاكسل"
        Me.btnImport.UseVisualStyleBackColor = True
        '
        'btnSaveDataBase
        '
        Me.btnSaveDataBase.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSaveDataBase.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnSaveDataBase.Location = New System.Drawing.Point(912, 75)
        Me.btnSaveDataBase.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnSaveDataBase.Name = "btnSaveDataBase"
        Me.btnSaveDataBase.Size = New System.Drawing.Size(160, 62)
        Me.btnSaveDataBase.TabIndex = 210
        Me.btnSaveDataBase.Text = "حفظ صنف جديد"
        Me.btnSaveDataBase.UseVisualStyleBackColor = True
        '
        'Button1
        '
        Me.Button1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Button1.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button1.Location = New System.Drawing.Point(398, 75)
        Me.Button1.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(175, 62)
        Me.Button1.TabIndex = 213
        Me.Button1.Text = "حفظ شجرة الحسابات"
        Me.Button1.UseVisualStyleBackColor = True
        Me.Button1.Visible = False
        '
        'Button2
        '
        Me.Button2.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Button2.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button2.Location = New System.Drawing.Point(580, 75)
        Me.Button2.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button2.Name = "Button2"
        Me.Button2.Size = New System.Drawing.Size(160, 62)
        Me.Button2.TabIndex = 214
        Me.Button2.Text = "تسوية المخازن"
        Me.Button2.UseVisualStyleBackColor = True
        Me.Button2.Visible = False
        '
        'btnSaveApp
        '
        Me.btnSaveApp.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSaveApp.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnSaveApp.Location = New System.Drawing.Point(232, 75)
        Me.btnSaveApp.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnSaveApp.Name = "btnSaveApp"
        Me.btnSaveApp.Size = New System.Drawing.Size(160, 62)
        Me.btnSaveApp.TabIndex = 215
        Me.btnSaveApp.Text = "الاصناف من البرنامج الى المتجر الالكترونى"
        Me.btnSaveApp.UseVisualStyleBackColor = True
        '
        'btnSaveDataBaseUnits
        '
        Me.btnSaveDataBaseUnits.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSaveDataBaseUnits.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnSaveDataBaseUnits.Location = New System.Drawing.Point(746, 75)
        Me.btnSaveDataBaseUnits.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnSaveDataBaseUnits.Name = "btnSaveDataBaseUnits"
        Me.btnSaveDataBaseUnits.Size = New System.Drawing.Size(160, 62)
        Me.btnSaveDataBaseUnits.TabIndex = 216
        Me.btnSaveDataBaseUnits.Text = "حفظ صنف جديد" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "بالوحدات"
        Me.btnSaveDataBaseUnits.UseVisualStyleBackColor = True
        '
        'btnSaveAppToAccount
        '
        Me.btnSaveAppToAccount.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSaveAppToAccount.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnSaveAppToAccount.Location = New System.Drawing.Point(66, 75)
        Me.btnSaveAppToAccount.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnSaveAppToAccount.Name = "btnSaveAppToAccount"
        Me.btnSaveAppToAccount.Size = New System.Drawing.Size(160, 62)
        Me.btnSaveAppToAccount.TabIndex = 217
        Me.btnSaveAppToAccount.Text = "الاصناف من المتجر الى البرنامج"
        Me.btnSaveAppToAccount.UseVisualStyleBackColor = True
        '
        'btnCompanies
        '
        Me.btnCompanies.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCompanies.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnCompanies.Location = New System.Drawing.Point(66, 150)
        Me.btnCompanies.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnCompanies.Name = "btnCompanies"
        Me.btnCompanies.Size = New System.Drawing.Size(160, 62)
        Me.btnCompanies.TabIndex = 218
        Me.btnCompanies.Text = "اضافة الشركات على البرنامج"
        Me.btnCompanies.UseVisualStyleBackColor = True
        '
        'btnCompaniesOnlineStore
        '
        Me.btnCompaniesOnlineStore.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCompaniesOnlineStore.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnCompaniesOnlineStore.Location = New System.Drawing.Point(232, 150)
        Me.btnCompaniesOnlineStore.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnCompaniesOnlineStore.Name = "btnCompaniesOnlineStore"
        Me.btnCompaniesOnlineStore.Size = New System.Drawing.Size(160, 62)
        Me.btnCompaniesOnlineStore.TabIndex = 219
        Me.btnCompaniesOnlineStore.Text = "اضافة الشركات على المتجر"
        Me.btnCompaniesOnlineStore.UseVisualStyleBackColor = True
        '
        'btnUpdateOnlineApp
        '
        Me.btnUpdateOnlineApp.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnUpdateOnlineApp.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnUpdateOnlineApp.Location = New System.Drawing.Point(398, 150)
        Me.btnUpdateOnlineApp.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnUpdateOnlineApp.Name = "btnUpdateOnlineApp"
        Me.btnUpdateOnlineApp.Size = New System.Drawing.Size(175, 62)
        Me.btnUpdateOnlineApp.TabIndex = 220
        Me.btnUpdateOnlineApp.Text = "تحديث الاسعار والبيانات على المتجر"
        Me.btnUpdateOnlineApp.UseVisualStyleBackColor = True
        '
        'Button3
        '
        Me.Button3.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Button3.Font = New System.Drawing.Font("JF Flat", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Button3.Location = New System.Drawing.Point(580, 150)
        Me.Button3.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Button3.Name = "Button3"
        Me.Button3.Size = New System.Drawing.Size(160, 62)
        Me.Button3.TabIndex = 221
        Me.Button3.Text = "تحديث اسعار البيع والشراء"
        Me.Button3.UseVisualStyleBackColor = True
        '
        'FrmItemsNewExcel
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1251, 766)
        Me.Controls.Add(Me.Button3)
        Me.Controls.Add(Me.btnUpdateOnlineApp)
        Me.Controls.Add(Me.btnCompaniesOnlineStore)
        Me.Controls.Add(Me.btnCompanies)
        Me.Controls.Add(Me.btnSaveAppToAccount)
        Me.Controls.Add(Me.btnSaveDataBaseUnits)
        Me.Controls.Add(Me.btnSaveApp)
        Me.Controls.Add(Me.Button2)
        Me.Controls.Add(Me.Button1)
        Me.Controls.Add(Me.btnSaveDataBase)
        Me.Controls.Add(Me.btnImport)
        Me.Controls.Add(Me.Panel4)
        Me.Controls.Add(Me.DTGV)
        Me.Controls.Add(Me.Panel3)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.Panel5)
        Me.Controls.Add(Me.Panel2)
        Me.Controls.Add(Me.bgHeader)
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Name = "FrmItemsNewExcel"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "أستيراد الاصناف من الاكسل"
        Me.bgHeader.ResumeLayout(False)
        Me.bgHeader.PerformLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DTGV, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Public WithEvents bgHeader As System.Windows.Forms.Panel
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Public WithEvents Label26 As System.Windows.Forms.Label
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents Panel5 As System.Windows.Forms.Panel
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents Panel3 As System.Windows.Forms.Panel
    Friend WithEvents DTGV As System.Windows.Forms.DataGridView
    Friend WithEvents Panel4 As System.Windows.Forms.Panel
    Friend WithEvents btnImport As System.Windows.Forms.Button
    Friend WithEvents btnSaveDataBase As System.Windows.Forms.Button
    Friend WithEvents dtpDate As System.Windows.Forms.DateTimePicker
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Button1 As System.Windows.Forms.Button
    Friend WithEvents Button2 As Button
    Friend WithEvents btnSaveApp As Button
    Friend WithEvents btnSaveDataBaseUnits As Button
    Friend WithEvents btnSaveAppToAccount As Button
    Friend WithEvents btnCompanies As Button
    Friend WithEvents btnCompaniesOnlineStore As Button
    Friend WithEvents btnUpdateOnlineApp As Button
    Friend WithEvents Button3 As Button
End Class
