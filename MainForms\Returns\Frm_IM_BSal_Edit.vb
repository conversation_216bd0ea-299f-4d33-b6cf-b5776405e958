﻿Imports vb = Microsoft.VisualBasic
Public Class Frm_IM_BSa_Edit

    Dim aray_itm_id As New ArrayList
    Di<PERSON> aray_Stores As New ArrayList

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT CAST(bill_no as float) AS [رقم الفاتورة],Vendorname AS [أسم العميل], bill_date AS [تاريخ الفاتورة], billtime AS [وقت الفاتورة], totalpricebeforedisc AS [القيمة قبل الخصم], disc AS [الخصم], totalpriceafterdisc AS [القيمة بعد الخصم] FROM IM_Bsal where BILL_NO <> N'جرد'"

        If ChkAll.Checked = False Then
            If cmbvendornameshow.Text <> "" Then
                S = S & " and Vendorname =N'" & cmbvendornameshow.Text.Trim & "'"
            End If
            If txtbillnoSearch.Text <> "" Then
                S = S & " and BILL_NO =N'" & txtbillnoSearch.Text.Trim & "'"
            End If
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and  bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم الفاتورة]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [تاريخ الفاتورة]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [أسم العميل]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
        SumDGV()
        SumDetails() : DataGridView1.Columns(0).Width = 0
        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(2).Value)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(2).Value = SM
        Next


    End Sub

    Private Sub btnprint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnprint.Click
        GetData()
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False

        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub
    Private Sub GetDetails()
        'If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        'If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim ItmID As String = 0
        If DataGridView1.RowCount <> 0 Then
            ItmID = DataGridView1.SelectedRows(0).Cells(0).Value
        End If
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id as [الباركود],itm_cat as [أسم المجموعة] ,itm_name as [اسم الصنف],price as [السعر] ,qu as [1الكمية],qu_unity as [الكمية],itm_Unity as [الوحدة]  ,totalprice as [الأجمالي],Stores as [المخزن] from IM_Bsal_Data where bill_no =N'" & ItmID & "'"
        dr = cmd.ExecuteReader
        DataGridView3.DataSource = Cls.PopulateDataView(dr) : SumDetails()
        DataGridView3.Columns(1).Visible = False
        DataGridView3.Columns(4).Visible = False

    End Sub

    Private Sub DataGridView1_CellClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles DataGridView1.CellClick
        GetDetails()
    End Sub

    Private Sub SumDGV()
        Dim SM As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM = SM + DataGridView1.Rows(i).Cells(4).Value
        Next
        txttotalpricebefor.Text = SM
        Dim SM2 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM2 = SM2 + DataGridView1.Rows(i).Cells(5).Value
        Next
        txttotaldisc.Text = SM2

        Dim SM3 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM3 = SM3 + DataGridView1.Rows(i).Cells(6).Value
        Next
        txttotalpriceafter.Text = SM3

    End Sub
    Private Sub SumDetails()
        If DataGridView1.RowCount = 0 Then Exit Sub
        txpricebefor.Text = DataGridView1.SelectedRows(0).Cells(6).Value
        txtdisc.Text = DataGridView1.SelectedRows(0).Cells(4).Value
        txtpriceafter.Text = DataGridView1.SelectedRows(0).Cells(5).Value
    End Sub

    Private Sub Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button3.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim ItmID, XDate As String
        Dim NameVendor As String
        Try
            Dim TextManufacturingProduct As Boolean = False
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from ManufacturingProduct"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                TextManufacturingProduct = True
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
                ItmID = DataGridView1.SelectedRows(i).Cells(0).Value

                aray_itm_id.Clear()
                aray_Stores.Clear()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select itm_id,Stores from IM_Bsal_Data where bill_no =N'" & ItmID & "'"
                dr = cmd.ExecuteReader
                Do While dr.Read = True
                    aray_itm_id.Add(dr(0))
                    aray_Stores.Add(dr(1))
                Loop

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  IM_Bsal where bill_no =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  IM_Bsal_Data where bill_no =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  IM_Vst_disc where TIN_NO =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  IM_Vst where BillNo =N'" & ItmID & "'" : cmd.ExecuteNonQuery()

                cmd.CommandText = "delete From  MOVES where bill_no =N'" & ItmID & "' and MOVStatement =N'مرتجعات مبيعات'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'مرتجعات مقبوضات عملاء'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'مرتجعات خصومات عملاء'" : cmd.ExecuteNonQuery()

                NameVendor = DataGridView1.SelectedRows(i).Cells(1).Value
                IM.CustomerAccountTotal(NameVendor)

                For T As Integer = 0 To aray_itm_id.Count - 1
                    If NotUnityItemsProgram = "YES" Then
                        Dim bill_no_Expired As String = Cls.Get_Code_Value_Stores_More("IM_Bsal_Data", "bill_no_Expired", "bill_no =N'" & ItmID & "' and itm_id =N'" & aray_itm_id(T) & "' and Stores =N'" & aray_Stores(T) & "'")
                        Dim bill_EndDate As String = Cls.Get_Code_Value_Stores_More("IM_Bsal_Data", "bill_EndDate", "bill_no =N'" & ItmID & "' and itm_id =N'" & aray_itm_id(T) & "' and Stores =N'" & aray_Stores(T) & "'")
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        cmd.CommandText = "delete From  IM_Bsal_Data where bill_no =N'" & ItmID & "'" : cmd.ExecuteNonQuery()

                        IM.StoreExpired(aray_itm_id(T).ToString(), aray_Stores(T).ToString(), bill_EndDate.ToString(), bill_no_Expired)
                    End If
                    IM.Store(aray_itm_id(T), aray_Stores(T))

                    If ConnectOnlineStore = "YES" Then
                        EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", aray_itm_id(T))
                        StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", aray_itm_id(T))
                        Cos.UpdateProductStock(StockOnline, aray_itm_id(T), EditItmId)
                    End If
                Next

                XDate = DataGridView1.SelectedRows(i).Cells(2).Value
                Get_Movement_In_Out_Money(XDate, Treasury_Code)
            Next
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try

        GetData() : GetDetails()
    End Sub

    Private Sub DateTimePicker1_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DateTimePicker1.ValueChanged
        GetData()
    End Sub

    Private Sub DateTimePicker2_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DateTimePicker2.ValueChanged
        GetData()
    End Sub

    Private Sub FrmEditimport_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Me.MdiParent = MDIParent1
        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", cmbvendornameshow)
        End If
        cmbvendornameshow.Items.Add("نقدا")

        GetData()
    End Sub

    Private Sub BtnPrintBill_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnPrintBill.Click

        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        EditItmId = DataGridView1.SelectedRows(0).Cells(0).Value

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete from TmpBillsalData" : cmd.ExecuteNonQuery()
        cmd.CommandText = "delete from tmpSales_Bill" : cmd.ExecuteNonQuery()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "insert into TmpBillsalData (bill_no,itm_id,itm_cat,itm_name,price,qu,qu_unity,itm_Unity,totalprice,Stores,username ,bill_no_Expired,RateVAT,BeforeVAT,ValueVAT,bill_EndDate,Discounts,DiscountsValue,Discount_Price_After) select bill_no,itm_id,itm_cat,itm_name,price,qu,qu_unity,itm_Unity,totalprice,Stores,username,bill_no_Expired,RateVAT,BeforeVAT,ValueVAT,bill_EndDate,Discounts,DiscountsValue,Discount_Price_After  from IM_Bsal_Data where bill_no =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
        cmd.CommandText = "insert into tmpSales_Bill (bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,username) select bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,username from IM_Bsal where bill_no =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
        Frm_IM_BSal_Edit_Ad.Show()
    End Sub


    Private Sub ChkAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkAll.CheckedChanged
        If ChkAll.Checked = True Then
            cmbvendornameshow.Enabled = False
            cmbvendornameshow.SelectedIndex = -1
        Else
            cmbvendornameshow.Enabled = True
        End If
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class
