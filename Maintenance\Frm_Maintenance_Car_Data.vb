﻿Public Class Frm_Maintenance_Car_Data


    Sub CLEAR_ALL()
        txtCar_Data_ID.Text = ""
        txtChassis_No.Text = ""
        txtCitiesNumber.Text = ""
        txtDescription.Text = ""
        txtPaintings_No.Text = ""
        txtPaintings_No_Space.Text = ""
        txtPaintings_Letters.Text = ""
        txtPaintings_Letters_Space.Text = ""
        cmbCC.Text = ""
        cmbCustomer.Text = ""
        cmbCylinders_No.Text = ""
        cmbDeviceBrand.Text = ""
        cmbDeviceModel.Text = ""
        cmbMaintenance_Cities.Text = ""
        cmbManu_Year.Text = ""
        cmbTypeProduct.Text = ""
        cmbValves_No.Text = ""
        txtKiloMeter.Text = ""
        cmbDrivers.Text = ""
    End Sub

    Private Sub txtDriv_Car_Color_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub BtnNew_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnNew.Click
        CLEAR_ALL()
        Cls.fill_combo("Maintenance_Car_Data", "CC", cmbCC)
        Cls.fill_combo("Maintenance_Car_Data", "Cylinders_No", cmbCylinders_No)
        Cls.fill_combo("Maintenance_Car_Data", "Valves_No", cmbValves_No)
        txtCar_Data_ID.Text = Cls.MAXRECORD("Maintenance_Car_Data", "Car_Data_ID")
    End Sub

    Private Sub BtnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAdd.Click
        If Validate_Text() = False Then Exit Sub

        Dim MaxID As String = ""

        Cls.Select_More_Data("Customers", "id", "Vendorname=N'" & cmbCustomer.Text & "'")
        If dr.HasRows = False Then
            Cls.insert("Customers", "Vendorname,vintinval,vndiscount,VnPay,vnamntdebit,vnamntcredit,UserName", "N'" & cmbCustomer.Text & "',0,0,0,0,0,N'" & UserName & "'")
        End If

        '======================================================================================
        Cls.Select_More_Data("MaintenanceTypeProduct", "TypeProduct_ID", "TypeProduct_Name=N'" & cmbTypeProduct.Text & "'")
        If dr.HasRows = False Then
            MaxID = MaxRecordTables("MaintenanceTypeProduct", "TypeProduct_ID")
            Cls.insert("MaintenanceTypeProduct", "TypeProduct_ID,TypeProduct_Name", "N'" & MaxID & "',N'" & cmbTypeProduct.Text & "'")
        End If
        '======================================================================================
        Cls.Select_More_Data("Maintenance_Drivers", "Driv_ID", "Driv_Name=N'" & cmbDrivers.Text & "'")
        If dr.HasRows = False Then
            Cls.insert("Maintenance_Drivers", "Driv_Name,Company_Branch_ID", "N'" & cmbDrivers.Text & "',N'" & Company_Branch_ID & "'")
        End If
        '======================================================================================
        Cls.Select_More_Data("MaintenanceDeviceBrand", "DeviceBrand_ID", "DeviceBrand_Name=N'" & cmbDeviceBrand.Text & "'")
        If dr.HasRows = False Then
            MaxID = MaxRecordTables("MaintenanceDeviceBrand", "DeviceBrand_ID")
            Cls.insert("MaintenanceDeviceBrand", "DeviceBrand_ID,DeviceBrand_Name", "N'" & MaxID & "',N'" & cmbDeviceBrand.Text & "'")
        End If
        '======================================================================================
        Cls.Select_More_Data("MaintenanceDeviceModel", "DeviceModel_ID", "DeviceModel_Name=N'" & cmbDeviceModel.Text & "'")
        If dr.HasRows = False Then
            MaxID = MaxRecordTables("MaintenanceDeviceModel", "DeviceModel_ID")
            Cls.insert("MaintenanceDeviceModel", "DeviceModel_ID,DeviceModel_Name", "N'" & MaxID & "',N'" & cmbDeviceModel.Text & "'")
        End If
        '======================================================================================
        Cls.Select_More_Data("Maintenance_Manu_Year", "Manu_Year_ID", "Manu_Year=N'" & cmbManu_Year.Text & "'")
        If dr.HasRows = False Then
            MaxID = MaxRecordTables("Maintenance_Manu_Year", "Manu_Year_ID")
            Cls.insert("Maintenance_Manu_Year", "Manu_Year_ID,Manu_Year", "N'" & MaxID & "',N'" & cmbManu_Year.Text & "'")
        End If
        '======================================================================================

        Dim Car_Number As String = ""
        Dim Paintings_Type_ID As String = "" : Dim Paintings_No As String = "" : Dim Paintings_Letters As String = ""
        If rdoNumberPlatesLetters.Checked = True Then
            Paintings_No = txtPaintings_No.Text
            Paintings_Letters = txtPaintings_Letters.Text
            Paintings_Type_ID = Cls.Get_Code_Value("Maintenance_Paintings_Type", "Paintings_Type_ID", "Paintings_Type", rdoNumberPlatesLetters.Text)
            Car_Number = txtPaintings_No.Text + "| " + txtPaintings_Letters.Text
        ElseIf rdoOnlyNumberPlates.Checked = True Then
            Paintings_No = txtCitiesNumber.Text
            Paintings_Letters = cmbMaintenance_Cities.Text
            Paintings_Type_ID = Cls.Get_Code_Value("Maintenance_Paintings_Type", "Paintings_Type_ID", "Paintings_Type", rdoOnlyNumberPlates.Text)
            Car_Number = txtCitiesNumber.Text + " | " + cmbMaintenance_Cities.Text
        End If

        Dim Cust_Code As String = Cls.Get_Code_Value("Customers", "Cust_Code", "Vendorname", cmbCustomer.Text)
        Dim TypeProduct_ID As String = Cls.Get_Code_Value("MaintenanceTypeProduct", "TypeProduct_ID", "TypeProduct_Name", cmbTypeProduct.Text)
        Dim DeviceBrand_ID As String = Cls.Get_Code_Value("MaintenanceDeviceBrand", "DeviceBrand_ID", "DeviceBrand_Name", cmbDeviceBrand.Text)
        Dim DeviceModel_ID As String = Cls.Get_Code_Value("MaintenanceDeviceModel", "DeviceModel_ID", "DeviceModel_Name", cmbDeviceModel.Text)
        Dim Manu_Year_ID As String = Cls.Get_Code_Value("Maintenance_Manu_Year", "Manu_Year_ID", "Manu_Year", cmbManu_Year.Text)
        Dim Driv_ID As String = Cls.Get_Code_Value("Maintenance_Drivers", "Driv_ID", "Driv_Name", cmbDrivers.Text)


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Maintenance_Car_Data(Car_Data_ID,Car_Number,Paintings_Type_ID,Paintings_No,Paintings_Letters,Cust_Code,Driv_ID,TypeProduct_ID,DeviceBrand_ID,DeviceModel_ID,Manu_Year_ID,CC,Cylinders_No,Valves_No,Chassis_No,Description,KiloMeter,Company_Branch_ID) values ("
        S = S & "N'" & txtCar_Data_ID.Text.Trim & "',N'" & Car_Number.Trim & "',N'" & Paintings_Type_ID & "',N'" & Paintings_No.Trim & "',N'" & Paintings_Letters.Trim & "',N'" & Cust_Code & "',N'" & Driv_ID & "',N'" & TypeProduct_ID & "',N'" & DeviceBrand_ID & "',N'" & DeviceModel_ID & "',N'" & Manu_Year_ID & "',N'" & cmbCC.Text & "',N'" & cmbCylinders_No.Text & "',N'" & cmbValves_No.Text & "',N'" & txtChassis_No.Text & "',N'" & txtDescription.Text & "',N'" & txtKiloMeter.Text & "',N'" & Company_Branch_ID & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        CLEAR_ALL()
        txtCar_Data_ID.Text = Cls.MAXRECORD("Maintenance_Car_Data", "Car_Data_ID")
        Cls.fill_combo("Maintenance_Car_Data", "CC", cmbCC)
        Cls.fill_combo("Maintenance_Car_Data", "Cylinders_No", cmbCylinders_No)
        Cls.fill_combo("Maintenance_Car_Data", "Valves_No", cmbValves_No)
        Header()
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)
        cmbCustomer.Focus()
    End Sub

    Function Validate_Text() As Boolean
        If rdoNumberPlatesLetters.Checked = True Then
            If Trim(txtPaintings_Letters_Space.Text) = "" Then
                MsgBox("فضلاً أدخل الحروف", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
                txtPaintings_Letters_Space.Focus() : Return False : Exit Function
            End If
            If Trim(txtPaintings_No_Space.Text) = "" Then
                MsgBox("فضلاً أدخل الارقام", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
                txtPaintings_No_Space.Focus() : Return False : Exit Function
            End If
        ElseIf rdoOnlyNumberPlates.Checked = True Then
            If Trim(cmbMaintenance_Cities.Text) = "" Then
                MsgBox("فضلاً أدخل المدينة", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
                cmbMaintenance_Cities.Focus() : Return False : Exit Function
            End If
            If Trim(txtCitiesNumber.Text) = "" Then
                MsgBox("فضلاً أدخل رقم اللوحة", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
                txtCitiesNumber.Focus() : Return False : Exit Function
            End If
        End If

        If Trim(cmbTypeProduct.Text) = "" Then
            MsgBox("فضلاً أدخل نوع المركبة", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            cmbTypeProduct.Focus() : Return False : Exit Function
        End If

        Dim Paintings_No As String = "" : Dim Paintings_Letters As String = ""
        If rdoNumberPlatesLetters.Checked = True Then
            Paintings_No = txtPaintings_No.Text
            Paintings_Letters = txtPaintings_Letters.Text
        ElseIf rdoOnlyNumberPlates.Checked = True Then
            Paintings_No = txtCitiesNumber.Text
            Paintings_Letters = cmbMaintenance_Cities.Text
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * From Maintenance_Car_Data  WHERE Paintings_No =N'" & Paintings_No & "' and Paintings_Letters =N'" & Paintings_Letters & "'"
        dr = cmd.ExecuteReader()
        If dr.HasRows = True Then
            MsgBox("عفواً يوجد لوحة مسجله مسبقا بنفس الاسم", MsgBoxStyle.Exclamation)
            Return False : Exit Function
        End If
        Return True
    End Function

    Private Sub Header()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT  Car_Data_ID as [رقم], Car_Number as [رقم السيارة], Vendorname as [أسم العميل], TypeProduct_Name as [نوع المركبة], DeviceBrand_Name as [الماركة], DeviceModel_Name as [الموديل],Manu_Year as [سنة الصنع],CC AS [CC],Cylinders_No AS [عدد السلندرات],Valves_No as [عدد الصبابات],Chassis_No as [رقم الشاسية],Description as [وصف المركبة] FROM  View_Maintenance_Car_Data"
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

    End Sub

    Private Sub BtnEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnEdit.Click
        If ValedateEdit() = False Then Exit Sub

        Dim Car_Number As String = ""
        Dim Paintings_Type_ID As String = "" : Dim Paintings_No As String = "" : Dim Paintings_Letters As String = ""
        If rdoNumberPlatesLetters.Checked = True Then
            Paintings_No = txtPaintings_No.Text
            Paintings_Letters = txtPaintings_Letters.Text
            Paintings_Type_ID = Cls.Get_Code_Value("Maintenance_Paintings_Type", "Paintings_Type_ID", "Paintings_Type", rdoNumberPlatesLetters.Text)
            Car_Number = txtPaintings_No.Text + "| " + txtPaintings_Letters.Text
        ElseIf rdoOnlyNumberPlates.Checked = True Then
            Paintings_No = txtCitiesNumber.Text
            Paintings_Letters = cmbMaintenance_Cities.Text
            Paintings_Type_ID = Cls.Get_Code_Value("Maintenance_Paintings_Type", "Paintings_Type_ID", "Paintings_Type", rdoOnlyNumberPlates.Text)
            Car_Number = txtCitiesNumber.Text + " | " + cmbMaintenance_Cities.Text
        End If

        Dim Cust_Code As String = Cls.Get_Code_Value("Customers", "Cust_Code", "Vendorname", cmbCustomer.Text)
        Dim TypeProduct_ID As String = Cls.Get_Code_Value("MaintenanceTypeProduct", "TypeProduct_ID", "TypeProduct_Name", cmbTypeProduct.Text)
        Dim DeviceBrand_ID As String = Cls.Get_Code_Value("MaintenanceDeviceBrand", "DeviceBrand_ID", "DeviceBrand_Name", cmbDeviceBrand.Text)
        Dim DeviceModel_ID As String = Cls.Get_Code_Value("MaintenanceDeviceModel", "DeviceModel_ID", "DeviceModel_Name", cmbDeviceModel.Text)
        Dim Manu_Year_ID As String = Cls.Get_Code_Value("Maintenance_Manu_Year", "Manu_Year_ID", "Manu_Year", cmbManu_Year.Text)
        Dim Driv_ID As String = Cls.Get_Code_Value("Maintenance_Drivers", "Driv_ID", "Driv_Name", cmbDrivers.Text)


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "update Maintenance_Car_Data set "
        S = S & "Car_Data_ID =N'" & txtCar_Data_ID.Text.Trim & "',"
        S = S & "Car_Number =N'" & Car_Number.Trim & "',"
        S = S & "Paintings_Type_ID =N'" & Paintings_Type_ID & "',"
        S = S & "Paintings_No =N'" & Paintings_No.Trim & "',"
        S = S & "Paintings_Letters =N'" & Paintings_Letters.Trim & "',"
        S = S & "Cust_Code =N'" & Cust_Code & "',"
        S = S & "Driv_ID =N'" & Driv_ID & "',"
        S = S & "TypeProduct_ID =N'" & TypeProduct_ID & "',"
        S = S & "DeviceBrand_ID =N'" & DeviceBrand_ID & "',"
        S = S & "DeviceModel_ID =N'" & DeviceModel_ID & "',"
        S = S & "Manu_Year_ID =N'" & Manu_Year_ID & "',"
        S = S & "CC =N'" & cmbCC.Text.Trim & "',"
        S = S & "Cylinders_No =N'" & cmbCylinders_No.Text.Trim & "',"
        S = S & "Valves_No =N'" & cmbValves_No.Text.Trim & "',"
        S = S & "Chassis_No =N'" & txtChassis_No.Text.Trim & "',"
        S = S & "Description =N'" & txtDescription.Text.Trim & "',"
        S = S & "KiloMeter =N'" & txtKiloMeter.Text.Trim & "',"
        S = S & "Company_Branch_ID =N'" & Company_Branch_ID & "' where Car_Data_ID =N'" & txtCar_Data_ID.Text & "'"
        cmd.CommandText = S : H = cmd.ExecuteNonQuery()

        MsgBox("تم حفظ التعديل بنجاح", MsgBoxStyle.Information)
        Header()
        CLEAR_ALL()
        Cls.fill_combo("Maintenance_Car_Data", "CC", cmbCC)
        Cls.fill_combo("Maintenance_Car_Data", "Cylinders_No", cmbCylinders_No)
        Cls.fill_combo("Maintenance_Car_Data", "Valves_No", cmbValves_No)
        txtCar_Data_ID.Text = Cls.MAXRECORD("Maintenance_Car_Data", "Car_Data_ID")
    End Sub

    Function ValedateEdit() As Boolean
        If rdoNumberPlatesLetters.Checked = True Then
            If Trim(txtPaintings_Letters_Space.Text) = "" Then
                MsgBox("فضلاً أدخل الحروف", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
                txtPaintings_Letters_Space.Focus() : Return False : Exit Function
            End If
            If Trim(txtPaintings_No_Space.Text) = "" Then
                MsgBox("فضلاً أدخل الارقام", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
                txtPaintings_No_Space.Focus() : Return False : Exit Function
            End If
        ElseIf rdoOnlyNumberPlates.Checked = True Then
            If Trim(cmbMaintenance_Cities.Text) = "" Then
                MsgBox("فضلاً أدخل المدينة", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
                cmbMaintenance_Cities.Focus() : Return False : Exit Function
            End If
            If Trim(txtCitiesNumber.Text) = "" Then
                MsgBox("فضلاً أدخل رقم اللوحة", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
                txtCitiesNumber.Focus() : Return False : Exit Function
            End If
        End If

        If Trim(cmbTypeProduct.Text) = "" Then
            MsgBox("فضلاً أدخل نوع المركبة", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            cmbTypeProduct.Focus() : Return False : Exit Function
        End If

        Return True
    End Function

    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If Trim(txtCar_Data_ID.Text) = "" Then
            MsgBox("فضلاً ادخل كود السيارة", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtCar_Data_ID.Focus()
            Exit Sub
        End If

        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete  from Maintenance_Car_Data  WHERE Car_Data_ID =N'" & txtCar_Data_ID.Text & "'" : cmd.ExecuteNonQuery()
        Header()
        CLEAR_ALL()
        txtCar_Data_ID.Text = Cls.MAXRECORD("Maintenance_Car_Data", "Car_Data_ID")
    End Sub

    Private Sub BtnFind_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnFind.Click
        Panel_Search.Top = 20
        Panel_Search.Dock = DockStyle.Fill
    End Sub

    Private Sub DataGridView1_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DataGridView1.DoubleClick
        Dim ItmID As String
        ItmID = DataGridView1.SelectedRows(0).Cells(0).Value

        Dim Paintings_Type As String = "" : Dim Paintings_No As String = "" : Dim Paintings_Letters As String = ""
        Cls.Select_More_Data_Stores("View_Maintenance_Car_Data", "Car_Data_ID,Paintings_Type,Paintings_No,Paintings_Letters,Vendorname,TypeProduct_Name,DeviceBrand_Name,DeviceModel_Name,Manu_Year,CC,Cylinders_No,Valves_No,Chassis_No,Description", "Car_Data_ID=N'" & ItmID & "'")
        If dr.HasRows = True Then
            txtCar_Data_ID.Text = dr("Car_Data_ID").ToString
            Paintings_Type = dr("Paintings_Type").ToString
            Paintings_No = dr("Paintings_No").ToString
            Paintings_Letters = dr("Paintings_Letters").ToString
            cmbCustomer.Text = dr("Vendorname").ToString
            cmbTypeProduct.Text = dr("TypeProduct_Name").ToString
            cmbDeviceBrand.Text = dr("DeviceBrand_Name").ToString
            cmbDeviceModel.Text = dr("DeviceModel_Name").ToString
            cmbManu_Year.Text = dr("Manu_Year").ToString
            cmbCC.Text = dr("CC").ToString
            cmbCylinders_No.Text = dr("Cylinders_No").ToString
            cmbValves_No.Text = dr("Valves_No").ToString
            txtChassis_No.Text = dr("Chassis_No").ToString
            txtDescription.Text = dr("Description").ToString
        End If

        If Paintings_Type = "لوحات ارقام وحروف" Then
            rdoNumberPlatesLetters.Checked = True
            txtPaintings_No.Text = Paintings_No
            txtPaintings_Letters.Text = Paintings_Letters
            txtPaintings_No_Space.Text = Result_Space_Text(Paintings_No)
            txtPaintings_Letters_Space.Text = Result_Space_Text(Paintings_Letters)
        ElseIf Paintings_Type = "لوحات ارقام فقط" Then
            rdoOnlyNumberPlates.Checked = True
            txtCitiesNumber.Text = Paintings_No
            cmbMaintenance_Cities.Text = Paintings_Letters
        End If

        Panel_Search.Dock = DockStyle.None
        Panel_Search.Top = 1000
    End Sub

    Function Result_Space_Text(ByVal Text As String)
        Dim result As String, str As String, ret As String
        Dim i As Integer
        Dim arr As Char()
        result = Text
        result = StrReverse(result)
        i = 0
        ret = ""
        arr = result.Take(result.Length).ToArray
        For Each str In arr
            If str <> " " Then
                ret = ret + str
                i = i + 1
            End If
        Next
        ret = StrReverse(ret)
        Return ret
    End Function

    Private Sub btnBack_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBack.Click
        Panel_Search.Dock = DockStyle.None
        Panel_Search.Top = 1000
    End Sub

    Private Sub txtSearch_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearch.TextChanged
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If txtSearch.Text = "" Then
            S = "SELECT  Car_Data_ID as [رقم], Car_Number as [رقم السيارة], Vendorname as [أسم العميل], TypeProduct_Name as [نوع المركبة], DeviceBrand_Name as [الماركة], DeviceModel_Name as [الموديل],Manu_Year as [سنة الصنع],CC AS [CC],Cylinders_No AS [عدد السلندرات],Valves_No as [عدد الصبابات],Chassis_No as [رقم الشاسية],Description as [وصف المركبة] FROM  View_Maintenance_Car_Data"
        Else
            S = "SELECT  Car_Data_ID as [رقم], Car_Number as [رقم السيارة], Vendorname as [أسم العميل], TypeProduct_Name as [نوع المركبة], DeviceBrand_Name as [الماركة], DeviceModel_Name as [الموديل],Manu_Year as [سنة الصنع],CC AS [CC],Cylinders_No AS [عدد السلندرات],Valves_No as [عدد الصبابات],Chassis_No as [رقم الشاسية],Description as [وصف المركبة] FROM  View_Maintenance_Car_Data  WHERE Car_Number Like N'%" & txtSearch.Text & "%' or Vendorname Like N'%" & txtSearch.Text & "%' or tel1 Like N'%" & txtSearch.Text & "%' or DeviceBrand_Name Like N'%" & txtSearch.Text & "%' or DeviceModel_Name Like N'%" & txtSearch.Text & "%' or Manu_Year Like N'%" & txtSearch.Text & "%'"
        End If
        S = S & " order by 1"
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

    End Sub

    Private Sub rdoNumberPlatesLetters_CheckedChanged(sender As Object, e As EventArgs) Handles rdoNumberPlatesLetters.CheckedChanged
        If rdoNumberPlatesLetters.Checked = True Then
            Panel_NumberPlatesLetters.Visible = True
            Panel_OnlyNumberPlates.Visible = False
            Panel_NumberPlatesLetters.Location = New System.Drawing.Point(12, 20)
        End If
        If rdoOnlyNumberPlates.Checked = True Then
            Panel_NumberPlatesLetters.Visible = False
            Panel_OnlyNumberPlates.Visible = True
            Panel_OnlyNumberPlates.Location = New System.Drawing.Point(12, 20)
        End If
    End Sub

    Private Sub rdoOnlyNumberPlates_CheckedChanged(sender As Object, e As EventArgs) Handles rdoOnlyNumberPlates.CheckedChanged
        If rdoNumberPlatesLetters.Checked = True Then
            Panel_NumberPlatesLetters.Visible = True
            Panel_OnlyNumberPlates.Visible = False
        End If
        If rdoOnlyNumberPlates.Checked = True Then
            Panel_NumberPlatesLetters.Visible = False
            Panel_OnlyNumberPlates.Visible = True
        End If
    End Sub

    Private Sub txtPaintings_No_Space_TextChanged(sender As Object, e As EventArgs) Handles txtPaintings_Letters_Space.TextChanged
        Dim result As String, str As String, ret As String
        Dim i As Integer
        Dim arr As Char()
        result = txtPaintings_Letters_Space.Text
        result = StrReverse(result)
        i = 0
        ret = ""
        ' make a char array which each char is an own array element
        arr = result.Take(result.Length).ToArray
        'iterate through all elements
        For Each str In arr
            ' skip the first element . 
            ' only add a space every 3 elements 
            'If (i <> 0) And (i Mod 3 = 0) Then
            ret = ret + " "
            'End If
            ret = ret + str
            i = i + 1
        Next
        ret = StrReverse(ret)
        txtPaintings_Letters.Text = ret
    End Sub

    Private Sub txtCar_Data_ID_KeyUp(sender As Object, e As KeyEventArgs) Handles txtCar_Data_ID.KeyUp
        If rdoNumberPlatesLetters.Checked = True Then
            If e.KeyCode = 13 Then
                txtPaintings_Letters_Space.Focus()
            End If
        Else
            If e.KeyCode = 13 Then
                cmbMaintenance_Cities.Focus()
            End If
        End If
    End Sub

    Private Sub txtPaintings_No_Space_KeyUp(sender As Object, e As KeyEventArgs) Handles txtPaintings_Letters_Space.KeyUp
        If e.KeyCode = 13 Then
            txtPaintings_No_Space.Focus()
        End If
    End Sub

    Private Sub txtPaintings_No_Space_KeyUp_1(sender As Object, e As KeyEventArgs) Handles txtPaintings_No_Space.KeyUp
        If e.KeyCode = 13 Then
            cmbCustomer.Focus()
        End If
    End Sub

    Private Sub txtCitiesNumber_KeyUp(sender As Object, e As KeyEventArgs) Handles txtCitiesNumber.KeyUp
        If e.KeyCode = 13 Then
            cmbCustomer.Focus()
        End If
    End Sub

    Private Sub cmbCustomer_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbCustomer.KeyUp
        If e.KeyCode = 13 Then
            cmbTypeProduct.Focus()
        End If
    End Sub

    Private Sub cmbDeviceBrand_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbDeviceBrand.KeyUp
        If e.KeyCode = 13 Then
            cmbDeviceModel.Focus()
        End If
    End Sub

    Private Sub cmbDeviceModel_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbDeviceModel.KeyUp
        If e.KeyCode = 13 Then
            cmbManu_Year.Focus()
        End If
    End Sub

    Private Sub cmbManu_Year_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbManu_Year.KeyUp
        If e.KeyCode = 13 Then
            cmbCC.Focus()
        End If
    End Sub

    Private Sub cmbCC_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbCC.KeyUp
        If e.KeyCode = 13 Then
            cmbCylinders_No.Focus()
        End If
    End Sub

    Private Sub cmbCylinders_No_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbCylinders_No.KeyUp
        If e.KeyCode = 13 Then
            cmbValves_No.Focus()
        End If
    End Sub

    Private Sub cmbTypeProduct_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbTypeProduct.KeyUp
        If e.KeyCode = 13 Then
            cmbDeviceBrand.Focus()
        End If
    End Sub

    Private Sub cmbValves_No_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbValves_No.KeyUp
        If e.KeyCode = 13 Then
            txtChassis_No.Focus()
        End If
    End Sub

    Private Sub txtChassis_No_KeyUp(sender As Object, e As KeyEventArgs) Handles txtChassis_No.KeyUp
        If e.KeyCode = 13 Then
            txtDescription.Focus()
        End If
    End Sub

    Private Sub txtDescription_KeyUp(sender As Object, e As KeyEventArgs) Handles txtDescription.KeyUp
        If e.KeyCode = 13 Then
            BtnAdd.PerformClick()
        End If
    End Sub

    Private Sub txtPaintings_No_Space_TextChanged_1(sender As Object, e As EventArgs) Handles txtPaintings_No_Space.TextChanged
        Dim result As String, str As String, ret As String
        Dim i As Integer
        Dim arr As Char()
        result = txtPaintings_No_Space.Text
        result = StrReverse(result)
        i = 0
        ret = ""
        ' make a char array which each char is an own array element
        arr = result.Take(result.Length).ToArray
        'iterate through all elements
        For Each str In arr
            ' skip the first element . 
            ' only add a space every 3 elements 
            'If (i <> 0) And (i Mod 3 = 0) Then
            ret = ret + " "
            'End If
            ret = ret + str
            i = i + 1
        Next
        ret = StrReverse(ret)
        txtPaintings_No.Text = ret
    End Sub

    Private Sub Frm_Maintenance_Car_Data_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        Panel_Search.Top = 1000
        Header()
        Cls.fill_combo("Maintenance_Cities", "Cities_Name", cmbMaintenance_Cities)
        Cls.fill_combo("Customers", "Vendorname", cmbCustomer)
        Cls.fill_combo_Branch("Maintenance_Drivers", "Driv_Name", cmbDrivers)
        Bra.Fil("MaintenanceTypeProduct", "TypeProduct_Name", cmbTypeProduct)
        Bra.Fil("MaintenanceDeviceBrand", "DeviceBrand_Name", cmbDeviceBrand)
        Bra.Fil("MaintenanceDeviceModel", "DeviceModel_Name", cmbDeviceModel)
        Cls.fill_combo("Maintenance_Manu_Year", "Manu_Year", cmbManu_Year)
        Cls.fill_combo("Maintenance_Car_Data", "CC", cmbCC)
        Cls.fill_combo("Maintenance_Car_Data", "Cylinders_No", cmbCylinders_No)
        Cls.fill_combo("Maintenance_Car_Data", "Valves_No", cmbValves_No)
        txtCar_Data_ID.Text = Cls.MAXRECORD("Maintenance_Car_Data", "Car_Data_ID")

        cmbCustomer.Focus()
        cmbCustomer.SelectAll()
    End Sub

    Private Sub Button5_Click(sender As Object, e As EventArgs) Handles Button5.Click
        Frm_MaintenanceDeviceModel.Show()
    End Sub

    Private Sub Button6_Click(sender As Object, e As EventArgs) Handles Button6.Click
        Frm_MaintenanceTypeProduct.Show()
    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs) Handles Button3.Click
        Frm_Maintenance_ReceivingCar.Show()
    End Sub

    Private Sub Button4_Click(sender As Object, e As EventArgs) Handles Button4.Click
        Frm_MaintenanceDeviceBrand.Show()
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Frm_Maintenance_Supervisor.Show()
    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        Frm_Maintenance_Recipient.Show()
    End Sub
End Class