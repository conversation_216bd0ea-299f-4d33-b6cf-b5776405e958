﻿Imports CrystalDecisions.CrystalReports.Engine
Imports System.IO

Public Class Frm_daySales
    Dim WithEvents BS As New BindingSource
    Dim ColorWithItems As String = mykey.GetValue("UseColorWithItems", "NO")

    Private Sub ChkWithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub FillData()
        DataGridView1.DataSource = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        Try
            If RdoSalPrice.Checked = True Then
                If ColorWithItems = "" Then
                    S = "Select CAST(dbo.BillsalData.bill_no AS float) AS [رقم الفاتورة], dbo.BillsalData.bill_date As التاريخ, dbo.BillsalData.billtime As الوقت, dbo.BillsalData.itm_id As الباركود, dbo.BillsalData.itm_cat As المجموعة,   dbo.BillsalData.itm_name AS الصنف, dbo.BillsalData.price As [سعر البيع], dbo.BillsalData.qu_unity As الكمية, dbo.BillsalData.totalprice As الاجمالى, dbo.BillsalData.Stores As المخزن, dbo.BillsalData.Stat As الحالة,    dbo.BillsalData.Vendorname AS [اسم العميل], dbo.BillsalData.itm_Notes, dbo.BillsalData.DiscountsValue As [قيمة الخصم], dbo.BillsalData.StateDisc As [نوع الخصم], dbo.BillsalData.EmpName As الموظف,   dbo.Sales_Bill.Notes AS [ملاحظات الفاتورة], dbo.BillsalData.Discounts As [نسبة الخصم الصنف], dbo.Sales_Bill.disc As [قيمة الخصم الفاتورة], dbo.BillsalData.Discount_Price_After As [نسبة الخصم الفاتورة],   dbo.Sales_Bill.PriceBeforeDiscValue AS [قيمة قبل الخصم الفاتورة] From dbo.BillsalData RIGHT OUTER Join   dbo.Sales_Bill ON dbo.BillsalData.bill_no = dbo.Sales_Bill.bill_No Where (dbo.BillsalData.bill_date <> N'جرد')"
                Else
                    S = "Select CAST(dbo.BillsalData.bill_no AS float) AS [رقم الفاتورة], dbo.BillsalData.bill_date As التاريخ, dbo.BillsalData.billtime As الوقت, dbo.BillsalData.itm_id As الباركود, dbo.BillsalData.itm_cat As المجموعة,   dbo.BillsalData.itm_name AS الصنف, dbo.BillsalData.price As [سعر البيع], dbo.BillsalData.qu_unity As الكمية, dbo.BillsalData.totalprice As الاجمالى, dbo.BillsalData.Stores As المخزن, dbo.BillsalData.Stat As الحالة,    dbo.BillsalData.Vendorname AS [اسم العميل], dbo.BillsalData.itm_Notes as [" & ColorWithItems & "], dbo.BillsalData.DiscountsValue As [قيمة الخصم], dbo.BillsalData.StateDisc As [نوع الخصم], dbo.BillsalData.EmpName As الموظف,   dbo.Sales_Bill.Notes AS [ملاحظات الفاتورة], dbo.BillsalData.Discounts As [نسبة الخصم الصنف], dbo.Sales_Bill.disc As [قيمة الخصم الفاتورة], dbo.BillsalData.Discount_Price_After As [نسبة الخصم الفاتورة],   dbo.Sales_Bill.PriceBeforeDiscValue AS [قيمة قبل الخصم الفاتورة] From dbo.BillsalData RIGHT OUTER Join   dbo.Sales_Bill ON dbo.BillsalData.bill_no = dbo.Sales_Bill.bill_No Where (dbo.BillsalData.bill_date <> N'جرد')"
                End If
            End If
            If RdoTinPrice.Checked = True Then
                If ColorWithItems = "" Then
                    S = "Select CAST(dbo.BillsalData.bill_no AS float) AS [رقم الفاتورة], dbo.BillsalData.bill_date As التاريخ, dbo.BillsalData.billtime As الوقت, dbo.BillsalData.itm_id As الباركود, dbo.BillsalData.itm_cat As المجموعة,   dbo.BillsalData.itm_name AS الصنف, dbo.BillsalData.TinPriceAverage as [سعر الشراء], dbo.BillsalData.qu_unity As الكمية, dbo.BillsalData.totalprice As الاجمالى, dbo.BillsalData.Stores As المخزن, dbo.BillsalData.Stat As الحالة,    dbo.BillsalData.Vendorname AS [اسم العميل], dbo.BillsalData.itm_Notes, dbo.BillsalData.DiscountsValue As [قيمة الخصم], dbo.BillsalData.StateDisc As [نوع الخصم], dbo.BillsalData.EmpName As الموظف,   dbo.Sales_Bill.Notes AS [ملاحظات الفاتورة], dbo.BillsalData.Discounts As [نسبة الخصم الصنف], dbo.Sales_Bill.disc As [قيمة الخصم الفاتورة], dbo.BillsalData.Discount_Price_After As [نسبة الخصم الفاتورة],   dbo.Sales_Bill.PriceBeforeDiscValue AS [قيمة قبل الخصم الفاتورة] From dbo.BillsalData RIGHT OUTER Join   dbo.Sales_Bill ON dbo.BillsalData.bill_no = dbo.Sales_Bill.bill_No Where (dbo.BillsalData.bill_date <> N'جرد')"
                Else
                    S = "Select CAST(dbo.BillsalData.bill_no AS float) AS [رقم الفاتورة], dbo.BillsalData.bill_date As التاريخ, dbo.BillsalData.billtime As الوقت, dbo.BillsalData.itm_id As الباركود, dbo.BillsalData.itm_cat As المجموعة,   dbo.BillsalData.itm_name AS الصنف, dbo.BillsalData.TinPriceAverage as [سعر الشراء], dbo.BillsalData.qu_unity As الكمية, dbo.BillsalData.totalprice As الاجمالى, dbo.BillsalData.Stores As المخزن, dbo.BillsalData.Stat As الحالة,    dbo.BillsalData.Vendorname AS [اسم العميل], dbo.BillsalData.itm_Notes as [" & ColorWithItems & "], dbo.BillsalData.DiscountsValue As [قيمة الخصم], dbo.BillsalData.StateDisc As [نوع الخصم], dbo.BillsalData.EmpName As الموظف,   dbo.Sales_Bill.Notes AS [ملاحظات الفاتورة], dbo.BillsalData.Discounts As [نسبة الخصم الصنف], dbo.Sales_Bill.disc As [قيمة الخصم الفاتورة], dbo.BillsalData.Discount_Price_After As [نسبة الخصم الفاتورة],   dbo.Sales_Bill.PriceBeforeDiscValue AS [قيمة قبل الخصم الفاتورة] From dbo.BillsalData RIGHT OUTER Join   dbo.Sales_Bill ON dbo.BillsalData.bill_no = dbo.Sales_Bill.bill_No Where (dbo.BillsalData.bill_date <> N'جرد')"
                End If
            End If
            If chkChasherAll.Checked = False Then
                If cmbCasher.Text <> "" Then
                    S = S & " and dbo.BillsalData.username =N'" & cmbCasher.Text.Trim & "'"
                End If
                If CmbSeller.Text <> "" Then
                    S = S & " and dbo.BillsalData.EmpName =N'" & CmbSeller.Text.Trim & "'"
                End If
                If txtSheft_Status.Text <> "" Then
                    S = S & " and dbo.BillsalData.Sheft_Number =N'" & txtSheft_Status.Text.Trim & "'"
                End If
                If txtbillno.Text <> "" Then
                    S = S & " and dbo.BillsalData.bill_no =N'" & txtbillno.Text.Trim & "'"
                End If
                If TxtPrc.Text <> "" Then
                    S = S & " and dbo.BillsalData.itm_id =N'" & TxtPrc.Text.Trim & "'"
                End If
                If cmbcats.Text <> "" Then
                    S = S & " and dbo.BillsalData.itm_cat =N'" & cmbcats.Text.Trim & "'"
                End If
                If cmbitmnm.Text <> "" Then
                    S = S & " and dbo.BillsalData.itm_name =N'" & cmbitmnm.Text.Trim & "'"
                End If
                If cmbCustomers.Text <> "" Then
                    S = S & " and dbo.BillsalData.Vendorname =N'" & cmbCustomers.Text.Trim & "'"
                End If
                If cmbStores.Text <> "" Then
                    S = S & " and dbo.BillsalData.Stores =N'" & cmbStores.Text.Trim & "'"
                End If
                If cmbvendores.Text <> "" Then
                    S = S & " and dbo.BillsalData.ResourceName =N'" & cmbvendores.Text.Trim & "'"
                End If
            End If
            If chkState.Checked = False Then
                If ChkCash.Checked = True Then
                    S = S & " and dbo.BillsalData.Stat =N'" & ChkCash.Text.Trim & "'"
                End If
                If rdoStatus.Checked = True Then
                    S = S & " and dbo.BillsalData.Stat =N'" & rdoStatus.Text.Trim & "'"
                End If
                If rdoVisa.Checked = True Then
                    S = S & " and dbo.BillsalData.Stat =N'" & rdoVisa.Text.Trim & "'"
                End If
                If rdoCashWallet.Checked = True Then
                    S = S & " and dbo.BillsalData.Stat =N'" & rdoCashWallet.Text.Trim & "'"
                End If
            End If
            'If ChkWithoutDate.Checked = False Then
            '    S = S & " AND CONVERT(DATE, dbo.BillsalData.bill_date) BETWEEN '" &
            '          Format(DateTimePicker1.Value, "yyyy-MM-dd") & "' AND '" &
            '          Format(DateTimePicker2.Value, "yyyy-MM-dd") & "'"
            'End If
            If ChkWithoutDate.Checked = False Then
                S = S & " and dbo.BillsalData.bill_date BETWEEN N'" & Cls.C_date(DateTimePicker1.Text) & "' AND N'" & Cls.C_date(DateTimePicker2.Text) & "'"
                'S = S & " and dbo.BillsalData.bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and dbo.BillsalData.bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
            End If
            If chkTime.Checked = False Then
                Dim TimeAmPm24Hour1 As String = Cls.SlpitTime24Hour(dtpTime1.Text)
                Dim TimeAmPm24Hour2 As String = Cls.SlpitTime24Hour(dtpTime2.Text)
                S = S & " and dbo.BillsalData.billtime BETWEEN N'" & TimeAmPm24Hour1 & "' AND N'" & TimeAmPm24Hour2 & "'"
                'S = S & " and dbo.BillsalData.billtime >=N'" & TimeAmPm24Hour1 & "' and dbo.BillsalData.billtime <=N'" & TimeAmPm24Hour2 & "'"
            End If
            If PermtionName <> "مدير" Then
                S = S & " and dbo.BillsalData.Company_Branch_ID =N'" & Company_Branch_ID & "'"
            End If
            If chkPendingBill.Checked = True Then
                S = S & " and dbo.Sales_Bill.PendingBill =N'1'"
            Else
                S = S & " and dbo.Sales_Bill.PendingBill =N'0'"
            End If
            If FilterSelect = "Number" Then
                S = S & " order by [رقم الفاتورة]"
            End If
            If FilterSelect = "Date" Then
                S = S & " order by [التاريخ]"
            End If
            If FilterSelect = "Name" Then
                S = S & " order by [اسم العميل]"
            End If
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DataGridView1.DataSource = Cls.PopulateDataView(dr)
            If DataGridView1.Columns.Count > 5 Then
                DataGridView1.Columns(5).Width = 100
                DataGridView1.Columns(6).Width = 140
                DataGridView1.Columns(9).Width = 100

                If ColorWithItems = "YES" Then
                    DataGridView1.Columns(12).Visible = True
                Else
                    DataGridView1.Columns(12).Visible = False
                End If
            End If

        Catch ex As Exception
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If RdoSalPrice.Checked = True Then
                If ColorWithItems = "" Then
                    S = "Select dbo.BillsalData.bill_no As [رقم الفاتورة], dbo.BillsalData.bill_date As التاريخ, dbo.BillsalData.billtime As الوقت, dbo.BillsalData.itm_id As الباركود, dbo.BillsalData.itm_cat As المجموعة,   dbo.BillsalData.itm_name AS الصنف, dbo.BillsalData.price As [سعر البيع], dbo.BillsalData.qu_unity As الكمية, dbo.BillsalData.totalprice As الاجمالى, dbo.BillsalData.Stores As المخزن, dbo.BillsalData.Stat As الحالة,    dbo.BillsalData.Vendorname AS [اسم العميل], dbo.BillsalData.itm_Notes, dbo.BillsalData.DiscountsValue As [قيمة الخصم], dbo.BillsalData.StateDisc As [نوع الخصم], dbo.BillsalData.EmpName As الموظف,   dbo.Sales_Bill.Notes AS [ملاحظات الفاتورة], dbo.BillsalData.Discounts As [نسبة الخصم الصنف], dbo.Sales_Bill.disc As [قيمة الخصم الفاتورة], dbo.BillsalData.Discount_Price_After As [نسبة الخصم الفاتورة],   dbo.Sales_Bill.PriceBeforeDiscValue AS [قيمة قبل الخصم الفاتورة] From dbo.BillsalData RIGHT OUTER Join   dbo.Sales_Bill ON dbo.BillsalData.bill_no = dbo.Sales_Bill.bill_No Where (dbo.BillsalData.bill_date <> N'جرد')"
                Else
                    S = "Select dbo.BillsalData.bill_no As [رقم الفاتورة], dbo.BillsalData.bill_date As التاريخ, dbo.BillsalData.billtime As الوقت, dbo.BillsalData.itm_id As الباركود, dbo.BillsalData.itm_cat As المجموعة,   dbo.BillsalData.itm_name AS الصنف, dbo.BillsalData.price As [سعر البيع], dbo.BillsalData.qu_unity As الكمية, dbo.BillsalData.totalprice As الاجمالى, dbo.BillsalData.Stores As المخزن, dbo.BillsalData.Stat As الحالة,    dbo.BillsalData.Vendorname AS [اسم العميل], dbo.BillsalData.itm_Notes as [" & ColorWithItems & "], dbo.BillsalData.DiscountsValue As [قيمة الخصم], dbo.BillsalData.StateDisc As [نوع الخصم], dbo.BillsalData.EmpName As الموظف,   dbo.Sales_Bill.Notes AS [ملاحظات الفاتورة], dbo.BillsalData.Discounts As [نسبة الخصم الصنف], dbo.Sales_Bill.disc As [قيمة الخصم الفاتورة], dbo.BillsalData.Discount_Price_After As [نسبة الخصم الفاتورة],   dbo.Sales_Bill.PriceBeforeDiscValue AS [قيمة قبل الخصم الفاتورة] From dbo.BillsalData RIGHT OUTER Join   dbo.Sales_Bill ON dbo.BillsalData.bill_no = dbo.Sales_Bill.bill_No Where (dbo.BillsalData.bill_date <> N'جرد')"
                End If
            End If
            If RdoTinPrice.Checked = True Then
                If ColorWithItems = "" Then
                    S = "Select dbo.BillsalData.bill_no As [رقم الفاتورة], dbo.BillsalData.bill_date As التاريخ, dbo.BillsalData.billtime As الوقت, dbo.BillsalData.itm_id As الباركود, dbo.BillsalData.itm_cat As المجموعة,   dbo.BillsalData.itm_name AS الصنف, dbo.BillsalData.TinPriceAverage as [سعر الشراء], dbo.BillsalData.qu_unity As الكمية, dbo.BillsalData.totalprice As الاجمالى, dbo.BillsalData.Stores As المخزن, dbo.BillsalData.Stat As الحالة,    dbo.BillsalData.Vendorname AS [اسم العميل], dbo.BillsalData.itm_Notes, dbo.BillsalData.DiscountsValue As [قيمة الخصم], dbo.BillsalData.StateDisc As [نوع الخصم], dbo.BillsalData.EmpName As الموظف,   dbo.Sales_Bill.Notes AS [ملاحظات الفاتورة], dbo.BillsalData.Discounts As [نسبة الخصم الصنف], dbo.Sales_Bill.disc As [قيمة الخصم الفاتورة], dbo.BillsalData.Discount_Price_After As [نسبة الخصم الفاتورة],   dbo.Sales_Bill.PriceBeforeDiscValue AS [قيمة قبل الخصم الفاتورة] From dbo.BillsalData RIGHT OUTER Join   dbo.Sales_Bill ON dbo.BillsalData.bill_no = dbo.Sales_Bill.bill_No Where (dbo.BillsalData.bill_date <> N'جرد')"
                Else
                    S = "Select dbo.BillsalData.bill_no As [رقم الفاتورة], dbo.BillsalData.bill_date As التاريخ, dbo.BillsalData.billtime As الوقت, dbo.BillsalData.itm_id As الباركود, dbo.BillsalData.itm_cat As المجموعة,   dbo.BillsalData.itm_name AS الصنف, dbo.BillsalData.TinPriceAverage as [سعر الشراء], dbo.BillsalData.qu_unity As الكمية, dbo.BillsalData.totalprice As الاجمالى, dbo.BillsalData.Stores As المخزن, dbo.BillsalData.Stat As الحالة,    dbo.BillsalData.Vendorname AS [اسم العميل], dbo.BillsalData.itm_Notes as [" & ColorWithItems & "], dbo.BillsalData.DiscountsValue As [قيمة الخصم], dbo.BillsalData.StateDisc As [نوع الخصم], dbo.BillsalData.EmpName As الموظف,   dbo.Sales_Bill.Notes AS [ملاحظات الفاتورة], dbo.BillsalData.Discounts As [نسبة الخصم الصنف], dbo.Sales_Bill.disc As [قيمة الخصم الفاتورة], dbo.BillsalData.Discount_Price_After As [نسبة الخصم الفاتورة],   dbo.Sales_Bill.PriceBeforeDiscValue AS [قيمة قبل الخصم الفاتورة] From dbo.BillsalData RIGHT OUTER Join   dbo.Sales_Bill ON dbo.BillsalData.bill_no = dbo.Sales_Bill.bill_No Where (dbo.BillsalData.bill_date <> N'جرد')"
                End If
            End If
            If chkChasherAll.Checked = False Then
                If cmbCasher.Text <> "" Then
                    S = S & " and dbo.BillsalData.username =N'" & cmbCasher.Text.Trim & "'"
                End If
                If CmbSeller.Text <> "" Then
                    S = S & " and dbo.BillsalData.EmpName =N'" & CmbSeller.Text.Trim & "'"
                End If
                If txtSheft_Status.Text <> "" Then
                    S = S & " and dbo.BillsalData.Sheft_Number =N'" & txtSheft_Status.Text.Trim & "'"
                End If
                If txtbillno.Text <> "" Then
                    S = S & " and dbo.BillsalData.bill_no =N'" & txtbillno.Text.Trim & "'"
                End If
                If TxtPrc.Text <> "" Then
                    S = S & " and dbo.BillsalData.itm_id =N'" & TxtPrc.Text.Trim & "'"
                End If
                If cmbcats.Text <> "" Then
                    S = S & " and dbo.BillsalData.itm_cat =N'" & cmbcats.Text.Trim & "'"
                End If
                If cmbitmnm.Text <> "" Then
                    S = S & " and dbo.BillsalData.itm_name =N'" & cmbitmnm.Text.Trim & "'"
                End If
                If cmbCustomers.Text <> "" Then
                    S = S & " and dbo.BillsalData.Vendorname =N'" & cmbCustomers.Text.Trim & "'"
                End If
                If cmbStores.Text <> "" Then
                    S = S & " and dbo.BillsalData.Stores =N'" & cmbStores.Text.Trim & "'"
                End If
                If cmbvendores.Text <> "" Then
                    S = S & " and dbo.BillsalData.ResourceName =N'" & cmbvendores.Text.Trim & "'"
                End If
            End If
            If chkState.Checked = False Then
                If ChkCash.Checked = True Then
                    S = S & " and dbo.BillsalData.Stat =N'" & ChkCash.Text.Trim & "'"
                End If
                If rdoStatus.Checked = True Then
                    S = S & " and dbo.BillsalData.Stat =N'" & rdoStatus.Text.Trim & "'"
                End If
                If rdoVisa.Checked = True Then
                    S = S & " and dbo.BillsalData.Stat =N'" & rdoVisa.Text.Trim & "'"
                End If
            End If
            If ChkWithoutDate.Checked = False Then
                S = S & " and dbo.BillsalData.bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and dbo.BillsalData.bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
            End If
            If chkTime.Checked = False Then
                Dim TimeAmPm24Hour1 As String = Cls.SlpitTime24Hour(dtpTime1.Text)
                Dim TimeAmPm24Hour2 As String = Cls.SlpitTime24Hour(dtpTime2.Text)
                S = S & " and dbo.BillsalData.billtime >=N'" & TimeAmPm24Hour1 & "' and dbo.BillsalData.billtime <=N'" & TimeAmPm24Hour2 & "'"
            End If
            If PermtionName <> "مدير" Then
                S = S & " and dbo.BillsalData.Company_Branch_ID =N'" & Company_Branch_ID & "'"
            End If
            If chkPendingBill.Checked = True Then
                S = S & " and dbo.Sales_Bill.PendingBill =N'1'"
            Else
                S = S & " and dbo.Sales_Bill.PendingBill =N'0'"
            End If
            If FilterSelect = "Number" Then
                S = S & " order by [رقم الفاتورة]"
            End If
            If FilterSelect = "Date" Then
                S = S & " order by [التاريخ]"
            End If
            If FilterSelect = "Name" Then
                S = S & " order by [اسم العميل]"
            End If
            cmd.CommandText = S : dr = cmd.ExecuteReader
            DataGridView1.DataSource = Cls.PopulateDataView(dr)
            DataGridView1.Columns(5).Width = 100
            DataGridView1.Columns(6).Width = 140
            DataGridView1.Columns(9).Width = 100
            If ColorWithItems = "YES" Then
                DataGridView1.Columns(12).Visible = True
            Else
                DataGridView1.Columns(12).Visible = False
            End If
        End Try

        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(1).Value.ToString)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(1).Value = SM
        Next

        'Dim SM As String
        'For i As Integer = 0 To DataGridView1.RowCount - 1
        '    SM = Val(DataGridView1.Rows(i).Cells(1).Value)
        '    SM = Cls.R_date(SM)
        '    DataGridView1.Rows(i).Cells(1).Value = SM
        '    'DataGridView1.Rows(i).Cells(16).Value = Cls.Get_Code_Value_Stores_More("Sales_Bill", "Notes", "bill_No=N'" & DataGridView1.Rows(i).Cells(0).Value & "'")
        '    'DataGridView1.Rows(i).Cells(18).Value = Cls.Get_Code_Value_Stores_More("Sales_Bill", "disc", "bill_No=N'" & DataGridView1.Rows(i).Cells(0).Value & "'")
        '    Dim totalpricebeforedisc As String = Cls.Get_Code_Value_Stores_More("Sales_Bill", "totalpricebeforedisc", "bill_No=N'" & DataGridView1.Rows(i).Cells(0).Value & "'")
        '    If Val(DataGridView1.Rows(i).Cells(18).Value) <> 0 Then
        '        DataGridView1.Rows(i).Cells(19).Value = Val(totalpricebeforedisc) / Val(DataGridView1.Rows(i).Cells(18).Value)
        '        DataGridView1.Rows(i).Cells(19).Value = Math.Round(DataGridView1.Rows(i).Cells(19).Value, 2)
        '    End If
        'Next

        sumgridtotal()
        sumgridtotalBSal()
        GetProfits()


    End Sub

    Private Sub BtnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnShow.Click
        GetSerialNumber()
        FillData()
        'FillSalesBill()
        GetBsal()
        GetDataDiscSal()
        GetDataIM_Vst_disc()
        If chkExpenses.Checked = True Then
            GetExpenses()
        End If
        txtTotal.Text = Val(txttotalprisesal.Text) - Val(txtSumSalBsal.Text) - Val(txtDsic.Text) - Val(txtExpenses.Text) - Val(txtDiscountsValue.Text) + Val(txtDsicBSal.Text)
        GetProfits()
        GetTotalCashAndVisaAndStatus()
    End Sub

    Private Sub GetExpenses()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT Cats AS [المجموعة],Exp_Name AS [أسم المصروف],Exp_Value AS [المبلغ], Exp_Date AS [تاريخ ] FROM Expenses where id <> N''"

        If cmbCasher.Text <> "" Then
            If chkChasherAll.Checked = False Then
                S = S & " and UserName =N'" & cmbCasher.Text.Trim & "'"
            End If
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and Exp_Date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and Exp_Date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " and Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView4.DataSource = Cls.PopulateDataView(dr)
        DataGridView4.Columns(1).Visible = False

        Dim SM As String
        For i As Integer = 0 To DataGridView4.RowCount - 1
            SM = Val(DataGridView4.Rows(i).Cells(3).Value)
            SM = Cls.R_date(SM)
            DataGridView4.Rows(i).Cells(3).Value = SM
        Next

        Dim SM4 As Double
        For i As Integer = 0 To DataGridView4.Rows.Count - 1
            SM4 = SM4 + DataGridView4.Rows(i).Cells(2).Value
        Next
        txtExpenses.Text = SM4
    End Sub

    Sub sumgridtotal()
        Dim SM1, SM2, SM3, DiscVal, DiscItemsVal As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM1 = SM1 + Val(DataGridView1.Rows(i).Cells(7).Value.ToString())
            SM3 = Val(DataGridView1.Rows(i).Cells(6).Value.ToString()) * Val(DataGridView1.Rows(i).Cells(7).Value.ToString())
            SM3 = Math.Round(SM3, 2)
            DataGridView1.Rows(i).Cells(8).Value = SM3
            SM2 = Val(SM2) + Val(DataGridView1.Rows(i).Cells(8).Value.ToString())
            DiscItemsVal = DiscItemsVal + Val(DataGridView1.Rows(i).Cells(13).Value.ToString())


            If Val(DataGridView1.Rows(i).Cells(13).Value.ToString) <> 0 Then
                If DataGridView1.Rows(i).Cells(14).Value.ToString = "نسبة" Then
                    DiscVal = Val((Val(DataGridView1.Rows(i).Cells(8).Value) * (100 - Val(DataGridView1.Rows(i).Cells(17).Value))) / 100)
                    DiscVal = Math.Round(DiscVal, 2)
                Else
                    DiscVal = Val(Val(DataGridView1.Rows(i).Cells(8).Value) - Val(DataGridView1.Rows(i).Cells(13).Value))
                End If
                DataGridView1.Rows(i).Cells(8).Value = DiscVal
            End If
        Next
        txtNumberSales.Text = SM1
        txttotalprisesal.Text = Math.Round(SM2, 2)
        txtDiscountsValue.Text = Math.Round(DiscItemsVal, 2)
    End Sub

    Sub sumgridtotalBSal()
        Dim SM4 As Double
        For i As Integer = 0 To DataGridView2.Rows.Count - 1
            SM4 = SM4 + DataGridView2.Rows(i).Cells(7).Value
        Next
        txtSumSalBsal.Text = Math.Round(SM4)
    End Sub

    Private Sub GetProfits()

        Dim SMTinPrice, SMStore, ValStoreTin, ValSal As Double

        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SMTinPrice = Val(DataGridView1.Rows(i).Cells(6).Value.ToString())
            SMStore = Val(DataGridView1.Rows(i).Cells(7).Value.ToString())
            ValStoreTin = SMStore * SMTinPrice
        Next


        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            ValSal += ValSal + Val(DataGridView1.Rows(i).Cells(6).Value.ToString())
        Next
    End Sub

    Private Sub GetBsal()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If RdoSalPrice.Checked = True Then
            S = "SELECT bill_no AS [رقم الفاتورة],bill_date as [التاريخ],itm_id as [الباركود],itm_cat as [المجموعة],itm_name as [الصنف],price as [سعر البيع],qu_unity as [الكمية],totalprice as [اجمالى سعر البيع ],Stores as [المخزن] FROM View_IM_Bsal where bill_date <> N'جرد'"
        End If
        If RdoTinPrice.Checked = True Then
            S = "SELECT bill_no AS [رقم الفاتورة],bill_date as [التاريخ],itm_id as [الباركود],itm_cat as [المجموعة],itm_name as [الصنف],TinPrice as [سعر الشراء],qu_unity as [الكمية],TotalPriceTin as [اجمالى سعر الشراء ],Stores as [المخزن] FROM View_IM_Bsal where bill_date <> N'جرد'"
        End If
        If chkChasherAll.Checked = False Then
            If cmbCasher.Text <> "" Then
                S = S & " and username =N'" & cmbCasher.Text.Trim & "'"
            End If
            If CmbSeller.Text <> "" Then
                S = S & " and EmpName =N'" & CmbSeller.Text.Trim & "'"
            End If
            If txtbillno.Text <> "" Then
                S = S & " and bill_no =N'" & txtbillno.Text.Trim & "'"
            End If
            If TxtPrc.Text <> "" Then
                S = S & " and itm_id =N'" & TxtPrc.Text.Trim & "'"
            End If
            If cmbcats.Text <> "" Then
                S = S & " and itm_cat =N'" & cmbcats.Text.Trim & "'"
            End If
            If cmbitmnm.Text <> "" Then
                S = S & " and itm_name =N'" & cmbitmnm.Text.Trim & "'"
            End If
            If cmbStores.Text <> "" Then
                S = S & " and Stores =N'" & cmbStores.Text.Trim & "'"
            End If
        End If
        If chkState.Checked = False Then
            If ChkCash.Checked = True Then
                S = S & " and Stat =N'" & ChkCash.Text.Trim & "'"
            End If
            If rdoStatus.Checked = True Then
                S = S & " and Stat =N'" & rdoStatus.Text.Trim & "'"
            End If
            If rdoVisa.Checked = True Then
                S = S & " and Stat =N'" & rdoVisa.Text.Trim & "'"
            End If
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        If chkTime.Checked = False Then
            Dim TimeAmPm24Hour1 As String = Cls.SlpitTime24Hour(dtpTime1.Text)
            Dim TimeAmPm24Hour2 As String = Cls.SlpitTime24Hour(dtpTime2.Text)
            S = S & " and billtime >=N'" & TimeAmPm24Hour1 & "' and billtime <=N'" & TimeAmPm24Hour2 & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " and Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم الفاتورة]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [التاريخ]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [الصنف]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView2.DataSource = Cls.PopulateDataView(dr)
        sumgridtotalBSal()

        Dim SM As String
        For i As Integer = 0 To DataGridView2.RowCount - 1
            SM = Val(DataGridView2.Rows(i).Cells(1).Value)
            SM = Cls.R_date(SM)
            DataGridView2.Rows(i).Cells(1).Value = SM
        Next

    End Sub

    Private Sub GetDataDiscSal()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select dbo.Vst_disc.id, dbo.Vst_disc.TIN_NO, dbo.Vst_disc.Vendorname, dbo.Vst_disc.det, dbo.Vst_disc.amnt, dbo.Vst_disc.pdate, dbo.Vst_disc.UserName, dbo.Sales_Bill.Stat,   dbo.Vst_disc.Company_Branch_ID, dbo.Sales_Bill.EmpName  From dbo.Vst_disc INNER Join  dbo.Sales_Bill ON dbo.Vst_disc.TIN_NO = dbo.Sales_Bill.bill_No Where (dbo.Vst_disc.id <> N'')"

        If chkChasherAll.Checked = False Then
            If cmbCasher.Text <> "" Then
                S = S & " And (dbo.Vst_disc.UserName = N'" & cmbCasher.Text.Trim & "')"
            End If
            If CmbSeller.Text <> "" Then
                S = S & " and (dbo.Sales_Bill.EmpName =N'" & CmbSeller.Text.Trim & "')"
            End If
            If txtbillno.Text <> "" Then
                S = S & " AND  (dbo.Vst_disc.TIN_NO =N'" & txtbillno.Text.Trim & "')"
            End If
        End If
        If chkState.Checked = False Then
            If ChkCash.Checked = True Then
                S = S & " AND (dbo.Sales_Bill.Stat = N'نقداً')"
            End If
            If rdoStatus.Checked = True Then
                S = S & " AND  (dbo.Sales_Bill.Stat = N'آجل')"
            End If
            If rdoVisa.Checked = True Then
                S = S & " AND  (dbo.Sales_Bill.Stat = N'فيزا')"
            End If
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and dbo.Vst_disc.pdate >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and dbo.Vst_disc.pdate <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " and dbo.Vst_disc.Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView3.DataSource = Cls.PopulateDataView(dr)
        DataGridView3.Columns(1).Visible = False

        Dim SM As String
        For i As Integer = 0 To DataGridView3.RowCount - 1
            SM = Val(DataGridView3.Rows(i).Cells(5).Value)
            SM = Cls.R_date(SM)
            DataGridView3.Rows(i).Cells(5).Value = SM
        Next

        Dim SM4 As Double
        For i As Integer = 0 To DataGridView3.Rows.Count - 1
            SM4 = SM4 + DataGridView3.Rows(i).Cells(4).Value
        Next
        txtDsic.Text = SM4

    End Sub

    Private Sub GetDataIM_Vst_disc()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()

        S = "Select dbo.IM_Vst_disc.id, dbo.IM_Vst_disc.TIN_NO, dbo.IM_Vst_disc.Vendorname, dbo.IM_Vst_disc.det, dbo.IM_Vst_disc.amnt, dbo.IM_Vst_disc.pdate, dbo.IM_Vst_disc.UserName, dbo.Sales_Bill.Stat,     dbo.IM_Vst_disc.Company_Branch_ID, dbo.Sales_Bill.EmpName From dbo.IM_Vst_disc INNER Join     dbo.Sales_Bill ON dbo.IM_Vst_disc.TIN_NO = dbo.Sales_Bill.bill_No Where (dbo.IM_Vst_disc.id <> N'')"
        If chkChasherAll.Checked = False Then
            If cmbCasher.Text <> "" Then
                S = S & " And (dbo.IM_Vst_disc.UserName = N'" & cmbCasher.Text.Trim & "')"
            End If
            If CmbSeller.Text <> "" Then
                S = S & " and (dbo.Sales_Bill.EmpName =N'" & CmbSeller.Text.Trim & "')"
            End If
            If txtbillno.Text <> "" Then
                S = S & " AND  (dbo.IM_Vst_disc.TIN_NO =N'" & txtbillno.Text.Trim & "')"
            End If
        End If
        If chkState.Checked = False Then
            If ChkCash.Checked = True Then
                S = S & " AND (dbo.Sales_Bill.Stat = N'نقداً')"
            End If
            If rdoStatus.Checked = True Then
                S = S & " AND  (dbo.Sales_Bill.Stat = N'آجل')"
            End If
            If rdoVisa.Checked = True Then
                S = S & " AND  (dbo.Sales_Bill.Stat = N'فيزا')"
            End If
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and dbo.IM_Vst_disc.pdate >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and dbo.IM_Vst_disc.pdate <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " and dbo.IM_Vst_disc.Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView6.DataSource = Cls.PopulateDataView(dr)
        DataGridView6.Columns(1).Visible = False

        Dim SM As String
        For i As Integer = 0 To DataGridView6.RowCount - 1
            SM = Val(DataGridView6.Rows(i).Cells(5).Value)
            SM = Cls.R_date(SM)
            DataGridView6.Rows(i).Cells(5).Value = SM
        Next

        Dim SM4 As Double
        For i As Integer = 0 To DataGridView6.Rows.Count - 1
            SM4 = SM4 + DataGridView6.Rows(i).Cells(4).Value
        Next
        txtDsicBSal.Text = SM4

    End Sub

    Private Sub Frm_daySales_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cls.fill_combo_Branch("Users", "UserName", cmbCasher)
        Cls.fill_combo_Branch("Employees", "NameEmployee", CmbSeller)
        Bra.Fil("groups", "g_name", cmbcats)
        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", cmbCustomers)
            'Cls.fill_combo_Top100("Customers", "id", "Vendorname", "ORDER BY id", cmbCustomers)
        End If
        Cls.fill_combo_Branch("stores", "store", cmbStores)
        Cls.fill_combo("vendors", "Vendorname", cmbvendores)
        cmbStores.Text = mykey.GetValue("StoresName", "المخزن الرئيسى")
        'cmbCustomers.Items.Add("نقداً")

        BtnShow_Click(sender, e)
        'FillData()
        'GetBsal()
        'GetDataDiscSal()
        'GetExpenses()
        'txtTotal.Text = Val(txttotalprisesal.Text) - Val(txtSumSalBsal.Text) - Val(txtDsic.Text) - Val(txtExpenses.Text)
        'GetProfits()

        If PermtionName = "مدير" Then
            RdoTinPrice.Enabled = True
        Else
            RdoTinPrice.Enabled = False
        End If
        If DealingWithSerialItems = "YES" Then
            PanelSerialNumber.Visible = True
            txtSerialNumber.Visible = True
        End If
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        Dim XState As String
        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            If RdoSalPrice.Checked = True Then
                XState = DataGridView1.Rows(i).Cells(10).Value()
            End If
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases (BILL_NO,bill_date,itm_id,itm_cat,itm_name,price,qu,totalprice,det,TotalafterDisc,TotalBay,vndiscount,VnPay,TotalBeforeDisc,UserName,Stat,Name1,vnamntdebit,vnamntcredit,TotalDebtor,TotalCreditor,disc,CustomerName)  values("
            S = S & "N'" & DataGridView1.Rows(i).Cells(0).Value & "',N'" & DataGridView1.Rows(i).Cells(1).Value & "',N'" & DataGridView1.Rows(i).Cells(3).Value & "',"
            S = S & "N'" & DataGridView1.Rows(i).Cells(4).Value & "',N'" & DataGridView1.Rows(i).Cells(5).Value & "',N'" & DataGridView1.Rows(i).Cells(6).Value & "',"
            S = S & "N'" & DataGridView1.Rows(i).Cells(7).Value & "',N'" & DataGridView1.Rows(i).Cells(8).Value & "',"
            S = S & "N'" & txtNumberSales.Text & "',N'" & txttotalprisesal.Text & "',N'" & txtSumSalBsal.Text & "',N'" & txtDsic.Text & "',N'" & txtExpenses.Text & "',N'" & txtTotal.Text & "',N'" & cmbCasher.Text & "',N'" & XState & "',N'" & Cls.R_date(DataGridView1.Rows(i).Cells(1).Value) & "',N'" & txtTotalCash.Text & "',N'" & txtTotalVisa.Text & "',N'" & txtTotalStatus.Text & "',N'" & txtDiscountsValue.Text & "',N'" & DataGridView1.Rows(i).Cells(13).Value & "',N'" & DataGridView1.Rows(i).Cells(15).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

        Next
        AddReportView()
        Dim rpt
        If PrintSmall = "NO" Then
            If SettingPrinterAuto = "YES" Then
                Dim DefaultPrinterBill As String = mykey.GetValue("DefaultPrinterA4", "DefaultPrinterA4")
                If Not Cls.SetDefaultSystemPrinter4(DefaultPrinterBill) Then
                    MessageBox.Show("Error occured will trying to set the default printer!")
                End If
            End If
            rpt = New Rpt_DaySales
        End If
        If PrintSmall = "YES" Then
            If SettingPrinterAuto = "YES" Then
                Dim DefaultPrinterBill As String = mykey.GetValue("DefaultPrinterBill", "DefaultPrinterBill")
                If Not Cls.SetDefaultSystemPrinter4(DefaultPrinterBill) Then
                    MessageBox.Show("Error occured will trying to set the default printer!")
                End If
            End If

            rpt = New Rpt_DaySales_Small
        End If

        Cls.Select_More_Data_Branch_Print_Orderby("PrintSalesPurchases", "*", "Name1")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        Dim txtNameAr, txtNameEn, txtTitel As TextObject
        txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
        txtNameEn.Text = NameEnCompany
        txtTitel = rpt.Section1.ReportObjects("txtTitel")
        If RdoSalPrice.Checked = True Then
            txtTitel.Text = "البضاعة المباعة بسعر البيع"
        Else
            txtTitel.Text = "البضاعة المباعة بسعر التكلفة"
        End If
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()
        Frm_PrintReports.Text = "تقرير بالمبيعات اليومية"

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub FillSalesBill()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If RdoSalPrice.Checked = True Then
            S = "SELECT bill_no AS [رقم الفاتورة],Vendorname AS [اسم العميل], bill_date AS [تاريخ الفاتورة], billtime AS [وقت الفاتورة], totalpricebeforedisc AS [القيمة قبل الخصم], disc AS [الخصم], totalpriceafterdisc AS [القيمة بعد الخصم],BEY as [المدفوع],STAYING as [الباقى],Stat as [الحالة] FROM Sales_Bill where BILL_NO <> N'جرد'"
        End If
        If RdoTinPrice.Checked = True Then
            S = "SELECT bill_no AS [رقم الفاتورة],bill_date as [التاريخ],itm_id as [الباركود],itm_cat as [المجموعة],itm_name as [الصنف],TinPrice as [سعر الشراء],qu_unity as [الكمية],TotalPriceTin as [اجمالى سعر الشراء ],Stores as [المخزن] FROM View_IM_Bsal where bill_date <> N'جرد'"
        End If
        If chkChasherAll.Checked = False Then
            If cmbCasher.Text <> "" Then
                S = S & " and username =N'" & cmbCasher.Text.Trim & "'"
            End If
            If txtbillno.Text <> "" Then
                S = S & " and bill_no =N'" & txtbillno.Text.Trim & "'"
            End If
            If TxtPrc.Text <> "" Then
                S = S & " and itm_id =N'" & TxtPrc.Text.Trim & "'"
            End If
            If cmbcats.Text <> "" Then
                S = S & " and itm_cat =N'" & cmbcats.Text.Trim & "'"
            End If
            If cmbitmnm.Text <> "" Then
                S = S & " and itm_name =N'" & cmbitmnm.Text.Trim & "'"
            End If
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " and Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If
        S = S & " order by [رقم الفاتورة]"
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView2.DataSource = Cls.PopulateDataView(dr)
        sumgridtotalBSal()

        Dim SM As String
        For i As Integer = 0 To DataGridView2.RowCount - 1
            SM = Val(DataGridView2.Rows(i).Cells(1).Value)
            SM = Cls.R_date(SM)
            DataGridView2.Rows(i).Cells(1).Value = SM
        Next
    End Sub

    Sub ForPrintAll(ByVal BILL_NO As String, ByVal bill_date As String, ByVal itm_id As String, ByVal itm_cat As String, ByVal itm_name As String, ByVal stors As String,
                    ByVal price As String, ByVal qu As String, ByVal totalprice As String, ByVal totalpriceafterdisc As String, ByVal totalpricebeforedisc As String)

        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "Sp_PrintDaySales"
        cmd.Parameters.Clear()

        cmd.Parameters.AddWithValue("@BILL_NO", BILL_NO)
        cmd.Parameters.AddWithValue("@bill_date", bill_date)
        cmd.Parameters.AddWithValue("@itm_id", itm_id)
        cmd.Parameters.AddWithValue("@itm_cat", itm_cat)
        cmd.Parameters.AddWithValue("@itm_name", itm_name)
        cmd.Parameters.AddWithValue("@store", stors)
        cmd.Parameters.AddWithValue("@price", price)
        cmd.Parameters.AddWithValue("@qu", qu)
        cmd.Parameters.AddWithValue("@totalprice", totalprice)
        cmd.Parameters.AddWithValue("@totalpriceafterdisc", totalpriceafterdisc)
        cmd.Parameters.AddWithValue("@totalpricebeforedisc", totalpricebeforedisc)

        cmd.ExecuteNonQuery()

    End Sub

    Private Sub chkChasherAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkChasherAll.CheckedChanged
        If chkChasherAll.Checked = True Then
            cmbCasher.SelectedIndex = -1
            cmbCasher.Enabled = False
            txtbillno.Enabled = False
            txtSheft_Status.Enabled = False
            cmbCustomers.Enabled = False
            CmbSeller.Enabled = False
            TxtPrc.Enabled = False
            cmbcats.Enabled = False
            cmbitmnm.Enabled = False
            cmbStores.Enabled = False
            cmbvendores.Enabled = False
        Else
            cmbCasher.Enabled = True
            txtbillno.Enabled = True
            txtSheft_Status.Enabled = True
            cmbCustomers.Enabled = True
            CmbSeller.Enabled = True
            TxtPrc.Enabled = True
            cmbcats.Enabled = True
            cmbitmnm.Enabled = True
            cmbStores.Enabled = True
            cmbvendores.Enabled = True
        End If
    End Sub

    Private Declare Function WriteProfileString Lib "kernel32" Alias "WriteProfileStringA" _
  (ByVal lpszSection As String, ByVal lpszKeyName As String,
  ByVal lpszString As String) As Long
    Private Declare Function SendMessage Lib "user32" Alias "SendMessageA" _
         (ByVal hwnd As Long, ByVal wMsg As Long,
         ByVal wParam As Long, ByVal lparam As String) As Long
    Private Const HWND_BROADCAST As Long = &HFFFF&
    Private Const WM_WININICHANGE As Long = &H1A

    Private Function SetDefaultSystemPrinter3(ByVal strPrinterName As String) As Boolean
        'this method does not valid if the change is correct and does not revert to previous printer if wrong
        Dim DeviceLine As String

        'rebuild a valid device line string 
        DeviceLine = strPrinterName & ",,"

        'Store the new printer information in the 
        '[WINDOWS] section of the WIN.INI file for 
        'the DEVICE= item 
        Call WriteProfileString("windows", "Device", DeviceLine)

        'Cause all applications to reload the INI file 
        Call SendMessage(HWND_BROADCAST, WM_WININICHANGE, 0, "windows")

        Return True
    End Function

    Public Sub CreateView()
        'Dim strSQL As String = ""
        'strSQL = "USE [DatabaseElectric]"

        'connectionString()

        'Dim cmdUseDB As New SqlCommand(strSQL, Cn)
        'cmdUseDB.ExecuteNonQuery()
        'Dim cmd As New SqlCommand(strSQL, Cn)
        'cmd.CommandText = "CREATE VIEW View_BillsalDataSal AS " & _
        '"select * from tableA where ID in (12345)"

        'cmd.ExecuteNonQuery()


    End Sub

    Private Sub cmbcats_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbcats.SelectedIndexChanged
        If cmbcats.Text.Trim = "" Then Exit Sub
        cmbitmnm.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If PermtionName = "مدير" Then
            cmd.CommandText = "select distinct sname from Items where group_name =N'" & cmbcats.Text & "' order by 1"
        Else
            cmd.CommandText = "select distinct sname from Items where group_name =N'" & cmbcats.Text & "' and Company_Branch_ID =N'" & Company_Branch_ID & "' order by 1"
        End If
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbitmnm.Items.Add(Trim(dr(0)))
        Loop
        cmbitmnm.Text = ""
    End Sub

    Private Sub chkState_CheckedChanged(sender As Object, e As EventArgs) Handles chkState.CheckedChanged
        If chkState.Checked = True Then
            ChkCash.Enabled = False
            rdoStatus.Enabled = False
            rdoVisa.Enabled = False
            rdoCashWallet.Enabled = False
        Else
            ChkCash.Enabled = True
            rdoStatus.Enabled = True
            rdoVisa.Enabled = True
            rdoCashWallet.Enabled = True
        End If
    End Sub

    Private Sub chkTime_CheckedChanged(sender As Object, e As EventArgs) Handles chkTime.CheckedChanged
        If chkTime.Checked = True Then
            dtpTime1.Enabled = False
            dtpTime2.Enabled = False
        Else
            dtpTime1.Enabled = True
            dtpTime2.Enabled = True
        End If
    End Sub

    Private Sub GetTotalCashAndVisaAndStatus()
        Dim SMTotalCash, SMTotalVisa, SMTotalStatus, SMTotalCashWallet As Double
        Dim Stat As String
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            Stat = DataGridView1.Rows(i).Cells(10).Value.ToString()
            If Stat = "نقداً" Then
                SMTotalCash += Val(DataGridView1.Rows(i).Cells(6).Value.ToString()) * Val(DataGridView1.Rows(i).Cells(7).Value.ToString())
            End If
            If Stat = "بنك" Then
                SMTotalVisa += Val(DataGridView1.Rows(i).Cells(6).Value.ToString()) * Val(DataGridView1.Rows(i).Cells(7).Value.ToString())
            End If
            If Stat = "آجل" Then
                SMTotalStatus += Val(DataGridView1.Rows(i).Cells(6).Value.ToString()) * Val(DataGridView1.Rows(i).Cells(7).Value.ToString())
            End If
            If Stat = "محفظة" Then
                SMTotalCashWallet += Val(DataGridView1.Rows(i).Cells(6).Value.ToString()) * Val(DataGridView1.Rows(i).Cells(7).Value.ToString())
            End If
        Next

        txtTotalCash.Text = Math.Round(SMTotalCash, 2)
        txtTotalVisa.Text = Math.Round(SMTotalVisa, 2)
        txtTotalStatus.Text = Math.Round(SMTotalStatus, 2)
        txtTotalCashWallet.Text = Math.Round(SMTotalCashWallet, 2)
    End Sub

    Private Sub GetSerialNumber()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select bill_no from BillsalData_SerialNumber where Serial_Number_Name=N'" & txtSerialNumber.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            txtbillno.Text = dr(0).ToString
        End If
    End Sub

    Private Sub DataGridView1_DoubleClick(sender As Object, e As EventArgs) Handles DataGridView1.DoubleClick
        If DealingWithSerialItems = "YES" Then
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.Rows.Count) = 0 Then Beep() : Exit Sub
            SN_billno = DataGridView1.SelectedRows(0).Cells(0).Value
            SN_Barcode = DataGridView1.SelectedRows(0).Cells(3).Value
            SN_View = "View"
            FrmSales_SerialNumber.Close()
            FrmSales_SerialNumber.Show()
        End If
    End Sub

    Private Sub btnPrintAll_Click(sender As Object, e As EventArgs) Handles btnPrintAll.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        Dim XState As String
        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            If RdoSalPrice.Checked = True Then
                XState = DataGridView1.Rows(i).Cells(10).Value()
            End If

            Dim Custaddress As String = Cls.Get_Code_Value_Branch_More("Customers", "addr", "Vendorname =N'" & DataGridView1.Rows(i).Cells(11).Value & "'")
            Dim CustTel As String = Cls.Get_Code_Value_Branch_More("Customers", "tel1", "Vendorname =N'" & DataGridView1.Rows(i).Cells(11).Value & "'")

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases (BILL_NO,bill_date,itm_id,itm_cat,itm_name,price,qu,totalprice,det,TotalafterDisc,TotalBay,vndiscount,VnPay,TotalBeforeDisc,UserName,Stat,Name1,vnamntdebit,vnamntcredit,TotalDebtor,TotalCreditor,disc,CustomerName,Vendorname,Supervisor_Reform,Recipient,valuereturns,priceSal,Totalreturns)  values("
            S = S & "N'" & DataGridView1.Rows(i).Cells(0).Value & "',N'" & DataGridView1.Rows(i).Cells(1).Value & "',N'" & DataGridView1.Rows(i).Cells(3).Value & "',"
            S = S & "N'" & DataGridView1.Rows(i).Cells(4).Value & "',N'" & DataGridView1.Rows(i).Cells(5).Value & "',N'" & DataGridView1.Rows(i).Cells(6).Value & "',"
            S = S & "N'" & DataGridView1.Rows(i).Cells(7).Value & "',N'" & DataGridView1.Rows(i).Cells(8).Value & "',"
            S = S & "N'" & txtNumberSales.Text & "',N'" & txttotalprisesal.Text & "',N'" & txtSumSalBsal.Text & "',N'" & txtDsic.Text & "',N'" & txtExpenses.Text & "',N'" & txtTotal.Text & "',N'" & cmbCasher.Text & "',N'" & XState & "',N'" & Cls.R_date(DataGridView1.Rows(i).Cells(1).Value) & "',N'" & txtTotalCash.Text & "',N'" & txtTotalVisa.Text & "',N'" & txtTotalStatus.Text & "',N'" & txtDiscountsValue.Text & "',N'" & DataGridView1.Rows(i).Cells(13).Value & "',N'" & DataGridView1.Rows(i).Cells(15).Value & "',N'" & DataGridView1.Rows(i).Cells(11).Value & "',N'" & Custaddress & "',N'" & CustTel & "',N'" & DataGridView1.Rows(i).Cells(17).Value & "',N'" & DataGridView1.Rows(i).Cells(18).Value & "',N'" & DataGridView1.Rows(i).Cells(19).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

        Next
        AddReportView()
        Dim rpt
        If PrintSmall = "NO" Then
            If SettingPrinterAuto = "YES" Then
                Dim DefaultPrinterBill As String = mykey.GetValue("DefaultPrinterA4", "DefaultPrinterA4")
                If Not Cls.SetDefaultSystemPrinter4(DefaultPrinterBill) Then
                    MessageBox.Show("Error occured will trying to set the default printer!")
                End If
            End If
            rpt = New Rpt_DaySalesAll
        End If
        If PrintSmall = "YES" Then
            If SettingPrinterAuto = "YES" Then
                Dim DefaultPrinterBill As String = mykey.GetValue("DefaultPrinterBill", "DefaultPrinterBill")
                If Not Cls.SetDefaultSystemPrinter4(DefaultPrinterBill) Then
                    MessageBox.Show("Error occured will trying to set the default printer!")
                End If
            End If

            rpt = New Rpt_DaySales_Small
        End If
        If PrintSmall = "A5" Then
            If SettingPrinterAuto = "YES" Then
                Dim DefaultPrinterBill As String = mykey.GetValue("DefaultPrinterA4", "DefaultPrinterA4")
                If Not Cls.SetDefaultSystemPrinter4(DefaultPrinterBill) Then
                    MessageBox.Show("Error occured will trying to set the default printer!")
                End If
            End If
            rpt = New Rpt_DaySalesAll
        End If

        Cls.Select_More_Data_Branch_Print_Orderby("PrintSalesPurchases", "*", "Name1")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        Dim txtNameAr, txtNameEn, txtTitel As TextObject
        txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
        txtNameEn.Text = NameEnCompany
        txtTitel = rpt.Section1.ReportObjects("txtTitel")
        If RdoSalPrice.Checked = True Then
            txtTitel.Text = "البضاعة المباعة بسعر البيع"
        Else
            txtTitel.Text = "البضاعة المباعة بسعر التكلفة"
        End If
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()
        Frm_PrintReports.Text = "تقرير بالمبيعات اليومية"

        If RunDatabaseInternet = "YES" Then : connect() : End If

    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub

    Private Sub cmbCustomers_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbCustomers.KeyUp

        Cls.SearchTextBY(cmbCustomers.Text, "Customers", "id", "Vendorname", cmbCustomers)

    End Sub
End Class