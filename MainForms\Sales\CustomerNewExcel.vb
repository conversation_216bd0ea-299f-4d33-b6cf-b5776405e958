﻿Imports System.Data.OleDb

Public Class CustomerNewExcel
    Dim Cust_Code As String

    Public Property Cust_Code1 As String
        Get
            Return Cust_Code
        End Get
        Set(value As String)
            Cust_Code = value
        End Set
    End Property

    Private Sub btnImport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnImport.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        ImportExcel()
    End Sub

    Private Sub ImportExcel()
        DTGV.Columns.Clear()
        Dim MyFileDialog As New OpenFileDialog()
        'Dim Xsheet As String = txtSheetName.Text
        With MyFileDialog
            .Filter = "Excel files|*xlsx;*xls"
            .Title = "Open File"
            .ShowDialog()
        End With

        If MyFileDialog.FileName.ToString <> "" Then
            Dim ExcelFile As String = MyFileDialog.FileName.ToString
            Dim ds As New DataSet
            Dim da As OleDbDataAdapter
            Dim dt As DataTable
            Dim conn As OleDbConnection
            conn = New OleDbConnection(
                       "Provider=Microsoft.Jet.OLEDB.4.0;" &
                       "Data Source= " & ExcelFile & ";" &
                       "Extended Properties=Excel 8.0;")
            Try

                da = New OleDbDataAdapter("select * from [Customers$]", conn)
                conn.Open()
                da.Fill(ds, "Customers")
                dt = ds.Tables("Customers")

            Catch ex As Exception
                MsgBox(ex.Message)
                conn.Close()
            End Try
            Try
                DTGV.DataSource = ds
                DTGV.DataMember = "Customers"
            Catch ex As Exception
                MsgBox(ex.Message)
            End Try

        End If
    End Sub


    Private Sub btnSaveDataBase_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSaveDataBase.Click
        Dim StatusForm As String = Me.Tag
        If StatusForm = "Customer" Then
            GODataBaseCust()
        End If
        If StatusForm = "Vendor" Then
            GODataBaseVendors()
        End If
    End Sub

    Private Sub GODataBaseCust()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        connectionStringOpen()

        Dim Cust_Code As String = ""
        Dim Vendorname As String = ""
        Dim addr As String = ""
        Dim tel1 As String = ""
        Dim vnamntcredit As String = ""
        Dim vnamntdebit As String = ""
        Dim notes As String = ""
        Dim Mobile As String = ""
        Dim Regions As String = ""
        Dim Apartment As String = ""
        Dim Role As String = ""
        Dim Mark As String = ""

        Dim XLoopCust As Double = 0
        Dim XLoopCustName As Double = 0
        Dim XLoopCust_Code As Double = 0

        For i As Integer = 0 To DTGV.Rows.Count - 1
            Cust_Code = DTGV.Rows(i).Cells(0).Value.ToString()
            Vendorname = DTGV.Rows(i).Cells(1).Value.ToString()

            If Trim(Vendorname) = "" Then
                Vendorname = Cust_Code
                'MsgBox("فضلاً أدخل اسم العميل", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
                'XLoopCust = 1
            End If
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * From customers where Vendorname =N'" & Vendorname & "'"
            dr = cmd.ExecuteReader()
            If dr.HasRows = True Then
                XLoopCustName = 1
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * From customers where Cust_Code =N'" & Cust_Code & "'"
            dr = cmd.ExecuteReader()
            If dr.HasRows = True Then
                'MsgBox("عفواً يوجد كود عميل مسجل مسبقا بنفس الرقم", MsgBoxStyle.Exclamation)
                XLoopCust_Code = 1
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
            End If

        Next
        If XLoopCust = 1 Then
            MsgBox(" فضلاً أدخل اسم العميل", MsgBoxStyle.Exclamation)
            Exit Sub
        End If
        If XLoopCustName = 1 Then
            MsgBox(" عفواً يوجد عميل مسجل مسبقا بنفس الاسم", MsgBoxStyle.Exclamation)
            Exit Sub
        End If
        If XLoopCust_Code = 1 Then
            MsgBox("عفواً يوجد كود عميل مسجل مسبقا بنفس الرقم", MsgBoxStyle.Exclamation)
            Exit Sub
        End If


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            If DTGV.Rows(i).Cells(3).Value.ToString <> "" Then
                Cust_Code = DTGV.Rows(i).Cells(0).Value.ToString()
                Vendorname = DTGV.Rows(i).Cells(1).Value.ToString()
                addr = DTGV.Rows(i).Cells(2).Value.ToString()
                tel1 = DTGV.Rows(i).Cells(3).Value.ToString()
                vnamntcredit = DTGV.Rows(i).Cells(4).Value.ToString()
                vnamntdebit = DTGV.Rows(i).Cells(5).Value.ToString()
                notes = DTGV.Rows(i).Cells(6).Value.ToString()
                Mobile = DTGV.Rows(i).Cells(7).Value.ToString()
                Regions = DTGV.Rows(i).Cells(8).Value.ToString()
                Apartment = DTGV.Rows(i).Cells(9).Value.ToString()
                Role = DTGV.Rows(i).Cells(10).Value.ToString()
                Mark = DTGV.Rows(i).Cells(11).Value.ToString()


                MAXRECORDCust_Code()
                If Val(Cust_Code) = 0 Then
                    MAXRECORDCust_Code()
                Else
                    Cust_Code = Cust_Code
                End If

                Cls.insert("customers", "Company_Branch_ID,Cust_Code,Vendorname,addr,tel1,notes,vintinval,vndiscount,VnPay,vnamntdebit,vnamntcredit,UserName,GeoArea_Code,Mobile,Apartment,Role,Region,Mark,PriceType_ID", "N'" & Company_Branch_ID & "',N'" & Cust_Code & "',N'" & Vendorname.Trim & "',N'" & addr & "',N'" & tel1 & "',N'" & notes & "',0,0,0,0,0,N'" & UserName & "',0,N'" & Mobile & "',N'" & Apartment & "',N'" & Role & "',N'" & Regions & "',N'" & Mark & "',N'0'")

                Try
                    Dim inventory As String = "جرد"
                    If vnamntdebit <> "0" Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        S = "insert into vst (Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,VND_no,billno,Treasury_Code,CashBank) values"
                        S = S & " (N'" & Company_Branch_ID & "',N'" & Vendorname.Trim & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(dtpDate.Text) & "'," & Val(vnamntdebit) & ",N'" & inventory & "',N'" & inventory & "',N'" & inventory & "',N'" & inventory & "',N'" & inventory & "',N'" & Treasury_Code & "',0)"
                        cmd.CommandText = S : cmd.ExecuteNonQuery()
                    ElseIf vnamntcredit <> "0" Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        S = "insert into Sales_bill(Company_Branch_ID,bill_No,Vendorname,bill_date,billtime,disc,totalpriceafterdisc,totalpricebeforedisc,stat,Treasury_Code) values ("
                        S = S & "N'" & Company_Branch_ID & "',N'" & inventory & "',N'" & Vendorname.Trim & "' ,N'" & Cls.C_date(dtpDate.Text) & "' ,N'" & Cls.get_time(True) & "' ,0 ,0," & Val(vnamntcredit) & " ,N'" & inventory & "',N'" & Treasury_Code & "')"
                        cmd.CommandText = S : cmd.ExecuteNonQuery()
                    End If
                Catch ex As Exception
                    ErrorHandling(ex, Me.Text)
                End Try

                IM.CustomerAccountTotal(Vendorname)

            End If
        Next

        Get_Movement_In_Out_Money(dtpDate.Text, Treasury_Code)

        MsgBox("تم حفظ بيانات العملاء من الاكسل الى قاعدة البيانات", MsgBoxStyle.Information)
        CloseDB()
    End Sub

    Private Sub GODataBaseEdit()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        connectionStringOpen()

        Dim Cust_Code As String = ""
        Dim Vendorname As String = ""
        Dim addr As String = ""
        Dim tel1 As String = ""
        Dim vnamntcredit As String = ""
        Dim vnamntdebit As String = ""
        Dim notes As String = ""
        Dim Mobile As String = ""
        Dim Regions As String = ""
        Dim Apartment As String = ""
        Dim Role As String = ""
        Dim Mark As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1

            Cust_Code = DTGV.Rows(i).Cells(0).Value.ToString()
                Vendorname = DTGV.Rows(i).Cells(1).Value.ToString()
                addr = DTGV.Rows(i).Cells(2).Value.ToString()
                tel1 = DTGV.Rows(i).Cells(3).Value.ToString()
                vnamntcredit = DTGV.Rows(i).Cells(4).Value.ToString()
                vnamntdebit = DTGV.Rows(i).Cells(5).Value.ToString()
                notes = DTGV.Rows(i).Cells(6).Value.ToString()
                Mobile = DTGV.Rows(i).Cells(7).Value.ToString()
                Regions = DTGV.Rows(i).Cells(8).Value.ToString()
                Apartment = DTGV.Rows(i).Cells(9).Value.ToString()
                Role = DTGV.Rows(i).Cells(10).Value.ToString()
                Mark = DTGV.Rows(i).Cells(11).Value.ToString()

                If Cust_Code <> "" Then
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "Select * From customers where Vendorname =N'" & Vendorname & "'"
                    dr = cmd.ExecuteReader()
                    If dr.HasRows = True Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update customers set Cust_Code =N'" & Cust_Code & "'  where Vendorname =N'" & Vendorname & "'" : cmd.ExecuteNonQuery()
                    End If
                End If

        Next

        Get_Movement_In_Out_Money(dtpDate.Text, Treasury_Code)

        MsgBox("تم حفظ بيانات العملاء من الاكسل الى قاعدة البيانات", MsgBoxStyle.Information)
        CloseDB()
    End Sub

    Private Sub GODataBaseVendors()
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        connectionStringOpen()

        Dim Cust_Code As String = ""
        Dim Vendorname As String = ""
        Dim addr As String = ""
        Dim tel1 As String = ""
        Dim vnamntcredit As String = ""
        Dim vnamntdebit As String = ""
        Dim notes As String = ""
        Dim Mobile As String = ""
        Dim Regions As String = ""
        Dim Apartment As String = ""
        Dim Role As String = ""
        Dim Mark As String = ""

        Dim XLoopCust As Double = 0
        Dim XLoopCustName As Double = 0
        Dim XLoopCust_Code As Double = 0

        For i As Integer = 0 To DTGV.Rows.Count - 1
            Cust_Code = DTGV.Rows(i).Cells(0).Value.ToString()
            Vendorname = DTGV.Rows(i).Cells(1).Value.ToString()

            If Trim(Vendorname) = "" Then
                MsgBox("فضلاً أدخل اسم العميل", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
                XLoopCust = 1
            End If
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * From vendors where Vendorname =N'" & Vendorname & "'"
            dr = cmd.ExecuteReader()
            If dr.HasRows = True Then
                XLoopCustName = 1
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * From vendors where Cust_Code =N'" & Cust_Code & "'"
            dr = cmd.ExecuteReader()
            If dr.HasRows = True Then
                'MsgBox("عفواً يوجد كود عميل مسجل مسبقا بنفس الرقم", MsgBoxStyle.Exclamation)
                XLoopCust_Code = 1
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Red
            End If

        Next
        If XLoopCust = 1 Then
            MsgBox(" فضلاً أدخل اسم العميل", MsgBoxStyle.Exclamation)
            Exit Sub
        End If
        If XLoopCustName = 1 Then
            MsgBox(" عفواً يوجد عميل مسجل مسبقا بنفس الاسم", MsgBoxStyle.Exclamation)
            Exit Sub
        End If
        If XLoopCust_Code = 1 Then
            MsgBox("عفواً يوجد كود عميل مسجل مسبقا بنفس الرقم", MsgBoxStyle.Exclamation)
            Exit Sub
        End If


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.Rows.Count - 1
            If DTGV.Rows(i).Cells(3).Value.ToString <> "" Then
                Cust_Code = DTGV.Rows(i).Cells(0).Value.ToString()
                Vendorname = DTGV.Rows(i).Cells(1).Value.ToString()
                addr = DTGV.Rows(i).Cells(2).Value.ToString()
                tel1 = DTGV.Rows(i).Cells(3).Value.ToString()
                vnamntcredit = DTGV.Rows(i).Cells(4).Value.ToString()
                vnamntdebit = DTGV.Rows(i).Cells(5).Value.ToString()
                notes = DTGV.Rows(i).Cells(6).Value.ToString()
                Mobile = DTGV.Rows(i).Cells(7).Value.ToString()
                Regions = DTGV.Rows(i).Cells(8).Value.ToString()
                Apartment = DTGV.Rows(i).Cells(9).Value.ToString()
                Role = DTGV.Rows(i).Cells(10).Value.ToString()
                Mark = DTGV.Rows(i).Cells(11).Value.ToString()


                MAXRECORDCust_Code()
                If Val(Cust_Code) = 0 Then
                    MAXRECORDCust_Code()
                Else
                    Cust_Code = Cust_Code
                End If

                Cls.insert("customers", "Company_Branch_ID,Cust_Code,Vendorname,addr,tel1,notes,vintinval,vndiscount,VnPay,vnamntdebit,vnamntcredit,UserName,GeoArea_Code,Mobile,Apartment,Role,Region,Mark,PriceType_ID", "N'" & Company_Branch_ID & "',N'" & Cust_Code & "',N'" & Vendorname.Trim & "',N'" & addr & "',N'" & tel1 & "',N'" & notes & "',0,0,0,0,0,N'" & UserName & "',0,N'" & Mobile & "',N'" & Apartment & "',N'" & Role & "',N'" & Regions & "',N'" & Mark & "',N'0'")

                Try
                    Dim inventory As String = "جرد"
                    If vnamntdebit <> "0" Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        S = "insert into vst (Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,VND_no,billno,Treasury_Code,CashBank) values"
                        S = S & " (N'" & Company_Branch_ID & "',N'" & Vendorname.Trim & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(dtpDate.Text) & "'," & Val(vnamntdebit) & ",N'" & inventory & "',N'" & inventory & "',N'" & inventory & "',N'" & inventory & "',N'" & inventory & "',N'" & Treasury_Code & "',0)"
                        cmd.CommandText = S : cmd.ExecuteNonQuery()
                    ElseIf vnamntcredit <> "0" Then
                        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                        S = "insert into Sales_bill(Company_Branch_ID,bill_No,Vendorname,bill_date,billtime,disc,totalpriceafterdisc,totalpricebeforedisc,stat,Treasury_Code) values ("
                        S = S & "N'" & Company_Branch_ID & "',N'" & inventory & "',N'" & Vendorname.Trim & "' ,N'" & Cls.C_date(dtpDate.Text) & "' ,N'" & Cls.get_time(True) & "' ,0 ,0," & Val(vnamntcredit) & " ,N'" & inventory & "',N'" & Treasury_Code & "')"
                        cmd.CommandText = S : cmd.ExecuteNonQuery()
                    End If
                Catch ex As Exception
                    ErrorHandling(ex, Me.Text)
                End Try

                IM.CustomerAccountTotal(Vendorname)

            End If
        Next

        Get_Movement_In_Out_Money(dtpDate.Text, Treasury_Code)

        MsgBox("تم حفظ بيانات العملاء من الاكسل الى قاعدة البيانات", MsgBoxStyle.Information)
        CloseDB()
    End Sub

    Private Sub CloseDB()
        If Cn.State = Data.ConnectionState.Closed Then
            Try
                Cn.Close()
            Catch ex As Exception
                MsgBox(ex.Message)
            End Try
        End If
    End Sub


    Private Sub MAXRECORDCust_Code()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select * from Customers"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Cust_Code = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(Cust_Code) as mb FROM Customers"
            dr = cmd.ExecuteReader
            dr.Read()
            Dim sh As Integer
            sh = dr("mb")
            Cust_Code = sh + 1
        End If

    End Sub

    Private Sub Headerx()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()

        S = "Select Cust_Code as [كود العميل],Vendorname as [الاسم],addr  as [العنوان],tel1  as [تليفون],vnamntcredit  as [مدين],vnamntdebit  as [دائن],notes  as [ملاحظات],Mobile as [موبايل],Region as [المنطقة],Apartment as [الشقة],Role as [الدور],Mark as [علامة] From customers order by 1"
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)
        DTGV.Columns(0).Width = 50
        DTGV.Columns(1).Width = 140
        DTGV.Columns(2).Width = 50
        DTGV.Columns(3).Width = 50
        DTGV.Columns(4).Width = 50
        DTGV.Columns(5).Width = 45
        DTGV.Columns(6).Width = 45
        DTGV.Columns(7).Width = 45
        DTGV.Columns(8).Width = 45
        DTGV.Columns(9).Width = 45
        DTGV.Columns(10).Width = 45
        DTGV.Columns(11).Width = 45
    End Sub

    Private Sub FrmItemsNewExcel_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        Dim XX As String = Me.Tag
    End Sub

    Private Sub btnEditDataBase_Click(sender As Object, e As EventArgs) Handles btnEditDataBase.Click
        GODataBaseEdit()
    End Sub
End Class