﻿Imports CrystalDecisions.CrystalReports.Engine

Public Class Frm_Product_Dismissal_Notice_Factory_Total
    Dim Dt_AddBill As New DataTable
    Dim RNXD As Integer
    Dim Manufacturing_ID As String
    Dim ActionRead As Boolean = False
    Dim WithEvents BS As New BindingSource
    Dim ListBoxSelectedIndex As Integer
    Dim QuntAll As Integer
    Dim AlaertParcode As Boolean
    Dim itm_id As String = ""
    Dim itm_cat As String = ""
    Dim itm_name As String = ""
    Dim Unity As String = ""
    Dim qunt As String = ""
    Dim qunt_unity As String = ""
    Dim quntTotal As String = ""
    Dim StoresFrom As String = ""
    Dim StoresTo As String = ""
    Dim QuntFrom As Double
    Dim TinPrice As String = ""
    Dim StausMainStores As Integer = 0

#Region "View Items"
    Private Sub Frm_Offers_Items_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Bra.Fil("groups", "g_name", cmbCatsManufacturingView)
        Cls.fill_combo_Branch("stores", "store", cmbStoreView)
    End Sub

    Private Sub cmbStoreManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbStoreView.SelectedIndexChanged
        If cmbStoreView.Text.Trim = "" Then Exit Sub
        cmbCatsManufacturingView.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select distinct group_name from Items where Stores =N'" & cmbStoreView.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbCatsManufacturingView.Items.Add(Trim(dr(0)))
        Loop
        cmbCatsManufacturingView.Text = ""
        cmbCatsManufacturingView.Focus()
    End Sub

    Private Sub cmbCatsManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbCatsManufacturingView.SelectedIndexChanged
        If ActionRead = False Then
            If cmbCatsManufacturingView.Text.Trim = "" Then Exit Sub
            cmbItemsManufacturingView.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sname from Items where group_name =N'" & cmbCatsManufacturingView.Text & "' and Stores =N'" & cmbStoreView.Text & "' order by 1"
            dr = cmd.ExecuteReader
            Do While dr.Read
                cmbItemsManufacturingView.Items.Add(Trim(dr(0)))
            Loop
            cmbItemsManufacturingView.Text = ""
            cmbItemsManufacturingView.Focus()
        End If
    End Sub

    Private Sub cmbStoreManufacturing_DropDown(sender As Object, e As EventArgs) Handles cmbStoreView.DropDown
        cmbCatsManufacturingView.Text = ""
        cmbItemsManufacturingView.Text = ""
    End Sub

    Private Sub cmbCatsManufacturing_DropDown(sender As Object, e As EventArgs) Handles cmbCatsManufacturingView.DropDown
        cmbItemsManufacturingView.Text = ""
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        GetData()
    End Sub

    Private Sub GetData()
        Cls.delete_Branch_All("PrintSalesPurchases")

        Dim aray_1 As New ArrayList
        Dim aray_2 As New ArrayList
        Dim aray_3 As New ArrayList
        Dim aray_4 As New ArrayList
        Dim aray_5 As New ArrayList
        Dim aray_6 As New ArrayList
        Dim aray_7 As New ArrayList

        aray_1.Clear() : aray_2.Clear() : aray_3.Clear() : aray_4.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select itm_id,sname,TinPrice,Stores from Items where id <> N''"
        If chkAll.Checked = False Then
            If cmbStoreView.Text <> "" Then
                S = S & " and  Stores =N'" & cmbStoreView.Text & "'"
            End If
            If cmbCatsManufacturingView.Text <> "" Then
                S = S & " and  group_name =N'" & cmbCatsManufacturingView.Text & "'"
            End If
            If cmbItemsManufacturingView.Text <> "" Then
                S = S & " and  sname =N'" & cmbItemsManufacturingView.Text & "'"
            End If
        End If
        If FilterSelect = "Number" Then
            S = S & " order by itm_id"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by sname"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by sname"
        End If
        cmd.CommandText = S
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_1.Add(dr(0)) : aray_2.Add(dr(1)) : aray_3.Add(dr(2)) : aray_4.Add(dr(3))
        Loop

        Dim qu, Totalqu As Double
        Dim SM10, SM11 As Double
        Dim itm_id As String = ""
        Dim Stores As String = ""
        Dim Price As String = ""
        For i As Integer = 0 To aray_1.Count - 1
            itm_id = aray_1(i).ToString
            aray_5.Clear() : aray_6.Clear() : aray_7.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id from PrintSalesPurchases where itm_id=N'" & aray_1(i).ToString & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = False Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                If ChkWithoutDate.Checked = False Then
                    'cmd.CommandText = "Select dbo.ManufacturingDismissalNotice.itm_id_Manufacturing, dbo.Manufacturing_BillsalData.qu, dbo.ManufacturingDismissalNotice.TotalCostPrice, dbo.ManufacturingDismissalNotice.Stores_TO From dbo.ManufacturingDismissalNotice INNER Join  dbo.Manufacturing_BillsalData ON dbo.ManufacturingDismissalNotice.bill_no = dbo.Manufacturing_BillsalData.bill_noNotice Group By dbo.ManufacturingDismissalNotice.itm_id_Manufacturing, dbo.ManufacturingDismissalNotice.Stores_TO, dbo.Manufacturing_BillsalData.qu, dbo.ManufacturingDismissalNotice.TotalCostPrice HAVING(dbo.ManufacturingDismissalNotice.itm_id_Manufacturing = N'" & itm_id & "') AND (dbo.ManufacturingDismissalNotice.Stores_TO = N'" & cmbStoreView.Text & "')"
                Else
                    cmd.CommandText = "select qunt_Manufacturing,TotalCostPrice,Stores_TO from ManufacturingDismissalNotice where itm_id_Manufacturing=N'" & itm_id & "' and Stores_TO = N'" & cmbStoreView.Text & "'"
                End If
                dr = cmd.ExecuteReader
                Do While dr.Read = True
                    aray_5.Add(dr("qunt_Manufacturing"))
                    aray_7.Add(dr("Stores_TO"))
                Loop

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select CostPrice from ManufacturingProduct where itm_id_Manufacturing=N'" & itm_id & "'" : dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    Price = dr("CostPrice").ToString
                End If


                SM10 = 0 : SM11 = 0 : Totalqu = 0 : qu = 0
                For M As Integer = 0 To aray_5.Count - 1
                    SM10 += Val(aray_5(M).ToString)
                    Stores = aray_7(M).ToString
                Next
                qu = SM10
                Totalqu = Val(Price) * Val(qu)
                Totalqu = Math.Round(Val(Totalqu), 2)

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into PrintSalesPurchases (itm_id,itm_name,qu,price,totalprice,store)  values("
                S = S & "N'" & aray_1(i).ToString & "',N'" & aray_2(i).ToString & "',N'" & qu & "',N'" & Price & "',N'" & Totalqu & "',N'" & Stores & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        Next

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete from PrintSalesPurchases where qu =N'0'" : cmd.ExecuteNonQuery()

        '=======================================================================

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "select itm_id as [الباركود] ,itm_name as [اسم الصنف],price as [سعر التكلفة] ,qu as [الكمية],totalprice as [إجمالى التكلفة],store as [المخزن] from PrintSalesPurchases order by itm_name"
        cmd.CommandText = S : dr = cmd.ExecuteReader
        dgv_Material.DataSource = Cls.PopulateDataView(dr)

        'Dim SM2 As String
        'For i As Integer = 0 To dgv_Material.RowCount - 1
        '    SM2 = Val(dgv_Material.Rows(i).Cells(2).Value) * Val(dgv_Material.Rows(i).Cells(3).Value)
        '    dgv_Material.Rows(i).Cells(4).Value = SM2
        'Next

        Dim SM As Double
        For i As Integer = 0 To dgv_Material.Rows.Count - 1
            SM = SM + Val(dgv_Material.Rows(i).Cells(3).Value.ToString)
        Next
        txtTotalWeight.Text = SM

        Dim SM1 As Double
        For i As Integer = 0 To dgv_Material.Rows.Count - 1
            SM1 = SM1 + Val(dgv_Material.Rows(i).Cells(4).Value.ToString)
        Next
        txtTotalCostPrice.Text = SM1
    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbCatsManufacturingView.Enabled = False
            cmbItemsManufacturingView.Enabled = False
            cmbStoreView.Enabled = False
        Else
            cmbCatsManufacturingView.Enabled = True
            cmbItemsManufacturingView.Enabled = True
            cmbStoreView.Enabled = True
        End If
    End Sub
#End Region

#Region "Print"
    Private Sub btnPrint_Click(sender As Object, e As EventArgs) Handles btnPrint.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If dgv_Material.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If dgv_Material.RowCount = 0 Then Beep() : Exit Sub
        If (dgv_Material.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To dgv_Material.Rows.Count - 1
            S = "insert into PrintSalesPurchases(Company_Branch_ID,itm_id,itm_name,price,qu,totalprice,store) values"
            S = S & " (N'" & Company_Branch_ID & "',N'" & dgv_Material.Rows(i).Cells(0).Value & "',N'" & dgv_Material.Rows(i).Cells(1).Value & "',N'" & dgv_Material.Rows(i).Cells(2).Value & "',N'" & dgv_Material.Rows(i).Cells(3).Value & "',N'" & dgv_Material.Rows(i).Cells(4).Value & "',N'" & dgv_Material.Rows(i).Cells(5).Value & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        AddReportView()
        Dim rpt As New Rpt_Product_Manufacturing_Total

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        AddReportView()
        Dim txtname, txtNameAr, txtNameEn, TotalCostPrice_Object, Manufacturing_Allowance_Object As TextObject

        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير اجمالى أذن صرف من المصنع"
        txtNameAr = rpt.Section1.ReportObjects("txtTitelNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelNameEn")
        txtNameEn.Text = NameEnCompany
        TotalCostPrice_Object = rpt.Section1.ReportObjects("txtWeightqunt")
        TotalCostPrice_Object.Text = txtTotalWeight.Text
        Manufacturing_Allowance_Object = rpt.Section1.ReportObjects("txtTotalCostPrice")
        Manufacturing_Allowance_Object.Text = txtTotalCostPrice.Text
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "تقرير اجمالى أذن صرف من المصنع"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs)

    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub

#End Region


End Class
