﻿Imports System.Data.SqlClient

Public Class frmBanksManagement
    Private currentBankID As Integer = 0
    Private isEditMode As Boolean = False

    Private Sub frmBanksManagement_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        InitializeForm()
        LoadCurrencies()
        LoadBanks()
    End Sub

    Private Sub InitializeForm()
        ' إعداد عناصر التحكم
        dtpOpeningDate.Value = DateTime.Now
        txtBankCode.Enabled = True

        ' إعداد DataGridView
        dgvBanks.AutoGenerateColumns = False
        dgvBanks.Columns.Clear()

        ' إضافة الأعمدة يدوياً
        Dim colBankID As New DataGridViewTextBoxColumn()
        colBankID.Name = "BankID"
        colBankID.HeaderText = "ID"
        colBankID.DataPropertyName = "BankID"
        colBankID.Visible = False
        dgvBanks.Columns.Add(colBankID)

        Dim colBankCode As New DataGridViewTextBoxColumn()
        colBankCode.Name = "BankCode"
        colBankCode.HeaderText = "كود البنك"
        colBankCode.DataPropertyName = "BankCode"
        dgvBanks.Columns.Add(colBankCode)

        Dim colBankName As New DataGridViewTextBoxColumn()
        colBankName.Name = "BankName"
        colBankName.HeaderText = "اسم البنك"
        colBankName.DataPropertyName = "BankName"
        dgvBanks.Columns.Add(colBankName)

        Dim colBranch As New DataGridViewTextBoxColumn()
        colBranch.Name = "BranchName"
        colBranch.HeaderText = "الفرع"
        colBranch.DataPropertyName = "BranchName"
        dgvBanks.Columns.Add(colBranch)

        Dim colAccount As New DataGridViewTextBoxColumn()
        colAccount.Name = "AccountNumber"
        colAccount.HeaderText = "رقم الحساب"
        colAccount.DataPropertyName = "AccountNumber"
        dgvBanks.Columns.Add(colAccount)

        Dim colCurrency As New DataGridViewTextBoxColumn()
        colCurrency.Name = "Currency"
        colCurrency.HeaderText = "العملة"
        colCurrency.DataPropertyName = "Currency"
        dgvBanks.Columns.Add(colCurrency)

        Dim colBalance As New DataGridViewTextBoxColumn()
        colBalance.Name = "CurrentBalance"
        colBalance.HeaderText = "الرصيد الحالي"
        colBalance.DataPropertyName = "CurrentBalance"
        colBalance.DefaultCellStyle.Format = "N2"
        colBalance.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
        dgvBanks.Columns.Add(colBalance)

        ' تنسيق الجريد
        dgvBanks.RightToLeft = RightToLeft.Yes
        dgvBanks.EnableHeadersVisualStyles = False
        dgvBanks.ColumnHeadersDefaultCellStyle.BackColor = Color.Teal
        dgvBanks.ColumnHeadersDefaultCellStyle.ForeColor = Color.White
        dgvBanks.ColumnHeadersDefaultCellStyle.Font = New Font("Arial", 12, FontStyle.Bold)
        dgvBanks.DefaultCellStyle.Font = New Font("Arial", 11)

        ClearForm()
    End Sub

    Private Sub LoadCurrencies()
        Dim query As String = "SELECT ID_Currency, Name_Currency FROM Type_Currency ORDER BY Name_Currency"
        Dim dt As DataTable = DBHelper.GetDataTable(query)

        cmbCurrency.DataSource = dt
        cmbCurrency.DisplayMember = "Name_Currency"
        cmbCurrency.ValueMember = "ID_Currency"
    End Sub

    Private Sub LoadBanks(Optional searchText As String = "")
        Dim query As String = "SELECT b.BankID, b.BankCode, b.BankName, b.BranchName, " &
                             "b.AccountNumber, t.Name_Currency AS Currency, " &
                             "b.CurrentBalance FROM BankData b " &
                             "INNER JOIN Type_Currency t ON b.ID_Currency = t.ID_Currency"

        If Not String.IsNullOrEmpty(searchText) Then
            query += " WHERE b.BankCode LIKE '%' + @Search + '%' OR " &
                     "b.BankName LIKE '%' + @Search + '%' OR " &
                     "b.BranchName LIKE '%' + @Search + '%' OR " &
                     "b.AccountNumber LIKE '%' + @Search + '%'"
        End If

        query += " ORDER BY b.BankCode"

        Dim params As New Dictionary(Of String, Object)
        params.Add("@Search", searchText)

        Try
            Dim dt As DataTable = DBHelper.GetDataTable(query, params)
            dgvBanks.DataSource = Nothing
            Application.DoEvents()
            dgvBanks.DataSource = dt
        Catch ex As Exception
            MessageBox.Show("حدث خطأ أثناء تحميل بيانات البنوك: " & ex.Message, "خطأ",
                          MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ClearForm()
        currentBankID = 0
        isEditMode = False
        txtBankCode.Clear()
        txtBankName.Clear()
        txtBranch.Clear()
        txtAccountNo.Clear()
        txtNotes.Clear()
        dtpOpeningDate.Value = DateTime.Now
        numDebit.Value = 0
        numCredit.Value = 0
        chkActive.Checked = True
        If cmbCurrency.Items.Count > 0 Then
            cmbCurrency.SelectedIndex = 0
        End If

        btnSave.Text = "حفظ"
        txtBankCode.Focus()
    End Sub

    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        ClearForm()
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If Not ValidateForm() Then Exit Sub

        Dim query As String
        Dim params As New Dictionary(Of String, Object)

        If isEditMode Then
            query = "UPDATE BankData SET BankCode = @BankCode, BankName = @BankName, " &
                    "BranchName = @BranchName, AccountNumber = @AccountNumber, " &
                    "ID_Currency = @CurrencyID, OpeningDate = @OpeningDate, " &
                    "OpeningDebit = @OpeningDebit, OpeningCredit = @OpeningCredit, " &
                    "CurrentBalance = (@OpeningDebit - @OpeningCredit), " &
                    "Notes = @Notes, IsActive = @IsActive, " &
                    "ModifiedDate = GETDATE(), ModifiedBy = @User " &
                    "WHERE BankID = @BankID"

            params.Add("@BankID", currentBankID)
        Else
            query = "INSERT INTO BankData (BankCode, BankName, BranchName, AccountNumber, " &
                    "ID_Currency, OpeningDate, OpeningDebit, OpeningCredit, CurrentBalance, " &
                    "Notes, IsActive, CreatedBy) " &
                    "VALUES (@BankCode, @BankName, @BranchName, @AccountNumber, " &
                    "@CurrencyID, @OpeningDate, @OpeningDebit, @OpeningCredit, " &
                    "(@OpeningDebit - @OpeningCredit), @Notes, @IsActive, @User)"
        End If

        params.Add("@BankCode", txtBankCode.Text.Trim())
        params.Add("@BankName", txtBankName.Text.Trim())
        params.Add("@BranchName", txtBranch.Text.Trim())
        params.Add("@AccountNumber", txtAccountNo.Text.Trim())
        params.Add("@CurrencyID", cmbCurrency.SelectedValue)
        params.Add("@OpeningDate", dtpOpeningDate.Value)
        params.Add("@OpeningDebit", numDebit.Value)
        params.Add("@OpeningCredit", numCredit.Value)
        params.Add("@Notes", txtNotes.Text.Trim())
        params.Add("@IsActive", chkActive.Checked)
        params.Add("@User", UserName)

        Try
            If DBHelper.ExecuteNonQuery(query, params) > 0 Then
                MessageBox.Show("تم حفظ بيانات البنك بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                ClearForm()
                LoadBanks()
            End If
        Catch ex As SqlException When ex.Number = 2627 ' انتهاك فريد للقيد
            MessageBox.Show("كود البنك مسجل بالفعل، يجب إدخال كود آخر", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtBankCode.Focus()
        Catch ex As Exception
            MessageBox.Show("حدث خطأ أثناء حفظ البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrEmpty(txtBankCode.Text.Trim()) Then
            MessageBox.Show("يجب إدخال كود البنك", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtBankCode.Focus()
            Return False
        End If

        If String.IsNullOrEmpty(txtBankName.Text.Trim()) Then
            MessageBox.Show("يجب إدخال اسم البنك", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtBankName.Focus()
            Return False
        End If

        ' التحقق من أن المدين والدائن لا يكونان معاً لهما قيمة
        If numDebit.Value > 0 And numCredit.Value > 0 Then
            MessageBox.Show("لا يمكن أن يكون رصيد أول المدة مدين ودائن في نفس الوقت", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            numDebit.Focus()
            Return False
        End If

        Return True
    End Function

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If currentBankID = 0 Then
            MessageBox.Show("يجب اختيار بنك للحذف أولاً", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        If MessageBox.Show("هل أنت متأكد من حذف هذا البنك؟", "تأكيد الحذف",
                          MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then

            Dim query As String = "DELETE FROM BankData WHERE BankID = @BankID"
            Dim params As New Dictionary(Of String, Object)
            params.Add("@BankID", currentBankID)

            Try
                If DBHelper.ExecuteNonQuery(query, params) > 0 Then
                    MessageBox.Show("تم حذف البنك بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    ClearForm()
                    LoadBanks()
                End If
            Catch ex As Exception
                MessageBox.Show("حدث خطأ أثناء الحذف: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        Dim searchText As String = InputBox("أدخل نص البحث (كود البنك، اسم البنك، الفرع، رقم الحساب):", "بحث عن بنك")

        If Not String.IsNullOrEmpty(searchText) Then
            LoadBanks(searchText)
        End If
    End Sub

    Private Sub dgvBanks_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgvBanks.CellClick
        If e.RowIndex >= 0 Then
            currentBankID = Convert.ToInt32(dgvBanks.Rows(e.RowIndex).Cells("BankID").Value)
            LoadBankDetails(currentBankID)
        End If
    End Sub

    Private Sub LoadBankDetails(bankID As Integer)
        Dim query As String = "SELECT * FROM BankData WHERE BankID = @BankID"
        Dim params As New Dictionary(Of String, Object)
        params.Add("@BankID", bankID)

        Dim dt As DataTable = DBHelper.GetDataTable(query, params)

        If dt.Rows.Count > 0 Then
            Dim row As DataRow = dt.Rows(0)

            isEditMode = True
            txtBankCode.Text = row("BankCode").ToString()
            txtBankName.Text = row("BankName").ToString()
            txtBranch.Text = row("BranchName").ToString()
            txtAccountNo.Text = row("AccountNumber").ToString()

            If Not IsDBNull(row("ID_Currency")) Then
                cmbCurrency.SelectedValue = row("ID_Currency")
            End If

            If Not IsDBNull(row("OpeningDate")) Then
                dtpOpeningDate.Value = Convert.ToDateTime(row("OpeningDate"))
            End If

            numDebit.Value = Convert.ToDecimal(row("OpeningDebit"))
            numCredit.Value = Convert.ToDecimal(row("OpeningCredit"))

            If Not IsDBNull(row("Notes")) Then
                txtNotes.Text = row("Notes").ToString()
            End If

            chkActive.Checked = Convert.ToBoolean(row("IsActive"))

            btnSave.Text = "تحديث"
        End If
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub

    Private Sub btnExport_Click(sender As Object, e As EventArgs) Handles btnExport.Click
        Try
            Dim saveDialog As New SaveFileDialog()
            saveDialog.Filter = "Excel Files|*.xlsx"
            saveDialog.Title = "حفظ كملف Excel"
            saveDialog.FileName = "بيانات البنوك_" & DateTime.Now.ToString("yyyyMMdd")

            If saveDialog.ShowDialog() = DialogResult.OK Then
                Dim excelApp As New Microsoft.Office.Interop.Excel.Application()
                Dim workbook As Microsoft.Office.Interop.Excel.Workbook = excelApp.Workbooks.Add()
                Dim worksheet As Microsoft.Office.Interop.Excel.Worksheet = workbook.ActiveSheet

                ' تصدير البيانات
                For i As Integer = 0 To dgvBanks.Columns.Count - 1
                    worksheet.Cells(1, i + 1) = dgvBanks.Columns(i).HeaderText
                Next

                For i As Integer = 0 To dgvBanks.Rows.Count - 1
                    For j As Integer = 0 To dgvBanks.Columns.Count - 1
                        worksheet.Cells(i + 2, j + 1) = dgvBanks.Rows(i).Cells(j).Value.ToString()
                    Next
                Next

                workbook.SaveAs(saveDialog.FileName)
                workbook.Close()
                excelApp.Quit()

                MessageBox.Show("تم تصدير البيانات بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
        Catch ex As Exception
            MessageBox.Show("حدث خطأ أثناء التصدير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class

Public Class DBHelper
    Public Shared ReadOnly Property ConnectionString() As String
        Get
            Return constring
        End Get
    End Property

    Public Shared Function GetDataTable(query As String, Optional params As Dictionary(Of String, Object) = Nothing) As DataTable
        Dim dt As New DataTable()
        Using conn As New SqlConnection(ConnectionString)
            Using cmd As New SqlCommand(query, conn)
                If params IsNot Nothing Then
                    For Each param In params
                        cmd.Parameters.AddWithValue(param.Key, param.Value)
                    Next
                End If
                conn.Open()
                Using da As New SqlDataAdapter(cmd)
                    da.Fill(dt)
                End Using
            End Using
        End Using
        Return dt
    End Function

    Public Shared Function ExecuteNonQuery(query As String, Optional params As Dictionary(Of String, Object) = Nothing) As Integer
        Using conn As New SqlConnection(ConnectionString)
            Using cmd As New SqlCommand(query, conn)
                If params IsNot Nothing Then
                    For Each param In params
                        cmd.Parameters.AddWithValue(param.Key, param.Value)
                    Next
                End If
                conn.Open()
                Return cmd.ExecuteNonQuery()
            End Using
        End Using
    End Function

    Public Shared Function ExecuteScalar(query As String, Optional params As Dictionary(Of String, Object) = Nothing) As Object
        Using conn As New SqlConnection(ConnectionString)
            Using cmd As New SqlCommand(query, conn)
                If params IsNot Nothing Then
                    For Each param In params
                        cmd.Parameters.AddWithValue(param.Key, param.Value)
                    Next
                End If
                conn.Open()
                Return cmd.ExecuteScalar()
            End Using
        End Using
    End Function
End Class