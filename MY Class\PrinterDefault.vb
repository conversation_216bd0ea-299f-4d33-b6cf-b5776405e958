﻿Imports System.Runtime.InteropServices

Module PrinterDefault

    Private Declare Function SetDefaultPrinter2 Lib "winspool.drv" Alias "SetDefaultPrinterA" (ByVal printerName As String) As Boolean

    Sub Main()
        ' احصل على اسم الطابعة الافتراضية الحالية
        Dim defaultPrinterName As String = New Printing.PrinterSettings().PrinterName

        ' احصل على قائمة جميع الطابعات المتاحة في النظام
        Dim printerList As String() = Printing.PrinterSettings.InstalledPrinters.Cast(Of String)().ToArray()

        ' ابحث عن الطابعة الأخرى غير الطابعة الافتراضية
        Dim newPrinterName As String = printerList.FirstOrDefault(Function(p) p <> defaultPrinterName)

        If Not String.IsNullOrEmpty(newPrinterName) Then
            ' قم بتعيين الطابعة الجديدة كطابعة افتراضية
            SetDefaultPrinter2(newPrinterName)

            Console.WriteLine("تم تغيير الطابعة الافتراضية إلى: " & newPrinterName)
        Else
            Console.WriteLine("لا يوجد طابعة أخرى متاحة للتبديل.")
        End If

        Console.ReadLine()
    End Sub

    Public Sub SetDefaultPrinter(ByVal printerName As String)
        SetDefaultPrinter2(printerName)
    End Sub

End Module
