﻿Public Class frmEmployees

    Private Sub frmEmployees_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If Action = "Edit" Then
            FillData()
            btnSave.Text = "تعديل"
        Else
            MAXRECORD()
            cmbNameEmployee.Focus()
        End If
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbNameEmployee)
    End Sub

    Private Sub MAXRECORD()
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select * from Employees"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            txtCode.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(EMPID As float)) as mb FROM Employees"
            dr = cmd.ExecuteReader
            dr.Read()
            Dim sh As Long
            sh = dr("mb")
            txtCode.Text = sh + 1
        End If
    End Sub

    Private Sub FillData()
        Try
            ' استعلام آمن لتحميل بيانات الموظف
            Dim query As String = "SELECT EMPID, NameEmployee, Address, Phone, MaritalStatus, Qualification, Job, Administration, DateBirth, DateAppointment, DateEndService, NationalID, NumberInsurance, MilitaryService, BalanceLeavePrior, BalanceAnnualLeave, TotalBalanceLeave, DaysAbsence, DaysSanctions, LeaveWithoutPay, Emp_Salary, Emp_Rate_Hour, Emp_Count_Hour FROM Employees WHERE EMPID = @empId"

            Dim parameters As New Dictionary(Of String, Object) From {
                {"@empId", EditItmId}
            }

            Dim result As DataTable = SecureDatabaseManager.Instance.ExecuteQuery(query, parameters)
            If result.Rows.Count > 0 Then
                Dim row As DataRow = result.Rows(0)

                ' تعبئة الحقول من البيانات المسترجعة
                Dim XEMPID As String = row("EMPID").ToString()
                Dim XNameEmployee As String = row("NameEmployee").ToString()
                Dim XAddress As String = If(IsDBNull(row("Address")), "", row("Address").ToString())
                Dim XPhone As String = If(IsDBNull(row("Phone")), "", row("Phone").ToString())
                Dim XMaritalStatus As String = If(IsDBNull(row("MaritalStatus")), "", row("MaritalStatus").ToString())
                Dim XQualification As String = If(IsDBNull(row("Qualification")), "", row("Qualification").ToString())
                Dim XJob As String = If(IsDBNull(row("Job")), "", row("Job").ToString())
                Dim XAdministration As String = If(IsDBNull(row("Administration")), "", row("Administration").ToString())
                Dim XDateBirth As String = If(IsDBNull(row("DateBirth")), "", row("DateBirth").ToString())
                Dim XDateAppointment As String = If(IsDBNull(row("DateAppointment")), "", row("DateAppointment").ToString())
                Dim XDateEndService As String = If(IsDBNull(row("DateEndService")), "", row("DateEndService").ToString())
                Dim XNationalID As String = If(IsDBNull(row("NationalID")), "", row("NationalID").ToString())
                Dim XNumberInsurance As String = If(IsDBNull(row("NumberInsurance")), "", row("NumberInsurance").ToString())
                Dim XMilitaryService As String = If(IsDBNull(row("MilitaryService")), "", row("MilitaryService").ToString())
                Dim XBalanceLeavePrior As String = If(IsDBNull(row("BalanceLeavePrior")), "", row("BalanceLeavePrior").ToString())
                Dim XBalanceAnnualLeave As String = If(IsDBNull(row("BalanceAnnualLeave")), "", row("BalanceAnnualLeave").ToString())
                Dim XTotalBalanceLeave As String = If(IsDBNull(row("TotalBalanceLeave")), "", row("TotalBalanceLeave").ToString())
                Dim XDaysAbsence As String = If(IsDBNull(row("DaysAbsence")), "", row("DaysAbsence").ToString())
                Dim XDaysSanctions As String = If(IsDBNull(row("DaysSanctions")), "", row("DaysSanctions").ToString())
                Dim XLeaveWithoutPay As String = If(IsDBNull(row("LeaveWithoutPay")), "", row("LeaveWithoutPay").ToString())

                ' تعبئة الحقول في النموذج
                txtCode.Text = XEMPID
                cmbNameEmployee.Text = XNameEmployee
                txtAddress.Text = XAddress
                txtPhone.Text = XPhone
                cmbMaritalStatus.Text = XMaritalStatus
                cmbQualification.Text = XQualification
                cmbJob.Text = XJob
                cmbAdministration.Text = XAdministration
                dtpDateBirth.Text = XDateBirth
                dtpDateAppointment.Text = XDateAppointment
                dtpDateEndService.Text = XDateEndService
                txtNationalID.Text = XNationalID
                txtNumberInsurance.Text = XNumberInsurance
                cmbMilitaryService.Text = XMilitaryService
                txtBalanceLeavePrior.Text = XBalanceLeavePrior
                txtBalanceAnnualLeave.Text = XBalanceAnnualLeave
                txtTotalBalanceLeave.Text = XTotalBalanceLeave
                txtDaysAbsence.Text = XDaysAbsence
                txtDaysSanctions.Text = XDaysSanctions
                txtLeaveWithoutPay.Text = XLeaveWithoutPay
                txtEmp_Salary.Text = If(IsDBNull(row("Emp_Salary")), "", row("Emp_Salary").ToString())
                txtEmp_Rate_Hour.Text = If(IsDBNull(row("Emp_Rate_Hour")), "", row("Emp_Rate_Hour").ToString())
                txtEmp_Count_Hour.Text = If(IsDBNull(row("Emp_Count_Hour")), "", row("Emp_Count_Hour").ToString())
            Else
                ' لا توجد بيانات للموظف
                ErrorHandler.LogWarning($"لم يتم العثور على بيانات للموظف رقم: {EditItmId}")
                MessageBox.Show("لم يتم العثور على بيانات الموظف المحدد", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في تحميل بيانات الموظف", ex, $"EmployeeID: {EditItmId}")
            MessageBox.Show("حدث خطأ في تحميل بيانات الموظف", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub cmbNameEmployee_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbNameEmployee.KeyUp
        If e.KeyCode = 13 Then
            txtAddress.Focus()
        End If
    End Sub

    Private Sub txtAddress_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtAddress.KeyUp
        If e.KeyCode = 13 Then
            txtPhone.Focus()
        End If
    End Sub

    Private Sub txtPhone_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtPhone.KeyUp
        If e.KeyCode = 13 Then
            cmbMaritalStatus.Focus()
        End If
    End Sub

    Private Sub cmbMaritalStatus_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbMaritalStatus.KeyUp
        If e.KeyCode = 13 Then
            cmbQualification.Focus()
        End If
    End Sub

    Private Sub cmbQualification_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbQualification.KeyUp
        If e.KeyCode = 13 Then
            cmbJob.Focus()
        End If
    End Sub

    Private Sub cmbJob_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbJob.KeyUp
        If e.KeyCode = 13 Then
            cmbAdministration.Focus()
        End If
    End Sub

    Private Sub cmbAdministration_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbAdministration.KeyUp
        If e.KeyCode = 13 Then
            dtpDateBirth.Focus()
        End If
    End Sub

    Private Sub dtpDateBirth_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDateBirth.KeyUp
        If e.KeyCode = 13 Then
            dtpDateAppointment.Focus()
        End If
    End Sub

    Private Sub dtpDateAppointment_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDateAppointment.KeyUp
        If e.KeyCode = 13 Then
            dtpDateEndService.Focus()
        End If
    End Sub

    Private Sub dtpDateEndService_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDateEndService.KeyUp
        If e.KeyCode = 13 Then
            txtNationalID.Focus()
        End If
    End Sub

    Private Sub txtNationalID_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtNationalID.KeyUp
        If e.KeyCode = 13 Then
            txtNumberInsurance.Focus()
        End If
    End Sub

    Private Sub txtNumberInsurance_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtNumberInsurance.KeyUp
        If e.KeyCode = 13 Then
            cmbMilitaryService.Focus()
        End If
    End Sub

    Private Sub cmbMilitaryService_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbMilitaryService.KeyUp
        If e.KeyCode = 13 Then
            txtBalanceLeavePrior.Focus()
        End If
    End Sub

    Private Sub txtBalanceLeavePrior_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtBalanceLeavePrior.KeyUp
        If e.KeyCode = 13 Then
            txtBalanceAnnualLeave.Focus()
        End If
    End Sub

    Private Sub txtBalanceAnnualLeave_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtBalanceAnnualLeave.KeyUp
        If e.KeyCode = 13 Then
            txtTotalBalanceLeave.Focus()
        End If
    End Sub

    Private Sub txtTotalBalanceLeave_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtTotalBalanceLeave.KeyUp
        If e.KeyCode = 13 Then
            txtDaysAbsence.Focus()
        End If
    End Sub

    Private Sub txtDaysAbsence_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtDaysAbsence.KeyUp
        If e.KeyCode = 13 Then
            txtDaysSanctions.Focus()
        End If
    End Sub

    Private Sub txtDaysSanctions_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtDaysSanctions.KeyUp
        If e.KeyCode = 13 Then
            txtLeaveWithoutPay.Focus()
        End If
    End Sub

    Private Sub txtLeaveWithoutPay_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtLeaveWithoutPay.KeyUp
        If e.KeyCode = 13 Then
            txtEmp_Salary.Focus()
        End If
    End Sub

    Private Sub txtNationalID_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtNationalID.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtNationalID)
    End Sub

    Private Sub txtNumberInsurance_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtNumberInsurance.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtNumberInsurance)
    End Sub

    Private Sub txtBalanceLeavePrior_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtBalanceLeavePrior.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtBalanceLeavePrior)
    End Sub

    Private Sub txtBalanceAnnualLeave_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtBalanceAnnualLeave.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtBalanceAnnualLeave)
    End Sub

    Private Sub txtTotalBalanceLeave_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTotalBalanceLeave.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtTotalBalanceLeave)
    End Sub

    Private Sub txtDaysAbsence_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtDaysAbsence.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtDaysAbsence)
    End Sub

    Private Sub txtDaysSanctions_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtDaysSanctions.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtDaysSanctions)
    End Sub

    Private Sub txtLeaveWithoutPay_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtLeaveWithoutPay.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtLeaveWithoutPay)
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateTextAdd() = False Then Exit Sub

        Try
            ' حذف آمن للموظف في حالة التعديل
            If Action = "Edit" Then
                Dim deleteQuery As String = "DELETE FROM Employees WHERE EMPID = @empId"
                Dim deleteParams As New Dictionary(Of String, Object) From {
                    {"@empId", EditItmId}
                }
                SecureDatabaseManager.Instance.ExecuteNonQuery(deleteQuery, deleteParams)
            End If

            ' إدراج آمن لبيانات الموظف
            Dim insertQuery As String = "INSERT INTO Employees(Company_Branch_ID, EMPID, NameEmployee, Address, Phone, MaritalStatus, Qualification, Job, Administration, DateBirth, DateAppointment, DateEndService, NationalID, NumberInsurance, MilitaryService, BalanceLeavePrior, BalanceAnnualLeave, TotalBalanceLeave, DaysAbsence, DaysSanctions, LeaveWithoutPay, UserName, Bran_code, Emp_Type_Code, Emp_Status_Code, Emp_Salary, Emp_Rate_Hour, Emp_Count_Hour) VALUES (@companyBranchId, @empId, @nameEmployee, @address, @phone, @maritalStatus, @qualification, @job, @administration, @dateBirth, @dateAppointment, @dateEndService, @nationalId, @numberInsurance, @militaryService, @balanceLeavePrior, @balanceAnnualLeave, @totalBalanceLeave, @daysAbsence, @daysSanctions, @leaveWithoutPay, @userName, @branCode, @empTypeCode, @empStatusCode, @empSalary, @empRateHour, @empCountHour)"

            Dim insertParams As New Dictionary(Of String, Object) From {
                {"@companyBranchId", Company_Branch_ID},
                {"@empId", txtCode.Text.Trim()},
                {"@nameEmployee", cmbNameEmployee.Text.Trim()},
                {"@address", txtAddress.Text.Trim()},
                {"@phone", txtPhone.Text.Trim()},
                {"@maritalStatus", cmbMaritalStatus.Text.Trim()},
                {"@qualification", cmbQualification.Text.Trim()},
                {"@job", cmbJob.Text.Trim()},
                {"@administration", cmbAdministration.Text.Trim()},
                {"@dateBirth", Cls.C_date(dtpDateBirth.Text)},
                {"@dateAppointment", Cls.C_date(dtpDateAppointment.Text)},
                {"@dateEndService", Cls.C_date(dtpDateEndService.Text)},
                {"@nationalId", txtNationalID.Text.Trim()},
                {"@numberInsurance", txtNumberInsurance.Text.Trim()},
                {"@militaryService", cmbMilitaryService.Text.Trim()},
                {"@balanceLeavePrior", Val(txtBalanceLeavePrior.Text.Trim())},
                {"@balanceAnnualLeave", Val(txtBalanceAnnualLeave.Text.Trim())},
                {"@totalBalanceLeave", Val(txtTotalBalanceLeave.Text.Trim())},
                {"@daysAbsence", Val(txtDaysAbsence.Text.Trim())},
                {"@daysSanctions", Val(txtDaysSanctions.Text.Trim())},
                {"@leaveWithoutPay", Val(txtLeaveWithoutPay.Text.Trim())},
                {"@userName", UserName},
                {"@branCode", 0},
                {"@empTypeCode", 0},
                {"@empStatusCode", 0},
                {"@empSalary", Val(txtEmp_Salary.Text.Trim())},
                {"@empRateHour", Val(txtEmp_Rate_Hour.Text.Trim())},
                {"@empCountHour", Val(txtEmp_Count_Hour.Text.Trim())}
            }

            SecureDatabaseManager.Instance.ExecuteNonQuery(insertQuery, insertParams)

            ' إجراءات ما بعد الحفظ
            If Action = "Edit" Then
                Action = "Add"
                Me.Close()
                frmShowEmployees.btnShow.PerformClick()
            Else
                MsgBox("تمت عملية الحفظ بنجاح", MsgBoxStyle.Information)
                ClearSave()
                MAXRECORD()
                Cls.fill_combo_Branch("Employees", "NameEmployee", cmbNameEmployee)
            End If

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في حفظ بيانات الموظف", ex, $"EmployeeName: {cmbNameEmployee.Text.Trim()}")
            MessageBox.Show("حدث خطأ في حفظ بيانات الموظف", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Function ValidateTextAdd() As Boolean
        If cmbNameEmployee.Text = "" Then MsgBox("فضلا ادخل إسم الموظف", MsgBoxStyle.Exclamation) : cmbNameEmployee.Focus() : Return False
        'If txtPhone.Text = "" Then MsgBox("فضلا ادخل رقم التليفون", MsgBoxStyle.Exclamation) : txtPhone.Focus() : Return False
        'If cmbQualification.Text = "" Then MsgBox("فضلا ادخل رقم المؤهل", MsgBoxStyle.Exclamation) : cmbQualification.Focus() : Return False
        'If cmbJob.Text = "" Then MsgBox("فضلا ادخل الوظيفة", MsgBoxStyle.Exclamation) : cmbJob.Focus() : Return False
        'If txtNationalID.Text = "" Then MsgBox("فضلا ادخل الرقم القومى", MsgBoxStyle.Exclamation) : txtNationalID.Focus() : Return False

        Try
            ' التحقق الآمن من وجود اسم الموظف
            Dim checkQuery As String = "SELECT COUNT(*) FROM Employees WHERE NameEmployee = @employeeName"
            Dim checkParams As New Dictionary(Of String, Object) From {
                {"@employeeName", cmbNameEmployee.Text.Trim()}
            }

            Dim count As Object = SecureDatabaseManager.Instance.ExecuteScalar(checkQuery, checkParams)
            Dim employeeCount As Integer = If(count Is Nothing, 0, CInt(count))

            If employeeCount > 0 Then
                If Action = "Edit" Then
                    If txtCode.Text.Trim() <> EditItmId Then
                        MsgBox("إسم الموظف مسجل مسبقاً", MsgBoxStyle.Exclamation)
                        cmbNameEmployee.Focus()
                        Return False
                    End If
                Else
                    MsgBox("إسم الموظف مسجل مسبقاً", MsgBoxStyle.Exclamation)
                    cmbNameEmployee.Focus()
                    Return False
                End If
            End If

            Return True

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في التحقق من اسم الموظف", ex, $"EmployeeName: {cmbNameEmployee.Text.Trim()}")
            MessageBox.Show("حدث خطأ في التحقق من البيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    Private Sub ClearSave()
        Cls.clear(Me)
        cmbNameEmployee.Focus()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub txtEmp_Salary_TextChanged(sender As Object, e As EventArgs) Handles txtEmp_Salary.TextChanged
        MyVars.CheckNumber(txtEmp_Salary)
    End Sub

    Private Sub txtEmp_Rate_Hour_TextChanged(sender As Object, e As EventArgs) Handles txtEmp_Rate_Hour.TextChanged
        MyVars.CheckNumber(txtEmp_Rate_Hour)
    End Sub

    Private Sub txtEmp_Count_Hour_TextChanged(sender As Object, e As EventArgs) Handles txtEmp_Count_Hour.TextChanged
        MyVars.CheckNumber(txtEmp_Count_Hour)
    End Sub

    Private Sub txtEmp_Salary_KeyUp(sender As Object, e As KeyEventArgs) Handles txtEmp_Salary.KeyUp
        If e.KeyCode = 13 Then
            txtEmp_Rate_Hour.Focus()
        End If
    End Sub

    Private Sub txtEmp_Rate_Hour_KeyUp(sender As Object, e As KeyEventArgs) Handles txtEmp_Rate_Hour.KeyUp
        If e.KeyCode = 13 Then
            txtEmp_Count_Hour.Focus()
        End If
    End Sub

    Private Sub txtEmp_Count_Hour_KeyUp(sender As Object, e As KeyEventArgs) Handles txtEmp_Count_Hour.KeyUp
        If e.KeyCode = 13 Then
            btnSave.PerformClick()
        End If
    End Sub

    Private Sub BtnFind_Click(sender As Object, e As EventArgs) Handles BtnFind.Click
        frmShowEmployees.Show()
        Me.Close()
    End Sub

    Private Sub txtCode_TextChanged(sender As Object, e As EventArgs) Handles txtCode.TextChanged
        MyVars.CheckNumber(txtCode)
    End Sub
End Class