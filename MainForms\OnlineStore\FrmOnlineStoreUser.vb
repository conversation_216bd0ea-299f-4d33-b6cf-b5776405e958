﻿Imports CrystalDecisions.CrystalReports.Engine

Public Class FrmOnlineStoreUser
    Dim Area_ID As String

    Private Sub Frm_Group_Branch_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Cos.Fill_ComboBox_OnlineStore("[User] Where (IsDelete = 0)", "UserName", cmbCustomersView)
        Cos.Fill_ComboBox_OnlineStore("[ActivityType] Where (IsDelete = 0)", "ActivityTypeName", cmbActivityType)
        Cos.Fill_ComboBox_OnlineStore("[SlidesPrice] Where (IsDelete = 0)", "SlidesPriceName", cmbSlidesPrice)
        Cos.Fill_ComboBox_OnlineStore("[Governorate] Where (IsDelete = 0)", "UserName", cmbCustomersView)
        Cos.Fill_ComboBox_OnlineStore("[Area] Where (IsDelete = 0)", "Name", cmbArea)

        btnShow_Click(sender, e)
    End Sub

    Private Sub DTGV_CellValueChanged(sender As Object, e As DataGridViewCellEventArgs) Handles DTGV.CellValueChanged
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim ID As String = DTGV.SelectedRows(0).Cells(0).Value
        Dim UserName As String = DTGV.SelectedRows(0).Cells(1).Value
        Dim ShopName As String = DTGV.SelectedRows(0).Cells(2).Value
        Dim Mobile1 As String = DTGV.SelectedRows(0).Cells(3).Value
        Dim Address As String = DTGV.SelectedRows(0).Cells(7).Value
        Dim SlidesPriceId As String = DTGV.SelectedRows(0).Cells(8).Value

        If SlidesPriceId = 1 Or SlidesPriceId = 2 Or SlidesPriceId = 3 Then
            Cos.UpdateTabelOnlineStore("[User]", "UserName", UserName, ID)
            Cos.UpdateTabelOnlineStore("[User]", "ShopName", ShopName, ID)
            Cos.UpdateTabelOnlineStore("[User]", "Mobile1", Mobile1, ID)
            Cos.UpdateTabelOnlineStore("[User]", "Address", Address, ID)
            Cos.UpdateTabelOnlineStore("[User]", "SlidesPriceId", SlidesPriceId, ID)
        Else
            MsgBox("يوجد الى الان ثلاث شرائح للعملاء الارقام المتاحة 1 , 2 , 3", MsgBoxStyle.Information)
            SlidesPriceId = Cos.GetCodeValueOnlineStore("[User]", "SlidesPriceId", "Id", ID)
            DTGV.SelectedRows(0).Cells(2).Value = SlidesPriceId
            Cn.Close()
            connect()
        End If
    End Sub

    Private Sub btnShow_Click(sender As Object, e As EventArgs) Handles btnShow.Click
        Try
            If Not ConnectingOnlineStore() Is Nothing Then
                DTGV.DataSource = ""
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "Select dbo.[User].Id As [كود العميل], dbo.[User].UserName As [العميل], dbo.[User].ShopName As [اسم المحل], dbo.[User].Mobile1 As [الموبايل], dbo.ActivityType.ActivityTypeName As [النشاط], dbo.Governorate.Name As [المحافظة],    dbo.Area.Name AS [المنطقة], dbo.[User].Address As [العنوان], dbo.[User].SlidesPriceId As [الشرائح], dbo.[User].Password AS [الباسورد]  From dbo.[User] INNER Join dbo.ActivityType ON dbo.[User].ActivityTypeId = dbo.ActivityType.Id INNER Join  dbo.SlidesPrice ON dbo.[User].SlidesPriceId = dbo.SlidesPrice.Id INNER Join  dbo.Governorate ON dbo.[User].GovernorateId = dbo.Governorate.Id INNER Join   dbo.Area ON dbo.[User].AreaId = dbo.Area.Id Where (dbo.[User].IsDelete = 0)"
                If chkAll.Checked = False Then
                    If txtId.Text <> "" Then
                        S = S & " And dbo.[User].Id = N'" & txtId.Text.Trim & "'"
                    End If
                    If cmbCustomersView.Text <> "" Then
                        S = S & " And dbo.[User].UserName =N'" & cmbCustomersView.Text.Trim & "'"
                    End If
                    If cmbActivityType.Text <> "" Then
                        S = S & " And dbo.ActivityType.ActivityTypeName =N'" & cmbActivityType.Text.Trim & "'"
                    End If
                    If cmbSlidesPrice.Text <> "" Then
                        S = S & " And dbo.SlidesPrice.SlidesPriceName =N'" & cmbSlidesPrice.Text.Trim & "'"
                    End If
                    If cmbGovernorate.Text <> "" Then
                        S = S & " And dbo.Governorate.Name =N'" & cmbGovernorate.Text.Trim & "'"
                    End If
                    If cmbArea.Text <> "" Then
                        S = S & " And dbo.Area.Name =N'" & cmbArea.Text.Trim & "'"
                    End If
                End If
                S = S & " order by dbo.[User].Id"

                cmd.CommandText = S : dr = cmd.ExecuteReader
                DTGV.DataSource = Cls.PopulateDataView(dr)
                DTGV.Columns(1).Width = 120
                DTGV.Columns(2).Width = 120

                DTGV.Columns(0).ReadOnly = True
                DTGV.Columns(4).ReadOnly = True
                DTGV.Columns(5).ReadOnly = True
                DTGV.Columns(6).ReadOnly = True

                txtNumber.Text = DTGV.RowCount
                Cn.Close()
                connect()
            Else
                Cn.Close()
                connect()
                MsgBox(Cls_Constant.ErrMsg)
            End If


        Catch ex As Exception

        End Try

    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbCustomersView.Enabled = False
            cmbActivityType.Enabled = False
            cmbArea.Enabled = False
            cmbGovernorate.Enabled = False
            cmbSlidesPrice.Enabled = False
            txtId.Enabled = False
        Else
            cmbCustomersView.Enabled = True
            cmbActivityType.Enabled = True
            cmbArea.Enabled = True
            cmbGovernorate.Enabled = True
            cmbSlidesPrice.Enabled = True
            txtId.Enabled = True
        End If
    End Sub

    Private Sub btnPrint_Click(sender As Object, e As EventArgs) Handles btnPrint.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DTGV.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If SettingPrinterAuto = "YES" Then
            If PrintSmall = "YES" Then

            End If
            Dim DefaultPrinterBill As String = mykey.GetValue("DefaultPrinterBill", "DefaultPrinterBill")
            If Not Cls.SetDefaultSystemPrinter4(DefaultPrinterBill) Then
                MessageBox.Show("Error occured will trying to set the default printer!")
            End If
        End If

        Cls.GetDefaultPrinterA4()

        Try
            If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

            Cls.delete_Branch_All("PrintSalesPurchases")

            S = "Select dbo.[User].Id As [كود العميل], dbo.[User].UserName As [العميل], dbo.[User].ShopName As [اسم المحل], dbo.[User].Mobile1 As [الموبايل], dbo.ActivityType.ActivityTypeName As [النشاط], dbo.Governorate.Name As [المحافظة],    dbo.Area.Name AS [المنطقة], dbo.[User].Address As [العنوان], dbo.[User].SlidesPriceId As [الشرائح], dbo.[User].Password AS [الباسورد]  From dbo.[User] INNER Join dbo.ActivityType ON dbo.[User].ActivityTypeId = dbo.ActivityType.Id INNER Join  dbo.SlidesPrice ON dbo.[User].SlidesPriceId = dbo.SlidesPrice.Id INNER Join  dbo.Governorate ON dbo.[User].GovernorateId = dbo.Governorate.Id INNER Join   dbo.Area ON dbo.[User].AreaId = dbo.Area.Id Where (dbo.[User].IsDelete = 0)"

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            For i As Integer = 0 To DTGV.Rows.Count - 1
                S = "insert into PrintSalesPurchases(Vendorname,qu,VnPay,CustomerName,BILL_NO,totalprice)  values("
                S = S & "N'" & DTGV.Rows(i).Cells(1).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(3).Value & "',N'" & DTGV.Rows(i).Cells(7).Value & "',N'" & DTGV.Rows(i).Cells(9).Value & "',N'" & txtNumber.Text & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            Next
            AddReportView()
            Dim rpt As New rpt_OnlineStoreUser
            Dim txt, txtNameAr, txtNameEn As TextObject

            Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
            Dim dt As New DataTable
            dt.Load(dr)
            rpt.SetDataSource(dt)
            txt = rpt.Section1.ReportObjects("txtTitel")
            txt.Text = "تقرير عملاء المتجر الالكترونى"
            txtNameAr = rpt.Section1.ReportObjects("Text1")
            txtNameAr.Text = NameArCompay
            txtNameEn = rpt.Section1.ReportObjects("Text2")
            txtNameEn.Text = NameEnCompany
            If SelectLogoPathOther = "YES" Then
                rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
            Else
                rpt.SetParameterValue("ImageURL", CMPLogoPath)
            End If
            Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
            Frm_PrintReports.Text = "تقرير عملاء المتجر الالكترونى"
            Frm_PrintReports.Show()

            If RunDatabaseInternet = "YES" Then : connect() : End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub
End Class