﻿Public Class frmLinkingAccountsTree
    Dim MaxRecoedCode As String
    Private Sub frmLinkingAccountsTree_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Bra.Fil("AccountsTree", "ACCName", cmbAccountName)
        Bra.Fil("Account_Statement_Link", "Statement_link", cmbLink_Statement)
        FillData()
    End Sub

    Private Sub btnAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAdd.Click
        If Trim(cmbLink_Statement.Text) = "" Then MsgBox("من فضلك ادخل اسم المحافظة", MsgBoxStyle.Exclamation) : Exit Sub
        If Trim(cmbAccountName.Text) = "" Then MsgBox("من فضلك ادخل كود المحافظة", MsgBoxStyle.Exclamation) : Exit Sub
        If Cls.Check_Field_Value("AccountsTreeLinking", "Link_AccountsTree", cmbAccountName.Text) Then MsgBox("عفوا بيان مسجل من قبل", MsgBoxStyle.Exclamation) : Exit Sub

        MAXRECORD("AccountsTreeLinking", "Link_ID")

        Cls.insert("AccountsTreeLinking", "Link_ID,Link_Statement,ACCNumber,Link_AccountsTree", "N'" & MaxRecoedCode & "',N'" & cmbLink_Statement.Text & "',N'" & txtAccountCode.Text & "',N'" & cmbAccountName.Text & "'")
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)

        cmbLink_Statement.Text = ""
        cmbAccountName.Text = ""
        txtAccountCode.Text = ""

        FillData()

    End Sub

    Private Sub MAXRECORD(ByVal Tables As String, ByVal Code As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " & Tables & ""
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            MaxRecoedCode = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" & Code & " As float)) as mb FROM " & Tables & " where " & Code & " <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            MaxRecoedCode = sh + 1
        End If

    End Sub

    Private Sub FillData()
        Dgv_Add.DataSource = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT Link_ID as [الكود],Link_Statement as [بيان الربط],ACCNumber as [رقم الحساب],Link_AccountsTree as [أسم الحساب] FROM AccountsTreeLinking"

        cmd.CommandText = S : dr = cmd.ExecuteReader
        Dgv_Add.DataSource = Cls.PopulateDataView(dr)

    End Sub

    Private Sub cmbAccountName_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbAccountName.SelectedIndexChanged
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select ACCNumber from AccountsTree where ACCName=N'" & cmbAccountName.Text & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            txtAccountCode.Text = dr("ACCNumber")
        End If
    End Sub

    Private Sub cmbLink_Statement_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbLink_Statement.SelectedIndexChanged
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Statement_ID from Account_Statement_Link where Statement_link=N'" & cmbLink_Statement.Text & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            txtStatement_link.Text = dr("Statement_ID")
        End If
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If Dgv_Add.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To Dgv_Add.SelectedRows.Count - 1
            If Dgv_Add.RowCount = 0 Then Beep() : Exit Sub
            If (Dgv_Add.SelectedRows.Count) = 0 Then Beep() : Exit Sub

            Dim Link_ID As String = Dgv_Add.SelectedRows(i).Cells(0).Value

            cmd.CommandText = "delete from AccountsTreeLinking where Link_ID =N'" & Link_ID & "'" : cmd.ExecuteNonQuery()
        Next
        FillData()
    End Sub
End Class