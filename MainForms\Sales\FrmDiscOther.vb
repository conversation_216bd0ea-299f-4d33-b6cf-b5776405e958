﻿Public Class FrmDiscOther

    Private Sub FrmDiscCustomers_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Me.MdiParent = MdiParent
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        DataGridView1.DataSource = ""
        DataGridView1.DataSource = Nothing
        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", ComboBox1)

        End If
        ComboBox1.Items.Add("نقداً")

        GetData()
        GetDateNotBeenActivatedPrograms(DateTimePicker1)
        GetDateNotBeenActivatedOutcome()
    End Sub

    Private Sub GetDateNotBeenActivatedOutcome()
        If PermtionName <> "مدير" Then
            If DateNotBeenActivatedOutcome = "NO" Then
                DateTimePicker1.Enabled = True
            Else
                DateTimePicker1.Enabled = False
            End If
        End If
    End Sub

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT id, Vendorname AS [العميل],DiscStatement as [بيان الخصم],amnt AS [المبلغ], pdate AS [التاريخ],det as الملاحظات FROM Vst_disc_other  order by 1"
        dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
        dr.Close()
        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(4).Value)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(4).Value = SM
        Next
        DataGridView1.Columns(0).Visible = False
        DataGridView1.Columns(4).Width = 150

        Dim SM2 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM2 = SM2 + DataGridView1.Rows(i).Cells(3).Value
        Next
        Label6.Text = "جملة الخصومات الاخرى السابقة لكل العملاء = " & SM2

    End Sub

    Private Sub ComboBox1_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ComboBox1.SelectedIndexChanged
        Label5.Text = "جملة الخصومات لهذا العميل = " & IM.VnDiscF(ComboBox1.Text.Trim)

    End Sub

    Private Sub amnt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles amnt.TextChanged
        If Not IsNumeric(amnt.Text) Then
            System.Windows.Forms.SendKeys.Send("{backspace}")
        End If
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If amnt.Text = "" Then
            MsgBox("من فضلك ادخل المبلغ", MsgBoxStyle.Critical)
            Exit Sub
        End If
        If ComboBox1.Text = "" Then
            MsgBox("من فضلك اختر اسم العميل", MsgBoxStyle.Critical)
            Exit Sub
        End If
        Dim XNO As String = "خصومات أخرى"

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Vst_disc_other(Company_Branch_ID,Vendorname,DiscStatement,amnt,pdate,VND_XTM,det,TIN_NO,UserName,Treasury_Code) values (N'" & Company_Branch_ID & "',N'" & ComboBox1.Text.Trim & "',N'" & txtDiscStatement.Text.Trim & "'," & Val(amnt.Text) & ",N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Cls.get_time(True) & "',N'" & det.Text & "',N'" & XNO & "',N'" & UserName & "',N'" & Treasury_Code & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()
        IM.CustomerAccountTotal(ComboBox1.Text)

        '===============================================================================
        Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
        If UseAccounts = "YES" Then
            Daily_Restrictions()
        End If
        '===============================================================================

        Get_Movement_In_Out_Money(DateTimePicker1.Text, Treasury_Code)

        Dim CurrentBalanceCustVnd As String = GetCurrentBalanceCustVnd(ComboBox1.Text)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Vst_disc_other set CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where TIN_NO =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()


        amnt.Text = ""
        ComboBox1.SelectedIndex = -1
        det.Text = ""
        GetData()
        MsgBox("تم تسجيل الخصم بنجاح", MsgBoxStyle.Information)
    End Sub

    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If

        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim ItmID As String
        ItmID = DataGridView1.SelectedRows(0).Cells(0).Value

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete From  Vst_disc_other where id =N'" & ItmID & "'" : cmd.ExecuteNonQuery()

        cmd.CommandText = "delete From  MOVES where bill_no =N'" & ItmID & "' and MOVStatement =N'خصومات اخرى'" : cmd.ExecuteNonQuery()
        cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
        cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'خصومات اخرى'" : cmd.ExecuteNonQuery()

        GetData()
    End Sub

    Private Sub Daily_Restrictions()
        Dim Account As String = ""
        Dim AccountCode As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'خصومات اخرى'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            Account = dr("Link_AccountsTree")
            AccountCode = dr("ACCNumber")
        End If

        '========================================================================================


        Dim AccountTreasury As String = "" : Dim AccountCodeTreasury As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select ACCName,ACCNumber from AccountsTree where ACCName=N'الخزينة'" : dr = cmd.ExecuteReader
        If dr.Read Then
            AccountTreasury = dr("ACCName") : AccountCodeTreasury = dr("ACCNumber")
        End If


        Dim MOVRegNumber As String = Cls.MAXRECORD("MOVES", "MOVRegNumber")
        Dim MOVID As String = Cls.MAXRECORD("MOVES", "MOVID")
        Dim bill_no As String = Cls.MAXRECORD("Vst_disc_other", "id") - 1


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
        S = S & "N'" & bill_no & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & Account & "',N'" & amnt.Text & "',N'" & amnt.Text & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        ' من حساب / خصومات اخرى
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
        S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCode & "',N'" & Account & "',N'" & amnt.Text & "',N'0',N'" & Account & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        ' الى حساب / الخزينة
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
        S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(DateTimePicker1.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'0',N'" & amnt.Text & "',N'" & AccountTreasury & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

    End Sub

End Class