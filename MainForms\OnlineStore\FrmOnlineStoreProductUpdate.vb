﻿Imports System.Data.SqlClient

Public Class FrmOnlineStoreProductUpdate
    Private Sub Form2_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Location = New Point(CInt((Screen.PrimaryScreen.WorkingArea.Width / 2) - (Me.Width / 2)), CInt((Screen.PrimaryScreen.WorkingArea.Height / 2) - (Me.Height / 2)))
        Button1.Left = CInt((Me.ClientRectangle.Width / 2) - (Button1.Width / 2))
        PictureBox1.Size = My.Resources.Loading.Size
        PictureBox1.Image = My.Resources.Loading
        PictureBox1.Left = CInt((Me.ClientRectangle.Width / 2) - (PictureBox1.Width / 2))
        PictureBox1.Visible = False
        BackgroundWorker1.WorkerSupportsCancellation = True
        Button1_Click(sender, e)
    End Sub

    Private Sub BackgroundWorker1_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundWorker1.DoWork

        'Dim sw As New Stopwatch
        'sw.Start()
        ''------------------------------
        Dim CountLoop As Long

        If ConnectOnlineStore = "YES" Then
            If CheckForInternetConnection() = False Then
                MsgBox("تأكد من الاتصال بالانترنت", MsgBoxStyle.Information)
                Exit Sub
            End If
            Dim aray_itm_id As New ArrayList
            Dim aray_sname As New ArrayList
            Dim aray_Price As New ArrayList
            Dim aray_Price2 As New ArrayList
            Dim aray_Price3 As New ArrayList
            Dim aray_DiscountedPrice As New ArrayList
            Dim aray_DiscountedPrice2 As New ArrayList
            Dim aray_DiscountedPrice3 As New ArrayList
            Dim aray_store As New ArrayList
            Dim aray_Description As New ArrayList
            Dim aray_Tag As New ArrayList
            Dim aray_LimitQuantity As New ArrayList
            Dim aray_group_name As New ArrayList
            Dim aray_CompaniesName As New ArrayList

            Dim itm_id As String
            Dim sname As String
            Dim Price As String
            Dim Price2 As String
            Dim Price3 As String
            Dim DiscountedPrice As String
            Dim DiscountedPrice2 As String
            Dim DiscountedPrice3 As String
            Dim store As String
            Dim Description As String
            Dim Tag As String
            Dim LimitQuantity As String
            Dim group_name As String
            Dim CompaniesName As String

            connect()
            aray_itm_id.Clear() : aray_sname.Clear() : aray_Price.Clear() : aray_Price2.Clear() : aray_Price3.Clear() : aray_DiscountedPrice.Clear() : aray_DiscountedPrice2.Clear() : aray_DiscountedPrice.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select itm_id,sname,SalPrice,WholePrice,WholeWholePrice,DiscountedPrice,DiscountedPrice2,DiscountedPrice3,store,Description,Tag,LimitQuantity,group_name,CompaniesName from Items"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                aray_itm_id.Add(dr(0))
                aray_sname.Add(dr(1))
                aray_Price.Add(dr(2))
                aray_Price2.Add(dr(3))
                aray_Price3.Add(dr(4))
                aray_DiscountedPrice.Add(dr(5))
                aray_DiscountedPrice2.Add(dr(6))
                aray_DiscountedPrice3.Add(dr(7))
                aray_store.Add(dr(8))
                aray_Description.Add(dr(9))
                aray_Tag.Add(dr(10))
                aray_LimitQuantity.Add(dr(11))
                aray_group_name.Add(dr(12))
                aray_CompaniesName.Add(dr(13))
            Loop

            If Not ConnectingOnlineStore() Is Nothing Then
                For i As Integer = 0 To aray_itm_id.Count - 1
                    'Price = "0"
                    'Price2 = "0"
                    itm_id = aray_itm_id(i).ToString()
                    sname = aray_sname(i).ToString()
                    Price = aray_Price(i).ToString()
                    Price2 = aray_Price2(i).ToString()
                    Price3 = aray_Price3(i).ToString()
                    DiscountedPrice = aray_DiscountedPrice(i).ToString()
                    DiscountedPrice2 = aray_DiscountedPrice2(i).ToString()
                    DiscountedPrice3 = aray_DiscountedPrice3(i).ToString()
                    store = aray_store(i).ToString()
                    Description = aray_Description(i).ToString()
                    Tag = aray_Tag(i).ToString()
                    LimitQuantity = aray_LimitQuantity(i).ToString()
                    group_name = aray_group_name(i).ToString()
                    CompaniesName = aray_CompaniesName(i).ToString()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    S = "select count(*) from Product where  Code =N'" & itm_id & "'"
                    cmd.CommandText = S : H = cmd.ExecuteScalar
                    If H = 0 Then
                        Cos.AddProduct(sname, itm_id, itm_id, Description, group_name, CompaniesName, Price, DiscountedPrice, Price2, DiscountedPrice2, Price3, DiscountedPrice3, store, LimitQuantity)
                        ConnectingOnlineStore()
                    End If

                    Cos.UpdateProductPriceStock2(store, Price, Price2, Price3, DiscountedPrice, DiscountedPrice2, DiscountedPrice3, itm_id, sname)
                Next
                Cn.Close()
                connect()

                MsgBox("تم تحديث البيانات بنجاح", MsgBoxStyle.Information)
            Else
                Cn.Close()
                connect()
                MsgBox(Cls_Constant.ErrMsg)
            End If

        End If

        ''------------------------------
        'sw.Stop()
        'MsgBox(sw.Elapsed.ToString)

        Me.Close()
    End Sub

    Private Sub BackgroundWorker1_ProgressChanged(sender As Object, e As System.ComponentModel.ProgressChangedEventArgs) Handles BackgroundWorker1.ProgressChanged
        'ProgressBar1.Value = e.ProgressPercentage
    End Sub

    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
        'MsgBox("You Don App ......", MsgBoxStyle.Information)
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Button1.Refresh()
        PictureBox1.Visible = True
        PictureBox1.Refresh()
        BackgroundWorker1.RunWorkerAsync()
        Control.CheckForIllegalCrossThreadCalls = False
    End Sub
End Class