﻿Imports System.Data.SqlClient
Public Class Cls_Users

    Function connect() As SqlConnection
        Dim Cn As New SqlConnection()
        NetworkName = mykey.GetValue("NetworkName", "LocalDB")

        Dim CnState As Boolean = False
        If NetworkName = "No" Then
            constring = "Data Source=" + ComputerName + ";Initial Catalog=" + DataBaseName + ";Integrated Security=True"
        End If
        If NetworkName = "Yes" Then
            constring = "Data Source=" + ComputerName + ";Initial Catalog=" + DataBaseName + ";Password=" + PasswordServer + ";Persist Security Info=True;User ID=" + UserNameServer + ";Connection Timeout=200;pooling=true;max pool size=2500"
        End If
        If NetworkName = "Source" Then
            constring = "Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\DatabaseElectric.mdf;Integrated Security=True;"
        End If
        If NetworkName = "LocalDB" Then
            constring = My.Computer.FileSystem.ReadAllText(Application.StartupPath & "\ConnectionString.txt")
        End If

        Try
            Cn.ConnectionString = constring
            cmd.Connection = Cn
            If Cn.State = ConnectionState.Closed Then
                Cn.Open()
                CnState = True
                'trans = Cn.BeginTransaction
                'cmd.Transaction = trans
                Return Cn
            End If
        Catch ex As Exception
        End Try


        If CnState = False Then
            For i As Integer = 0 To aray_NetworkNameDefault.Count - 1
                NetworkName = aray_NetworkNameDefault(i).ToString

                If NetworkName = "No" Then
                    constring = "Data Source=" + ComputerName + ";Initial Catalog=" + DataBaseName + ";Integrated Security=True"
                End If
                If NetworkName = "Yes" Then
                    constring = "Data Source=" + ComputerName + ";Initial Catalog=" + DataBaseName + ";Password=" + PasswordServer + ";Persist Security Info=True;User ID=" + UserNameServer + ";Connection Timeout=200;pooling=true;max pool size=2500"
                End If
                If NetworkName = "Source" Then
                    constring = "Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\DatabaseElectric.mdf;Integrated Security=True;"
                End If
                If NetworkName = "LocalDB" Then
                    constring = My.Computer.FileSystem.ReadAllText(Application.StartupPath & "\ConnectionString.txt")
                End If

                Try
                    Cn.ConnectionString = constring
                    cmd.Connection = Cn
                    If Cn.State = ConnectionState.Closed Then
                        Cn.Open()
                        CnState = True
                        'trans = Cn.BeginTransaction
                        'cmd.Transaction = trans
                        Return Cn
                    End If
                Catch ex As Exception
                End Try
            Next
        End If

        Try
            mykey.SetValue("FITSOFTConstring", constring)
        Catch ex As Exception
        End Try

        If CnState = False Then
            MsgBox("يوجد مشكلة بالاتصال بقاعدة البيانات" & " " & msm, MsgBoxStyle.Exclamation)
            frmSettingNetwork.ShowDialog()
            Return Nothing
        End If
    End Function
    Friend Function fn_Sp_InsertUser(ByVal USerName As String, ByVal UserPassword As String)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using Insertuser As SqlCommand = connect.CreateCommand
                Insertuser.CommandType = CommandType.StoredProcedure
                Insertuser.CommandText = "Sp_InsertUsers"
                Insertuser.Parameters.AddWithValue("@UserName", USerName)
                Insertuser.Parameters.AddWithValue("@UserPassword", UserPassword)
                Insertuser.Parameters.Add("@identity", SqlDbType.Int).Direction = ParameterDirection.ReturnValue
                Insertuser.ExecuteNonQuery()
                Return Insertuser.Parameters("@identity").Value
            End Using
        Else
            MsgBox(Cls_Validation.ErrMsg)
            Return Nothing
        End If
    End Function
    Friend Function fn_Sp_InsertPermation(ByVal USerid As Integer, ByVal permtionname As String)
        If Not connect() Is Nothing Then
            Using Insertpermtion As SqlCommand = connect.CreateCommand
                Insertpermtion.CommandType = CommandType.StoredProcedure
                Insertpermtion.CommandText = "Sp_InsertPermtions"
                Insertpermtion.Parameters.AddWithValue("@UserID", USerid)
                Insertpermtion.Parameters.AddWithValue("@PermtionName", permtionname)
                Insertpermtion.ExecuteNonQuery()
            End Using
        Else
            MsgBox(Cls_Validation.ErrMsg)
            Return Nothing
        End If
    End Function
    Friend Sub Sb_Sp_FillComboUsername(ByVal cmb As ComboBox)
        If Not connect() Is Nothing Then
            Using SelectnameItem As SqlCommand = connect.CreateCommand
                SelectnameItem.CommandType = CommandType.Text
                SelectnameItem.CommandText = "Select UserID,UserName from Users where Recase=1"
                Dim Dr As SqlDataReader = SelectnameItem.ExecuteReader
                'While Dr.Read
                '    cmb.Items.Add(Dr("SupplierName"))
                'End While
                Dim Dt As New DataTable
                Dt.Load(Dr)
                cmb.DataSource = Dt
                cmb.ValueMember = Dt.Columns(0).ColumnName
                cmb.DisplayMember = Dt.Columns(1).ColumnName
                'Dt.Dispose()
                Dr.Close()
            End Using
        Else
            MsgBox(Cls_Validation.ErrMsg)
        End If
    End Sub
    Friend Function fn_Sp_SelectUser(ByVal USerID As Integer) As SqlDataReader
        If Not connect() Is Nothing Then
            Using Insertuser As SqlCommand = connect.CreateCommand
                Insertuser.CommandType = CommandType.StoredProcedure
                Insertuser.CommandText = "SpSelectUses"
                Insertuser.Parameters.AddWithValue("@USerID", USerID)
                Dim dr As SqlDataReader = Insertuser.ExecuteReader
                dr.Read()
                If dr.HasRows = False Then
                    Exit Function
                Else
                    Return dr
                End If

            End Using
        Else
            MsgBox(Cls_Validation.ErrMsg)
            Return Nothing
        End If
    End Function
    Friend Sub Sb_Sp_DeleteUsers(ByVal USerID As Integer)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using Insertuser As SqlCommand = connect.CreateCommand
                Insertuser.CommandType = CommandType.StoredProcedure
                Insertuser.CommandText = "Sp_DeleteUsers"
                Insertuser.Parameters.AddWithValue("@USerID", USerID)
                Insertuser.ExecuteNonQuery()
            End Using
        Else
            MsgBox(Cls_Validation.ErrMsg)
        End If
    End Sub
    Friend Function fn_Sp_Selectpermtion(ByVal USerID As Integer, ByVal frm As Form)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using Insertuser As SqlCommand = connect.CreateCommand
                Insertuser.CommandType = CommandType.StoredProcedure
                Insertuser.CommandText = "SpSelectPermation"
                Insertuser.Parameters.AddWithValue("@USerID", USerID)
                Dim dr As SqlDataReader = Insertuser.ExecuteReader
                Dim ctl As Control
                Dim ctl1 As Control
                Do While dr.Read
                    For Each ctl In frm.Controls
                        If TypeOf ctl Is GroupBox Then
                            For Each ctl1 In ctl.Controls
                                If TypeOf ctl1 Is CheckBox Then
                                    If ctl1.Text = dr(0) Then
                                        CType(ctl1, CheckBox).Checked = True
                                    End If
                                End If
                            Next
                        End If
                    Next
                Loop
            End Using
        Else
            MsgBox(Cls_Validation.ErrMsg)
            Return Nothing
        End If
    End Function
    Friend Function Fn_Sp_SelectA_C(ByVal Activename As String) As Boolean
        On Error Resume Next
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using cmd As SqlCommand = connect.CreateCommand
                cmd.CommandType = CommandType.StoredProcedure
                cmd.CommandText = "Sp_SelectA_C"
                cmd.Parameters.AddWithValue("@Activename", Activename)
                Dim dr As SqlDataReader = cmd.ExecuteReader
                dr.Read()
                If dr.HasRows = False Then
                    Return False
                Else
                    Return True
                End If

            End Using

        Else
            'MsgBox("من فضلك راجع الدعم الفنى لوجود مشكله فى الاتصال بقاعدة البيانات يرجى الاتصال على رقم 01004052561 ", MsgBoxStyle.Exclamation)
        End If

    End Function
    Friend Sub Sb_Sp_InsertA_C(ByVal Activename As String)
        If Not connect() Is Nothing Then
            'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            'Using cmddelete As SqlCommand = connect.CreateCommand
            '    cmddelete.CommandType = CommandType.Text
            '    cmddelete.CommandText = "delete from A_C"
            '    cmddelete.ExecuteNonQuery()
            'End Using
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using cmd As SqlCommand = connect.CreateCommand
                cmd.CommandType = CommandType.StoredProcedure
                cmd.CommandText = "Sp_InsertA_C"
                cmd.Parameters.AddWithValue("@activename", Activename)
                cmd.ExecuteNonQuery()
            End Using
        Else
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Sub
    Friend Function Sp_SelectCountOnFastALL(ByVal Table As String, ByVal Number As String)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using cmd As SqlCommand = connect.CreateCommand
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "Select Count(" & Number & ") from " & Table & ""
                Return cmd.ExecuteScalar
            End Using
        Else
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Function

    Friend Function Sp_SelectCountOnFastBill()
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using cmd As SqlCommand = connect.CreateCommand
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "Select Count(bill_No) from Sales_Bill"
                Return cmd.ExecuteScalar
            End Using
        Else
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Function
    Friend Function Sp_SelectCountOnFastItems()
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using cmd As SqlCommand = connect.CreateCommand
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "Select Count(itm_id) from Items"
                Return cmd.ExecuteScalar
            End Using
        Else
            MsgBox(Cls_Constant.ErrMsg)
        End If
    End Function

    Sub testper(ByVal userID As Integer)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using Insertuser As SqlCommand = connect.CreateCommand
                Insertuser.CommandType = CommandType.StoredProcedure
                Insertuser.CommandText = "SpSelectPermation"
                Insertuser.Parameters.AddWithValue("@USerID", userID)
                Dim dr As SqlDataReader = Insertuser.ExecuteReader
                'dr.Read()
                ' Dim ctl As Object
                Do While dr.Read
                    For Each ctl1 As ToolStripMenuItem In MDIParent1.MenuStrip.Items
                        For Each ct As ToolStripMenuItem In ctl1.DropDownItems
                            If ct.Text.Equals(dr(0)) Then
                                ct.Enabled = True
                            End If
                            If dr(0) = "مدير" Then
                                ct.Enabled = True
                            End If
                        Next
                    Next
                Loop
            End Using
        Else
            MsgBox(Cls_Constant.ErrMsg)

        End If
    End Sub
    Friend Function fn_Sp_Selectpermtiontoenter(ByVal USerID As Integer, ByVal frm As Form)
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using Insertuser As SqlCommand = connect.CreateCommand
                Insertuser.CommandType = CommandType.StoredProcedure
                Insertuser.CommandText = "SpSelectPermation"
                Insertuser.Parameters.AddWithValue("@USerID", USerID)
                Dim dr As SqlDataReader = Insertuser.ExecuteReader
                'dr.Read()
                Dim ctl As Object
                Do While dr.Read
                    For Each ctl In frm.Controls
                        If TypeOf ctl Is MenuStrip Then
                            For Each ctl1 As ToolStripMenuItem In CType(ctl, MenuStrip).Items
                                If TypeOf ctl1 Is ToolStripMenuItem Then
                                    Dim n As String = dr(0)
                                    If ctl1.Text = dr(0) Then
                                        CType(ctl1, ToolStripMenuItem).Enabled = True
                                    End If
                                    If dr(0) = "مدير" Then
                                        CType(ctl1, ToolStripMenuItem).Enabled = True
                                    End If
                                End If
                            Next
                        End If
                    Next
                Loop
            End Using
        Else
            MsgBox(Cls_Validation.ErrMsg)
            Return Nothing
        End If
    End Function
    Friend Function fn_Sp_SelectUsertoenter(ByVal USername As String, ByVal userpassword As String) As Boolean
        If Not connect() Is Nothing Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Using Insertuser As SqlCommand = connect.CreateCommand
                Insertuser.CommandType = CommandType.Text
                Insertuser.CommandText = "Select * from users where username=N'" + USername + "' and userpassword=N'" + userpassword + "'"
                Dim dr As SqlDataReader = Insertuser.ExecuteReader
                dr.Read()
                If dr.HasRows = False Then
                    Return False
                Else
                    UserIDIdentity = dr(0)
                    Return True
                End If
            End Using
            'connect.Close()
        Else
            MsgBox(Cls_Validation.ErrMsg)
            Return Nothing
        End If
    End Function

End Class
