﻿Imports vb = Microsoft.VisualBasic
Imports System.Data.OleDb
Imports CrystalDecisions.CrystalReports.Engine

Public Class FrmItemsTransfer
    Dim ListBoxSelectedIndex As Integer
    Dim QuntAll As Double
    Dim WithEvents BS As New BindingSource
    Dim IDTMFrom, IDTMTO As Integer
    Dim AlaertParcode As Boolean
    Dim itm_id As String = ""
    Dim itm_cat As String = ""
    Dim itm_name As String = ""
    Dim Unity As String = ""
    Dim qunt As String = ""
    Dim qunt_unity As String = ""
    Dim quntTotal As String = ""
    Dim Vendorname As String = ""
    Dim StoresFrom As String = ""
    Dim StoresTo As String = ""
    Dim QuntFrom As Double
    'Dim ReturnStore As Double = 0

    Private Sub Headerx()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If PermtionName = "مدير" Then
            S = "select itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الصنف],TinPrice as [سعر الشراء],SalPrice as [سعر البيع],rng as [الحد الأدنى] ,store as [المخزون],Stores as [المخزن] from items order by 1"
        Else
            S = "select itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الصنف],TinPrice as [سعر الشراء],SalPrice as [سعر البيع],rng as [الحد الأدنى] ,store as [المخزون],Stores as [المخزن] from items where Company_Branch_ID =N'" & Company_Branch_ID & "' order by 1"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)
        DTGV.Columns(0).Width = 60
        DTGV.Columns(1).Width = 70
        DTGV.Columns(2).Width = 120
        DTGV.Columns(3).Width = 50
        DTGV.Columns(4).Width = 50
        DTGV.Columns(5).Width = 45
        DTGV.Columns(6).Width = 45
        DTGV.Columns(7).Width = 80

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "SELECT SUM(ValStore) FROM ITEMS" : dr = cmd.ExecuteReader : dr.Read()
        'Label3.Text = "سعر المخزون = " & dr(0)
        'dr.Close()

        'Label5.Text = "عدد الأصناف = " & DTGV.RowCount
    End Sub

    Private Sub FrmItemsNew_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Me.KeyUp
    End Sub
    Private Sub FrmItemsNew_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cls.FillComboDataAdapterQuickSearch("Items", "sname", cmbitmnm)

        Cls.fill_combo_Branch("stores", "store", cmbStoresFrom)
        Cls.fill_combo_Branch("stores", "store", cmbStoresTo)
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployees)
        'txtsearsh.Text = " بحث ...." : txtsearsh.Font = New Font(txtsearsh.Font, FontStyle.Italic)
        'Headerx()

        If Action = "Edit" Then
            FillData()
            btnsave.Text = "تعديل"
        Else
            MAXRECORD()
            cmbEmployees.Focus()
        End If

        'Dim CheckInternetConnect As Boolean = CheckForInternetConnection()
        GetDateNotBeenActivatedPrograms(dtpDateItem)
    End Sub

    Private Sub FillData()
        'Try
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select IDTM as [IDTM],ITM_ID AS [الباركود], itm_cat as [المجموعة] ,itm_name as [اسم الصنف],price as [السعر] ,qu as [الكمية1],qu_unity as [الكمية],itm_Unity as [الوحدة] ,totalprice as [الأجمالي],Stores as [أسم المخزن],UserName as [حجم الوحدة],bill_EndDate as [تاريخ الصلاحية],bill_no_Expired as [رقم الصلاحية],ValueVAT as [ValueVAT],RateVAT as [RateVAT],Discounts as [الخصم],DiscountsValue as [قيمة الخصم] from BillsalData where bill_no =N'" & EditItmId & "'"
            dr = cmd.ExecuteReader
        'Dgv_Add.DataSource = Cls.PopulateDataView(dr)
        'Dgv_Add.Columns(1).Visible = False


        'Dim UnitySize As String = ""
        '    For i As Integer = 0 To Dgv_Add.Rows.Count - 1
        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        cmd.CommandText = "select UnitySize_Name from View_ItemsUnitySize where itm_id=N'" & Dgv_Add.Rows(i).Cells(0).Value & "' and Unity_Name=N'" & Dgv_Add.Rows(i).Cells(6).Value & "'"
        '        dr = cmd.ExecuteReader : dr.Read()
        '        If dr.HasRows = True Then
        '            UnitySize = dr(0).ToString
        '        End If

        '        Dgv_Add.Rows(i).Cells(9).Value = UnitySize
        '    Next

        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "select bill_No,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,stat,bey,STAYING,SalesTax,Notes,EmpName,DiscountTax,bill_NoTax,ValueVAT,RateValues from Sales_Bill where bill_no =N'" & EditItmId & "'"
        '    dr = cmd.ExecuteReader : dr.Read()
        '    Dim XVendorname, Xbill_date, Xbilltime, Xtotalpricebeforedisc, Xdisc, Xtotalpriceafterdisc, Xstat, Xbey, XSTAYING, XSalesTax, XNotes, XEmpName As String
        '    Xbill_No = dr("bill_No").ToString
        '    XVendorname = dr("Vendorname").ToString
        '    Xbill_date = dr("bill_date").ToString
        '    Xbilltime = dr("billtime").ToString
        '    Xtotalpricebeforedisc = dr("totalpricebeforedisc").ToString
        '    Xdisc = dr("disc").ToString
        '    Xtotalpriceafterdisc = dr("totalpriceafterdisc").ToString
        '    Xstat = dr("stat").ToString
        '    Xbey = dr("bey").ToString
        '    XSTAYING = dr("STAYING").ToString
        '    XSalesTax = dr("SalesTax").ToString
        '    If dr(11) Is DBNull.Value Then
        '    Else
        '        XNotes = dr("Notes").ToString
        '    End If
        '    XEmpName = dr("EmpName").ToString
        '    DiscountTax = dr("DiscountTax").ToString
        '    bill_NoTax = dr("bill_NoTax").ToString
        '    txtTotalValueVAT.Text = dr("ValueVAT").ToString
        '    txtSalestax.Text = dr("RateValues").ToString

        '    If Xstat = "نقداً" Then
        '        ChkCash.Checked = True
        '    End If
        '    If Xstat = "آجل" Then
        '        ChkState.Checked = True
        '    End If
        '    If Xstat = "فيزا" Then
        '        chkVisa.Checked = True
        '    End If
        '    txtbillno.Text = Xbill_No
        '    cmbvendores.Text = XVendorname
        '    DateTimePicker1.Text = Cls.R_date(Xbill_date)
        '    txttotalpeforedisc.Text = Xtotalpricebeforedisc
        '    txttotalafterdisc.Text = Xtotalpriceafterdisc
        '    txtdisc.Text = Xdisc
        '    txtstaying.Text = XSTAYING
        '    txtSalestax.Text = XSalesTax
        '    txtNotes.Text = XNotes
        '    cmbEmployees.Text = XEmpName
        '    txtpaying.Text = Xbey


        '    aray_itm_id.Clear() : aray_Stores.Clear() : aray_qu.Clear() : aray_TinPrice.Clear()
        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "Select itm_id,Stores,qu from BillsalData where bill_no =N'" & EditItmId & "'"
        '    dr = cmd.ExecuteReader
        '    Do While dr.Read = True
        '        aray_itm_id.Add(dr(0))
        '        aray_Stores.Add(dr(1))
        '        aray_qu.Add(dr(2))
        '    Loop
        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub


    Private Sub cmbcats_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbcats.DropDown
        TxtPrc.Text = ""
        cmbcats.Text = ""
        cmbitmnm.Text = ""
        txtqunt.Text = ""
        txtquntAll.Text = ""
    End Sub

    Function ValidateSave() As Boolean

        If txtNumberTransfer.Text = "" Then MsgBox("فضلا أدخل رقم أذن التحويل", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbStoresFrom.Text = "" Then MsgBox("من فضلك اختر المخزن المحول منه", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If cmbStoresTo.Text = "" Then MsgBox("من فضلك اختر المخزن المحول البه", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If DTGV.Rows.Count < 1 Then MsgBox("فضلا أكمل بيانات الفاتورة", MsgBoxStyle.Exclamation) : Return False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from ItemsTransferData where bill_no =N'" & txtNumberTransfer.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H > 0 Then
            MsgBox("رقم أذن التحويل مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtNumberTransfer.Focus() : Return False
        End If
        Return True
    End Function

    Sub IDTMSUMFrom()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select IDTM from items where  itm_id =N'" & TxtPrc.Text.Trim & "' and Stores=N'" & cmbStoresFrom.Text & "'"
        dr = cmd.ExecuteReader
        dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            IDTMFrom = dr("IDTM")
        End If
    End Sub

    Sub IDTMSUMTO()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select IDTM from items where  itm_id =N'" & TxtPrc.Text.Trim & "' and Stores=N'" & cmbStoresTo.Text & "'"
        dr = cmd.ExecuteReader
        dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            IDTMTO = dr("IDTM")
        End If
    End Sub

    Sub IDTMSUMFromXXX(ByVal i As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select IDTM from items where  itm_id =N'" & DTGV.Rows(i).Cells(1).Value & "' and Stores=N'" & DTGV.Rows(i).Cells(7).Value & "'"
        dr = cmd.ExecuteReader
        dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            IDTMFrom = dr("IDTM")
        End If
    End Sub

    Sub IDTMSUMTOXXX(ByVal i As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select IDTM from items where  itm_id =N'" & DTGV.Rows(i).Cells(1).Value & "' and Stores=N'" & cmbStoresTo.Text & "'"
        dr = cmd.ExecuteReader
        dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            IDTMTO = dr("IDTM")
        End If
    End Sub

    Sub Clear_All()
        cmbitmnm.Text = ""
        TxtPrc.Text = ""
        txtqunt.Text = ""
        txtquntAll.Text = ""
        'cmbStores.Text = ""
        'cmbStoresTo.Text = ""
        cmbitmnm.Focus()
        DTGV.DataSource = ""

    End Sub

    Private Sub btnsave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnsave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        IDTMSUMFrom()
        If ValidateTextAdd() = False Then Exit Sub

        Dim ItmID, Indexqunt, IndexquntUnity, Store As String
        For i As Integer = 0 To DTGV.Rows.Count - 1
            If DTGV.RowCount = 0 Then Beep() : Exit Sub
            ItmID = DTGV.Rows(i).Cells(1).Value
            Store = DTGV.Rows(i).Cells(8).Value
            If Store = cmbStoresFrom.Text Then
                If TxtPrc.Text = ItmID Then
                    Indexqunt = DTGV.Rows(i).Cells(5).Value
                    IndexquntUnity = DTGV.Rows(i).Cells(6).Value
                    RNXD = DTGV.Rows(i).Cells(0).RowIndex
                    DTGV.Rows.RemoveAt(RNXD)

                    Dim XTotalqu As String = Val(txtqunt.Text) + Val(Indexqunt)
                    Dim XXTotalquUnity As String = Val(txtquntUnity.Text) + Val(IndexquntUnity)

                    DTGV.DataSource = Fn_AddBill(IDTMFrom, TxtPrc.Text, cmbcats.Text, cmbitmnm.Text, cmbUnity.Text, XTotalqu, XXTotalquUnity, txtquntAll.Text, cmbStoresFrom.Text, cmbStoresTo.Text)

                    GoTo 2
                End If
            End If

        Next

        DTGV.DataSource = Fn_AddBill(IDTMFrom, TxtPrc.Text, cmbcats.Text, cmbitmnm.Text, cmbUnity.Text, txtqunt.Text, txtquntUnity.Text, txtquntAll.Text, cmbStoresFrom.Text, cmbStoresTo.Text)

2:
        ClearAdd()
        TxtPrc.Focus()
        SumAllPrice()
        DTGV.Columns(0).Visible = False
        DTGV.Columns(5).Visible = False

    End Sub

    Private Sub SumAllPrice()
        Dim SM As Double
        For i As Integer = 0 To DTGV.Rows.Count - 1
            SM = SM + DTGV.Rows(i).Cells(6).Value
        Next
        txttotalpeforedisc.Text = SM
    End Sub

    Dim Dt_AddBill As New DataTable
    Friend Function Fn_AddBill(ByVal Col_IDTM As String, ByVal Col_Prc As String, ByVal Col_Cats As String, ByVal Col_Name As String, ByVal Col_Unity As String _
, ByVal Col_Quant As Double, ByVal Col_qu_unity As Double, ByVal Col_QuantRemainder As Double, ByVal Col_StoreFrom As String, ByVal Col_StoreTo As String) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("IDTM", GetType(String))
            Dt_AddBill.Columns.Add("الباركود", GetType(String))
            Dt_AddBill.Columns.Add("المجموعة", GetType(String))
            Dt_AddBill.Columns.Add("أسم الصنف", GetType(String))
            Dt_AddBill.Columns.Add("الوحدة", GetType(String))
            Dt_AddBill.Columns.Add("1الكمية", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية الباقية", GetType(Double))
            Dt_AddBill.Columns.Add("من مخزن", GetType(String))
            Dt_AddBill.Columns.Add("الى مخزن", GetType(String))
        End If

        'DTV_Width()

        Dt_AddBill.Rows.Add(Col_IDTM, Col_Prc, Col_Cats, Col_Name, Col_Unity, Col_Quant, Col_qu_unity, Col_QuantRemainder, Col_StoreFrom, Col_StoreTo)
        Return Dt_AddBill
    End Function

    Private Sub ClearAdd()
        cmbUnity.Text = ""
        cmbcats.Text = ""
        cmbitmnm.Text = ""
        txtqunt.Text = ""
        txtquntAll.Text = ""
        txtquntUnity.Text = ""
        TxtPrc.Text = ""
        cmbUnity.Text = ""
    End Sub

    Function ValidateTextAdd() As Boolean
        If txtNumberTransfer.Text = "" Then MsgBox("فضلا أدخل رقم أذن التحويل", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbcats.Text = "" Then MsgBox("فضلا أدخل مجموعة الصنف", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbitmnm.Text = "" Then MsgBox("فضلا أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbitmnm.Focus() : Return False
        If cmbStoresFrom.Text = "" Then MsgBox("من فضلك اختر المخزن المحول منه", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If cmbStoresTo.Text = "" Then MsgBox("من فضلك اختر المخزن المحول البه", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If txtquntUnity.Text = "" Or txtquntUnity.Text = "0" Then MsgBox("فضلا أدخل الكمية ", MsgBoxStyle.Exclamation) : txtquntUnity.Focus() : Return False
        If TxtPrc.Text = "" Then MsgBox("فضلا أدخل باركود الصنف", MsgBoxStyle.Exclamation) : Return False

        Dim XMSG As String
        IDTMSUMFrom()

        Dim Xstore As Double = IM.Get_Itm_Store(TxtPrc.Text, cmbStoresFrom.Text)
        If cmbStoresFrom.Text = cmbStoresTo.Text Then
            MsgBox("لايمكن التحويل من المخزن الى نفسة اختار مخزن اخر", MsgBoxStyle.Critical)
            Return False
        End If
        Dim IncreaseQuantity As String = mykey.GetValue("IncreaseQuantity", "NO")
        If IncreaseQuantity <> "YES" Then
            If Val(txtqunt.Text) > Val(Xstore) Then
                MsgBox("الكمية بالمخزن لا تكفي الكمية التى سيتم نقلها من المخزن", MsgBoxStyle.Critical)
                Return False
            End If
        End If


        If Xstore < 0 Then
            XMSG = MsgBox("الكمية بالمخزن قد نفذت أو أنك لم تقم بتسجيل آخر عملية مشتريات " & Environment.NewLine & MsgBoxStyle.MsgBoxRight + MsgBoxStyle.Exclamation)
            Exit Function
        End If

        If txtquntAll.Text < 0 Then
            MsgBox("لايمكن التحويل من المخزن لان الكمية لا تكفى لتحويل القيمة المسموح به", MsgBoxStyle.Critical)
            Return False
        End If

        'Dim StausMainFrom As String = ""
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select StausMainStore from stores where  store = N'" & cmbStoresFrom.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        'If dr.HasRows = True Then
        '    StausMainFrom = dr("StausMainStore")
        'End If

        'Dim StausMainTO As String = ""
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select StausMainStore from stores where  store = N'" & cmbStoresTo.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        'If dr.HasRows = True Then
        '    StausMainTO = dr("StausMainStore")
        'End If

        'If StausMainFrom = StausMainTO Then
        '    MsgBox("لايمكن التحويل من مخزن فرعى الى مخزن فرعى أخر", MsgBoxStyle.Critical)
        '    Return False
        'End If


        Return True
    End Function
    Function SumListCombo(ByVal X As ListBox)
        Dim SM As Integer
        For i As Integer = 0 To X.Items.Count - 1
            SM = SM + Val(X.Items(i))
        Next
        Return SM
    End Function


    Function ValidatAdd() As Boolean
        If cmbcats.Text.Trim = "" Then MsgBox("أختر مجموعة الصنف", MsgBoxStyle.Exclamation) : cmbcats.Focus() : Return False
        If cmbitmnm.Text.Trim = "" Then MsgBox("أدخل اسم الصنف", MsgBoxStyle.Exclamation) : cmbitmnm.Focus() : Return False
        If Val(txtqunt.Text) > 0 Then
            'If Val(txttinprice.Text) = 0 Then
            '    MsgBox("فضلا أدخل سعر شراء الوحدة", MsgBoxStyle.Exclamation) : txttinprice.Focus() : Return False
            'End If
        End If

        Return True
    End Function

    Private Sub txtsearsh_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles txtsearsh.MouseClick
        If txtsearsh.Text = " بحث ...." Then txtsearsh.SelectAll() : txtsearsh.Font = New Font(txtsearsh.Font, FontStyle.Regular)
    End Sub

    Private Sub TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtsearsh.TextChanged
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
            If PermtionName = "مدير" Then
                S = "select itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الاسم],TinPrice as [سعر الشراء],SalPrice as [سعر البيع],rng as [الحد الأدنى] ,store as [المخزون],Stores as [المخزن] from items"
            Else
                S = "select itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الاسم],TinPrice as [سعر الشراء],SalPrice as [سعر البيع],rng as [الحد الأدنى] ,store as [المخزون],Stores as [المخزن] from items where Company_Branch_ID =N'" & Company_Branch_ID & "'"
            End If
        Else
            Dim X As String = "%" & txtsearsh.Text.Trim & "%"
            If PermtionName = "مدير" Then
                S = "select itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الاسم],TinPrice as [سعر الشراء],SalPrice as [سعر البيع],rng as [الحد الأدنى] ,store as [المخزون],Stores as [المخزن] from items where  sname like N'" & X & "'"
            Else
                S = "select itm_id  as [الباركود], group_name as [المجموعة] ,sname as [الاسم],TinPrice as [سعر الشراء],SalPrice as [سعر البيع],rng as [الحد الأدنى] ,store as [المخزون],Stores as [المخزن] from items where  sname like N'" & X & "' and Company_Branch_ID =N'" & Company_Branch_ID & "'"
            End If
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)
        DTGV.Columns(0).Width = 60
        DTGV.Columns(1).Width = 70
        DTGV.Columns(2).Width = 120
        DTGV.Columns(3).Width = 50
        DTGV.Columns(4).Width = 50
        DTGV.Columns(5).Width = 45
        DTGV.Columns(6).Width = 45
        DTGV.Columns(7).Width = 80
        'Label5.Text = "عدد الأصناف = " & DTGV.RowCount
    End Sub

    Private Sub txtqunt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtqunt.TextChanged
        If Not IsNumeric(txtqunt.Text) Then
            System.Windows.Forms.SendKeys.Send("{backspace}")
        End If

        GetItemsUnityTotalCarton()
    End Sub

    Private Sub txtrng_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs)
        On Error Resume Next
        If e.KeyCode = 13 Then
            TxtPrc.Focus()
            ' 
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from items where group_name =N'" & cmbcats.Text.Trim & "'" : H = cmd.ExecuteScalar
            Dim X As String
            Dim XH As String
            XH = H
            If Len(XH) = 1 Then
                X = "0" & H
            ElseIf Len(XH) = 2 Then
                X = H
            End If
            cmd.CommandText = "select id from Groups where G_name =N'" & cmbcats.Text.Trim & "'"
            dr = cmd.ExecuteReader : dr.Read()

            'If Len(txttinprice.Text) = 1 Then
            '    TxtPrc.Text = dr(0) & "0" & Int(txttinprice.Text.Trim) & X
            'ElseIf Len(txttinprice.Text) = 2 Then
            '    TxtPrc.Text = dr(0) & Int(txttinprice.Text.Trim) & X
            'ElseIf Len(txttinprice.Text) = 3 Then
            '    TxtPrc.Text = dr(0) & "00" & X
            'ElseIf Len(txttinprice.Text) = 4 Then
            '    TxtPrc.Text = dr(0) & "0" & Int(txttinprice.Text.Trim) & X
            'End If
        End If
        TxtPrc.Text = vb.Right(Clss.GenerateItmId_Or_Parcode(), 8)
        TxtPrc.Focus()
        TxtPrc.SelectAll()
    End Sub

    Private Sub cmbcats_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbcats.SelectedIndexChanged
        'If cmbcats.Text.Trim = "" Then Exit Sub
        'cmbitmnm.Items.Clear()
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select sname from Items where group_name =N'" & cmbcats.Text & "' order by 1"
        'dr = cmd.ExecuteReader
        'Do While dr.Read
        '    cmbitmnm.Items.Add(Trim(dr(0)))
        'Loop
        'cmbitmnm.Text = ""
    End Sub

    Private Sub BtnAddCat_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        FrmCats.Show()
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        TxtPrc.Text = vb.Right(Clss.GenerateItmId_Or_Parcode(), 8)
    End Sub

    Private Sub cmbitmnm_DropDown(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbitmnm.DropDown
        TxtPrc.Text = ""
        cmbitmnm.Text = ""
        cmbcats.Text = ""
        txtqunt.Text = ""
        txtquntAll.Text = ""
    End Sub

    Private Sub cmbitmnm_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbitmnm.KeyUp
        If e.KeyCode = 13 Then
            If AlaertParcode = False Then
                GetDataSales()
            End If
        End If
    End Sub

    Private Sub GetDataSales()
        Bol = True
        QuntFrom = 0
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id,group_name,Unity,store from items where sname=N'" & cmbitmnm.Text & "' and Stores=N'" & cmbStoresFrom.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                TxtPrc.Text = dr("itm_id").ToString()
                cmbcats.Text = dr("group_name").ToString()
                cmbUnity.Text = dr("Unity").ToString()
                QuntAll = Val(dr("store").ToString())
            End If
        Catch ex As Exception
        End Try
        '==========================================================
        QuntFrom = 0
        For i As Integer = 0 To DTGV.Rows.Count - 1
            If DTGV.Rows(i).Cells(8).Value = cmbStoresFrom.Text Then
                If DTGV.Rows(i).Cells(1).Value = TxtPrc.Text Then
                    QuntFrom += DTGV.Rows(i).Cells(6).Value
                End If
            End If
        Next

        If DTGV.Rows.Count = 0 Then
            txtquntAll.Text = QuntAll - Val(txtquntUnity.Text)
        Else
            txtquntAll.Text = QuntAll - QuntFrom
        End If

        GetItemsUnity(cmbUnity, TxtPrc.Text)

        txtqunt.Text = 0
        txtquntUnity.Text = 0
        txtquntUnity.Focus()
        txtquntUnity.SelectAll()

        GetItemsUnityTotalCarton()

        Bol = False


    End Sub

    Private Sub TxtPrc_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles TxtPrc.KeyDown
        If ((e.KeyCode = Keys.S) AndAlso (e.Modifiers = (Keys.Control))) Then
            btnSaveAll.PerformClick()
        End If
    End Sub

    Private Sub TxtPrc_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles TxtPrc.KeyUp
        If e.KeyCode = 13 Then
            AlaertParcode = True
            If TxtPrc.Text.Trim = "" Then
            Else

                Bol = True
                Try
1:
                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "select sname,group_name,Unity,store from items where itm_id=N'" & TxtPrc.Text & "' and Stores=N'" & cmbStoresFrom.Text & "'"
                    dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then
                        cmbitmnm.Text = dr("sname")
                        cmbcats.Text = dr("group_name")
                        cmbUnity.Text = dr("Unity")
                        QuntAll = Convert.ToDouble(dr("store").ToString)
                    End If
                Catch ex As Exception
                    GoTo 1
                End Try

                '==========================================================
                QuntFrom = 0
                For i As Integer = 0 To DTGV.Rows.Count - 1
                    If DTGV.Rows(i).Cells(8).Value = cmbStoresFrom.Text Then
                        If DTGV.Rows(i).Cells(1).Value = TxtPrc.Text Then
                            QuntFrom += DTGV.Rows(i).Cells(6).Value
                        End If
                    End If
                Next

                If DTGV.Rows.Count = 0 Then
                    txtquntAll.Text = QuntAll - Val(txtquntUnity.Text)
                Else
                    txtquntAll.Text = QuntAll - QuntFrom
                End If


                GetItemsUnity(cmbUnity, TxtPrc.Text)

                txtqunt.Text = 0
                txtquntUnity.Text = 0
                txtquntUnity.Focus()
                txtquntUnity.SelectAll()

                GetItemsUnityTotalCarton()

                Bol = False

            End If

            'IDTMSUMFrom()
            AlaertParcode = False
        End If
    End Sub

    Sub AddStores(ByVal Store As String)
        REM للتاكد من عدم التكرار
        Dim TableName, FieldName, StringFind As String
        Dim S As String
        TableName = "stores"
        FieldName = "store"
        StringFind = Store
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select * From " & TableName & " Where " & FieldName & "=N'" & StringFind & "'"

        cmd.CommandType = CommandType.Text
        cmd.CommandText = S
        dr = cmd.ExecuteReader
        'dr.Read()
        If dr.HasRows = False Then
            Cls.Get_Value_Count_More("Stores", "StausMainStore =N'0'")
            Dim StausMainStore As Integer
            If H = 0 Then
                StausMainStore = 0
            Else
                StausMainStore = 1
            End If
            REM لحفظ المخزن
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Stores(Company_Branch_ID,store,UserName,StausMainStore) values (N'" & Company_Branch_ID & "',N'" & StringFind & "',N'" & UserName & "',N'" & StausMainStore & "')"
            cmd.CommandText = S
            cmd.ExecuteNonQuery()
        End If
    End Sub
    Sub AddGroubs(ByVal Cats As String)
        REM للتاكد من عدم التكرار
        Dim TableName, FieldName, StringFind As String
        Dim S As String
        TableName = "groups"
        FieldName = "g_name"
        StringFind = Cats
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select * From " & TableName & " Where " & FieldName & "=N'" & StringFind & "'"

        cmd.CommandType = CommandType.Text
        cmd.CommandText = S
        dr = cmd.ExecuteReader
        'dr.Read()
        If dr.HasRows = False Then
            dr.Close()
            REM لحفظ المجموعه
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into groups(Company_Branch_ID,g_name,UserName) values (N'" & Company_Branch_ID & "',N'" & StringFind & "',N'" & UserName & "')"
            cmd.CommandText = S
            cmd.ExecuteNonQuery()
        End If

    End Sub

    Private Sub cmbUnity_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbUnity.KeyUp
        If e.KeyCode = 13 Then
            cmbStoresFrom.Focus()
        End If
    End Sub

    Private Sub cmbStores_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbStoresFrom.KeyUp
        If e.KeyCode = 13 Then
            cmbStoresTo.Focus()
        End If
    End Sub

    Private Sub cmbStoresTo_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbStoresTo.KeyUp
        If e.KeyCode = 13 Then
            txtNotes.Focus()
        End If
    End Sub

    Private Sub GetDataStores()
        Bol = True
        TxtPrc.Text = Cls.Get_Code_Value_Stores_More("items", "itm_id", "sname=N'" & cmbitmnm.Text.Trim & "' and Stores =N'" & cmbStoresTo.Text & "'")

        TxtPrc.Focus()
        TxtPrc.SelectAll()
        Bol = False
    End Sub

    Private Sub txtNumberTransfer_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtNumberTransfer.TextChanged
        If Not IsNumeric(txtNumberTransfer.Text) Then
            System.Windows.Forms.SendKeys.Send("{backspace}")
        End If
    End Sub

    Private Sub MAXRECORD()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from ItemsTransfer_BillsalData"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Me.txtNumberTransfer.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(bill_no As float)) as mb FROM ItemsTransfer_BillsalData where bill_no <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            Me.txtNumberTransfer.Text = sh + 1
        End If
    End Sub

    Private Sub cmbStores_DropDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbStoresFrom.DropDown
        TxtPrc.Text = ""
        cmbcats.Text = ""
        cmbitmnm.Text = ""
        txtqunt.Text = ""
        txtquntUnity.Text = ""
        txtquntAll.Text = ""
        cmbUnity.Text = ""
    End Sub

    Private Sub cmbStoresTo_DropDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbStoresTo.DropDown
        TxtPrc.Text = ""
        cmbcats.Text = ""
        cmbitmnm.Text = ""
        txtqunt.Text = ""
        txtquntUnity.Text = ""
        txtquntAll.Text = ""
    End Sub

    Private Sub btnSaveAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSaveAll.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateSave() = False Then Exit Sub

        ItemsTransferBill()

        If chkprint.Checked = True Then
            PrintReport()
        End If
        Bra.Fil("groups", "g_name", cmbcats)
        Cls.fill_combo_Branch("stores", "store", cmbStoresFrom)
        'Headerx()
        Dt_AddBill.Rows.Clear()
        Clear_All()
        MAXRECORD()
        MsgBox("تمت عملية التسجيل بنجاح", MsgBoxStyle.Information)

        Cls.FillComboDataAdapterQuickSearch("Items", "sname", cmbitmnm)

    End Sub

    Private Sub PrintReport()
        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        For i As Integer = 0 To DTGV.Rows.Count - 1
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases(Company_Branch_ID,bill_no,bill_date,itm_id,itm_cat,itm_name,Unity,qu,totalpriceafterdisc,store,Stat,totalprice) values"
            S = S & " (N'" & Company_Branch_ID & "',N'" & txtNumberTransfer.Text & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & DTGV.Rows(i).Cells(1).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(3).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(5).Value & "',N'" & DTGV.Rows(i).Cells(7).Value & "',N'" & DTGV.Rows(i).Cells(8).Value & "',N'" & DTGV.Rows(i).Cells(9).Value & "',N'" & txttotalpeforedisc.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New Rpt_Items_Transfer_Data
        Dim txt, txtNameAr, txtNameEn, txtDateItem, txtCmpAddress, txtCmpEmail, txtCmpTel, txtCmpMobile, txtCommercialRecord, txtTaxCard, txtEndorsement, txtEmployees As TextObject

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txt = rpt.Section1.ReportObjects("txtReportTitel")
        txt.Text = "فاتورة أذن التحويل"
        txtNameAr = rpt.Section1.ReportObjects("txtTitelNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelNameEn")
        txtNameEn.Text = NameEnCompany
        txtDateItem = rpt.Section1.ReportObjects("txtDateItem")
        txtDateItem.Text = dtpDateItem.Text
        txtCmpAddress = rpt.Section1.ReportObjects("txtAddress")
        txtCmpAddress.Text = CmpAddress
        txtCmpEmail = rpt.Section1.ReportObjects("txtEmail")
        txtCmpEmail.Text = CmpEmail
        txtCmpTel = rpt.Section1.ReportObjects("txtTel")
        txtCmpTel.Text = CmpTel
        txtCmpMobile = rpt.Section1.ReportObjects("txtMobile")
        txtCmpMobile.Text = CmpMobile
        txtCommercialRecord = rpt.Section1.ReportObjects("txtCommercialRecord")
        txtCommercialRecord.Text = CMPCommercialRecord
        txtTaxCard = rpt.Section1.ReportObjects("txtTaxCard")
        txtTaxCard.Text = CMPTaxCard
        txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
        txtEndorsement.Text = CMPEndorsement
        txtEmployees = rpt.Section1.ReportObjects("txtEmployees")
        txtEmployees.Text = cmbEmployees.Text
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "تقرير بفاتورة أذن التحويل"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Sub ForPrintAll(ByVal BILL_NO As String, ByVal bill_date As String, ByVal StoresFrom As String, ByVal StoresTO As String, ByVal Total_qu As String,
                    ByVal itm_id As String, ByVal itm_cat As String, ByVal itm_name As String, ByVal itm_Unity As String, ByVal qu As String)


        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "SP_Print_Bill_ItemsTransfer"
        cmd.Parameters.Clear()

        cmd.Parameters.AddWithValue("@bill_no", BILL_NO)
        cmd.Parameters.AddWithValue("@bill_date", bill_date)
        cmd.Parameters.AddWithValue("@Vendorname", StoresFrom)
        cmd.Parameters.AddWithValue("@CustomerName", StoresTO)
        cmd.Parameters.AddWithValue("@totalpriceafterdisc", Total_qu)
        cmd.Parameters.AddWithValue("@itm_id", itm_id)
        cmd.Parameters.AddWithValue("@itm_cat", itm_cat)
        cmd.Parameters.AddWithValue("@itm_name", itm_name)
        cmd.Parameters.AddWithValue("@Unity", itm_Unity)
        cmd.Parameters.AddWithValue("@qu", qu)
        cmd.ExecuteNonQuery()

    End Sub

    Private Sub btnNew_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnNew.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        ClearGrid()
        Clear_All()
        MAXRECORD()
    End Sub

    Sub ClearGrid()
        Dt_AddBill.Rows.Clear() : Clear_All()
        DTGV.DataSource = ""
        Clear_All()
        'Dt_AddBill.Columns.Add("الباركود", GetType(String))
        'Dt_AddBill.Columns.Add("أسم المجموعة", GetType(String))
        'Dt_AddBill.Columns.Add("أسم الصنف", GetType(String))
        'Dt_AddBill.Columns.Add("الوحدة", GetType(String))
        'Dt_AddBill.Columns.Add("الكمية", GetType(Double))
        'Dt_AddBill.Columns.Add("الكمية الباقية", GetType(Double))
        '  Next
    End Sub

    Dim RNXD As Integer
    Private Sub BtnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDelete.Click
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        RNXD = DTGV.CurrentRow.Index
        DTGV.Rows.RemoveAt(RNXD)
    End Sub

    Private Sub btnImport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnImport.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        ImportExcel()
        SumAllPrice()
    End Sub

    Private Sub ImportExcel()
        DTGV.Columns.Clear()
        Dim MyFileDialog As New OpenFileDialog()
        'Dim Xsheet As String = txtSheetName.Text
        With MyFileDialog
            .Filter = "Excel files|*xlsx;*xls"
            .Title = "Open File"
            .ShowDialog()
        End With

        If MyFileDialog.FileName.ToString <> "" Then
            Dim ExcelFile As String = MyFileDialog.FileName.ToString
            Dim ds As New DataSet
            Dim da As OleDbDataAdapter
            Dim dt As DataTable
            Dim conn As OleDbConnection
            conn = New OleDbConnection(
                       "Provider=Microsoft.Jet.OLEDB.4.0;" &
                       "Data Source= " & ExcelFile & ";" &
                       "Extended Properties=Excel 8.0;")
            Try

                da = New OleDbDataAdapter("select * from [Items$]", conn)
                conn.Open()
                da.Fill(ds, "Items")
                dt = ds.Tables("Items")

            Catch ex As Exception
                MsgBox(ex.Message)
                conn.Close()
            End Try
            Try
                DTGV.DataSource = ds
                DTGV.DataMember = "Items"
            Catch ex As Exception
                MsgBox(ex.Message)
            End Try
        End If
    End Sub

    Private Sub txtNumberTransfer_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtNumberTransfer.KeyUp
        If e.KeyCode = 13 Then
            dtpDateItem.Focus()
        End If
    End Sub

    Private Sub dtpDateItem_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDateItem.KeyUp
        If e.KeyCode = 13 Then
            cmbStoresFrom.Focus()
        End If
    End Sub

    Private Sub MAXRECORDXXX()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select * from Items"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            IDTMTO = 1000
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(IDTM) as mb FROM Items"

            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Integer
            sh = dr("mb")
            IDTMTO = sh + 1
        End If

    End Sub

    Private Sub cmbUser_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbEmployees.KeyUp
        If e.KeyCode = 13 Then
            cmbStoresFrom.Focus()
            cmbStoresFrom.SelectAll()
        End If
    End Sub

    Private Sub cmbUnity_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbUnity.SelectedIndexChanged
        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from ItemsUnity where itm_id=N'" & TxtPrc.Text & "' and Unity_Name=N'" & cmbUnity.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : NumberPieces = dr(0).ToString : Else NumberPieces = 0 : End If
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(txtquntUnity.Text)
        End If
        GetItemsUnityTotalCarton()
    End Sub

    Private Sub txtquntUnity_TextChanged(sender As Object, e As EventArgs) Handles txtquntUnity.TextChanged
        MyVars.CheckNumber(txtquntUnity)
        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If

        QuntFrom = 0
        For i As Integer = 0 To DTGV.Rows.Count - 1
            If DTGV.Rows(i).Cells(8).Value = cmbStoresFrom.Text Then
                If DTGV.Rows(i).Cells(1).Value = TxtPrc.Text Then
                    QuntFrom += DTGV.Rows(i).Cells(6).Value
                End If
            End If
        Next

        If DTGV.Rows.Count = 0 Then
            txtquntAll.Text = QuntAll - Val(txtquntUnity.Text)
        Else
            txtquntAll.Text = QuntAll - QuntFrom - Val(txtquntUnity.Text)
        End If
    End Sub

    Private Sub cmbStoresFrom_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbStoresFrom.SelectedIndexChanged
        Cls.fill_combo_Stores_Where_More("Items", "sname", "Stores=N'" & cmbStoresFrom.Text & "' and QuickSearch=0", cmbitmnm)
    End Sub

    Private Sub txtquntUnity_KeyUp(sender As Object, e As KeyEventArgs) Handles txtquntUnity.KeyUp
        If e.KeyCode = 13 Then
            btnsave.PerformClick()
        End If
    End Sub

    Private Sub cmbitmnm_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbitmnm.SelectedIndexChanged
        If AlaertParcode = False Then
            GetDataSales()
        End If
    End Sub

    Private Sub ItemsTransferBill()
        Dim bill_no_Expired As String = ""
        Dim Xbill_no_Expired As String = ""

        Dim EMPID As String = Cls.Get_Code_Value_Branch_More("Employees", "EMPID", "NameEmployee=N'" & cmbEmployees.Text.Trim & "' and Stores =N'" & cmbStoresTo.Text & "'")
        Dim Expired As String = "" : Dim bill_EndDate As String = "" : Dim bill_ProductionDate As String = ""
        For i As Integer = 0 To DTGV.Rows.Count - 1
            StoresFrom = DTGV.Rows(i).Cells(8).Value
            StoresTo = DTGV.Rows(i).Cells(9).Value
            itm_id = DTGV.Rows(i).Cells(1).Value
            itm_cat = DTGV.Rows(i).Cells(2).Value
            itm_name = DTGV.Rows(i).Cells(3).Value
            Unity = DTGV.Rows(i).Cells(4).Value
            qunt = DTGV.Rows(i).Cells(5).Value
            qunt_unity = DTGV.Rows(i).Cells(6).Value
            quntTotal = DTGV.Rows(i).Cells(7).Value

            Expired = Cls.Get_Code_Value_Branch_More("BilltINData", "Expired", "itm_id=N'" & itm_id & "' and Stores =N'" & cmbStoresFrom.Text & "'")
            bill_EndDate = Cls.Get_Code_Value_Branch_More("BilltINData", "bill_EndDate", "itm_id=N'" & itm_id & "' and Stores =N'" & cmbStoresFrom.Text & "'")
            bill_ProductionDate = Cls.Get_Code_Value_Branch_More("BilltINData", "bill_ProductionDate", "itm_id=N'" & itm_id & "' and Stores =N'" & cmbStoresFrom.Text & "'")

            Cls.Get_Value_Count_More("Items", "sname =N'" & itm_name & "' and Stores =N'" & StoresTo & "'")
            If H = 0 Then
                AddGroubs(DTGV.Rows(i).Cells(2).Value)
                AddStores(StoresTo)
                IDTMSUMFromXXX(i)
                MAXRECORDXXX()
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "select Stores,itm_id,TinPrice,TinPriceAverage,SalPrice,WholePrice,WholeWholePrice,MinimumSalPrice,rng,group_branch,RateDiscTinPrice,RateDiscSalPrice,RateDiscWholePrice,RateDiscWholeWholePrice from items where Stores =N'" & StoresFrom & "' and itm_id =N'" & itm_id & "'" : dr = cmd.ExecuteReader : dr.Read()
                Dim store, TinPrice, TinPriceAverage, SalPrice, WholePrice, WholeWholePrice, MinimumSalPrice, rng, group_branch, RateDiscTinPrice, RateDiscSalPrice, RateDiscWholePrice, RateDiscWholeWholePrice As String
                store = dr(0).ToString() : TinPrice = dr(2).ToString() : TinPriceAverage = dr(3).ToString() : SalPrice = dr(4).ToString() : WholePrice = dr(5).ToString()
                WholeWholePrice = dr(6).ToString() : MinimumSalPrice = dr(7).ToString() : rng = dr(8).ToString() : group_branch = dr(9).ToString()
                RateDiscTinPrice = dr(10).ToString() : RateDiscSalPrice = dr(11).ToString() : RateDiscWholePrice = dr(12).ToString() : RateDiscWholeWholePrice = dr(13).ToString()
                '====================================================================================================================

                Vendorname = Cls.Get_Code_Value_More_ALL_Select("Select Vendorname From BilltINData where itm_id = N'" & itm_id & "' AND Stores = N'" & StoresFrom & "'")

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Items (Company_Branch_ID,IDTM,itm_id,group_name,group_branch,sname,Unity,rng,tinprice,salprice,TinPriceAverage,WholePrice,WholeWholePrice,MinimumSalPrice,RateDiscTinPrice,RateDiscSalPrice,RateDiscWholePrice,RateDiscWholeWholePrice,tin,sal,btin,bsal,decayed,tinpricetotal,salpricetotal,btinpricetotal,bsalpricetotal,decayedpricetotal,store,ValStore,profits,UserName,Stores,QuickSearch,Vendorname) values ("
                S = S & "N'" & Company_Branch_ID & "',N'" & IDTMTO & "',N'" & itm_id & "',N'" & itm_cat & "',N'" & group_branch & "',N'" & itm_name & "',N'" & Unity & "',"
                S = S & "" & Val(rng) & ","
                S = S & "" & Val(TinPrice) & ","
                S = S & "" & Val(SalPrice) & ","
                S = S & "" & Val(TinPrice) & ","
                S = S & "" & Val(WholePrice) & ","
                S = S & "" & Val(WholeWholePrice) & ","
                S = S & "" & Val(MinimumSalPrice) & ","
                S = S & "" & Val(RateDiscTinPrice) & ","
                S = S & "" & Val(RateDiscSalPrice) & ","
                S = S & "" & Val(RateDiscWholePrice) & ","
                S = S & "" & Val(RateDiscWholeWholePrice) & ","
                S = S & "0,0,0,0,0,0,0,0,0,0,0,0,0,N'" & UserName & "',N'" & StoresTo & "',0,N'" & Vendorname & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                Xbill_no_Expired = MaxRecordTables("BilltINData", "bill_no_Expired")


                Dim X As String = "جرد"
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into BilltINData (Company_Branch_ID,bill_no,IDTM,itm_id,itm_cat,group_branch,itm_name,itm_Unity,price,TinPriceAverage,qu,qu_unity,totalprice,UserName,Stores,bill_date,bill_EndDate,Expired,qu_expired,bill_ProductionDate,bill_no_Expired,Price_Unity,CurrentStock,Vendorname)"
                S = S & " values (N'" & Company_Branch_ID & "',N'" & X & "'," & IDTMTO & ",N'" & itm_id & "',N'" & itm_cat & "',N'" & group_branch & "',N'" & itm_name & "',N'" & Unity & "'," & TinPrice & "," & TinPriceAverage & "," & Val(0) & "," & Val(0) & "," & Val(TinPrice) * Val(0) & ",N'" & UserName & "',N'" & StoresTo & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & bill_EndDate & "',N'" & Expired & "'," & Val(0) & ",N'" & bill_ProductionDate & "',N'" & Xbill_no_Expired & "',N'" & TinPrice & "'," & Val(0) & ",N'" & Vendorname & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

            End If
            If Xbill_no_Expired = "" Then
                bill_no_Expired = Cls.Get_Code_Value_More_ALL_Select("Select MIN(bill_no_Expired) As Exbill_no_Expired, qu_expired, itm_id, Stores, bill_EndDate, qu_unity From dbo.BilltINData Group By itm_id, Stores, bill_EndDate, qu_expired, qu_unity HAVING(itm_id = N'" & itm_id & "') AND (Stores = N'" & StoresTo & "') AND (qu_expired <> 0)")
            Else
                bill_no_Expired = Xbill_no_Expired
            End If

            'مشتريات تحويل
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into ItemsTransfer_BilltINData (Company_Branch_ID,bill_no,itm_id,itm_Unity,qu,qu_unity,Stores,bill_date,EMPID,UserName,bill_EndDate,bill_no_Expired,itm_cat,StoresFrom)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & txtNumberTransfer.Text.Trim & "',N'" & itm_id & "',N'" & Unity & "',N'" & qunt & "',N'" & qunt_unity & "',N'" & StoresTo & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & EMPID & "',N'" & UserName & "',N'" & bill_EndDate & "',N'" & bill_no_Expired & "',N'" & itm_cat & "',N'" & StoresFrom & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            IM.StoreExpired(itm_id, StoresTo, bill_EndDate.ToString(), bill_no_Expired)

            '===================================================
            bill_no_Expired = Cls.Get_Code_Value_More_ALL_Select("Select MIN(bill_no_Expired) As Exbill_no_Expired, qu_expired, itm_id, Stores, bill_EndDate, qu_unity From dbo.BilltINData Group By itm_id, Stores, bill_EndDate, qu_expired, qu_unity HAVING(itm_id = N'" & itm_id & "') AND (Stores = N'" & StoresFrom & "') AND (qu_expired <> 0)")

            'مبيعات تحويل
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into ItemsTransfer_BillsalData (Company_Branch_ID,bill_no,itm_id,itm_Unity,qu,qu_unity,Stores,bill_date,EMPID,UserName,bill_EndDate,bill_no_Expired,itm_cat,StoresTO)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & txtNumberTransfer.Text.Trim & "',N'" & itm_id & "',N'" & Unity & "',N'" & qunt & "',N'" & qunt_unity & "',N'" & StoresFrom & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & EMPID & "',N'" & UserName & "',N'" & bill_EndDate & "',N'" & bill_no_Expired & "',N'" & itm_cat & "',N'" & StoresTo & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            IM.StoreExpired(itm_id, StoresFrom, bill_EndDate.ToString(), bill_no_Expired)

            '===================================================
            IM.Store(itm_id, StoresTo)

            If ConnectOnlineStore = "YES" Then
                EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", itm_id)
                StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", itm_id)
                Cos.UpdateProductStock(StockOnline, itm_id, EditItmId)
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsTransfer_BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtNumberTransfer.Text & "' and itm_id = N'" & itm_id & "' and Stores =N'" & StoresTo & "'" : cmd.ExecuteNonQuery()

            IM.Store(itm_id, StoresFrom)

            If ConnectOnlineStore = "YES" Then
                EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", itm_id)
                StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", itm_id)
                Cos.UpdateProductStock(StockOnline, itm_id, EditItmId)
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update ItemsTransfer_BillsalData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtNumberTransfer.Text & "' and itm_id = N'" & itm_id & "' and Stores =N'" & StoresFrom & "'" : cmd.ExecuteNonQuery()

        Next

    End Sub

    Private Sub GetItemsUnityTotalCarton()
        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select NumberPieces from View_ItemsUnity where itm_id=N'" & TxtPrc.Text & "' and Unity_Name =N'" & cmbUnity.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                NumberPieces = dr(0).ToString
            End If

            Dim Itm_Store As Double = IM.Get_Itm_Store(TxtPrc.Text.Trim, cmbStoresFrom.Text.Trim)
            If NumberPieces = 0 Or NumberPieces = 1 Then
                txtquntAll.Text = Itm_Store
            Else
                If Itm_Store = 0 And NumberPieces = 0 Then
                    txtquntAll.Text = 0
                Else
                    txtquntAll.Text = Val(Itm_Store) / Val(NumberPieces)
                    txtquntAll.Text = Math.Round(Val(txtquntAll.Text), 2)
                End If
            End If
        Else
            txtqunt.Text = Val(txtquntUnity.Text)
        End If

        'Dim store, NumberPieces, NumberPiecesXX, Total2, Total3, TotalCarton As Double
        'NumberPieces = 0 : Total2 = 0 : Total3 = 0 : TotalCarton = 0 : NumberPiecesXX = 0

        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select NumberPieces from View_ItemsUnity where itm_id=N'" & TxtPrc.Text & "' and Unity_Name =N'" & cmbUnity.Text & "'"
        'dr = cmd.ExecuteReader : dr.Read()
        'If dr.HasRows = True Then
        '    NumberPiecesXX = dr(0).ToString
        'End If

        'If NumberPiecesXX <> 1 Then
        '    Dim StoreTotal As Double = 0
        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "select store,NumberPieces from View_ItemsUnity where itm_id=N'" & TxtPrc.Text & "' and Stores=N'" & cmbStoresFrom.Text & "' and UnitySize_ID =N'3'"
        '    dr = cmd.ExecuteReader : dr.Read()
        '    If dr.HasRows = True Then
        '        store = dr(0).ToString : NumberPieces = dr(1).ToString
        '        If store = 0 And NumberPieces = 0 Then
        '            TotalCarton = 0
        '        Else
        '            StoreTotal = Val(store) - Val(QuntFrom) - Val(txtqunt.Text)
        '            TotalCarton = Val(StoreTotal) / Val(NumberPieces)
        '            TotalCarton = Math.Round(Val(TotalCarton), 2)
        '            'Dim PRC As String = TotalCarton
        '            'Dim split As String() = New String() {"."}
        '            'Dim itemsSplit As String() = PRC.Split(split, StringSplitOptions.None)
        '            'TotalCarton = itemsSplit(0).ToString()
        '        End If
        '        'Total2 = Val(TotalCarton) * Val(NumberPieces)
        '        'Total3 = Val(store) - Val(Total2)

        '        'Dim PRC2 As String = Total3
        '        'Dim split2 As String() = New String() {"."}
        '        'Dim itemsSplit2 As String() = PRC2.Split(split2, StringSplitOptions.None)
        '        'Total3 = itemsSplit2(0).ToString()
        '        'txtquntAll.Text = TotalCarton.ToString + "." + Total3.ToString
        '        txtquntAll.Text = TotalCarton
        '    End If
        'Else
        '    txtquntAll.Text = Val(QuntAll) - Val(QuntFrom) - Val(txtqunt.Text)
        'End If

    End Sub

    Private Sub txtNotes_KeyUp(sender As Object, e As KeyEventArgs) Handles txtNotes.KeyUp
        If e.KeyCode = 13 Then
            GetDataStores()
            TxtPrc.Focus()
        End If
    End Sub

    Private Sub ChackExpirationDate(ByVal itm_id As String, ByVal Stores As String, ByVal qu As String)
        Dim Expired As String
        Expired = Cls.Get_Code_Value_More("BilltINData", "Expired", "itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "'")
        If Expired = "بصلاحية" Then
            Try
                Dim qu_expired As String = "" : Dim Total_expired As String = "" : Dim bill_EndDate As String = ""
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select itm_id, itm_name, MIN(bill_EndDate) As Ex_bill_EndDate, Expired, qu_expired, Stores From dbo.BilltINData Group By itm_id, itm_name, Expired, qu_expired, Stores HAVING(itm_id = N'" & itm_id & "') AND (Stores = N'" & Stores & "')"
                dr = cmd.ExecuteReader : dr.Read()
                If dr.HasRows = True Then
                    bill_EndDate = dr(2).ToString : qu_expired = dr(4).ToString
                End If

                If qu_expired = 0 Then
                    Total_expired = Val(qu) - Val(qu_expired)
                Else
                    Total_expired = Val(qu_expired) - Val(qu)
                End If

                If Total_expired < 0 Then

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "update BilltINData set qu_expired =N'0' where itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "' and bill_EndDate =N'" & bill_EndDate & "'" : cmd.ExecuteNonQuery()

                    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                    cmd.CommandText = "Select itm_id, itm_name, MIN(bill_EndDate) As Ex_bill_EndDate, Expired, qu_expired, Stores From dbo.BilltINData Group By itm_id, itm_name, Expired, qu_expired, Stores HAVING(itm_id = N'" & itm_id & "') AND (Stores = N'" & Stores & "')"
                    dr = cmd.ExecuteReader : dr.Read()
                    If dr.HasRows = True Then
                        bill_EndDate = dr(2).ToString : qu_expired = dr(4).ToString
                    End If

                    Dim PRC As String = Total_expired
                    Dim split As String() = New String() {"-"}
                    Dim itemsSplit As String() = PRC.Split(split, StringSplitOptions.None)
                    Dim Xexpired As String = itemsSplit(1).ToString()

                    If qu_expired = 0 Then
                        Total_expired = Val(Xexpired) - Val(qu_expired)
                    Else
                        Total_expired = Val(qu_expired) - Val(Xexpired)
                    End If
                End If
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "update BilltINData set qu =N'" & Total_expired & "',qu_unity =N'" & Total_expired & "' where itm_id =N'" & itm_id & "' and Stores =N'" & Stores & "' and bill_EndDate =N'" & bill_EndDate & "'" : cmd.ExecuteNonQuery()

            Catch ex As Exception
                ErrorHandling(ex, Me.Text)
            End Try
        End If
    End Sub


End Class