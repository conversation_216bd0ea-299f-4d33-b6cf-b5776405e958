﻿Public Class Frm_Active_LicenseKey
    Dim C_Bisns As New Cls_Constant
    Dim C_Validate As New Cls_Validation
    Dim sear As String
    Dim searhdd As String
    Dim seartotal As String
    Dim C_Data As New Cls_Users
    Dim cp As New Cet_Cpu

    Private Sub Btn_Active_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Btn_Active.Click

        Try
            If TextBox2.Text = "" Then : MsgBox("برجاء ادخال الكود المقابل او كود التفعيل") : Exit Sub : End If

            mykey.SetValue("ActivationLicenseKey", TextBox1.Text)
            ActivationLicenseKey = TextBox1.Text

            mykey.SetValue("ActivationLicenseKeyActive", TextBox2.Text)
            ActivationLicenseKeyActive = TextBox2.Text

            ConvertLicenseKey()

            If Val(TextBox2.Text) = Val(seartotal) Then
                C_Data.Sb_Sp_InsertA_C(TextBox2.Text)
                activ_1 = True
                MDIParent1.Show()
                Me.Close()
            End If

            If Val(TextBox2.Text) <> Val(seartotal) Then
                Dim Bill As Integer = C_Data.Sp_SelectCountOnFastALL("Sales_Bill", "bill_No")
                Dim Items As Integer = C_Data.Sp_SelectCountOnFastALL("Items", "itm_id")
                Dim Emp As Integer = C_Data.Sp_SelectCountOnFastALL("Employees", "EMPID")
                Dim Exp As Integer = C_Data.Sp_SelectCountOnFastALL("Expenses", "id")
                Dim Cust As Integer = C_Data.Sp_SelectCountOnFastALL("Customers", "id")
                Dim Vendor As Integer = C_Data.Sp_SelectCountOnFastALL("vendors", "id")
                Dim MOR As Integer = C_Data.Sp_SelectCountOnFastALL("MaintenanceOrderRunning", "OrderDayID")
                Dim MCD As Integer = C_Data.Sp_SelectCountOnFastALL("Maintenance_Car_Data", "Car_Data_ID")
                Dim MFP As Integer = C_Data.Sp_SelectCountOnFastALL("ManufacturingProduct", "Manufacturing_ID")
                Dim MDN As Integer = C_Data.Sp_SelectCountOnFastALL("ManufacturingDismissalNotice", "id")
                Dim MOVES As Integer = C_Data.Sp_SelectCountOnFastALL("MOVES", "MOVID")

                'If Bill > 350 Or Items > 350 Or Emp > 10 Or Exp > 50 Or Cust > 60 Or Vendor > 60 Or MOR > 50 Or MCD > 50 Or MFP > 50 Or MDN > 50 Or MOVES > 50 Then

                'If Bill > 250 Or Items > 250 Or Emp > 10 Or Exp > 50 Or Cust > 60 Or Vendor > 20 Or MOR > 50 Or MCD > 50 Or MFP > 50 Or MDN > 50 Or MOVES > 50 Then

                'If Bill > 100 Or Items > 250 Or Emp > 10 Or Exp > 50 Or Cust > 60 Or Vendor > 10 Or MOR > 50 Or MCD > 50 Or MFP > 50 Or MDN > 50 Or MOVES > 50 Then
                'If Bill > 80 Or Items > 8000 Or Emp > 10 Or Exp > 50 Or Cust > 60 Or Vendor > 10 Or MOR > 50 Or MCD > 50 Or MFP > 50 Or MDN > 50 Or MOVES > 50 Then

                If Bill > 50 Or Items > 50 Or Emp > 10 Or Exp > 50 Or Cust > 10 Or Vendor > 10 Or MOR > 50 Or MCD > 50 Or MFP > 50 Or MDN > 50 Or MOVES > 50 Then

                    MsgBox("هذا الرقم غير صحيح أتصل بصاحب البرنامج", MsgBoxStyle.Critical, "")
                    MsgBox(" لم تقم بشراء النسخه النهائية من البرنامج برجاء الاتصال بشركة فيت سوفت للبرمجيات م/ 01004052561 ", MsgBoxStyle.Critical, "")
                    End
                    End If
                    'C_Validate.banna(frm_Main)
                    activ_1 = False
                    'MDIParent1.Text = "demo"
                    'MDIParent1.Show()
                    'Me.Dispose()
                End If
        Catch ex As Exception

        End Try

        'برجاء عدم محاولة تشغيل البرنامج باحدي الطرق الغير شرعيه لن تستطيع استخراج القيم لاتحاول لانه بيسجل كل القيم ومراقب حتي لاتتعرض للمسألة القانونية
    End Sub

    Private Sub ConvertLicenseKey()

        Dim key As Long
        Dim serial As Long = TextBox1.Text
        key = serial * serial + 53 / serial + 113 * (serial / 4)

        seartotal = key

    End Sub

    Private Sub Frm_Active_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing

    End Sub
    Private Sub Frm_Active_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Try
            TextBox1.Text = ActivationLicenseKey

            ConvertLicenseKey()

            TextBox2.Select()
            Dim x As Boolean = C_Data.Fn_Sp_SelectA_C(seartotal)
            If x = True Then
                Me.Close()
                activ_1 = True
            Else
                MsgBox(" لم تقم بشراء النسخه النهائية من البرنامج برجاء الاتصال بشركة فيت سوفت للبرمجيات  م/ 01004052561 ", MsgBoxStyle.Critical, "")
            End If
            ReturnbyActive = x
        Catch ex As Exception

        End Try
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        End
    End Sub

    Private Sub TextBox2_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles TextBox2.KeyUp
        If e.KeyCode = 13 Then
            Btn_Active.PerformClick()
        End If

    End Sub

    Private Sub TextBox1_TextChanged(sender As Object, e As EventArgs) Handles TextBox1.TextChanged
        MyVars.CheckNumber(TextBox1)
    End Sub
End Class
