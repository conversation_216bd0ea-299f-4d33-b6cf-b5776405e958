﻿
Public Class FrmInputBox
    Private Sub FrmInputBox_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub

    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOK.Click
        If MsgBoxInputBox = "ProgramAdditions" Then
            If txtPassword.Text = InputBoxPasswordText Then
                InputBoxPasswordText = ""
                txtPassword.Text = ""
                Me.Close()
                frmProgramAdditions.ShowDialog()
            Else
                MsgBox("عفواا كلمة السر خطأ", MsgBoxStyle.Exclamation)
            End If
        End If
    End Sub

    Private Sub txtPassword_KeyUp(sender As Object, e As KeyEventArgs) Handles txtPassword.KeyUp
        If e.KeyCode = 13 Then
            btnOK.PerformClick()
        End If
    End Sub
End Class