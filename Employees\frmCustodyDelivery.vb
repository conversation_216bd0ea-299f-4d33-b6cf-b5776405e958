﻿Imports System.Data.SqlClient

Public Class frmCustodyDelivery
    Public Property CustodyID As String

    Dim con As New SqlConnection(constring)
    Dim cmd As New SqlCommand

    Private Sub frmCustodyDelivery_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            con.Open()
            cmd.Connection = con
            txtCustodyID.Text = CustodyID
            LoadReceivers()
        Catch ex As Exception
            MsgBox("Error: " & ex.Message, MsgBoxStyle.Critical)
        End Try
    End Sub

    Private Sub LoadReceivers()
        cmbReceiver.Items.Clear()
        cmd.CommandText = "SELECT NameEmployee FROM Employees ORDER BY NameEmployee"
        Dim dr As SqlDataReader = cmd.ExecuteReader
        While dr.Read
            cmbReceiver.Items.Add(dr("NameEmployee"))
        End While
        dr.Close()
    End Sub

    Private Sub btnSaveDelivery_Click(sender As Object, e As EventArgs) Handles btnSaveDelivery.Click
        If Validate_Delivery() = False Then Exit Sub

        Try
            ' تحديث حالة العهدة إلى "مسلمة"
            cmd.CommandText = "UPDATE Custody SET Status = " &
                             "(SELECT CustodyStatusID FROM CustodyStatus WHERE CustodyStatusID = 2) " &
                             "WHERE CustodyID =N'" & CustodyID & "'"
            cmd.ExecuteNonQuery()

            ' تسجيل بيانات التسليم
            cmd.CommandText = "INSERT INTO CustodyDelivery (CustodyID, DeliveryDate, ReceivedBy, DeliveryNotes) VALUES (" &
                             "N'" & CustodyID & "', " &
                             "GETDATE(), " &
                             "N'" & cmbReceiver.Text & "', " &
                             "N'" & txtNotes.Text & "')"
            cmd.ExecuteNonQuery()

            MsgBox("تم تسليم العهدة بنجاح", MsgBoxStyle.Information)
            Me.Close()
        Catch ex As Exception
            MsgBox("Error: " & ex.Message, MsgBoxStyle.Critical)
        End Try
    End Sub

    Function Validate_Delivery() As Boolean
        If Trim(cmbReceiver.Text) = "" Then
            MsgBox("فضلاً أدخل اسم المستلم", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            cmbReceiver.Focus() : Return False
        End If
        Return True
    End Function

    Private Sub frmCustodyDelivery_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        If con.State = ConnectionState.Open Then con.Close()
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub
End Class