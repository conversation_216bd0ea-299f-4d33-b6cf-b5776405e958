﻿Public Class outcome
    Dim archiveManager As New Cls_ArchiveManager

    Dim ItmID As String

    Private Sub TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Txt_Value.TextChanged
        If Not IsNumeric(Txt_Value.Text) Then
            System.Windows.Forms.SendKeys.Send("{backspace}")
        End If
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        GetNumericValueSeparators()

        If cmbTreasuryName.Visible = True Then
            If Trim(cmbTreasuryName.Text) = "" Then
                MsgBox("فضلاً أدخل أسم الخزينة", MsgBoxStyle.Exclamation)
                cmbTreasuryName.Focus()
                Exit Sub
            End If
        End If

        If Txt_ExpName.Text = "" Or Val(Txt_Value.Text) = 0 Or Cmb_ExpName.Text.Trim = "" Then
            MsgBox("اكمل البيانات", MsgBoxStyle.Exclamation)
            Exit Sub
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * From Cat_Expenses where g_name =N'" & Cmb_ExpName.Text & "'"
        dr = cmd.ExecuteReader()
        If dr.HasRows = False Then
            MsgBox("عفواً لا يوجد بند مسجل مسبقا بنفس الاسم", MsgBoxStyle.Exclamation)
            Exit Sub
        End If

        If cmbTreasuryName.Visible = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * From Treasury where Treasury_Name =N'" & cmbTreasuryName.Text & "'"
            dr = cmd.ExecuteReader()
            If dr.HasRows = False Then
                MsgBox("عفواً لا يوجد خزينة مسجل بنفس الاسم", MsgBoxStyle.Exclamation)
                Exit Sub
            Else
            End If
        End If

        If Button1.Text = "حفظ التعديل" Then
            UpdateData()
        Else
            savedata()
        End If

        Get_Movement_In_Out_Money(Dtp_Date.Text, Treasury_Code)

        '===============================================================================
        Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
        If UseAccounts = "YES" Then
            Daily_Restrictions()
        End If
        '===============================================================================

        If Button1.Text = "حفظ التعديل" Then
            Button1.Text = "حفظ"
            ItmID = ""
            GetData()
        End If
        clear()
    End Sub

    Private Sub UpdateData()
        Dim TreasuryCode As Integer
        If cmbTreasuryName.Visible = True Then
            TreasuryCode = Cls.Get_Code_Value_Branch_More("Treasury", "Treasury_Code", "Treasury_Name=N'" & cmbTreasuryName.Text & "'")
        Else
            TreasuryCode = Treasury_Code
        End If

        Dim Payment_Status As String = ""

        If ChkCash.Checked = True Then
            Payment_Status = "نقدي"
        ElseIf ChkState.Checked = True Then
            Payment_Status = "أجل"
        ElseIf chkBank.Checked = True Then
            Payment_Status = "بنك"
        ElseIf rdoCashWallet.Checked = True Then
            Payment_Status = "محفظة"
        End If


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Expenses set Exp_Name =N'" & Txt_ExpName.Text & "',Exp_Value =N'" & Txt_Value.Text & "',Exp_Date =N'" & Cls.C_date(Dtp_Date.Text) & "',Exp_Notes =N'" & Txt_Notes.Text & "',Exp_Date_ =N'" & Dtp_Date.Text & "',UserName =N'" & UserName & "',cats =N'" & Cmb_ExpName.Text.Trim & "',Treasury_Code =N'" & TreasuryCode & "',Company_Branch_ID =N'" & Company_Branch_ID & "',Payment_Status =N'" & Payment_Status & "',Vendorname =N'" & cmbvendores.Text & "' where id =N'" & ItmID & "'" : cmd.ExecuteNonQuery()


        IM.VendorAccountTotal(cmbvendores.Text.Trim)
    End Sub


    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "select id, Exp_Name as [الاسم],Exp_Value as [القيمة],Exp_Date_ as [التاريخ],Exp_Notes as [ملاحظات],Payment_Status as [حالة الدفعة],Vendorname as [اسم المورد] from Expenses order by 1"
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
        DataGridView1.Columns(0).Width = 0
        SumTotal()
    End Sub

    Private Sub outcome_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cls.fill_combo_Branch("Treasury", "Treasury_Name", cmbTreasuryName)
        cmbTreasuryName.Text = Cls.Get_Code_Value_Branch_More("Treasury", "Treasury_Name", "Treasury_Code=N'" & Treasury_Code & "'")
        Cls.fill_combo("vendors", "Vendorname", cmbvendores)

        Bra.Fil("Cat_Expenses", "g_name", Cmb_ExpName)
        Bra.Fil("Cat_Expenses", "g_name", Cmb_ExpNameFind)
        GetData()

        If TreasuryControl = "YES" Then
            lblTreasuryName.Visible = True
            cmbTreasuryName.Visible = True
        Else
            lblTreasuryName.Visible = False
            cmbTreasuryName.Visible = False
        End If

        If PermtionName = "مدير" Then
            Button2.Visible = True
        Else
            Button2.Visible = False
        End If
        GetDateNotBeenActivatedOutcome()
    End Sub

    Private Sub GetDateNotBeenActivatedOutcome()
        If PermtionName <> "مدير" Then
            If DateNotBeenActivatedOutcome = "NO" Then
                Dtp_Date.Enabled = True
            Else
                Dtp_Date.Enabled = False
            End If
        End If
    End Sub

    Sub savedata()
        Dim TreasuryCode As Integer
        If cmbTreasuryName.Visible = True Then
            TreasuryCode = Cls.Get_Code_Value_Branch_More("Treasury", "Treasury_Code", "Treasury_Name=N'" & cmbTreasuryName.Text & "'")
        Else
            TreasuryCode = Treasury_Code
        End If

        Dim Payment_Status As String = ""

        If ChkCash.Checked = True Then
            Payment_Status = "نقدي"
        ElseIf ChkState.Checked = True Then
            Payment_Status = "أجل"
        ElseIf chkBank.Checked = True Then
            Payment_Status = "بنك"
        ElseIf rdoCashWallet.Checked = True Then
            Payment_Status = "محفظة"
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Insert into Expenses(Exp_Name,Exp_Value,Exp_Date,Exp_Notes,Exp_Date_,UserName,cats,Treasury_Code,Company_Branch_ID,Payment_Status,Vendorname) values (N'" & Txt_ExpName.Text & "'," & Txt_Value.Text & ",N'" & Cls.C_date(Dtp_Date.Text) & "',N'" & Txt_Notes.Text & "',N'" & Dtp_Date.Text & "',N'" & UserName & "',N'" & Cmb_ExpName.Text.Trim & "',N'" & TreasuryCode & "',N'" & Company_Branch_ID & "',N'" & Payment_Status & "',N'" & cmbvendores.Text & "')"
        cmd.CommandText = S
        cmd.ExecuteNonQuery()

        IM.VendorAccountTotal(cmbvendores.Text.Trim)


        GetData()
        MsgBox("تم حفظ المصروف بنجاح", MsgBoxStyle.Information, "حفظ")
    End Sub

    Private Sub SumTotal()
        Dim SM As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM = SM + DataGridView1.Rows(i).Cells(2).Value
        Next
        TxtSearch.Text = FormatNumberWithSeparators(SM)
    End Sub

    Sub clear()
        Txt_ExpName.Text = ""
        'Txt_Notes.Text = ""
        Txt_Value.Text = ""
        Cmb_ExpName.SelectedIndex = -1
    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        Dim ItmID, XDate As String
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub

            ItmID = DataGridView1.SelectedRows(i).Cells(0).Value

            '=========================================== Archive Manager ============================================================
            archiveManager.ArchiveAndDeleteExpensesData("Delete", ItmID, UserName, "تم حذف المصروف لعدم النشاط")
            '=========================================== Archive Manager ============================================================


            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete From  Expenses where id =N'" & ItmID & "'" : cmd.ExecuteNonQuery()

            cmd.CommandText = "delete From  MOVES where bill_no =N'" & ItmID & "' and MOVStatement =N'مصروفات'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'مصروفات'" : cmd.ExecuteNonQuery()

            XDate = DataGridView1.SelectedRows(i).Cells(3).Value
            Get_Movement_In_Out_Money(XDate, Treasury_Code)
        Next
        GetData()
        Dim SM As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM = SM + DataGridView1.Rows(i).Cells(2).Value
        Next
        TxtSearch.Text = FormatNumberWithSeparators(SM)
    End Sub

    Private Sub BtnAddVendor_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnAddVendor.Click
        Frm_IM_Outcome_Cat.Show()
    End Sub

    Private Sub Daily_Restrictions()
        Dim Account As String = ""
        Dim AccountCode As String = ""
        Dim AccountTreasury As String = "" : Dim AccountCodeTreasury As String = ""


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مصروفات'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            Account = dr("Link_AccountsTree")
            AccountCode = dr("ACCNumber")
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select ACCName,ACCNumber from AccountsTree where ACCName=N'الخزينة'" : dr = cmd.ExecuteReader
        If dr.Read Then
            AccountTreasury = dr("ACCName") : AccountCodeTreasury = dr("ACCNumber")
        End If

        '========================================================================================

        Dim MOVRegNumber As String = Cls.MAXRECORD("MOVES", "MOVRegNumber")
        Dim MOVID As String = Cls.MAXRECORD("MOVES", "MOVID")
        Dim bill_no As String = Cls.MAXRECORD("Expenses", "id") - 1

        If Button1.Text = "حفظ التعديل" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete From  MOVES where bill_no =N'" & ItmID & "' and MOVStatement =N'مصروفات'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & ItmID & "' and MOVDNameAccount =N'مصروفات'" : cmd.ExecuteNonQuery()

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
            S = S & "N'" & bill_no & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(Dtp_Date.Text) & "',N'" & Account & "',N'" & Txt_Value.Text & "',N'" & Txt_Value.Text & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            ' من حساب / المصروفات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
            S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(Dtp_Date.Text) & "',N'" & AccountCode & "',N'" & Account & "',N'" & Txt_Value.Text & "',N'0',N'" & Account & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            ' الى حساب / الخزينة
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
            S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(Dtp_Date.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'0',N'" & Txt_Value.Text & "',N'" & AccountTreasury & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

        Else

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
            S = S & "N'" & bill_no & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(Dtp_Date.Text) & "',N'" & Account & "',N'" & Txt_Value.Text & "',N'" & Txt_Value.Text & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            ' من حساب / المصروفات
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
            S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(Dtp_Date.Text) & "',N'" & AccountCode & "',N'" & Account & "',N'" & Txt_Value.Text & "',N'0',N'" & Account & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            ' الى حساب / الخزينة
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
            S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(Dtp_Date.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'0',N'" & Txt_Value.Text & "',N'" & AccountTreasury & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If
    End Sub

    Private Sub btnUpdateExpName_Click(sender As Object, e As EventArgs) Handles btnUpdateExpName.Click
        Bra.Fil("Cat_Expenses", "g_name", Cmb_ExpName)
        Bra.Fil("Cat_Expenses", "g_name", Cmb_ExpNameFind)
    End Sub

    Private Sub Cmb_ExpName_KeyUp(sender As Object, e As KeyEventArgs) Handles Cmb_ExpName.KeyUp
        If e.KeyCode = 13 Then
            Txt_ExpName.Focus()
        End If
    End Sub

    Private Sub Txt_ExpName_KeyUp(sender As Object, e As KeyEventArgs) Handles Txt_ExpName.KeyUp
        If e.KeyCode = 13 Then
            Txt_Value.Focus()
        End If
    End Sub

    Private Sub Txt_Value_KeyUp(sender As Object, e As KeyEventArgs) Handles Txt_Value.KeyUp
        If e.KeyCode = 13 Then
            Txt_Notes.Focus()
            Txt_Value.Text = FormatNumberWithSeparators(Txt_Value.Text)
        End If
    End Sub

    Private Sub Dtp_Date_KeyUp(sender As Object, e As KeyEventArgs) Handles Dtp_Date.KeyUp
        If e.KeyCode = 13 Then
            Txt_Notes.Focus()
        End If
    End Sub

    Private Sub Txt_Notes_KeyUp(sender As Object, e As KeyEventArgs) Handles Txt_Notes.KeyUp
        If e.KeyCode = 13 Then
            Button1.PerformClick()
        End If
    End Sub

    Private Sub DataGridView1_DoubleClick(sender As Object, e As EventArgs) Handles DataGridView1.DoubleClick
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim TreasuryCode As Integer = Cls.Get_Code_Value_Branch_More("Treasury", "Treasury_Code", "Treasury_Name=N'" & cmbTreasuryName.Text & "'")

        ItmID = DataGridView1.SelectedRows(0).Cells(0).Value.ToString

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Expenses where id=N'" & ItmID & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            Cmb_ExpName.Text = dr("Cats").ToString
            Txt_ExpName.Text = dr("Exp_Name").ToString
            Txt_Value.Text = dr("Exp_Value").ToString
            Dtp_Date.Text = Cls.R_date(dr("Exp_Date").ToString)
            Txt_Notes.Text = dr("Exp_Notes").ToString
            TreasuryCode = dr("Treasury_Code").ToString
        End If
        cmbTreasuryName.Text = Cls.Get_Code_Value_Branch_More("Treasury", "Treasury_Name", "Treasury_Code=N'" & TreasuryCode & "'")

        If PermtionName = "مدير" Then
            Button1.Text = "حفظ التعديل"
        End If
    End Sub

    Private Sub txtsearsh_TextChanged(sender As Object, e As EventArgs) Handles txtsearsh.TextChanged
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If txtsearsh.Text = " بحث باسم المصروف ...." Or txtsearsh.Text = "" Then
            S = "select id as [رقم], Exp_Name as [الاسم],Exp_Value as [القيمة],Exp_Date_ as [التاريخ],Exp_Notes as [ملاحظات] from Expenses"
        Else
            S = "select id as [رقم], Exp_Name as [الاسم],Exp_Value as [القيمة],Exp_Date_ as [التاريخ],Exp_Notes as [ملاحظات] from Expenses where  Exp_Name Like N'%" & txtsearsh.Text & "%'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [التاريخ]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [الاسم]"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

        DataGridView1.Columns(0).Width = 0
        SumTotal()
    End Sub

    Private Sub txtsearsh_MouseClick(sender As Object, e As MouseEventArgs) Handles txtsearsh.MouseClick
        If txtsearsh.Text = " بحث باسم المصروف ...." Then txtsearsh.SelectAll() : txtsearsh.Font = New Font(txtsearsh.Font, FontStyle.Regular)
    End Sub

    Private Sub Cmb_ExpNameFind_SelectedIndexChanged(sender As Object, e As EventArgs) Handles Cmb_ExpNameFind.SelectedIndexChanged
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If Cmb_ExpNameFind.Text = " بحث بند المصروف ...." Or Cmb_ExpNameFind.Text = "" Then
            S = "select id as [رقم], Exp_Name as [الاسم],Exp_Value as [القيمة],Exp_Date_ as [التاريخ],Exp_Notes as [ملاحظات] from Expenses"
        Else
            S = "select id as [رقم], Exp_Name as [الاسم],Exp_Value as [القيمة],Exp_Date_ as [التاريخ],Exp_Notes as [ملاحظات] from Expenses where  Cats Like N'%" & Cmb_ExpNameFind.Text & "%'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [التاريخ]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [الاسم]"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

        DataGridView1.Columns(0).Width = 0
        SumTotal()
    End Sub

    Private Sub Cmb_ExpNameFind_MouseClick(sender As Object, e As MouseEventArgs) Handles Cmb_ExpNameFind.MouseClick
        If Cmb_ExpNameFind.Text = " بحث بند المصروف ...." Then Cmb_ExpNameFind.SelectAll() : Cmb_ExpNameFind.Font = New Font(txtsearsh.Font, FontStyle.Regular)
    End Sub

    Private Sub GetNumericValueSeparators()
        ActionNumericSeparators = True

        Txt_Value.Text = GetRemoveNumericSeparatorsValue(Txt_Value.Text)

        ActionNumericSeparators = False
    End Sub

End Class