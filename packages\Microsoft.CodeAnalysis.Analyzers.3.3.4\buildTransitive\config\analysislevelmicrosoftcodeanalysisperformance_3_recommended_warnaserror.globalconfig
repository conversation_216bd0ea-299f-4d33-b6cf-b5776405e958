# NOTE: Requires **VS2019 16.7** or later

# 'MicrosoftCodeAnalysisPerformance' Rules from '3.0.0' release with 'Recommended' analysis mode escalated to 'error' severity
# Description: 'MicrosoftCodeAnalysisPerformance' Rules with enabled-by-default state from '3.0.0' release with 'Recommended' analysis mode. Rules that are first released in a version later than '3.0.0' are disabled. Enabled rules with 'warning' severity are escalated to 'error' severity to respect 'CodeAnalysisTreatWarningsAsErrors' MSBuild property.

is_global = true

global_level = -99


# RS1008: Avoid storing per-compilation data into the fields of a diagnostic analyzer
dotnet_diagnostic.RS1008.severity = error

# RS1012: Start action has no registered actions
dotnet_diagnostic.RS1012.severity = error

# RS1013: Start action has no registered non-end actions
dotnet_diagnostic.RS1013.severity = error

# RS1034: Prefer 'IsKind' for checking syntax kinds
dotnet_diagnostic.RS1034.severity = none
