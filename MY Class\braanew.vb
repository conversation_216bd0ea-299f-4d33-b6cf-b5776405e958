﻿Public Class braanew
    Friend Function Find(ByVal TableName As String, ByVal FieldName As String, ByVal StringFind As String) As Boolean
        Dim S As String
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select * From " & TableName & " Where " & FieldName & "=N'" & StringFind & "'"
        'cmd.Connection = Cn
        cmd.CommandType = CommandType.Text
        cmd.CommandText = S
        dr.Close()
        dr = cmd.ExecuteReader
        dr.Read()

        Return Fin = dr.HasRows
    End Function

    Public Function Selected(ByVal field As String, ByVal table As String, ByVal Condition_Where As String, ByVal Text As String, ByVal DataReder As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select " + field + " from " + table + " where " + Condition_Where + "=N'" & Text & "'"
        dr = cmd.ExecuteReader : dr.<PERSON>()
        If dr.<PERSON> = False Then dr.Close() : Exit Function
        If dr(0) Is DBNull.Value Then
        Else
            DataReder = dr(0)
        End If
    End Function

    Friend Function Fil(ByVal TableName As String, ByVal FieldName As String, ByVal ctl As ComboBox)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select distinct " & FieldName & " From " & TableName & " order by 1"
        dr = cmd.ExecuteReader
        ctl.Items.Clear()
        Do While dr.Read
            ctl.Items.Add(Trim(dr(0).ToString()))
        Loop
    End Function

    Friend Function Fil(ByVal TableName As String, ByVal FieldName As String, ByVal ctl As ListBox)
        Dim S As String
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select " & FieldName & " From " & TableName
        cmd.CommandType = CommandType.Text
        cmd.CommandText = S

        dr = cmd.ExecuteReader
        ctl.Items.Clear()
        Do While dr.Read
            ctl.Items.Add(dr(0))
        Loop


    End Function


    
End Class
