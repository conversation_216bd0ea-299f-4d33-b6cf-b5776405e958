﻿Imports vb = Microsoft.VisualBasic
Imports CrystalDecisions.CrystalReports.Engine
Imports System.IO
Public Class Frm_IM_Bsal_Show
    Dim WithEvents BS As New BindingSource
    Dim ColorWithItems As String = mykey.GetValue("UseColorWithItems", "")
    Dim SalesPricePublic As String = mykey.GetValue("SalesPricePublic", "NO")
    Dim Cls_Altfiqith As New Class_Altfiqith
    Dim CustomerAddress As String = ""
    Dim CustomerTel As String = ""
    Dim AmountDebitCreditPrevious, AmountDebitCreditAfter As String

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT CAST(bill_no as float) AS [رقم الفاتورة],Vendorname AS [أسم العميل], bill_date AS [تاريخ الفاتورة], billtime AS [وقت الفاتورة], totalpricebeforedisc AS [القيمة قبل الخصم], disc AS [الخصم], totalpriceafterdisc AS [القيمة بعد الخصم],BEY as [المدفوع],STAYING as [الباقى],Stat as [الحالة] FROM IM_Bsal where BILL_NO <> N'جرد'"

        If ChkAll.Checked = False Then
            If cmbvendornameshow.Text <> "" Then
                S = S & " and Vendorname =N'" & cmbvendornameshow.Text.Trim & "'"
            End If
            If txtbillnoSearch.Text <> "" Then
                S = S & " and BILL_NO =N'" & txtbillnoSearch.Text.Trim & "'"
            End If
            If cmbEmployees.Text <> "" Then
                S = S & " and EmpName =N'" & cmbEmployees.Text.Trim & "'"
            End If
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم الفاتورة]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [تاريخ الفاتورة]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [اسم العميل]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
        SumDGV()
        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(2).Value)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(2).Value = SM
        Next

        DataGridView1.Columns(0).Width = 75
        DataGridView1.Columns(1).Width = 85
        DataGridView1.Columns(2).Width = 85
        DataGridView1.Columns(3).Width = 85
        DataGridView1.Columns(4).Width = 75
        DataGridView1.Columns(5).Width = 75
        DataGridView1.Columns(6).Width = 75

    End Sub

    Private Sub GetDetails()
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim ItmID As String

        ItmID = DataGridView1.SelectedRows(0).Cells(0).Value

        Panel2.Top = 80
        Panel2.Dock = DockStyle.Fill

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id as [الباركود],itm_cat as [المجموعة] ,itm_name as [اسم الصنف],price as [السعر] ,qu_unity as [الكمية],itm_Unity as [الوحدة] ,totalprice as [الأجمالي],Stores as [المخزن] from IM_Bsal_Data where bill_no =N'" & ItmID & "'"
        dr = cmd.ExecuteReader
        DataGridView2.DataSource = Cls.PopulateDataView(dr)
        DataGridView2.Columns(0).Width = 75
        DataGridView2.Columns(1).Width = 75
        DataGridView2.Columns(2).Width = 100
        DataGridView2.Columns(3).Width = 65
        DataGridView2.Columns(4).Width = 65
        DataGridView2.Columns(5).Width = 65
        DataGridView2.Columns(6).Width = 65
        DataGridView2.Columns(7).Width = 65

        TxtTotal.Text = DataGridView1.SelectedRows(0).Cells(7).Value
        txt_disc.Text = DataGridView1.SelectedRows(0).Cells(5).Value
        txt_afterdisc.Text = DataGridView1.SelectedRows(0).Cells(6).Value


    End Sub

    Private Sub SumDGV()
        Dim SM As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM = SM + DataGridView1.Rows(i).Cells(4).Value
        Next
        txttotalpricebefor.Text = SM

        Dim SM2 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM2 = SM2 + DataGridView1.Rows(i).Cells(5).Value
        Next
        txt_disc.Text = SM2

        Dim SM3 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM3 = SM3 + DataGridView1.Rows(i).Cells(6).Value
        Next
        txt_afterdisc.Text = SM3

        Dim SM4 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM4 = SM4 + DataGridView1.Rows(i).Cells(7).Value
        Next
        txtpaying.Text = SM4

        Dim SM5 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM5 = SM5 + DataGridView1.Rows(i).Cells(8).Value
        Next
        txtstaying.Text = SM5

    End Sub

    Private Sub DateTimePicker1_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        GetData()
    End Sub

    Private Sub DateTimePicker2_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        GetData()
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub BtnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnShow.Click
        GetData()
    End Sub

    Private Sub FrmShowImport_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", cmbvendornameshow)

        End If
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployees)
        cmbvendornameshow.Items.Add("نقدا")

        GetData()
        Panel2.Top = 10000
        PanelEmployees.Top = 10000
    End Sub

    Private Sub DataGridView1_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles DataGridView1.DoubleClick
        GetDetails()
        insertxt()
    End Sub

    Private Sub ChkAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkAll.CheckedChanged
        If ChkAll.Checked = True Then
            cmbvendornameshow.Enabled = False
            txtbillnoSearch.Enabled = False
            cmbvendornameshow.SelectedIndex = -1
        Else
            cmbvendornameshow.Enabled = True
            txtbillnoSearch.Enabled = True
        End If
    End Sub

    Private Sub btnprint_Click_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnprint.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            S = "insert into PrintSalesPurchases(Company_Branch_ID,BILL_NO,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,BEY,STAYING,Stat,TotalBeforeDisc,TotalDisc,TotalafterDisc,TotalBay,Totalstaying)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView1.Rows(i).Cells(0).Value & "',N'" & DataGridView1.Rows(i).Cells(1).Value & "',N'" & DataGridView1.Rows(i).Cells(2).Value & "',N'" & DataGridView1.Rows(i).Cells(3).Value & "',N'" & DataGridView1.Rows(i).Cells(4).Value & "',N'" & DataGridView1.Rows(i).Cells(5).Value & "',N'" & DataGridView1.Rows(i).Cells(6).Value & "',N'" & DataGridView1.Rows(i).Cells(7).Value & "',N'" & DataGridView1.Rows(i).Cells(8).Value & "',N'" & DataGridView1.Rows(i).Cells(9).Value & "',N'" & txttotalpricebefor.Text & "',N'" & txt_disc.Text & "',N'" & txt_afterdisc.Text & "',N'" & txtpaying.Text & "',N'" & txtstaying.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New Rpt_DaySald
        Dim txt, txtNameAr, txtNameEn As TextObject

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txt = rpt.Section1.ReportObjects("Text16")
        txt.Text = "تقرير بمرتجعات المبيعات"
        txtNameAr = rpt.Section1.ReportObjects("Text2")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("Text18")
        txtNameEn.Text = NameEnCompany
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "تقرير بمرتجعات المبيعات"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Sub InsertForPrint(ByVal BILL_NO As String, ByVal Vendorname As String, ByVal bill_date As String, ByVal billtime As String,
                       ByVal totalpricebeforedisc As String, ByVal disc As String, ByVal totalpriceafterdisc As String, ByVal BEY As String,
                       ByVal STAYING As String, ByVal Stat As String, ByVal TotalBeforeDisc As String, ByVal TotalDisc As String,
                       ByVal TotalafterDisc As String, ByVal TotalBay As String, ByVal Totalstaying As String)

        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "SP_PrintDaySalesAll"
        cmd.Parameters.Clear()

        cmd.Parameters.AddWithValue("@BILL_NO", BILL_NO)
        cmd.Parameters.AddWithValue("@Vendorname", Vendorname)
        cmd.Parameters.AddWithValue("@bill_date", bill_date)
        cmd.Parameters.AddWithValue("@billtime", billtime)
        cmd.Parameters.AddWithValue("@totalpricebeforedisc", totalpricebeforedisc)
        cmd.Parameters.AddWithValue("@disc", disc)
        cmd.Parameters.AddWithValue("@totalpriceafterdisc", totalpriceafterdisc)
        cmd.Parameters.AddWithValue("@BEY", BEY)
        cmd.Parameters.AddWithValue("@STAYING", STAYING)
        cmd.Parameters.AddWithValue("@Stat", Stat)
        cmd.Parameters.AddWithValue("@TotalBeforeDisc", TotalBeforeDisc)
        cmd.Parameters.AddWithValue("@TotalDisc", TotalDisc)
        cmd.Parameters.AddWithValue("@TotalafterDisc", TotalafterDisc)
        cmd.Parameters.AddWithValue("@TotalBay", TotalBay)
        cmd.Parameters.AddWithValue("@Totalstaying", Totalstaying)

        cmd.ExecuteNonQuery()

    End Sub
    Dim ItmID As String
    Dim Vendor As String
    Sub insertxt()
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        ItmID = DataGridView1.SelectedRows(0).Cells(0).Value
        Vendor = DataGridView1.SelectedRows(0).Cells(1).Value

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * from IM_Bsal where BILL_NO=N'" & ItmID & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            txtbillno.Text = dr("BILL_NO")
            txtvendores.Text = dr("Vendorname")
            txtbilldate.Text = Cls.R_date(dr("bill_date"))
            txtbilltime.Text = dr("billtime")
            txttotalpeforedisc.Text = dr("totalpricebeforedisc")
            txtdisc1.Text = dr("disc")
            txttotalafterdisc1.Text = dr("totalpriceafterdisc")
            txtpaying1.Text = dr("BEY")
            txt_staying1.Text = dr("STAYING")
            txtCreditPrevious.Text = dr("CreditPrevious").ToString
            txtDebitPrevious.Text = dr("DebitPrevious").ToString
            txtCreditCurrent.Text = dr("CreditCurrent").ToString
            txtDebitCurrent.Text = dr("DebitCurrent").ToString
        End If
        AmountDebitCreditPrevious = Val(txtCreditPrevious.Text) - Val(txtDebitPrevious.Text)
        AmountDebitCreditAfter = Val(txtCreditCurrent.Text) - Val(txtDebitCurrent.Text)
        ItmID = ""

    End Sub

    Private Sub Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button3.Click
        If DataGridView2.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView2.Rows.Count - 1
            S = "insert into PrintSalesPurchases(Company_Branch_ID,itm_id,itm_cat,itm_name,price,qu,Unity,totalprice,store,BILL_NO,Vendorname,bill_date,billtime,totalpricebeforedisc,disc,totalpriceafterdisc,BEY,STAYING,Delivery_Date,vnamntcredit,vnamntdebit,TotalCreditor,TotalDebtor,Name8,Name9)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView2.Rows(i).Cells(0).Value & "',N'" & DataGridView2.Rows(i).Cells(1).Value & "',N'" & DataGridView2.Rows(i).Cells(2).Value & "',N'" & DataGridView2.Rows(i).Cells(3).Value & "',N'" & DataGridView2.Rows(i).Cells(4).Value & "',N'" & DataGridView2.Rows(i).Cells(5).Value & "',N'" & DataGridView2.Rows(i).Cells(6).Value & "',N'" & DataGridView2.Rows(i).Cells(7).Value & "',N'" & txtbillno.Text & "',N'" & txtvendores.Text & "',N'" & txtbilldate.Text & "',N'" & Cls.Get_Time_AM_PM(txtbilltime.Text.ToString) & "',N'" & txttotalpeforedisc.Text & "',N'" & txtdisc1.Text & "',N'" & txttotalafterdisc1.Text & "',N'" & txtpaying1.Text & "',N'" & txt_staying1.Text & "',N'" & DefaultCurrencyProgram & "',N'" & txtCreditCurrent.Text & "',N'" & txtDebitCurrent.Text & "',N'" & txtCreditPrevious.Text & "',N'" & txtDebitPrevious.Text & "',N'" & AmountDebitCreditPrevious & "',N'" & AmountDebitCreditAfter & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        If ShowCustomerAddressSales = "YES" Then
            GetCustomerAddress()
        End If

        AddReportView()

        'Dim rpt As New Rpt_SalesPurchases
        Dim txt, txtname, txtNameAr, txtNameEn, txtCmpAddress, txtCmpEmail, txtCmpTel, txtCmpMobile, txtCmpFax, txtCmpUnderBILL, txtCmpAddressBill, txtCommercialRecord, txtTaxCard, txtSaleTax, txtDiscountTaxs, txtSaleTaxText, txtDiscountTaxText, txtAltfiqith, txtCMPNameDown, txtCustomerAddressSales, txtCustomerTel, txtDelegateName, txtEndorsement, txtProgramNameBill, txtObjectUserName, txtObjectCommercialAndIndustrialProfitsTax As TextObject


        Dim rpt
        If PrintSmall = "YES" Then
            If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                rpt = New Rpt_SoldSmall
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                rpt = New Rpt_SoldSmall_2
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                rpt = New Rpt_SoldSmall_3
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                rpt = New Rpt_SoldSmall_4
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                rpt = New Rpt_SoldSmall_5
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                rpt = New Rpt_SoldSmall_6
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                rpt = New Rpt_SoldSmall_7
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                rpt = New Rpt_SoldSmall_8
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                rpt = New Rpt_SoldSmall_9
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                rpt = New Rpt_SoldSmall_10
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                rpt = New Rpt_SoldSmall_11
                If ShowCustomerAddressSales = "YES" Then
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = "عنوان العميل" + ": " + CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = "رقم تليفون العميل" + ": " + CustomerTel
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                End If
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                rpt = New Rpt_SoldSmall_12
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                rpt = New Rpt_SoldSmall_13
                txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                txtDelegateName.Text = cmbEmployees.Text
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                rpt = New Rpt_SoldSmall_14
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                rpt = New Rpt_SoldSmall_15
                txtObjectUserName = rpt.Section1.ReportObjects("UserName")
                txtObjectUserName.Text = UserName
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                rpt = New Rpt_SoldSmall_16
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                rpt = New Rpt_SoldSmall_17
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                rpt = New Rpt_SoldSmall_18
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                rpt = New Rpt_SoldSmall_19
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                rpt = New Rpt_SoldSmall_20
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                rpt = New Rpt_SalesBill_21
                If CommercialAndIndustrialProfitsTax = 0 Then
                    txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                    txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                End If
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                rpt = New Rpt_SalesBill_22
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                rpt = New Rpt_SalesBill_23
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                rpt = New Rpt_SalesBill_24
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                rpt = New Rpt_SalesBill_25
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                rpt = New Rpt_SalesBill_26
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                rpt = New Rpt_SalesBill_27
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                rpt = New Rpt_SalesBill_28
            End If
            If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                rpt = New Rpt_SalesBill_29
            End If
        Else
            If ColorWithItems = "YES" Then
                rpt = New Rpt_SalesBill_4
            Else
                If DesignAnotherSalesInvoice = "تصميم رقم 1" Then
                    rpt = New Rpt_SalesBill
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 2" Then
                    rpt = New Rpt_SalesBill_2
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 3" Then
                    rpt = New Rpt_SalesBill_3
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 4" Then
                    rpt = New Rpt_SalesBill_4
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 5" Then
                    rpt = New Rpt_SalesBill_5
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 6" Then
                    rpt = New Rpt_SalesBill_6
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 7" Then
                    rpt = New Rpt_SalesBill_7
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 8" Then
                    rpt = New Rpt_SalesBill_8
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 9" Then
                    rpt = New Rpt_SalesBill_9
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 10" Then
                    rpt = New Rpt_SalesBill_10
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 11" Then
                    rpt = New Rpt_SalesBill_10
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 12" Then
                    rpt = New Rpt_SalesBill_Delegate
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 13" Then
                    rpt = New Rpt_SalesBill_Delegate_2
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                    txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                    txtDelegateName.Text = cmbEmployees.Text
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 14" Then
                    rpt = New Rpt_SalesBill_14
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 15" Then
                    rpt = New Rpt_SalesBill_15
                    txtEndorsement = rpt.Section1.ReportObjects("txtEndorsement")
                    txtEndorsement.Text = CMPEndorsement
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 16" Then
                    rpt = New Rpt_SalesBill_16
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 17" Then
                    rpt = New Rpt_SalesBill_17
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 18" Then
                    rpt = New Rpt_SalesBill_18
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 19" Then
                    rpt = New Rpt_SalesBill_19
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 20" Then
                    rpt = New Rpt_SalesBill_20
                    If CustomerAddress <> "" Then
                        txtCustomerAddressSales = rpt.Section1.ReportObjects("txtCustomerAddressSales")
                        txtCustomerAddressSales.Text = CustomerAddress
                        txtCustomerTel = rpt.Section1.ReportObjects("txtCustomerTel")
                        txtCustomerTel.Text = CustomerTel
                        txtDelegateName = rpt.Section1.ReportObjects("txtDelegateName")
                        txtDelegateName.Text = cmbEmployees.Text
                    End If
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 21" Then
                    rpt = New Rpt_SalesBill_21
                    If CommercialAndIndustrialProfitsTax = 0 Then
                        txtObjectCommercialAndIndustrialProfitsTax = rpt.Section1.ReportObjects("txtCommercialAndIndustrialProfitsTax")
                        txtObjectCommercialAndIndustrialProfitsTax.Text = ""
                    End If
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 22" Then
                    rpt = New Rpt_SalesBill_22
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 23" Then
                    rpt = New Rpt_SalesBill_23
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 24" Then
                    rpt = New Rpt_SalesBill_24
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 25" Then
                    rpt = New Rpt_SalesBill_25
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 26" Then
                    rpt = New Rpt_SalesBill_26
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 27" Then
                    rpt = New Rpt_SalesBill_27
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 28" Then
                    rpt = New Rpt_SalesBill_28
                End If
                If DesignAnotherSalesInvoice = "تصميم رقم 29" Then
                    rpt = New Rpt_SalesBill_29
                End If
            End If
            txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
            txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(txttotalafterdisc1.Text)
            txtCMPNameDown = rpt.Section1.ReportObjects("txtCMPNameDown")
            txtCMPNameDown.Text = CMPNameDown
        End If
        If PrintSmall = "A5" Then
            If SalesPricePublic = "YES" Then
                rpt = New Rpt_SalesBill_A5_PricePublic
            Else
                rpt = New Rpt_SalesBill_A5_1
            End If
            If ShowTax = "YES" Then
                rpt = New Rpt_SalesBill_A5_Nubaria_Tax
                txtSaleTax = rpt.Section1.ReportObjects("txtSaleTax")
                    txtSaleTax.Text = ""
                    txtSaleTaxText = rpt.Section1.ReportObjects("txtSaleTaxText")
                txtSaleTaxText.Text = ""
            End If
            txtAltfiqith = rpt.Section1.ReportObjects("txtAltfiqith")
            txtAltfiqith.Text = Cls_Altfiqith.NumToTxt(txttotalafterdisc1.Text)
        End If
        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txt = rpt.Section1.ReportObjects("txtTitelAddress")
        txt.Text = "تقرير مرتجعات مبيعات"
        txtname = rpt.Section1.ReportObjects("txtName")
        txtname.Text = "أسم العميل"
        txtNameAr = rpt.Section1.ReportObjects("txtTitelAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
        txtNameEn.Text = NameEnCompany
        txtCmpAddress = rpt.Section1.ReportObjects("txtAddress")
        txtCmpAddress.Text = CmpAddress
        txtCmpEmail = rpt.Section1.ReportObjects("txtEmail")
        txtCmpEmail.Text = CmpEmail
        txtCmpTel = rpt.Section1.ReportObjects("txtTel")
        txtCmpTel.Text = CmpTel
        txtCmpMobile = rpt.Section1.ReportObjects("txtMobile")
        txtCmpMobile.Text = CmpMobile
        If PrintSmall <> "YES" Then
            txtCmpFax = rpt.Section1.ReportObjects("txtFax")
            txtCmpFax.Text = CmpFax
        End If

        If HideProgramNameBill = "YES" Then
            txtProgramNameBill = rpt.Section1.ReportObjects("txtProgramNameBill")
            txtProgramNameBill.Text = ""
        Else
            txtProgramNameBill = rpt.Section1.ReportObjects("txtProgramNameBill")
            txtProgramNameBill.Text = ProgramNameBill
        End If

        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "تقرير مرتجعات مبيعات"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub
    Sub ForPrintAll(ByVal itm_id As String, ByVal itm_cat As String, ByVal itm_name As String, ByVal price As String, ByVal qu As String, ByVal totalprice As String, ByVal stors As String, _
                ByVal BILL_NO As String, ByVal Vendorname As String, ByVal bill_date As String, ByVal billtime As String, _
                ByVal totalpricebeforedisc As String, ByVal disc As String, ByVal totalpriceafterdisc As String, ByVal BEY As String, ByVal STAYING As String)


        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "Sp_PrintSalesPurchases"
        cmd.Parameters.Clear()

        cmd.Parameters.AddWithValue("@itm_id", itm_id)
        cmd.Parameters.AddWithValue("@itm_cat", itm_cat)
        cmd.Parameters.AddWithValue("@itm_name", itm_name)
        cmd.Parameters.AddWithValue("@price", price)
        cmd.Parameters.AddWithValue("@qu", qu)
        cmd.Parameters.AddWithValue("@totalprice", totalprice)
        cmd.Parameters.AddWithValue("@store", stors)
        cmd.Parameters.AddWithValue("@BILL_NO", BILL_NO)
        cmd.Parameters.AddWithValue("@Vendorname", Vendorname)
        cmd.Parameters.AddWithValue("@bill_date", bill_date)
        cmd.Parameters.AddWithValue("@billtime", billtime)
        cmd.Parameters.AddWithValue("@totalpricebeforedisc", totalpricebeforedisc)
        cmd.Parameters.AddWithValue("@disc", disc)
        cmd.Parameters.AddWithValue("@totalpriceafterdisc", totalpriceafterdisc)
        cmd.Parameters.AddWithValue("@BEY", BEY)
        cmd.Parameters.AddWithValue("@STAYING", STAYING)

        cmd.ExecuteNonQuery()

    End Sub

    Private Sub btnEmployees_Click(sender As Object, e As EventArgs) Handles btnEmployees.Click
        PanelEmployees.Top = 92
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        PanelEmployees.Top = 10000
    End Sub

    Private Sub btnViewBill_Click(sender As Object, e As EventArgs) Handles btnViewBill.Click
        Panel2.Top = 600
        Panel2.Dock = DockStyle.None
    End Sub

    Private Sub GetCustomerAddress()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select addr,tel1 from Customers where Vendorname=N'" & cmbvendornameshow.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                CustomerAddress = dr(0).ToString
                CustomerTel = dr(1).ToString
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class
