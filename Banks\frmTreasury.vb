﻿Public Class frmTreasury

    Private Sub frmTreasury_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        On Error Resume Next
        If Action = "Edit" Then
            FillData()
            btnSave.Text = "تعديل"
        Else
            MAXRECORD()
            cmbTypeMovement.Focus()
        End If
        FILLCOMBOBOX()
    End Sub

    Private Sub MAXRECORD()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "select * from Cashier"
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            txtSeries.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(CSHID As float)) as mb FROM Cashier"
            dr = cmd.ExecuteReader
            dr.Read()
            Dim sh As Long
            sh = dr("mb")
            txtSeries.Text = sh + 1
        End If
    End Sub

    Private Sub FillComboBox()
        cmbBank.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select distinct Bank from Banks"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            cmbBank.Items.Add(dr("Bank"))
        Loop
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub txtDocumentNumber_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtDocumentNumber.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtDocumentNumber)
    End Sub

    Private Sub txtDebtor_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtDebtor.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtDebtor)
        SumTotal()
    End Sub

    Private Sub txtCreditor_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtCreditor.TextChanged
        On Error Resume Next
        MyVars.CheckNumber(txtCreditor)
        SumTotal()
    End Sub

    Private Sub cmbTypeMovement_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbTypeMovement.KeyUp
        If e.KeyCode = 13 Then
            txtDocumentNumber.Focus()
        End If
    End Sub

    Private Sub txtDocumentNumber_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtDocumentNumber.KeyUp
        If e.KeyCode = 13 Then
            cmbPaymentMethod.Focus()
        End If
    End Sub

    Private Sub cmbPaymentMethod_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbPaymentMethod.KeyUp
        If e.KeyCode = 13 Then
            txtDebtor.Focus()
        End If
    End Sub

    Private Sub txtDebtor_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtDebtor.KeyUp
        If e.KeyCode = 13 Then
            txtCreditor.Focus()
        End If
    End Sub

    Private Sub txtCreditor_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtCreditor.KeyUp
        If e.KeyCode = 13 Then
            dtpDateCollection.Focus()
        End If
    End Sub

    Private Sub dtpDateCollection_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtpDateCollection.KeyUp
        If e.KeyCode = 13 Then
            cmbBank.Focus()
        End If
    End Sub

    Private Sub cmbBank_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles cmbBank.KeyUp
        If e.KeyCode = 13 Then
            txtNotes.Focus()
        End If
    End Sub

    Private Sub txtNotes_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtNotes.KeyUp
        If e.KeyCode = 13 Then
            btnSave.PerformClick()
        End If
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        If ValidateTextAdd() = False Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If Action = "Edit" Then
            cmd.CommandText = "delete From  Cashier where CSHID =N'" & EditItmId & "'" : cmd.ExecuteNonQuery()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Cashier(CSHID,DateMovement,TypeMovement,DocumentNumber,PaymentMethod,PreviousBalance,Debtor,Creditor,CurrentBalance,DateCollection,Bank,Notes,UserName) values ("
        S = S & "N'" & txtSeries.Text & "',N'" & Cls.C_date(dtpDateMovement.Text) & "',N'" & cmbTypeMovement.Text & "',N'" & txtDocumentNumber.Text & "',"
        S = S & "N'" & cmbPaymentMethod.Text & "',N'" & txtPreviousBalance.Text & "',N'" & txtDebtor.Text & "',N'" & txtCreditor.Text & "',"
        S = S & "N'" & txtCurrentBalance.Text & "',N'" & Cls.C_date(dtpDateCollection.Text) & "',N'" & cmbBank.Text & "',N'" & txtNotes.Text & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        If Action = "Edit" Then
            Action = "Add"
            Me.Close()
            frmShowTreasury.btnShow.PerformClick()
        Else
            MsgBox("تمت عملية الحفظ بنجاح", MsgBoxStyle.Information)
          ClearSave()
            MAXRECORD()
            FillComboBox()
        End If
    End Sub

    Function ValidateTextAdd() As Boolean
        If cmbTypeMovement.Text = "صرف" Or cmbTypeMovement.Text = "ايداع" Then
        Else
            If cmbTypeMovement.Text = "" Then
                MsgBox("فضلا أدخل نوع الحركة", MsgBoxStyle.Exclamation) : cmbTypeMovement.Focus()
                Exit Function
            End If
            MsgBox("فضلا أدخل نوع الحركة", MsgBoxStyle.Exclamation) : cmbTypeMovement.Focus() : Return False
        End If
        If cmbPaymentMethod.Text = "" Then MsgBox("فضلا ادخل طريقة الدفع", MsgBoxStyle.Exclamation) : cmbPaymentMethod.Focus() : Return False
        If txtDocumentNumber.Text = "" Then MsgBox("فضلا ادخل رقم المستند", MsgBoxStyle.Exclamation) : txtDocumentNumber.Focus() : Return False
        If cmbBank.Text = "" Then MsgBox("فضلا ادخل أسم البنك", MsgBoxStyle.Exclamation) : cmbBank.Focus() : Return False
        Return True
    End Function

    Private Sub ClearSave()
        Cls.clear(Me)
        dtpDateMovement.Focus()
    End Sub

    Private Sub FillData()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select CSHID,DateMovement,TypeMovement,DocumentNumber,PaymentMethod,PreviousBalance,Debtor,Creditor,CurrentBalance,DateCollection,Bank,Notes FROM Cashier where CSHID =N'" & EditItmId & "'"
        dr = cmd.ExecuteReader : dr.Read()
        Dim XCSHID, XDateMovement, XTypeMovement, XDocumentNumber, XPaymentMethod, XPreviousBalance, XDebtor, XCreditor, XCurrentBalance, XDateCollection, XBank, XNotes As String
        XCSHID = dr("CSHID")
        XDateMovement = dr("DateMovement")
        XTypeMovement = dr("TypeMovement")
        XPaymentMethod = dr("PaymentMethod")
        XDocumentNumber = dr("DocumentNumber")
        XPreviousBalance = dr("PreviousBalance")
        XDebtor = dr("Debtor")
        XCreditor = dr("Creditor")
        XCurrentBalance = dr("CurrentBalance")
        XDateCollection = dr("DateCollection")
        XBank = dr("Bank")
        XNotes = dr("Notes")


        txtSeries.Text = XCSHID
        dtpDateMovement.Text = XDateMovement
        cmbTypeMovement.Text = XTypeMovement
        txtDocumentNumber.Text = XDocumentNumber
        cmbPaymentMethod.Text = XPaymentMethod
        txtPreviousBalance.Text = XPreviousBalance
        txtDebtor.Text = XDebtor
        txtCreditor.Text = XCreditor
        txtCurrentBalance.Text = XCurrentBalance
        dtpDateCollection.Text = XDateCollection
        cmbBank.Text = XBank
        txtNotes.Text = XNotes
    End Sub

    Private Sub cmbTypeMovement_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbTypeMovement.SelectedIndexChanged
        On Error Resume Next
        Select Case cmbTypeMovement.Text
            Case "ايداع"
                txtCreditor.Enabled = True
                txtDebtor.Enabled = False
            Case "صرف"
                txtCreditor.Enabled = False
                txtDebtor.Enabled = True
        End Select
    End Sub

    Private Sub SumTotal()
        txtCurrentBalance.Text = Format(Val(txtPreviousBalance.Text) + Val(txtCreditor.Text) - Val(txtDebtor.Text), "Fixed")
    End Sub

    Private Sub txtPreviousBalance_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtPreviousBalance.TextChanged
        On Error Resume Next
        SumTotal()
    End Sub

    Private Sub txtCurrentBalance_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtCurrentBalance.TextChanged
        On Error Resume Next
        SumTotal()
    End Sub

    Private Sub txtSeries_TextChanged(sender As Object, e As EventArgs) Handles txtSeries.TextChanged
        MyVars.CheckNumber(txtSeries)
    End Sub
End Class