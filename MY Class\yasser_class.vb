﻿Imports System.Text
Imports System.Security.Cryptography
Imports VB = Microsoft.VisualBasic
Imports System.IO
Imports System.Data.SqlClient

Public Class yasser_class
    Dim connection As SqlConnection
    Dim command As SqlCommand
    Dim adapter As New SqlDataAdapter()
    Dim ds As New DataSet()

    Function R_date(ByVal dt As String) As Date
        On Error Resume Next
        Dim dy As String
        Dim mn As String
        Dim yr As String
        dy = Mid(dt, 7, 2)
        If Len(Trim(dy)) = 1 Then dy = "0" + Trim(Str(dy))
        mn = Mid(dt, 5, 2)
        If Len(Trim(mn)) = 1 Then mn = "0" + Trim(Str(mn))
        yr = Mid(dt, 1, 4)
        Return Convert.ToDateTime(yr & "/" & mn & "/" & dy)
    End Function
    Function get_time(ByVal formatted As Boolean) As String
        Try
            Dim tm As String
            Select Case formatted
                Case True
                    Dim h, m, s
                    h = Now.Hour.ToString
                    If Len(h) = 1 Then h = "0" & h
                    m = Now.Minute.ToString
                    If Len(m) = 1 Then m = "0" & m
                    s = Now.Second.ToString
                    If Len(s) = 1 Then s = "0" & s
                    tm = h & " : " & m & " : " & s
                Case False
                    Dim h, m, s
                    h = Now.Hour.ToString
                    If Len(h) = 1 Then h = "0" & h
                    m = Now.Minute.ToString
                    If Len(m) = 1 Then m = "0" & m
                    s = Now.Second.ToString
                    If Len(s) = 1 Then s = "0" & s
                    tm = h & m & s
            End Select
            Return tm
        Catch ex As Exception
        End Try
    End Function

    Function Get_Time_AM_PM(ByVal Time24 As String) As String
        Try
            Dim split As String() = New String() {":"}
            Dim itemsSplit As String() = Time24.Split(split, StringSplitOptions.None)
            Dim hh As String = itemsSplit(0).ToString()
            Dim mm As String = itemsSplit(1).ToString()
            Dim tt As String = itemsSplit(2).ToString()

            Dim theTime = New Date(2020, 1, 1, hh, mm, tt)
            Dim tm As String = theTime.ToString("hh:mm tt", Globalization.CultureInfo.InvariantCulture)

            Return tm
        Catch ex As Exception
        End Try
    End Function

    Function SlpitTime24Hour(ByVal Time1 As String) As String
        Try
            Dim AMPM As String = ""
            Dim Time As String = Time1
            Dim split As String() = New String() {":"}
            Dim itemsSplit As String() = Time.Split(split, StringSplitOptions.None)
            Dim Hour As String = itemsSplit(0).ToString()
            Dim Minute As String = itemsSplit(1).ToString()
            Dim Second As String = itemsSplit(2).ToString()

            Dim Time2 As String = Second
            Dim split2 As String() = New String() {" "}
            Dim itemsSplit2 As String() = Time2.Split(split2, StringSplitOptions.None)
            AMPM = itemsSplit2(1).ToString()

            If AMPM = "م" Then : AMPM = "PM" : End If
            If AMPM = "ص" Then : AMPM = "AM" : End If
            If AMPM = "pm" Then : AMPM = "PM" : End If
            If AMPM = "am" Then : AMPM = "AM" : End If

            If Hour = "12" And AMPM = "AM" Then : Hour = "00" : End If
            If Hour = "01" Or Hour = "1" And AMPM = "AM" Then : Hour = "01" : End If
            If Hour = "02" Or Hour = "2" And AMPM = "AM" Then : Hour = "02" : End If
            If Hour = "03" Or Hour = "3" And AMPM = "AM" Then : Hour = "03" : End If
            If Hour = "04" Or Hour = "4" And AMPM = "AM" Then : Hour = "04" : End If
            If Hour = "05" Or Hour = "5" And AMPM = "AM" Then : Hour = "05" : End If
            If Hour = "06" Or Hour = "6" And AMPM = "AM" Then : Hour = "06" : End If
            If Hour = "07" Or Hour = "7" And AMPM = "AM" Then : Hour = "07" : End If
            If Hour = "08" Or Hour = "8" And AMPM = "AM" Then : Hour = "08" : End If
            If Hour = "09" Or Hour = "9" And AMPM = "AM" Then : Hour = "09" : End If
            If Hour = "10" And AMPM = "AM" Then : Hour = "10" : End If
            If Hour = "11" And AMPM = "AM" Then : Hour = "11" : End If
            If Hour = "12" And AMPM = "PM" Then : Hour = "12" : End If
            If Hour = "01" Or Hour = "1" And AMPM = "PM" Then : Hour = "13" : End If
            If Hour = "02" Or Hour = "2" And AMPM = "PM" Then : Hour = "14" : End If
            If Hour = "03" Or Hour = "3" And AMPM = "PM" Then : Hour = "15" : End If
            If Hour = "04" Or Hour = "4" And AMPM = "PM" Then : Hour = "16" : End If
            If Hour = "05" Or Hour = "5" And AMPM = "PM" Then : Hour = "17" : End If
            If Hour = "06" Or Hour = "6" And AMPM = "PM" Then : Hour = "18" : End If
            If Hour = "07" Or Hour = "7" And AMPM = "PM" Then : Hour = "19" : End If
            If Hour = "08" Or Hour = "8" And AMPM = "PM" Then : Hour = "20" : End If
            If Hour = "09" Or Hour = "9" And AMPM = "PM" Then : Hour = "21" : End If
            If Hour = "10" And AMPM = "PM" Then : Hour = "22" : End If
            If Hour = "11" And AMPM = "PM" Then : Hour = "23" : End If
            TimeAmPm24Hour = Hour + " : " + Minute.Trim + " : " + "00"
            Return TimeAmPm24Hour
        Catch ex As Exception
        End Try
    End Function

    Function GenerateHash(ByVal SourceText As String) As String
        Try
            Dim Ue As New UnicodeEncoding()
            Dim ByteSourceText() As Byte = Ue.GetBytes(SourceText)
            Dim Md5 As New MD5CryptoServiceProvider()
            Dim ByteHash() As Byte = Md5.ComputeHash(ByteSourceText)
            Return Convert.ToBase64String(ByteHash)
        Catch ex As Exception
        End Try
    End Function

    Sub fill_combo(ByVal Table_Name As String, ByVal Field_Name As String, ByVal combo As ComboBox)
        Try
            combo.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " order by 1"
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                combo.Items.Add(Trim(dr(0)))
            Loop
        Catch ex As Exception
        End Try
    End Sub

    Public Sub fill_combo_Top100(ByVal Table_Name As String, ByVal Field_ID As String, ByVal Field_Name As String, ByVal ORDER_BY As String, ByVal combo As ComboBox)
        Using conn As New SqlConnection(constring)
            Using cmd As New SqlCommand()
                cmd.Connection = conn
                ' تحميل أول 100 عميل فقط في البداية
                cmd.CommandText = "SELECT TOP (100)  " & Field_ID & ", " & Field_Name & " " &
                                    "FROM " & Table_Name & " " &
                                    "" & ORDER_BY & ""

                Try
                    conn.Open()
                    Dim dt As New DataTable()
                    dt.Load(cmd.ExecuteReader())

                    combo.DisplayMember = "" & Field_Name & ""
                    combo.ValueMember = "" & Field_ID & ""
                    combo.DataSource = dt

                Catch ex As Exception
                    MessageBox.Show("خطأ في تحميل العملاء: " & ex.Message)
                End Try
            End Using
        End Using
    End Sub


    Public Function SearchTextBY(searchText As String, ByVal Table_Name As String, ByVal Field_ID As String, ByVal Field_Name As String, comboBox As ComboBox) As Boolean
        If searchText.Length < 3 Then
            Return False
        End If

        Using conn As New SqlConnection(constring)
            Using cmd As New SqlCommand()
                cmd.Connection = conn

                ' بناء جملة SQL ديناميكياً باستخدام المتغيرات
                cmd.CommandText = $"SELECT TOP (200)  {Field_ID}, {Field_Name} " &
                                $"FROM {Table_Name} " &
                                $"WHERE {Field_ID} LIKE @SearchText " &
                                $"ORDER BY {Field_ID}"

                cmd.Parameters.AddWithValue("@SearchText", "%" & searchText & "%")

                Try
                    conn.Open()
                    Dim dt As New DataTable()
                    dt.Load(cmd.ExecuteReader())

                    If dt.Rows.Count > 0 Then
                        ' تعيين خصائص ComboBox
                        comboBox.DisplayMember = Field_ID
                        comboBox.ValueMember = Field_Name
                        comboBox.DataSource = dt

                        ' حفظ النص المكتوب
                        Dim currentText As String = searchText
                        comboBox.DroppedDown = True
                        comboBox.Text = currentText
                        comboBox.SelectionStart = currentText.Length

                        Return True
                    End If

                    Return False

                Catch ex As Exception
                    MessageBox.Show("خطأ في البحث: " & ex.Message)
                    Return False
                End Try
            End Using
        End Using
    End Function


    Sub fill_combo_Branch(ByVal Table_Name As String, ByVal Field_Name As String, ByVal combo As ComboBox)
        Try
            combo.DataSource = Nothing
            combo.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " order by 1"
            Else
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & "  where Company_Branch_ID=N'" & Company_Branch_ID & "'  or Company_Branch_ID =N'0' order by 1"
            End If
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                combo.Items.Add(Trim(dr(0)))
            Loop
        Catch ex As Exception
        End Try
    End Sub

    Sub fill_combo_Where(ByVal Table_Name As String, ByVal Field_Name As String, ByVal Field_Name_where As String, ByVal Text As String, ByVal combo As ComboBox)
        Try
            combo.DataSource = Nothing
            combo.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where " & Field_Name_where & " =N'" & Text & "' order by 1"
            Else
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where " & Field_Name_where & " =N'" & Text & "' and Company_Branch_ID = N'" & Company_Branch_ID & "'  or Company_Branch_ID =N'0' order by 1"
            End If
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                combo.Items.Add(Trim(dr(0)))
            Loop
        Catch ex As Exception
        End Try
    End Sub

    Sub fill_combo_Stores_Where(ByVal Table_Name As String, ByVal Field_Name As String, ByVal Field_Name_where As String, ByVal Text As String, ByVal combo As ComboBox)
        Try
            combo.DataSource = Nothing
            combo.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where " & Field_Name_where & " =N'" & Text & "' order by 1"
            Else
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where " & Field_Name_where & " =N'" & Text & "' and Company_Branch_ID = N'" & Company_Branch_ID & "'  or Company_Branch_ID =N'0' order by 1"
                'cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where " & Field_Name_where & " =N'" & Text & "' and Stores = N'" & StoresName & "' order by 1"
            End If
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                combo.Items.Add(Trim(dr(0)))
            Loop
        Catch ex As Exception
        End Try
    End Sub

    Sub fill_combo_Where_More(ByVal Table_Name As String, ByVal Field_Name As String, ByVal crtria As String, ByVal combo As ComboBox)
        Try
            combo.DataSource = Nothing
            combo.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where " & crtria & " order by 1"
            Else
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where Company_Branch_ID = N'" & Company_Branch_ID & "'  or Company_Branch_ID =N'0' and " & crtria & " order by 1"
            End If
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                combo.Items.Add(Trim(dr(0)))
            Loop
        Catch ex As Exception
        End Try
    End Sub

    Sub fill_combo_orderby(ByVal crtria As String, ByVal combo As ComboBox)
        Try
            combo.DataSource = Nothing
            combo.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "" & crtria & ""
            Else
                cmd.CommandText = "" & crtria & ""
            End If
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                combo.Items.Add(Trim(dr(0)))
            Loop
        Catch ex As Exception
        End Try
    End Sub

    Sub fill_combo_Stores_Where_More(ByVal Table_Name As String, ByVal Field_Name As String, ByVal crtria As String, ByVal combo As ComboBox)
        Try
            combo.DataSource = Nothing
            combo.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where " & crtria & " order by 1"
            Else
                cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where Company_Branch_ID = N'" & Company_Branch_ID & "'  or Company_Branch_ID =N'0' and " & crtria & " order by 1"

                'cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " where Stores = N'" & StoresName & "' and " & crtria & " order by 1"
            End If
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                combo.Items.Add(Trim(dr(0)))
            Loop
        Catch ex As Exception
        End Try
    End Sub

    Function Select_SUM_Value(ByVal Table_Name As String, ByVal Field_Name As String, ByVal Field_Name_where As String, ByVal Text As String) As Double
        Try
            Dim Return_Value As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "Select sum(" & Field_Name & ") from " & Table_Name & " where " & Field_Name_where & " =N'" & Text & "'" : dr = cmd.ExecuteReader : dr.Read()
            Else
                cmd.CommandText = "Select sum(" & Field_Name & ") from " & Table_Name & " where " & Field_Name_where & " =N'" & Text & "' and Company_Branch_ID =N'" & Company_Branch_ID & "'" : dr = cmd.ExecuteReader : dr.Read()
            End If
            If dr(0) Is DBNull.Value Then Return_Value = 0 Else Return_Value = dr(0).ToString
            Return_Value = Math.Round(Return_Value, 2)
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function

    Function Select_SUM_Value_More(ByVal Table_Name As String, ByVal Field_Name As String, ByVal crtria As String, ByVal Text As String) As Double
        Try
            Dim Return_Value As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "Select sum(" & Field_Name & ") from " & Table_Name & " where " & crtria : dr = cmd.ExecuteReader : dr.Read()
            Else
                cmd.CommandText = "Select sum(" & Field_Name & ") from " & Table_Name & " where Company_Branch_ID = N'" & Company_Branch_ID & "'  or Company_Branch_ID =N'0' and " & crtria : dr = cmd.ExecuteReader : dr.Read()
            End If
            If dr(0) Is DBNull.Value Then Return_Value = 0 Else Return_Value = dr(0).ToString
            Return_Value = Math.Round(Return_Value, 2)
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function

    Function Select_Min_Value(ByVal Table_Name As String, ByVal Field_Name As String) As Double
        Try
            Dim Return_Value As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "Select min(" & Field_Name & ") from " & Table_Name & ""
            Else
                cmd.CommandText = "Select min(" & Field_Name & ") from " & Table_Name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0' "
            End If
            dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Return_Value = 0 Else Return_Value = dr(0).ToString
            Return_Value = Math.Round(Return_Value, 2)
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function

    Function Select_Max_Value(ByVal Table_Name As String, ByVal Field_Name As String) As Double
        Try
            Dim Return_Value As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "Select MAX(" & Field_Name & ") from " & Table_Name & ""
            Else
                cmd.CommandText = "Select MAX(" & Field_Name & ") from " & Table_Name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0' "
            End If
            dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Return_Value = 0 Else Return_Value = dr(0).ToString
            Return_Value = Math.Round(Return_Value, 2)
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function


    Function Select_SUM_Value_More(ByVal Table_Name As String, ByVal Field_Name As String, ByVal crtria As String) As Double
        Try
            Dim Return_Value As Double
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "Select sum(" & Field_Name & ")  AS Expr1 from " & Table_Name & " where " & crtria : dr = cmd.ExecuteReader : dr.Read()
            Else
                cmd.CommandText = "Select sum(" & Field_Name & ")  AS Expr1 from " & Table_Name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0'  and " & crtria : dr = cmd.ExecuteReader : dr.Read()
            End If
            If dr(0) Is DBNull.Value Then Return_Value = 0 Else Return_Value = dr(0).ToString
            Return_Value = Math.Round(Return_Value, 2)
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function

    Function Get_Select_Grid_S_OrderBy(ByVal Field_Name As String, ByVal Table_Name As String, ByVal crtria As String) As String
        Try
            Dim Return_Value As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where " & crtria & " order by 1"
            Else
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0'  and " & crtria & " order by 1"

                'Return_Value = "Select " & Field_Name & " from " & Table_Name & " where Stores =N'" & StoresName & "' and " & crtria & " order by 1"
            End If
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function

    Function Get_Select_Grid_S(ByVal Field_Name As String, ByVal Table_Name As String, ByVal crtria As String) As String
        Try
            Dim Return_Value As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where " & crtria
            Else
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where " & crtria & " and Company_Branch_ID =N'" & Company_Branch_ID & "'"

                'Return_Value = "Select " & Field_Name & " from " & Table_Name & " where " & crtria & " and Stores =N'" & StoresName & "'"
            End If
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function

    Function Get_Select_Grid_S_OrderBy_Branch(ByVal Field_Name As String, ByVal Table_Name As String, ByVal crtria As String) As String
        Try
            Dim Return_Value As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where " & crtria & " order by 1"
            Else
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0'  and " & crtria & " order by 1"
            End If
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function

    Function Get_Select_Grid_S_OrderBy_Name(ByVal Field_Name As String, ByVal Table_Name As String, ByVal crtria As String, ByVal orderby As String) As String
        Try
            Dim Return_Value As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where " & crtria & " order by " & orderby & ""
            Else
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0'  and " & crtria & " order by " & orderby & ""
            End If
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function

    Function Get_Select_Grid_S_Branch(ByVal Field_Name As String, ByVal Table_Name As String, ByVal crtria As String) As String
        Try
            Dim Return_Value As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where " & crtria
            Else
                Return_Value = "Select " & Field_Name & " from " & Table_Name & " where " & crtria & " and Company_Branch_ID =N'" & Company_Branch_ID & "'"
            End If
            Return Return_Value
        Catch ex As Exception
        End Try
    End Function

    Function Get_Value_Count_More(ByVal Table_Name As String, ByVal crtria As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select count(*) from " & Table_Name & " where " & crtria
            Else
                cmd.CommandText = "select count(*) from " & Table_Name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0'  and " & crtria
            End If
            H = cmd.ExecuteScalar
        Catch ex As Exception
        End Try
    End Function

    Sub fill_combo_QuickSearch(ByVal Table_Name As String, ByVal Field_Name As String, ByVal combo As ComboBox)
        Try
            combo.Items.Clear()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "Select distinct " & Field_Name & " from " & Table_Name & " where QuickSearch=0 order by 1  "
            Else
                cmd.CommandText = "Select distinct " & Field_Name & " from " & Table_Name & " where QuickSearch=0 And Company_Branch_ID =N'" & Company_Branch_ID & "' order by 1  "
            End If
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            dr = cmd.ExecuteReader
            Do While dr.Read = True
                combo.Items.Add(dr(0))
            Loop
        Catch ex As Exception
        End Try
    End Sub

    Sub FillComboDataSet(ByVal Table_Name As String, ByVal ID As String, ByVal Name As String, ByVal combo As ComboBox)
        ds.Clear()
        combo.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "select distinct " & ID & "," & Name & " from " & Table_Name & ""
        connection = New SqlConnection(constring)
        Try
            connection.Open()
            command = New SqlCommand(S, connection)
            adapter.SelectCommand = command
            adapter.Fill(ds)
            adapter.Dispose()
            command.Dispose()
            connection.Close()
            combo.DataSource = ds.Tables(0)
            combo.ValueMember = "" & ID & ""
            combo.DisplayMember = "" & Name & ""
            combo.Text = ""
        Catch ex As Exception
        End Try
    End Sub

    Sub FillComboDataSetQuickSearch(ByVal Table_Name As String, ByVal ID As String, ByVal Name As String, ByVal combo As ComboBox)
        ds.Clear()
        combo.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If Company_Branch_ID = "0" Then
            S = "select distinct " & ID & "," & Name & " from " & Table_Name & " where QuickSearch=0"
        Else
            S = "select distinct " & ID & "," & Name & " from " & Table_Name & " where Company_Branch_ID=N'" & Company_Branch_ID & "' and QuickSearch=0"
        End If
        connection = New SqlConnection(constring)
        Try
            connection.Open()
            command = New SqlCommand(S, connection)
            adapter.SelectCommand = command
            adapter.Fill(ds)
            adapter.Dispose()
            command.Dispose()
            connection.Close()
            combo.DataSource = ds.Tables(0)
            combo.ValueMember = "" & ID & ""
            combo.DisplayMember = "" & Name & ""
            combo.Text = ""
        Catch ex As Exception
        End Try
    End Sub

    Sub FillComboDataAdapterQuickSearch(ByVal Table_Name As String, ByVal Name As String, ByVal combo As ComboBox)
        Try
            ActionDataAdapter = True
            connectionStringOpen()
            Dim sqlConnection As New SqlConnection(constring)
            combo.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                S = "SELECT distinct " & Name & " from " & Table_Name & " where QuickSearch=0"
            Else
                S = "SELECT distinct " & Name & " from " & Table_Name & " where Company_Branch_ID=N'" & Company_Branch_ID & "' and QuickSearch=0"
            End If
            Dim adapter As New SqlDataAdapter(S, sqlConnection)
            Dim table As New DataTable()
            adapter.Fill(table)
            combo.DataSource = table
            combo.DisplayMember = "" & Name & ""
            combo.ValueMember = "" & Name & ""
            combo.Text = ""
            sqlConnection.Close()
            connect()
            ActionDataAdapter = False
        Catch ex As Exception
        End Try
    End Sub

    Sub fill_combo_DataAdapter_Stores_Where_More(ByVal Table_Name As String, ByVal Field_Name As String, ByVal crtria As String, ByVal combo As ComboBox)
        Try
            ActionDataAdapter = True
            connectionStringOpen()
            Dim sqlConnection As New SqlConnection(constring)
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                S = "select distinct " & Field_Name & " from " & Table_Name & " where " & crtria & " order by 1"
            Else
                S = "select distinct " & Field_Name & " from " & Table_Name & " where Company_Branch_ID = N'" & Company_Branch_ID & "'  or Company_Branch_ID =N'0' and " & crtria & " order by 1"
            End If
            Dim adapter As New SqlDataAdapter(S, sqlConnection)
            Dim table As New DataTable()
            adapter.Fill(table)
            combo.DataSource = table
            combo.DisplayMember = "" & Field_Name & ""
            combo.ValueMember = "" & Field_Name & ""
            sqlConnection.Close()
            connect()
            ActionDataAdapter = False
        Catch ex As Exception
        End Try
    End Sub

    Sub fill_combo_DataAdapter_Stores_Where(ByVal Table_Name As String, ByVal Field_Name As String, ByVal Field_Name_where As String, ByVal Text As String, ByVal combo As ComboBox)
        Try
            ActionDataAdapter = True
            connectionStringOpen()
            Dim sqlConnection As New SqlConnection(constring)
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                S = "select distinct " & Field_Name & " from " & Table_Name & " where " & Field_Name_where & " =N'" & Text & "' order by 1"
            Else
                S = "select distinct " & Field_Name & " from " & Table_Name & " where " & Field_Name_where & " =N'" & Text & "' and Company_Branch_ID = N'" & Company_Branch_ID & "'  or Company_Branch_ID =N'0' order by 1"
            End If
            Dim adapter As New SqlDataAdapter(S, sqlConnection)
            Dim table As New DataTable()
            adapter.Fill(table)
            combo.DataSource = table
            combo.DisplayMember = "" & Field_Name & ""
            combo.ValueMember = "" & Field_Name & ""
            sqlConnection.Close()
            connect()
            ActionDataAdapter = False
        Catch ex As Exception
        End Try
    End Sub

    Sub fill_combo_DataAdapter_Stores_Where_QuickSearch(ByVal Table_Name As String, ByVal Field_Name As String, ByVal Field_Name_where As String, ByVal Text As String, ByVal combo As ComboBox)
        Try
            ActionDataAdapter = True
            connectionStringOpen()
            Dim sqlConnection As New SqlConnection(constring)
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                S = "select distinct " & Field_Name & " from " & Table_Name & " where " & Field_Name_where & " =N'" & Text & "' and QuickSearch=0 order by 1"
            Else
                S = "select distinct " & Field_Name & " from " & Table_Name & " where " & Field_Name_where & " =N'" & Text & "' and QuickSearch=0 and Company_Branch_ID = N'" & Company_Branch_ID & "'  or Company_Branch_ID =N'0' order by 1"
            End If
            Dim adapter As New SqlDataAdapter(S, sqlConnection)
            Dim table As New DataTable()
            adapter.Fill(table)
            combo.DataSource = table
            combo.DisplayMember = "" & Field_Name & ""
            combo.ValueMember = "" & Field_Name & ""
            sqlConnection.Close()
            connect()
            ActionDataAdapter = False
        Catch ex As Exception
        End Try
    End Sub


    Sub Fill_List(ByVal Table_Name As String, ByVal Field_Name As String, ByVal lis As ListBox)
        lis.Items.Clear()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct " & Field_Name & " from " & Table_Name & " order by 1"
            dr = cmd.ExecuteReader
1:          If dr.Read Then
                lis.Items.Add(dr(Field_Name))
                GoTo 1
            End If
        Catch ex As Exception
        End Try
    End Sub
    Function Check_Field_Value(ByVal Table_name As String, ByVal crtria As String) As Boolean
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct * from " & Table_name & " where " & crtria
            dr = cmd.ExecuteReader

            If dr.Read Then
                Return True
            Else
                Return False
            End If
        Catch ex As Exception
        End Try
    End Function

    Function Check_Field_Value(ByVal Table_name As String, ByVal Field_Name As String, ByVal Value_toChek As String) As Boolean
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct " & Field_Name & " from " & Table_name & " where " & Field_Name & " =N'" & Value_toChek & "'"
            dr = cmd.ExecuteReader

            If dr.Read Then
                Return True
            Else
                Return False
            End If
        Catch ex As Exception
        End Try
    End Function
    Function Check_Field_Value(ByVal Table_name As String, ByVal Field_Name As String, ByVal Value_toChek As Double) As Boolean
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct " & Field_Name & " from " & Table_name & " where " & Field_Name & " =N'" & Value_toChek & "'"
            dr = cmd.ExecuteReader
            If dr.Read Then
                Return True
            Else
                Return False
            End If
        Catch ex As Exception
        End Try
    End Function

    Function Select_More_Data_Branch(ByVal Table_name As String, ByVal Field As String, ByVal crtria As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select " & Field & " from " & Table_name & " where " & crtria
            Else
                cmd.CommandText = "select " & Field & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0'  and " & crtria
            End If
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
        End Try
    End Function

    Function Select_More_Data_Branch_All(ByVal Table_name As String, ByVal Field As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select " & Field & " from " & Table_name & ""
            Else
                cmd.CommandText = "select " & Field & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0' "
            End If
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
        End Try
    End Function

    Function Select_More_Data_Stores(ByVal Table_name As String, ByVal Field As String, ByVal crtria As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select " & Field & " from " & Table_name & " where " & crtria
            Else
                cmd.CommandText = "select " & Field & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0'  and " & crtria
                'cmd.CommandText = "select " & Field & " from " & Table_name & " where Stores =N'" & StoresName & "' and " & crtria
            End If
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
        End Try
    End Function

    Function Select_More_Data_Stores_Loop(ByVal Table_name As String, ByVal Field As String, ByVal crtria As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select " & Field & " from " & Table_name & " where " & crtria
            Else
                cmd.CommandText = "select " & Field & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0'  and " & crtria
                'cmd.CommandText = "select " & Field & " from " & Table_name & " where Stores =N'" & StoresName & "' and " & crtria
            End If
            dr = cmd.ExecuteReader
        Catch ex As Exception
        End Try
    End Function

    Function Select_More_Data_Stores_All(ByVal Table_name As String, ByVal Field As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select " & Field & " from " & Table_name & ""
            Else
                cmd.CommandText = "select " & Field & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0' "
                'cmd.CommandText = "select " & Field & " from " & Table_name & " where Stores =N'" & StoresName & "'"
            End If
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
        End Try
    End Function

    Function Select_More_Data_Stores_All_2(ByVal Table_name As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "" & Table_name & ""
            Else
                cmd.CommandText = "" & Table_name & " AND Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0' "
            End If
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
        End Try
    End Function

    Function Select_More_Data_Branch_Print(ByVal Table_name As String, ByVal Field As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select " & Field & " from " & Table_name & ""
            Else
                cmd.CommandText = "select " & Field & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0' "
            End If
            dr = cmd.ExecuteReader
        Catch ex As Exception
        End Try
    End Function


    Function Select_More_Data_Branch_Print_Orderby(ByVal Table_name As String, ByVal Field As String, ByVal orderby As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select " & Field & " from " & Table_name & " order by " & orderby & ""
            Else
                cmd.CommandText = "select " & Field & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0'  and  order by " & orderby & ""
            End If
            dr = cmd.ExecuteReader
        Catch ex As Exception
        End Try
    End Function

    Function Select_More_Data_Branch_Print_Orderby_Username(ByVal Table_name As String, ByVal Field As String, ByVal orderby As String, ByVal fbl_user As String, ByVal txt_user As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select " & Field & " from " & Table_name & " where " & fbl_user & " =N'" & txt_user & "' order by " & orderby & ""
            Else
                cmd.CommandText = "select " & Field & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0' and " & fbl_user & " =N'" & txt_user & "'  order by " & orderby & ""
            End If
            dr = cmd.ExecuteReader
        Catch ex As Exception
        End Try
    End Function

    Function Select_More_Data_Branch_Print_Orderby_DESC(ByVal Table_name As String, ByVal Field As String, ByVal orderby As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select " & Field & " from " & Table_name & " order by " & orderby & " DESC"
            Else
                cmd.CommandText = "select " & Field & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0'  and  order by " & orderby & " DESC"
            End If
            dr = cmd.ExecuteReader
        Catch ex As Exception
        End Try
    End Function

    Function Select_More_Data(ByVal Table_name As String, ByVal Field As String, ByVal crtria As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select " & Field & " from " & Table_name & " where " & crtria
            dr = cmd.ExecuteReader : dr.Read()
        Catch ex As Exception
        End Try
    End Function

    Function Get_Code_Value_Branch(ByVal Table_name As String, ByVal Field_Code As String, ByVal Field_Name As String, ByVal Value_toChek As String)
        Try
            Dim Code As String = "0"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where " & Field_Name & " =N'" & Value_toChek & "'"
            Else
                cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0'  and " & Field_Name & " =N'" & Value_toChek & "'"
            End If
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Code = dr(0).ToString
            End If
            Return Code
        Catch ex As Exception
        End Try
    End Function

    Function Get_Code_Value_Branch_More(ByVal Table_name As String, ByVal Field_Code As String, ByVal crtria As String)
        Try
            Dim Code As String = "0"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where " & crtria
            Else
                cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0'  and  " & crtria
            End If
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Code = dr(0).ToString
            End If
            Return Code
        Catch ex As Exception
        End Try
    End Function

    Function Get_Code_Value_Stores(ByVal Table_name As String, ByVal Field_Code As String, ByVal Field_Name As String, ByVal Value_toChek As String)
        Try
            Dim Code As String = "0"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where " & Field_Name & " =N'" & Value_toChek & "'"
            Else
                cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0'  and " & Field_Name & " =N'" & Value_toChek & "'"
                'cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where Stores =N'" & StoresName & "' and " & Field_Name & " =N'" & Value_toChek & "'"
            End If
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Code = dr(0).ToString
            End If
            Return Code
        Catch ex As Exception
        End Try
    End Function

    Function Get_Code_Value_Stores_More(ByVal Table_name As String, ByVal Field_Code As String, ByVal crtria As String)
        'Try
        Dim Code As String = "0"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where " & crtria
            Else
                cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0'  and  " & crtria
                'cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where Stores =N'" & StoresName & "' and  " & crtria
            End If
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Code = dr(0).ToString
            End If
            Return Code
        'Catch ex As Exception
        'End Try
    End Function

    Function Get_Code_Value(ByVal Table_name As String, ByVal Field_Code As String, ByVal Field_Name As String, ByVal Value_toChek As String)
        'Try
        Dim Code As String = "0"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where " & Field_Name & " =N'" & Value_toChek & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Code = dr(0).ToString
            End If
            Return Code
        'Catch ex As Exception
        'End Try
    End Function

    Function Get_Code_Value_More(ByVal Table_name As String, ByVal Field_Code As String, ByVal crtria As String)
        Try
            Dim Code As String = "0"
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select distinct " & Field_Code & " from " & Table_name & " where " & crtria
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Code = dr(0).ToString
            End If
            Return Code
        Catch ex As Exception
        End Try
    End Function

    Function Get_Code_Value_More_ALL_Select(ByVal crtria As String)
        Try
            Dim Code As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "" & crtria & ""
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Code = dr(0).ToString
            Else
                Code = 0
            End If
            Return Code
        Catch ex As Exception
        End Try
    End Function

    Function Get_Check_Return_True_False(ByVal Table_name As String, ByVal crtria As String)
        Try
            Dim Return_True As Boolean
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select count(*) from " & Table_name & " where  " & crtria : H = cmd.ExecuteScalar
            If H = 1 Then
                Return_True = True
            End If
            Return Return_True
        Catch ex As Exception
        End Try
    End Function

    Function PopulateDataView(ByVal dataReader As IDataReader) As DataView
        Dim tableName As String
        tableName = "M"
        Dim dataReaderTable As New DataTable(tableName)
        Try
            For count As Integer = 0 To dataReader.FieldCount - 1
                Dim tempCol As New DataColumn(dataReader.GetName(count), dataReader.GetFieldType(count))
                dataReaderTable.Columns.Add(tempCol)
            Next
            While dataReader.Read()
                Dim drr As DataRow = dataReaderTable.NewRow()
                For i As Integer = 0 To dataReader.FieldCount - 1
                    drr(i) = dataReader.GetValue(dataReader.GetOrdinal(dataReader.GetName(i)))
                Next
                dataReaderTable.Rows.Add(drr)
            End While
            Return dataReaderTable.DefaultView
        Catch
            Return Nothing
        End Try
    End Function

    Function PopulateDataViewVertical(ByVal dataReader As IDataReader) As DataView
        Dim tableName As String
        tableName = "M"
        Dim dataReaderTable As New DataTable(tableName)
        Try
            ' إنشاء الأعمدة كما في الكود الأصلي
            For count As Integer = 0 To dataReader.FieldCount - 1
                Dim tempCol As New DataColumn(dataReader.GetName(count), dataReader.GetFieldType(count))
                dataReaderTable.Columns.Add(tempCol)
            Next

            ' قراءة البيانات
            While dataReader.Read()
                Dim drr As DataRow = dataReaderTable.NewRow()
                For i As Integer = 0 To dataReader.FieldCount - 1
                    drr(i) = dataReader.GetValue(dataReader.GetOrdinal(dataReader.GetName(i)))
                Next
                dataReaderTable.Rows.Add(drr)
            End While

            ' إنشاء جدول جديد للعرض الرأسي
            Dim verticalTable As New DataTable("VerticalView")
            verticalTable.Columns.Add("الخصائص", GetType(String))
            verticalTable.Columns.Add("القيم", GetType(String))

            ' تحويل البيانات للعرض الرأسي
            For Each row As DataRow In dataReaderTable.Rows
                Dim columnNames() As String = dataReaderTable.Columns.Cast(Of DataColumn)().Select(Function(c) c.ColumnName).ToArray()

                For i As Integer = 0 To columnNames.Length - 1
                    Dim newRow As DataRow = verticalTable.NewRow()
                    newRow("الخصائص") = columnNames(i)
                    newRow("القيم") = row(i).ToString()
                    verticalTable.Rows.Add(newRow)
                Next

                ' إضافة فاصل بين السجلات
                Dim separatorRow As DataRow = verticalTable.NewRow()
                separatorRow("الخصائص") = "---"
                separatorRow("القيم") = "---"
                verticalTable.Rows.Add(separatorRow)
            Next

            Return verticalTable.DefaultView
        Catch ex As Exception
            ' يمكنك إضافة التسجيل للخطأ هنا إذا أردت
            Return Nothing
        End Try
    End Function

    Sub insert(ByVal tbl_name As String, ByVal fields As String, ByVal values As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "insert into " & tbl_name & " (" & fields & ")values(" & values & ")"
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try
    End Sub

    Sub Update_Branch(ByVal tbl_name As String, ByVal fields As String, ByVal values As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "update " & tbl_name & " set " & fields & " where " & values & ""
            Else
                cmd.CommandText = "update " & tbl_name & " set " & fields & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0'  and " & values & ""
            End If
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try
    End Sub

    Sub Update(ByVal tbl_name As String, ByVal fields As String, ByVal values As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update " & tbl_name & " set " & fields & " where " & values & ""
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try
    End Sub


    Sub delete_Branch(ByVal tbl_name As String, ByVal crtria As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "delete from " & tbl_name & " where " & crtria
            Else
                cmd.CommandText = "delete from " & tbl_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0'  and " & crtria
            End If
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try
    End Sub

    Sub delete_Branch_All(ByVal tbl_name As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "delete from " & tbl_name & ""
            Else
                cmd.CommandText = "delete from " & tbl_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0' "
            End If
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try
    End Sub

    Sub delete_Branch_All_Users(ByVal tbl_name As String, ByVal fbl_user As String, ByVal txt_user As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Company_Branch_ID = "0" Then
                cmd.CommandText = "delete from " & tbl_name & " where " & fbl_user & " =N'" & txt_user & "'"
            Else
                cmd.CommandText = "delete from " & tbl_name & " where Company_Branch_ID =N'" & Company_Branch_ID & "' or Company_Branch_ID =N'0' and " & fbl_user & " =N'" & txt_user & "'"
            End If
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try
    End Sub

    Sub delete(ByVal tbl_name As String, ByVal crtria As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete from " & tbl_name & " where " & crtria
            cmd.ExecuteNonQuery()
        Catch ex As Exception
        End Try
    End Sub

    Sub clear(ByVal cr As Control)
        Dim c, c1 As Control
        For Each c In cr.Controls
            If TypeOf c Is TextBox Or TypeOf c Is ComboBox Then
                c.Text = ""
            ElseIf TypeOf c Is GroupBox Then
                For Each c1 In c.Controls
                    If TypeOf c1 Is TextBox Or TypeOf c1 Is ComboBox Then
                        c1.Text = ""
                    End If
                Next
            End If
        Next
    End Sub

    Sub fill_combo(ByVal Table_Name As String, ByVal Field_Name As String, ByVal crtria As String, ByVal combo As ComboBox)
        Try
            combo.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select " & Field_Name & " from " & Table_Name & " where " & crtria
            dr = cmd.ExecuteReader
1:          If dr.Read Then
                combo.Items.Add(dr(Field_Name))
                GoTo 1
            End If
        Catch ex As Exception
        End Try
    End Sub

    Function return_brcode(ByVal cat As String, ByVal itm As String)
        Try
            Dim br As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select brcode from itms where cat=N'" & cat & "' and itm=N'" & itm & "'"
            dr = cmd.ExecuteReader
            If dr.Read Then
                br = dr("brcode")
                Return br
            Else
            End If
        Catch ex As Exception
        End Try
    End Function

    Function get_random(ByVal lnth As Integer, ByVal rnd_type As String) As String
        Try
            If rnd_type <> "a" And rnd_type <> "n" Then
                MsgBox("Error Creating Rnd", vbCritical)
                Return "Error Creating Rnd"
                Exit Function
            End If
            If IsNumeric(lnth) = False Then
                MsgBox("Error Creating Rnd", vbCritical)
                Return "Error Creating Rnd"
                Exit Function
            End If
            Dim rndx As New Random
            Dim nm As Integer
            nm = Int(rndx.NextDouble * 100) / 4
            Dim rndm As String
            Select Case rnd_type
                Case "n"
                    For i = 1 To lnth
                        nm = Int(rndx.NextDouble * 10)
                        rndm = rndm + Trim(Str(nm))
                    Next
                Case "a"
                    For i = 1 To lnth
                        nm = Int((rndx.NextDouble * 100) / 4)
                        rndm = rndm + Chr(nm + 97)
                    Next
            End Select
            Return rndm
        Catch ex As Exception
        End Try
    End Function

    Function C_date(ByVal dt As Date) As String
        Dim dy
        Dim mn
        Dim yr
        dy = dt.Day
        If Len(Trim(dy)) = 1 Then dy = "0" + Trim(Str(dy))
        mn = Month(dt)
        If Len(Trim(mn)) = 1 Then mn = "0" + Trim(Str(mn))
        yr = Year(dt)
        Return yr & mn & dy
    End Function

    Function C_date2(ByVal dt As Date) As String
        Dim dy
        Dim mn
        Dim yr

        dy = dt.Day
        If Len(Trim(dy)) = 1 Then dy = "0" + Trim(Str(dy))
        mn = Month(dt)
        If Len(Trim(mn)) = 1 Then mn = "0" + Trim(Str(mn))
        yr = Year(dt)
        Return yr & mn & dy
    End Function

    Function sum_any(ByVal Table_Name As String, ByVal Field_Name As String, ByVal crtria As String) As Double
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sum(" & Field_Name & ") as sms from " & Table_Name & " where " & crtria
            dr = cmd.ExecuteReader

            If dr.Read Then
                If IsDBNull(dr("sms")) Then
                    Return 0
                Else
                    Dim m = dr("sms")
                    Return m
                End If
            Else
                Return 0
            End If
        Catch ex As Exception
        End Try
    End Function

    Function Get_Count_Pre_Zero(ByVal X As String)
        If Len(X) = 1 Then X = "0" & "0" & X
        If Len(X) = 2 Then X = "0" & X
        If Len(X) = 1 Then X = X

        Return X
    End Function
    Function Set_Count_Down(ByVal X As String)
        If Len(X) = 3 Then X = X
        If Len(X) = 2 Then X = "0" & X
        If Len(X) = 1 Then X = "0" & "0" & X
        Dim M As String
        Dim MX As String

        M = VB.Right(Cls.GenerateHash(Now.Ticks), 20) & VB.Left(Cls.GenerateHash(Now.Ticks), 20)
        MX = VB.Left(M, 13) & VB.Left(X, 1) & VB.Mid(M, 13, 7) & VB.Mid(X, 2, 1) & VB.Mid(M, 21, 12) & VB.Right(X, 1) & VB.Right(M, 8)
        X = MX
        Return X
    End Function
    Function Get_Count_Down(ByVal X As String)
        X = Val(VB.Mid(X, 14, 1) & VB.Mid(X, 22, 1) & VB.Mid(X, 35, 1))
        Return X
    End Function

    Function ConfirmVerssion(ByVal x As String) As Boolean
        Try
            Dim M As String
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ghost from ghost" : dr = cmd.ExecuteReader : dr.Read() : M = dr("ghost")
            If x = M Then
                Return True
            ElseIf M <> x Then
                Return False
            End If
        Catch ex As Exception
        End Try
    End Function

    Function MAXRECORD(ByVal Table As String, ByVal Felds As String)
        Dim Code As String = "0"
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " + Table + ""
        dr = cmd.ExecuteReader
        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Code = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" + Felds + " As float)) as mb FROM " + Table + ""
            dr = cmd.ExecuteReader
            dr.Read()
            Dim sh As Long
            sh = dr("mb")
            Code = sh + 1
        End If
        Return Code
    End Function


    Private Declare Function WriteProfileString Lib "kernel32" Alias "WriteProfileStringA" _
   (ByVal lpszSection As String, ByVal lpszKeyName As String,
   ByVal lpszString As String) As Long
    Private Declare Function SendMessage Lib "user32" Alias "SendMessageA" _
         (ByVal hwnd As Long, ByVal wMsg As Long,
         ByVal wParam As Long, ByVal lparam As String) As Long
    Private Const HWND_BROADCAST As Long = &HFFFF&
    Private Const WM_WININICHANGE As Long = &H1A

    Public Function SetDefaultSystemPrinter4(ByVal strPrinterName As String) As Boolean
        On Error Resume Next
        'this method does not valid if the change is correct and does not revert to previous printer if wrong
        Dim DeviceLine As String

        'rebuild a valid device line string 
        DeviceLine = strPrinterName & ",,"

        'Store the new printer information in the 
        '[WINDOWS] section of the WIN.INI file for 
        'the DEVICE= item 
        Call WriteProfileString("windows", "Device", DeviceLine)

        'Cause all applications to reload the INI file 
        Call SendMessage(HWND_BROADCAST, WM_WININICHANGE, 0, "windows")

        Return True

    End Function

    Public Sub GetDefaultPrinterBill()
        If SettingPrinterAuto = "YES" Then
            If Not Cls.SetDefaultSystemPrinter4(DefaultPrinterBill) Then
                MessageBox.Show("Error occured will trying to set the default printer!")
            End If
        End If
    End Sub

    Public Sub GetDefaultPrinterBill2()
        If SettingPrinterAuto = "YES" Then
            If Not Cls.SetDefaultSystemPrinter4(DefaultPrinterBill2) Then
                MessageBox.Show("Error occured will trying to set the default printer!")
            End If
        End If
    End Sub

    Public Sub GetDefaultPrinterA4()
        If SettingPrinterAuto = "YES" Then
            If Not Cls.SetDefaultSystemPrinter4(DefaultPrinterA4) Then
                MessageBox.Show("Error occured will trying to set the default printer!")
            End If
        End If
    End Sub

    Public Sub GetDefaultPrinterBarcode()
        If SettingPrinterAuto = "YES" Then
            If Not Cls.SetDefaultSystemPrinter4(DefaultPrinterBarcode) Then
                MessageBox.Show("Error occured will trying to set the default printer!")
            End If
        End If
    End Sub

    Function Return_Code(ByVal Field As String, ByVal Table As String, ByVal Code As String, ByVal TXT As String)
        Dim XCode As String
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select " & Field & " from " & Table & " where " & Code & "=N'" & TXT & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            XCode = Val(dr(0).ToString)
            Return XCode
        End If
    End Function

    Function GenerateItmId_Or_Parcode() As String
        Dim DDTT As String
        DDTT = Now.Year.ToString
        DDTT = (DDTT & (Now.Month.ToString))
        DDTT = (DDTT & (Now.Day.ToString))
        DDTT = (DDTT & (Now.Hour.ToString))
        DDTT = (DDTT & (Now.Minute.ToString))
        DDTT = (DDTT & (Now.Second.ToString))
        DDTT = (DDTT & (Now.Millisecond.ToString))

        DDTT = VB.Mid(DDTT, 3, 12)
        If Len(DDTT) = 6 Then
            DDTT = DDTT & "0" & "0" & "0" & "0" & "0" & "0"
        End If
        If Len(DDTT) = 7 Then
            DDTT = DDTT & "0" & "0" & "0" & "0" & "0"
        End If
        If Len(DDTT) = 8 Then
            DDTT = DDTT & "0" & "0" & "0" & "0"
        End If
        If Len(DDTT) = 9 Then
            DDTT = DDTT & "0" & "0" & "0"
        End If
        If Len(DDTT) = 10 Then
            DDTT = DDTT & "0" & "0"
        End If
        If Len(DDTT) = 11 Then
            DDTT = DDTT & "0"
        End If
        Return DDTT
    End Function



End Class
