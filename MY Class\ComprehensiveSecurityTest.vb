Imports System.Data.SqlClient
Imports System.IO

''' <summary>
''' فئة الاختبار الشامل للأمان - تشغيل جميع اختبارات الأمان والوظائف
''' </summary>
Public Class ComprehensiveSecurityTest
    Private Shared testResults As New List(Of TestResult)
    Private Shared testStartTime As DateTime

    ''' <summary>
    ''' نتيجة اختبار واحد
    ''' </summary>
    Public Class TestResult
        Public Property TestName As String
        Public Property Status As String
        Public Property Message As String
        Public Property ExecutionTime As TimeSpan
        Public Property ErrorDetails As String
    End Class

    ''' <summary>
    ''' تشغيل جميع الاختبارات الشاملة
    ''' </summary>
    Public Shared Sub RunComprehensiveTests()
        Try
            testStartTime = DateTime.Now
            testResults.Clear()

            Console.WriteLine("🧪 بدء الاختبار الشامل لنظام FIT SOFT")
            Console.WriteLine("============================================================")

            ' المرحلة 1: اختبار الأمان
            Console.WriteLine("🔒 المرحلة 1: اختبار الأمان")
            RunSecurityTests()

            ' المرحلة 2: اختبار الوظائف الأساسية
            Console.WriteLine("⚙️ المرحلة 2: اختبار الوظائف الأساسية")
            RunFunctionalTests()

            ' المرحلة 3: اختبار معالجة الأخطاء
            Console.WriteLine("🛠️ المرحلة 3: اختبار معالجة الأخطاء")
            RunErrorHandlingTests()

            ' المرحلة 4: اختبار الأداء
            Console.WriteLine("🚀 المرحلة 4: اختبار الأداء")
            RunPerformanceTests()

            ' إنشاء التقرير النهائي
            GenerateFinalReport()

        Catch ex As Exception
            Console.WriteLine($"❌ خطأ في تشغيل الاختبارات: {ex.Message}")
            ErrorHandler.LogError("خطأ في الاختبار الشامل", ex)
        End Try
    End Sub

    ''' <summary>
    ''' تشغيل اختبارات الأمان
    ''' </summary>
    Private Shared Sub RunSecurityTests()
        ' اختبار 1: تهيئة النظم الأمنية
        RunTest("تهيئة النظم الأمنية", Sub() TestSecurityInitialization())

        ' اختبار 2: SecureDatabaseManager
        RunTest("SecureDatabaseManager", Sub() TestSecureDatabaseManager())

        ' اختبار 3: SafeQueryBuilder
        RunTest("SafeQueryBuilder", Sub() TestSafeQueryBuilder())

        ' اختبار 4: مقاومة SQL Injection
        RunTest("مقاومة SQL Injection", Sub() TestSQLInjectionResistance())

        ' اختبار 5: تشفير كلمات المرور
        RunTest("تشفير كلمات المرور", Sub() TestPasswordEncryption())

        ' اختبار 6: ErrorHandler
        RunTest("ErrorHandler", Sub() TestErrorHandler())
    End Sub

    ''' <summary>
    ''' تشغيل اختبارات الوظائف الأساسية
    ''' </summary>
    Private Shared Sub RunFunctionalTests()
        ' اختبار 1: تسجيل الدخول
        RunTest("تسجيل الدخول", Sub() TestLoginFunctionality())

        ' اختبار 2: نماذج المبيعات
        RunTest("نماذج المبيعات", Sub() TestSalesForms())

        ' اختبار 3: نماذج المشتريات
        RunTest("نماذج المشتريات", Sub() TestPurchaseForms())

        ' اختبار 4: نماذج المحاسبة
        RunTest("نماذج المحاسبة", Sub() TestAccountingForms())

        ' اختبار 5: نماذج الموظفين
        RunTest("نماذج الموظفين", Sub() TestEmployeeForms())
    End Sub

    ''' <summary>
    ''' تشغيل اختبارات معالجة الأخطاء
    ''' </summary>
    Private Shared Sub RunErrorHandlingTests()
        ' اختبار 1: معالجة أخطاء قاعدة البيانات
        RunTest("معالجة أخطاء قاعدة البيانات", Sub() TestDatabaseErrorHandling())

        ' اختبار 2: معالجة البيانات الفارغة
        RunTest("معالجة البيانات الفارغة", Sub() TestNullDataHandling())

        ' اختبار 3: معالجة الاستثناءات
        RunTest("معالجة الاستثناءات", Sub() TestExceptionHandling())
    End Sub

    ''' <summary>
    ''' تشغيل اختبارات الأداء
    ''' </summary>
    Private Shared Sub RunPerformanceTests()
        ' اختبار 1: أداء الاستعلامات
        RunTest("أداء الاستعلامات", Sub() TestQueryPerformance())

        ' اختبار 2: إدارة الذاكرة
        RunTest("إدارة الذاكرة", Sub() TestMemoryManagement())

        ' اختبار 3: Connection Pooling
        RunTest("Connection Pooling", Sub() TestConnectionPooling())
    End Sub

    ''' <summary>
    ''' تشغيل اختبار واحد مع قياس الوقت
    ''' </summary>
    Private Shared Sub RunTest(testName As String, testAction As Action)
        Dim result As New TestResult With {
            .TestName = testName,
            .Status = "RUNNING"
        }

        Dim startTime As DateTime = DateTime.Now

        Try
            Console.WriteLine($"  🔄 تشغيل: {testName}")
            testAction.Invoke()

            result.Status = "PASSED"
            result.Message = "تم بنجاح"
            Console.WriteLine($"  ✅ نجح: {testName}")

        Catch ex As Exception
            result.Status = "FAILED"
            result.Message = ex.Message
            result.ErrorDetails = ex.ToString()
            Console.WriteLine($"  ❌ فشل: {testName} - {ex.Message}")
            ErrorHandler.LogError($"فشل اختبار: {testName}", ex)
        Finally
            result.ExecutionTime = DateTime.Now - startTime
            testResults.Add(result)
        End Try
    End Sub

    ' ==================== اختبارات الأمان ====================

    ''' <summary>
    ''' اختبار تهيئة النظم الأمنية
    ''' </summary>
    Private Shared Sub TestSecurityInitialization()
        ' تهيئة ErrorHandler
        ErrorHandler.Initialize()

        ' تهيئة SecureDatabaseManager
        SecureDatabaseManager.Instance.Initialize()

        ' التحقق من إنشاء مجلد السجلات
        If Not Directory.Exists("Logs") Then
            Throw New Exception("لم يتم إنشاء مجلد السجلات")
        End If

        Console.WriteLine("    ✓ تم تهيئة جميع النظم الأمنية بنجاح")
    End Sub

    ''' <summary>
    ''' اختبار SecureDatabaseManager
    ''' </summary>
    Private Shared Sub TestSecureDatabaseManager()
        ' اختبار إنشاء الاتصال
        Using connection As SqlConnection = SecureDatabaseManager.Instance.CreateConnection()
            If connection Is Nothing Then
                Throw New Exception("فشل في إنشاء الاتصال")
            End If
            Console.WriteLine("    ✓ تم إنشاء الاتصال بنجاح")
        End Using

        ' اختبار استعلام آمن
        Dim testQuery As String = "SELECT COUNT(*) FROM users WHERE username = @username"
        Dim testParams As New Dictionary(Of String, Object) From {
            {"@username", "test"}
        }

        Try
            Dim result As Object = SecureDatabaseManager.Instance.ExecuteScalar(testQuery, testParams)
            Console.WriteLine("    ✓ تم تنفيذ الاستعلام الآمن بنجاح")
        Catch ex As Exception
            ' هذا متوقع إذا لم تكن قاعدة البيانات متصلة
            Console.WriteLine("    ⚠️ اختبار الاستعلام تم تخطيه (قاعدة البيانات غير متصلة)")
        End Try

        ' اختبار تشفير كلمة المرور
        Dim testPassword As String = "testPassword123"
        Dim hashedPassword As String = SecureDatabaseManager.Instance.HashPassword(testPassword)

        If String.IsNullOrEmpty(hashedPassword) OrElse hashedPassword = testPassword Then
            Throw New Exception("فشل في تشفير كلمة المرور")
        End If

        Console.WriteLine("    ✓ تم تشفير كلمة المرور بنجاح")
    End Sub

    ''' <summary>
    ''' اختبار SafeQueryBuilder
    ''' </summary>
    Private Shared Sub TestSafeQueryBuilder()
        ' اختبار بناء استعلام بسيط
        Dim baseQuery As String = "SELECT * FROM users WHERE active = 1"
        Dim queryBuilder As New SafeQueryBuilder(baseQuery)

        ' إضافة شروط
        queryBuilder.AddWhereCondition("username", "testUser")
        queryBuilder.AddWhereCondition("age", 25, ">")

        ' بناء الاستعلام
        Dim finalQuery As String = queryBuilder.Build()

        If Not finalQuery.Contains("@param") Then
            Throw New Exception("فشل في إنشاء معاملات آمنة")
        End If

        If queryBuilder.Parameters.Count = 0 Then
            Throw New Exception("لم يتم إنشاء معاملات")
        End If

        Console.WriteLine("    ✓ تم بناء الاستعلام الآمن بنجاح")

        ' اختبار شروط التاريخ
        queryBuilder.AddDateRangeCondition("created_date", DateTime.Now.AddDays(-30), DateTime.Now)

        ' اختبار شروط LIKE
        queryBuilder.AddLikeCondition("description", "test")

        finalQuery = queryBuilder.Build()
        Console.WriteLine("    ✓ تم اختبار جميع أنواع الشروط بنجاح")
    End Sub

    ''' <summary>
    ''' اختبار مقاومة SQL Injection
    ''' </summary>
    Private Shared Sub TestSQLInjectionResistance()
        ' محاولات SQL Injection مختلفة
        Dim maliciousInputs As String() = {
            "admin'; DROP TABLE users; --",
            "' OR '1'='1",
            "'; DELETE FROM users; --",
            "admin' UNION SELECT * FROM passwords --",
            "1' OR 1=1 --"
        }

        For Each maliciousInput In maliciousInputs
            ' اختبار مع SafeQueryBuilder
            Dim queryBuilder As New SafeQueryBuilder("SELECT * FROM users WHERE username = @username")
            queryBuilder.AddWhereCondition("username", maliciousInput)

            Dim query As String = queryBuilder.Build()

            ' التحقق من أن المدخل الخبيث تم تعقيمه
            If query.Contains(maliciousInput) Then
                Throw New Exception($"فشل في حماية من SQL Injection: {maliciousInput}")
            End If
        Next

        Console.WriteLine("    ✓ تم صد جميع محاولات SQL Injection بنجاح")
    End Sub

    ''' <summary>
    ''' اختبار تشفير كلمات المرور
    ''' </summary>
    Private Shared Sub TestPasswordEncryption()
        Dim testPasswords As String() = {
            "password123",
            "admin",
            "P@ssw0rd!",
            "123456",
            "قكلمة_مرور_عربية"
        }

        For Each password In testPasswords
            Dim hashedPassword As String = SecureDatabaseManager.Instance.HashPassword(password)

            ' التحقق من أن كلمة المرور تم تشفيرها
            If hashedPassword = password Then
                Throw New Exception($"لم يتم تشفير كلمة المرور: {password}")
            End If

            ' التحقق من أن التشفير ثابت
            Dim hashedPassword2 As String = SecureDatabaseManager.Instance.HashPassword(password)
            If hashedPassword <> hashedPassword2 Then
                Throw New Exception($"التشفير غير ثابت لكلمة المرور: {password}")
            End If
        Next

        Console.WriteLine("    ✓ تم اختبار تشفير كلمات المرور بنجاح")
    End Sub

    ''' <summary>
    ''' اختبار ErrorHandler
    ''' </summary>
    Private Shared Sub TestErrorHandler()
        ' اختبار تسجيل المعلومات
        ErrorHandler.LogInfo("اختبار تسجيل المعلومات")

        ' اختبار تسجيل التحذير
        ErrorHandler.LogWarning("اختبار تسجيل التحذير")

        ' اختبار SafeExecute
        Dim result As String = ErrorHandler.SafeExecute(
            Function() As String
                Return "نجح الاختبار"
            End Function,
            "فشل الاختبار",
            "اختبار SafeExecute"
        )

        If result <> "نجح الاختبار" Then
            Throw New Exception("فشل في SafeExecute")
        End If

        ' اختبار SafeExecute مع خطأ
        ErrorHandler.SafeExecute(
            Sub()
                Throw New Exception("خطأ اختباري")
            End Sub,
            "اختبار SafeExecute مع خطأ"
        )

        Console.WriteLine("    ✓ تم اختبار ErrorHandler بنجاح")
    End Sub

    ' ==================== اختبارات الوظائف الأساسية ====================

    ''' <summary>
    ''' اختبار وظائف تسجيل الدخول
    ''' </summary>
    Private Shared Sub TestLoginFunctionality()
        Try
            ' اختبار التحقق من صحة المستخدم (محاكاة)
            Dim testUsername As String = "admin"
            Dim testPassword As String = "password123"

            ' محاولة تسجيل دخول آمن
            Dim isValid As Boolean = SecureDatabaseManager.Instance.ValidateUser(testUsername, testPassword)

            Console.WriteLine("    ✓ تم اختبار آلية تسجيل الدخول الآمنة")

            ' اختبار مقاومة محاولات الاختراق
            Dim maliciousUsername As String = "admin'; DROP TABLE users; --"
            Dim maliciousPassword As String = "' OR '1'='1"

            Dim isMaliciousValid As Boolean = SecureDatabaseManager.Instance.ValidateUser(maliciousUsername, maliciousPassword)

            If isMaliciousValid Then
                Throw New Exception("فشل في صد محاولة اختراق تسجيل الدخول")
            End If

            Console.WriteLine("    ✓ تم صد محاولات اختراق تسجيل الدخول")

        Catch ex As Exception When ex.Message.Contains("قاعدة البيانات")
            Console.WriteLine("    ⚠️ تم تخطي اختبار تسجيل الدخول (قاعدة البيانات غير متصلة)")
        End Try
    End Sub

    ''' <summary>
    ''' اختبار نماذج المبيعات
    ''' </summary>
    Private Shared Sub TestSalesForms()
        ' اختبار بناء استعلام المبيعات الآمن
        Dim salesQueryBuilder As New SafeQueryBuilder(
            "SELECT bill_no, Vendorname, bill_date, totalpriceafterdisc FROM Sales_Bill WHERE bill_No <> @excludeValue"
        )

        salesQueryBuilder.AddCustomCondition("", New Dictionary(Of String, Object) From {{"@excludeValue", "جرد"}})
        salesQueryBuilder.AddWhereCondition("Vendorname", "عميل تجريبي")
        salesQueryBuilder.AddDateRangeCondition("bill_date", DateTime.Now.AddDays(-30), DateTime.Now)

        Dim salesQuery As String = salesQueryBuilder.Build()

        If Not salesQuery.Contains("@excludeValue") OrElse Not salesQuery.Contains("@param") Then
            Throw New Exception("فشل في بناء استعلام المبيعات الآمن")
        End If

        Console.WriteLine("    ✓ تم اختبار بناء استعلامات المبيعات الآمنة")

        ' اختبار معالجة بيانات الفاتورة
        TestBillDataProcessing("Sales")

        Console.WriteLine("    ✓ تم اختبار نماذج المبيعات بنجاح")
    End Sub

    ''' <summary>
    ''' اختبار نماذج المشتريات
    ''' </summary>
    Private Shared Sub TestPurchaseForms()
        ' اختبار بناء استعلام المشتريات الآمن
        Dim purchaseQueryBuilder As New SafeQueryBuilder(
            "SELECT bill_No, Vendorname, bill_date, totalpriceafterdisc FROM purchase_bill WHERE bill_No <> @excludeValue"
        )

        purchaseQueryBuilder.AddCustomCondition("", New Dictionary(Of String, Object) From {{"@excludeValue", "جرد1"}})
        purchaseQueryBuilder.AddWhereCondition("Vendorname", "مورد تجريبي")
        purchaseQueryBuilder.AddDateRangeCondition("bill_date", DateTime.Now.AddDays(-30), DateTime.Now)

        Dim purchaseQuery As String = purchaseQueryBuilder.Build()

        If Not purchaseQuery.Contains("@excludeValue") OrElse Not purchaseQuery.Contains("@param") Then
            Throw New Exception("فشل في بناء استعلام المشتريات الآمن")
        End If

        Console.WriteLine("    ✓ تم اختبار بناء استعلامات المشتريات الآمنة")

        ' اختبار معالجة بيانات الفاتورة
        TestBillDataProcessing("Purchase")

        Console.WriteLine("    ✓ تم اختبار نماذج المشتريات بنجاح")
    End Sub

    ''' <summary>
    ''' اختبار نماذج المحاسبة
    ''' </summary>
    Private Shared Sub TestAccountingForms()
        ' اختبار بناء استعلام الميزانية الآمن
        Dim balanceQueryBuilder As New SafeQueryBuilder(
            "SELECT ACCNumber, ACCName, ACCDebtor, ACCCreditor FROM FINALBALANCE WHERE ACCNumber <> @excludeValue"
        )

        balanceQueryBuilder.AddCustomCondition("", New Dictionary(Of String, Object) From {{"@excludeValue", ""}})
        balanceQueryBuilder.AddWhereCondition("ACCName", "حساب تجريبي")

        Dim balanceQuery As String = balanceQueryBuilder.Build()

        If Not balanceQuery.Contains("@excludeValue") Then
            Throw New Exception("فشل في بناء استعلام الميزانية الآمن")
        End If

        Console.WriteLine("    ✓ تم اختبار بناء استعلامات الميزانية الآمنة")

        ' اختبار حساب الأرصدة المالية
        TestFinancialCalculations()

        Console.WriteLine("    ✓ تم اختبار نماذج المحاسبة بنجاح")
    End Sub

    ''' <summary>
    ''' اختبار نماذج الموظفين
    ''' </summary>
    Private Shared Sub TestEmployeeForms()
        ' اختبار بناء استعلام الموظفين الآمن
        Dim employeeQueryBuilder As New SafeQueryBuilder(
            "SELECT EMPID, NameEmployee, Address, Phone FROM Employees WHERE EMPID <> @excludeValue"
        )

        employeeQueryBuilder.AddCustomCondition("", New Dictionary(Of String, Object) From {{"@excludeValue", ""}})
        employeeQueryBuilder.AddWhereCondition("NameEmployee", "موظف تجريبي")
        employeeQueryBuilder.AddDateRangeCondition("DateAppointment", DateTime.Now.AddYears(-5), DateTime.Now)

        Dim employeeQuery As String = employeeQueryBuilder.Build()

        If Not employeeQuery.Contains("@excludeValue") Then
            Throw New Exception("فشل في بناء استعلام الموظفين الآمن")
        End If

        Console.WriteLine("    ✓ تم اختبار بناء استعلامات الموظفين الآمنة")

        ' اختبار معالجة بيانات الراتب
        TestSalaryDataProcessing()

        Console.WriteLine("    ✓ تم اختبار نماذج الموظفين بنجاح")
    End Sub

    ''' <summary>
    ''' اختبار معالجة بيانات الفاتورة
    ''' </summary>
    Private Shared Sub TestBillDataProcessing(billType As String)
        ' محاكاة معالجة بيانات الفاتورة
        Dim billData As New Dictionary(Of String, Object) From {
            {"bill_no", "TEST001"},
            {"Vendorname", "عميل تجريبي"},
            {"bill_date", DateTime.Now},
            {"totalpricebeforedisc", 1000},
            {"disc", 50},
            {"totalpriceafterdisc", 950}
        }

        ' التحقق من صحة البيانات
        For Each item In billData
            If item.Value Is Nothing Then
                Throw New Exception($"بيانات فارغة في {item.Key}")
            End If
        Next

        Console.WriteLine($"    ✓ تم اختبار معالجة بيانات فاتورة {billType}")
    End Sub

    ''' <summary>
    ''' اختبار الحسابات المالية
    ''' </summary>
    Private Shared Sub TestFinancialCalculations()
        ' محاكاة حسابات مالية
        Dim debitAmount As Decimal = 1000
        Dim creditAmount As Decimal = 800
        Dim balance As Decimal = debitAmount - creditAmount

        If balance <> 200 Then
            Throw New Exception("خطأ في الحسابات المالية")
        End If

        Console.WriteLine("    ✓ تم اختبار الحسابات المالية")
    End Sub

    ''' <summary>
    ''' اختبار معالجة بيانات الراتب
    ''' </summary>
    Private Shared Sub TestSalaryDataProcessing()
        ' محاكاة حساب الراتب
        Dim basicSalary As Decimal = 5000
        Dim allowances As Decimal = 1000
        Dim deductions As Decimal = 500
        Dim netSalary As Decimal = basicSalary + allowances - deductions

        If netSalary <> 5500 Then
            Throw New Exception("خطأ في حساب الراتب")
        End If

        Console.WriteLine("    ✓ تم اختبار حساب الراتب")
    End Sub

    ' ==================== اختبارات معالجة الأخطاء ====================

    ''' <summary>
    ''' اختبار معالجة أخطاء قاعدة البيانات
    ''' </summary>
    Private Shared Sub TestDatabaseErrorHandling()
        ' محاولة تنفيذ استعلام خاطئ
        Try
            Dim invalidQuery As String = "SELECT * FROM NonExistentTable"
            SecureDatabaseManager.Instance.ExecuteQuery(invalidQuery, New Dictionary(Of String, Object))

            ' إذا وصلنا هنا، فهناك مشكلة
            Throw New Exception("لم يتم التعامل مع خطأ قاعدة البيانات بشكل صحيح")

        Catch ex As Exception When Not ex.Message.Contains("لم يتم التعامل")
            ' هذا متوقع - تم التعامل مع الخطأ بشكل صحيح
            Console.WriteLine("    ✓ تم التعامل مع خطأ قاعدة البيانات بشكل صحيح")
        End Try

        ' اختبار معالجة اتصال قاعدة البيانات المفقود
        Try
            Using connection As SqlConnection = SecureDatabaseManager.Instance.CreateConnection()
                ' محاولة تنفيذ عملية مع اتصال مغلق
                connection.Close()
                Dim command As New SqlCommand("SELECT 1", connection)
                command.ExecuteScalar()
            End Using

        Catch ex As Exception
            Console.WriteLine("    ✓ تم التعامل مع خطأ الاتصال المغلق بشكل صحيح")
        End Try
    End Sub

    ''' <summary>
    ''' اختبار معالجة البيانات الفارغة
    ''' </summary>
    Private Shared Sub TestNullDataHandling()
        ' اختبار SafeQueryBuilder مع قيم فارغة
        Dim queryBuilder As New SafeQueryBuilder("SELECT * FROM users WHERE active = 1")

        ' إضافة قيم فارغة - يجب أن يتم تجاهلها
        queryBuilder.AddWhereCondition("username", Nothing)
        queryBuilder.AddWhereCondition("email", "")
        queryBuilder.AddWhereCondition("phone", "   ")

        Dim query As String = queryBuilder.Build()

        ' يجب أن يحتوي الاستعلام على الشرط الأساسي فقط
        If queryBuilder.Parameters.Count > 0 Then
            Throw New Exception("لم يتم تجاهل القيم الفارغة بشكل صحيح")
        End If

        Console.WriteLine("    ✓ تم التعامل مع القيم الفارغة بشكل صحيح")

        ' اختبار معالجة DBNull
        Dim testData As Object = DBNull.Value
        Dim safeValue As String = If(testData Is DBNull.Value, "", testData.ToString())

        If safeValue <> "" Then
            Throw New Exception("لم يتم التعامل مع DBNull بشكل صحيح")
        End If

        Console.WriteLine("    ✓ تم التعامل مع DBNull بشكل صحيح")
    End Sub

    ''' <summary>
    ''' اختبار معالجة الاستثناءات
    ''' </summary>
    Private Shared Sub TestExceptionHandling()
        ' اختبار SafeExecute مع استثناء
        Dim result As String = ErrorHandler.SafeExecute(
            Function() As String
                Throw New InvalidOperationException("خطأ اختباري")
                Return "لن يصل هنا"
            End Function,
            "قيمة افتراضية",
            "اختبار الاستثناء"
        )

        If result <> "قيمة افتراضية" Then
            Throw New Exception("لم يتم التعامل مع الاستثناء بشكل صحيح")
        End If

        Console.WriteLine("    ✓ تم التعامل مع الاستثناءات بشكل صحيح")

        ' اختبار SafeExecute للإجراءات
        Dim exceptionThrown As Boolean = False

        ErrorHandler.SafeExecute(
            Sub()
                exceptionThrown = True
                Throw New Exception("خطأ اختباري في الإجراء")
            End Sub,
            "اختبار استثناء الإجراء"
        )

        If Not exceptionThrown Then
            Throw New Exception("لم يتم تنفيذ الإجراء")
        End If

        Console.WriteLine("    ✓ تم التعامل مع استثناءات الإجراءات بشكل صحيح")
    End Sub

    ' ==================== اختبارات الأداء ====================

    ''' <summary>
    ''' اختبار أداء الاستعلامات
    ''' </summary>
    Private Shared Sub TestQueryPerformance()
        Dim stopwatch As New Stopwatch()
        Dim iterations As Integer = 100

        stopwatch.Start()

        For i As Integer = 1 To iterations
            Dim queryBuilder As New SafeQueryBuilder("SELECT * FROM users WHERE active = 1")
            queryBuilder.AddWhereCondition("id", i)
            queryBuilder.AddWhereCondition("username", $"user{i}")

            Dim query As String = queryBuilder.Build()
        Next

        stopwatch.Stop()

        Dim averageTime As Double = stopwatch.ElapsedMilliseconds / iterations

        If averageTime > 10 Then ' أكثر من 10 مللي ثانية للاستعلام الواحد
            Throw New Exception($"أداء الاستعلامات بطيء: {averageTime:F2} مللي ثانية للاستعلام الواحد")
        End If

        Console.WriteLine($"    ✓ أداء الاستعلامات جيد: {averageTime:F2} مللي ثانية للاستعلام الواحد")
    End Sub

    ''' <summary>
    ''' اختبار إدارة الذاكرة
    ''' </summary>
    Private Shared Sub TestMemoryManagement()
        Dim initialMemory As Long = GC.GetTotalMemory(False)

        ' إنشاء عدد كبير من الكائنات
        For i As Integer = 1 To 1000
            Dim queryBuilder As New SafeQueryBuilder("SELECT * FROM test")
            queryBuilder.AddWhereCondition("field", i)
            Dim query As String = queryBuilder.Build()
        Next

        ' تشغيل جامع القمامة
        GC.Collect()
        GC.WaitForPendingFinalizers()
        GC.Collect()

        Dim finalMemory As Long = GC.GetTotalMemory(False)
        Dim memoryIncrease As Long = finalMemory - initialMemory

        ' يجب ألا تزيد الذاكرة بشكل كبير
        If memoryIncrease > 1024 * 1024 Then ' أكثر من 1 ميجابايت
            Console.WriteLine($"    ⚠️ زيادة في استخدام الذاكرة: {memoryIncrease / 1024} كيلوبايت")
        Else
            Console.WriteLine($"    ✓ إدارة الذاكرة جيدة: زيادة {memoryIncrease / 1024} كيلوبايت")
        End If
    End Sub

    ''' <summary>
    ''' اختبار Connection Pooling
    ''' </summary>
    Private Shared Sub TestConnectionPooling()
        Try
            Dim connections As New List(Of SqlConnection)

            ' إنشاء عدة اتصالات
            For i As Integer = 1 To 5
                Dim connection As SqlConnection = SecureDatabaseManager.Instance.CreateConnection()
                connections.Add(connection)
            Next

            ' إغلاق الاتصالات
            For Each connection In connections
                connection.Dispose()
            Next

            Console.WriteLine("    ✓ تم اختبار Connection Pooling بنجاح")

        Catch ex As Exception
            Console.WriteLine($"    ⚠️ تم تخطي اختبار Connection Pooling: {ex.Message}")
        End Try
    End Sub

    ' ==================== إنشاء التقرير النهائي ====================

    ''' <summary>
    ''' إنشاء التقرير النهائي للاختبارات
    ''' </summary>
    Private Shared Sub GenerateFinalReport()
        Dim totalTime As TimeSpan = DateTime.Now - testStartTime
        Dim passedTests As Integer = testResults.Count(Function(t) t.Status = "PASSED")
        Dim failedTests As Integer = testResults.Count(Function(t) t.Status = "FAILED")
        Dim totalTests As Integer = testResults.Count

        Console.WriteLine()
        Console.WriteLine("=" * 60)
        Console.WriteLine("📊 التقرير النهائي للاختبار الشامل")
        Console.WriteLine("=" * 60)

        Console.WriteLine($"⏱️ إجمالي وقت التنفيذ: {totalTime.TotalSeconds:F2} ثانية")
        Console.WriteLine($"📈 إجمالي الاختبارات: {totalTests}")
        Console.WriteLine($"✅ الاختبارات الناجحة: {passedTests}")
        Console.WriteLine($"❌ الاختبارات الفاشلة: {failedTests}")
        Console.WriteLine($"📊 معدل النجاح: {(passedTests * 100.0 / totalTests):F1}%")

        Console.WriteLine()
        Console.WriteLine("📋 تفاصيل الاختبارات:")
        Console.WriteLine("-" * 60)

        For Each result In testResults
            Dim statusIcon As String = If(result.Status = "PASSED", "✅", "❌")
            Console.WriteLine($"{statusIcon} {result.TestName}: {result.Status} ({result.ExecutionTime.TotalMilliseconds:F0}ms)")

            If result.Status = "FAILED" Then
                Console.WriteLine($"    💬 {result.Message}")
            End If
        Next

        ' إنشاء ملف تقرير
        CreateReportFile(totalTime, passedTests, failedTests, totalTests)

        Console.WriteLine()
        Console.WriteLine("🎯 ملخص الأمان:")
        Console.WriteLine("-" * 30)

        Dim securityTests As List(Of TestResult) = testResults.Where(Function(t) t.TestName.Contains("الأمان") OrElse
                                                                     t.TestName.Contains("SQL Injection") OrElse
                                                                     t.TestName.Contains("تشفير") OrElse
                                                                     t.TestName.Contains("SecureDatabaseManager") OrElse
                                                                     t.TestName.Contains("SafeQueryBuilder")).ToList()

        Dim securityPassed As Integer = securityTests.Count(Function(t) t.Status = "PASSED")
        Dim securityTotal As Integer = securityTests.Count

        If securityTotal > 0 Then
            Console.WriteLine($"🔒 اختبارات الأمان: {securityPassed}/{securityTotal} ({(securityPassed * 100.0 / securityTotal):F1}%)")

            If securityPassed = securityTotal Then
                Console.WriteLine("🎉 جميع اختبارات الأمان نجحت! النظام آمن.")
            Else
                Console.WriteLine("⚠️ بعض اختبارات الأمان فشلت. يرجى مراجعة المشاكل.")
            End If
        End If

        Console.WriteLine()
        Console.WriteLine("🚀 التوصيات:")
        Console.WriteLine("-" * 15)

        If failedTests = 0 Then
            Console.WriteLine("✨ ممتاز! جميع الاختبارات نجحت.")
            Console.WriteLine("✅ النظام جاهز للاستخدام الآمن.")
            Console.WriteLine("📝 يُنصح بإجراء اختبارات دورية.")
        Else
            Console.WriteLine("🔧 يرجى إصلاح الاختبارات الفاشلة قبل النشر.")
            Console.WriteLine("📋 راجع ملفات السجل للحصول على تفاصيل أكثر.")
            Console.WriteLine("🔄 أعد تشغيل الاختبارات بعد الإصلاح.")
        End If

        Console.WriteLine()
        Console.WriteLine("=" * 60)
        Console.WriteLine("🏁 انتهى الاختبار الشامل")
        Console.WriteLine("=" * 60)
    End Sub

    ''' <summary>
    ''' إنشاء ملف تقرير مفصل
    ''' </summary>
    Private Shared Sub CreateReportFile(totalTime As TimeSpan, passedTests As Integer, failedTests As Integer, totalTests As Integer)
        Try
            Dim reportPath As String = Path.Combine("Logs", $"SecurityTestReport_{DateTime.Now:yyyyMMdd_HHmmss}.txt")

            ' التأكد من وجود مجلد السجلات
            If Not Directory.Exists("Logs") Then
                Directory.CreateDirectory("Logs")
            End If

            Using writer As New StreamWriter(reportPath, False, System.Text.Encoding.UTF8)
                writer.WriteLine("تقرير الاختبار الشامل لنظام FIT SOFT")
                writer.WriteLine("=" * 50)
                writer.WriteLine($"تاريخ التشغيل: {DateTime.Now}")
                writer.WriteLine($"إجمالي وقت التنفيذ: {totalTime.TotalSeconds:F2} ثانية")
                writer.WriteLine($"إجمالي الاختبارات: {totalTests}")
                writer.WriteLine($"الاختبارات الناجحة: {passedTests}")
                writer.WriteLine($"الاختبارات الفاشلة: {failedTests}")
                writer.WriteLine($"معدل النجاح: {(passedTests * 100.0 / totalTests):F1}%")
                writer.WriteLine()

                writer.WriteLine("تفاصيل الاختبارات:")
                writer.WriteLine("-" * 30)

                For Each result In testResults
                    writer.WriteLine($"اسم الاختبار: {result.TestName}")
                    writer.WriteLine($"الحالة: {result.Status}")
                    writer.WriteLine($"وقت التنفيذ: {result.ExecutionTime.TotalMilliseconds:F0} مللي ثانية")
                    writer.WriteLine($"الرسالة: {result.Message}")

                    If Not String.IsNullOrEmpty(result.ErrorDetails) Then
                        writer.WriteLine("تفاصيل الخطأ:")
                        writer.WriteLine(result.ErrorDetails)
                    End If

                    writer.WriteLine("-" * 30)
                Next
            End Using

            Console.WriteLine($"📄 تم إنشاء تقرير مفصل: {reportPath}")

        Catch ex As Exception
            Console.WriteLine($"⚠️ فشل في إنشاء ملف التقرير: {ex.Message}")
        End Try
    End Sub
End Class
