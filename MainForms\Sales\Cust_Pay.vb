﻿Imports CrystalDecisions.CrystalReports.Engine

Public Class Cust_Pay

    Dim VND_no As String
    Dim TotalAccountBeforPid As String
    Dim PreviousBalance As Double = 0
    Dim CurrentBalance As Double = 0
    Dim Amntcredit, Amntdebit, AmntcreditPrevious, AmntdebitPrevious, AmountDebitCreditPrevious, AmountDebitCreditAfter As Double
    Dim CodeID As Integer = 0

    Private Sub Cust_Pay_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        PanelChooseDesign.Top = 5000
        PanelSearch.Top = 5000
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        VendorAccount()
        Cls.fill_combo_Branch("Treasury", "Treasury_Name", cmbTreasuryName)
        cmbTreasuryName.Text = Cls.Get_Code_Value_Branch_More("Treasury", "Treasury_Name", "Treasury_Code=N'" & Treasury_Code & "'")
        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", Cmbvendorname)
        End If
        Cls.fill_combo("Check_Status", "StatusNameAr", cmbCheck_Status)
        Cls.fill_combo("BankData", "BankName", cmbBankName)
        MAXRECORD("Vst")
        Dim VstNo As Double = VND_no
        MAXRECORD("Vst_Check_Type")
        Dim Vst_CheckNo As Double = VND_no
        If VstNo > Vst_CheckNo Then
            txtno.Text = VstNo
        Else
            txtno.Text = Vst_CheckNo
        End If

        GetEmployeeCustody()

        GetDesignRptCustPay()

        If ActivateAddDepositBankCustomerPayments = "YES" Then
            RadioButton3.Visible = True
        Else
            RadioButton3.Visible = False
        End If

        If TreasuryControl = "YES" Then
            lblTreasuryName.Visible = True
            cmbTreasuryName.Visible = True
        Else
            lblTreasuryName.Visible = False
            cmbTreasuryName.Visible = False
        End If
        GetDateNotBeenActivatedPrograms(dtpvndrecipient)
        GetDateNotBeenActivatedPrograms(dtpDate_Maturity)
        GetDateNotBeenActivatedOutcome()

        If NumberPayNotBeenActivated = "YES" Then
            txtno.Enabled = False
        Else
            txtno.Enabled = True
        End If
        If DateNotBeenActivatedOutcome = "YES" Then
            dtpvndrecipient.Enabled = False
        Else
            dtpvndrecipient.Enabled = True
        End If
    End Sub

    Private Sub GetEmployeeCustody()
        If EmployeeCustody = "YES" Then
            Cls.fill_combo("Employees", "NameEmployee", cmbrecipient)
        Else
            Try
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select distinct VND_rcv From Vst where VND_rcv <> N'بفاتورة' order by 1"
                dr = cmd.ExecuteReader
                cmbrecipient.Items.Clear()
                Do While dr.Read
                    cmbrecipient.Items.Add(Trim(dr(0)))
                Loop
            Catch ex As Exception
                ErrorHandling(ex, Me.Text)
            End Try
        End If


    End Sub
    Private Sub GetDateNotBeenActivatedOutcome()
        If PermtionName <> "مدير" Then
            If DateNotBeenActivatedOutcome = "NO" Then
                dtpvndrecipient.Enabled = True
            Else
                dtpvndrecipient.Enabled = False
            End If
        End If
    End Sub

    'Sub ZeroLbl()
    '    LblValDisc.Text = "إجمالي المبالغ المدفوعة " & "  " & "0" & "  " & "ج " : dr.Close()
    '    LblValPay.Text = "إجمالي المديونية" & "  " & "0" & "  " & "ج" : dr.Close()
    '    lblValSal.Text = "إجمالي المشتريات" & "  " & "0" & "  " & "ك" : dr.Close()
    '    WIDTHLBLE()
    'End Sub

    'Sub CustmersAccount()
    '    If Cmbvendorname.Text.Trim = "" Then Exit Sub

    '    lblValSal.Text = "القيمة" & "  " & Return_Alf_Genih(IM.VnTinF(Cmbvendorname.Text.Trim)) & "  " & "ج "

    '    LblValPay.Text = "المدفوع " & "  " & Return_Alf_Genih(IM.VnPayF(Cmbvendorname.Text.Trim)) & "  " & "ج "

    '    LblValDisc.Text = "الخصومات " & "  " & Return_Alf_Genih(IM.VnDiscF(Cmbvendorname.Text.Trim)) & "  " & "ج "

    '    lblCredit.Text = IM.VNState(Cmbvendorname.Text.Trim)

    '    WIDTHLBLE()

    'End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If

        GetNumericValueSeparators()

        If ValidateTextAdd() = False Then Exit Sub


        If txtCredit.Text <> 0 Then
            TotalAccountBeforPid = Val(txtCredit.Text)
        End If
        If txtDebit.Text <> 0 Then
            TotalAccountBeforPid = Val(txtDebit.Text)
        End If

        Custmersfinance()

        Get_Movement_In_Out_Money(dtpvndrecipient.Text, Treasury_Code)

        '===============================================================================
        Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
        If UseAccounts = "YES" Then
            Daily_Restrictions()
        End If
        '===============================================================================
        If chkprint.Checked = True Then
            PrintReport()
        Else
            'MsgBox("تم إجراء العملية بنجاح", MsgBoxStyle.Information)
        End If

        Cmbvendorname.SelectedIndex = -1

        cmbrecipient.Text = ""
        txtno.Text = ""
        txtammnt.Text = ""
        Cmbvendorname.Text = ""

        MAXRECORD("Vst")
        Dim VstNo As Double = VND_no
        MAXRECORD("Vst_Check_Type")
        Dim Vst_CheckNo As Double = VND_no
        If VstNo > Vst_CheckNo Then
            txtno.Text = VstNo
        Else
            txtno.Text = Vst_CheckNo
        End If

        Cmbvendorname.Focus()
        Cmbvendorname.SelectAll()

    End Sub
    Function ValidateTextAdd() As Boolean

        If Trim(Cmbvendorname.Text) = "" Then
            MsgBox("فضلاً أختر أسم العميل", MsgBoxStyle.Exclamation)
            Cmbvendorname.Focus()
            Exit Function
        End If

        If Val(txtammnt.Text) = 0 Then
            MsgBox("فضلاً أدخل المبلغ", MsgBoxStyle.Exclamation)
            txtammnt.Focus()
            Return False
        End If

        If Trim(cmbrecipient.Text) = "" Then
            MsgBox("فضلاً أدخل المستلم", MsgBoxStyle.Exclamation)
            cmbrecipient.Focus()
            Return False
        End If

        If Trim(txtno.Text) = "" Then
            MsgBox("فضلاً أدخل رقم الإيصال", MsgBoxStyle.Exclamation)
            txtno.Focus()
            Return False
        End If
        If cmbTreasuryName.Visible = True Then
            If Trim(cmbTreasuryName.Text) = "" Then
                MsgBox("فضلاً أدخل أسم الخزينة", MsgBoxStyle.Exclamation)
                cmbTreasuryName.Focus()
                Return False
            End If
        End If

        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If rdoDiscountCashPayment.Checked = True Then
                cmd.CommandText = "Select * From vst where VND_no =N'" & txtno.Text & "'"
            Else
                cmd.CommandText = "Select * From Vst_Receipts where VND_no =N'" & txtno.Text & "'"
            End If
            dr = cmd.ExecuteReader()
            If dr.HasRows = True Then
                MsgBox("عفواً يوجد رقم ايصال او شيك مسجل مسبقا بنفس الرقم", MsgBoxStyle.Exclamation)
                Return False : Exit Function
            Else
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * From Customers where Vendorname =N'" & Cmbvendorname.Text & "'"
            dr = cmd.ExecuteReader()
            If dr.HasRows = False Then
                MsgBox("عفواً لا يوجد عميل مسجل بنفس الاسم", MsgBoxStyle.Exclamation)
                Return False : Exit Function
            Else
            End If

            If cmbTreasuryName.Visible = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "Select * From Treasury where Treasury_Name =N'" & cmbTreasuryName.Text & "'"
                dr = cmd.ExecuteReader()
                If dr.HasRows = False Then
                    MsgBox("عفواً لا يوجد خزينة مسجل بنفس الاسم", MsgBoxStyle.Exclamation)
                    Return False : Exit Function
                Else
                End If
            End If

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
        Return True
    End Function

    Sub Custmersfinance()
        'Try
        GetDebtorlCreditorPrevious()

            Dim Employees As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select dbo.Customers.Vendorname, dbo.Employees.NameEmployee  From dbo.Customers INNER Join  dbo.Employees ON dbo.Customers.Emp_Code = dbo.Employees.EMPID  Where (dbo.Customers.Vendorname = N'" & Cmbvendorname.Text & "')"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                Employees = dr("NameEmployee").ToString
            End If

            Dim TreasuryCode As Integer
            If cmbTreasuryName.Visible = True Then
                TreasuryCode = Cls.Get_Code_Value_Branch_More("Treasury", "Treasury_Code", "Treasury_Name=N'" & cmbTreasuryName.Text & "'")
            Else
                TreasuryCode = Treasury_Code
            End If

            '===============================================================
            Dim MTHODX As String = ""
            Dim Date_Maturity As String = "0"
            Dim Check_Type As String = ""
            Dim CashBank As Integer = 0
            Dim Type_CurrentBalanceCustVnd As String = ""
        Dim Check_Status_ID As Integer = 0
        Dim BankID As Integer = 0

        If RadioButton1.Checked = True Then
            MTHODX = "نقدي"
            Date_Maturity = "0"
            Check_Type = "مفتوح"
            CashBank = 0
        ElseIf RadioButton2.Checked = True Then
            MTHODX = "شيك"
            Date_Maturity = Cls.C_date(dtpDate_Maturity.Text)
            Check_Type = "مغلق"
            CashBank = 0
            Check_Status_ID = Cls.Get_Code_Value_Branch_More("Check_Status", "StatusId", "StatusNameAr=N'" & cmbCheck_Status.Text & "'")
            BankID = Cls.Get_Code_Value_Branch_More("BankData", "BankID", "BankName=N'" & cmbBankName.Text & "'")

        ElseIf RadioButton3.Checked = True Then
            MTHODX = "بنك"
            Date_Maturity = "0"
            Check_Type = "مفتوح"
            CashBank = 1
        ElseIf RadioButton4.Checked = True Then
            MTHODX = "محفظة"
            Date_Maturity = "0"
            Check_Type = "مفتوح"
            CashBank = 1
        End If
        If rdoDiscountCashPayment.Checked = True Then
            If RadioButton1.Checked = True Or RadioButton3.Checked = True Or RadioButton4.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into vst (Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,VND_no,billno,UserName,VND_Date_Maturity,Check_Type,EmpName,Treasury_Code,CashBank) values"
                S = S & " (N'" & Company_Branch_ID & "',N'" & Cmbvendorname.Text & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(dtpvndrecipient.Text) & "'," & txtammnt.Text.Trim & ",N'" & MTHODX & "',N'" & cmbrecipient.Text & "',N'" & txtdet.Text.Trim & "',N'" & txtno.Text & "',N'دفعة نقدية',N'" & UserName & "',N'" & Date_Maturity & "',N'" & Check_Type & "',N'" & Employees & "',N'" & TreasuryCode & "',N'" & CashBank & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
                Type_CurrentBalanceCustVnd = "vst"
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into Vst_Check_Type (Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,VND_no,billno,UserName,VND_Date_Maturity,Check_Type,PaymentTotal,Treasury_Code,Check_Status_ID,BankID) values"
                S = S & " (N'" & Company_Branch_ID & "',N'" & Cmbvendorname.Text & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(dtpvndrecipient.Text) & "'," & txtammnt.Text.Trim & ",N'" & MTHODX & "',N'" & cmbrecipient.Text & "',N'" & txtdet.Text.Trim & "',N'" & txtno.Text & "',N'دفعة نقدية',N'" & UserName & "',N'" & Date_Maturity & "',N'" & Check_Type & "'," & txtammnt.Text.Trim & ",N'" & TreasuryCode & "'," & Check_Status_ID & "," & BankID & ")"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
                    Type_CurrentBalanceCustVnd = "Vst_Check_Type"
                End If
            End If

        If rdoAddCashPayment.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Vst_Receipts (Company_Branch_ID,Vendorname,VND_XTM,VND_dt,VND_amx,VND_ho,VND_rcv,VND_dec,VND_no,billno,UserName,VND_Date_Maturity,EmpName,Treasury_Code) values"
            S = S & " (N'" & Company_Branch_ID & "',N'" & Cmbvendorname.Text & "',N'" & Cls.get_time(True) & "',N'" & Cls.C_date(dtpvndrecipient.Text) & "'," & txtammnt.Text.Trim & ",N'" & MTHODX & "',N'" & cmbrecipient.Text & "',N'" & txtdet.Text.Trim & "',N'" & txtno.Text & "',N'دفعة نقدية',N'" & UserName & "',N'" & Date_Maturity & "',N'" & Employees & "',N'" & TreasuryCode & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
            Type_CurrentBalanceCustVnd = "Vst_Receipts"
        End If

        If chkCustody.Checked = True Then
            Dim EmployeeID As String = Cls.Get_Code_Value_More("Employees", "EMPID", "NameEmployee =N'" & cmbrecipient.Text & "'")
            Dim StatusID As String = 1

            MAXRECORDALL("Custody", "CustodyID")

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Dim S As String = "INSERT INTO Custody (CustodyID, EMPID, Amount, ReceiptNumber, PaymentMethod, Notes, Status, CreatedBy) VALUES ("
            S &= "N'" & CodeID & "', "
            S &= "N'" & EmployeeID & "', "
            S &= "N'" & txtammnt.Text & "', "
            S &= "N'" & txtno.Text & "', "
            S &= "N'" & MTHODX & "', "
            S &= "N'" & txtdet.Text & "', "
            S &= "N'" & StatusID & "', "
            S &= "N'" & UserName & "')"

            cmd.CommandText = S
            cmd.ExecuteNonQuery()
        End If


        If RadioButton3.Checked = True Then
            If ActivateAddDepositBankCustomerPayments = "YES" Then
                GetBankCustPayments()
            End If
        End If

        IM.CustomerAccountTotal(Cmbvendorname.Text.Trim)
        VendorAccount()

            Dim CurrentBalanceCustVnd As String = GetCurrentBalanceCustVnd(Cmbvendorname.Text)
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            If Type_CurrentBalanceCustVnd = "vst" Then
                cmd.CommandText = "update vst set CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where   VND_no =N'" & txtno.Text & "'" : cmd.ExecuteNonQuery()
            End If
            If Type_CurrentBalanceCustVnd = "Vst_Check_Type" Then
                cmd.CommandText = "update Vst_Check_Type set CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where   VND_no =N'" & txtno.Text & "'" : cmd.ExecuteNonQuery()
            End If
            If Type_CurrentBalanceCustVnd = "Vst_Receipts" Then
                cmd.CommandText = "update Vst_Receipts set CurrentBalanceCustVnd = " & Val(CurrentBalanceCustVnd) & " where  VND_no =N'" & txtno.Text & "'" : cmd.ExecuteNonQuery()
            End If

            'Dim AmntcreditAfter, AmntdebitAfter As Double
            'If txtCredit.Text <> 0 Then
            '    AmntcreditAfter = Val(txtCredit.Text) - Val(txtammnt.Text)
            'End If
            'If txtDebit.Text <> 0 Then
            '    AmntdebitAfter = Val(txtDebit.Text) - Val(txtammnt.Text)
            'End If

            'AmntcreditPrevious

            AmountDebitCreditPrevious = Val(AmntcreditPrevious) - Val(AmntdebitPrevious)
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Vst set CreditPrevious = " & Val(AmntcreditPrevious) & ",DebitPrevious = " & Val(AmntdebitPrevious) & ",CreditCurrent = " & Val(txtCredit.Text) & ",DebitCurrent = " & Val(txtDebit.Text) & " where VND_no =N'" & txtno.Text & "'" : cmd.ExecuteNonQuery()

        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try



    End Sub

    Sub VendorAccount()

        If Cmbvendorname.Text.Trim = "" Then Exit Sub
        'Try
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select vintinval,valuereturns,vndiscount,VnPay,VnReceipts,ValueVAT,Vst_Check_Type,vnamntcredit,vnamntdebit from Customers where Vendorname =N'" & Cmbvendorname.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                txtValSal.Text = FormatNumberWithSeparators(dr("vintinval").ToString)
                txtBsal.Text = FormatNumberWithSeparators(dr("valuereturns").ToString)
                txtValDisc.Text = FormatNumberWithSeparators(dr("vndiscount").ToString)
                txtValPay.Text = FormatNumberWithSeparators(dr("VnPay").ToString)
                txtVnReceipts.Text = FormatNumberWithSeparators(dr("VnReceipts").ToString)
                txtValueVAT.Text = FormatNumberWithSeparators(dr("ValueVAT").ToString)
                txtVst_Check_Type.Text = FormatNumberWithSeparators(dr("Vst_Check_Type").ToString)
                txtCredit.Text = FormatNumberWithSeparators(dr("vnamntcredit").ToString)
                txtDebit.Text = FormatNumberWithSeparators(dr("vnamntdebit").ToString)
                If txtDebit.Text <> "0" Then
                    txtDebit.Text = "-" + txtDebit.Text
                End If
                lblCustomerBalance.Text = "حساب العميل"
            End If

            Vendor_Customer = Cls.Get_Code_Value_Branch_More("Vendor_Customer", "Vendorname", "Vendorname=N'" & Cmbvendorname.Text & "'")
            If Vendor_Customer = Cmbvendorname.Text Then
                txtCredit.Text = 0 : txtDebit.Text = 0
                Dim CurrentBalanceCustVnd As String = GetCurrentBalanceCustVnd(Cmbvendorname.Text)
                If CurrentBalanceCustVnd < 0 Then
                    txtCredit.Text = FormatNumberWithSeparators(FormatNumberWithSeparators(CurrentBalanceCustVnd))
                Else
                    txtDebit.Text = FormatNumberWithSeparators(FormatNumberWithSeparators(CurrentBalanceCustVnd))
                End If
                lblCustomerBalance.Text = "حساب عميل ومورد"
            End If

        'Catch ex As Exception
        '    ErrorHandling(ex, Me.Text)
        'End Try
    End Sub

    Function Return_Alf_Genih(ByVal X As Integer) As String
        If Len(X) <= 3 Then Return X : Exit Function
        If Len(X) > 3 Then
            Dim M As Integer
            M = Int(X - 1000 * Int(X / 1000))
            Dim MM As Integer = Int(X / 1000)
            Return M & " | " & MM
        End If
    End Function

    Private Sub Cmbvendorname_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Cmbvendorname.KeyUp
        If e.KeyCode = 13 Then
            cmbrecipient.Focus()
        End If
    End Sub

    Private Sub Cmbvendorname_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Cmbvendorname.SelectedIndexChanged
        If Cmbvendorname.Text.Trim = "" Then Exit Sub
        VendorAccount()
    End Sub

    Private Sub txtammnt_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtammnt.KeyUp
        If e.KeyCode = 13 Then
            txtammnt.Text = FormatNumberWithSeparators(txtammnt.Text)
            If txtno.Enabled = False Then
                Button1.PerformClick()
            Else
                txtdet.Focus()
            End If
        End If
    End Sub

    Private Sub txtammnt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtammnt.TextChanged
        MyVars.CheckNumber(txtammnt)
    End Sub

    Private Sub txtno_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtno.KeyUp
        If e.KeyCode = 13 Then
            Button1.PerformClick()
        End If
    End Sub

    Private Sub txtno_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtno.TextChanged
        MyVars.CheckNumber(txtno)
    End Sub

    Private Sub RadioButton1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RadioButton1.CheckedChanged
        radiobtnessal()
        If RadioButton1.Checked = True Then
            lblDate_Maturity.Visible = False
            dtpDate_Maturity.Visible = False
            lblCheck_Status.Visible = False
            cmbCheck_Status.Visible = False
            lblBankName.Visible = False
            cmbBankName.Visible = False
        End If
        If RadioButton2.Checked = True Then
            lblDate_Maturity.Visible = True
            dtpDate_Maturity.Visible = True
            lblCheck_Status.Visible = True
            cmbCheck_Status.Visible = True
            lblBankName.Visible = True
            cmbBankName.Visible = True
        End If
    End Sub

    Sub radiobtnessal()
        If RadioButton1.Checked = True Or RadioButton3.Checked = True Or RadioButton4.Checked = True Then
            LblEsal.Text = "أدخل رقم الأيصال"
        ElseIf RadioButton2.Checked = True Then
            LblEsal.Text = "أدخل رقم الشيك"
        End If
    End Sub

    Private Sub RadioButton2_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RadioButton2.CheckedChanged
        radiobtnessal()
        If RadioButton1.Checked = True Then
            lblDate_Maturity.Visible = False
            dtpDate_Maturity.Visible = False
            lblCheck_Status.Visible = False
            cmbCheck_Status.Visible = False
            lblBankName.Visible = False
            cmbBankName.Visible = False
        End If
        If RadioButton2.Checked = True Then
            lblDate_Maturity.Visible = True
            dtpDate_Maturity.Visible = True
            lblCheck_Status.Visible = True
            cmbCheck_Status.Visible = True
            lblBankName.Visible = True
            cmbBankName.Visible = True
            cmbCheck_Status.Text = Cls.Get_Code_Value_Branch_More("Check_Status", "StatusNameAr", "StatusId=6")

        End If
    End Sub

    Private Sub MAXRECORD(ByVal Tabel As String)
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select * from " & Tabel & " where VND_no <> N'جرد' and VND_no <> N'دفعة نقدية'"
            dr = cmd.ExecuteReader

            Dim dt As New DataTable
            dt.Load(dr)

            If dt.Rows.Count = 0 Then
                VND_no = 1
            Else
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandType = CommandType.Text
                cmd.CommandText = "SELECT MAX(CAST(VND_no As float)) as mb FROM " & Tabel & " where VND_no <> N'جرد' and VND_no <> N'دفعة نقدية'"
                dr = cmd.ExecuteReader
                dr.Read()

                Dim sh As Long
                sh = dr("mb")
                VND_no = sh + 1
            End If
        Catch ex As Exception
        ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub MAXRECORDALL(ByVal Table As String, ByVal Felds As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " + Table + ""
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            CodeID = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" + Felds + " As int)) as mb FROM " + Table + " where " + Felds + " <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            CodeID = sh + 1
        End If

    End Sub


    Private Sub Daily_Restrictions()
        Try
            Dim Account As String = ""
            Dim AccountCode As String = ""

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مقبوضات عملاء'"
            dr = cmd.ExecuteReader
            If dr.Read Then
                Account = dr("Link_AccountsTree")
                AccountCode = dr("ACCNumber")
            End If

            '========================================================================================


            Dim AccountTreasury As String = "" : Dim AccountCodeTreasury As String = ""
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select ACCName,ACCNumber from AccountsTree where ACCName=N'الخزينة'" : dr = cmd.ExecuteReader
            If dr.Read Then
                AccountTreasury = dr("ACCName") : AccountCodeTreasury = dr("ACCNumber")
            End If

            Dim MOVRegNumber As String = Cls.MAXRECORD("MOVES", "MOVRegNumber")
            Dim MOVID As String = Cls.MAXRECORD("MOVES", "MOVID")
            Dim bill_no As String = Cls.MAXRECORD("Vst", "id") - 1

            If RadioButton1.Checked = True Or RadioButton3.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
                S = S & "N'" & bill_no & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpvndrecipient.Text) & "',N'" & Account & "',N'" & txtammnt.Text & "',N'" & txtammnt.Text & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' من حساب / الخزينة
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpvndrecipient.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'" & txtammnt.Text & "',N'0',N'" & AccountTreasury & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' الى حساب / مقبوضات عملاء
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpvndrecipient.Text) & "',N'" & AccountCode & "',N'" & Account & "',N'0',N'" & txtammnt.Text & "',N'" & Account & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If

        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub rdoDiscountCashPayment_CheckedChanged(sender As Object, e As EventArgs) Handles rdoDiscountCashPayment.CheckedChanged
        MAXRECORD("Vst")
        Dim VstNo As Double = VND_no
        MAXRECORD("Vst_Check_Type")
        Dim Vst_CheckNo As Double = VND_no
        If VstNo > Vst_CheckNo Then
            txtno.Text = VstNo
        Else
            txtno.Text = Vst_CheckNo
        End If
    End Sub

    Private Sub rdoAddCashPayment_CheckedChanged(sender As Object, e As EventArgs) Handles rdoAddCashPayment.CheckedChanged
        MAXRECORD("Vst_Receipts")
        txtno.Text = VND_no
    End Sub

    Private Sub cmbrecipient_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbrecipient.KeyUp
        If e.KeyCode = 13 Then
            txtammnt.Focus()
        End If
    End Sub

    Private Sub PrintReport()


        Dim txt, txtNameEn, txtCmpAddress, txtCmpEmail, txtCmpTel, txtCmpMobile, txtTitelAddress, txtTaxCard As TextObject
        Try
            Cls.delete_Branch_All("PrintSalesPurchases")

            Dim MTHODX As String
            Dim Date_Maturity As String
            Dim CashPayment As String

            Dim TotalAccountAfterPid As String = ""
            If txtCredit.Text <> 0 Then
                TotalAccountAfterPid = Val(txtCredit.Text)
            End If
            If txtDebit.Text <> 0 Then
                TotalAccountAfterPid = Val(txtDebit.Text)
            End If

            If RadioButton1.Checked = True Then
                MTHODX = "نقدي"
                Date_Maturity = ""
            Else
                MTHODX = "شيك"
                Date_Maturity = dtpDate_Maturity.Text
            End If

            If rdoDiscountCashPayment.Checked = True Then
                CashPayment = "خصم من حساب"
            Else
                CashPayment = "إضافة الى حساب"
            End If


            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases(Company_Branch_ID,BILL_NO,Vendorname,CustomerName,price,bill_date,billtime,VnPay,Stat,det,TotalCreditor,vnamntcredit)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & txtno.Text & "',N'" & Cmbvendorname.Text & "',N'" & cmbrecipient.Text & "',N'" & txtammnt.Text & "',N'" & dtpvndrecipient.Text & "',N'" & Date_Maturity & "',N'" & MTHODX & "',N'" & CashPayment & "',N'" & txtdet.Text & "',N'" & TotalAccountBeforPid & "',N'" & TotalAccountAfterPid & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            AddReportView()

            Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
            Dim dt As New DataTable
            dt.Load(dr)

            Dim rpt
            If rdoDesignCustPay1.Checked = True Then
                If PrintSmall = "YES" Then
                    rpt = New Rpt_Cust_Pay_Small
                End If
                If PrintSmall = "NO" Then
                    rpt = New Rpt_Cust_Pay_Sales
                End If
                If PrintSmall = "A5" Then
                    rpt = New Rpt_Cust_Pay_Sales
                End If
            End If
            If rdoDesignCustPay2.Checked = True Then
                If PrintSmall = "YES" Then
                    rpt = New Rpt_Cust_Pay_Small2
                End If
                If PrintSmall = "NO" Then
                    rpt = New Rpt_Cust_Pay_Sales2
                End If
                If PrintSmall = "A5" Then
                    rpt = New Rpt_Cust_Pay_Sales2
                End If
            End If

            rpt.SetDataSource(dt)
            txt = rpt.Section1.ReportObjects("txtTitelAr")
            txt.Text = NameArCompay
            txtNameEn = rpt.Section1.ReportObjects("txtTitelEn")
            txtNameEn.Text = NameEnCompany
            txtCmpAddress = rpt.Section1.ReportObjects("txtAddress")
            txtCmpAddress.Text = CmpAddress
            txtCmpTel = rpt.Section1.ReportObjects("txtTel")
            txtCmpTel.Text = CmpTel
            txtCmpMobile = rpt.Section1.ReportObjects("txtMobile")
            txtCmpMobile.Text = CmpMobile
            txtTitelAddress = rpt.Section1.ReportObjects("txtTitelAddress")
            txtTitelAddress.Text = "ايصال مدفوعات عملاء"
            If SelectLogoPathOther = "YES" Then
                rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
            Else
                rpt.SetParameterValue("ImageURL", CMPLogoPath)
            End If
            Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
            Frm_PrintReports.Text = "ايصال مدفوعات عملاء"
            Frm_PrintReports.Show()
        Catch ex As Exception
            'ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub btnCloseChooseDesign_Click(sender As Object, e As EventArgs) Handles btnCloseChooseDesign.Click
        PanelChooseDesign.Top = 5000
    End Sub

    Private Sub btnViewChooseDesign_Click(sender As Object, e As EventArgs) Handles btnViewChooseDesign.Click
        PanelChooseDesign.Location = New System.Drawing.Point(342, 426)
    End Sub

    Private Sub SetDesignRptCustPay()
        If rdoDesignCustPay1.Checked = True Then
            mykey.SetValue("DesignRptCustPay", "DesignRptCustPay1")
        End If
        If rdoDesignCustPay2.Checked = True Then
            mykey.SetValue("DesignRptCustPay", "DesignRptCustPay2")
        End If
    End Sub

    Private Sub GetDesignRptCustPay()

        Dim DesignRptCustPay As String = mykey.GetValue("DesignRptCustPay", "DesignRptCustPay1")
        If DesignRptCustPay = "DesignRptCustPay1" Then
            rdoDesignCustPay1.Checked = True
        End If
        If DesignRptCustPay = "DesignRptCustPay2" Then
            rdoDesignCustPay2.Checked = True
        End If

    End Sub

    Private Sub rdoDesignCustPay1_CheckedChanged(sender As Object, e As EventArgs) Handles rdoDesignCustPay1.CheckedChanged
        SetDesignRptCustPay()
    End Sub

    Private Sub rdoDesignCustPay2_CheckedChanged(sender As Object, e As EventArgs) Handles rdoDesignCustPay2.CheckedChanged
        SetDesignRptCustPay()
    End Sub

    Private Sub txtsearsh_TextChanged(sender As Object, e As EventArgs) Handles txtsearsh.TextChanged
        'Try
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If txtsearsh.Text = " بحث ...." Or txtsearsh.Text = "" Then
            S = Cls.Get_Select_Grid_S("Cust_Code as [كود العميل],Vendorname as [الاسم],addr  as [العنوان],tel1  as [تليفون]", "customers", "Vendorname <> ''")
        Else
            If rdoName.Checked = True Then
                S = Cls.Get_Select_Grid_S("Cust_Code as [كود العميل],Vendorname as [الاسم],addr  as [العنوان],tel1  as [تليفون]", "customers", "Vendorname Like N'%" & txtsearsh.Text & "%'")
            End If
            If rdoCode.Checked = True Then
                S = Cls.Get_Select_Grid_S("Cust_Code as [كود العميل],Vendorname as [الاسم],addr  as [العنوان],tel1  as [تليفون]", "customers", "Cust_Code Like N'%" & txtsearsh.Text & "%'")
            End If
            If rdoTel.Checked = True Then
                S = Cls.Get_Select_Grid_S("Cust_Code as [كود العميل],Vendorname as [الاسم],addr  as [العنوان],tel1  as [تليفون]", "customers", "tel1 Like N'%" & txtsearsh.Text & "%'")
            End If
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)
        DTGV.Columns(0).Width = 120
        DTGV.Columns(1).Width = 280
        'Catch ex As Exception
        'End Try
    End Sub

    Private Sub DTGV_DoubleClick(sender As Object, e As EventArgs) Handles DTGV.DoubleClick
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Cmbvendorname.Text = DTGV.SelectedRows(0).Cells(1).Value
        PanelSearch.Top = 5000
    End Sub

    Private Sub btnCloseSearch_Click(sender As Object, e As EventArgs) Handles btnCloseSearch.Click
        PanelSearch.Top = 5000
    End Sub

    Private Sub btnSearchCustomer_Click(sender As Object, e As EventArgs) Handles btnSearchCustomer.Click
        PanelSearch.Location = New System.Drawing.Point(12, 141)
        PanelSearch.Size = New System.Drawing.Size(800, 278)
        txtsearsh_TextChanged(sender, e)
    End Sub

    Private Sub GetBankCustPayments()

        Dim BNKID As String = MaxRecordTables("Banks", "BNKID")

        SearchBalanceBanks()

        CurrentBalance = Format(Val(PreviousBalance) + Val(txtammnt.Text) - Val(0), "Fixed")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Banks(BNKID,DateMovement,TypeMovement,PaymentMethod,AccountNumber,AccountNumberTo,DocumentNumber,PreviousBalance,Debtor,Creditor,Transfer,CurrentBalance,DateCollection,Bank,Notes,UserName,Currency_Name,TypeTransfer) values ("
        S = S & "N'" & BNKID & "',N'" & Cls.C_date(dtpvndrecipient.Text) & "',N'ايداع',N'نقداً',"
        S = S & "N'" & BankPayCustAccountNumber & "',N'0',N'" & BankPayCustAccountNumber & "',N'" & PreviousBalance & "',"
        S = S & "N'0',N'" & txtammnt.Text & "',N'0',N'" & CurrentBalance & "',"
        S = S & "N'" & Cls.C_date(dtpvndrecipient.Text) & "',N'" & BankPayCustNameBank & "',N'" & txtdet.Text & "',N'" & UserName & "',N'" & BankPayCustNameCurrency & "',N'0')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        '===============================================================================
        Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
        If UseAccounts = "YES" Then
            Daily_Restrictions_Banks()
        End If
        '===============================================================================
    End Sub

    Private Sub RadioButton4_CheckedChanged(sender As Object, e As EventArgs) Handles RadioButton4.CheckedChanged
        radiobtnessal()
        If RadioButton4.Checked = True Then
            lblDate_Maturity.Visible = False
            dtpDate_Maturity.Visible = False
            lblCheck_Status.Visible = False
            cmbCheck_Status.Visible = False
            lblBankName.Visible = False
            cmbBankName.Visible = False
        End If
        If RadioButton2.Checked = True Then
            lblDate_Maturity.Visible = True
            dtpDate_Maturity.Visible = True
            lblCheck_Status.Visible = True
            cmbCheck_Status.Visible = True
            lblBankName.Visible = True
            cmbBankName.Visible = True
        End If
    End Sub

    Private Sub SearchBalanceBanks()
        'On Error Resume Next

        Dim aray_BNKID As New ArrayList

        aray_BNKID.Clear()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select BNKID,AccountNumberTo from BANKS where AccountNumber =N'" & BankPayCustAccountNumber & "' and Currency_Name =N'" & BankPayCustNameCurrency & "'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_BNKID.Add(dr("BNKID").ToString())
        Loop

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select BNKID,AccountNumberTo from BANKS where AccountNumberTo =N'" & BankPayCustAccountNumber & "' and Currency_Name =N'" & BankPayCustNameCurrency & "'"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            aray_BNKID.Add(dr("BNKID").ToString())
        Loop


        Dim SM, SM1 As Double
        For i As Integer = 0 To aray_BNKID.Count - 1

            Dim BNKID As Integer = aray_BNKID(i)

            Dim TransferTO, Transfer, Creditor, Debtor As Double
            Dim TypeTransfer As Integer

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Debtor from BANKS where BNKID =N'" & BNKID & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Debtor = 0 Else Debtor = dr(0)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Creditor from BANKS where BNKID =N'" & BNKID & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Creditor = 0 Else Creditor = dr(0)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Transfer from BANKS where BNKID =N'" & BNKID & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then Transfer = 0 Else Transfer = dr(0)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select TypeTransfer from BANKS where BNKID =N'" & BNKID & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr(0) Is DBNull.Value Then TypeTransfer = 0 Else TypeTransfer = dr(0)


            If TypeTransfer = 0 Then
                SM1 = Creditor - Debtor - Transfer
                SM += SM1
            Else
                If Creditor > Transfer Then
                    SM1 = Creditor - Debtor - Transfer
                    SM += SM1
                Else
                    SM1 = Creditor - Debtor + Transfer
                    SM += SM1
                End If
            End If
        Next

        PreviousBalance = SM

    End Sub

    Private Sub Daily_Restrictions_Banks()
        Dim Creditor As String = ""
        Dim CreditorCode As String = ""

        Dim Account As String = ""
        Dim AccountCode As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'ايداع بنكى'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            Creditor = dr("Link_AccountsTree")
            CreditorCode = dr("ACCNumber")
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مقبوضات عملاء'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            Account = dr("Link_AccountsTree")
            AccountCode = dr("ACCNumber")
        End If

        '========================================================================================

        Dim MOVRegNumber As String = Cls.MAXRECORD("MOVES", "MOVRegNumber")
        Dim MOVID As String = Cls.MAXRECORD("MOVES", "MOVID")
        Dim bill_no As String = Cls.MAXRECORD("Vst", "id") - 1

        If RadioButton3.Checked = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
            S = S & "N'" & bill_no & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpvndrecipient.Text) & "',N'" & Creditor & "',N'" & txtammnt.Text & "',N'" & txtammnt.Text & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            ' من حساب / ايداع بنكى
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
            S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpvndrecipient.Text) & "',N'" & CreditorCode & "',N'" & Creditor & "',N'" & txtammnt.Text & "',N'0',N'" & Creditor & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            ' الى حساب / مقبوضات عملاء
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
            S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpvndrecipient.Text) & "',N'" & AccountCode & "',N'" & Account & "',N'0',N'" & txtammnt.Text & "',N'" & Account & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If

    End Sub

    Private Sub RadioButton3_CheckedChanged(sender As Object, e As EventArgs) Handles RadioButton3.CheckedChanged
        radiobtnessal()
        If RadioButton3.Checked = True Then
            lblDate_Maturity.Visible = False
            dtpDate_Maturity.Visible = False
            lblCheck_Status.Visible = False
            cmbCheck_Status.Visible = False
            lblBankName.Visible = False
            cmbBankName.Visible = False
        End If
        If RadioButton2.Checked = True Then
            lblDate_Maturity.Visible = True
            dtpDate_Maturity.Visible = True
            lblCheck_Status.Visible = True
            cmbCheck_Status.Visible = True
            lblBankName.Visible = True
            cmbBankName.Visible = True
        End If
    End Sub

    Private Sub GetDebtorlCreditorPrevious()
        Try
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select vnamntcredit , vnamntdebit from Customers where Vendorname=N'" & Cmbvendorname.Text & "'"
            dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                AmntcreditPrevious = dr("vnamntcredit")
                AmntdebitPrevious = dr("vnamntdebit")
                AmountDebitCreditPrevious = AmntcreditPrevious - AmntdebitPrevious
            End If
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub GetNumericValueSeparators()
        ActionNumericSeparators = True

        txtammnt.Text = GetRemoveNumericSeparatorsValue(txtammnt.Text)

        ActionNumericSeparators = False
    End Sub
End Class
