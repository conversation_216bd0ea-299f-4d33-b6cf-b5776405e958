﻿Imports CrystalDecisions.CrystalReports.Engine
Public Class frmShowBanks
    Dim WithEvents BS As New BindingSource

    Private Sub frmShowBanks_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        FillComboBox()
        GetData()
    End Sub

    Private Sub FillComboBox()
        cmbAccountNumber.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select distinct AccountNumber from Banks"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            cmbAccountNumber.Items.Add(dr("AccountNumber"))
        Loop


        cmbBank.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select distinct Bank from Banks"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            cmbBank.Items.Add(dr("Bank"))
        Loop


        cmbDocumentNumber.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select distinct DocumentNumber from Banks"
        dr = cmd.ExecuteReader
        Do While dr.Read = True
            cmbDocumentNumber.Items.Add(dr("DocumentNumber"))
        Loop

        Bra.Fil("Type_Currency", "Name_Currency", cmbCurrency)

    End Sub

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT BNKID AS [مسلسل],DateMovement AS [تاريخ الحركة], TypeMovement AS [نوع الحركة], PaymentMethod AS [طريقة الدفع], AccountNumber AS [رقم الحساب],DocumentNumber as [رقم المستند],Debtor as [صرف],Creditor as [ايداع],CurrentBalance as [الرصيد الحالى],DateCollection as [تاريخ التحصيل],Bank as [البنك],Currency_Name as [العملة],Notes as [ملاحظات] FROM Banks where BNKID <> N''"

        If cmbAccountNumber.Text <> Nothing Then
            S = S & "and AccountNumber =N'" & cmbAccountNumber.Text.Trim & "'"
        End If
        If cmbBank.Text <> Nothing Then
            S = S & "and Bank =N'" & cmbBank.Text.Trim & "'"
        End If
        If cmbDocumentNumber.Text <> Nothing Then
            S = S & "and DocumentNumber =N'" & cmbDocumentNumber.Text.Trim & "'"
        End If
        If cmbCurrency.Text <> Nothing Then
            S = S & "and Currency_Name =N'" & cmbCurrency.Text.Trim & "'"
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & "and DateMovement >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and DateMovement <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

        'DataGridView1.Columns(0).Width = 40
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim NumberID As String
            NumberID = DataGridView1.SelectedRows(0).Cells(0).Value


            cmd.CommandText = "delete from Banks where BNKID =N'" & NumberID & "'" : cmd.ExecuteNonQuery()
        Next
        GetData()
    End Sub

    Private Sub btnEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnEdit.Click
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        EditItmId = DataGridView1.SelectedRows(0).Cells(0).Value
        Action = "Edit"
        frmBanks.Show()
    End Sub

    Private Sub btnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnShow.Click
        GetData()
    End Sub

    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click
        Dim txtFROMDate As String
        Dim txtToDate As String
        Dim f As New Frm_PrintReports
        Dim rpt As New rptBanks
        Dim txt As TextObject
        txtFROMDate = Format(Me.DateTimePicker1.Value, "yyy, MM, dd, 00, 00, 000")
        txtToDate = Format(Me.DateTimePicker2.Value, "yyy, MM, dd, 00, 00, 00")
        'On Error Resume Next
        GETSERVERNAMEANDDATABASENAME(rpt, DataBaseName, PasswordServer, UserNameServer)
        If ChkWithoutDate.Checked = False Then
            If cmbAccountNumber.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{Banks.DateMovement} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")AND{Banks.AccountNumber} =N'" & cmbAccountNumber.Text & "'"
            End If
            If cmbBank.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{Banks.DateMovement} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")AND{Banks.Bank} =N'" & cmbBank.Text & "'"
            End If
            If cmbDocumentNumber.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{Banks.DateMovement} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")AND{Banks.DocumentNumber} =N'" & cmbDocumentNumber.Text & "'"
            End If
            If cmbCurrency.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{Banks.DateMovement} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")AND{Banks.Currency_Name} =N'" & cmbCurrency.Text & "'"
            End If
            txt = rpt.Section1.ReportObjects("Text9")
            txt.Text = "خلال الفترة من" & Format(Me.DateTimePicker1.Value, "dd - MM - yyyy") & " الى " & Format(Me.DateTimePicker2.Value, "dd - MM - yyyy")
        Else
            If cmbAccountNumber.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{Banks.AccountNumber} =N'" & cmbAccountNumber.Text & "'"
            End If
            If cmbBank.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{Banks.Bank} =N'" & cmbBank.Text & "'"
            End If
            If cmbDocumentNumber.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{Banks.DocumentNumber} =N'" & cmbDocumentNumber.Text & "'"
            End If
            If cmbCurrency.Text <> Nothing Then
                rpt.RecordSelectionFormula = "{Banks.Currency_Name} =N'" & cmbCurrency.Text & "'"
            End If
        End If
        AddReportView()
        Dim txtname, txtNameAr, txtNameEn As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير بحركة البنوك"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        f.CrystalReportViewer1.ReportSource = rpt
        f.CrystalReportViewer1.Zoom(95%)
        f.CrystalReportViewer1.RefreshReport()
        f.Text = "تقرير بحركة البنوك"
        f.Show()
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub
End Class