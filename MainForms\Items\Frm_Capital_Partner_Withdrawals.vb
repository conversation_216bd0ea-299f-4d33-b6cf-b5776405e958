﻿Public Class Frm_Capital_Partner_Withdrawals

    Dim ActivEdit As Boolean = False
    Dim Capital_Number As String = ""

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If ValidateTextAdd() = False Then Exit Sub

        Custmersfinance()

        '===============================================================================
        Dim UseAccounts As String = mykey.GetValue("UseAccounts", "NO")
        If UseAccounts = "YES" Then
            Daily_Restrictions()
        End If
        '===============================================================================

        Get_Movement_In_Out_Money(dtpCapital_Date.Text, Treasury_Code)

        cmbCapital_Owner.SelectedIndex = -1
        cmbTreasury.SelectedIndex = -1
        txtCapital_Number.Text = ""
        txtAmount.Text = ""
        cmbCapital_Owner.Text = ""
        MAXRECORD()
        MsgBox("تم إجراء العملية بنجاح", MsgBoxStyle.Information)
        cmbCapital_Owner.Focus()
        cmbCapital_Owner.SelectAll()

        If ActivEdit = True Then
            PanelAddNew.Dock = DockStyle.None
            PanelAddNew.Top = 5000
        End If
        ActivEdit = False
    End Sub
    Function ValidateTextAdd() As Boolean

        If Trim(cmbCapital_Owner.Text) = "" Then
            MsgBox("فضلاً أختر أسم المالك", MsgBoxStyle.Exclamation)
            cmbCapital_Owner.Focus()
            Exit Function
        End If

        If Val(txtAmount.Text) = 0 Then
            MsgBox("فضلاً أدخل المبلغ", MsgBoxStyle.Exclamation)
            txtAmount.Focus()
            Return False
        End If

        If Trim(txtCapital_Number.Text) = "" Then
            MsgBox("فضلاً أدخل رقم الإيصال", MsgBoxStyle.Exclamation)
            txtCapital_Number.Focus()
            Return False
        End If

        If Trim(cmbTreasury.Text) = "" Then
            MsgBox("فضلاً أختر الخزينة", MsgBoxStyle.Exclamation)
            cmbTreasury.Focus()
            Return False
        End If


        If ActivEdit = False Then
            Cls.Select_More_Data_Branch("Capital_Partner_Withdrawals", "*", "Capital_Number=N'" & txtCapital_Number.Text & "'")
            If dr.HasRows = True Then
                MsgBox("عفواً يوجد رقم ايصال مسجل مسبقا بنفس الرقم", MsgBoxStyle.Exclamation)
                Return False : Exit Function
            Else
            End If
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * From Capital_Owner where Owner_Name =N'" & cmbCapital_Owner.Text & "'"
        dr = cmd.ExecuteReader()
        If dr.HasRows = False Then
            MsgBox("عفواً  لا يوجد أسم المالك مسجل مسبقا بنفس الرقم", MsgBoxStyle.Exclamation)
            Return False : Exit Function
        Else
        End If

        Return True
    End Function

    Sub Custmersfinance()
        Dim Owner_Code As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Owner_Code from Capital_Owner where Owner_Name=N'" & cmbCapital_Owner.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Owner_Code = dr("Owner_Code")
        End If

        Dim Treasury_Code As String = Cls.Get_Code_Value_Branch("Treasury", "Treasury_Code", "Treasury_Name", cmbTreasury.Text)


        Dim Capital_Type_Code As Integer
        If rdoCheckOut.Checked = True Then
            Capital_Type_Code = 1
        End If
        If rdoDeposit.Checked = True Then
            Capital_Type_Code = 2
        End If

        If ActivEdit = False Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Capital_Partner_Withdrawals (Company_Branch_ID,Capital_Number,Owner_Code,Treasury_Code,Capital_Type_Code,Amount,Capital_Time,Capital_Date,Notes,UserName) values"
            S = S & " (N'" & Company_Branch_ID & "',N'" & txtCapital_Number.Text & "'," & Owner_Code & "," & Treasury_Code & "," & Capital_Type_Code & "," & txtAmount.Text.Trim & ",N'" & Cls.get_time(True) & "',N'" & Cls.C_date(dtpCapital_Date.Text) & "',N'" & txtNotes.Text.Trim & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        End If

        If ActivEdit = True Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Capital_Partner_Withdrawals set Company_Branch_ID =N'" & Company_Branch_ID & "',Owner_Code =N'" & Owner_Code & "',Treasury_Code =N'" & Treasury_Code & "',Capital_Type_Code =N'" & Capital_Type_Code & "',Amount =N'" & txtAmount.Text & "',Capital_Date =N'" & Cls.C_date(dtpCapital_Date.Text) & "',Capital_Time =N'" & Cls.get_time(True) & "' where Capital_Number =N'" & Capital_Number & "'" : cmd.ExecuteNonQuery()
        End If

    End Sub


    Private Sub MAXRECORD()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Capital_Partner_Withdrawals"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Me.txtCapital_Number.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(Capital_Number As float)) as mb FROM Capital_Partner_Withdrawals"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            Me.txtCapital_Number.Text = sh + 1
        End If

    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        GetData()
    End Sub

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select  dbo.Capital_Partner_Withdrawals.Capital_Number As [رقم], dbo.Capital_Owner.Owner_Name As [أسم المالك], dbo.Treasury.Treasury_Name As الخزينة, dbo.Capital_Type.Capital_Type_Name As [نوع الحركة],   dbo.Capital_Partner_Withdrawals.Amount AS المبلغ, dbo.Capital_Partner_Withdrawals.Capital_Date As [التاريخ], dbo.Capital_Partner_Withdrawals.Notes As ملاحظات, dbo.Capital_Partner_Withdrawals.UserName, dbo.Capital_Partner_Withdrawals.Company_Branch_ID From dbo.Capital_Partner_Withdrawals INNER Join  dbo.Capital_Owner ON dbo.Capital_Partner_Withdrawals.Owner_Code = dbo.Capital_Owner.Owner_Code INNER Join  dbo.Capital_Type ON dbo.Capital_Partner_Withdrawals.Capital_Type_Code = dbo.Capital_Type.Capital_Type_Code INNER Join   dbo.Treasury ON dbo.Capital_Partner_Withdrawals.Treasury_Code = dbo.Treasury.Treasury_Code Where (dbo.Capital_Partner_Withdrawals.Capital_Number <> N'')"
        If chkAllEmp.Checked = False Then
            If cmbCapital_OwnerView.Text <> "" Then
                S = S & " and dbo.Capital_Owner.Owner_Name = N'" & cmbCapital_OwnerView.Text.Trim & "'"
            End If
            If cmbTreasury.Text <> "" Then
                S = S & " and dbo.Treasury.Treasury_Name = N'" & cmbTreasury.Text.Trim & "'"
            End If
        End If
        If chkCheckOut_Deposit.Checked = False Then
            If rdoCheckOutView.Checked = True Then
                S = S & " and dbo.Capital_Type.Capital_Type_Name  = N'" & rdoCheckOutView.Text.Trim & "'"
            End If
            If rdoDepositView.Checked = True Then
                S = S & " and dbo.Capital_Type.Capital_Type_Name  = N'" & rdoDepositView.Text.Trim & "'"
            End If
        End If
        If chkAllDate.Checked = False Then
            S = S & " and dbo.Capital_Partner_Withdrawals.Capital_Date >=N'" & Cls.C_date(Dtp_from.Text) & "' and dbo.Capital_Partner_Withdrawals.Capital_Date <=N'" & Cls.C_date(Dtp_To.Text) & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " and dbo.Capital_Partner_Withdrawals.Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [التاريخ]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [أسم المالك]"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)
        DTGV.Columns(7).Visible = False
        DTGV.Columns(7).Visible = False
        Dim SMCheckOut, SMDeposit As Double
        Dim SMType As String
        For i As Integer = 0 To DTGV.RowCount - 1
            SMType = DTGV.Rows(i).Cells(3).Value
            If SMType = "سحب" Then
                SMCheckOut = SMCheckOut + DTGV.Rows(i).Cells(4).Value
            End If
            If SMType = "ايداع" Then
                SMDeposit = SMDeposit + DTGV.Rows(i).Cells(4).Value
            End If
        Next

        txtTotalCheckOut.Text = SMCheckOut
        txtTotalDeposit.Text = SMDeposit

        txtDebit.Text = 0
        txtCreditor.Text = 0

        If SMDeposit = SMCheckOut Then
            txtDebit.Text = 0
            txtCreditor.Text = 0
        Else
            If SMDeposit < SMCheckOut Then
                txtDebit.Text = Val(SMCheckOut) - Val(SMDeposit)
            Else
                txtCreditor.Text = Val(SMDeposit) - Val(SMCheckOut)
            End If
        End If


        'TotalCheckOut = Val(SMCheckOut) - Val(SMDeposit)

        'If TotalDeposit > TotalCheckOut Then
        '    txtDebit.Text = TotalDeposit
        'Else
        '    txtDebit.Text = TotalCheckOut
        'End If
    End Sub


    Private Sub Frm_Deficit_Increase_Delegate_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Bra.Fil("Capital_Owner", "Owner_Name", cmbCapital_Owner)
        Bra.Fil("Capital_Owner", "Owner_Name", cmbCapital_OwnerView)
        Cls.fill_combo_Branch("Treasury", "Treasury_Name", cmbTreasury)
        Cls.fill_combo_Branch("Treasury", "Treasury_Name", cmbTreasuryView)
        cmbTreasury.SelectedIndex = 0
        MAXRECORD()
        PanelAddNew.Top = 5000
        GetDateNotBeenActivatedPrograms(dtpCapital_Date)
    End Sub

    Private Sub cmbEmployees_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbCapital_Owner.KeyUp
        If e.KeyCode = 13 Then
            txtAmount.Focus()
        End If
    End Sub

    Private Sub txtAmount_KeyUp(sender As Object, e As KeyEventArgs) Handles txtAmount.KeyUp
        If e.KeyCode = 13 Then
            txtNotes.Focus()
        End If
    End Sub

    Private Sub txtNotes_KeyUp(sender As Object, e As KeyEventArgs) Handles txtNotes.KeyUp
        If e.KeyCode = 13 Then
            Button1.PerformClick()
        End If
    End Sub

    Private Sub btnAdd_Click(sender As Object, e As EventArgs) Handles btnAdd.Click
        PanelAddNew.Top = 20
        PanelAddNew.Dock = DockStyle.Fill
        MAXRECORD()
        cmbCapital_Owner.Focus()
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        PanelAddNew.Dock = DockStyle.None
        PanelAddNew.Top = 5000
        ActivEdit = False
    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DTGV.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        Dim Capital_Number, XDate As String
        Dim Capital_Type As String

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DTGV.SelectedRows.Count - 1
            If DTGV.RowCount = 0 Then Beep() : Exit Sub
            If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub

            Capital_Number = DTGV.SelectedRows(i).Cells(0).Value
            Capital_Type = DTGV.SelectedRows(i).Cells(3).Value

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete from Capital_Partner_Withdrawals where Capital_Number =N'" & Capital_Number & "'" : cmd.ExecuteNonQuery()

            If Capital_Type = "سحب" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  MOVES where bill_no =N'" & Capital_Number & "' and MOVStatement =N'مسحوبات جارى الشركاء'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & Capital_Number & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & Capital_Number & "' and MOVDNameAccount =N'مسحوبات جارى الشركاء'" : cmd.ExecuteNonQuery()
            End If

            If Capital_Type = "ايداع" Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  MOVES where bill_no =N'" & Capital_Number & "' and MOVStatement =N'ايداعات جارى الشركاء'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & Capital_Number & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & Capital_Number & "' and MOVDNameAccount =N'ايداعات جارى الشركاء'" : cmd.ExecuteNonQuery()
            End If


            XDate = DTGV.SelectedRows(i).Cells(5).Value
            Get_Movement_In_Out_Money(XDate, Treasury_Code)
        Next

        GetData()

    End Sub

    Private Sub chkAllEmp_CheckedChanged(sender As Object, e As EventArgs) Handles chkAllEmp.CheckedChanged
        If chkAllEmp.Checked = True Then
            cmbCapital_OwnerView.Enabled = False
            cmbTreasuryView.Enabled = False
        Else
            cmbCapital_OwnerView.Enabled = True
            cmbTreasuryView.Enabled = True
        End If
    End Sub

    Private Sub chkAllDate_CheckedChanged(sender As Object, e As EventArgs) Handles chkAllDate.CheckedChanged
        If chkAllDate.Checked = True Then
            Dtp_from.Enabled = False
            Dtp_To.Enabled = False
        Else
            Dtp_from.Enabled = True
            Dtp_To.Enabled = True
        End If
    End Sub

    Private Sub btnAddCapital_Owner_Click(sender As Object, e As EventArgs) Handles btnAddCapital_Owner.Click
        Frm_Capital_Owner2.ShowDialog()
    End Sub

    Private Sub chkCheckOut_Deposit_CheckedChanged(sender As Object, e As EventArgs) Handles chkCheckOut_Deposit.CheckedChanged
        If chkCheckOut_Deposit.Checked = True Then
            rdoCheckOutView.Enabled = False
            rdoDepositView.Enabled = False
        Else
            rdoCheckOutView.Enabled = True
            rdoDepositView.Enabled = True
        End If
    End Sub


    Private Sub cmbCapital_OwnerView_DropDown(sender As Object, e As EventArgs) Handles cmbCapital_OwnerView.DropDown
        Bra.Fil("Capital_Owner", "Owner_Name", cmbCapital_OwnerView)
    End Sub

    Private Sub Daily_Restrictions()
        Dim Account As String = ""
        Dim AccountCode As String = ""
        Dim Account2 As String = ""
        Dim AccountCode2 As String = ""
        Dim AccountTreasury As String = ""
        Dim AccountCodeTreasury As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'مسحوبات جارى الشركاء'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            Account = dr("Link_AccountsTree")
            AccountCode = dr("ACCNumber")
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select Link_AccountsTree,ACCNumber from AccountsTreeLinking where Link_Statement=N'ايداعات جارى الشركاء'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            Account2 = dr("Link_AccountsTree")
            AccountCode2 = dr("ACCNumber")
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select ACCName,ACCNumber from AccountsTree where ACCName=N'الخزينة'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            AccountTreasury = dr("ACCName")
            AccountCodeTreasury = dr("ACCNumber")
        End If
        '========================================================================================


        Dim MOVID As String = Cls.MAXRECORD("MOVES", "MOVID")
        Dim MOVRegNumber As String = Cls.MAXRECORD("MOVES", "MOVRegNumber")
        Dim bill_no As String = Cls.MAXRECORD("Capital_Partner_Withdrawals", "Capital_Number") - 1

        If ActivEdit = False Then


            ' مسحوبات جارى الشركاء
            If rdoCheckOut.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
                S = S & "N'" & bill_no & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpCapital_Date.Text) & "',N'" & Account & "',N'" & txtAmount.Text & "',N'" & txtAmount.Text & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' من حساب / مسحوبات جارى الشركاء
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpCapital_Date.Text) & "',N'" & AccountCode & "',N'" & Account & "',N'" & txtAmount.Text & "',N'0',N'" & Account & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' الى حساب / الخزينة
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpCapital_Date.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'0',N'" & txtAmount.Text & "',N'" & AccountTreasury & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

            End If

            ' ايداعات جارى الشركاء
            If rdoDeposit.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
                S = S & "N'" & bill_no & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpCapital_Date.Text) & "',N'" & Account2 & "',N'" & txtAmount.Text & "',N'" & txtAmount.Text & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' من حساب / الخزينة
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpCapital_Date.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'" & txtAmount.Text & "',N'0',N'" & AccountTreasury & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' الى حساب / ايداعات جارى الشركاء
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpCapital_Date.Text) & "',N'" & AccountCode2 & "',N'" & Account2 & "',N'0',N'" & txtAmount.Text & "',N'" & Account2 & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        End If

        If ActivEdit = True Then


            ' مسحوبات جارى الشركاء
            If rdoCheckOut.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  MOVES where bill_no =N'" & bill_no & "' and MOVStatement =N'مسحوبات جارى الشركاء'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & bill_no & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & bill_no & "' and MOVDNameAccount =N'مسحوبات جارى الشركاء'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
                S = S & "N'" & bill_no & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpCapital_Date.Text) & "',N'" & Account & "',N'" & txtAmount.Text & "',N'" & txtAmount.Text & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' من حساب / مسحوبات جارى الشركاء
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpCapital_Date.Text) & "',N'" & AccountCode & "',N'" & Account & "',N'" & txtAmount.Text & "',N'0',N'" & Account & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' الى حساب / الخزينة
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpCapital_Date.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'0',N'" & txtAmount.Text & "',N'" & AccountTreasury & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

            End If

            ' ايداعات جارى الشركاء
            If rdoDeposit.Checked = True Then
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                cmd.CommandText = "delete From  MOVES where bill_no =N'" & bill_no & "' and MOVStatement =N'ايداعات جارى الشركاء'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & bill_no & "' and MOVDNameAccount =N'الخزينة'" : cmd.ExecuteNonQuery()
                cmd.CommandText = "delete From  MOVESDATA where bill_no =N'" & bill_no & "' and MOVDNameAccount =N'ايداعات جارى الشركاء'" : cmd.ExecuteNonQuery()

                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVES(bill_no,MOVID,MOVRegNumber,MOVDate,MOVStatement,MOVDebtor,MOVCreditor,UserName) values ("
                S = S & "N'" & bill_no & "',N'" & MOVID & "' ,N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpCapital_Date.Text) & "',N'" & Account2 & "',N'" & txtAmount.Text & "',N'" & txtAmount.Text & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' من حساب / الخزينة
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpCapital_Date.Text) & "',N'" & AccountCodeTreasury & "',N'" & AccountTreasury & "',N'" & txtAmount.Text & "',N'0',N'" & AccountTreasury & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()

                ' الى حساب / ايداعات جارى الشركاء
                If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
                S = "insert into MOVESDATA (bill_no,MOVRegNumber,MOVDDate,MOVNumberAccount,MOVDNameAccount,MOVDDebtor,MOVDCreditor,MOVDNAMStatement,UserName)  values ("
                S = S & "N'" & bill_no & "',N'" & MOVRegNumber & "',N'" & Cls.C_date(dtpCapital_Date.Text) & "',N'" & AccountCode2 & "',N'" & Account2 & "',N'0',N'" & txtAmount.Text & "',N'" & Account2 & "',N'" & UserName & "')"
                cmd.CommandText = S : cmd.ExecuteNonQuery()
            End If
        End If

    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs) Handles btnEdit.Click
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        Dim bill_no As String = DTGV.SelectedRows(0).Cells(0).Value
        Capital_Number = bill_no
        txtCapital_Number.Text = bill_no

        Dim Owner_Code As String = "" : Dim Treasury_Code As String = "" : Dim Capital_Type_Code As String = "" : Dim Xbill_date As String = ""
        Cls.Select_More_Data_Stores("Capital_Partner_Withdrawals", "Owner_Code,Treasury_Code,Capital_Type_Code,Amount,Capital_Date,Notes", "Capital_Number=N'" & bill_no & "'")
        If dr.HasRows = True Then
            Owner_Code = dr(0).ToString
            Treasury_Code = dr(1).ToString
            Capital_Type_Code = dr(2).ToString
            txtAmount.Text = dr(3).ToString
            Xbill_date = dr(4).ToString
            txtNotes.Text = dr(5).ToString
            dtpCapital_Date.Text = Cls.R_date(Xbill_date)
        End If

        cmbCapital_Owner.Text = Cls.Get_Code_Value_Branch_More("Capital_Owner", "Owner_Name", "Owner_Code=N'" & Owner_Code & "'")
        cmbTreasury.Text = Cls.Get_Code_Value_Branch_More("Treasury", "Treasury_Name", "Treasury_Code=N'" & Treasury_Code & "'")

        If Capital_Type_Code = "1" Then
            rdoCheckOut.Checked = True
        End If
        If Capital_Type_Code = "2" Then
            rdoDeposit.Checked = True
        End If
        ActivEdit = True

        PanelAddNew.Top = 20
        PanelAddNew.Dock = DockStyle.Fill
    End Sub

    Private Sub txtCapital_Number_TextChanged(sender As Object, e As EventArgs) Handles txtCapital_Number.TextChanged
        MyVars.CheckNumber(txtCapital_Number)
    End Sub

    Private Sub txtAmount_TextChanged(sender As Object, e As EventArgs) Handles txtAmount.TextChanged
        MyVars.CheckNumber(txtAmount)
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class
