﻿Imports CrystalDecisions.CrystalReports.Engine

Public Class Frm_Product_Dismissal_Notice_Factory_Show
    Dim Dt_AddBill As New DataTable
    Dim RNXD As Integer
    Dim Manufacturing_ID As String
    Dim ActionRead As Boolean = False
    Dim WithEvents BS As New BindingSource
    Dim ListBoxSelectedIndex As Integer
    Dim QuntAll As Integer
    Dim AlaertParcode As Boolean
    Dim itm_id As String = ""
    Dim itm_cat As String = ""
    Dim itm_name As String = ""
    Dim Unity As String = ""
    Dim qunt As String = ""
    Dim qunt_unity As String = ""
    Dim quntTotal As String = ""
    Dim StoresFrom As String = ""
    Dim StoresTo As String = ""
    Dim QuntFrom As Double
    Dim TinPrice As String = ""
    Dim StausMainStores As Integer = 0
    Dim TotalPriceBeforeAverage As Double


#Region "View Items"
    Private Sub Frm_Offers_Items_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Bra.Fil("groups", "g_name", cmbCatsManufacturingView)
        Cls.fill_combo_Branch("stores", "store", cmbStoreView)
        Cls.fill_combo("vendors", "Vendorname", cmbItemsManufacturing)
        Cls.fill_combo_Branch("stores", "store", cmbStoresFrom)
        Cls.fill_combo_Branch("stores", "store", cmbStoresTo)
        PanelEdit.Top = 5000
    End Sub

    Private Sub cmbStoreManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbStoreView.SelectedIndexChanged
        If cmbStoreView.Text.Trim = "" Then Exit Sub
        cmbCatsManufacturingView.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select distinct group_name from Items where Stores =N'" & cmbStoreView.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbCatsManufacturingView.Items.Add(Trim(dr(0)))
        Loop
        cmbCatsManufacturingView.Text = ""
        cmbCatsManufacturingView.Focus()
    End Sub

    Private Sub cmbStoreManufacturing_DropDown(sender As Object, e As EventArgs) Handles cmbStoreView.DropDown
        cmbCatsManufacturingView.Text = ""
        cmbItemsManufacturingView.Text = ""
    End Sub

    Private Sub cmbCatsManufacturing_DropDown(sender As Object, e As EventArgs) Handles cmbCatsManufacturingView.DropDown
        cmbItemsManufacturingView.Text = ""
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        GetData()
        GetDetails()
    End Sub

    Private Sub GetData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()

        S = "SELECT id as [#],CAST(BILL_NO AS float) AS [رقم إذن الصرف],bill_date AS [التاريخ], sname AS [أسم الصنف], Stores_From AS [من المصنع], Stores_TO AS [الى مخزن المنتج التام],qunt_Manufacturing as [إجمالى الكميات],TotalCostPrice as [إجمالى التكلفة] FROM View_ManufacturingDismissalNotice where id <> N''"
        If chkAll.Checked = False Then
            If cmbStoreView.Text <> "" Then
                S = S & " and  Stores_TO =N'" & cmbStoreView.Text & "'"
            End If
            If cmbCatsManufacturingView.Text <> "" Then
                S = S & " and  group_name =N'" & cmbCatsManufacturingView.Text & "'"
            End If
            If cmbItemsManufacturingView.Text <> "" Then
                S = S & " and  sname =N'" & cmbItemsManufacturingView.Text & "'"
            End If
            If txtbillnoSearch.Text <> "" Then
                S = S & " and  bill_no =N'" & txtbillnoSearch.Text & "'"
            End If
        End If
        If ChkWithoutDate.Checked = False Then
            S = S & " and  bill_date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and bill_date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم إذن الصرف]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [التاريخ]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [أسم الصنف]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        dgv_Manufacturing.DataSource = Cls.PopulateDataView(dr)

        Dim SM2 As String
        For i As Integer = 0 To dgv_Manufacturing.RowCount - 1
            SM2 = Val(dgv_Manufacturing.Rows(i).Cells(2).Value)
            SM2 = Cls.R_date(SM2)
            dgv_Manufacturing.Rows(i).Cells(2).Value = SM2
        Next

        Dim SM As Double
        For i As Integer = 0 To dgv_Manufacturing.Rows.Count - 1
            SM = SM + Val(dgv_Manufacturing.Rows(i).Cells(6).Value.ToString)
        Next
        txtTotalWeight.Text = SM

        Dim SM1 As Double
        For i As Integer = 0 To dgv_Manufacturing.Rows.Count - 1
            SM1 = SM1 + Val(dgv_Manufacturing.Rows(i).Cells(7).Value.ToString)
        Next
        txtCostPrice.Text = SM

        dgv_Manufacturing.Columns(0).Visible = False
        dgv_Manufacturing.Columns(3).Width = 250
    End Sub

    Private Sub dgv_Manufacturing_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgv_Manufacturing.CellClick
        GetDetails()
    End Sub

    Private Sub GetDetails()
        If dgv_Manufacturing.RowCount = 0 Then Beep() : Exit Sub
        If (dgv_Manufacturing.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim bill_no As String
        bill_no = dgv_Manufacturing.SelectedRows(0).Cells(1).Value
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "select itm_id as [الباركود] ,sname as [اسم الصنف],TinPrice as [سعر التكلفة] ,qu as [الكمية],totalprice as [إجمالى التكلفة],Stores as [المخزن] from View_ManufacturingDismissalNoticeData where bill_noNotice =N'" & bill_no & "'"
        If FilterSelect = "Number" Then
            S = S & " order by [الباركود]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [اسم الصنف]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [اسم الصنف]"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        dgv_Material.DataSource = Cls.PopulateDataView(dr)
        dgv_Material.Columns(1).Width = 400

        Dim SM2 As String
        For i As Integer = 0 To dgv_Material.RowCount - 1
            SM2 = Val(dgv_Material.Rows(i).Cells(2).Value.ToString) * Val(dgv_Material.Rows(i).Cells(3).Value.ToString)
            dgv_Material.Rows(i).Cells(4).Value = SM2
        Next

        Dim SM As Double
        For i As Integer = 0 To dgv_Manufacturing.Rows.Count - 1
            SM = SM + Val(dgv_Manufacturing.Rows(i).Cells(7).Value.ToString)
        Next
        txtTotalCostPrice.Text = SM

        Dim SM1 As Double
        For i As Integer = 0 To dgv_Manufacturing.Rows.Count - 1
            SM1 = SM1 + Val(dgv_Manufacturing.Rows(i).Cells(6).Value.ToString)
        Next
        txtTotalWeight.Text = SM1

    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbCatsManufacturingView.Enabled = False
            cmbItemsManufacturingView.Enabled = False
            cmbStoreView.Enabled = False
            txtbillnoSearch.Enabled = False
        Else
            cmbCatsManufacturingView.Enabled = True
            cmbItemsManufacturingView.Enabled = True
            cmbStoreView.Enabled = True
            txtbillnoSearch.Enabled = True
        End If
    End Sub
#End Region

#Region "Delete Items"
    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If dgv_Manufacturing.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub
        If dgv_Manufacturing.RowCount = 0 Then Beep() : Exit Sub
        If (dgv_Manufacturing.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim bill_no As String

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To dgv_Manufacturing.SelectedRows.Count - 1
            bill_no = dgv_Manufacturing.SelectedRows(i).Cells(1).Value

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete From  ManufacturingDismissalNotice where bill_no =N'" & bill_no & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  Manufacturing_BillsalData where bill_noNotice =N'" & bill_no & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete From  Manufacturing_BilltINData where bill_noNotice =N'" & bill_no & "'" : cmd.ExecuteNonQuery()
        Next

        IM.UpdateDataBase()

        GetData()
        GetDetails()
    End Sub

#End Region

#Region "Print"
    Private Sub btnPrint_Click(sender As Object, e As EventArgs) Handles btnPrint.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If dgv_Manufacturing.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If dgv_Manufacturing.RowCount = 0 Then Beep() : Exit Sub
        If (dgv_Manufacturing.SelectedRows.Count) = 0 Then Beep() : Exit Sub

        Dim bill_no As String = dgv_Manufacturing.SelectedRows(0).Cells(1).Value
        Dim bill_date As String = dgv_Manufacturing.SelectedRows(0).Cells(2).Value
        Dim sname As String = dgv_Manufacturing.SelectedRows(0).Cells(3).Value
        Dim Stores_From As String = dgv_Manufacturing.SelectedRows(0).Cells(4).Value
        Dim Stores_TO As String = dgv_Manufacturing.SelectedRows(0).Cells(5).Value
        Dim Total_qu As String = dgv_Manufacturing.SelectedRows(0).Cells(6).Value
        Dim TotalCostPrice As String = dgv_Manufacturing.SelectedRows(0).Cells(7).Value


        Dim CostPrice As String = ""
        Dim Manufacturing_Allowance As String = ""
        Dim Filling_Allowance As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from View_Product_Manufacturing where  SnameManufacturing = N'" & sname & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            CostPrice = dr("TinPrice")
            Manufacturing_Allowance = dr("Manufacturing_Allowance")
            Filling_Allowance = dr("Filling_Allowance")
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        Dim dgvbill_no As String = ""
        Dim dgvbill_date As String = ""
        For i As Integer = 0 To dgv_Manufacturing.SelectedRows.Count - 1
            dgvbill_no = dgv_Manufacturing.SelectedRows(i).Cells(1).Value
            dgvbill_date = dgv_Manufacturing.SelectedRows(i).Cells(2).Value
        Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To dgv_Material.Rows.Count - 1
            TotalCostPrice = Val(dgv_Material.Rows(i).Cells(2).Value) * Val(dgv_Material.Rows(i).Cells(3).Value)

            S = "insert into PrintSalesPurchases(Company_Branch_ID,bill_no,bill_date,itm_id,itm_name,price,qu,totalpriceafterdisc,store,totalprice) values"
            S = S & " (N'" & Company_Branch_ID & "',N'" & dgvbill_no & "',N'" & dgvbill_date & "',N'" & dgv_Material.Rows(i).Cells(0).Value & "',N'" & dgv_Material.Rows(i).Cells(1).Value & "',N'" & dgv_Material.Rows(i).Cells(2).Value & "',N'" & dgv_Material.Rows(i).Cells(3).Value & "',N'" & TotalCostPrice & "',N'" & dgv_Material.Rows(i).Cells(5).Value & "',N'" & txtTotal_qu.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next

        AddReportView()
        Dim rpt As New Rpt_Product_Manufacturing

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        AddReportView()
        Dim txtname, txtNameAr, txtNameEn, sname_Object, CostPrice_Object, Weightqunt_Object, Filling_Allowance_Object, TotalCostPrice_Object, Manufacturing_Allowance_Object, txtbill_no_Object, txtbill_no_ObjectText As TextObject

        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "إذن صرف من المصنع"
        txtNameAr = rpt.Section1.ReportObjects("txtTitelNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelNameEn")
        txtNameEn.Text = NameEnCompany
        txtbill_no_Object = rpt.Section1.ReportObjects("txtbill_no")
        txtbill_no_Object.Text = bill_no
        txtbill_no_ObjectText = rpt.Section1.ReportObjects("txtbill_noText")
        txtbill_no_ObjectText.Text = "إذن صرف"
        sname_Object = rpt.Section1.ReportObjects("txtbill_date")
        sname_Object.Text = bill_date
        CostPrice_Object = rpt.Section1.ReportObjects("txtsname")
        CostPrice_Object.Text = sname
        Weightqunt_Object = rpt.Section1.ReportObjects("txtStores")
        Weightqunt_Object.Text = Stores_From
        TotalCostPrice_Object = rpt.Section1.ReportObjects("txtTotal_qu")
        TotalCostPrice_Object.Text = Total_qu
        Manufacturing_Allowance_Object = rpt.Section1.ReportObjects("txtTotalCostPrice")
        Manufacturing_Allowance_Object.Text = TotalCostPrice
        CostPrice_Object = rpt.Section1.ReportObjects("txtCostPrice")
        CostPrice_Object.Text = CostPrice
        Manufacturing_Allowance_Object = rpt.Section1.ReportObjects("txtManufacturing_Allowance")
        Manufacturing_Allowance_Object.Text = Manufacturing_Allowance
        Filling_Allowance_Object = rpt.Section1.ReportObjects("txtFilling_Allowance")
        Filling_Allowance_Object.Text = Filling_Allowance
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "إذن صرف من المصنع"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

#End Region

#Region "Edit Items"

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        PanelEdit.Dock = DockStyle.None
        PanelEdit.Top = 1000
    End Sub


    Private Sub txtbill_no_KeyUp(sender As Object, e As KeyEventArgs) Handles txtbill_no.KeyUp
        If e.KeyCode = 13 Then
            dtpDateItem.Focus()
        End If
    End Sub

    Private Sub dtpDateItem_KeyUp(sender As Object, e As KeyEventArgs) Handles dtpDateItem.KeyUp
        If e.KeyCode = 13 Then
            cmbStoresFrom.Focus()
        End If
    End Sub

    Private Sub cmbEmployees_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbEmployees.KeyUp
        If e.KeyCode = 13 Then
            cmbStoresFrom.Focus()
            cmbStoresFrom.SelectAll()
        End If
    End Sub

    Private Sub cmbStoresFrom_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbStoresFrom.KeyUp
        If e.KeyCode = 13 Then
            cmbStoresTo.Focus()
        End If
    End Sub

    Friend Function Fn_AddBill(ByVal Col_IDTM As String, ByVal Col_Prc As String, ByVal Col_Cats As String, ByVal Col_Name As String, ByVal Col_Unity As String, ByVal Col_TinPrice As String _
, ByVal Col_Quant As Double, ByVal Col_qu_unity As Double, ByVal Col_QuantRemainder As Double, ByVal Col_StoreFrom As String, ByVal Col_StoreTo As String) As DataTable
        If Dt_AddBill.Columns.Count = 0 Then
            Dt_AddBill.Columns.Add("IDTM", GetType(String))
            Dt_AddBill.Columns.Add("الباركود", GetType(String))
            Dt_AddBill.Columns.Add("أسم المجموعة", GetType(String))
            Dt_AddBill.Columns.Add("أسم الصنف", GetType(String))
            Dt_AddBill.Columns.Add("وحدة القياس", GetType(String))
            Dt_AddBill.Columns.Add("سعر الشراء", GetType(Double))
            Dt_AddBill.Columns.Add("1الكمية", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية", GetType(Double))
            Dt_AddBill.Columns.Add("الكمية الباقية", GetType(Double))
            Dt_AddBill.Columns.Add("من مخزن", GetType(String))
            Dt_AddBill.Columns.Add("الى مخزن", GetType(String))
        End If

        'DTV_Width()

        Dt_AddBill.Rows.Add(Col_IDTM, Col_Prc, Col_Cats, Col_Name, Col_Unity, Col_TinPrice, Col_Quant, Col_qu_unity, Col_QuantRemainder, Col_StoreFrom, Col_StoreTo)
        Return Dt_AddBill
    End Function

    Private Sub btnDeleteEdit_Click(sender As Object, e As EventArgs) Handles btnDeleteEdit.Click
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        RNXD = DTGV.CurrentRow.Index
        DTGV.Rows.RemoveAt(RNXD)
    End Sub

    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Clear_All()
        MAXRECORD()
    End Sub

    Private Sub SumAllPrice()
        Dim SM As Double
        For i As Integer = 0 To DTGV.Rows.Count - 1
            SM = SM + DTGV.Rows(i).Cells(7).Value
        Next
        txtTotal_qu.Text = SM

        Dim SM1 As Double
        For i As Integer = 0 To DTGV.Rows.Count - 1
            SM1 += DTGV.Rows(i).Cells(5).Value * DTGV.Rows(i).Cells(7).Value
        Next
        txtCostPrice.Text = SM1

    End Sub

    Sub Clear_All()
        cmbItemsManufacturing.Text = ""
        txtqunt.Text = ""
        cmbStoresFrom.Text = ""
        cmbStoresTo.Text = ""
        cmbItemsManufacturing.Focus()
        DTGV.DataSource = ""
        cmbItemsManufacturing.Text = ""
        cmbCatsManufacturing.Text = ""
        cmbEmployees.Text = ""
    End Sub

    Private Sub MAXRECORD()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from ManufacturingDismissalNotice"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Me.txtbill_no.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(bill_no As float)) as mb FROM ManufacturingDismissalNotice where bill_no <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            Me.txtbill_no.Text = sh + 1
        End If
    End Sub

    Private Sub PrintReport()
        Dim CostPrice As String = ""
        Dim Manufacturing_Allowance As String = ""
        Dim Filling_Allowance As String = ""
        Dim TotalCostPrice As String = ""
        Dim itm_id_Manufacturing As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from View_Product_Manufacturing where  SnameManufacturing = N'" & cmbItemsManufacturing.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            itm_id_Manufacturing = dr("itm_id_Manufacturing")
            CostPrice = dr("TinPrice")
            Manufacturing_Allowance = dr("Manufacturing_Allowance")
            Filling_Allowance = dr("Filling_Allowance")
        End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        For i As Integer = 0 To DTGV.Rows.Count - 1
            TotalCostPrice = Val(DTGV.Rows(i).Cells(5).Value) * Val(DTGV.Rows(i).Cells(6).Value)
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases(Company_Branch_ID,bill_no,bill_date,itm_id,itm_cat,itm_name,Unity,price,qu,totalpriceafterdisc,store,Stat,totalprice) values"
            S = S & " (N'" & Company_Branch_ID & "',N'" & txtbill_no.Text & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & DTGV.Rows(i).Cells(1).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(3).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(5).Value & "',N'" & DTGV.Rows(i).Cells(6).Value & "',N'" & TotalCostPrice & "',N'" & DTGV.Rows(i).Cells(9).Value & "',N'" & DTGV.Rows(i).Cells(10).Value & "',N'" & txtTotal_qu.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New Rpt_Product_Manufacturing
        Dim txtname, txtNameAr, txtNameEn, bill_date_Object, sname_Object, CostPrice_Object, Weightqunt_Object, Stores_Manufacturing_Object, TotalCostPrice_Object, Manufacturing_Allowance_Object, Filling_Allowance_Object, txtbill_no_Object, txtbill_no_ObjectText As TextObject

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "امر شغل تعبئة وتصنيع"
        txtNameAr = rpt.Section1.ReportObjects("txtTitelNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelNameEn")
        txtNameEn.Text = NameEnCompany
        txtbill_no_Object = rpt.Section1.ReportObjects("txtbill_no")
        txtbill_no_Object.Text = txtbill_no.Text
        txtbill_no_ObjectText = rpt.Section1.ReportObjects("txtbill_noText")
        txtbill_no_ObjectText.Text = "رقم أمر الشغل"
        sname_Object = rpt.Section1.ReportObjects("txtsname")
        sname_Object.Text = cmbItemsManufacturing.Text
        CostPrice_Object = rpt.Section1.ReportObjects("txtCostPrice")
        CostPrice_Object.Text = CostPrice
        Weightqunt_Object = rpt.Section1.ReportObjects("txtWeightqunt")
        Weightqunt_Object.Text = txtTotal_qu.Text
        'Stores_Manufacturing_Object = rpt.Section1.ReportObjects("txtStores")
        'Stores_Manufacturing_Object.Text = cmbStoreManufacturing.Text
        TotalCostPrice_Object = rpt.Section1.ReportObjects("txtTotalCostPrice")
        TotalCostPrice_Object.Text = txtCostPrice.Text
        Manufacturing_Allowance_Object = rpt.Section1.ReportObjects("txtManufacturing_Allowance")
        Manufacturing_Allowance_Object.Text = Manufacturing_Allowance
        Filling_Allowance_Object = rpt.Section1.ReportObjects("txtFilling_Allowance")
        Filling_Allowance_Object.Text = Filling_Allowance
        bill_date_Object = rpt.Section1.ReportObjects("txtbill_date")
        bill_date_Object.Text = dtpDateItem.Text
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "امر شغل تعبئة وتصنيع"
        Frm_PrintReports.Show()

    End Sub

    Private Sub dgv_Manufacturing_DoubleClick(sender As Object, e As EventArgs) Handles dgv_Manufacturing.DoubleClick
        If dgv_Manufacturing.RowCount = 0 Then Beep() : Exit Sub
        If (dgv_Manufacturing.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        txtbill_no.Text = dgv_Manufacturing.SelectedRows(0).Cells(1).Value

        PanelEdit.Top = 80
        PanelEdit.Dock = DockStyle.Fill

        Dt_AddBill.Rows.Clear()

        ActionRead = True
        Dim store, qu_unity As Double
        Dim CostPrice, Weight, Active As Long

        Try
2:
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select * from View_ManufacturingDismissalNotice where bill_no =N'" & txtbill_no.Text & "'"
            dr = cmd.ExecuteReader
            If dr.Read Then
                dtpDateItem.Text = Cls.R_date(dr("bill_date").ToString())
                cmbCatsManufacturing.Text = dr("group_name").ToString
                cmbStoresFrom.Text = dr("Stores_From").ToString
                cmbStoresTo.Text = dr("Stores_TO").ToString
                cmbEmployees.Text = dr("NameEmployee").ToString
                CostPrice += dr("TotalCostPrice").ToString
                txtqunt.Text = dr("qunt_Manufacturing").ToString
                cmbItemsManufacturing.Text = dr("sname").ToString
            End If
        Catch ex As Exception
            GoTo 2
        End Try


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id as [الباركود],group_name as [المجموعة],SnameManufacturing as [المنتج المصنع] ,sname as [الخامات],TinPrice as [سعر التكلفة] ,QU_Manufacturing as [الكمية المصنعة],store as [الكمية الحالية],QU_Rest as [الكمية المتبقية],CostPrice as [أجمالى التكلفة],Stores as [مخزن المصنع],Stores_Manufacturing as [مخزن المنتج التام],itm_Unity as [وحدة القياس] from View_Product_Manufacturing where SnameManufacturing =N'" & cmbItemsManufacturing.Text & "'  order by 1"
        dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)
        DTGV.Columns(0).Visible = False
        DTGV.Columns(11).Visible = False
        DTGV.Columns(2).Width = 190
        DTGV.Columns(4).Width = 190


        CostPrice = 0 : Weight = 0 = Active = 0

        For i As Integer = 0 To DTGV.RowCount - 1
            DTGV.Rows(i).Cells(9).Value = cmbStoresFrom.Text

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select store from Items where itm_id=N'" & DTGV.Rows(i).Cells(0).Value & "'  and Stores=N'" & DTGV.Rows(i).Cells(9).Value & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : store = dr("store").ToString() : End If
            DTGV.Rows(i).Cells(6).Value = Val(store)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_unity from View_Product_Manufacturing where SnameManufacturing=N'" & DTGV.Rows(i).Cells(2).Value & "' and sname=N'" & DTGV.Rows(i).Cells(3).Value & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : qu_unity = dr("qu_unity").ToString() : End If
            DTGV.Rows(i).Cells(5).Value = Val(qu_unity) * Val(txtqunt.Text)

            DTGV.Rows(i).Cells(7).Value = Val(store) - Val(DTGV.Rows(i).Cells(5).Value)

            DTGV.Rows(i).Cells(8).Value = Val(DTGV.Rows(i).Cells(4).Value) * Val(DTGV.Rows(i).Cells(5).Value)

            CostPrice += Val(DTGV.Rows(i).Cells(8).Value)
            Weight += Val(DTGV.Rows(i).Cells(4).Value)

            If DTGV.Rows(i).Cells(7).Value < 0 Then
                DTGV.Rows(i).DefaultCellStyle.BackColor = Color.Red
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.White
                Active += 1
            Else
                DTGV.Rows(i).DefaultCellStyle.BackColor = Color.White
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Black
            End If
        Next
        If Active > 0 Then
            MsgBox("عفواا الكمية لاتكفى لعملية التحويل من المصنع", MsgBoxStyle.Exclamation)
        End If

        SumAllPrice()

        Dim TotalManufacturing As Double = Val(txtManufacturing_Allowance.Text) + Val(txtFilling_Allowance.Text)
        txtFillingManufacturing.Text = Val(TotalManufacturing) * Val(txtqunt.Text)
        txtCostPrice.Text = Val(CostPrice.ToString()) + Val(txtFillingManufacturing.Text)
        txtTotal_qu.Text = Weight.ToString()

        DTGV.Columns(0).Visible = False
        DTGV.Columns(6).Visible = False
        DTGV.Columns(10).Visible = False
        ActionRead = False


        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "Select * from View_ManufacturingDismissalNoticeData where bill_noNotice =N'" & txtbill_no.Text & "'  order by 1"
        'dr = cmd.ExecuteReader
        'Do While dr.Read
        '    DTGV.DataSource = Fn_AddBill(0, dr("itm_id").ToString, dr("group_name").ToString, dr("sname").ToString, dr("itm_Unity").ToString, Val(dr("TinPrice").ToString), Val(dr("qu")), Val(dr("qu_unity").ToString), Val(dr("totalprice").ToString), dr("Stores_From").ToString, dr("Stores_TO").ToString)
        'Loop
    End Sub

    Private Sub cmbStoresTo_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbStoresTo.SelectedIndexChanged
        If cmbStoresTo.Text.Trim = "" Then Exit Sub
        cmbCatsManufacturing.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select distinct group_name from Items where Stores =N'" & cmbStoresTo.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbCatsManufacturing.Items.Add(Trim(dr(0)))
        Loop
        cmbCatsManufacturing.Text = ""
    End Sub

    Private Sub cmbCatsManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbCatsManufacturing.SelectedIndexChanged
        If ActionRead = False Then
            If cmbCatsManufacturing.Text.Trim = "" Then Exit Sub
            cmbItemsManufacturing.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sname from Items where group_name =N'" & cmbCatsManufacturing.Text & "' and Stores =N'" & cmbStoresTo.Text & "' order by 1"
            dr = cmd.ExecuteReader
            Do While dr.Read
                cmbItemsManufacturing.Items.Add(Trim(dr(0)))
            Loop
            cmbItemsManufacturing.Text = ""
        End If
    End Sub

    Private Sub cmbItemsManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbItemsManufacturing.SelectedIndexChanged
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id_Manufacturing,Manufacturing_Allowance,Filling_Allowance from View_Product_Manufacturing where SnameManufacturing=N'" & cmbItemsManufacturing.Text & "'  and Stores_Manufacturing=N'" & cmbStoresTo.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            txtManufacturing_Allowance.Text = dr("Manufacturing_Allowance").ToString()
            txtFilling_Allowance.Text = dr("Filling_Allowance").ToString()
        End If
        txtqunt.Focus()

    End Sub

    Private Sub txtqunt_TextChanged(sender As Object, e As EventArgs) Handles txtqunt.TextChanged
        MyVars.CheckNumber(txtqunt)
    End Sub

    Private Sub txtqunt_KeyUp(sender As Object, e As KeyEventArgs) Handles txtqunt.KeyUp
        If e.KeyCode = 13 Then
            btnAdd.PerformClick()
        End If
    End Sub

    Private Sub btnSaveAll_Click(sender As Object, e As EventArgs) Handles btnSaveAll.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateSave() = False Then Exit Sub

        Dim Active As Long
        For i As Integer = 0 To DTGV.RowCount - 1
            If DTGV.Rows(i).Cells(7).Value < 0 Then
                DTGV.Rows(i).DefaultCellStyle.BackColor = Color.Red
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.White
                Active += 1
            Else
                DTGV.Rows(i).DefaultCellStyle.BackColor = Color.White
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Black
            End If
        Next
        If Active > 0 Then
            MsgBox("عفواا الكمية لاتكفى لعملية التحويل من المصنع", MsgBoxStyle.Exclamation)
            Exit Sub
        End If

        '=======================================================================================================================================================
        ' مشتريات تصنيع

        Dim itm_id_Manufacturing As String = ""
        Dim TinPrice As String = ""
        Dim Unity As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id_Manufacturing,TinPrice,Unity from View_Product_Manufacturing_Show where sname=N'" & cmbItemsManufacturing.Text & "'  and Stores_Manufacturing=N'" & cmbStoresTo.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            itm_id_Manufacturing = dr("itm_id_Manufacturing").ToString()
            TinPrice = dr("TinPrice").ToString()
            Unity = dr("Unity").ToString()
        End If


        Dim EMPID As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select EMPID from Employees where  NameEmployee = N'" & cmbEmployees.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then : EMPID = dr("EMPID") : Else EMPID = 0 : End If


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete From  Manufacturing_BilltINData where bill_noNotice =N'" & txtbill_no.Text & "'" : cmd.ExecuteNonQuery()


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update ManufacturingDismissalNotice set bill_date =N'" & Cls.C_date(dtpDateItem.Text) & "',itm_id_Manufacturing =N'" & itm_id_Manufacturing & "',Stores_From =N'" & cmbStoresFrom.Text & "',Stores_TO =N'" & cmbStoresTo.Text & "',qunt_Manufacturing =N'" & txtqunt.Text & "',TotalCostPrice =N'" & txtCostPrice.Text & "',EMPID =N'" & EMPID & "',UserName =N'" & UserName & "' where bill_no =N'" & txtbill_no.Text & "'" : cmd.ExecuteNonQuery()


        Dim CostPrice As String = ""
        Dim TotalCostPrice As Double
        Dim TypeCurrency As String = mykey.GetValue("TypeCurrency", "جنية مصرى")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select CostPrice from View_Product_Manufacturing_Show where sname=N'" & cmbItemsManufacturing.Text & "'  and Stores_Manufacturing=N'" & cmbStoresTo.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            CostPrice = dr("CostPrice")
        Else
            GoTo 1
        End If
        TotalCostPrice = Val(CostPrice) * Val(txtqunt.Text)
1:


        TotalCostPrice = Val(CostPrice) * Val(txtqunt.Text)
        PriceTinAverage(itm_id_Manufacturing, cmbStoresTo.Text, "قطعة", txtqunt.Text, TotalCostPrice)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Manufacturing_BilltINData (bill_noNotice,itm_id,price,qu,qu_unity,itm_Unity,totalprice,Stores,username,bill_date,TinPriceAverage)  values (N'" & txtbill_no.Text & "',N'" & itm_id_Manufacturing & "',N'" & TinPrice & "',N'" & txtqunt.Text & "',N'" & txtqunt.Text & "',N'" & Unity & "',N'" & TotalCostPrice & "',N'" & cmbStoresTo.Text & "',N'" & UserName & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & Val(TotalPriceBeforeAverage.ToString) & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()
2:

        IM.Store(itm_id_Manufacturing, cmbStoresTo.Text)

        If ConnectOnlineStore = "YES" Then
            EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", itm_id_Manufacturing)
            StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", itm_id_Manufacturing)
            Cos.UpdateProductStock(StockOnline, itm_id_Manufacturing, EditItmId)
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Manufacturing_BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbill_no.Text & "' and itm_id = N'" & itm_id_Manufacturing & "' and Stores =N'" & cmbStoresTo.Text & "'" : cmd.ExecuteNonQuery()

        '=======================================================================================================================================================
        ' مبيعات تصنيع
        Dim ActivationEmail As String = mykey.GetValue("ActivationEmail", "NO")
        Dim Xstore As Double
        For i As Integer = 0 To DTGV.Rows.Count - 1
            If ActivationEmail = "YES" Then
                Xstore = IM.Get_Itm_Store(DTGV.Rows(i).Cells(0).Value, DTGV.Rows(i).Cells(9).Value.Trim)
                If Xstore < 1 Then
                    SendEmail("الكمية بالمخزن قد نفذت", DTGV.Rows(i).Cells(0).Value, DTGV.Rows(i).Cells(2).Value, Xstore)
                End If
                If Xstore - Val(txtqunt.Text) < IM.Get_Itm_Range(DTGV.Rows(i).Cells(0).Value, DTGV.Rows(i).Cells(9).Value) Then
                    SendEmail("الكمية وصلت للحد الادنى", DTGV.Rows(i).Cells(0).Value, DTGV.Rows(i).Cells(2).Value, Xstore)
                End If
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Manufacturing_BillsalData(bill_noNotice,itm_id,qu,qu_unity,price,totalprice,Stores,bill_date,UserName)  values("
            S = S & "N'" & txtbill_no.Text.Trim & "',N'" & DTGV.Rows(i).Cells(0).Value & "',N'" & DTGV.Rows(i).Cells(5).Value & "',N'" & DTGV.Rows(i).Cells(5).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & Val(DTGV.Rows(i).Cells(5).Value) * Val(DTGV.Rows(i).Cells(4).Value) & "',N'" & DTGV.Rows(i).Cells(9).Value & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            IM.Store(DTGV.Rows(i).Cells(0).Value, DTGV.Rows(i).Cells(9).Value)

            If ConnectOnlineStore = "YES" Then
                EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", DTGV.Rows(i).Cells(0).Value)
                StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", DTGV.Rows(i).Cells(0).Value)
                Cos.UpdateProductStock(StockOnline, DTGV.Rows(i).Cells(0).Value, EditItmId)
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update Manufacturing_BillsalData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbill_no.Text & "' and itm_id = N'" & DTGV.Rows(i).Cells(0).Value & "' and Stores =N'" & DTGV.Rows(i).Cells(9).Value & "'" : cmd.ExecuteNonQuery()

        Next

        If chkprint.Checked = True Then
            PrintReport()
        End If

        MsgBox("تمت عملية التصنيع وأضافة المنتج المصنع بنجاح", MsgBoxStyle.Information)

        ClearSave()

        MAXRECORD()
        PanelEdit.Dock = DockStyle.None
        PanelEdit.Top = 1000
    End Sub

    Private Sub ClearSave()
        cmbCatsManufacturing.Text = ""
        cmbItemsManufacturing.Text = ""
        cmbStoresFrom.Text = ""
        cmbStoresTo.Text = ""
        cmbEmployees.Text = ""
        txtbill_no.Text = ""
        txtqunt.Text = ""
        txtTotal_qu.Text = ""
        txtCostPrice.Text = ""
        txtManufacturing_Allowance.Text = ""
        txtFillingManufacturing.Text = ""
        DTGV.DataSource = ""
    End Sub

    Private Sub PriceTinAverage(ByVal Parcode As String, ByVal Stores As String, ByVal Unity As String, ByVal qunt As Double, ByVal TotalPrice As Double)
        Dim StoreItems, TotalPriceTinAverage, BalanceBeforeBuying, TotalBalanceBeforeBuying As Double
        '================================================ المخزون الحالى =====================================================
        Dim Xqunt As Double = qunt

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select store,TinPriceAverage from Items where itm_id =N'" & Parcode & "' and Stores =N'" & Stores & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            StoreItems = dr("store").ToString()
            Dim xx As String = dr("TinPriceAverage").ToString()
            If xx = "" Then
                TotalPriceTinAverage = 0
            Else
                TotalPriceTinAverage = xx
            End If
        End If
        If NotUnityItemsProgram = "YES" Then
            NumberPieces = Cls.Get_Code_Value_Branch_More("ItemsUnity", "NumberPieces", "itm_id=N'" & Parcode & "' and Unity_Name=N'" & Unity & "'")
            If NumberPieces <> 1 Then
                Xqunt = Val(NumberPieces) * Val(qunt)
            End If
        End If

        '================================================ متوسط سعر الشراء الجديد =====================================================

        Try
            BalanceBeforeBuying = Val(TotalPriceTinAverage) * Val(StoreItems)

            TotalBalanceBeforeBuying = Val(TotalPrice) + Val(BalanceBeforeBuying)
            TotalBalanceBeforeBuying = Math.Round(TotalBalanceBeforeBuying, 2)

            Dim TotalTotal As Double = Val(StoreItems) + Val(Xqunt)
            If TotalBalanceBeforeBuying = 0 And TotalTotal = 0 Then
                TotalPriceBeforeAverage = 0
            Else
                TotalPriceBeforeAverage = Val(TotalBalanceBeforeBuying) / Val(TotalTotal)
                If TinPriceAverageThreeDigits = "NO" Then
                    TotalPriceBeforeAverage = Math.Round(TotalPriceBeforeAverage, 2)
                Else
                    TotalPriceBeforeAverage = Math.Round(TotalPriceBeforeAverage, 3)
                End If
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update items set TinPriceAverage = " & Val(TotalPriceBeforeAverage) & " where itm_id =N'" & Parcode & "' and Stores =N'" & Stores & "'" : cmd.ExecuteNonQuery()
        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Function ValidateSave() As Boolean

        If txtbill_no.Text = "" Then MsgBox("فضلا أدخل رقم أذن الصرف", MsgBoxStyle.Exclamation) : txtbill_no.Focus() : Return False
        If cmbStoresFrom.Text = "" Then MsgBox("من فضلك اختر المخزن المحول منه", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If cmbStoresTo.Text = "" Then MsgBox("من فضلك اختر المخزن المحول البه", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If cmbItemsManufacturing.Text = "" Then MsgBox("من فضلك اختر أسم المنتج المصنع", MsgBoxStyle.Exclamation) : cmbItemsManufacturing.Focus() : Return False
        If txtqunt.Text = "" Then MsgBox("من فضلك الكمية المصنعة", MsgBoxStyle.Exclamation) : txtqunt.Focus() : Return False
        If DTGV.Rows.Count < 1 Then MsgBox("فضلا أكمل بيانات الفاتورة", MsgBoxStyle.Exclamation) : Return False

        Return True
    End Function

    Private Sub btnAdd_Click(sender As Object, e As EventArgs) Handles btnAdd.Click
        If ValidateTextAdd() = False Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id as [الباركود],group_name as [المجموعة],SnameManufacturing as [المنتج المصنع] ,sname as [الخامات],TinPrice as [سعر التكلفة] ,QU_Manufacturing as [الكمية المصنعة],store as [الكمية الحالية],QU_Rest as [الكمية المتبقية],CostPrice as [أجمالى التكلفة],Stores as [مخزن المصنع],Stores_Manufacturing as [مخزن المنتج التام],itm_Unity as [وحدة القياس] from View_Product_Manufacturing where SnameManufacturing =N'" & cmbItemsManufacturing.Text & "'  order by 1"
        dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)
        DTGV.Columns(0).Visible = False
        DTGV.Columns(11).Visible = False
        DTGV.Columns(2).Width = 190
        DTGV.Columns(4).Width = 190

        Dim store, qu_unity As Double
        Dim CostPrice, Weight, Active As Long
        CostPrice = 0 : Weight = 0 = Active = 0

        For i As Integer = 0 To DTGV.RowCount - 1
            DTGV.Rows(i).Cells(9).Value = cmbStoresFrom.Text

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select store from Items where itm_id=N'" & DTGV.Rows(i).Cells(0).Value & "'  and Stores=N'" & DTGV.Rows(i).Cells(9).Value & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : store = dr("store").ToString() : End If
            DTGV.Rows(i).Cells(6).Value = Val(store)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_unity from View_Product_Manufacturing where SnameManufacturing=N'" & DTGV.Rows(i).Cells(2).Value & "' and sname=N'" & DTGV.Rows(i).Cells(3).Value & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : qu_unity = dr("qu_unity").ToString() : End If
            DTGV.Rows(i).Cells(5).Value = Val(qu_unity) * Val(txtqunt.Text)

            DTGV.Rows(i).Cells(7).Value = Val(store) - Val(DTGV.Rows(i).Cells(5).Value)

            DTGV.Rows(i).Cells(8).Value = Val(DTGV.Rows(i).Cells(4).Value) * Val(DTGV.Rows(i).Cells(5).Value)

            CostPrice += Val(DTGV.Rows(i).Cells(8).Value)
            Weight += Val(DTGV.Rows(i).Cells(4).Value)

            If DTGV.Rows(i).Cells(7).Value < 0 Then
                DTGV.Rows(i).DefaultCellStyle.BackColor = Color.Red
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.White
                Active += 1
            Else
                DTGV.Rows(i).DefaultCellStyle.BackColor = Color.White
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Black
            End If
        Next
        If Active > 0 Then
            MsgBox("عفواا الكمية لاتكفى لعملية التحويل من المصنع", MsgBoxStyle.Exclamation)
        End If

        Dim TotalManufacturing As Double = Val(txtManufacturing_Allowance.Text) + Val(txtFilling_Allowance.Text)
        txtFillingManufacturing.Text = Val(TotalManufacturing) * Val(txtqunt.Text)
        txtCostPrice.Text = CostPrice.ToString() + Val(txtFillingManufacturing.Text)
        txtTotal_qu.Text = Weight.ToString()
    End Sub

    Function ValidateTextAdd() As Boolean
        If txtbill_no.Text = "" Then MsgBox("فضلا أدخل رقم أذن الصرف", MsgBoxStyle.Exclamation) : txtbill_no.Focus() : Return False
        If cmbStoresFrom.Text = "" Then MsgBox("فضلا أختر من مخزن المصنع", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If cmbStoresTo.Text.Trim = "" Then MsgBox("فضلا أختر الى مخزن المنتج التام", MsgBoxStyle.Exclamation) : cmbStoresTo.Focus() : Return False
        If cmbCatsManufacturing.Text = "" Then MsgBox("فضلا أختر مجموعة الصنف", MsgBoxStyle.Exclamation) : cmbCatsManufacturing.Focus() : Return False
        If cmbItemsManufacturing.Text = "" Then MsgBox("فضلا أختر أسم المنتج المصنع", MsgBoxStyle.Exclamation) : cmbItemsManufacturing.Focus() : Return False
        If txtqunt.Text = "" Then MsgBox("فضلا أدخل الكمية المصنعة", MsgBoxStyle.Exclamation) : txtqunt.Focus() : Return False

        Return True
    End Function

    Private Sub cmbCatsManufacturingView_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbCatsManufacturingView.SelectedIndexChanged
        If ActionRead = False Then
            If cmbCatsManufacturingView.Text.Trim = "" Then Exit Sub
            cmbItemsManufacturingView.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select sname from Items where group_name =N'" & cmbCatsManufacturingView.Text & "' and Stores =N'" & cmbStoreView.Text & "' order by 1"
            dr = cmd.ExecuteReader
            Do While dr.Read
                cmbItemsManufacturingView.Items.Add(Trim(dr(0)))
            Loop
            cmbItemsManufacturingView.Text = ""
            cmbItemsManufacturingView.Focus()
        End If
    End Sub

    Private Sub txtbill_no_TextChanged(sender As Object, e As EventArgs) Handles txtbill_no.TextChanged
        MyVars.CheckNumber(txtbill_no)
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub


#End Region

End Class
