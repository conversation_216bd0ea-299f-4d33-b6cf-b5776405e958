﻿Imports CrystalDecisions.CrystalReports.Engine
Public Class frmShowEmployees
    Dim WithEvents BS As New BindingSource
    Dim archiveManager As New Cls_ArchiveManager

    Private Sub frmShowEmployees_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbNameEmployee)
        GetData()
        If PermtionName <> "مدير" Then
            btnDelete.Visible = False
            btnEdit.Visible = False
        End If
    End Sub

    Private Sub GetData()
        Try
            ' بناء الاستعلام الأساسي
            Dim baseQuery As String = "SELECT EMPID as [كود الموظف], NameEmployee as [إسم الموظف], Address as [العنوان], Phone as [التليفون], MaritalStatus as [الحالة الاجتماعية], Qualification as [المؤهل], Job as [الوظيفة], Administration as [الادارة], DateBirth as [تاريخ الميلاد], DateAppointment as [تاريخ التعيين], DateEndService as [تاريخ نهاية الخدمة], NationalID as [الرقم القومى], NumberInsurance as [الرقم التأمينى], MilitaryService as [الخدمة العسكرية], BalanceLeavePrior as [رصيد اجازات سابق], BalanceAnnualLeave as [رصيد اجازات سنوى], TotalBalanceLeave as [اجمالى رصيد الاجازات], DaysAbsence as [ايام الغياب], DaysSanctions as [ايام الجزاءات], LeaveWithoutPay as [اجازة بدون مرتب] FROM Employees WHERE EMPID <> @excludeValue"

            Dim queryBuilder As New SafeQueryBuilder(baseQuery)
            queryBuilder.AddCustomCondition("", New Dictionary(Of String, Object) From {{"@excludeValue", ""}})

            ' إضافة شروط البحث الآمنة
            If Not ChbAll.Checked Then
                queryBuilder.AddWhereCondition("NameEmployee", cmbNameEmployee.Text.Trim())
            End If

            ' شروط التاريخ
            If Not ChkWithoutDate.Checked Then
                Dim fromDate As Date? = Cls.C_date(DateTimePicker1.Text)
                Dim toDate As Date? = Cls.C_date(DateTimePicker2.Text)
                queryBuilder.AddDateRangeCondition("DateAppointment", fromDate, toDate)
            End If

            ' إضافة ترتيب النتائج
            Dim finalQuery As String = queryBuilder.Build()
            Select Case FilterSelect
                Case "Number"
                    finalQuery += " ORDER BY [كود الموظف]"
                Case "Date"
                    finalQuery += " ORDER BY [تاريخ التعيين]"
                Case "Name"
                    finalQuery += " ORDER BY [إسم الموظف]"
            End Select

            ' تنفيذ الاستعلام الآمن
            Dim result As DataTable = SecureDatabaseManager.Instance.ExecuteQuery(finalQuery, queryBuilder.Parameters)
            DataGridView1.DataSource = result
            DataGridView1.Columns(0).Visible = False

        Catch ex As Exception
            ErrorHandler.LogError("خطأ في تحميل بيانات الموظفين", ex)
            MessageBox.Show("حدث خطأ في تحميل البيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ChbAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChbAll.CheckedChanged
        If ChbAll.Checked = True Then
            cmbNameEmployee.Enabled = False
            cmbNameEmployee.SelectedIndex = -1
        ElseIf ChbAll.Checked = False Then
            cmbNameEmployee.Enabled = True
        End If
    End Sub

    Private Sub ChkWithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub btnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnShow.Click
        GetData()
    End Sub

    Private Sub btnEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnEdit.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
        If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        EditItmId = DataGridView1.SelectedRows(0).Cells(0).Value
        Action = "Edit"
        'Me.Close()
        frmEmployees.Show()
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim NumberID As String
            NumberID = DataGridView1.SelectedRows(0).Cells(0).Value


            '=========================================== Archive Manager ============================================================
            archiveManager.ArchiveAndDeleteEmployeeData("Delete", NumberID, UserName, "تم حذف الموظف لعدم النشاط")
            '=========================================== Archive Manager ============================================================


            cmd.CommandText = "delete from Employees where EMPID =N'" & NumberID & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete from EmployeesAccount where Emp_Code =N'" & NumberID & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete from Employee_Customers where Employee_Code =N'" & NumberID & "'" : cmd.ExecuteNonQuery()
            cmd.CommandText = "delete from Geographic_Area_Employee where Employee_Code =N'" & NumberID & "'" : cmd.ExecuteNonQuery()

        Next
        GetData()
    End Sub

    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Dim txtFROMDate As String
        Dim txtToDate As String
        Dim f As New Frm_PrintReports
        Dim rpt As New rptEmployees
        Dim txt As TextObject
        txtFROMDate = Format(Me.DateTimePicker1.Value, "yyy, MM, dd, 00, 00, 000")
        txtToDate = Format(Me.DateTimePicker2.Value, "yyy, MM, dd, 00, 00, 00")
        'On Error Resume Next
        GETSERVERNAMEANDDATABASENAME(rpt, DataBaseName, PasswordServer, UserNameServer)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If ChkWithoutDate.Checked = False Or ChbAll.Checked = False Then
            If ChbAll.Checked = False Then
                rpt.RecordSelectionFormula = "{Employees.DateAppointment} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")AND{Employees.NameEmployee} =N'" & cmbNameEmployee.Text & "'"
                txt = rpt.Section1.ReportObjects("txtDate")
                txt.Text = "خلال الفترة من" & Format(Me.DateTimePicker1.Value, "dd - MM - yyyy") & " الى " & Format(Me.DateTimePicker2.Value, "dd - MM - yyyy")
            Else
                rpt.RecordSelectionFormula = "{Employees.DateAppointment} in DateTime (" & txtFROMDate & ") to DateTime (" & txtToDate & ")"
                txt = rpt.Section1.ReportObjects("txtDate")
                txt.Text = "خلال الفترة من" & Format(Me.DateTimePicker1.Value, "dd - MM - yyyy") & " الى " & Format(Me.DateTimePicker2.Value, "dd - MM - yyyy")
            End If
        End If
        AddReportView()
        Dim txtname, txtNameAr, txtNameEn As TextObject
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "تقرير بيانات الموظفين"
        txtNameAr = rpt.Section1.ReportObjects("txtTitemNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitemNameEn")
        txtNameEn.Text = NameEnCompany
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Show()

    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class