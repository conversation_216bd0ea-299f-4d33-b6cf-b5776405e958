﻿Imports System.Data.SqlClient
Imports System.IO

Public Class Form3
    Private Sub TextBox1_TextChanged(sender As Object, e As EventArgs) Handles TextBox1.TextChanged
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        connectionStringOpen()

        If TextBox1.Text <> "" Then
            ListBoxItems.Visible = True
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            Dim X As String = "%" & TextBox1.Text.Trim & "%"
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "Select sname from items where sname like N'" & X & "'"
            dr = cmd.ExecuteReader
            ListBoxItems.Items.Clear()
            Do While dr.Read
                ListBoxItems.Items.Add(dr(0))
            Loop
        End If
    End Sub

    Private Sub btnSearchItems_Click(sender As Object, e As EventArgs) Handles btnSearchItems.Click
        ListBoxItems.Visible = True
        ListBoxItems.Visible = True
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        Dim X As String = "%" & TextBox1.Text.Trim & "%"
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "Select sname from items"
        dr = cmd.ExecuteReader
        ListBoxItems.Items.Clear()
        Do While dr.Read
            ListBoxItems.Items.Add(dr(0))
        Loop
    End Sub

    Private Sub TextBox1_KeyDown(sender As Object, e As KeyEventArgs) Handles TextBox1.KeyDown
        If ((e.KeyCode = 8)) Then
            ListBoxItems.Visible = False
        End If
        If ((e.KeyCode = 40)) Then
            ListBoxItems.Focus()
            ListBoxItems.Focus()
        End If
        If ((e.KeyCode = 40)) Then
            ListBoxItems.Focus()
        End If
    End Sub

    Private Sub ListBox1_DoubleClick(sender As Object, e As EventArgs) Handles ListBoxItems.DoubleClick
        ListBoxItems.Visible = False
    End Sub

    Private Sub ListBox1_KeyUp(sender As Object, e As KeyEventArgs) Handles ListBoxItems.KeyUp
        If e.KeyCode = 13 Then
            ListBoxItems.Visible = False
        End If
    End Sub

    Private Sub Form3_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub
End Class