﻿Imports CrystalDecisions.CrystalReports.Engine
Imports System.IO
Public Class Frm_Checks_Payable_Show

    Dim WithEvents BS As New BindingSource
    Dim rpt As New Rpt_Checks_Payable
    Dim Simage As Image

    Private Sub ChkWithoutDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkWithoutDate.CheckedChanged
        If ChkWithoutDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub BtnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnShow.Click
        FillData()
    End Sub

    Private Sub FillData()

        Dim PayMade As String
        If rdoPayMade.Checked = True Then
            PayMade = "تم السداد"
        End If
        If rdoPayMadeNot.Checked = True Then
            PayMade = "لم يتم السداد"
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT Cust_Name as [أسم العميل],Amount as [المبلغ],Due_Date as [التاريخ],Bank_Name as [أسم البنك],Check_Number as [رقم الشيك],Notes as [ملاحظات],State_Check as [حالة الشيك] FROM Checks_Payable where State_Check =N'" & PayMade & "'"

        If ChkWithoutDate.Checked = False Then
            S = S & " and Due_Date >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and Due_Date <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(2).Value)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(2).Value = SM
        Next

        DataGridView1.Columns(0).Width = 110
        DataGridView1.Columns(1).Width = 70
        DataGridView1.Columns(2).Width = 80
        DataGridView1.Columns(3).Width = 90
        DataGridView1.Columns(4).Width = 90
        DataGridView1.Columns(5).Width = 100

        DataGridView1.Columns(6).Visible = False

        Dim SM4 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM4 = SM4 + DataGridView1.Rows(i).Cells(1).Value
        Next
        txtTotalCheck.Text = SM4

    End Sub

    Private Sub FillDataChack()

        Dim PayMade As String
        PayMade = "لم يتم السداد"

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "SELECT Cust_Name as [أسم العميل],Amount as [المبلغ],Due_Date as [التاريخ],Bank_Name as [أسم البنك],Check_Number as [رقم الشيك],Notes as [ملاحظات] FROM Checks_Payable where State_Check =N'" & PayMade & "' and Check_Number =N'" & txtActionChack.Text & "'"

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)

        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(2).Value)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(2).Value = SM
        Next

        DataGridView1.Columns(0).Width = 120
        DataGridView1.Columns(1).Width = 60
        DataGridView1.Columns(2).Width = 70
        DataGridView1.Columns(3).Width = 70
        DataGridView1.Columns(4).Width = 70
        DataGridView1.Columns(5).Width = 70

        '==========================================================
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from Checks_Payable where State_Check =N'" & PayMade & "' and Check_Number =N'" & txtActionChack.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            txtCust_Name.Text = dr("Cust_Name")
        End If
        If dr(1) Is DBNull.Value Then
        Else
            txtAmount.Text = dr("Amount")
        End If
        If dr(2) Is DBNull.Value Then
        Else
            dtpDue_Date.Text = Cls.R_date(dr("Due_Date"))
        End If
        If dr(3) Is DBNull.Value Then
        Else
            txtBank_Name.Text = dr("Bank_Name")
        End If
        If dr(4) Is DBNull.Value Then
        Else
            txtCheck_Number.Text = dr("Check_Number")
        End If
        If dr(5) Is DBNull.Value Then
        Else
            txtNotes.Text = dr("Notes")
        End If

    End Sub

    Private Sub txtActionChack_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtActionChack.TextChanged
        FillDataChack()
    End Sub

    Private Sub btnCollectionCheck_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCollectionCheck.Click
        Dim PayMade As String
        PayMade = "تم السداد"
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Checks_Payable set State_Check =N'" & PayMade & "' where Check_Number =N'" & txtActionChack.Text & "'" : cmd.ExecuteNonQuery()

        ClearTest()

        MsgBox("تم تحصيل الشيك", MsgBoxStyle.Information) : txtCust_Name.Focus()

    End Sub

    Private Sub ClearTest()
        txtAmount.Text = ""
        txtBank_Name.Text = ""
        txtCheck_Number.Text = ""
        txtCust_Name.Text = ""
        txtNotes.Text = ""
    End Sub

    Private Sub Frm_Checks_Payable_Show_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

    End Sub

    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If
        AddReportView()

        Cls.delete_Branch_All("PrintSalesPurchases")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.Rows.Count - 1

            S = "insert into PrintSalesPurchases(Company_Branch_ID,CustomerName,price,bill_date,UserName,BILL_NO,det,STAYING,totalpriceafterdisc) values ("
            S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView1.Rows(i).Cells(0).Value & "' ,N'" & DataGridView1.Rows(i).Cells(1).Value & "' ,N'" & DataGridView1.Rows(i).Cells(2).Value & "',N'" & DataGridView1.Rows(i).Cells(3).Value & "',N'" & DataGridView1.Rows(i).Cells(4).Value & "',N'" & DataGridView1.Rows(i).Cells(5).Value & "',N'" & DataGridView1.Rows(i).Cells(6).Value & "',N'" & txtTotalCheck.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

        Next

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        Dim txt, txtNameAr, txtNameEn As TextObject
        txt = rpt.Section1.ReportObjects("txtReportTitel")
        txt.Text = "تقرير بالشيكات"
        txtNameAr = rpt.Section1.ReportObjects("txtTitelNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelNameEn")
        txtNameEn.Text = NameEnCompany
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "تقرير بالشيكات"
        Frm_PrintReports.Show()
    End Sub

    Private Sub DISPLAYRECORD()
        On Error Resume Next
        With Me
            NameArCompay = ds.Tables("COMPANY").Rows(Me.BS.Position)("CMPName").ToString
            NameEnCompany = ds.Tables("COMPANY").Rows(Me.BS.Position)("CMPNameEnglish").ToString
        End With
        Dim sql As String
        sql = "SELECT CMPImage FROM COMPANY"
        Dim cmd As SqlClient.SqlCommand = New SqlClient.SqlCommand(sql, Cn)
        Dim b() As Byte
        b = cmd.ExecuteScalar()
        If (b.Length > 0) Then
            Dim stream As New MemoryStream(b, True)
            stream.Write(b, 0, b.Length)
            Simage = New Bitmap(stream)
            stream.Close()
        Else
            Simage = Nothing
        End If
    End Sub

End Class