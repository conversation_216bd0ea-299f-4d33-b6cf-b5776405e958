﻿Public Class FrmEmployeesDiscountReward
    Dim Result_Code As String
    Dim Yers As String = ""
    Dim Month As String = ""

    Private Sub FrmEmployeesDiscountReward_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbNameEmployee)
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbNameEmployeeFind)
        Bra.Fil("EmployeesDiscountReward_Type", "DiscountReward_Type", cmbDiscountReward_Type)
        Bra.Fil("EmployeesSalarySystem", "SalarySystemName", cmbSalaryPaymentSystem)
        Bra.Fil("EmployeesSalarySystem", "SalarySystemName", cmbViewSalaryPaymentSystem)
        cmbSalaryPaymentSystem.SelectedItem = "شهر"
        MAXRECORD("EmployeesDiscountReward", "Move_Number")
        Panel_Search.Top = 5000
        GetDateNotBeenActivatedPrograms(dtpMove_Date)
        GetDateNotBeenActivatedOutcome()

        If PermtionName <> "مدير" Then
            btnDelete.Visible = False
        End If
    End Sub

    Private Sub GetDateNotBeenActivatedOutcome()
        If PermtionName <> "مدير" Then
            If DateNotBeenActivatedOutcome = "NO" Then
                dtpMove_Date.Enabled = True
            Else
                dtpMove_Date.Enabled = False
            End If
        End If
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If Validate_Text() = False Then Exit Sub

        Dim EMPID As Integer
        Dim SalarySystem_ID As Integer
        Chack_Code("EmployeesSalarySystem", "SalarySystem_ID", "SalarySystemName", cmbSalaryPaymentSystem.Text)
        SalarySystem_ID = Result_Code

        Dim Recase As Integer
        Dim DiscountReward_Type_ID As Integer
        If rdoBorrowing.Checked = True Then
            Chack_Code("EmployeesDiscountReward_Type", "DiscountReward_Type_ID", "DiscountReward_Type", rdoBorrowing.Text)
            Recase = 0
        End If
        If rdoDelayDiscounts.Checked = True Then
            Chack_Code("EmployeesDiscountReward_Type", "DiscountReward_Type_ID", "DiscountReward_Type", rdoDelayDiscounts.Text)
            Recase = 0
        End If
        If rdoDiscountsAbsence.Checked = True Then
            Chack_Code("EmployeesDiscountReward_Type", "DiscountReward_Type_ID", "DiscountReward_Type", rdoDiscountsAbsence.Text)
            Recase = 0
        End If
        If rdoDiscountsSanctions.Checked = True Then
            Chack_Code("EmployeesDiscountReward_Type", "DiscountReward_Type_ID", "DiscountReward_Type", rdoDiscountsSanctions.Text)
            Recase = 0
        End If
        If rdoReward.Checked = True Then
            Chack_Code("EmployeesDiscountReward_Type", "DiscountReward_Type_ID", "DiscountReward_Type", rdoReward.Text)
            Recase = 1
        End If
        If rdoPremium.Checked = True Then
            Chack_Code("EmployeesDiscountReward_Type", "DiscountReward_Type_ID", "DiscountReward_Type", rdoPremium.Text)
            Recase = 1
        End If
        If rdoTransferAllowance.Checked = True Then
            Chack_Code("EmployeesDiscountReward_Type", "DiscountReward_Type_ID", "DiscountReward_Type", rdoTransferAllowance.Text)
            Recase = 1
        End If
        If rdoNatureWork.Checked = True Then
            Chack_Code("EmployeesDiscountReward_Type", "DiscountReward_Type_ID", "DiscountReward_Type", rdoNatureWork.Text)
            Recase = 1
        End If
        If rdoHousingAllowance.Checked = True Then
            Chack_Code("EmployeesDiscountReward_Type", "DiscountReward_Type_ID", "DiscountReward_Type", rdoHousingAllowance.Text)
            Recase = 1
        End If
        If rdoOtherAdditives.Checked = True Then
            Chack_Code("EmployeesDiscountReward_Type", "DiscountReward_Type_ID", "DiscountReward_Type", rdoOtherAdditives.Text)
            Recase = 1
        End If
        DiscountReward_Type_ID = Result_Code

        Chack_Code("Employees", "EMPID", "NameEmployee", cmbNameEmployee.Text)
        EMPID = Result_Code

        SplitYersMonth()

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into EmployeesDiscountReward(Company_Branch_ID,Move_Number,EMPID,SalarySystem_ID,Month,Years,Move_Date,Amount,DiscountReward_Type_ID,Recase,UserName,Treasury_Code) values ("
        S = S & "N'" & Company_Branch_ID & "',N'" & txtCode.Text & "',N'" & EMPID & "',N'" & SalarySystem_ID & "',N'" & Month & "',N'" & Yers & "',N'" & Cls.C_date(dtpMove_Date.Text) & "',N'" & txtAmount.Text & "',N'" & DiscountReward_Type_ID & "',N'" & Recase & "',N'" & UserName & "',N'" & Treasury_Code & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()

        Get_Movement_In_Out_Money(dtpMove_Date.Text, Treasury_Code)

        CLEAR_ALL()

        MAXRECORD("EmployeesDiscountReward", "Move_Number")
        Header()
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)
        cmbNameEmployee.Focus()
    End Sub

    Private Sub SplitYersMonth()
        Dim PRC As String = dtpMove_Date.Text
        Dim split As String() = New String() {"/"}
        Dim itemsSplit As String() = PRC.Split(split, StringSplitOptions.None)
        Yers = itemsSplit(2).ToString()
        Dim XMonth As String = itemsSplit(1).ToString()

        If XMonth = "01" Then
            Month = "يناير"
        End If
        If XMonth = "02" Then
            Month = "فبراير"
        End If
        If XMonth = "03" Then
            Month = "مارس"
        End If

        If XMonth = "04" Then
            Month = "ابريل"
        End If
        If XMonth = "05" Then
            Month = "مايو"
        End If
        If XMonth = "06" Then
            Month = "يونيو"
        End If
        If XMonth = "07" Then
            Month = "يوليه"
        End If
        If XMonth = "08" Then
            Month = "اغسطس"
        End If
        If XMonth = "09" Then
            Month = "سبتمبر"
        End If
        If XMonth = "10" Then
            Month = "اكتوبر"
        End If
        If XMonth = "11" Then
            Month = "نوفمبر"
        End If
        If XMonth = "12" Then
            Month = "ديسمبر"
        End If

    End Sub


    Sub CLEAR_ALL()
        txtAmount.Text = ""
        cmbNameEmployee.Text = ""
    End Sub

    Private Sub Chack_Code(ByVal Table As String, ByVal Code As String, ByVal Name As String, ByVal TextBox As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select " & Code & " from " & Table & " where " & Name & "=N'" & TextBox & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            Result_Code = dr(0)
        End If
    End Sub

    Function Validate_Text() As Boolean
        If Trim(cmbNameEmployee.Text) = "" Then
            MsgBox("فضلاً أختر اسم الموظف", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            cmbNameEmployee.Focus() : Return False : Exit Function
        End If
        If Trim(cmbSalaryPaymentSystem.Text) = "" Then
            MsgBox("فضلاً أختر نظام دفع الراتب", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            cmbSalaryPaymentSystem.Focus() : Return False : Exit Function
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * From EmployeesDiscountReward where Move_Number =N'" & txtCode.Text & "'"
        dr = cmd.ExecuteReader()
        If dr.HasRows = True Then
            MsgBox("عفواً يوجد رقم حركة مسجله مسبقا بنفس الرقم", MsgBoxStyle.Exclamation)
            Return False : Exit Function
        End If

        Return True
    End Function

    Private Sub MAXRECORD(ByVal Table As String, ByVal Felds As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " + Table + ""
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            txtCode.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" + Felds + " As float)) as mb FROM " + Table + " where " + Felds + " <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            txtCode.Text = sh + 1
        End If

    End Sub

    Private Sub txtAmount_TextChanged(sender As Object, e As EventArgs) Handles txtAmount.TextChanged
        MyVars.CheckNumber(txtAmount)
    End Sub

    Private Sub cmbNameEmployee_KeyUp(sender As Object, e As KeyEventArgs) Handles cmbNameEmployee.KeyUp
        If e.KeyCode = 13 Then
            dtpMove_Date.Focus()
        End If
    End Sub

    Private Sub dtpMove_Date_KeyUp(sender As Object, e As KeyEventArgs) Handles dtpMove_Date.KeyUp
        If e.KeyCode = 13 Then
            txtAmount.Focus()
        End If
    End Sub

    Private Sub txtAmount_KeyUp(sender As Object, e As KeyEventArgs) Handles txtAmount.KeyUp
        If e.KeyCode = 13 Then
            btnSave.PerformClick()
        End If
    End Sub

    Private Sub Header()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        If PermtionName = "مدير" Then
            S = "Select dbo.EmployeesDiscountReward.id, dbo.EmployeesDiscountReward.Move_Number As [رقم الحركة], dbo.Employees.NameEmployee As [أسم الموظف], dbo.EmployeesDiscountReward.Move_Date As التاريخ,  dbo.EmployeesDiscountReward.Amount AS المبلغ, dbo.EmployeesDiscountReward_Type.DiscountReward_Type As [نوع الحركة], dbo.EmployeesDiscountReward.Recase, dbo.EmployeesDiscountReward.UserName,dbo.EmployeesDiscountReward.Company_Branch_ID  From dbo.EmployeesDiscountReward INNER Join   dbo.EmployeesDiscountReward_Type ON dbo.EmployeesDiscountReward.DiscountReward_Type_ID = dbo.EmployeesDiscountReward_Type.DiscountReward_Type_ID INNER Join  dbo.Employees ON dbo.EmployeesDiscountReward.EMPID = dbo.Employees.EMPID Where (dbo.EmployeesDiscountReward.id <> N'')"
        Else
            S = "Select dbo.EmployeesDiscountReward.id, dbo.EmployeesDiscountReward.Move_Number As [رقم الحركة], dbo.Employees.NameEmployee As [أسم الموظف], dbo.EmployeesDiscountReward.Move_Date As التاريخ,  dbo.EmployeesDiscountReward.Amount AS المبلغ, dbo.EmployeesDiscountReward_Type.DiscountReward_Type As [نوع الحركة], dbo.EmployeesDiscountReward.Recase, dbo.EmployeesDiscountReward.UserName,dbo.EmployeesDiscountReward.Company_Branch_ID  From dbo.EmployeesDiscountReward INNER Join   dbo.EmployeesDiscountReward_Type ON dbo.EmployeesDiscountReward.DiscountReward_Type_ID = dbo.EmployeesDiscountReward_Type.DiscountReward_Type_ID INNER Join  dbo.Employees ON dbo.EmployeesDiscountReward.EMPID = dbo.Employees.EMPID Where (dbo.EmployeesDiscountReward.id <> N'') AND (dbo.EmployeesDiscountReward.Company_Branch_ID =N'" & Company_Branch_ID & "')"
        End If
        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
        DataGridView1.Columns(0).Visible = False
        DataGridView1.Columns(6).Visible = False
        DataGridView1.Columns(7).Visible = False
        DataGridView1.Columns(8).Visible = False

        DataGridView1.Columns(1).Width = 90
        DataGridView1.Columns(2).Width = 200

        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(3).Value)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(3).Value = SM
        Next

        Dim Recase As Integer
        Dim SM1 As Double
        Dim SM2 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            Recase = Val(DataGridView1.Rows(i).Cells(6).Value)
            If Recase = 0 Then
                SM1 = SM1 + DataGridView1.Rows(i).Cells(4).Value
            End If
            If Recase = 1 Then
                SM2 = SM2 + DataGridView1.Rows(i).Cells(4).Value
            End If
        Next
        txtTotalDebit.Text = SM1
        txtTotalCreditor.Text = SM2
    End Sub

    Private Sub HeaderSearch()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "Select dbo.EmployeesDiscountReward.id  As [رقم], dbo.EmployeesDiscountReward.Move_Number As [رقم الحركة], dbo.Employees.NameEmployee As [أسم الموظف], dbo.EmployeesDiscountReward.Move_Date As [التاريخ], dbo.EmployeesDiscountReward.Amount AS المبلغ, dbo.EmployeesDiscountReward_Type.DiscountReward_Type As [نوع الحركة], dbo.EmployeesSalarySystem.SalarySystemName As [نظام الراتب],  dbo.EmployeesDiscountReward.Recase, dbo.EmployeesDiscountReward.UserName, dbo.EmployeesDiscountReward.Company_Branch_ID From dbo.EmployeesDiscountReward INNER Join  dbo.EmployeesDiscountReward_Type ON dbo.EmployeesDiscountReward.DiscountReward_Type_ID = dbo.EmployeesDiscountReward_Type.DiscountReward_Type_ID INNER Join dbo.Employees ON dbo.EmployeesDiscountReward.EMPID = dbo.Employees.EMPID INNER Join  dbo.EmployeesSalarySystem ON dbo.EmployeesDiscountReward.SalarySystem_ID = dbo.EmployeesSalarySystem.SalarySystem_ID Where (dbo.EmployeesDiscountReward.id <> N'')"
        If chkAll.Checked = False Then
            If cmbNameEmployeeFind.Text <> "" Then
                S = S & " And (dbo.Employees.NameEmployee = N'" & cmbNameEmployeeFind.Text.Trim & "')"
            End If
            If cmbDiscountReward_Type.Text <> "" Then
                S = S & " And (dbo.EmployeesDiscountReward_Type.DiscountReward_Type = N'" & cmbDiscountReward_Type.Text.Trim & "')"
            End If
            If cmbDiscountReward_Type.Text <> "" Then
                S = S & " And (dbo.EmployeesDiscountReward_Type.DiscountReward_Type = N'" & cmbDiscountReward_Type.Text.Trim & "')"
            End If
            If cmbViewSalaryPaymentSystem.Text <> "" Then
                S = S & " And (dbo.EmployeesSalarySystem.SalarySystemName = N'" & cmbViewSalaryPaymentSystem.Text.Trim & "')"
            End If
        End If
        If chkDate.Checked = False Then
            S = S & " AND dbo.EmployeesDiscountReward.Move_Date >=N'" & Cls.C_date(dtpFrom.Text) & "' AND dbo.EmployeesDiscountReward.Move_Date <=N'" & Cls.C_date(dtpTo.Text) & "'"
        End If
        If PermtionName <> "مدير" Then
            S = S & " AND dbo.EmployeesDiscountReward.Company_Branch_ID =N'" & Company_Branch_ID & "'"
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم الحركة]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [التاريخ]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [أسم الموظف]"
        End If

        cmd.CommandText = S : dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)
        DataGridView1.Columns(0).Visible = False
        DataGridView1.Columns(7).Visible = False
        DataGridView1.Columns(8).Visible = False
        DataGridView1.Columns(1).Width = 90
        DataGridView1.Columns(2).Width = 200

        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(3).Value)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(3).Value = SM
        Next

        Dim Recase As Integer
        Dim SM1 As Double
        Dim SM2 As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            Recase = Val(DataGridView1.Rows(i).Cells(6).Value)
            If Recase = 0 Then
                SM1 = SM1 + DataGridView1.Rows(i).Cells(4).Value
            End If
            If Recase = 1 Then
                SM2 = SM2 + DataGridView1.Rows(i).Cells(4).Value
            End If
        Next
        txtTotalDebit.Text = SM1
        txtTotalCreditor.Text = SM2
    End Sub

    Private Sub BtnFind_Click(sender As Object, e As EventArgs) Handles BtnFind.Click
        Panel_Search.Top = 20
        Panel_Search.Dock = DockStyle.Fill
    End Sub

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        HeaderSearch()
    End Sub

    Private Sub chkAll_CheckedChanged(sender As Object, e As EventArgs) Handles chkAll.CheckedChanged
        If chkAll.Checked = True Then
            cmbNameEmployeeFind.Enabled = False
            cmbDiscountReward_Type.Enabled = False
            cmbViewSalaryPaymentSystem.Enabled = False
            cmbViewSalaryPaymentSystem.SelectedIndex = -1
            cmbNameEmployeeFind.SelectedIndex = -1
            cmbDiscountReward_Type.SelectedIndex = -1
        ElseIf chkAll.Checked = False Then
            cmbNameEmployeeFind.Enabled = True
            cmbDiscountReward_Type.Enabled = True
            cmbViewSalaryPaymentSystem.Enabled = True
            cmbViewSalaryPaymentSystem.SelectedIndex = -1
            cmbNameEmployeeFind.SelectedIndex = -1
            cmbDiscountReward_Type.SelectedIndex = -1
        End If
    End Sub

    Private Sub chkDate_CheckedChanged(sender As Object, e As EventArgs) Handles chkDate.CheckedChanged
        If chkDate.Checked = True Then
            dtpFrom.Enabled = False
            dtpTo.Enabled = False
        Else
            dtpFrom.Enabled = True
            dtpTo.Enabled = True
        End If
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Panel_Search.Dock = DockStyle.None
        Panel_Search.Top = 5000

    End Sub

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        Dim Move_Number, XDate As String
        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Move_Number = DataGridView1.SelectedRows(i).Cells(1).Value

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete  from EmployeesDiscountReward where Move_Number =N'" & Move_Number & "'" : cmd.ExecuteNonQuery()

            XDate = DataGridView1.SelectedRows(i).Cells(3).Value
            Get_Movement_In_Out_Money(XDate, Treasury_Code)
        Next

        HeaderSearch()
        CLEAR_ALL()
        MAXRECORD("EmployeesDiscountReward", "Move_Number")
        cmbNameEmployee.Focus()
    End Sub

    Private Sub txtCode_TextChanged(sender As Object, e As EventArgs) Handles txtCode.TextChanged
        MyVars.CheckNumber(txtCode)
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class