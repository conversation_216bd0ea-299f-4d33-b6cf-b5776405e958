﻿Imports VB = Microsoft.VisualBasic
Imports CrystalDecisions.CrystalReports.Engine
Public Class FrmShowDiscCustomers
    Dim WithEvents BS As New BindingSource

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        FillData()
    End Sub

    Private Sub FillData()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.<PERSON>()
        If ChkAll.Checked = True Then
            S = "SELECT id as [رقم],Vendorname AS [اسم العميل],amnt AS [المبلغ], pdate AS [التاريخ],TIN_NO as [رقم الفاتورة],det as الملاحظات FROM Vst_disc"
        Else
            S = "SELECT id as [رقم],Vendorname AS [اسم العميل],amnt AS [المبلغ], pdate AS [التاريخ],TIN_NO as [رقم الفاتورة],det as الملاحظات FROM Vst_disc where Vendorname =N'" & CmbVendorname.Text & "'"
        End If
        If ChkNowDate.Checked = False Then
            If VB.Right(S, 4) = "disc" Then
                S = S & " where pdate >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and pdate <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
                S = S & " and pdate >=N'" & Cls.C_date(DateTimePicker1.Text) & "' and pdate <=N'" & Cls.C_date(DateTimePicker2.Text) & "'"
            End If
        End If
        If FilterSelect = "Number" Then
            S = S & " order by [رقم]"
        End If
        If FilterSelect = "Date" Then
            S = S & " order by [التاريخ]"
        End If
        If FilterSelect = "Name" Then
            S = S & " order by [اسم العميل]"
        End If

        cmd.CommandText = S
        dr = cmd.ExecuteReader
        DataGridView1.DataSource = Cls.PopulateDataView(dr)


        Dim SM As String
        For i As Integer = 0 To DataGridView1.RowCount - 1
            SM = Val(DataGridView1.Rows(i).Cells(3).Value)
            SM = Cls.R_date(SM)
            DataGridView1.Rows(i).Cells(3).Value = SM
        Next
        DataGridView1.Columns(0).Width = 0
        SumDGV3()
    End Sub

    Private Sub FrmShowDiscCustomers_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If NotUploadingCustomerDataToTheSalesScreen = "NO" Then
            Cls.fill_combo("Customers", "Vendorname", CmbVendorname)

        End If
        CmbVendorname.Items.Add("نقدا")
        CmbVendorname.Text = "نقدا"
        SumDGV3()
    End Sub

    Private Sub ChkAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkAll.CheckedChanged
        If ChkAll.Checked = True Then
            CmbVendorname.SelectedIndex = -1
            CmbVendorname.Enabled = False
        Else
            CmbVendorname.Enabled = True
        End If
    End Sub

    Private Sub ChkNowDate_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkNowDate.CheckedChanged
        If ChkNowDate.Checked = True Then
            DateTimePicker1.Enabled = False
            DateTimePicker2.Enabled = False
        Else
            DateTimePicker1.Enabled = True
            DateTimePicker2.Enabled = True
        End If
    End Sub

    Private Sub Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button3.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Dim M As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If M = vbCancel Then Exit Sub

        For i As Integer = 0 To DataGridView1.SelectedRows.Count - 1
            If DataGridView1.RowCount = 0 Then Beep() : Exit Sub
            If (DataGridView1.SelectedRows.Count) = 0 Then Beep() : Exit Sub
            Dim ItmID As String
            ItmID = DataGridView1.SelectedRows(i).Cells(0).Value

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "delete from Vst_disc where id =N'" & ItmID & "'" : cmd.ExecuteNonQuery()
        Next
        FillData()

    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If DataGridView1.Rows.Count = 0 Then
            MsgBox("لاتوجد بيانات متوفرة للطباعة", MsgBoxStyle.Exclamation, "طباعة")
            Exit Sub
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases(Company_Branch_ID,Vendorname,qu,bill_date,BILL_NO,det,totalprice)  values("
            S = S & "N'" & Company_Branch_ID & "',N'" & DataGridView1.Rows(i).Cells(1).Value & "',N'" & DataGridView1.Rows(i).Cells(2).Value & "',N'" & DataGridView1.Rows(i).Cells(3).Value & "',N'" & DataGridView1.Rows(i).Cells(4).Value & "',N'" & DataGridView1.Rows(i).Cells(5).Value & "',N'" & txt_ValTinx.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New Rpt_DiscCtm
        Dim txt, txtname, txtNameAr, txtNameEn As TextObject

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txt = rpt.Section1.ReportObjects("Text10")
        txt.Text = "تقرير بخصومات العملاء"
        txtname = rpt.Section1.ReportObjects("Text12")
        txtname.Text = "أسم العميل"
        txtNameAr = rpt.Section1.ReportObjects("Text1")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("Text2")
        txtNameEn.Text = NameEnCompany
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "تقرير بخصومات العملاء"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Sub InsertForPrint(ByVal Vendorname As String, ByVal qu As String, ByVal bill_date As String, ByVal BILL_NO As String, ByVal det As String, ByVal totalprice As String)

        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "SP_Print_DiscCtm"
        cmd.Parameters.Clear()

        cmd.Parameters.AddWithValue("@Vendorname", Vendorname)
        cmd.Parameters.AddWithValue("@qu ", qu)
        cmd.Parameters.AddWithValue("@bill_date", bill_date)
        cmd.Parameters.AddWithValue("@BILL_NO", BILL_NO)
        cmd.Parameters.AddWithValue("@det", det)
        cmd.Parameters.AddWithValue("@totalprice", totalprice)

        cmd.ExecuteNonQuery()

    End Sub

    Private Sub SumDGV3()

        Dim SM As Double
        For i As Integer = 0 To DataGridView1.Rows.Count - 1
            SM = SM + DataGridView1.Rows(i).Cells(2).Value
        Next
        txt_ValTinx.Text = SM

    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        FrmFilter.ShowDialog()
    End Sub
End Class
