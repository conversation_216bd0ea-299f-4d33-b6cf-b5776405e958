﻿Imports CrystalDecisions.CrystalReports.Engine

Public Class Frm_Product_Dismissal_Notice_Factory
    Dim RNXD As Integer
    Dim billnoID As String
    Dim TotalPriceBeforeAverage As Double
    Dim aray_itm_id As New ArrayList
    Dim aray_Stores As New ArrayList
    Dim aray_qu As New ArrayList
    Dim aray_TinPrice As New ArrayList

    Private Sub Frm_Product_Dismissal_Notice_Factory_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        Cls.fill_combo_Branch("stores", "store", cmbStoresFrom)
        Cls.fill_combo_Branch("stores", "store", cmbStoresTo)
        Cls.fill_combo_Branch("Employees", "NameEmployee", cmbEmployees)
        'txtsearsh.Text = " بحث ...." : txtsearsh.Font = New Font(txtsearsh.Font, FontStyle.Italic)
        'Headerx()
        MAXRECORD()
        cmbEmployees.Focus()
    End Sub

    Private Sub MAXRECORD()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from ManufacturingDismissalNotice"
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            Me.txtbill_no.Text = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(bill_no As float)) as mb FROM ManufacturingDismissalNotice where bill_no <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            Me.txtbill_no.Text = sh + 1
        End If
    End Sub

    Private Sub txtqunt_TextChanged(sender As Object, e As EventArgs) Handles txtqunt.TextChanged
        MyVars.CheckNumber(txtqunt)
    End Sub

    Private Sub cmbCatsManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbCatsManufacturing.SelectedIndexChanged
        If cmbCatsManufacturing.Text.Trim = "" Then Exit Sub
        cmbItemsManufacturing.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select sname from Items where group_name =N'" & cmbCatsManufacturing.Text & "' and Stores =N'" & cmbStoresTo.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbItemsManufacturing.Items.Add(Trim(dr(0)))
        Loop
        cmbItemsManufacturing.Text = ""
    End Sub

    Private Sub btnAdd_Click(sender As Object, e As EventArgs) Handles btnAdd.Click
        If ValidateTextAdd() = False Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id as [الباركود],group_name as [المجموعة],SnameManufacturing as [المنتج المصنع] ,sname as [الخامات],TinPrice as [سعر التكلفة] ,QU_Manufacturing as [الكمية المصنعة],store as [الكمية الحالية],QU_Rest as [الكمية المتبقية],CostPrice as [أجمالى التكلفة],Stores as [مخزن المصنع],Stores_Manufacturing as [مخزن المنتج التام],itm_Unity as [وحدة القياس] from View_Product_Manufacturing where SnameManufacturing =N'" & cmbItemsManufacturing.Text & "'  order by 1"
        dr = cmd.ExecuteReader
        DTGV.DataSource = Cls.PopulateDataView(dr)
        DTGV.Columns(0).Visible = False
        DTGV.Columns(11).Visible = False
        DTGV.Columns(2).Width = 190
        DTGV.Columns(4).Width = 190

        Dim store, qu_unity As Double
        Dim CostPrice, Weight, Active As Long
        CostPrice = 0 : Weight = 0 = Active = 0

        For i As Integer = 0 To DTGV.RowCount - 1
            DTGV.Rows(i).Cells(9).Value = cmbStoresFrom.Text

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select store from Items where itm_id=N'" & DTGV.Rows(i).Cells(0).Value & "'  and Stores=N'" & DTGV.Rows(i).Cells(9).Value & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : store = dr("store").ToString() : End If
            DTGV.Rows(i).Cells(6).Value = Val(store)

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select qu_unity from View_Product_Manufacturing where SnameManufacturing=N'" & DTGV.Rows(i).Cells(2).Value & "' and sname=N'" & DTGV.Rows(i).Cells(3).Value & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then : qu_unity = dr("qu_unity").ToString() : End If
            DTGV.Rows(i).Cells(5).Value = Val(qu_unity) * Val(txtqunt.Text)

            DTGV.Rows(i).Cells(7).Value = Val(store) - Val(DTGV.Rows(i).Cells(5).Value)

            DTGV.Rows(i).Cells(8).Value = Val(DTGV.Rows(i).Cells(4).Value) * Val(DTGV.Rows(i).Cells(5).Value)

            CostPrice += Val(DTGV.Rows(i).Cells(8).Value)
            Weight += Val(DTGV.Rows(i).Cells(4).Value)

            If DTGV.Rows(i).Cells(7).Value < 0 Then
                DTGV.Rows(i).DefaultCellStyle.BackColor = Color.Red
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.White
                Active += 1
            Else
                DTGV.Rows(i).DefaultCellStyle.BackColor = Color.White
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Black
            End If
        Next
        If Active > 0 Then
            MsgBox("عفواا الكمية لاتكفى لعملية التحويل من المصنع", MsgBoxStyle.Exclamation)
        End If

        Dim TotalManufacturing As Double = Val(txtManufacturing_Allowance.Text) + Val(txtFilling_Allowance.Text)
        txtFillingManufacturing.Text = Val(TotalManufacturing) * Val(txtqunt.Text)
        txtCostPrice.Text = Val(CostPrice.ToString()) + Val(txtFillingManufacturing.Text)
        txtWeight.Text = Weight.ToString()




        'If ValidateTextAdd() = False Then Exit Sub

        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'cmd.CommandText = "select itm_id as [الباركود],group_name as [المجموعة],SnameManufacturing as [المنتج المصنع] ,sname as [الخامات],TinPrice as [سعر التكلفة] ,QU_Manufacturing as [الكمية المصنعة بالقطعة],QU_Unity_Manufacturing as [الكمية المصنعة بالكرتونة],store as [الكمية الحالية],QU_Rest as [الكمية المتبقية],CostPrice as [أجمالى التكلفة],Stores as [مخزن المصنع],Stores_Manufacturing as [مخزن المنتج التام],itm_Unity as [وحدة القياس] from View_Product_Manufacturing where SnameManufacturing =N'" & cmbItemsManufacturing.Text & "'  order by 1"
        'dr = cmd.ExecuteReader
        'DTGV.DataSource = Cls.PopulateDataView(dr)
        'DTGV.Columns(0).Visible = False
        'DTGV.Columns(12).Visible = False
        'DTGV.Columns(6).Visible = True
        'DTGV.Columns(2).Width = 190
        'DTGV.Columns(4).Width = 190

        'If NumberPieces = 1 Then
        '    DTGV.Columns(6).Visible = False
        'End If

        'Dim store, qu_unity, qu As Double
        'Dim CostPrice, Weight, Active As Long
        'CostPrice = 0 : Weight = 0 = Active = 0

        'If NotUnityItemsProgram = "YES" Then
        '    NumberPieces = Cls.Get_Code_Value_Branch_More("ItemsUnity", "NumberPieces", "itm_id=N'" & txtItm_idMaterial.Text & "' and Unity_Name=N'" & cmbUnity.Text & "'")
        'End If


        'For i As Integer = 0 To DTGV.RowCount - 1
        '    DTGV.Rows(i).Cells(10).Value = cmbStoresFrom.Text

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "select store from Items where itm_id=N'" & DTGV.Rows(i).Cells(0).Value & "'  and Stores=N'" & DTGV.Rows(i).Cells(10).Value & "'" : dr = cmd.ExecuteReader : dr.Read()
        '    If dr.HasRows = True Then : store = dr("store").ToString() : End If
        '    DTGV.Rows(i).Cells(7).Value = Val(store)

        '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '    cmd.CommandText = "select qu_unity,qu from View_Product_Manufacturing where SnameManufacturing=N'" & DTGV.Rows(i).Cells(2).Value & "' and sname=N'" & DTGV.Rows(i).Cells(3).Value & "'" : dr = cmd.ExecuteReader : dr.Read()
        '    If dr.HasRows = True Then : qu_unity = dr("qu_unity").ToString() : qu = dr("qu").ToString() : End If
        '    DTGV.Rows(i).Cells(5).Value = Val(txtqunt.Text)
        '    DTGV.Rows(i).Cells(6).Value = Val(txtquntUnity.Text)
        '    'If NotUnityItemsProgram = "YES" Then
        '    '    If NumberPieces <> 1 Then
        '    '        DTGV.Rows(i).Cells(5).Value = Val(txtqunt.Text)
        '    '        DTGV.Rows(i).Cells(6).Value = Val(txtquntUnity.Text)
        '    '    Else
        '    '        DTGV.Rows(i).Cells(5).Value = Val(txtqunt.Text)
        '    '        DTGV.Rows(i).Cells(6).Value = Val(txtquntUnity.Text)
        '    '    End If
        '    'Else
        '    '    DTGV.Rows(i).Cells(5).Value = Val(qu_unity) * Val(txtqunt.Text)
        '    '    DTGV.Rows(i).Cells(6).Value = Val(qu_unity) * Val(txtqunt.Text)
        '    'End If

        '    DTGV.Rows(i).Cells(8).Value = Val(store) - Val(DTGV.Rows(i).Cells(5).Value)

        '    DTGV.Rows(i).Cells(9).Value = Val(DTGV.Rows(i).Cells(4).Value) * Val(DTGV.Rows(i).Cells(5).Value)

        '    CostPrice += Val(DTGV.Rows(i).Cells(9).Value)
        '    Weight += Val(DTGV.Rows(i).Cells(4).Value)

        '    If DTGV.Rows(i).Cells(7).Value < 0 Then
        '        DTGV.Rows(i).DefaultCellStyle.BackColor = Color.Red
        '        DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.White
        '        Active += 1
        '    Else
        '        DTGV.Rows(i).DefaultCellStyle.BackColor = Color.White
        '        DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Black
        '    End If
        'Next
        'If Active > 0 Then
        '    MsgBox("عفواا الكمية لاتكفى لعملية التحويل من المصنع", MsgBoxStyle.Exclamation)
        'End If

        'Dim TotalManufacturing As Double = Val(txtManufacturing_Allowance.Text) + Val(txtFilling_Allowance.Text)
        'txtFillingManufacturing.Text = Val(TotalManufacturing) * Val(txtquntUnity.Text)
        'txtCostPrice.Text = Val(CostPrice.ToString()) + Val(txtFillingManufacturing.Text)
        'txtWeight.Text = Weight.ToString()
    End Sub

    Function ValidateTextAdd() As Boolean
        If txtbill_no.Text = "" Then MsgBox("فضلا أدخل رقم أذن الصرف", MsgBoxStyle.Exclamation) : txtbill_no.Focus() : Return False
        If cmbStoresFrom.Text = "" Then MsgBox("فضلا أختر من مخزن المصنع", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If cmbStoresTo.Text.Trim = "" Then MsgBox("فضلا أختر الى مخزن المنتج التام", MsgBoxStyle.Exclamation) : cmbStoresTo.Focus() : Return False
        If cmbCatsManufacturing.Text = "" Then MsgBox("فضلا أختر مجموعة الصنف", MsgBoxStyle.Exclamation) : cmbCatsManufacturing.Focus() : Return False
        If cmbItemsManufacturing.Text = "" Then MsgBox("فضلا أختر أسم المنتج المصنع", MsgBoxStyle.Exclamation) : cmbItemsManufacturing.Focus() : Return False
        If txtqunt.Text = "" Then MsgBox("فضلا أدخل الكمية المصنعة", MsgBoxStyle.Exclamation) : txtqunt.Focus() : Return False

        Return True
    End Function

    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        ClearSave()
        MAXRECORD()
    End Sub

    Private Sub ClearSave()
        cmbCatsManufacturing.Text = ""
        cmbItemsManufacturing.Text = ""
        cmbStoresFrom.Text = ""
        cmbStoresTo.Text = ""
        cmbEmployees.Text = ""
        txtbill_no.Text = ""
        txtqunt.Text = ""
        txtWeight.Text = ""
        txtCostPrice.Text = ""
        txtManufacturing_Allowance.Text = ""
        txtFillingManufacturing.Text = ""
        DTGV.DataSource = ""
    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs) Handles BtnDelete.Click
        If DTGV.RowCount = 0 Then Beep() : Exit Sub
        If (DTGV.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        For i As Integer = 0 To DTGV.SelectedRows.Count - 1
            RNXD = DTGV.CurrentRow.Index
            DTGV.Rows.RemoveAt(RNXD)
        Next
    End Sub

    Private Sub btnSaveAll_Click(sender As Object, e As EventArgs) Handles btnSaveAll.Click

        If NetworkName = "Yes" Then
            If UseExternalServer = "Yes" Then
                connect()
            End If
        End If
        If ValidateSave() = False Then Exit Sub

        Dim Active As Long
        For i As Integer = 0 To DTGV.RowCount - 1
            If DTGV.Rows(i).Cells(7).Value < 0 Then
                DTGV.Rows(i).DefaultCellStyle.BackColor = Color.Red
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.White
                Active += 1
            Else
                DTGV.Rows(i).DefaultCellStyle.BackColor = Color.White
                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Black
            End If
        Next
        If Active > 0 Then
            MsgBox("عفواا الكمية لاتكفى لعملية التحويل من المصنع", MsgBoxStyle.Exclamation)
            Exit Sub
        End If

        '=======================================================================================================================================================
        ' مشتريات تصنيع

        Dim itm_id_Manufacturing As String = ""
        Dim Unity As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id_Manufacturing,CostPrice from View_Product_Manufacturing_Show where sname=N'" & cmbItemsManufacturing.Text & "'  and Stores_Manufacturing=N'" & cmbStoresTo.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            itm_id_Manufacturing = dr("itm_id_Manufacturing").ToString()
            TinPrice = dr("CostPrice").ToString()
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Unity_Name from ItemsUnity where itm_id=N'" & itm_id_Manufacturing & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            Unity = dr("Unity_Name")
        End If


        Dim EMPID As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select EMPID from Employees where  NameEmployee = N'" & cmbEmployees.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then : EMPID = dr("EMPID") : Else EMPID = 0 : End If


        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into ManufacturingDismissalNotice (bill_no,bill_date,itm_id_Manufacturing,Stores_From,Stores_TO,qunt_Manufacturing,TotalCostPrice,EMPID,UserName)  values("
        S = S & "N'" & txtbill_no.Text.Trim & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & itm_id_Manufacturing & "',N'" & cmbStoresFrom.Text & "',N'" & cmbStoresTo.Text & "',N'" & txtqunt.Text & "',N'" & txtCostPrice.Text & "',N'" & EMPID & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()


        Dim CostPrice As String = ""
        Dim TotalCostPrice As Double
        Dim TypeCurrency As String = mykey.GetValue("TypeCurrency", "جنية مصرى")

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select CostPrice from View_Product_Manufacturing_Show where sname=N'" & cmbItemsManufacturing.Text & "'  and Stores_Manufacturing=N'" & cmbStoresTo.Text & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            CostPrice = dr("CostPrice")
        Else
            GoTo 1
        End If
        TotalCostPrice = Val(CostPrice) * Val(txtqunt.Text)
1:

        TotalCostPrice = Val(CostPrice) * Val(txtqunt.Text)
        PriceTinAverage(itm_id_Manufacturing, cmbStoresTo.Text, "قطعة", txtqunt.Text, TotalCostPrice)

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into Manufacturing_BilltINData (bill_noNotice,itm_id,price,qu,qu_unity,itm_Unity,totalprice,Stores,username,bill_date,TinPriceAverage)  values (N'" & txtbill_no.Text & "',N'" & itm_id_Manufacturing & "',N'" & TinPrice & "',N'" & txtqunt.Text & "',N'" & txtqunt.Text & "',N'" & Unity & "',N'" & TotalCostPrice & "',N'" & cmbStoresTo.Text & "',N'" & UserName & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & Val(TotalPriceBeforeAverage.ToString) & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()
2:

        IM.Store(itm_id_Manufacturing, cmbStoresTo.Text)

        If ConnectOnlineStore = "YES" Then
            EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", itm_id_Manufacturing)
            StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", itm_id_Manufacturing)
            Cos.UpdateProductStock(StockOnline, itm_id_Manufacturing, EditItmId)
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbill_no.Text & "' and itm_id = N'" & itm_id_Manufacturing & "' and Stores =N'" & cmbStoresTo.Text & "'" : cmd.ExecuteNonQuery()


        '=======================================================================================================================================================
        ' مبيعات تصنيع
        Dim ActivationEmail As String = mykey.GetValue("ActivationEmail", "NO")
        Dim Xstore As Double
        For i As Integer = 0 To DTGV.Rows.Count - 1
            If ActivationEmail = "YES" Then
                Xstore = IM.Get_Itm_Store(DTGV.Rows(i).Cells(0).Value, DTGV.Rows(i).Cells(9).Value.Trim)
                If Xstore < 1 Then
                    SendEmail("الكمية بالمخزن قد نفذت", DTGV.Rows(i).Cells(0).Value, DTGV.Rows(i).Cells(2).Value, Xstore)
                End If
                If Xstore - Val(txtqunt.Text) < IM.Get_Itm_Range(DTGV.Rows(i).Cells(0).Value, DTGV.Rows(i).Cells(9).Value) Then
                    SendEmail("الكمية وصلت للحد الادنى", DTGV.Rows(i).Cells(0).Value, DTGV.Rows(i).Cells(2).Value, Xstore)
                End If
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into Manufacturing_BillsalData(bill_noNotice,itm_id,qu,qu_unity,price,totalprice,Stores,itm_Unity,bill_date,UserName)  values("
            S = S & "N'" & txtbill_no.Text.Trim & "',N'" & DTGV.Rows(i).Cells(0).Value & "',N'" & DTGV.Rows(i).Cells(5).Value & "',N'" & DTGV.Rows(i).Cells(5).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & Val(DTGV.Rows(i).Cells(5).Value) * Val(DTGV.Rows(i).Cells(4).Value) & "',N'" & DTGV.Rows(i).Cells(9).Value & "',N'" & DTGV.Rows(i).Cells(11).Value & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & UserName & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()

            IM.Store(DTGV.Rows(i).Cells(0).Value, DTGV.Rows(i).Cells(9).Value)

            If ConnectOnlineStore = "YES" Then
                EditItmId = Cls.Get_Code_Value("Items", "sname", "itm_id", DTGV.Rows(i).Cells(0).Value)
                StockOnline = Cls.Get_Code_Value("Items", "store", "itm_id", DTGV.Rows(i).Cells(0).Value)
                Cos.UpdateProductStock(StockOnline, DTGV.Rows(i).Cells(0).Value, EditItmId)
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbill_no.Text & "' and itm_id = N'" & DTGV.Rows(i).Cells(0).Value & "' and Stores =N'" & DTGV.Rows(i).Cells(9).Value & "'" : cmd.ExecuteNonQuery()

        Next

        Get_Movement_In_Out_Money(dtpDateItem.Text, Treasury_Code)

        If chkprint.Checked = True Then
            PrintReport()
        End If


        MsgBox("تمت عملية التصنيع وأضافة المنتج المصنع بنجاح", MsgBoxStyle.Information)

        ClearSave()

        MAXRECORD()







        '        If NetworkName = "Yes" Then
        '            If UseExternalServer = "Yes" Then
        '                connect()
        '            End If
        '        End If
        '        If ValidateSave() = False Then Exit Sub

        '        Dim Active As Long
        '        For i As Integer = 0 To DTGV.RowCount - 1
        '            If DTGV.Rows(i).Cells(7).Value < 0 Then
        '                DTGV.Rows(i).DefaultCellStyle.BackColor = Color.Red
        '                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.White
        '                Active += 1
        '            Else
        '                DTGV.Rows(i).DefaultCellStyle.BackColor = Color.White
        '                DTGV.Rows(i).DefaultCellStyle.ForeColor = Color.Black
        '            End If
        '        Next
        '        If Active > 0 Then
        '            MsgBox("عفواا الكمية لاتكفى لعملية التحويل من المصنع", MsgBoxStyle.Exclamation)
        '            Exit Sub
        '        End If

        '        '=======================================================================================================================================================
        '        ' مشتريات تصنيع

        '        Dim itm_id_Manufacturing As String = ""
        '        Dim Unity As String = ""
        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        cmd.CommandText = "select itm_id_Manufacturing,CostPrice from View_Product_Manufacturing_Show where sname=N'" & cmbItemsManufacturing.Text & "'  and Stores_Manufacturing=N'" & cmbStoresTo.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        '        If dr.HasRows = True Then
        '            itm_id_Manufacturing = dr("itm_id_Manufacturing").ToString()
        '            TinPrice = dr("CostPrice").ToString()
        '        End If

        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        cmd.CommandText = "select Unity_Name from ItemsUnity where itm_id=N'" & itm_id_Manufacturing & "'" : dr = cmd.ExecuteReader : dr.Read()
        '        If dr.HasRows = True Then
        '            Unity = dr("Unity_Name")
        '        End If


        '        Dim EMPID As String = ""
        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        cmd.CommandText = "select EMPID from Employees where  NameEmployee = N'" & cmbEmployees.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        '        If dr.HasRows = True Then : EMPID = dr("EMPID") : Else EMPID = 0 : End If


        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        S = "insert into ManufacturingDismissalNotice (bill_no,bill_date,itm_id_Manufacturing,Stores_From,Stores_TO,qunt_Manufacturing,TotalCostPrice,EMPID,UserName)  values("
        '        S = S & "N'" & txtbill_no.Text.Trim & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & itm_id_Manufacturing & "',N'" & cmbStoresFrom.Text & "',N'" & cmbStoresTo.Text & "',N'" & txtqunt.Text & "',N'" & txtCostPrice.Text & "',N'" & EMPID & "',N'" & UserName & "')"
        '        cmd.CommandText = S : cmd.ExecuteNonQuery()


        '        Dim CostPrice As String = ""
        '        Dim TotalCostPrice As Double
        '        Dim TypeCurrency As String = mykey.GetValue("TypeCurrency", "جنية مصرى")

        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        cmd.CommandText = "select CostPrice from View_Product_Manufacturing_Show where sname=N'" & cmbItemsManufacturing.Text & "'  and Stores_Manufacturing=N'" & cmbStoresTo.Text & "'"
        '        dr = cmd.ExecuteReader : dr.Read()
        '        If dr.HasRows = True Then
        '            CostPrice = dr("CostPrice")
        '        Else
        '            GoTo 1
        '        End If
        '        TotalCostPrice = Val(CostPrice) * Val(txtqunt.Text)
        '1:

        '        TotalCostPrice = Val(CostPrice) * Val(txtqunt.Text)
        '        PriceTinAverage(itm_id_Manufacturing, cmbStoresTo.Text, "قطعة", txtqunt.Text, TotalCostPrice)

        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        S = "insert into Manufacturing_BilltINData (bill_noNotice,itm_id,price,qu,qu_unity,itm_Unity,totalprice,Stores,username,bill_date,TinPriceAverage)  values (N'" & txtbill_no.Text & "',N'" & itm_id_Manufacturing & "',N'" & TinPrice & "',N'" & txtqunt.Text & "',N'" & txtquntUnity.Text & "',N'" & Unity & "',N'" & TotalCostPrice & "',N'" & cmbStoresTo.Text & "',N'" & UserName & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & Val(TotalPriceBeforeAverage.ToString) & "')"
        '        cmd.CommandText = S : cmd.ExecuteNonQuery()
        '2:

        '        IM.Store(itm_id_Manufacturing, cmbStoresTo.Text)

        '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '        cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbill_no.Text & "' and itm_id = N'" & itm_id_Manufacturing & "' and Stores =N'" & cmbStoresTo.Text & "'" : cmd.ExecuteNonQuery()


        '        '=======================================================================================================================================================
        '        ' مبيعات تصنيع
        '        Dim ActivationEmail As String = mykey.GetValue("ActivationEmail", "NO")
        '        Dim Xstore As Double
        '        For i As Integer = 0 To DTGV.Rows.Count - 1
        '            If ActivationEmail = "YES" Then
        '                Xstore = IM.Get_Itm_Store(DTGV.Rows(i).Cells(0).Value, DTGV.Rows(i).Cells(10).Value.Trim)
        '                If Xstore < 1 Then
        '                    SendEmail("الكمية بالمخزن قد نفذت", DTGV.Rows(i).Cells(0).Value, DTGV.Rows(i).Cells(2).Value, Xstore)
        '                End If
        '                If Xstore - Val(txtqunt.Text) < IM.Get_Itm_Range(DTGV.Rows(i).Cells(0).Value, DTGV.Rows(i).Cells(10).Value) Then
        '                    SendEmail("الكمية وصلت للحد الادنى", DTGV.Rows(i).Cells(0).Value, DTGV.Rows(i).Cells(2).Value, Xstore)
        '                End If
        '            End If

        '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '            S = "insert into Manufacturing_BillsalData(bill_noNotice,itm_id,qu,qu_unity,price,totalprice,Stores,itm_Unity,bill_date,UserName)  values("
        '            S = S & "N'" & txtbill_no.Text.Trim & "',N'" & DTGV.Rows(i).Cells(0).Value & "',N'" & DTGV.Rows(i).Cells(5).Value & "',N'" & DTGV.Rows(i).Cells(6).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & Val(DTGV.Rows(i).Cells(5).Value) * Val(DTGV.Rows(i).Cells(4).Value) & "',N'" & DTGV.Rows(i).Cells(10).Value & "',N'" & DTGV.Rows(i).Cells(12).Value & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & UserName & "')"
        '            cmd.CommandText = S : cmd.ExecuteNonQuery()

        '            IM.Store(DTGV.Rows(i).Cells(0).Value, DTGV.Rows(i).Cells(10).Value)

        '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        '            cmd.CommandText = "update BilltINData set CurrentStock = " & Val(CurrentStock) & ",CurrentStockTotal = " & Val(CurrentStockTotal) & " where bill_No =N'" & txtbill_no.Text & "' and itm_id = N'" & DTGV.Rows(i).Cells(0).Value & "' and Stores =N'" & DTGV.Rows(i).Cells(9).Value & "'" : cmd.ExecuteNonQuery()

        '        Next

        '        Get_Movement_In_Out_Money(dtpDateItem.Text)

        '        If chkprint.Checked = True Then
        '            PrintReport()
        '        End If


        '        MsgBox("تمت عملية التصنيع وأضافة المنتج المصنع بنجاح", MsgBoxStyle.Information)

        '        ClearSave()

        '        MAXRECORD()

    End Sub

    Private Sub MAXRECORDAuto(ByVal Tabel As String, ByVal Feild As String)
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " + Tabel + ""
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            billnoID = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" + Feild + " As float)) as mb FROM " + Tabel + " where " + Feild + " <> N'جرد'"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            billnoID = sh + 1
        End If

    End Sub

    Private Sub PriceTinAverage(ByVal Parcode As String, ByVal Stores As String, ByVal Unity As String, ByVal qunt As Double, ByVal TotalPrice As Double)
        Dim StoreItems, TotalPriceTinAverage, BalanceBeforeBuying, TotalBalanceBeforeBuying As Double
        '================================================ المخزون الحالى =====================================================
        Dim Xqunt As Double = qunt

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select store,TinPriceAverage from Items where itm_id =N'" & Parcode & "' and Stores =N'" & Stores & "'"
        dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            StoreItems = dr("store").ToString()
            Dim xx As String = dr("TinPriceAverage").ToString()
            If xx = "" Then
                TotalPriceTinAverage = 0
            Else
                TotalPriceTinAverage = xx
            End If
        End If
        If NotUnityItemsProgram = "YES" Then
            NumberPieces = Cls.Get_Code_Value_Branch_More("ItemsUnity", "NumberPieces", "itm_id=N'" & Parcode & "' and Unity_Name=N'" & Unity & "'")
            If NumberPieces <> 1 Then
                Xqunt = Val(NumberPieces) * Val(qunt)
            End If
        End If

        '================================================ متوسط سعر الشراء الجديد =====================================================

        Try
            BalanceBeforeBuying = Val(TotalPriceTinAverage) * Val(StoreItems)

            TotalBalanceBeforeBuying = Val(TotalPrice) + Val(BalanceBeforeBuying)
            TotalBalanceBeforeBuying = Math.Round(TotalBalanceBeforeBuying, 2)

            Dim TotalTotal As Double = Val(StoreItems) + Val(Xqunt)
            If TotalBalanceBeforeBuying = 0 And TotalTotal = 0 Then
                TotalPriceBeforeAverage = 0
            Else
                TotalPriceBeforeAverage = Val(TotalBalanceBeforeBuying) / Val(TotalTotal)
                If TinPriceAverageThreeDigits = "NO" Then
                    TotalPriceBeforeAverage = Math.Round(TotalPriceBeforeAverage, 2)
                Else
                    TotalPriceBeforeAverage = Math.Round(TotalPriceBeforeAverage, 3)
                End If
            End If

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "update items set TinPriceAverage = " & Val(TotalPriceBeforeAverage) & ",TinPrice = " & Val(TinPrice) & " where itm_id =N'" & Parcode & "' and Stores =N'" & Stores & "'" : cmd.ExecuteNonQuery()


        Catch ex As Exception
            ErrorHandling(ex, Me.Text)
        End Try
    End Sub

    Private Sub cmbItemsManufacturing_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbItemsManufacturing.SelectedIndexChanged
        Dim Itm_id As String = ""
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select itm_id_Manufacturing,Manufacturing_Allowance,Filling_Allowance from View_Product_Manufacturing where SnameManufacturing=N'" & cmbItemsManufacturing.Text & "'  and Stores_Manufacturing=N'" & cmbStoresTo.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            txtManufacturing_Allowance.Text = dr("Manufacturing_Allowance").ToString()
            txtFilling_Allowance.Text = dr("Filling_Allowance").ToString()
            txtItm_idMaterial.Text = dr("itm_id_Manufacturing").ToString()
        End If

        GetItemsUnity(cmbUnity, txtItm_idMaterial.Text)

        txtquntUnity.Focus()
    End Sub

    Private Sub cmbStoresTo_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbStoresTo.SelectedIndexChanged
        If cmbStoresTo.Text.Trim = "" Then Exit Sub
        cmbCatsManufacturing.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select distinct group_name from Items where Stores =N'" & cmbStoresTo.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbCatsManufacturing.Items.Add(Trim(dr(0)))
        Loop
        cmbCatsManufacturing.Text = ""
    End Sub

    Function ValidateSave() As Boolean

        If txtbill_no.Text = "" Then MsgBox("فضلا أدخل رقم أذن الصرف", MsgBoxStyle.Exclamation) : txtbill_no.Focus() : Return False
        If cmbStoresFrom.Text = "" Then MsgBox("من فضلك اختر المخزن المحول منه", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If cmbStoresTo.Text = "" Then MsgBox("من فضلك اختر المخزن المحول البه", MsgBoxStyle.Exclamation) : cmbStoresFrom.Focus() : Return False
        If cmbItemsManufacturing.Text = "" Then MsgBox("من فضلك اختر أسم المنتج المصنع", MsgBoxStyle.Exclamation) : cmbItemsManufacturing.Focus() : Return False
        If txtquntUnity.Text = "" Then MsgBox("من فضلك الكمية المصنعة", MsgBoxStyle.Exclamation) : txtquntUnity.Focus() : Return False
        If DTGV.Rows.Count < 1 Then MsgBox("فضلا أكمل بيانات الفاتورة", MsgBoxStyle.Exclamation) : Return False

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select count(*) from ManufacturingDismissalNotice where bill_no =N'" & txtbill_no.Text.Trim & "'" : H = cmd.ExecuteScalar
        If H > 0 Then
            MsgBox("رقم أذن الصرف مسجل مسبقاً", MsgBoxStyle.Exclamation) : txtbill_no.Focus() : Return False
        End If
        Return True
    End Function

    Private Sub PrintReport()
        Dim CostPrice As String = ""
        Dim Manufacturing_Allowance As String = ""
        Dim Filling_Allowance As String = ""
        Dim TotalCostPrice As String = ""
        Dim itm_id_Manufacturing As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from View_Product_Manufacturing where  SnameManufacturing = N'" & cmbItemsManufacturing.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = True Then
            itm_id_Manufacturing = dr("itm_id_Manufacturing")
            CostPrice = dr("TinPrice")
            Manufacturing_Allowance = dr("Manufacturing_Allowance")
            Filling_Allowance = dr("Filling_Allowance")
        End If

        If RunDatabaseInternet = "YES" Then : ConnectNetworkInternet() : End If

        Cls.delete_Branch_All("PrintSalesPurchases")

        For i As Integer = 0 To DTGV.Rows.Count - 1
            TotalCostPrice = Val(DTGV.Rows(i).Cells(5).Value) * Val(DTGV.Rows(i).Cells(6).Value)
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            S = "insert into PrintSalesPurchases(Company_Branch_ID,bill_no,bill_date,itm_id,itm_cat,itm_name,Unity,price,qu,totalpriceafterdisc,store,Stat,totalprice) values"
            S = S & " (N'" & Company_Branch_ID & "',N'" & txtbill_no.Text & "',N'" & Cls.C_date(dtpDateItem.Text) & "',N'" & DTGV.Rows(i).Cells(1).Value & "',N'" & DTGV.Rows(i).Cells(2).Value & "',N'" & DTGV.Rows(i).Cells(3).Value & "',N'" & DTGV.Rows(i).Cells(4).Value & "',N'" & DTGV.Rows(i).Cells(5).Value & "',N'" & DTGV.Rows(i).Cells(6).Value & "',N'" & TotalCostPrice & "',N'" & DTGV.Rows(i).Cells(9).Value & "',N'" & DTGV.Rows(i).Cells(10).Value & "',N'" & txtWeight.Text & "')"
            cmd.CommandText = S : cmd.ExecuteNonQuery()
        Next
        AddReportView()
        Dim rpt As New Rpt_Product_Manufacturing
        Dim txtname, txtNameAr, txtNameEn, sname_Object, CostPrice_Object, Weightqunt_Object, Stores_Manufacturing_Object, TotalCostPrice_Object, Manufacturing_Allowance_Object, Filling_Allowance_Object, txtquntManufacturing, bill_date_Object As TextObject

        Cls.Select_More_Data_Branch_Print("PrintSalesPurchases", "*")
        Dim dt As New DataTable
        dt.Load(dr)
        rpt.SetDataSource(dt)
        txtname = rpt.Section1.ReportObjects("txtReportTitel")
        txtname.Text = "إذن صرف من المصنع"
        txtNameAr = rpt.Section1.ReportObjects("txtTitelNameAr")
        txtNameAr.Text = NameArCompay
        txtNameEn = rpt.Section1.ReportObjects("txtTitelNameEn")
        txtNameEn.Text = NameEnCompany
        sname_Object = rpt.Section1.ReportObjects("txtsname")
        sname_Object.Text = cmbItemsManufacturing.Text
        CostPrice_Object = rpt.Section1.ReportObjects("txtCostPrice")
        CostPrice_Object.Text = CostPrice
        Weightqunt_Object = rpt.Section1.ReportObjects("txtWeightqunt")
        Weightqunt_Object.Text = txtWeight.Text
        Stores_Manufacturing_Object = rpt.Section1.ReportObjects("txtStores")
        Stores_Manufacturing_Object.Text = cmbStoresTo.Text
        TotalCostPrice_Object = rpt.Section1.ReportObjects("txtTotalCostPrice")
        TotalCostPrice_Object.Text = txtCostPrice.Text
        Manufacturing_Allowance_Object = rpt.Section1.ReportObjects("txtManufacturing_Allowance")
        Manufacturing_Allowance_Object.Text = Manufacturing_Allowance
        Filling_Allowance_Object = rpt.Section1.ReportObjects("txtFilling_Allowance")
        Filling_Allowance_Object.Text = Filling_Allowance
        txtquntManufacturing = rpt.Section1.ReportObjects("txtquntManufacturing")
        txtquntManufacturing.Text = txtqunt.Text
        txtquntManufacturing = rpt.Section1.ReportObjects("txttextquntManufacturing")
        txtquntManufacturing.Text = "الكمية المصنعة"
        bill_date_Object = rpt.Section1.ReportObjects("txtbill_date")
        bill_date_Object.Text = dtpDateItem.Text
        If SelectLogoPathOther = "YES" Then
            rpt.SetParameterValue("ImageURL", Application.StartupPath & "\" & CMPLogoPath)
        Else
            rpt.SetParameterValue("ImageURL", CMPLogoPath)
        End If
        Frm_PrintReports.CrystalReportViewer1.ReportSource = rpt
        Frm_PrintReports.Text = "امر شغل تعبئة وتصنيع"
        Frm_PrintReports.Show()

        If RunDatabaseInternet = "YES" Then : connect() : End If
    End Sub

    Private Sub txtbill_no_TextChanged(sender As Object, e As EventArgs) Handles txtbill_no.TextChanged
        MyVars.CheckNumber(txtbill_no)
    End Sub

    Private Sub cmbUnity_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbUnity.SelectedIndexChanged

        If NotUnityItemsProgram = "YES" Then
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select itm_id_Manufacturing,Manufacturing_Allowance,Filling_Allowance from View_Product_Manufacturing where SnameManufacturing=N'" & cmbItemsManufacturing.Text & "'  and Stores_Manufacturing=N'" & cmbStoresTo.Text & "'" : dr = cmd.ExecuteReader : dr.Read()
            If dr.HasRows = True Then
                txtManufacturing_Allowance.Text = dr("Manufacturing_Allowance").ToString()
                txtFilling_Allowance.Text = dr("Filling_Allowance").ToString()
                txtItm_idMaterial.Text = dr("itm_id_Manufacturing").ToString()
            End If

            NumberPieces = Cls.Get_Code_Value_Branch_More("ItemsUnity", "NumberPieces", "itm_id=N'" & txtItm_idMaterial.Text & "' and Unity_Name=N'" & cmbUnity.Text & "'")
            txtFilling_Allowance.Text = Val(NumberPieces) * Val(txtFilling_Allowance.Text)
            txtManufacturing_Allowance.Text = Val(NumberPieces) * Val(txtManufacturing_Allowance.Text)

            If NumberPieces = 0 Or NumberPieces = 1 Then
                txtqunt.Text = Val(txtquntUnity.Text)
            Else
                txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
            End If
        End If
        txtquntUnity.Focus()
    End Sub

    Private Sub txtquntUnity_TextChanged(sender As Object, e As EventArgs) Handles txtquntUnity.TextChanged
        MyVars.CheckNumber(txtquntUnity)

        If NumberPieces = 0 Or NumberPieces = 1 Then
            txtqunt.Text = Val(txtquntUnity.Text)
        Else
            txtqunt.Text = Val(NumberPieces) * Val(txtquntUnity.Text)
        End If
    End Sub
End Class