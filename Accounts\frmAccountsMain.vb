﻿Public Class frmAccountsMain
    Dim MaxRecoedCode As String

    Private Sub btnNew_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnNew.Click
        txtCode.Text = ""
        txtName.Text = ""

        Bra.Fil("Accounts_Main", "Main_Name", cmbFind)

        MAXRECORD("Accounts_Main", "Main_Code")
        txtCode.Text = MaxRecoedCode
        txtName.Focus()
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        If Trim(txtCode.Text) = "" Then MsgBox("من فضلك ادخل الكود", MsgBoxStyle.Exclamation) : Exit Sub
        If Trim(txtName.Text) = "" Then MsgBox("من فضلك ادخل الاسم", MsgBoxStyle.Exclamation) : Exit Sub
        If Cls.Check_Field_Value("Accounts_Main", "Main_Name", txtName.Text) Then MsgBox("عفوا الاسم مسجل من قبل", MsgBoxStyle.Exclamation) : Exit Sub
        If Cls.Check_Field_Value("Accounts_Main", "Main_Code", txtCode.Text) Then MsgBox("عفوا الكود مسجل من قبل", MsgBoxStyle.Exclamation) : Exit Sub

        Cls.insert("Accounts_Main", "Main_Code,Main_Name", "N'" & txtCode.Text & "',N'" & txtName.Text & "'")
        ''MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)

        txtCode.Text = ""
        txtName.Text = ""

        Bra.Fil("Accounts_Main", "Main_Name", cmbFind)

        MAXRECORD("Accounts_Main", "Main_Code")
        txtCode.Text = MaxRecoedCode
        txtName.Focus()
    End Sub

    Private Sub frmAccountsMain_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Bra.Fil("Accounts_Main", "Main_Name", cmbFind)
        MAXRECORD("Accounts_Main", "Main_Code")
        txtCode.Text = MaxRecoedCode
        txtName.Focus()
    End Sub

    Private Sub MAXRECORD(ByVal Tables As String, ByVal Code As String)
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select * from " & Tables & ""
        dr = cmd.ExecuteReader

        Dim dt As New DataTable
        dt.Load(dr)

        If dt.Rows.Count = 0 Then
            MaxRecoedCode = 1
        Else
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandType = CommandType.Text
            cmd.CommandText = "SELECT MAX(CAST(" & Code & " As float)) as mb FROM " & Tables & " where " & Code & " <> N''"
            dr = cmd.ExecuteReader
            dr.Read()

            Dim sh As Long
            sh = dr("mb")
            MaxRecoedCode = sh + 1
        End If

    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If Trim(txtCode.Text) = "" Then MsgBox("من فضلك ادخل الكود", MsgBoxStyle.Exclamation) : Exit Sub
        If Trim(txtName.Text) = "" Then MsgBox("من فضلك ادخل الاسم", MsgBoxStyle.Exclamation) : Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update Accounts_Main set Main_Name =N'" & txtName.Text & "' where Main_Code =N'" & txtCode.Text & "'" : cmd.ExecuteNonQuery()

        MsgBox("تم التعديل بنجاح", MsgBoxStyle.Information)

        txtCode.Text = ""
        txtName.Text = ""

        Bra.Fil("Accounts_Main", "Main_Name", cmbFind)

        MAXRECORD("Accounts_Main", "Main_Code")
        txtCode.Text = MaxRecoedCode
        txtName.Focus()
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If cmbFind.SelectedIndex = -1 Then MsgBox("من فضلك اختر بيان صحيح", MsgBoxStyle.Exclamation) : Exit Sub

        Dim msg As New MsgBoxResult
        msg = (MsgBox("هل تريد بالفعل الحذف", MsgBoxStyle.Question + MsgBoxStyle.YesNo))
        If msg = MsgBoxResult.Yes Then
            Cls.delete("Accounts_Main", "Main_Name=N'" & cmbFind.Text & "'")
            MsgBox("تم الحذف بنجاح", MsgBoxStyle.Information)
        End If

       txtCode.Text = ""
        txtName.Text = ""

        Bra.Fil("Accounts_Main", "Main_Name", cmbFind)

        MAXRECORD("Accounts_Main", "Main_Code")
        txtCode.Text = MaxRecoedCode
        txtName.Focus()
    End Sub

    Private Sub cmbFind_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbFind.SelectedIndexChanged
        If cmbFind.Text = "" Then Exit Sub
        Dim Government_Code As String = ""

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * from Accounts_Main where Main_Name=N'" & cmbFind.Text & "'"
        dr = cmd.ExecuteReader
        If dr.Read Then
            txtCode.Text = dr("Main_Code")
            txtName.Text = dr("Main_Name").ToString()
        End If
    End Sub

    Private Sub txtCode_TextChanged(sender As Object, e As EventArgs) Handles txtCode.TextChanged
        MyVars.CheckNumber(txtCode)
    End Sub
End Class