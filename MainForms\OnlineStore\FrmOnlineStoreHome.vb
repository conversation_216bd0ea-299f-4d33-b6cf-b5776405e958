﻿Public Class FrmOnlineStoreHome
    Private Sub FrmOnlineStoreHome_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub

    Private Sub btnOnlineStoreOrders_Click(sender As Object, e As EventArgs) Handles btnOnlineStoreOrders.Click
        FrmOnlineStoreOrders.ShowDialog()
    End Sub

    Private Sub btnOnlineStoreProduct_Click(sender As Object, e As EventArgs) Handles btnOnlineStoreProduct.Click
        FrmOnlineStoreProduct.ShowDialog()
    End Sub

    Private Sub btnOnlineStorePrivacyPolicy_Click(sender As Object, e As EventArgs) Handles btnOnlineStorePrivacyPolicy.Click
        FrmOnlineStorePrivacyPolicy.ShowDialog()
    End Sub

    Private Sub btnOnlineStoreGovernorate_Click(sender As Object, e As EventArgs) Handles btnOnlineStoreGovernorate.Click
        FrmOnlineStoreGovernorate.ShowDialog()
    End Sub

    Private Sub btnOnlineStoreArea_Click(sender As Object, e As EventArgs) Handles btnOnlineStoreArea.Click
        FrmOnlineStoreArea.ShowDialog()
    End Sub

    Private Sub btnOnlineStoreAboutUs_Click(sender As Object, e As EventArgs) Handles btnOnlineStoreAboutUs.Click
        FrmOnlineStoreAboutUs.ShowDialog()
    End Sub

    Private Sub btnOnlineStoreProductOffers_Click(sender As Object, e As EventArgs) Handles btnOnlineStoreProductOffers.Click
        FrmOnlineStoreProductOffers.ShowDialog()
    End Sub

    Private Sub btnOnlineStoreConnect_Click(sender As Object, e As EventArgs) Handles btnOnlineStoreConnect.Click
        FrmOnlineStoreConnect.ShowDialog()
    End Sub

    Private Sub btnOnlineStoreCategory_Click(sender As Object, e As EventArgs) Handles btnOnlineStoreCategory.Click
        FrmOnlineStoreCategory.ShowDialog()
    End Sub

    Private Sub btnOnlineStoreCompany_Click(sender As Object, e As EventArgs) Handles btnOnlineStoreCompany.Click
        FrmOnlineStoreCompany.ShowDialog()
    End Sub

    Private Sub btnOnlineStoreUser_Click(sender As Object, e As EventArgs) Handles btnOnlineStoreUser.Click
        FrmOnlineStoreUser.ShowDialog()
    End Sub

    Private Sub btnOnlineStoreProductUpdate_Click(sender As Object, e As EventArgs) Handles btnOnlineStoreProductUpdate.Click
        FrmOnlineStoreProductUpdate.ShowDialog()
    End Sub

    Private Sub btnOnlineStoreDataUpdate_Click(sender As Object, e As EventArgs) Handles btnOnlineStoreDataUpdate.Click
        FrmOnlineStoreDataUpdate.ShowDialog()

        If ConnectOnlineStore = "YES" Then
            'If CheckForInternetConnection() = False Then
            '    MsgBox("تأكد من الاتصال بالانترنت", MsgBoxStyle.Information)
            '    Exit Sub
            'End If
            'Dim aray_OrderNo As New ArrayList
            'Dim aray_TotalValueDiscountOrder As New ArrayList
            'Dim aray_Id As New ArrayList
            'Dim aray_ProductId As New ArrayList


            'Dim OrderNo As String
            'Dim TotalValueDiscountOrder As String = ""
            'Dim ProductId As String = ""
            'Dim Id As String = ""
            'Dim DiscountedPrice As String = ""
            'Dim DiscountedPrice2 As String = ""
            'Dim DiscountedPrice3 As String = ""
            'Dim Price As String = ""
            'Dim Price2 As String = ""
            'Dim Price3 As String = ""

            'If Not ConnectingOnlineStore() Is Nothing Then

            '    aray_Id.Clear() : aray_ProductId.Clear()
            '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '    cmd.CommandText = "Select Id,ProductId  From dbo.[OrderItem] Where (DiscountedPrice <> 0)"
            '    dr = cmd.ExecuteReader
            '    Do While dr.Read = True
            '        aray_Id.Add(dr(0))
            '        aray_ProductId.Add(dr(1))
            '    Loop

            '    For i As Integer = 0 To aray_Id.Count - 1
            '        Id = aray_Id(i).ToString()
            '        ProductId = aray_ProductId(i).ToString()

            '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '        cmd.CommandText = "select DiscountedPrice,DiscountedPrice2,DiscountedPrice3,Price,Price2,Price3 From dbo.[Product] Where (Id =N'" & ProductId & "')"
            '        dr = cmd.ExecuteReader : dr.Read()
            '        If dr.HasRows = True Then
            '            DiscountedPrice = dr(0).ToString
            '            DiscountedPrice2 = dr(1).ToString
            '            DiscountedPrice3 = dr(2).ToString
            '            Price = dr(3).ToString
            '            Price2 = dr(4).ToString
            '            Price3 = dr(5).ToString
            '        End If

            '        If Val(DiscountedPrice) = 0 Or Val(DiscountedPrice2) = 0 Or Val(DiscountedPrice3) = 0 Then
            '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '            cmd.CommandText = "update [OrderItem] set DiscountedPrice =N'" & Val(0.00) & "',DiscountedPriceOrder =N'" & Val(0.00) & "',TotalValueDiscountOrder =N'" & Val(0.00) & "' where ProductId =N'" & ProductId & "'" : cmd.ExecuteNonQuery()
            '        End If

            '        If DiscountedPrice = "" Then
            '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '            cmd.CommandText = "update [Product] set DiscountedPrice =N'" & Val(0.00) & "' where Id =N'" & ProductId & "'" : cmd.ExecuteNonQuery()
            '        End If
            '        If DiscountedPrice2 = "" Then
            '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '            cmd.CommandText = "update [Product] set DiscountedPrice2 =N'" & Val(0.00) & "' where Id =N'" & ProductId & "'" : cmd.ExecuteNonQuery()
            '        End If
            '        If DiscountedPrice3 = "" Then
            '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '            cmd.CommandText = "update [Product] set DiscountedPrice3 =N'" & Val(0.00) & "' where Id =N'" & ProductId & "'" : cmd.ExecuteNonQuery()
            '        End If

            '        If Price = "" Then
            '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '            cmd.CommandText = "update [Product] set Price =N'" & Val(0.00) & "' where Id =N'" & ProductId & "'" : cmd.ExecuteNonQuery()
            '        End If
            '        If Price2 = "" Then
            '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '            cmd.CommandText = "update [Product] set Price2 =N'" & Val(0.00) & "' where Id =N'" & ProductId & "'" : cmd.ExecuteNonQuery()
            '        End If
            '        If Price3 = "" Then
            '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '            cmd.CommandText = "update [Product] set Price3 =N'" & Val(0.00) & "' where Id =N'" & ProductId & "'" : cmd.ExecuteNonQuery()
            '        End If

            '    Next


            '    '===========================================================================================
            '    aray_OrderNo.Clear() : aray_TotalValueDiscountOrder.Clear()
            '    If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '    cmd.CommandText = "Select Id,OrderNo,TotalValueDiscountOrder  From dbo.[Order] Where (TotalValueDiscountOrder <> 0)"
            '    dr = cmd.ExecuteReader
            '    Do While dr.Read = True
            '        aray_OrderNo.Add(dr(1))
            '        aray_TotalValueDiscountOrder.Add(dr(2))
            '    Loop

            '    For i As Integer = 0 To aray_OrderNo.Count - 1
            '        OrderNo = aray_OrderNo(i).ToString()
            '        TotalValueDiscountOrder = 0

            '        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '        cmd.CommandText = "Select DiscountedPrice  From dbo.[OrderItem] Where (OrderNo =N'" & OrderNo & "')"
            '        dr = cmd.ExecuteReader
            '        Do While dr.Read = True
            '            TotalValueDiscountOrder += Val(dr(0).ToString)
            '        Loop
            '        If TotalValueDiscountOrder = 0 Then
            '            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            '            cmd.CommandText = "update [Order] set TotalValueDiscountOrder =N'" & Val(0) & "' where OrderNo =N'" & OrderNo & "'" : cmd.ExecuteNonQuery()
            '        End If
            '    Next
            '    '===========================================================================================


            '    Cn.Close()
            '    connect()

            '    MsgBox("تم تحديث البيانات بنجاح", MsgBoxStyle.Information)
            'Else
            '    Cn.Close()
            '    connect()
            '    MsgBox(Cls_Constant.ErrMsg)
            'End If

        End If

    End Sub
End Class