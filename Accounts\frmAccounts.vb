﻿Imports System.Windows.Forms.Form
Imports System.Data
Imports System.Data.SqlClient

Public Class frmAccounts
    Dim WithEvents BS As New BindingSource
    Public SqlConnection1 As SqlClient.SqlConnection = New SqlClient.SqlConnection
    Dim ActionGrid As Boolean = False

 Private Sub ADDBUTTON_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ADDBUTTON.Click
        CLEAR_ALL()
        txtNumberAccount.Focus()
    End Sub
    Sub CLEAR_ALL()
        txtNumberAccount.Text = ""
        cmbNameAccount.Text = ""
        cmbMainAccount.Text = ""
        cmbGroubAccount.Text = ""
        txtDebtor.Text = ""
        txtCreditor.Text = ""
        txtNotes.Text = ""
    End Sub

    Private Sub frmAccounts_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        Bra.Fil("Accounts_Main", "Main_Name", cmbMainAccount)
        Headerx()
        'connectionString()
        'ds.EnforceConstraints = False
        'If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        'Dim str As String = "SELECT * FROM View_AccountsTree ORDER BY ACCNumber"
        'SqlDataAdapter1 = New SqlClient.SqlDataAdapter(str, Cn)
        'ds.Clear()
        'SqlDataAdapter1.Fill(ds, "AccountsTree")
        'BS.DataSource = ds
        'BS.DataMember = "AccountsTree"
        'SqlDataAdapter1.Dispose()
        'FilList()
        'DISPLAYTREEVIEW()
    End Sub

    Private Sub Headerx()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "select Main_Name as [الحساب الرئيسى],Groub_Name as [مجموعة الحساب],ACCNumber as [رقم الحساب],ACCName as [أسم الحساب],ACCDebtor as [مدين],ACCCreditor as [دائن],ACCNotes as [ملاحظات] from View_AccountsTree order by 1"
        cmd.CommandText = S : dr = cmd.ExecuteReader
        Dgv_Accounts.DataSource = Cls.PopulateDataView(dr)
        Dgv_Accounts.Columns(0).Width = 90
        Dgv_Accounts.Columns(1).Width = 100
        Dgv_Accounts.Columns(2).Width = 60
        Dgv_Accounts.Columns(3).Width = 180
        Dgv_Accounts.Columns(4).Width = 60
        Dgv_Accounts.Columns(5).Width = 60
        Dgv_Accounts.Columns(6).Width = 60
    End Sub

    Sub FilList()
        cmd.CommandType = CommandType.Text
        cmd.CommandText = "Select * from View_AccountsTree ORDER BY ACCNumber"
        dr = cmd.ExecuteReader
        LISTBOX.Items.Clear()
        Do While dr.Read
            LISTBOX.Items.Add(dr(1))
        Loop
        dr.Close()
    End Sub
    Private Sub DISPLAYTREEVIEW()
        'On Error Resume Next
        TreeView1.Nodes.Clear()
        ds.AcceptChanges()
        For i = 0 To ds.Tables("View_AccountsTree").Rows.Count - 1
            TreeView1.Nodes.Add(ds.Tables("View_AccountsTree").Rows(i).Item(1).ToString)
            TreeView1.Nodes(i).Nodes.Add(ds.Tables("View_AccountsTree").Rows(i).Item(2).ToString)
            TreeView1.Nodes(i).Nodes.Add(ds.Tables("View_AccountsTree").Rows(i).Item(3).ToString)
            TreeView1.Nodes(i).Nodes.Add(ds.Tables("View_AccountsTree").Rows(i).Item(4).ToString)
            TreeView1.Nodes(i).Nodes.Add(ds.Tables("View_AccountsTree").Rows(i).Item(5).ToString)
            TreeView1.Nodes(i).Nodes.Add(ds.Tables("View_AccountsTree").Rows(i).Item(6).ToString)
        Next
    End Sub

    Private Sub SAVEBUTTON_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles SAVEBUTTON.Click
        'On Error Resume Next
        Static P As Integer
        P = Me.BS.Count
        If Validate_Text() = False Then Exit Sub
        SAVERECORD()
        frmAccounts_Load(sender, e)
        Me.BS.Position = P + 1
        'MsgBox("تم الحفظ بنجاح", MsgBoxStyle.Information)
        CLEAR_ALL()
    End Sub
    Function Validate_Text() As Boolean
        On Error Resume Next
        If Trim(txtNumberAccount.Text) = "" Then
            MsgBox("فضلاً أدخل رقم الحساب", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtNumberAccount.Focus() : Return False : Exit Function
        End If
        If Trim(cmbNameAccount.Text) = "" Then
            MsgBox("فضلاً أدخل أسم الحساب", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            cmbNameAccount.Focus() : Return False : Exit Function
        End If
        'If Trim(cmbMainAccount.Text) = "" Then
        '    MsgBox("فضلاً أدخل الحساب الاب", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
        '    cmbMainAccount.Focus() : Return False : Exit Function
        'End If
        If Trim(cmbGroubAccount.Text) = "" Then
            MsgBox("فضلاً أدخل مجموعة الحساب", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            cmbGroubAccount.Focus() : Return False : Exit Function
        End If
        If Trim(txtDebtor.Text) = "" Then
            MsgBox("فضلاً أدخل رصيد مدين افتتاحى", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtDebtor.Focus() : Return False : Exit Function
        End If
        If Trim(txtCreditor.Text) = "" Then
            MsgBox("فضلاً أدخل رصيد دائن افتتاحى", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtCreditor.Focus() : Return False : Exit Function
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "Select * From AccountsTree where ACCNumber =N'" & txtNumberAccount.Text & "'"
        dr = cmd.ExecuteReader()
        If dr.HasRows = True Then
            MessageBox.Show("تم تسجيل بيانات الحساب سابقاً", "تكرار بيانات حساب", MessageBoxButtons.OK, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading)
            Return False : Exit Function
        End If
        Return True
    End Function

    Private Sub SAVERECORD()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        S = "insert into AccountsTree (ACCNumber,ACCName,ACCMain_Code,ACCGroub_Code,ACCDebtor,ACCCreditor,ACCNotes,UserName) values"
        S = S & " (N'" & txtNumberAccount.Text.Trim & "',N'" & cmbNameAccount.Text.Trim & "',N'" & txtCodeMainAccount.Text.Trim & "',"
        S = S & "N'" & txtCodeGroubAccount.Text.Trim & "',N'" & txtDebtor.Text.Trim & "',"
        S = S & "N'" & txtCreditor.Text.Trim & "',N'" & txtNotes.Text.Trim & "',N'" & UserName & "')"
        cmd.CommandText = S : cmd.ExecuteNonQuery()
    End Sub

    Private Sub EDITBUTTON_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles EDITBUTTON.Click
        'On Error Resume Next
        Static P As Integer
        P = Me.BS.Position
        UPDATERECORD()
        frmAccounts_Load(sender, e)
        Me.BS.Position = P
    End Sub

    Private Sub UPDATERECORD()
        If Trim(txtNumberAccount.Text) = "" Then
            MsgBox("فضلاً أدخل رقم الحساب", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtNumberAccount.Focus() : Exit Sub
        End If
        If Trim(cmbNameAccount.Text) = "" Then
            MsgBox("فضلاً أدخل أسم الحساب", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            cmbNameAccount.Focus() : Exit Sub
        End If
        If Trim(cmbGroubAccount.Text) = "" Then
            MsgBox("فضلاً أدخل مجموعة الحساب", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            cmbGroubAccount.Focus() : Exit Sub
        End If
        If Trim(txtDebtor.Text) = "" Then
            MsgBox("فضلاً أدخل رصيد مدين افتتاحى", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtDebtor.Focus() : Exit Sub
        End If
        If Trim(txtCreditor.Text) = "" Then
            MsgBox("فضلاً أدخل رصيد دائن افتتاحى", MsgBoxStyle.Exclamation + MsgBoxStyle.MsgBoxRight, "بيان مطلوب")
            txtCreditor.Focus() : Exit Sub
        End If

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "update AccountsTree set ACCNumber=N'" & txtNumberAccount.Text & "',ACCName=N'" & cmbNameAccount.Text & "',ACCMain_Code=N'" & txtCodeMainAccount.Text & "',ACCGroub_Code =N'" & txtCodeGroubAccount.Text & "', ACCDebtor=N'" & txtDebtor.Text & "', ACCCreditor=N'" & txtCreditor.Text & "',ACCNotes =N'" & txtNotes.Text & "',UserName=N'" & UserName & "' where ACCNumber =N'" & txtNumberAccount.Text & "'"
        cmd.ExecuteNonQuery()
        MsgBox("تم التعديل بنجاح", MsgBoxStyle.Information)
     CLEAR_ALL()

    End Sub

    Private Sub LISTBOX_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LISTBOX.Click
        On Error Resume Next
        Me.BS.Position = Me.LISTBOX.SelectedIndex
        DISPLAYRECORD()
    End Sub
    Private Sub DISPLAYRECORD()
        On Error Resume Next
        With Me
            .txtNumberAccount.Text = ds.Tables("ACCOUNTSTREE").Rows(Me.BS.Position)("ACCNumber").ToString
            .cmbNameAccount.Text = ds.Tables("ACCOUNTSTREE").Rows(Me.BS.Position)("ACCName").ToString
            .cmbMainAccount.Text = ds.Tables("ACCOUNTSTREE").Rows(Me.BS.Position)("ACCMain").ToString
            .cmbGroubAccount.Text = ds.Tables("ACCOUNTSTREE").Rows(Me.BS.Position)("ACCGroub").ToString
            .txtDebtor.Text = ds.Tables("ACCOUNTSTREE").Rows(Me.BS.Position)("ACCDebtor").ToString
            .txtCreditor.Text = ds.Tables("ACCOUNTSTREE").Rows(Me.BS.Position)("ACCCreditor").ToString
            .txtNotes.Text = ds.Tables("ACCOUNTSTREE").Rows(Me.BS.Position)("ACCNotes").ToString
        End With
    End Sub
    Private Sub TreeView1_AfterSelect(ByVal sender As System.Object, ByVal e As System.Windows.Forms.TreeViewEventArgs) Handles TreeView1.AfterSelect
        Dim Adp As SqlClient.SqlDataAdapter
        Dim con As SqlClient.SqlConnection
        On Error Resume Next
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        Dim strsql1 As String = "SELECT ACCNumber,ACCName,ACCMain,ACCGroub,ACCType,ACCDebtor,ACCCreditor,ACCNotes FROM ACCOUNTSTREE WHERE ACCNumber =N'" & TreeView1.SelectedNode.Text & "'"
        con = New SqlClient.SqlConnection(constring)
        Dim ds As New DataSet
        Adp = New SqlClient.SqlDataAdapter(strsql1, con)
        ds.Clear()
        Adp.Fill(ds)
        If ds.Tables(0).Rows.Count > 0 Then
            BS.Position = Val(ds.Tables(0).Rows(0).Item(0)) - 1
        End If
        Adp.Dispose()
        con.Close()
    End Sub
    Private Sub DELETEBUTTON_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DELETEBUTTON.Click

        If txtNumberAccount.Text = "" Then
            MsgBox("لاتوجد بيانات للحذف", MsgBoxStyle.Exclamation, "حذف")
            Exit Sub
        End If
        Dim x As String = MsgBox("هل تريد بالفعل حذف البيانات", MsgBoxStyle.OkCancel + MsgBoxStyle.Exclamation)
        If x = vbCancel Then Exit Sub

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "delete from AccountsTree where ACCNumber =N'" & txtNumberAccount.Text & "'" : cmd.ExecuteNonQuery()

        frmAccounts_Load(sender, e)
        CLEAR_ALL()
    End Sub
    Private Sub txtNumberAccount_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtNumberAccount.TextChanged
        MyVars.CheckNumber(txtNumberAccount)
    End Sub
    Private Sub txtDebtor_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtDebtor.TextChanged
        MyVars.CheckNumber(txtDebtor)
    End Sub
    Private Sub txtCreditor_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtCreditor.TextChanged
        MyVars.CheckNumber(txtCreditor)
    End Sub

    Private Sub cmbMainAccount_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbMainAccount.SelectedIndexChanged
        If ActionGrid = False Then
            If cmbMainAccount.Text.Trim = "" Then Exit Sub

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Main_Code from Accounts_Main where Main_Name=N'" & cmbMainAccount.Text & "'"
            dr = cmd.ExecuteReader
            If dr.Read Then
                txtCodeMainAccount.Text = dr("Main_Code")
            End If


            cmbGroubAccount.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select Groub_Name from Accounts_Groub where Main_Code =N'" & txtCodeMainAccount.Text & "' order by 1"
            dr = cmd.ExecuteReader
            Do While dr.Read
                cmbGroubAccount.Items.Add(Trim(dr(0)))
            Loop
            cmbGroubAccount.SelectedIndex = -1
        End If
    End Sub

    Private Sub cmbGroubAccount_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbGroubAccount.SelectedIndexChanged
        If ActionGrid = False Then
            If cmbGroubAccount.Text.Trim = "" Then Exit Sub

            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "Select Groub_Code from Accounts_Groub where Groub_Name=N'" & cmbGroubAccount.Text & "'"
            dr = cmd.ExecuteReader
            If dr.Read Then
                txtCodeGroubAccount.Text = dr("Groub_Code")
            End If

            cmbNameAccount.Items.Clear()
            If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
            cmd.CommandText = "select ACCName from AccountsTree where ACCGroub_Code =N'" & txtCodeGroubAccount.Text & "' order by 1"
            dr = cmd.ExecuteReader
            Do While dr.Read
                cmbNameAccount.Items.Add(Trim(dr(0)))
            Loop
            cmbNameAccount.Text = ""
        End If
    End Sub

    Private Sub Dgv_Accounts_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Dgv_Accounts.DoubleClick
        GetDataGrid()
    End Sub

    Private Sub GetDataGrid()

        Bra.Fil("Accounts_Groub", "Groub_Name", cmbGroubAccount)

        ActionGrid = True
        If Dgv_Accounts.RowCount = 0 Then Beep() : Exit Sub
        If (Dgv_Accounts.SelectedRows.Count) = 0 Then Beep() : Exit Sub
        Dim ACCNumber As String
        ACCNumber = Dgv_Accounts.SelectedRows(0).Cells(2).Value

        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select  Main_Code,Main_Name,Groub_Code,Groub_Name,ACCNumber,ACCName,ACCDebtor,ACCCreditor,ACCNotes from View_AccountsTree where ACCNumber =N'" & ACCNumber & "'" : dr = cmd.ExecuteReader : dr.Read()
        If dr.HasRows = False Then Exit Sub
        If dr(0) Is DBNull.Value Then
        Else
            txtCodeMainAccount.Text = dr("Main_Code")
        End If
        If dr(1) Is DBNull.Value Then
        Else
            cmbMainAccount.Text = dr("Main_Name")
        End If
        If dr(2) Is DBNull.Value Then
        Else
            txtCodeGroubAccount.Text = dr("Groub_Code")
        End If
        If dr(3) Is DBNull.Value Then
        Else
            cmbGroubAccount.Text = dr("Groub_Name")
        End If
        If dr(4) Is DBNull.Value Then
        Else
            txtNumberAccount.Text = dr("ACCNumber")
        End If
        If dr(5) Is DBNull.Value Then
        Else
            cmbNameAccount.Text = dr("ACCName")
        End If
        If dr(6) Is DBNull.Value Then
        Else
            txtDebtor.Text = dr("ACCDebtor")
        End If
        If dr(7) Is DBNull.Value Then
        Else
            txtCreditor.Text = dr("ACCCreditor")
        End If
        If dr(8) Is DBNull.Value Then
        Else
            txtNotes.Text = dr("ACCNotes")
        End If
        ActionGrid = False
    End Sub

    Private Sub btnMainAccount_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnMainAccount.Click
        frmAccountsMain.ShowDialog()
    End Sub

    Private Sub cmbMainAccount_DropDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbMainAccount.DropDown
        Bra.Fil("Accounts_Main", "Main_Name", cmbMainAccount)
    End Sub

    Private Sub btnGroubAccount_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnGroubAccount.Click
        frmAccountsGroub.ShowDialog()
    End Sub

    Private Sub cmbGroubAccount_DropDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmbGroubAccount.DropDown
        cmbGroubAccount.Items.Clear()
        If Not dr Is Nothing AndAlso Not dr.IsClosed Then dr.Close()
        cmd.CommandText = "select Groub_Name from Accounts_Groub where Main_Code =N'" & txtCodeMainAccount.Text & "' order by 1"
        dr = cmd.ExecuteReader
        Do While dr.Read
            cmbGroubAccount.Items.Add(Trim(dr(0)))
        Loop
        cmbGroubAccount.SelectedIndex = -1
    End Sub
End Class